#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问卷星重定向接口测试脚本
支持测试加密和非加密两种数据格式
"""

import requests
import json
import base64
from datetime import datetime
import sys

# 配置
BASE_URL = "http://localhost:8080/activity"  # 修改为您的实际服务地址
WJX_REDIRECT_URL = f"{BASE_URL}/survey/wjx-redirect"

def test_non_encrypted_data():
    """测试非加密数据格式"""
    print("=" * 50)
    print("测试非加密数据格式")
    print("=" * 50)
    
    # 模拟问卷星POST的非加密JSON数据
    survey_data = {
        "activity": 123456789,
        "joinid": "test_join_" + datetime.now().strftime("%Y%m%d_%H%M%S"),
        "name": "测试问卷",
        "ipaddress": "*************",
        "source": "问卷星",
        "thirdusername": "",
        "nickname": "测试用户",
        "sojumpparam": "test$123$456$testuser$$789$101112$ext1$ext2$ext3",
        "realname": "张三",
        "reldept": "技术部",
        "relext": "",
        "province": "北京市",
        "city": "北京市",
        "index": 1,
        "timetaken": 120,
        "totalvalue": 85,
        "answer": json.dumps({
            "q1": "1",
            "q2": "2,3",
            "q3": "这是填空题的答案",
            "q4": "1^其他选项内容",
            "q5": "非常满意"
        }),
        "sign": "test_signature",
        "emuid": 12345,
        "emno": "test_em_001",
        "pid": "test_pid_001",
        "emscene": "test_scene",
        "submittime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "unionid": "test_union_id",
        "ext1": "扩展参数1",
        "ext2": "扩展参数2",
        "ext3": "扩展参数3"
    }
    
    # 转换为JSON字符串作为content参数
    content = json.dumps(survey_data, ensure_ascii=False)
    
    # 发送POST请求
    data = {"content": content}
    
    try:
        print(f"发送请求到: {WJX_REDIRECT_URL}")
        print(f"Content长度: {len(content)}")
        print(f"Content预览: {content[:200]}...")
        
        response = requests.post(
            WJX_REDIRECT_URL,
            data=data,
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "WJX-Test-Client/1.0"
            },
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 请求成功!")
            # 如果返回的是HTML页面，保存到文件
            if 'text/html' in response.headers.get('content-type', ''):
                with open('test_result_non_encrypted.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print("📄 HTML响应已保存到: test_result_non_encrypted.html")
            else:
                print(f"响应内容: {response.text[:500]}...")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")

def test_encrypted_data():
    """测试加密数据格式（模拟）"""
    print("\n" + "=" * 50)
    print("测试加密数据格式（模拟Base64编码）")
    print("=" * 50)
    
    # 模拟问卷星POST的JSON数据
    survey_data = {
        "activity": 987654321,
        "joinid": "encrypted_join_" + datetime.now().strftime("%Y%m%d_%H%M%S"),
        "name": "加密测试问卷",
        "ipaddress": "*************",
        "source": "问卷星",
        "realname": "李四",
        "answer": json.dumps({
            "q1": "2",
            "q2": "1,4",
            "q3": "加密测试答案"
        }),
        "emuid": 54321,
        "submittime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # 模拟加密：这里只是Base64编码，实际加密会更复杂
    json_str = json.dumps(survey_data, ensure_ascii=False)
    # 注意：这不是真正的AES加密，只是为了测试
    fake_encrypted = base64.b64encode(json_str.encode('utf-8')).decode('utf-8')
    
    data = {"content": fake_encrypted}
    
    try:
        print(f"发送请求到: {WJX_REDIRECT_URL}")
        print(f"模拟加密Content长度: {len(fake_encrypted)}")
        print(f"模拟加密Content预览: {fake_encrypted[:100]}...")
        
        response = requests.post(
            WJX_REDIRECT_URL,
            data=data,
            headers={
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "WJX-Test-Client/1.0"
            },
            timeout=30
        )
        
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 请求成功!")
            if 'text/html' in response.headers.get('content-type', ''):
                with open('test_result_encrypted.html', 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print("📄 HTML响应已保存到: test_result_encrypted.html")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")

def test_health_check():
    """测试健康检查接口"""
    print("\n" + "=" * 50)
    print("测试健康检查接口")
    print("=" * 50)
    
    health_url = f"{WJX_REDIRECT_URL}/health"
    
    try:
        response = requests.get(health_url, timeout=10)
        print(f"健康检查响应: {response.status_code}")
        if response.status_code == 200:
            print("✅ 服务健康检查通过!")
            print(f"响应内容: {response.text}")
        else:
            print(f"❌ 健康检查失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 健康检查异常: {e}")

def test_invalid_data():
    """测试无效数据"""
    print("\n" + "=" * 50)
    print("测试无效数据处理")
    print("=" * 50)
    
    test_cases = [
        {"content": ""},  # 空内容
        {"content": "invalid_json_data"},  # 无效JSON
        {},  # 缺少content参数
    ]
    
    for i, data in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {data}")
        try:
            response = requests.post(WJX_REDIRECT_URL, data=data, timeout=10)
            print(f"响应状态码: {response.status_code}")
            if 'text/html' in response.headers.get('content-type', ''):
                print("返回了HTML错误页面")
            else:
                print(f"响应内容: {response.text[:200]}...")
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")

if __name__ == "__main__":
    print("问卷星重定向接口测试脚本")
    print(f"目标服务: {BASE_URL}")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查服务是否可用
    test_health_check()
    
    # 测试非加密数据（主要测试场景）
    test_non_encrypted_data()
    
    # 测试模拟加密数据
    test_encrypted_data()
    
    # 测试异常情况
    test_invalid_data()
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("请检查生成的HTML文件查看页面效果")
    print("=" * 50)
