package cn.emoney.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
public class CustServiceDO {
    public Integer actcycleId;
    public String actcycleStartDate;
    public Integer actcycleType;
    public String createDate;
    public String currentAllocationTime;
    public String currentBelongUserCode;
    public String currentBelongUserId;
    public String currentBelongUserName;
    public String custCode;
    public String custId;
    public String emNum;
    public String encryptPhoneNum;
    public String firstAllocationTime;
    public Integer id;
    public String lockBelongUserCode;
    public String lockBelongUserId;
    public String updateDate;
    public String userWechatLink;
    public String jobNumber;
    public String kfUrl;
    public Integer wechatJoin;
}
