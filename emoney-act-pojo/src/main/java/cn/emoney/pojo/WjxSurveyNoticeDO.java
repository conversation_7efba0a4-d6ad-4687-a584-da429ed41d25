package cn.emoney.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 问卷星答卷场景推送通知URL配置类
 *
 * <AUTHOR>
 * @date 2022/03/31 11:01
 **/
@Data
public class WjxSurveyNoticeDO {

    /**
     * 自增序号
     */
    private Integer id;

    /**
     * 场景名称:
     */
    private String scene;

    /**
     * 推送URL
     */
    private String pushurl;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private java.util.Date createtime;


    private Boolean isdel;

}
