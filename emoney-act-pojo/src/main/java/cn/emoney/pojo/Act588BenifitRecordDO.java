package cn.emoney.pojo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

public class Act588BenifitRecordDO implements Comparable<Act588BenifitRecordDO>  {
    public Integer id;

    public String uid;

    public String uname;

    public Integer benefitId;

    public String benefitName;

    public String source;

    public String actCode;

    @JSONField(format = "yyyy-MM-dd")
    public Date writeTime;

    public String showTime;

    public Integer benefitConfirm;

    @Override
    public int compareTo(Act588BenifitRecordDO other) {
        return Integer.compare(this.id, other.id);
    }
}