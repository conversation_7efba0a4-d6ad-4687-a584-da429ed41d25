package cn.emoney.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
@Data
public class ActCallFailedDO implements Comparable<ActCallFailedDO>  {
    public Integer id;

    public String uid;

    public String traceId;

    public String actCode;

    public Integer actionType;

    public String actionContext;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public Date actionTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public Date lastModifyTime;

    @Override
    public int compareTo(ActCallFailedDO other) {
        return Integer.compare(this.id, other.id);
    }
}