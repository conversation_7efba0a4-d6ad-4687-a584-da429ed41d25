package cn.emoney.pojo.bo;

import cn.emoney.act.quest.logic.QuestCondition;
import cn.emoney.act.quest.logic.QuestReward;
import cn.emoney.act.quest.logic.QuestTarget;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class FirstClassQuestDefinitionDTO {
    private List<QuestTarget> target;
    private List<QuestCondition> condition;
    private List<QuestReward> reward;
}
