package cn.emoney.pojo.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PointDetailDTO {
    private Long id;
    private Long uid;
    private Long taskId;
    private String platform;
    private Float taskPoint;
    private String taskName;
    private Integer pointStatus;
    private String pid;
    private String emNo;
    private Boolean isDailytask;
    private String subId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expirationTime;
    private Float leftPoint;
    private Boolean isValid;
    private Boolean isDirectional;
    private Integer lockDays;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    private String createBy;
    private String updateBy;
    private String remark;
    private Boolean isAppoint;
}
