package cn.emoney.pojo.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CourseDetailDTO {

    @JsonProperty("Id")
    private int id;

    // 直播名称
    @JsonProperty("CourseName")
    private String courseName;

    // 课程摘要
    @JsonProperty("CourseSummary")
    private String courseSummary;

    // 课程封面url
    @JsonProperty("CourseImageUrl")
    private String courseImageUrl;

    // 直播开始时间
    @JsonProperty("CourseBeginTime")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime courseBeginTime;

    // 直播结束时间
    @JsonProperty("CourseEndTime")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime courseEndTime;

    // 课程最早进入时间
    @JsonProperty("CourseAccessTime")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime courseAccessTime;

    // 直播室ID
    @JsonProperty("CourseLiveId")
    private int courseLiveId;

    // 直播地址url
    @JsonProperty("CourseLiveUrl")
    private String courseLiveUrl;

    // 直播编号
    @JsonProperty("CourseMeetingNo")
    private String courseMeetingNo;

    // 直播口令
    @JsonProperty("CourseMeetingPassword")
    private String courseMeetingPassword;

    // 课程预告url
    @JsonProperty("CourseForeNoticeUrl")
    private String courseForeNoticeUrl;

    // 课程预告内容
    @JsonProperty("CourseForeNoticeContent")
    private String courseForeNoticeContent;

    // 提前预告时间
    @JsonProperty("LiveBeforeMinutes")
    private int liveBeforeMinutes;

    // 课程回放地址
    @JsonProperty("CoursePlayBackUrl")
    private String coursePlayBackUrl;

    // 录播编号
    @JsonProperty("PlayBackMeetingNo")
    private String playBackMeetingNo;

    // 录播口令
    @JsonProperty("PlayBackPassword")
    private String playBackPassword;

    // 禁推名单
    @JsonProperty("CourseUserList")
    private String courseUserList;

    // 是否删除
    @JsonProperty("IsDelete")
    private int isDelete;

    // 创建时间
    @JsonProperty("CreateTime")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createTime;

    // 讲师列表
    @JsonProperty("TeacherList")
    private List<TeacherInfo> teacherList;

    // 课程标签列表
    @JsonProperty("LabelList")
    private List<CourseTag> labelList;

    // 课包列表
    @JsonProperty("BagList")
    private List<CourseBag> bagList;

    @JsonProperty("IsFavorite")
    private boolean isFavorite;

    // 讲师名称
    @JsonProperty("CourseTeacherName")
    private String courseTeacherName;

    // 讲师部门
    @JsonProperty("CourseTeacherDept")
    private String courseTeacherDept;

    // 所属栏目(可能有多个)
    @JsonProperty("ColumnID")
    private String columnID;

    @JsonProperty("StrategyTagId")
    private int strategyTagId;

    @JsonProperty("IsDisablePop")
    private int isDisablePop;

    // 用户禁用弹窗名单
    @JsonProperty("UserList")
    private String userList;

    // 预约课程是否推送cmp
    @JsonProperty("SubscribeIsPushCMP")
    private int subscribeIsPushCMP;

    // 是否预约
    @JsonProperty("IsSubscribe")
    private int isSubscribe;

    // 课程所属版本列表
    @JsonProperty("VersionList")
    private List<ProductVersion> versionList;

    // 进入课程码
    @JsonProperty("AccessPassword")
    private String accessPassword;

    // 用户屏蔽分组列表
    @JsonProperty("GroupList")
    private List<LiveUserGroup> groupList;

    // 播放量
    @JsonProperty("ViewCount")
    private int viewCount;

    // 屏蔽课程名单
    @JsonProperty("MaskUserList")
    private String maskUserList;

    // 用户反屏蔽分组列表
    @JsonProperty("NoMaskGroupList")
    private List<LiveUserGroup> noMaskGroupList;

    // 白名单用户列表
    @JsonProperty("WhiteUserList")
    private String whiteUserList;

    // 屏蔽用户的注册时间区间
    @JsonProperty("RegisterTimeZone")
    private String registerTimeZone;

    // 屏蔽用户的到期时间区间(格式:2020-10-10 00:00:00~2020-10-14 23:59:59)
    @JsonProperty("ExpiredTimeZone")
    private String expiredTimeZone;

    // 提醒观看显示图片
    @JsonProperty("ReViewTipsImageUrl")
    private String reViewTipsImageUrl;

    @JsonProperty("EvaluateCategroyId")
    private int evaluateCategroyId;

    // 画像分组id
    @JsonProperty("PersonasUserGroupId")
    private String personasUserGroupId;

    // 反屏蔽画像分组id
    @JsonProperty("MaskPersonasUserGroupId")
    private String maskPersonasUserGroupId;

    // 问卷链接
    @JsonProperty("SurveyLink")
    private String surveyLink;

    // 188按钮图标url
    @JsonProperty("IconImageUrl")
    private String iconImageUrl;

    // 支付卡片文案
    @JsonProperty("PayText")
    private String payText;

    // 支付链接地址
    @JsonProperty("PayUrl")
    private String payUrl;

    // 支付标题
    @JsonProperty("PayTitle")
    private String payTitle;

    // 支付按钮描述
    @JsonProperty("PayButtonDesc")
    private String payButtonDesc;

    // 推送cmpcode
    @JsonProperty("CMPCode")
    private String CMPCode;

    // 是否推送cmp
    @JsonProperty("IsPushCMP")
    private int isPushCMP;

    // 过滤器id
    @JsonProperty("FilterStrategyId")
    private String filterStrategyId;

    // 互动码
    @JsonProperty("InteractiveCode")
    private String interactiveCode;

    // 使用新互动
    @JsonProperty("UseNewInteractive")
    private Boolean useNewInteractive;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TeacherInfo {
        @JsonProperty("Id")
        private int id;

        // 讲师姓名
        @JsonProperty("TeacherName")
        private String teacherName;

        // 讲师部门
        @JsonProperty("TeacherDept")
        private String teacherDept;

        @JsonProperty("QueueId")
        private int queueId;

        @JsonProperty("IsDelete")
        private int isDelete;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CourseTag {
        @JsonProperty("Id")
        private int id;

        // 标签名称
        @JsonProperty("TagName")
        private String tagName;

        // 标签分类
        @JsonProperty("TagClassfication")
        private int tagClassification;

        // 创建时间
        @JsonProperty("CreateTime")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime createTime;

        @JsonProperty("IsDelete")
        private int isDelete;

        @JsonProperty("QueueId")
        private int queueId;

        @JsonProperty("FavoriteCount")
        private int favoriteCount;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CourseBag {
        @JsonProperty("Id")
        private int id;

        // 课包名称
        @JsonProperty("CourseBagName")
        private String courseBagName;

        @JsonProperty("IsDelete")
        private int isDelete;

        @JsonProperty("QueueId")
        private int queueId;

        // 栏目列表
        @JsonProperty("ColumnList")
        private List<Column> columnList;

        // 版本列表 (set to null in the JSON data)
        @JsonProperty("VersionList")
        private List<ProductVersion> versionList;

        // 课包摘要
        @JsonProperty("BagSummary")
        private String bagSummary;

        // 课包信息
        @JsonProperty("BagInfo")
        private String bagInfo;

        // 课包图片url
        @JsonProperty("BagImageUrl")
        private String bagImageUrl;

        // 课包缩略图url
        @JsonProperty("BagThumbImageUrl")
        private String bagThumbImageUrl;

        // 课程数量
        @JsonProperty("CourseCount")
        private int courseCount;

        // 画像分组id
        @JsonProperty("PersonasUserGroupId")
        private String personasUserGroupId;

        // 白名单用户列表
        @JsonProperty("WhiteUserList")
        private String whiteUserList;

        // 屏蔽用户列表
        @JsonProperty("MaskUserList")
        private String maskUserList;

        // 反屏蔽画像分组id
        @JsonProperty("MaskPersonasUserGroupId")
        private String maskPersonasUserGroupId;

        // Constructor, getters, and setters are omitted for brevity.

        // Inner class for the Column
        @Data
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Column {
            @JsonProperty("Id")
            private int id;

            @JsonProperty("ParentColumnId")
            private int parentColumnId;

            // 栏目名称
            @JsonProperty("ColumnName")
            private String columnName;

            @JsonProperty("IsDelete")
            private int isDelete;

            @JsonProperty("QueueId")
            private int queueId;

            @JsonProperty("FavoriteCount")
            private int favoriteCount;

            @JsonProperty("IsStrategyColumn")
            private int isStrategyColumn;

            @JsonProperty("SecondColumnList")
            private List<Column> secondColumnList;
        }
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProductVersion {
        @JsonProperty("Id")
        private int id;

        // 产品ID
        @JsonProperty("PId")
        private String PId;

        // 产品名称
        @JsonProperty("PIdName")
        private String PIdName;

        @JsonProperty("PlatFormClass")
        private int platFormClass;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LiveUserGroup {
        // 用户组相关信息，根据具体数据进行定义
    }
}