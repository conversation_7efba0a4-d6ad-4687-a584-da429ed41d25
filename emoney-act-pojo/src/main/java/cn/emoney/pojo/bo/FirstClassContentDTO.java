package cn.emoney.pojo.bo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class FirstClassContentDTO {
    private Long id;
    private String name;
    private List<ClassColumn> columns;
    private Date startTime;
    private Date endTime;
    private Integer status;
    private boolean valid;
    private Map<String, Object> meta;

    @Data
    @Accessors(chain = true)
    public static class ClassColumn {
        /**
         * 栏目ID
         */
        private Integer id;
        private String name;
        private int size;
        /**
         * 排序方式
         */
        private int sort;
        /**
         * 动态课表
         */
        private boolean dynamic;
        private int safeSize;

        public static final Integer ID_ASC = 1, ID_DESC = -1;
        public static final Integer TIME_ASC = 2, TIME_DESC = -2;
    }
}
