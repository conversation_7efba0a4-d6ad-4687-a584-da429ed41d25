package cn.emoney.pojo.bo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-03-18
 */
@Data
public class FirstClassDTO {
    public Integer id;
    public String className;
    public String classSummary;
    public String teacherName;
    public String classUrl;
    public String classCover;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public Date beginTime;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public Date endTime;
    public boolean isDone;
    //新的数据源 0：录播   1：直播
    public Integer parentID;
    public String parentName;
    public boolean hasPoint;
    public String pointTaskID;
    public String voteUrl;
    public Integer point;
    public String tipName;
    public String activityID;
    public String tgNO;
    public String liveVideoID;
    public String voteUrl1;
    public long playTime;
    public Integer viewCount;

    // 智盈第一课新增
    private Integer bookBaseAmount;
    private Integer replayBaseAmount;
    private String weekComment;
    /**
     * @deprecated App那边要读这个封面，但是实际和视频封面是一样的
     */
    @Deprecated
    private String appCoverImg;
}
