package cn.emoney.pojo.bo;

import cn.emoney.act.quest.logic.QuestReward;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class FirstClassCourseDTO {
    private Integer id;
    private Integer courseId;
    private String title;
    private String summary;
    private String teacherName;
    /**
     * 课程评论，首次用于 智盈第一课-周评
     */
    private String comment;
    private String coverPc;
    private String coverApp;
    private Integer parentId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    // 其他 //
    /**
     * 课程奖励
     */
    private List<CourseReward> rewards;
    /**
     * 课程附加 URL
     */
    private List<ExtraUrl> extraUrls;
    private Integer bookBaseAmount;
    private Integer replayBaseAmount;
    private Integer order;

    /**
     * 活动批次
     */
    @Deprecated
    private transient Integer activitySeq;

    public void addReward(Integer id, String name, QuestReward reward) {
        CourseReward courseReward = new CourseReward();
        courseReward.setId(id);
        courseReward.setName(name);
        courseReward.setReward(reward);
        if (rewards == null) {
            rewards = new ArrayList<>();
        }
        rewards.add(courseReward);
    }

    @Data
    public static class CourseReward {
        private Integer id;
        private String name;
        private QuestReward reward;
    }

    @Data
    public static class ExtraUrl {
        // 0: 练习 1: 笔记 2: 报告
        private final Integer type;
        private final String url;

        public ExtraUrl(Integer type, String url) {
            this.type = type;
            this.url = url;
        }
    }
}
