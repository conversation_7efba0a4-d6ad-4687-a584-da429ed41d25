package cn.emoney.pojo.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class ActivityQuestDTO {
    private Long id;
    private String name;

    private String type;
    private String content;
    // 0: disabled, 1: enabled
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @JsonIgnore
    public boolean isEnabled() {
        return Objects.equals(status, 1);
    }

    public boolean isValid(Date time) {
        return isEnabled() && startTime.before(time) && (endTime == null || endTime.after(time));
    }

    @JsonIgnore
    public boolean isValid() {
        return isValid(new Date());
    }
}
