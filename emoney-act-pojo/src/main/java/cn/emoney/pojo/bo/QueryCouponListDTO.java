package cn.emoney.pojo.bo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-12-15
 */
@Data
public class QueryCouponListDTO {
    /// <summary>
    /// 优惠活动编号
    /// </summary>
    public String COUPON_ACTIVITY_ID;
    /// <summary>
    /// 优惠活动名称
    /// </summary>
    public String COUPON_ACTIVITY_NAME;
    /// <summary>
    /// 优惠券抵扣金额
    /// </summary>
    public double COUPON_PRICE;
    /// <summary>
    /// 优惠券编码
    /// </summary>
    public String COUPON_CODE;
    /// <summary>
    /// 优惠券名称
    /// </summary>
    public String COUPON_RULE_NAME;
    /// <summary>
    /// 优惠券使用状态（0：未使用，1：已使用）
    /// </summary>
    public int COUPON_USE_STATUS;
    /// <summary>
    /// 优惠券有效状态（0：有效，-1：无效）
    /// </summary>
    public int COUPON_ISENABLE;
    /// <summary>
    /// 优惠券可用起始时间
    /// </summary>
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    public Date COUPON_START_TIME;
    /// <summary>
    /// 优惠券到期时间
    /// </summary>
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    public Date COUPON_END_TIME;
    /// <summary>
    /// 是否可叠加使用（1：是，0：否）
    /// </summary>
    public int IsCanUseMore;
}
