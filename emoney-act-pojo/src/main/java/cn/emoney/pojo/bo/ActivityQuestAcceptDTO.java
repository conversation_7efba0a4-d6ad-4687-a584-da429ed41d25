package cn.emoney.pojo.bo;

import lombok.Data;

import java.time.ZonedDateTime;

@Data
public class ActivityQuestAcceptDTO {
    public static final int CANCELED = -1;
    public static final int ACCEPTED = 0;
    public static final int COMPLETED = 1;

    private Long id;
    private Long uid;
    private Long questId;
    private Integer status;
    private ZonedDateTime createTime;
    private ZonedDateTime finishTime;
    private ZonedDateTime cancelTime;
}
