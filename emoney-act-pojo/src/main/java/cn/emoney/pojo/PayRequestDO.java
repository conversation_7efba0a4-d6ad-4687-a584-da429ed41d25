package cn.emoney.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-10-11
 */
@Data
public class PayRequestDO {
    public String token;
    /** 平台号(8:pc程序,9:IOS,10:安卓,11:pc浏览器,12:移动浏览器,13:苹果ipad,14:安卓平板,15:微信小程序)*/
    public String platformId;
    public String userName;
    public String phoneEncrypt;

    /**购买产品ID，如有多个用逗号分隔*/
    public String pids;
    /**0-9，根据活动要求传值，无要求默认传0*/
    public String groupCode;
    /**1001-智盈APP，1002-智盈PC，1003-智盈小程序*/
    public int clientSource;
    /**业务类型，为空时排除基金业务类型*/
    public String businessType;
}
