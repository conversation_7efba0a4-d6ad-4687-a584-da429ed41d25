package cn.emoney.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName Lottery_Detail
 */
//@TableName(value ="Lottery_Detail")
@Data
public class LotteryDetailDO implements Serializable {
    /**
     * 
     */
    //@TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    //@TableField(value = "mobile0x")
    private String mobile0x;

    /**
     * 
     */
    //@TableField(value = "uid")
    private String uid;

    /**
     * 
     */
    //@TableField(value = "em")
    private String em;

    /**
     * 
     */
    //@TableField(value = "mobilemask")
    private String mobilemask;

    /**
     * 
     */
    //@TableField(value = "lotterytarget")
    private String lotterytarget;

    /**
     * 
     */
    //@TableField(value = "lotterytime")
    private Date lotterytime;

    /**
     * 
     */
    //@TableField(value = "prizename")
    private String prizename;

    /**
     * 
     */
    //@TableField(value = "prizecode")
    private String prizecode;

    /**
     * 
     */
    //@TableField(value = "prizeconfirm")
    private String prizeconfirm;

    /**
     * 
     */
    //@TableField(value = "batchno")
    private String batchno;

    private String group;

    private Integer level;

    //@TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LotteryDetailDO other = (LotteryDetailDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getMobile0x() == null ? other.getMobile0x() == null : this.getMobile0x().equals(other.getMobile0x()))
            && (this.getUid() == null ? other.getUid() == null : this.getUid().equals(other.getUid()))
            && (this.getEm() == null ? other.getEm() == null : this.getEm().equals(other.getEm()))
            && (this.getMobilemask() == null ? other.getMobilemask() == null : this.getMobilemask().equals(other.getMobilemask()))
            && (this.getLotterytarget() == null ? other.getLotterytarget() == null : this.getLotterytarget().equals(other.getLotterytarget()))
            && (this.getLotterytime() == null ? other.getLotterytime() == null : this.getLotterytime().equals(other.getLotterytime()))
            && (this.getPrizename() == null ? other.getPrizename() == null : this.getPrizename().equals(other.getPrizename()))
            && (this.getPrizecode() == null ? other.getPrizecode() == null : this.getPrizecode().equals(other.getPrizecode()))
            && (this.getPrizeconfirm() == null ? other.getPrizeconfirm() == null : this.getPrizeconfirm().equals(other.getPrizeconfirm()))
            && (this.getBatchno() == null ? other.getBatchno() == null : this.getBatchno().equals(other.getBatchno()))
            && (this.getGroup() == null ? other.getGroup() == null : this.getGroup().equals(other.getGroup()))
            && (this.getLevel() == null ? other.getLevel() == null : this.getLevel().equals(other.getLevel()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getMobile0x() == null) ? 0 : getMobile0x().hashCode());
        result = prime * result + ((getUid() == null) ? 0 : getUid().hashCode());
        result = prime * result + ((getEm() == null) ? 0 : getEm().hashCode());
        result = prime * result + ((getMobilemask() == null) ? 0 : getMobilemask().hashCode());
        result = prime * result + ((getLotterytarget() == null) ? 0 : getLotterytarget().hashCode());
        result = prime * result + ((getLotterytime() == null) ? 0 : getLotterytime().hashCode());
        result = prime * result + ((getPrizename() == null) ? 0 : getPrizename().hashCode());
        result = prime * result + ((getPrizecode() == null) ? 0 : getPrizecode().hashCode());
        result = prime * result + ((getPrizeconfirm() == null) ? 0 : getPrizeconfirm().hashCode());
        result = prime * result + ((getBatchno() == null) ? 0 : getBatchno().hashCode());
        result = prime * result + ((getGroup() == null) ? 0 : getGroup().hashCode());
        result = prime * result + ((getLevel() == null) ? 0 : getLevel().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", mobile0x=").append(mobile0x);
        sb.append(", uid=").append(uid);
        sb.append(", em=").append(em);
        sb.append(", mobilemask=").append(mobilemask);
        sb.append(", lotterytarget=").append(lotterytarget);
        sb.append(", lotterytime=").append(lotterytime);
        sb.append(", prizename=").append(prizename);
        sb.append(", prizecode=").append(prizecode);
        sb.append(", prizeconfirm=").append(prizeconfirm);
        sb.append(", batchno=").append(batchno);
        sb.append(", group=").append(group);
        sb.append(", level=").append(level);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}