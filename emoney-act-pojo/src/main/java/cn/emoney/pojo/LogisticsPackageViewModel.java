package cn.emoney.pojo;

import cn.hutool.core.date.DateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-10-11
 */
@Data
public class LogisticsPackageViewModel {
    public int Id;
    public int States;
    public String Description;
    public Long BeginTime;
    public Long EndTime;
    /**
     * 活动类型(0:常规活动,1:预售活动(原预售模式),2:定金活动(原预售模式，不需要适当性匹配),3:尾款活动(原预售模式),4:打赏活动(不需要适当性匹配),5:定金活动(新预售模式，不需要适当性匹配),6:预售活动(新预售模式),7:尾款活动(新预售模式))
     * */
    public int ActivityType;
    /**
     * 活动包类型(0:常规包,1:新手包,2:续费包,3:智盈换购,4:基本面换购,5:打赏包(不需要适当性匹配),6:换购)
     * */
    public int PackType;
    public String OrderNumber;
    public String GoodsName;
    public int ClientSource;
    public int BuyStates;
}
