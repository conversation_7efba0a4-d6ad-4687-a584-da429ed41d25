package cn.emoney.pojo;

/**
 * <AUTHOR>
 * @date 2024-04-24
 */
public class PointAndCouponRequestDO {
    public String uid;  // 用户ID
    public String pid;  // 商品ID或相关标识符
    public String actCode;  // 活动代码
    public String pointTaskID;  // 积分任务ID
    public String couponActivityID;  // 优惠券活动ID
    public String couponPrice;  // 优惠券金额

    /**
     * 构造方法，用于直接初始化所有请求参数
     *
     * @param uid 用户ID
     * @param pid 商品ID或相关标识符
     * @param actCode 活动代码
     * @param pointTaskID 积分任务ID
     * @param couponActivityID 优惠券活动ID
     * @param couponPrice 优惠券金额
     */
    public PointAndCouponRequestDO(String uid, String pid, String actCode, String pointTaskID, String couponActivityID, String couponPrice) {
        this.uid = uid;
        this.pid = pid;
        this.actCode = actCode;
        this.pointTaskID = pointTaskID;
        this.couponActivityID = couponActivityID;
        this.couponPrice = couponPrice;
    }
}
