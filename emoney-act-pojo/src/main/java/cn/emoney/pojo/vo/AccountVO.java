package cn.emoney.pojo.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-01-17
 */
@Data
public class AccountVO {
    @JsonAlias("customerID")
    private String customerId;
    @JsonAlias("UserName")
    private String username;
    @JsonProperty(value = "Pwd", access = JsonProperty.Access.WRITE_ONLY)
    private String pwd;
    @JsonFormat(pattern = "yyyy/M/d H:mm:ss")
    private LocalDateTime startDate;
    @JsonFormat(pattern = "yyyy/M/d H:mm:ss")
    private LocalDateTime endDate;
    @JsonAlias("endDatel2sh")
    @JsonFormat(pattern = "yyyy/M/d H:mm:ss")
    private LocalDateTime endDateL2Sh;
    @JsonAlias("endDatel2sz")
    @JsonFormat(pattern = "yyyy/M/d H:mm:ss")
    private LocalDateTime endDateL2Sz;
    @JsonAlias("Mobile")
    private String mobile;
    @JsonAlias("MaskMobile")
    private String maskMobile;
    /**
     * uid 用户ID
     */
    @JsonAlias("ParentID")
    private Long parentId;
    @JsonAlias("PID")
    private Long pid;
    @JsonAlias("SoftName")
    private String softName;
    @JsonAlias("notbefore")
    @JsonFormat(pattern = "yyyy/M/d H:mm:ss")
    private LocalDateTime notBefore;
    private Integer flags;
}
