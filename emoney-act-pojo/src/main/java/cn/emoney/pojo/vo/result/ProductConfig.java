package cn.emoney.pojo.vo.result;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.Date;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProductConfig {

    /**
     * 自增长Id
     */
    @JsonAlias("ProductVersionConfigId")
    public long productVersionConfigId;

    /**
     * 配置唯一KEY
     */
    @JsonAlias("ConfigKey")
    public String configKey;

    /**
     * 配置内容
     */
    @JsonAlias("ConfigContent")
    public String configContent;

    /**
     * 配置的格式 1.JSON 2.XML 3.Text
     */
    @JsonAlias("ConfigFormat")
    public int configFormat;

    /**
     * 配置名称
     */
    @JsonAlias("ConfigName")
    public String configName;

    /**
     * 配置描述
     */
    @JsonAlias("ConfigDesc")
    public String configDesc;

    /**
     * 创建时间
     * TODO 服务器返回格式存在不一致问题, 暂时忽略
     */
    @JsonIgnore
    @JsonAlias("CreateTime")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssX")
    public Date createTime;

    /**
     * 修改时间
     * TODO 服务器返回格式存在不一致问题, 暂时忽略
     */
    @JsonIgnore
    @JsonAlias("ModifyTime")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssX")
    public Date modifyTime;

    /**
     * 是否启用
     */
    @JsonAlias("IsEnabled")
    public Boolean isEnabled;

    /**
     * 是否删除
     */
    @JsonAlias("IsDeleted")
    public Boolean isDeleted;

    /**
     * 创建人
     */
    @JsonAlias("CreateUserName")
    public String createUserName;

    /**
     * 最后修改人
     */
    @JsonAlias("ModifyUserName")
    public String modifyUserName;
}
