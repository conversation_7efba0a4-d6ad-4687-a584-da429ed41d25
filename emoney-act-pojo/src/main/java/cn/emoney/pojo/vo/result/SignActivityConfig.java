package cn.emoney.pojo.vo.result;

import lombok.Data;

@Data
public class SignActivityConfig {

    /**
     * 获取活动有序id
     */
    private Integer activityId;

    /**
     * 活动开始日期
     */
    private String beginTime;

    /**
     * 活动结束日期
     */
    private String endTime;

    /**
     * 活动编号
     */
    private String actCode;

    /**
     * 活动持续周次
     */
    private Integer signWeek;

    /**
     * 7天PACCode
     */
    private String pacCode7Days;

    /**
     * 8天PACCode
     */
    private String pacCode8Days;

    /**
     * 活动描述
     */
    private String remark;

    /**
     * 活动状态 0未开始 1进行中 2已结束
     */
    private Integer status;

}
