package cn.emoney.pojo.vo;

import cn.emoney.pojo.bo.FirstClassCourseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FirstClassCourseVO {
    private Integer id;
    private Integer courseId;
    private String title;
    private String summary;
    private String teacherName;
    /**
     * 课程评论，首次用于 智盈第一课-周评
     */
    private String comment;
    private Integer parentId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    // 其他 //
    /**
     * 课程附加 URL
     */
    private List<FirstClassCourseDTO.ExtraUrl> extraUrls;

    // 视频相关 //
    /**
     * 视频源
     */
    private Integer videoSource;
    /**
     * 视频 ID
     */
    private String videoId;
    /**
     * 视频播放地址, 根据 parentId + webCastId 计算
     */
    @Deprecated
    @JsonIgnore
    private String videoUrl;
    private String videoCover;
    private String videoCoverApp;
    private Integer viewCount;
    private Integer bookBaseAmount;
    private Integer replayBaseAmount;
    private Boolean playDone;
}
