package cn.emoney.pojo.vo.survey;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 推送问卷回执
 *
 * <AUTHOR>
 * @date 2022/01/28 14:58
 **/
public class WjxSurveyReceiptVO {

    /**
     * 流水号
     */
    public String joinId ;

    /**
     * EM用户Uid
     */
    public int emUid;

    /**
     * 问卷编号
     */
    public Long activity;

    /**
     * 接受时间
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public DateTime receiveTime;

    /**
     * 总分
     */
    public int totalValue;

    /**
     * IP地址
     */
    public String ipAddress;


}
