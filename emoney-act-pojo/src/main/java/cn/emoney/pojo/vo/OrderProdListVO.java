package cn.emoney.pojo.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-12-03
 */
@Data
public class OrderProdListVO {
    public String ORDER_ID;
    public String CUSTOMER_NAME;
    public String MIDPWD;
    public String MID;
    public String EmCard;
    @JSONField(format="yyyy-MM-dd HH:mm:ss")
    public Date ORDADD_TIME;
    public String ORDADD_TIME_Start;
    public String ORDADD_TIME_End;
    @J<PERSON><PERSON>ield(format="yyyy-MM-dd HH:mm:ss")
    public Date StockUpDate;
    public String StockUpDate_Start;
    public String StockUpDate_End;
    public String ProdLineid;
    public String DetID;
    public String ProdLIneName;
    public String PRODID;
    public String PRODNAME;
    public String ACTIVITY_CODE;
    public String ACTIVITY_NAME;
    public Double SPRICE;
    public String OLDPROD;
    public Integer Refund_Sign;
    public String Cancel_Time;
    public String FromAppId;
    public String LOGINDATE;
    public String ProdType;
}
