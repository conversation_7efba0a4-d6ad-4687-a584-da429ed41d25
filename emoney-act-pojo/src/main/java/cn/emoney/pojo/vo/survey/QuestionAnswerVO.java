package cn.emoney.pojo.vo.survey;

import lombok.Data;

/**
 * 问题答案展示对象
 * 用于在页面上展示单个问题的题目和答案
 *
 * <AUTHOR>
 * @date 2025/01/11
 */
@Data
public class QuestionAnswerVO {

    /**
     * 题号（如：q1, q2, q3等）
     */
    private String questionNo;

    /**
     * 题目内容
     */
    private String questionText;

    /**
     * 答案内容（原始答案）
     */
    private String answerText;

    /**
     * 格式化后的答案内容（用于页面展示）
     */
    private String formattedAnswerText;

    /**
     * 答案类型（单选、多选、填空等）
     */
    private String answerType;

    /**
     * 题目类型（选择题、填空题等）
     */
    private String questionType;

    /**
     * 是否为必答题
     */
    private Boolean required;

    /**
     * 题目序号（用于排序）
     */
    private Integer questionOrder;

    /**
     * 选项列表（如果是选择题）
     */
    private String options;

    /**
     * 分值
     */
    private Integer score;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 构造函数
     */
    public QuestionAnswerVO() {
    }

    /**
     * 构造函数
     * @param questionNo 题号
     * @param questionText 题目内容
     * @param answerText 答案内容
     */
    public QuestionAnswerVO(String questionNo, String questionText, String answerText) {
        this.questionNo = questionNo;
        this.questionText = questionText;
        this.answerText = answerText;
        this.formattedAnswerText = answerText;
    }

    /**
     * 获取显示用的题号（去掉q前缀，转换为数字）
     */
    public String getDisplayQuestionNo() {
        if (questionNo != null && questionNo.startsWith("q")) {
            try {
                int num = Integer.parseInt(questionNo.substring(1));
                return String.valueOf(num);
            } catch (NumberFormatException e) {
                return questionNo;
            }
        }
        return questionNo;
    }

    /**
     * 获取格式化的答案文本
     */
    public String getFormattedAnswerText() {
        if (formattedAnswerText != null) {
            return formattedAnswerText;
        }
        return answerText;
    }

    /**
     * 判断是否为多选答案（包含逗号分隔）
     */
    public boolean isMultipleChoice() {
        return answerText != null && answerText.contains(",");
    }

    /**
     * 获取多选答案列表
     */
    public String[] getMultipleAnswers() {
        if (isMultipleChoice()) {
            return answerText.split(",");
        }
        return new String[]{answerText};
    }

    /**
     * 判断答案是否为空
     */
    public boolean isEmpty() {
        return answerText == null || answerText.trim().isEmpty();
    }
}
