package cn.emoney.pojo.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ValidateUserVO {
    /**
     * 到期日
     */
    @JsonFormat(pattern = "yyyy/M/dd HH:mm:ss")
    private LocalDateTime endDate;
    /**
     * 沪市 Level2 到期日
     */
    @JsonFormat(pattern = "yyyy/M/dd HH:mm:ss")
    @JsonAlias("endDatel2sh")
    private LocalDateTime endDateL2sh;
    /**
     * 深圳 Level2 到期日
     */
    @JsonFormat(pattern = "yyyy/M/dd HH:mm:ss")
    @JsonAlias("endDatel2sz")
    private LocalDateTime endDateL2sz;
    /**
     * 文本描述
     */
    private String msg;
}
