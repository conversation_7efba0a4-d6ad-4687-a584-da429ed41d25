package cn.emoney.pojo.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class FirstClassContentVO {
    private Long id;
    private String name;
    private List<ClassColumn> columns;
    private Date startTime;
    private Date endTime;
    // 0: 禁用, 1: 启用
    private Integer status;
    private boolean valid;
    private Map<String, Object> meta;

    @Data
    public static class ClassColumn {
        private Integer id;
        private String name;
    }
}
