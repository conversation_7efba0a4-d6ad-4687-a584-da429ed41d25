package cn.emoney.pojo.vo.survey;

import lombok.Data;
import java.util.List;

/**
 * 问卷展示数据传输对象
 * 用于在页面上展示问卷提交结果和答题内容
 *
 * <AUTHOR>
 * @date 2025/01/11
 */
@Data
public class SurveyDisplayVO {

    /**
     * 问卷名称
     */
    private String surveyName;

    /**
     * 提交时间（格式化后的字符串）
     */
    private String submitTime;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 总分
     */
    private Integer totalValue;

    /**
     * 耗时（秒）
     */
    private Integer timeTaken;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 来源
     */
    private String source;

    /**
     * 问卷ID
     */
    private Long activityId;

    /**
     * 流水号
     */
    private String joinId;

    /**
     * 答题列表
     */
    private List<QuestionAnswerVO> answers;

    /**
     * 处理状态（success/error）
     */
    private String status = "success";

    /**
     * 处理消息
     */
    private String message = "问卷提交成功！";

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 是否有答题内容
     */
    public boolean hasAnswers() {
        return answers != null && !answers.isEmpty();
    }

    /**
     * 获取答题数量
     */
    public int getAnswerCount() {
        return answers != null ? answers.size() : 0;
    }

    /**
     * 格式化耗时显示
     */
    public String getFormattedTimeTaken() {
        if (timeTaken == null || timeTaken <= 0) {
            return "未知";
        }
        
        if (timeTaken < 60) {
            return timeTaken + "秒";
        } else if (timeTaken < 3600) {
            int minutes = timeTaken / 60;
            int seconds = timeTaken % 60;
            return minutes + "分" + (seconds > 0 ? seconds + "秒" : "");
        } else {
            int hours = timeTaken / 3600;
            int minutes = (timeTaken % 3600) / 60;
            return hours + "小时" + (minutes > 0 ? minutes + "分" : "");
        }
    }

    /**
     * 获取地理位置信息
     */
    public String getLocationInfo() {
        if (province != null && city != null) {
            return province + " " + city;
        } else if (province != null) {
            return province;
        } else if (city != null) {
            return city;
        }
        return "未知";
    }
}
