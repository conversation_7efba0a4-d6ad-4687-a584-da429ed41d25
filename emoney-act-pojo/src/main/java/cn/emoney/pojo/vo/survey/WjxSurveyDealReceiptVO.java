package cn.emoney.pojo.vo.survey;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 问卷星问卷服务处理回执
 *
 * <AUTHOR>
 * @date 2022/01/28 15:22
 **/
public class WjxSurveyDealReceiptVO {

    /**
     * 本地记录ID
     */
    public int id;

    /**
     *问卷流水号
     */
    public String joinId ;

    /**
     * EM用户UID
     */
    public int emUid;

    /**
     * 问卷编号
     */
    public Long activity;

    /**
     * 处理持久化时间
     */

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public DateTime dealTime;

}
