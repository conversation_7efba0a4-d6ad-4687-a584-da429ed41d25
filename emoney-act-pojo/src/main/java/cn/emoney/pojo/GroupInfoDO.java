package cn.emoney.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-11-29
 */
@Data
public class GroupInfoDO {
    public Integer id;
    public String teacher;
    public String groupImgUrl;
    public Integer isValid;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public Date createTime;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public Date updateTime;
}
