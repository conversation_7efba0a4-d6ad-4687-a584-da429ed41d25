package cn.emoney.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName Lottery_PrizeLimit
 */
//@TableName(value ="Lottery_PrizeLimit")
@Data
public class LotteryPrizelimitDO implements Serializable {
    /**
     * 
     */
    //@TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    //@TableField(value = "batchno")
    private String batchno;

    /**
     * 
     */
    //@TableField(value = "group")
    private String group;

    /**
     * 
     */
    //@TableField(value = "zonestart")
    private Date zonestart;

    /**
     * 
     */
    //@TableField(value = "zoneend")
    private Date zoneend;

    /**
     * 
     */
    //@TableField(value = "prizelimit")
    private Integer prizelimit;

    /**
     * 
     */
    //@TableField(value = "prizesend")
    private Integer prizesend;

    /**
     * 
     */
    //@TableField(value = "writetime")
    private Date writetime;

    private int level;

    //@TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LotteryPrizelimitDO other = (LotteryPrizelimitDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBatchno() == null ? other.getBatchno() == null : this.getBatchno().equals(other.getBatchno()))
            && (this.getGroup() == null ? other.getGroup() == null : this.getGroup().equals(other.getGroup()))
            && (this.getZonestart() == null ? other.getZonestart() == null : this.getZonestart().equals(other.getZonestart()))
            && (this.getZoneend() == null ? other.getZoneend() == null : this.getZoneend().equals(other.getZoneend()))
            && (this.getPrizelimit() == null ? other.getPrizelimit() == null : this.getPrizelimit().equals(other.getPrizelimit()))
            && (this.getPrizesend() == null ? other.getPrizesend() == null : this.getPrizesend().equals(other.getPrizesend()))
            && (this.getWritetime() == null ? other.getWritetime() == null : this.getWritetime().equals(other.getWritetime()))
            && (this.getLevel() == 0 ? other.getLevel() == 0 : this.getLevel()==other.getLevel());
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBatchno() == null) ? 0 : getBatchno().hashCode());
        result = prime * result + ((getGroup() == null) ? 0 : getGroup().hashCode());
        result = prime * result + ((getZonestart() == null) ? 0 : getZonestart().hashCode());
        result = prime * result + ((getZoneend() == null) ? 0 : getZoneend().hashCode());
        result = prime * result + ((getPrizelimit() == null) ? 0 : getPrizelimit().hashCode());
        result = prime * result + ((getPrizesend() == null) ? 0 : getPrizesend().hashCode());
        result = prime * result + ((getWritetime() == null) ? 0 : getWritetime().hashCode());
        result = prime * result + ((getLevel() == 0) ? 0 : getLevel());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", batchno=").append(batchno);
        sb.append(", group=").append(group);
        sb.append(", zonestart=").append(zonestart);
        sb.append(", zoneend=").append(zoneend);
        sb.append(", prizelimit=").append(prizelimit);
        sb.append(", prizesend=").append(prizesend);
        sb.append(", writetime=").append(writetime);
        sb.append(", level=").append(level);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}