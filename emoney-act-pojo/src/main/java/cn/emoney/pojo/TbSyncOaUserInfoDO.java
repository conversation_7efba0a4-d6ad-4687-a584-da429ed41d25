package cn.emoney.pojo;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class TbSyncOaUserInfoDO {
    private String userid;

    private String usercode;

    private String usercname;

    private String userdept;

    private String deptname;

    private String manager;

    private String jobid;

    private String jobname;

    private String teamid;

    private String teamname;

    private String groupid;

    private String groupname;

    private String avatar;

    private String thumbAvatar;

    private String jobnum;

    private String qrCode;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date modtime;

    private String tid;

    private String sid;

    private String state;
}