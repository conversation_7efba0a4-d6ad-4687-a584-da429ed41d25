package cn.emoney.pojo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-04-10
 */
public class QSKHInfoDO {
    public int code;
    public Data data;
    public String msg;

    // Getters and Setters

    public static class Data {
        public String mobile;
        public Date openTime;
        public boolean isOpened;

        // Get<PERSON> and Setters
    }

    public static class Status {
        public Boolean canParticipate;
        public Boolean canLottery;
        public Boolean isOpened;
    }
}
