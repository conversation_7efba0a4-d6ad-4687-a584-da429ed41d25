package cn.emoney.pojo;;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
public class WjxSurveyResultDO {

	/**
	 * 自增序号
	 */
	private Integer id;

	/**
	 * 问卷ID
	 */
	private Long activity;

	/**
	 * 流水号
	 */
	private String joinid;

	/**
	 * 问卷名称
	 */
	private String name;

	/**
	 * IP地址
	 */
	private String ipaddress;

	/**
	 * 来源
	 */
	private String source;

	/**
	 * 第三方名称
	 */
	private String thirdusername;

	/**
	 * 昵称
	 */
	private String nickname;

	/**
	 * 跳转入参
	 */
	private String sojumpparam;

	/**
	 * 姓名
	 */
	private String realname;

	/**
	 * 部门
	 */
	private String reldept;

	/**
	 * 扩展信息
	 */
	private String relext;

	/**
	 * 省份
	 */
	private String province;

	/**
	 * 城市
	 */
	private String city;

	/**
	 * 第几个回答问卷的
	 */
	private Integer index;

	/**
	 * 耗时
	 */
	private Integer timetaken;

	/**
	 * 总分
	 */
	private Integer totalvalue;

	/**
	 * 答案
	 */
	private String answer;

	/**
	 * 签名
	 */
	private String sign;

	/**
	 * 益盟UID
	 */
	private Integer emuid;

	/**
	 * 益盟账号
	 */
	private String emno;

	/**
	 * 益盟pid
	 */
	private String pid;

	/**
	 * 业务场景
	 */
	private String emscene;

	/**
	 * 用户提交时间
	 */
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private java.util.Date submittime;

	/**
	 * 接受时间
	 */
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private java.util.Date receivetime;

	/**
	 * 写入时间
	 */
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
	private java.util.Date writetime;

	/**
	 * 是否删除
	 */
	private String isdel;

	/**
	 * 微信UnionId
	 */
	private String unionid;

	/**
	 * 扩展参数1
	 */
	private String ext1;

	/**
	 * 扩展参数2
	 */
	private String ext2;

	/**
	 * 扩展参数3
	 */
	private String ext3;

}
