package cn.emoney.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-03-18
 */
@Data
public class TyUserRegEventDO {

    public String customerID ;

    public String mobileX ;

    public String hardInfo ;

    public String packageName ;

    public int sid ;

    public int tid ;

    public String version ;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    public Date createTime ;

    public String clientIP ;

    public String agent ;
}
