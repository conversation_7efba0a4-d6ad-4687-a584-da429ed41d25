package cn.emoney.pojo;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-06-15
 */
public class JunePayStatusDO {
    //主订单号
    public String OrderId;

    //定金单支付时间
    public Date OrderTime;

    // 定金单金额
    public double OrderPrice;

    // 尾款订单时间
    public Date SubOrderTime;

    // 尾款订单金额
    public double SubOrderPrice;

    //尾款订单号
    public String SubOrderId;

    //商品编号
    public int GoodId;

    //订单状态
    public int OrderStatus;

    //尾款订单状态 1 已支付
    public int SubOrderStatus;
    /// <summary>
    /// 全款订单号
    /// </summary>
    public String FullOrderId;

    /// <summary>
    /// 全款订单时间
    /// </summary>
    public Date FullOrderTime;

    /// <summary>
    /// 全款订单金额
    /// </summary>
    public double FullOrderPrice;

    /// <summary>
    /// 全款订单状态 1 已支付
    /// </summary>
    public int FullOrderStatus;
}

