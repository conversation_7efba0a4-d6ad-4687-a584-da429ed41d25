package cn.emoney.pojo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-05
 */
@Data
public class ActDsSignDetail {
    public List<ActDsSignBenefitDO> dayList;
    public ActDsSignBenefitDO appendPrize;
    public int dayCount;

    public ActDsSignDetail() {
    }

    protected ActDsSignDetail(List<ActDsSignBenefitDO> dayList, ActDsSignBenefitDO appendPrize, int dayCount) {
        this.dayList = dayList;
        this.appendPrize = appendPrize;
        this.dayCount = dayCount;
    }
}
