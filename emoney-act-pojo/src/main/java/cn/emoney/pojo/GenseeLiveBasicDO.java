package cn.emoney.pojo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GenseeLiveBasicDO {
    /**
     * 直播Id
     */
    private String webcastId;
    /**
     * 展视原始链接
     */
    private String attendeeJoinUrl;
    /**
     * 录播时长
     */
    private String description;
    /**
     * 直播主题
     */
    private String subject;
    /**
     * 讲师名称
     */
    private String teacherName;
    /**
     * 直播开始时间
     */
    private LocalDateTime startTime;
    /**
     * 直播结束时间
     */
    private LocalDateTime endTime;
}
