package cn.emoney.pojo;

import lombok.Data;
import org.springframework.lang.Nullable;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class CourseTaskDO {
    private Long taskId;
    private Integer courseId;
    @Nullable
    private Duration liveTime, recordTime;
    private LocalDateTime startTime, endTime;

    public CourseTaskDO(Long taskId, Integer courseId) {
        this(taskId, courseId,
                Duration.ofMinutes(45),
                Duration.ofMinutes(45),
                LocalDate.of(2023, 8, 8),
                LocalDate.of(2023, 8, 31)
        );
    }

    public CourseTaskDO(Long taskId, Integer courseId,
                        @Nullable Duration liveTime, @Nullable Duration recordTime,
                        LocalDate startTime, LocalDate endTime) {
        this.taskId = taskId;
        this.courseId = courseId;
        this.liveTime = liveTime;
        this.recordTime = recordTime;
        this.startTime = startTime.atStartOfDay();
        this.endTime = endTime.atStartOfDay().plusDays(1);
    }
}
