package cn.emoney.pojo;

import com.fasterxml.jackson.annotation.*;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SCMOrderQueryDTO {
    /**
     * 加密手机号
     */
    @JsonProperty("MIDPWD")
    private String midPwd;

    /**
     * 下单开始时间
     */
    @JsonProperty("ORDADD_TIME_Start")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate ordAddTimeStart;

    /**
     * 下单结束时间
     */
    @JsonProperty("ORDADD_TIME_End")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate ordAddTimeEnd;

    /**
     * 备货开始时间
     */
    @JsonProperty("StockUpDate_Start")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate stockUpDateStart;

    /**
     * 备货结束时间
     */
    @JsonProperty("StockUpDate_End")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate stockUpDateEnd;

    /**
     * 退货开始时间
     */
    @JsonProperty("Cancel_Time_Start")
    private LocalDate cancelTimeStart;

    /**
     * 退货结束时间
     */
    @JsonProperty("Cancel_Time_End")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate cancelTimeEnd;

    /**
     * EM卡号
     */
    @JsonProperty("EmCard")
    private String emCard;

    /**
     * 产品线ID，多个逗号分隔
     */
    @JsonIgnore
    private List<String> prodLineId;

    /**
     * 可销售单元ID
     */
    @JsonIgnore
    private List<String> prodId;

    /**
     * 产品ID
     */
    @JsonIgnore
    private List<String> productId;

    /**
     * 活动包编号
     */
    @JsonIgnore
    private List<String> activityCode;

    /**
     * 退货标识（0 未退货; -1 已退货;1 全部）
     */
    @JsonProperty("Refund_Sign")
    private Integer refundSign;

    /**
     * 产品线ID，多个逗号分隔
     */
    @JsonGetter("ProdLineid")
    public String getProdLineIdStr() {
        return prodLineId == null ? null : String.join(",", prodLineId);
    }

    @JsonSetter("ProdLineid")
    public void setProdLineIdStr(String prodLineId) {
        this.prodLineId = Arrays.asList(prodLineId.split(","));
    }

    /**
     * 可销售单元ID，多个逗号分隔
     */
    @JsonGetter("PRODID")
    public String getProdIdStr() {
        return prodId == null ? null : String.join(",", prodId);
    }

    @JsonSetter("PRODID")
    public void setProdIdStr(String prodId) {
        this.prodId = Arrays.asList(prodId.split(","));
    }

    /**
     * 产品ID，多个逗号分隔
     */
    @JsonGetter("ProductID")
    public String getProductIdStr() {
        return productId == null ? null : String.join(",", productId);
    }

    @JsonSetter("ProductID")
    public void setProductIdStr(String productId) {
        this.productId = Arrays.asList(productId.split(","));
    }

    /**
     * 活动包编号，多个逗号分隔
     */
    @JsonGetter("ACTIVITY_CODE")
    public String getActivityCodeStr() {
        return activityCode == null ? null : String.join(",", activityCode);
    }

    @JsonSetter("ACTIVITY_CODE")
    public void setActivityCodeStr(String activityCode) {
        this.activityCode = Arrays.asList(activityCode.split(","));
    }
}