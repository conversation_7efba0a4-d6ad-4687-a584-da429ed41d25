package cn.emoney.pojo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName Lottery_WhiteList
 */
//@TableName(value ="Lottery_WhiteList")
@Data
public class LotteryWhitelistDO implements Serializable {
    /**
     * 
     */
    //@TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    //@TableField(value = "batchno")
    private String batchno;

    /**
     * 
     */
    //@TableField(value = "uid")
    private String uid;

    /**
     * 
     */
    //@TableField(value = "group")
    private String group;

    /**
     * 
     */
    //@TableField(value = "writetime")
    private Date writetime;

    //@TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LotteryWhitelistDO other = (LotteryWhitelistDO) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBatchno() == null ? other.getBatchno() == null : this.getBatchno().equals(other.getBatchno()))
            && (this.getUid() == null ? other.getUid() == null : this.getUid().equals(other.getUid()))
            && (this.getGroup() == null ? other.getGroup() == null : this.getGroup().equals(other.getGroup()))
            && (this.getWritetime() == null ? other.getWritetime() == null : this.getWritetime().equals(other.getWritetime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBatchno() == null) ? 0 : getBatchno().hashCode());
        result = prime * result + ((getUid() == null) ? 0 : getUid().hashCode());
        result = prime * result + ((getGroup() == null) ? 0 : getGroup().hashCode());
        result = prime * result + ((getWritetime() == null) ? 0 : getWritetime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", batchno=").append(batchno);
        sb.append(", uid=").append(uid);
        sb.append(", group=").append(group);
        sb.append(", writetime=").append(writetime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}