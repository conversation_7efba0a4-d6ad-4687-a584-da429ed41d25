package cn.emoney.pojo;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.lang.Nullable;

import java.time.LocalDateTime;

/**
 * 物流订单
 */
@Data
public class SCMOrderDTO {
    /**
     * 订单号码
     */
    @JsonAlias("ORDER_ID")
    private String orderId;

    /**
     * 客户名称
     */
    @JsonAlias("CUSTOMER_NAME")
    private String customerName;

    /**
     * 密文电话号码
     */
    @JsonAlias("MIDPWD")
    private String midPwd;

    /**
     * 加*电话号码
     */
    @JsonAlias("MID")
    private String mid;

    /**
     * EM账号
     */
    @JsonAlias("EmCard")
    private String emCard;

    /**
     * 录单时间
     */
    @JsonAlias("ORDADD_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss[.SSS]")
    private LocalDateTime ordAddTime;

    /**
     * 录单时间
     */
    @Nullable
    @JsonAlias("ORDADD_TIME_Start")
    private String ordAddTimeStart;

    /**
     * 录单时间
     */
    @Nullable
    @JsonAlias("ORDADD_TIME_End")
    private String ordAddTimeEnd;

    /**
     * 备货日期
     */
    @JsonAlias("StockUpDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss[.SSS]")
    private LocalDateTime stockUpDate;

    /**
     * 备货日期
     */
    @Nullable
    @JsonAlias("StockUpDate_Start")
    private String stockUpDateStart;

    /**
     * 备货日期
     */
    @Nullable
    @JsonAlias("StockUpDate_End")
    private String stockUpDateEnd;

    /**
     * 产品线ID
     */
    @JsonAlias("ProdLineid")
    private String prodLineId;

    /**
     * 订单明细ID
     */
    @JsonAlias("DetID")
    private String detId;

    /**
     * 产品线名称
     */
    @JsonAlias("ProdLIneName")
    private String prodLineName;

    /**
     * 可销售单元ID
     */
    @JsonAlias("PRODID")
    private String prodId;

    /**
     * 产品ID
     */
    @JsonAlias("ProductID")
    private String productId;

    /**
     * 产品名称
     */
    @JsonAlias("PRODNAME")
    private String prodName;

    /**
     * 活动代码
     */
    @JsonAlias("ACTIVITY_CODE")
    private String activityCode;

    /**
     * 活动名称
     */
    @JsonAlias("ACTIVITY_NAME")
    private String activityName;

    /**
     * 产品购买金额
     */
    @JsonAlias("SPRICE")
    private Integer sPrice;

    /**
     * 卡号
     */
    @JsonAlias("OLDPROD")
    private String oldProd;

    /**
     * 退货状态 （0 未退货; -1 已退货;1 全部）
     */
    @JsonAlias("Refund_Sign")
    private Integer refundSign;

    /**
     * 退款时间
     */
    @Nullable
    @JsonAlias("Cancel_Time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss[.SSS]")
    private LocalDateTime cancelTime;

    /**
     * 退款时间
     */
    @Nullable
    @JsonAlias("Cancel_Time_Start")
    private String cancelTimeStart;

    /**
     * 退款时间
     */
    @Nullable
    @JsonAlias("Cancel_Time_End")
    private String cancelTimeEnd;

    @Nullable
    @JsonAlias("FromAppId")
    private String fromAppId;


    @JsonAlias("LOGINDATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss[.SSS]")
    private LocalDateTime loginDate;

    /**
     * 可销售单元类型（A23001：软件；A23002：续费充值；A23004：产品换购；）
     */
    @JsonAlias("ProdType")
    private String prodType;

    @JsonAlias("VersionType")
    private String versionType;

    /**
     * 产品实际退款金额
     */
    @JsonAlias("RealBackPrice")
    private Integer realBackPrice;

    @Nullable
    @JsonAlias("WITCHSTATE")
    private String witchState;

    @Nullable
    @JsonAlias("STATUSID")
    private String statusId;

    @Nullable
    @JsonAlias("STATUS")
    private String status;

    @Nullable
    @JsonAlias("OrderSource")
    private String orderSource;

    @Nullable
    @JsonAlias("OrderDetailID")
    private String orderDetailId;

    @Nullable
    @JsonAlias("ChannelCode")
    private String channelCode;
}