package cn.emoney.pojo;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
public class PayOrderDO {
    private Integer id;

    private String orderid;

    private String customername;

    private byte[] midpwd;

    private String mid;

    private String emcode;

    private BigDecimal orderprice;

    private Integer orderstatus;

    private Date createtime;

    private Date updatetime;

    private Boolean isneedinvoice;

    private String invoicetype;

    private String invoicetitle;

    private String note;

    private String ordersource;

    private String tsrcode;

    private String extendedsource;

    private String dealercode;

    private String receivename;

    private String province;

    private String provincecode;

    private String city;

    private String citycode;

    private String area;

    private String areacode;

    private String zipcode;

    private String address;

    private String deptid;

    private Integer ordercreateid;

    private String ordercreatemsg;

    private String qq;

    private String channelcode;

    private String requesturl;

    private String couponcode;

    private String iseinvoice;

    private String billcompanyname;

    private String billtaxpayerno;

    private String billcompanyaddress;

    private String billcompanytelephone;

    private String billcompanybankname;

    private String billcompanybankaccount;

    private String inviocegroup;

    private String wxaccount;

    private String pid;

    private String tid;

    private String sid;
}