package cn.emoney.act.quest.logic.reward;

import cn.emoney.act.quest.logic.QuestReward;
import cn.emoney.act.quest.logic.QuestTarget;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PrivilegeReward implements QuestReward {
    private String code;
    private QuestTarget target;

    public PrivilegeReward(String code) {
        this.code = code;
    }

    @Override
    public String type() {
        return "privilege";
    }

    @Override
    public QuestTarget target() {
        return this.target;
    }
}
