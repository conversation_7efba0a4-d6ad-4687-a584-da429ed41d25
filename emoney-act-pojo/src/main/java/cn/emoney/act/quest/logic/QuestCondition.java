package cn.emoney.act.quest.logic;

import cn.emoney.act.quest.logic.condition.CountCondition;
import cn.emoney.act.quest.utils.WrappedList;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import java.util.Arrays;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.WRAPPER_OBJECT)
@JsonSubTypes({
        @JsonSubTypes.Type(value = QuestCondition.OneOf.class, name = "oneOf"),
        @JsonSubTypes.Type(value = CountCondition.class, name = "count"),
})
public interface QuestCondition {
    default QuestTarget target() {
        return null;
    }

    static QuestCondition oneOf(List<QuestCondition> subs) {
        return new OneOf(subs);
    }

    static QuestCondition oneOf(QuestCondition... subs) {
        return oneOf(Arrays.asList(subs));
    }

    final class OneOf extends WrappedList<QuestCondition> implements QuestCondition {
        @JsonCreator
        private OneOf(List<QuestCondition> subs) {
            super(subs);
        }
    }
}