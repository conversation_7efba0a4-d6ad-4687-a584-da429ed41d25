package cn.emoney.act.quest.utils;

import cn.emoney.act.quest.logic.QuestReward;
import cn.emoney.act.quest.logic.QuestTarget;

import java.util.Collection;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class QuestUtils {

    /**
     * 奖励过滤:
     * 1. 如果奖励不满足条件, 返回空流
     * 2. 如果奖励是多选一, 选一个不为空的奖励
     * 3. 如果奖励是多选多, 返回所有满足条件的奖励
     * 4. 如果奖励是单选, 返回奖励
     *
     * @param reward
     * @param predicate
     * @return
     */
    public static Stream<QuestReward> filterReward(QuestReward reward, Predicate<QuestTarget> predicate) {
        QuestTarget target = reward.target();
        if (target != null && !match(target, predicate)) {
            return Stream.empty();
        }
        if (reward instanceof QuestReward.AllOf) {
            return ((QuestReward.AllOf) reward).stream().flatMap(s -> filterReward(s, predicate));
        }
        if (reward instanceof QuestReward.OneOf) {
            return ((QuestReward.OneOf) reward).stream()
                    .map(s -> filterReward(s, predicate).collect(Collectors.toList()))
                    .filter(s -> !s.isEmpty())
                    .limit(1)
                    .flatMap(Collection::stream);
        }
        return Stream.of(reward);
    }

    public static Stream<QuestReward> filterReward(List<QuestReward> reward, Predicate<QuestTarget> predicate) {
        return filterReward((QuestReward) (reward instanceof QuestReward ? reward : QuestReward.allOf(reward)), predicate);
    }

    public static boolean match(QuestTarget target, Predicate<QuestTarget> predicate) {
        if (target instanceof QuestTarget.OneOf) {
            return ((QuestTarget.OneOf) target).stream().anyMatch(s -> match(s, predicate));
        }
        return predicate.test(target);
    }
}
