package cn.emoney.act.quest.utils;

import java.util.AbstractList;
import java.util.List;

public class WrappedList<T> extends AbstractList<T> {
    private final List<T> origin;

    public WrappedList(List<T> origin) {
        this.origin = origin;
    }

    @Override
    public T get(int index) {
        return origin.get(index);
    }

    @Override
    public int size() {
        return origin.size();
    }
}
