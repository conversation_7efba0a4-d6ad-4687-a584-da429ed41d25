package cn.emoney.act.quest.model;

import lombok.Data;
import org.springframework.lang.Nullable;

/**
 * 奖励状态
 * 1：成功
 * 0：不在奖励范围内
 * -1：失败
 */
@Data
public class RewardResult {
    private int code;
    @Nullable
    private String message;

    public boolean isMaySuccess() {
        return isSuccess() || this.code == 2;
    }

    public boolean isSuccess() {
        return this.code == 1;
    }

    public boolean isMatch() {
        return this.code != 0;
    }

    public boolean isFail() {
        return this.code == -1;
    }

    public static RewardResult maySuccess(String message) {
        RewardResult result = new RewardResult();
        result.setCode(2);
        result.setMessage(message);
        return result;
    }

    public static RewardResult success() {
        RewardResult result = new RewardResult();
        result.setCode(1);
        return result;
    }

    public static RewardResult notMatch() {
        RewardResult result = new RewardResult();
        result.setCode(0);
        return result;
    }

    public static RewardResult fail(String message) {
        RewardResult result = new RewardResult();
        result.setCode(-1);
        result.setMessage(message);
        return result;
    }
}
