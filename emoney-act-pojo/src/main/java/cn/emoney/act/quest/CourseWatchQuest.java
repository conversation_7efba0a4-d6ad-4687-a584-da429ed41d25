package cn.emoney.act.quest;

import cn.emoney.act.quest.logic.QuestReward;
import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

@Data
@Accessors(chain = true)
public class CourseWatchQuest {
    private Long questId;
    private List<Task> tasks;
    private List<Integer> userGroups;
    @Nullable
    private Duration liveDuration, recordDuration;
    @Nullable
    private LocalDateTime startTime, endTime;

    @JsonSetter("tasks")
    public CourseWatchQuest setTasks(List<Task> tasks) {
        this.tasks = tasks;
        return this;
    }

    public CourseWatchQuest setTasks(Task... tasks) {
        return setTasks(Arrays.asList(tasks));
    }

    public Stream<Task> stream() {
        if (tasks == null) {
            return Stream.empty();
        }
        return this.tasks.stream().peek(task -> {
            if (task.getStartTime() == null) {
                task.setStartTime(this.startTime);
            }
            if (task.getEndTime() == null) {
                task.setEndTime(this.endTime);
            }
            if (task.getLiveDuration() == null) {
                task.setLiveDuration(this.liveDuration);
            }
            if (task.getRecordDuration() == null) {
                task.setRecordDuration(this.recordDuration);
            }
        });
    }

    @Data
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class Task {
        private Long id;
        private Integer courseId;
        private List<QuestReward> rewards;

        @Nullable
        private Duration liveDuration, recordDuration;
        @Nullable
        private LocalDateTime startTime, endTime;

        public Task(Integer id, Integer courseId) {
            this((long) id, courseId);
        }

        public Task(Long id, Integer courseId) {
            this.id = id;
            this.courseId = courseId;
        }

        @JsonSetter("rewards")
        public Task setRewards(List<QuestReward> rewards) {
            this.rewards = rewards;
            return this;
        }

        public Task setRewards(QuestReward... rewards) {
            return setRewards(Arrays.asList(rewards));
        }
    }

}
