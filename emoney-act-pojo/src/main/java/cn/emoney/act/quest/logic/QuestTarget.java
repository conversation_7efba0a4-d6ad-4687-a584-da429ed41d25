package cn.emoney.act.quest.logic;

import cn.emoney.act.quest.logic.target.PidTarget;
import cn.emoney.act.quest.utils.WrappedList;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import java.util.Arrays;
import java.util.List;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.WRAPPER_OBJECT)
@JsonSubTypes({
        @JsonSubTypes.Type(value = QuestTarget.OneOf.class, name = "oneOf"),

        @JsonSubTypes.Type(value = PidTarget.class, name = "pid"),
})
public interface QuestTarget {
    static QuestTarget oneOf(List<QuestTarget> subs) {
        return new QuestTarget.OneOf(subs);
    }

    static QuestTarget oneOf(QuestTarget... subs) {
        return oneOf(Arrays.asList(subs));
    }

    final class OneOf extends WrappedList<QuestTarget> implements QuestTarget {
        @JsonCreator
        private OneOf(List<QuestTarget> subs) {
            super(subs);
        }
    }
}
