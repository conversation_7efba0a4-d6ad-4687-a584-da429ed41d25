package cn.emoney.act.quest.logic.condition;

import cn.emoney.act.quest.logic.QuestCondition;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonGetter;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode
public class CountCondition implements QuestCondition, Comparable<Integer> {
    private final Integer value;

    @JsonCreator
    public CountCondition(Integer value) {
        this.value = value;
    }

    @JsonGetter("value")
    public Integer value() {
        return this.value;
    }

    @Override
    public int compareTo(Integer o) {
        return Integer.compare(this.value, o);
    }
}
