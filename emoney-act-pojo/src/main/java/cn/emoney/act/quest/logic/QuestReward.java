package cn.emoney.act.quest.logic;

import cn.emoney.act.quest.logic.reward.CouponReward;
import cn.emoney.act.quest.logic.reward.PointReward;
import cn.emoney.act.quest.logic.reward.PrivilegeReward;
import cn.emoney.act.quest.utils.WrappedList;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import java.util.Arrays;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.WRAPPER_OBJECT)
@JsonSubTypes({
        @JsonSubTypes.Type(value = QuestReward.OneOf.class, name = "oneOf"),
        @JsonSubTypes.Type(value = QuestReward.AllOf.class, name = "allOf"),

        @JsonSubTypes.Type(value = PointReward.class, name = "point"),
        @JsonSubTypes.Type(value = PrivilegeReward.class, name = "privilege"),
        @JsonSubTypes.Type(value = CouponReward.class, name = "coupon"),
})
public interface QuestReward {
    String type();

    default QuestTarget target() {
        return null;
    }

    static QuestReward.OneOf oneOf(List<QuestReward> subs) {
        return new OneOf(subs);
    }

    static QuestReward.OneOf oneOf(QuestReward... subs) {
        return oneOf(Arrays.asList(subs));
    }

    static QuestReward.AllOf allOf(List<QuestReward> subs) {
        if (subs instanceof QuestReward.AllOf) {
            return (QuestReward.AllOf) subs;
        }
        return new AllOf(subs);
    }

    static QuestReward.AllOf allOf(QuestReward... subs) {
        return allOf(Arrays.asList(subs));
    }

    final class OneOf extends WrappedList<QuestReward> implements QuestReward {
        @JsonCreator
        private OneOf(List<QuestReward> subs) {
            super(subs);
        }

        @Override
        public String type() {
            return "oneOf";
        }
    }

    final class AllOf extends WrappedList<QuestReward> implements QuestReward {
        @JsonCreator
        private AllOf(List<QuestReward> subs) {
            super(subs);
        }

        @Override
        public String type() {
            return "allOf";
        }
    }
}
