package cn.emoney.act.exception;

import cn.emoney.common.result.PointResult;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;
import java.util.List;

@Getter
@ToString
public class PointException extends RuntimeException {
    private static final List<String> CONFLICT_508_MSG = Arrays.asList("今天积分参与次数超限，请明天早点来吧！", "任务已完成或参与次数超限");
    private final String code;
    private final boolean isConflict;

    public PointException(PointResult<?> result) {
        super(result.getMsg());
        this.code = result.getCode();
        this.isConflict = "508".equals(this.code) && CONFLICT_508_MSG.contains(result.getMsg());
    }
}
