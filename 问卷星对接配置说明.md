# 问卷星对接配置说明

## 概述

本系统支持问卷星的两种数据传输格式：
1. **加密版本**：数据经过AES加密传输（需要配置解密密钥）
2. **非加密版本**：数据以明文JSON格式传输（无需配置解密密钥）

系统会自动检测数据格式并进行相应处理。

## 配置方式

### 1. 非加密版本（推荐）

如果您使用的是非加密版本的问卷星，配置非常简单：

#### 系统配置
在 `application-*.properties` 中：
```properties
# 问卷星配置
# 非加密版本无需配置密钥，留空即可
wjx.aes.key=
```

#### 问卷星后台配置
1. 登录问卷星后台
2. 进入问卷设置 → 跳转设置
3. 设置跳转URL：`http://your-domain/activity/survey/wjx-redirect`
4. 勾选"POST答卷数据到该地址"
5. 保存设置

### 2. 加密版本

如果您使用的是加密版本的问卷星：

#### 系统配置
在 `application-*.properties` 中：
```properties
# 问卷星配置
# 加密版本需要配置从问卷星获取的AES密钥
wjx.aes.key=your-actual-aes-key-here
```

#### 问卷星后台配置
1. 登录问卷星后台
2. 进入问卷设置 → 跳转设置
3. 设置跳转URL：`http://your-domain/activity/survey/wjx-redirect`
4. 勾选"POST答卷数据到该地址"
5. 获取AES解密密钥（在设置界面会显示）
6. 将密钥配置到系统中
7. 保存设置

## 数据处理流程

```mermaid
graph TD
    A[问卷星POST数据] --> B[系统接收]
    B --> C{检测数据格式}
    C -->|JSON格式| D[直接使用]
    C -->|非JSON格式| E{是否配置密钥}
    E -->|已配置| F[AES解密]
    E -->|未配置| G[直接使用原始数据]
    F --> H[解析成功]
    G --> I[尝试解析]
    D --> H
    H --> J[推送到Kafka]
    H --> K[展示页面]
    I -->|成功| J
    I -->|失败| L[错误处理]
```

## 测试验证

### 1. 健康检查
访问：`GET http://your-domain/activity/survey/wjx-redirect/health`

预期响应：
```json
{
    "code": "1",
    "message": "服务正常",
    "data": "2025-01-11 10:30:00"
}
```

### 2. 测试页面
访问：`GET http://your-domain/activity/survey/wjx-redirect/test`

会显示一个测试的问卷结果页面。

### 3. 实际测试
1. 在问卷星创建一个测试问卷
2. 配置跳转URL到您的系统
3. 填写并提交问卷
4. 检查是否正确跳转到结果页面
5. 检查后台日志确认数据是否正确推送到Kafka

## 常见问题

### Q1: 如何判断我使用的是哪种版本？
**A**: 联系问卷星商务确认，或者：
- 先按非加密版本配置（不设置密钥）
- 如果数据处理失败，再尝试加密版本配置

### Q2: 数据解析失败怎么办？
**A**: 检查以下几点：
1. 问卷星跳转URL配置是否正确
2. 是否勾选了"POST答卷数据到该地址"
3. 如果是加密版本，检查AES密钥是否正确
4. 查看系统日志获取详细错误信息

### Q3: 页面显示异常怎么办？
**A**: 
1. 检查模板文件是否正确部署
2. 检查答案解析服务是否正常工作
3. 查看浏览器控制台和系统日志

### Q4: 数据没有进入Kafka队列？
**A**:
1. 检查Kafka配置是否正确
2. 检查现有的SurveyService是否正常工作
3. 查看异步处理的日志信息

## 日志监控

关键日志信息：
- `接收到问卷星重定向请求`：确认请求到达
- `检测到非加密格式数据`：确认数据格式
- `AES解密成功`：确认解密成功
- `问卷数据解析成功`：确认数据解析
- `问卷数据已成功推送到Kafka队列`：确认业务处理
- `问卷答案解析成功`：确认页面数据准备

## 联系支持

如果遇到问题，请提供以下信息：
1. 问卷星版本信息
2. 系统日志（包含错误信息）
3. 问卷星配置截图
4. 测试问卷的具体数据

---

**注意**：本系统设计为向后兼容，支持未来问卷星可能的格式变更。
