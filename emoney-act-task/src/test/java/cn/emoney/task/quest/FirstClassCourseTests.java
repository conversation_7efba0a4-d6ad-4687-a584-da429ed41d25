package cn.emoney.task.quest;

import cn.emoney.pojo.bo.FirstClassCourseDTO;
import cn.emoney.service.FirstClassCourseService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Sort;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest
public class FirstClassCourseTests {
    @Autowired
    private FirstClassCourseService service;

    @Test
    void listCourse() {
        List<FirstClassCourseDTO> courses = service.listCourse(3,
                Sort.by(Sort.Direction.DESC, "order")
                        .and(Sort.by(Sort.Direction.DESC, "id")), 30);
        Assertions.assertFalse(courses.isEmpty());
        List<FirstClassCourseDTO> collect = courses.stream().sorted(
                        Comparator.comparing(FirstClassCourseDTO::getOrder, Comparator.nullsLast(Comparator.reverseOrder()))
                                .thenComparing(FirstClassCourseDTO::getId, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        Assertions.assertEquals(courses, collect);
    }

    @Test
    void getCourse() {
        Assertions.assertNotNull(service.findById(56).orElse(null));
        Assertions.assertNull(service.findById(999).orElse(null));
    }
}
