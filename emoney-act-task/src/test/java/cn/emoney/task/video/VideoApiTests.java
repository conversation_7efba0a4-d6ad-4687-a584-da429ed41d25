package cn.emoney.task.video;

import cn.emoney.act.client.video.VideoStreamClient;
import cn.emoney.act.client.video.dto.UserLiveStatisticsDTO;
import cn.emoney.act.client.video.dto.UserVodStatisticsDTO;
import lombok.var;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.stream.Collectors;

@SpringBootTest
public class VideoApiTests {

    @Autowired
    private VideoStreamClient client;


    @Test
    public void testNewPlayProgressRequest() {
        var aa = client.queryUserVodStatistics("4f8fb29f7386417b860af770b421c2d7",
                        LocalDate.of(2024, 8, 1),
                        LocalDate.of(2024, 8, 7)
                ).getDetail().stream()
                .sorted(Comparator.comparing(UserVodStatisticsDTO::getTotalPlayDuration).reversed())
                .collect(Collectors.toList());
        System.out.println(aa.size());

        var bb = client.queryUserLiveStatistics("f2acd7cc23624799b3a71b4108bf284c",
                        LocalDate.of(2024, 8, 1),
                        LocalDate.of(2024, 8, 7)
                ).getDetail().stream()
                .sorted(Comparator.comparing(UserLiveStatisticsDTO::getTotalPlayDuration).reversed())
                .collect(Collectors.toList());
        System.out.println(bb.size());
    }
}
