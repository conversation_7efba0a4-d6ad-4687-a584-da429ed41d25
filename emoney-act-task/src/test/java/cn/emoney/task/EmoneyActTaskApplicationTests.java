package cn.emoney.task;

import cn.emoney.pojo.PayOrderDO;
import cn.emoney.service.PayService;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.List;

@SpringBootTest
class EmoneyActTaskApplicationTests {

    @Autowired
    private PayService payService;

    @Test
    void contextLoads() {
    }

}
