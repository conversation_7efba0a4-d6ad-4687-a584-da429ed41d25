package cn.emoney.task.jobhandler;

import cn.emoney.common.exception.BaseRuntimeException;
import cn.emoney.common.utils.GenseeUtils;
import cn.emoney.pojo.CourseWatchDO;
import cn.emoney.pojo.bo.CourseDetailDTO;
import cn.emoney.pojo.bo.FirstClassCourseDTO;
import cn.emoney.service.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Stream;

@Slf4j
@Component
public class AutoFirstClassTaskCompleteJob {
    private final CourseService courseService;
    private final GenseeService genseeService;
    private final UserGroupService userGroupService;
    private final FirstClassRollService firstClassRollService;
    private final FirstClassCourseService firstClassCourseService;


    public AutoFirstClassTaskCompleteJob(CourseService courseService,
                                         GenseeService genseeService, UserGroupService userGroupService,
                                         FirstClassRollService firstClassRollService,
                                         FirstClassCourseService firstClassCourseService) {
        this.courseService = courseService;
        this.genseeService = genseeService;
        this.userGroupService = userGroupService;
        this.firstClassRollService = firstClassRollService;
        this.firstClassCourseService = firstClassCourseService;
    }

    /**
     * 智盈第一课从展视拉数据完成任务
     *
     * @param param
     * @return
     */
    @XxlJob("AutoFirstClassTaskCompleteJob")
    public ReturnT<String> onCall(String param) {
        LocalDate targetDate = StringUtils.hasText(param) ? LocalDate.parse(param) : LocalDate.now().minusDays(1);
        doTask(10021L, 14, 2216,
                LocalDate.of(2024, 5, 10).atStartOfDay(),
                LocalDate.of(2024, 6, 5).plusDays(1).atStartOfDay().minusSeconds(1),
                targetDate);
        doTask(10023L, 15, 2265,
                LocalDate.of(2024, 5, 29).atStartOfDay(),
                LocalDate.of(2024, 7, 5).plusDays(1).atStartOfDay().minusSeconds(1),
                targetDate);
        doTask(10024L, 16, 2265,
                LocalDate.of(2024, 7, 8).atStartOfDay(),
                LocalDate.of(2024, 8, 4).plusDays(1).atStartOfDay().minusSeconds(1),
                targetDate);
        return ReturnT.SUCCESS;
    }

    private static final int GUEST_USER_GROUP = 2243;

    private void doTask(Long questId, Integer columnId, Integer userGroup, LocalDateTime inTimeHead, LocalDateTime inTimeTail, LocalDate targetDate) {
        List<FirstClassCourseDTO> courses = firstClassCourseService.listCourse(columnId, 99);
        userGroupService.preloadGroupUsers(userGroup);
        userGroupService.preloadGroupUsers(GUEST_USER_GROUP);
        Stream<Pair<Integer, Stream<Long>>> pairStream = courses.stream().map(course -> {
            Stream<Long> users = streamUsers(course.getCourseId(), Duration.ofMinutes(15), inTimeHead, inTimeTail, targetDate)
                    .filter(uid -> userGroupService.anyMatch(uid, userGroup, GUEST_USER_GROUP));
            return Pair.of(course.getId(), users);
        });
        pairStream.forEach(pair -> {
            Integer subId = pair.getFirst();
            LongAdder completed = new LongAdder(),
                    success = new LongAdder(),
                    fail = new LongAdder(),
                    ignore = new LongAdder();
            pair.getSecond().forEach(uid -> {
                try {
                    Pair<List<Integer>, List<Integer>> result = firstClassRollService.completeCourseDirect(questId, uid, Collections.singletonList(subId), "act-task");
                    if (!result.getFirst().isEmpty()) {
                        completed.increment();
                    } else if (!result.getSecond().isEmpty()) {
                        success.increment();
                    } else {
                        log.warn("course may failed: questId={}, uid={}, subId={}", questId, uid, subId);
                        fail.increment();
                    }
                } catch (BaseRuntimeException ignored) {
                    ignore.increment();
                } catch (Exception e) {
                    fail.increment();
                    log.error("course failed: questId={}, uid={}, subId={}", questId, uid, subId, e);
                }
            });
            log.info("Complete course: questId={}, subId={}, success={}, fail={}, total={}, ignored={}",
                    questId, subId, success.sum(), fail.sum(), completed.sum() + success.sum(), ignore.sum());
            XxlJobLogger.log("Complete course: questId={}, subId={}, success={}, fail={}, total={}, ignored={}",
                    questId, subId, success.sum(), fail.sum(), completed.sum() + success.sum(), ignore.sum());
        });
    }

    private Stream<Long> streamUsers(Integer courseId, Duration minDuration,
                                     LocalDateTime inTimeHead, LocalDateTime inTimeTail,
                                     LocalDate targetDate) {
        CourseDetailDTO course = courseService.detail(courseId);
        if (!Optional.ofNullable(course)
                .map(CourseDetailDTO::getCourseBeginTime)
                .filter(time -> !time.toLocalDate().isAfter(targetDate))
                .isPresent()
        ) {
            return Stream.empty();
        }
        Stream<List<CourseWatchDO>> liveStream = GenseeUtils.findWebCastId(course.getCourseLiveUrl())
                .map(id -> genseeService.streamLive(id, minDuration, 0, 1000))
                .orElse(Stream.empty());
        Stream<List<CourseWatchDO>> replayStream = GenseeUtils.findWebCastId(course.getCoursePlayBackUrl())
                .map(id -> genseeService.streamReplay(id, minDuration, inTimeHead, inTimeTail, 0, 1000))
                .orElse(Stream.empty());
        return Stream.concat(liveStream, replayStream)
                .flatMap(List::stream)
                .map(CourseWatchDO::getUid)
                .distinct();
    }
}
