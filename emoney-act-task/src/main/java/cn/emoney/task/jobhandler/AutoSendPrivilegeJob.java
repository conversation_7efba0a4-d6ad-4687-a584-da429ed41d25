package cn.emoney.task.jobhandler;

import cn.emoney.pojo.InviteAwardDetailDO;
import cn.emoney.pojo.InviteInfoDO;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.OrderProdListDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.OrderProdListVO;
import cn.emoney.service.InviteService;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.UserService;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-12-02
 */
@Component
@Slf4j
public class AutoSendPrivilegeJob {

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private InviteService inviteService;

    @Autowired
    private UserService userService;

    @XxlJob("AutoSendPrivilegeJobHandler")
    public ReturnT<String> AutoSendPrivilegeJobHandler(String param) throws Exception {
        try {
            XxlJobLogger.log("AutoSendPrivilegeJob, Started:" + DateUtil.formatDateTime(new Date()));
            //获取前2个小时的所有物流订单
            OrderProdListDTO orderProdListDTO = new OrderProdListDTO();
            orderProdListDTO.setORDADD_TIME_Start(DateTime.now().offset(DateField.HOUR, -2).toString("yyyy-MM-dd HH:mm:ss"));
            orderProdListDTO.setORDADD_TIME_End(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
            orderProdListDTO.setRefund_Sign(0);
            List<OrderProdListVO> orderProdListVOS = logisticsService.queryOrderProdList(orderProdListDTO);
            if (orderProdListVOS != null) {
                //筛选出智盈订单（智盈、智盈大师）
                orderProdListVOS = orderProdListVOS.stream().filter(x -> (x.getPRODID().contains("88801") || x.getPRODID().contains("88802") || x.getPRODID().contains("88808")) && x.getSPRICE() > 100).collect(Collectors.toList());
                for (OrderProdListVO orderProdListVO : orderProdListVOS) {
                    InviteInfoDO inviteInfoDO = inviteService.queryByBeInvited(orderProdListVO.getMIDPWD());
                    if (inviteInfoDO != null) {
                        //查询赠送记录
                        List<InviteAwardDetailDO> inviteAwardDetailDOS = inviteService.getByMobileXAndOrderMobile(inviteInfoDO.getMobileX(), inviteInfoDO.beInvited);

                        //判断智盈订单是否已赠送过奖励
                        if (inviteAwardDetailDOS.stream().anyMatch(x -> x.sendType.equals("Buy")) && orderProdListVO.getPRODID().contains("88801")) {
                            continue;
                        }
                        //判断大师订单是否已赠送过奖励
                        if (inviteAwardDetailDOS.stream().anyMatch(x -> x.sendType.equals("Upgrade")) && (orderProdListVO.getPRODID().contains("88802") || orderProdListVO.getPRODID().contains("88808"))) {
                            continue;
                        }
                        //获取用户版本
                        String pid = userService.GetAccountPID(inviteInfoDO.mobileX);

                        InviteAwardDetailDO inviteAwardDetailDO = new InviteAwardDetailDO();
                        inviteAwardDetailDO.setMobileX(inviteInfoDO.getMobileX());
                        inviteAwardDetailDO.setMobileVer(pid);
                        inviteAwardDetailDO.setOrderMobileX(inviteInfoDO.beInvited);
                        inviteAwardDetailDO.setOrderProduct(orderProdListVO.getPRODID());
                        inviteAwardDetailDO.setOrderId(orderProdListVO.getORDER_ID());
                        inviteAwardDetailDO.setDetId(orderProdListVO.getDetID());
                        inviteAwardDetailDO.setStatus(0);
                        inviteAwardDetailDO.setCreateTime(orderProdListVO.ORDADD_TIME);


                        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
                        sendPrivilegeDTO.setAppId("a002");
                        sendPrivilegeDTO.setApplyUserID("scb_public");
                        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
                        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();
                        createActivityGrantApplyAccountDTO.setAccountType(2);
                        createActivityGrantApplyAccountDTO.setMID(inviteInfoDO.getMobileX());
                        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
                        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

                        switch (orderProdListVO.getPRODID().substring(5)) {
                            case "88808":
                                switch (pid) {
                                    case "*********":
                                        inviteAwardDetailDO.setSendType("Upgrade");
                                        inviteAwardDetailDO.setAward("5个月智盈大师L1使用期");
                                        sendPrivilegeDTO.setActivityID("1");
                                        break;
                                    case "*********":
                                        inviteAwardDetailDO.setSendType("Upgrade");
                                        inviteAwardDetailDO.setAward("78天智盈大师L2使用期");
                                        sendPrivilegeDTO.setActivityID("2");
                                        break;
                                    case "*********":
                                        inviteAwardDetailDO.setSendType("Upgrade");
                                        inviteAwardDetailDO.setAward("2年智盈使用期");
                                        sendPrivilegeDTO.setActivityID("3");
                                        break;
                                    case "153000000":
                                    case "100000000":
                                        inviteAwardDetailDO.setSendType("Upgrade");
                                        inviteAwardDetailDO.setAward("78天金融平台使用期");
                                        sendPrivilegeDTO.setActivityID("4");
                                        break;
                                    case "202100000":
                                    case "202200000":
                                    case "202000000":
                                        inviteAwardDetailDO.setSendType("Upgrade");
                                        inviteAwardDetailDO.setAward("1个月金融平台使用期");
                                        sendPrivilegeDTO.setActivityID("5");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case "88802":
                                switch (pid) {
                                    case "*********":
                                        inviteAwardDetailDO.setSendType("Upgrade");
                                        inviteAwardDetailDO.setAward("5个月智盈大师L1使用期");
                                        sendPrivilegeDTO.setActivityID("");
                                        break;
                                    case "*********":
                                        inviteAwardDetailDO.setSendType("Upgrade");
                                        inviteAwardDetailDO.setAward("78天智盈大师L2使用期");
                                        sendPrivilegeDTO.setActivityID("");
                                        break;
                                    case "*********":
                                        inviteAwardDetailDO.setSendType("Upgrade");
                                        inviteAwardDetailDO.setAward("2年智盈使用期");
                                        sendPrivilegeDTO.setActivityID("");
                                        break;
                                    case "153000000":
                                    case "100000000":
                                        inviteAwardDetailDO.setSendType("Upgrade");
                                        inviteAwardDetailDO.setAward("78天金融平台使用期");
                                        sendPrivilegeDTO.setActivityID("");
                                        break;
                                    case "202100000":
                                    case "202200000":
                                    case "202000000":
                                        inviteAwardDetailDO.setSendType("Upgrade");
                                        inviteAwardDetailDO.setAward("1个月金融平台使用期");
                                        sendPrivilegeDTO.setActivityID("");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            case "88801":
                                switch (pid) {
                                    case "*********":
                                        inviteAwardDetailDO.setSendType("Buy");
                                        inviteAwardDetailDO.setAward("1个月智盈大师L1使用期");
                                        sendPrivilegeDTO.setActivityID("");
                                        break;
                                    case "*********":
                                        inviteAwardDetailDO.setSendType("Buy");
                                        inviteAwardDetailDO.setAward("15天智盈大师L2使用期");
                                        sendPrivilegeDTO.setActivityID("");
                                        break;
                                    case "*********":
                                        inviteAwardDetailDO.setSendType("Buy");
                                        inviteAwardDetailDO.setAward("1年智盈使用期");
                                        sendPrivilegeDTO.setActivityID("");
                                        break;
                                    case "153000000":
                                    case "100000000":
                                        inviteAwardDetailDO.setSendType("Buy");
                                        inviteAwardDetailDO.setAward("15天金融平台使用期");
                                        sendPrivilegeDTO.setActivityID("");
                                        break;
                                    case "202100000":
                                    case "202200000":
                                    case "202000000":
                                        inviteAwardDetailDO.setSendType("Buy");
                                        inviteAwardDetailDO.setAward("2个月金融平台使用期");
                                        sendPrivilegeDTO.setActivityID("");
                                        break;
                                    default:
                                        break;
                                }
                                break;
                            default:
                                break;
                        }

                        //if (!inviteAwardDetailDO.getAward().isEmpty()) {
                        //    if (logisticsService.sendPrivilege(sendPrivilegeDTO)) {
                        //        inviteAwardDetailDO.setStatus(1);
                        //    }
                        //
                        //}
                    }
                }
            }

            XxlJobLogger.log("AutoSendPrivilegeJob, End:" + DateUtil.formatDateTime(new Date()));
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("AutoSendPrivilegeJobHandler error:" + e);
        }
        return ReturnT.FAIL;
    }
}
