package cn.emoney.task.jobhandler;

import cn.emoney.act.client.video.VideoStreamClient;
import cn.emoney.act.client.video.dto.UserLiveStatisticsDTO;
import cn.emoney.act.client.video.dto.UserVodStatisticsDTO;
import cn.emoney.act.exception.PointException;
import cn.emoney.act.quest.CourseWatchQuest;
import cn.emoney.act.quest.logic.QuestReward;
import cn.emoney.act.quest.logic.reward.PointReward;
import cn.emoney.act.quest.model.RewardResult;
import cn.emoney.common.utils.GenseeUtils;
import cn.emoney.common.utils.StreamUtils;
import cn.emoney.mapper.activity.Act588benifitrecordMapper;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.CourseWatchDO;
import cn.emoney.pojo.bo.CourseDetailDTO;
import cn.emoney.service.*;
import cn.emoney.service.redis.RedisTemplateSupplier;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2021-12-22
 */
@Slf4j
@Component
public class AutoCourseWatchPointJob {
    private final CourseWatchQuestService service;
    private final CourseService courseService;
    private final GenseeService genseeService;
    private final VideoStreamClient videoStream;
    private final UserGroupService userGroupService;
    private final PointService pointService;
    private final HashOperations<String, Long, String> operations;
    private final Act588benifitrecordMapper benifitrecordMapper;

    // 应对最后一天跨天播放 或 延迟记录，追溯 3 天
    private final int appendDays = 3;
    private final int sqlBatchSize = 1000;

    public AutoCourseWatchPointJob(CourseWatchQuestService service,
                                   CourseService courseService,
                                   GenseeService genseeService,
                                   VideoStreamClient videoStream,
                                   UserGroupService userGroupService,
                                   PointService pointService,
                                   Act588benifitrecordMapper benifitrecordMapper,
                                   RedisTemplateSupplier supplier) {
        this.service = service;
        this.courseService = courseService;
        this.genseeService = genseeService;
        this.videoStream = videoStream;
        this.userGroupService = userGroupService;
        this.pointService = pointService;
        this.benifitrecordMapper = benifitrecordMapper;
        RedisTemplate<String, String> template = supplier.get(RedisSerializer.string(), "act:task:courseWatchPoint");
        template.setHashKeySerializer(new GenericToStringSerializer<>(Long.class));
        this.operations = template.opsForHash();
    }

    @XxlJob("AutoVideoWatchPointHandler")
    public ReturnT<String> autoVideoWatchPointJob(String param) {
        LocalDate targetDate = StringUtils.hasText(param) ? LocalDate.parse(param) : LocalDate.now().minusDays(1);
        int cores = Runtime.getRuntime().availableProcessors();
        ExecutorService executor = Executors.newFixedThreadPool(cores * 4);
        List<CompletableFuture<Void>> taskFutures = new ArrayList<>();

        try {
            List<CourseWatchQuest> quests = service.listQuests();
            quests.stream()
                    .filter(s -> checkDateInRange(s.getStartTime(), s.getEndTime(), targetDate, appendDays))
                    .forEach(quest -> {
                        this.log("Prepare Quest: {}", quest);
                        try {
                            // 原始数据准备
                            List<PreparedQuestTask> prepared = prepareQuest(quest, targetDate)
                                    .peek(t -> this.log("[{}-{}] 直播记录: {}, 录播记录: {}, 合并人数: {}",
                                            quest.getQuestId(), t.getTask().getId(),
                                            t.getLiveCount(), t.getRecordCount(),
                                            t.getUidList().size()))
                                    .collect(Collectors.toList());
                            // 预载用户画像组
                            List<Integer> userGroups = quest.getUserGroups();
                            if (userGroups != null && !userGroups.isEmpty()) {
                                userGroups.forEach(userGroupService::preloadGroupUsers);
                            }
                            // 原始数据过滤
                            List<AbstractMap.SimpleEntry<CourseWatchQuest.Task, List<Long>>> filtered = prepared.stream().map(p -> {
                                Stream<Long> stream = p.getUidList().parallelStream();
                                // 用户画像组过滤
                                if (userGroups != null) {
                                    stream = stream.filter(uid -> userGroupService.anyMatch(uid, userGroups));
                                }
                                // 未完成过滤
                                stream = StreamUtils.partition(stream, 500)
                                        .flatMap(s -> filterIncomplete(p.getTask().getId(), s));
                                return new AbstractMap.SimpleEntry<>(p.getTask(), stream.collect(Collectors.toList()));
                            }).collect(Collectors.toList());

                            // 任务执行
                            for (AbstractMap.SimpleEntry<CourseWatchQuest.Task, List<Long>> entry : filtered) {
                                CourseWatchQuest.Task task = entry.getKey();
                                List<CompletableFuture<Act588BenifitRecordDO>> futures = entry.getValue().stream()
                                        .map(uid -> CompletableFuture.supplyAsync(() -> completePointTask(task, uid), executor)).collect(Collectors.toList());

                                CompletableFuture<Void> taskFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                                        .whenComplete((result, err) -> {
                                            if (err != null) {
                                                this.error("Task: {} 处理异常", task, err);
                                            }
                                            // 设置过期时间
                                            operations.getOperations().expireAt(String.valueOf(task.getId()),
                                                    ZonedDateTime.of(
                                                            Optional.ofNullable(task.getEndTime()).orElse(LocalDateTime.now()).plusDays(30), ZoneId.systemDefault()
                                                    ).toInstant());
                                            // 统计结果
                                            List<Act588BenifitRecordDO> results = futures.stream()
                                                    .filter(CompletableFuture::isDone)
                                                    .map(CompletableFuture::join)
                                                    .collect(Collectors.toList());
                                            // 本次计数
                                            Long current = results.stream().filter(s -> s.benefitConfirm == 1).count();
                                            // 累计计数
                                            Long count = operations.size(String.valueOf(task.getId()));
                                            this.log("[{}-{}] 应发：{}, 实发：{}, 累计: {}", quest.getQuestId(), task.getId(), futures.size(), current, count);

                                            Lists.partition(results, 200).forEach(item -> {
                                                try {
                                                    benifitrecordMapper.insertBatch(item);
                                                } catch (Exception e) {
                                                    String list = item.stream().map(s -> s.uid).collect(Collectors.joining(","));
                                                    this.log("[{}-{}] 发放记录失败, 用户列表：{}", quest.getQuestId(), task.getId(), list, e);
                                                }
                                            });
                                        });
                                taskFutures.add(taskFuture);
                            }
                        } catch (Exception e) {
                            this.error("Quest: {} 处理异常", quest, e);
                        }
                    });
            // 等待所有任务完成
            try {
                CompletableFuture.allOf(taskFutures.toArray(new CompletableFuture[0]))
                        .get(2, TimeUnit.HOURS);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                this.error("任务执行超时");
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("error: {}", e.getMessage(), e);
            XxlJobLogger.log("error:" + e);
        } finally {
            executor.shutdownNow();
        }
        return ReturnT.FAIL;
    }

    private Stream<PreparedQuestTask> prepareQuest(CourseWatchQuest quest, LocalDate targetDate) {
        return quest.stream().map(task -> {
            CourseDetailDTO course = courseService.detail(task.getCourseId());
            LocalDateTime endDate = Optional.ofNullable(quest.getEndTime()).orElseGet(LocalDateTime::now);
            // 计算直播任务完成的人数
            Stream<Long> liveUsers = Stream.empty();
            if (task.getLiveDuration() != null && checkDateInRange(course.getCourseBeginTime(), course.getCourseEndTime(), targetDate, appendDays)) {
                String webCastId = GenseeUtils.findWebCastId(course.getCourseLiveUrl()).orElse(null);
                if (webCastId != null) {
                    Stream<Long> dbUsers = genseeService.streamLive(webCastId, task.getLiveDuration(), 0, sqlBatchSize)
                            .flatMap(Collection::stream)
                            .map(CourseWatchDO::getUid);
                    Stream<Long> apiUsers = videoStream.queryUserLiveStatistics(webCastId, targetDate.atStartOfDay(), endDate).getDetail().stream()
                            .filter(s -> s.getTotalDuration().compareTo(task.getLiveDuration()) >= 0)
                            .map(UserLiveStatisticsDTO::getUserId);
                    liveUsers = Stream.concat(dbUsers, apiUsers).distinct();
                } else {
                    this.error("[{}-{}] 未找到直播ID: {}", quest.getQuestId(), task.getId(), course.getCourseLiveUrl());
                }
            }

            // 计算回放任务完成的人数
            Stream<Long> recordedUsers = Stream.empty();
            if (task.getRecordDuration() != null && checkDateInRange(course.getCourseBeginTime(), task.getEndTime(), targetDate, appendDays)) {
                String webCastId = GenseeUtils.findWebCastId(course.getCoursePlayBackUrl()).orElse(null);
                if (webCastId != null) {
                    Stream<Long> dbUsers = genseeService.streamReplay(webCastId, task.getRecordDuration(),
                                    task.getStartTime(), task.getEndTime(),
                                    0, sqlBatchSize)
                            .flatMap(Collection::stream)
                            .map(CourseWatchDO::getUid);
                    Stream<Long> apiUsers = videoStream.queryUserVodStatistics(webCastId, targetDate.atStartOfDay(), endDate).getDetail().stream()
                            .filter(s -> s.getTotalDuration().compareTo(task.getRecordDuration()) >= 0)
                            .map(UserVodStatisticsDTO::getUserId);
                    recordedUsers = Stream.concat(dbUsers, apiUsers).distinct();
                } else {
                    this.error("[{}-{}] 未找到录播ID: {}", quest.getQuestId(), task.getId(), course.getCoursePlayBackUrl());
                }
            }

            LongAdder liveCount = new LongAdder();
            LongAdder recordCount = new LongAdder();

            List<Long> uidList = Stream.concat(Stream.concat(
                                    liveUsers.peek(user -> liveCount.increment()),
                                    recordedUsers.peek(user -> recordCount.increment())
                            ),
                            // 测试账户
                            Stream.of(1001942207L, 1002184507L)
                    )
                    .distinct()
                    .collect(Collectors.toList());
            return new PreparedQuestTask(task)
                    .setUidList(uidList)
                    .setLiveCount(liveCount.intValue())
                    .setRecordCount(recordCount.intValue());
        });
    }

    private boolean checkDateInRange(LocalDateTime start, LocalDateTime end, LocalDate target, int appendDays) {
        return (start == null || target.compareTo(start.toLocalDate()) >= 0) &&
               (end == null || target.compareTo(end.toLocalDate().plusDays(appendDays)) <= 0);
    }

    public Act588BenifitRecordDO completePointTask(CourseWatchQuest.Task task, Long uid) {
        List<RewardResult> results = completeTask(task, uid);
        boolean maySuccess = true, success = true;
        for (RewardResult result : results) {
            if (!result.isSuccess()) {
                success = false;
            }
            if (!result.isMaySuccess()) {
                maySuccess = false;
                break;
            }
        }

        if (maySuccess) {
            if (!success) {
                log.error("[{}] 用户: {} 奖励可能成功: {}", task.getId(), uid, results);
                XxlJobLogger.log("[{}] 用户: {} 奖励可能成功: {}", task.getId(), uid, results);
            }
            operations.putIfAbsent(String.valueOf(task.getId()), uid, "");
        } else {
            log.error("[{}] 用户: {} 奖励发送失败: {}", task.getId(), uid, results);
            XxlJobLogger.log("[{}] 用户: {} 奖励发送失败: {}", task.getId(), uid, results);
        }

        Act588BenifitRecordDO record = new Act588BenifitRecordDO();
        record.uid = String.valueOf(uid);
        record.benefitId = 60231016;
        record.benefitName = "18期上拽课程任务奖励, Course[" + task.getCourseId() + "], Task[" + task.getId() + "]";
        record.source = "gensee";
        record.actCode = "UpNo18-231016";
        record.writeTime = new Date();
        record.benefitConfirm = maySuccess ? (success ? 1 : 2) : 0;
        return record;
    }

    /**
     * 完成任务
     *
     * @param task 任务
     * @param uid  用户id
     * @return 符合条件的奖励结果
     */
    public List<RewardResult> completeTask(CourseWatchQuest.Task task, Long uid) {
        if (task.getRewards() == null) {
            return Collections.emptyList();
        }
        // 命中奖励
        return task.getRewards().stream()
                .map(reward -> {
                    try {
                        return sendReward(uid, reward);
                    } catch (Exception e) {
                        log.error("用户: {} 奖励发送异常: {}", uid, reward, e);
                        return RewardResult.fail("奖励发放异常:" + e.getMessage());
                    }
                })
                .filter(RewardResult::isMatch)
                .collect(Collectors.toList());
    }

    public RewardResult sendReward(Long uid, QuestReward reward) {
        if (reward instanceof PointReward) {
            return sendPointReward(uid, ((PointReward) reward).getTaskId());
        }
        log.error("暂未支持的奖励类型: {}, uid: {}, content: {}", reward.getClass().getName(), uid, reward);
        return RewardResult.fail("暂未支持的奖励类型");
    }

    private RewardResult sendPointReward(Long uid, Long pointTaskId) {
        try {
            pointService.addPointByTaskId(uid, pointTaskId);
            return RewardResult.success();
        } catch (PointException e) {
            if (e.isConflict()) {
                return RewardResult.maySuccess(e.getMessage());
//                PointDetailDTO dto = pointService
//                        .queryRecordByTaskId(uid, pointTaskId).orElse(null);
//                if (dto != null) {
//                    if (Boolean.FALSE.equals(dto.getIsDailytask())) {
//                        return RewardResult.success();
//                    }
//                    return RewardResult.maySuccess(e.getMessage());
//                }
            }
            return RewardResult.fail("积分发放失败:" + e.getMessage());
        }
    }

    // 筛选未完成任务的用户
    private Stream<Long> filterIncomplete(Long task, List<Long> uid) {
        if (uid == null || uid.isEmpty()) {
            return Stream.empty();
        }
        List<String> checked = operations.multiGet(String.valueOf(task), uid);
        return IntStream.range(0, uid.size())
                .mapToObj(i -> checked.get(i) == null ? uid.get(i) : null)
                .filter(Objects::nonNull);
    }

    private void log(String s, Object... o) {
        log.info(s, o);
        XxlJobLogger.log(s, o);
    }

    private void error(String s, Object... o) {
        log.error(s, o);
        AtomicBoolean hasError = new AtomicBoolean(false);
        Object[] array = Arrays.stream(o)
                .map(x -> {
                    if (x instanceof Throwable) {
                        hasError.set(true);
                        return ((Throwable) x).getMessage();
                    }
                    return x;
                })
                .toArray();
        if (hasError.get()) {
            XxlJobLogger.log(s + ": {}", array);
        } else {
            XxlJobLogger.log(s, o);
        }
    }


    @Data
    @Accessors(chain = true)
    private static class PreparedQuestTask {
        private final CourseWatchQuest.Task task;
        private Integer liveCount, recordCount;
        private List<Long> uidList;

        private PreparedQuestTask(CourseWatchQuest.Task task) {
            this.task = task;
        }
    }
}
