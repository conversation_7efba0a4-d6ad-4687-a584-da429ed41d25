package cn.emoney.task.jobhandler;

import cn.emoney.service.SignRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
public class SignRecordJob {

    @Autowired
    private SignRecordService signRecordService;

    /**
     * 针对本周已三次打卡未领取使用期用户定时补发使用期
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob("compensateFeeCycleJobHandler")
    public ReturnT<String> compensateFeeCycleJobHandler(String param) throws Exception {
        XxlJobLogger.log("SignRecordJob.compensateFeeCycleJobHandler, Started:" + com.xxl.job.core.util.DateUtil.formatDateTime(new Date()));
        try {
            signRecordService.batchCompensateSendPrivilege();
            XxlJobLogger.log("SignRecordJob.compensateFeeCycleJobHandler, End:" + DateUtil.formatDateTime(new Date()));
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("SignRecordJob.compensateFeeCycleJobHandler Job error:", e);
        }
        return ReturnT.FAIL;
    }
}
