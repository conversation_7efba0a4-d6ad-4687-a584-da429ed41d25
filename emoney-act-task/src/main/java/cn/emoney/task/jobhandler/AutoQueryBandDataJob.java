package cn.emoney.task.jobhandler;

import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.InviteAwardDetailDO;
import cn.emoney.pojo.bo.OrderProdListDTO;
import cn.emoney.pojo.vo.OrderProdListVO;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-03-07
 */
@Component
@Slf4j
public class AutoQueryBandDataJob {

    @XxlJob("AutoQueryBandDataJobHandler")
    public ReturnT<String> AutoQueryBandDataJobHandler(String param) throws Exception {
        try {
            XxlJobLogger.log("AutoQueryBandDataJob, Started:" + DateUtil.formatDateTime(new Date()));

            int v = 4;
            int s = 0;
            int maxVersion = 5;
            int maxS = 5;
            while (v <= maxVersion) {
                while (s <= maxS) {
                    String advancedClassUserReportList = OkHttpUtil.get(MessageFormat.format("http://admin.5starband.emoney.cn/Test/GetUserReportList?v={0}&s={1}", v, s), null);
                    if(s==0){
                        Thread.sleep(120000);
                    }
                    else {
                        Thread.sleep(20000);
                    }
                    s++;
                }
                v++;
            }

            XxlJobLogger.log("AutoQueryBandDataJob, Ended:" + DateUtil.formatDateTime(new Date()));
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("AutoQueryBandDataJobHandler error:" + e);
            XxlJobLogger.log("AutoQueryBandDataJobHandler error:" + e.toString());
        }
        return ReturnT.FAIL;
    }

}
