package cn.emoney.task.jobhandler;

import cn.emoney.service.BenefitDsService;
import cn.emoney.service.Lottery20240306Service;
import cn.emoney.service.Lottery20240615Service;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-09-07
 */
@Component
@Slf4j
public class AutoSendDsBenefitJob {
    @Autowired
    private BenefitDsService benefitDsService;

    @Autowired
    private Lottery20240306Service lottery20240306Service;
    @Autowired
    private Lottery20240615Service lottery20240615Service;

    /**
     * 【大师续费签到活动】 成功续费大师用户 一次补齐未签到领取的奖励
     *
     * @param param
     * @return
     * @throws Exception
     */
    @XxlJob("AutoSendDsBenefitJobHandler")
    public ReturnT<String> autoSendDsBenefitJobHandler(String param) throws Exception {
        XxlJobLogger.log("AutoSendDsBenefitJob.autoSendDsBenefitJobHandler, Started:" + com.xxl.job.core.util.DateUtil.formatDateTime(new Date()));
        try {
            benefitDsService.autoSendSignBenefit();
            XxlJobLogger.log("AutoSendDsBenefitJob.autoSendDsBenefitJobHandler, End:" + DateUtil.formatDateTime(new Date()));
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("AutoSendDsBenefitJob.autoSendDsBenefitJobHandler Job error:", e);
            XxlJobLogger.log("AutoSendDsBenefitJob.autoSendDsBenefitJobHandler Job error:", e.toString());
        }
        return ReturnT.FAIL;
    }

    /**
     * 20240611-618大师续费复用
     * 【3月大师续费抽奖活动】获取用户购买的物流包匹配的抽奖次数配置
     * 5分钟执行一次
     * <AUTHOR>
     * @date 2024/3/1 11:31
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     */
    @XxlJob("autoGetUserPackagePCountJobHandler")
    public ReturnT<String> autoGetUserPackageConf(String param) throws Exception {
        XxlJobLogger.log("AutoSendDsBenefitJob.autoGetUserPackagePCountJobHandler, Started:" + com.xxl.job.core.util.DateUtil.formatDateTime(new Date()));
        try {
            //lottery20240306Service.autoGetUserPackagePCount();
            lottery20240615Service.autoGetUserPackagePCount_0615();
            lottery20240615Service.autoGetUserPackagePCount_tj();
            XxlJobLogger.log("AutoSendDsBenefitJob.autoGetUserPackagePCountJobHandler, End:" + DateUtil.formatDateTime(new Date()));
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("AutoSendDsBenefitJob.autoGetUserPackagePCountJobHandler Job error:", e);
            XxlJobLogger.log("AutoSendDsBenefitJob.autoGetUserPackagePCountJobHandler Job error:", e.toString());
        }
        return ReturnT.FAIL;
    }

    /**
     * 20240611-618大师续费复用
     * 【3月大师续费抽奖活动】清理用户退货的物流包匹配的抽奖次数配置
     * 5分钟执行一次
     * <AUTHOR>
     * @date 2024/3/1 11:31
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     */
    @XxlJob("autoClearUserPackagePCountJobHandler")
    public ReturnT<String> autoClearUserPackageConf(String param) throws Exception {
        XxlJobLogger.log("AutoSendDsBenefitJob.autoClearUserPackagePCountJobHandler, Started:" + com.xxl.job.core.util.DateUtil.formatDateTime(new Date()));
        try {
            //lottery20240306Service.autoClearUserPackagePCount();
            lottery20240615Service.autoClearUserPackagePCount_0615();
            lottery20240615Service.autoClearUserPackagePCount_tj();
            XxlJobLogger.log("AutoSendDsBenefitJob.autoClearUserPackagePCountJobHandler, End:" + DateUtil.formatDateTime(new Date()));
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("AutoSendDsBenefitJob.autoClearUserPackagePCountJobHandler Job error:", e);
            XxlJobLogger.log("AutoSendDsBenefitJob.autoClearUserPackagePCountJobHandler Job error:", e.toString());
        }
        return ReturnT.FAIL;
    }
}
