package cn.emoney.task.jobhandler;

import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.PayOrderDO;
import cn.emoney.pojo.bo.SendMessageDTO;
import cn.emoney.pojo.bo.SendMessageData;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.pojo.vo.UserLoginIdInfoVO;
import cn.emoney.service.MobileService;
import cn.emoney.service.PayService;
import cn.emoney.service.UserService;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.protocol.types.Field;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-03-28
 */
@Component
@Slf4j
public class AutoSendMessageJob {

    @Value("${swscUrl}")
    private String swscUrl;

    @Autowired
    private PayService payService;

    @Autowired
    private MobileService mobileService;

    @Autowired
    private UserService userService;

    /**
     * 60分钟后发送短信提醒
     *
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2022/3/28 15:29
     */
    @XxlJob("AutoSendMessageJobHandler")
    public ReturnT<String> autoSendMessageJobHandler(String param) throws Exception {
        try {
            String schName = "";
            XxlJobLogger.log("AutoSendMessageJobHandler, Started:" + DateUtil.formatDateTime(new Date()));
            Date startTime = DateUtil.offsetMinute(DateUtil.date(), -60);
            Date endTime = DateUtil.offsetMinute(DateUtil.date(), -59);
            List<PayOrderDO> payOrderDOS = payService.queryByCreateTimeAndOrderStatus(startTime, endTime, 0);
            if (payOrderDOS != null && payOrderDOS.size() > 0) {
                for (PayOrderDO payOrderDO : payOrderDOS) {
                    String mobileX = "0x" + Convert.toHex(payOrderDO.getMidpwd());
                    ApiGateWayResult<String> result = mobileService.sendTextMessage(mobileX, MessageFormat.format("[优惠流失提醒]尊敬的益盟用户，您的团购名额10分钟后失效，点击 {0} 立即参团，抢占优惠。", "r.emoney.cn/pay0329"), "181");
                    XxlJobLogger.log("发送短信：" + mobileX + ";result:" + JSON.toJSONString(result));
                }
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("AutoSendMessageJobHandler error:" + e);
            XxlJobLogger.log("AutoSendMessageJobHandler error:" + e.toString());
        }
        return ReturnT.FAIL;
    }

    /**
     * 60分钟后发送短信提醒
     *
     * @param param
     * @return com.xxl.job.core.biz.model.ReturnT<java.lang.String>
     * <AUTHOR>
     * @date 2022/3/28 15:29
     */
    @XxlJob("AutoShowPopJobHandler")
    public ReturnT<String> autoShowPopJobHandler(String param) throws Exception {
        try {
            XxlJobLogger.log("AutoShowPopJobHandler, Started:" + DateUtil.formatDateTime(new Date()));
            Date startTime = DateUtil.offsetMinute(DateUtil.date(), -5);
            Date endTime = DateUtil.offsetMinute(DateUtil.date(), -4);
            List<PayOrderDO> payOrderDOS = payService.queryByCreateTimeAndOrderStatus(startTime, endTime, 0);
            if (payOrderDOS != null && payOrderDOS.size() > 0) {
                for (PayOrderDO payOrderDO : payOrderDOS) {
                    String mobileX = "0x" + Convert.toHex(payOrderDO.getMidpwd());
                    String userName = "";
                    UserLoginIdInfoVO userLoginIdInfoVO = userService.GetLoginIDInfoByAccount(mobileX);
                    List<BindAccountVO> bindAccountVOS = userService.GetBindAccountList(userLoginIdInfoVO.getPID().toString());
                    if (bindAccountVOS != null) {
                        BindAccountVO userInfoVO = bindAccountVOS.stream().filter(h -> h.getAccountType() == 0).findFirst().orElse(null);
                        if (userInfoVO != null) {
                            userName = userInfoVO.getAccountName();
                        }
                    }
                    String pid = userService.GetAccountPID(userName);
                    if (!StrUtil.isEmpty(pid)) {
                        SendMessageDTO sendMessageDTO = new SendMessageDTO();
                        sendMessageDTO.setType("notify");
                        sendMessageDTO.setTime(DateUtil.formatDateTime(DateUtil.date()));
                        sendMessageDTO.setAppid("10013");
                        SendMessageData sendMessageData = new SendMessageData();
                        sendMessageData.setTitle("订单提醒");
                        sendMessageData.setUrl("http://www.emoney.cn/dianjin/ym/rj/tc/ddtx/index.html");
                        sendMessageDTO.setMessage(sendMessageData);
                        Map<String, String> stringMap = new HashMap<>();
                        stringMap.put("uid", userLoginIdInfoVO.PID.toString());
                        stringMap.put("group", "softonline_" + pid);
                        stringMap.put("message", URLUtil.encode(JSON.toJSONString(sendMessageDTO)));
                        String res = OkHttpUtil.get(swscUrl + "/pushmessage", stringMap);
                        XxlJobLogger.log("发送弹窗：" + mobileX + ";result:" + res);
                    }
                }
            }
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("AutoShowPopJobHandler error:" + e);
            XxlJobLogger.log("AutoShowPopJobHandler error:" + e.toString());
        }
        return ReturnT.FAIL;
    }
}
