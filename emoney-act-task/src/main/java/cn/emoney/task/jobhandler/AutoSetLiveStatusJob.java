package cn.emoney.task.jobhandler;

import cn.emoney.pojo.LiveVisitNoticeDO;
import cn.emoney.pojo.bo.FirstClassDTO;
import cn.emoney.service.FirstClassConfigCourseService;
import cn.emoney.service.FirstClassService;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.BoundListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-06-16
 */
@Component
@Slf4j
public class AutoSetLiveStatusJob {
    private final FirstClassService classService;
    private final FirstClassConfigCourseService courseService;
    private final BoundListOperations<String, LiveVisitNoticeDO> liveVisitNoticeOps;
    private final static SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public AutoSetLiveStatusJob(FirstClassService classService,
                                FirstClassConfigCourseService courseService,
                                @Qualifier("liveVisitRedisTemplate") RedisTemplate<String, LiveVisitNoticeDO> redisTemplate) {
        this.classService = classService;
        this.courseService = courseService;
        this.liveVisitNoticeOps = redisTemplate.boundListOps("LiveVisitInfo_Activity");
    }

    @XxlJob("AutoSetLiveStatusJobHandler")
    public ReturnT<String> autoSetLiveStatusJobHandler(String param) throws Exception {
        try {
            XxlJobLogger.log("AutoSetLiveStatusJobHandler, Started:" + DateUtil.formatDateTime(new Date()));

            LiveVisitNoticeDO notice;
            while ((notice = liveVisitNoticeOps.leftPop()) != null) {
                //进入会场
                if (notice.OpType == 0) {
                    try {
                        String visitTimeStr = StringUtils.substringBefore(notice.OpTime, ".").replace("T", " ");
                        Date visitTime = simpleDateFormat.parse(visitTimeStr);
                        FirstClassDTO course = courseService.findByVideoId(notice.MID)
                                .filter(i -> visitTime.before(i.getEndTime()))
                                .orElse(null);
                        if (course != null) {
                            //设置为已观看状态
                            classService.SetClassRecord(notice.UID, "", String.valueOf(course.getId()), "pc", course.getActivityID());
                            XxlJobLogger.log("AutoSetLiveStatusJobHandler 观看用户id：" + notice.UID + " ,对应课程：" + JSON.toJSONString(course));
                        }
                    } catch (Exception e) {
                        XxlJobLogger.log("AutoSetLiveStatusJobHandler 异常 " + e.getMessage() + "\n" +
                                         "观看用户id：" + notice.UID + " ,对应课程：" + JSON.toJSONString(notice));
                    }
                }
            }
            XxlJobLogger.log("AutoSetLiveStatusJobHandler, Ended:" + DateUtil.formatDateTime(new Date()));
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("AutoSetLiveStatusJobHandler error:" + e);
            XxlJobLogger.log("AutoSetLiveStatusJobHandler error:" + e);
        }
        return ReturnT.FAIL;
    }
}
