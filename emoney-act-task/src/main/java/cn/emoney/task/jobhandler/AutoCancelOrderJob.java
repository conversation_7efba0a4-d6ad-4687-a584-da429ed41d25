package cn.emoney.task.jobhandler;

import cn.emoney.pojo.InviteAwardDetailDO;
import cn.emoney.pojo.bo.OrderProdListDTO;
import cn.emoney.pojo.vo.OrderProdListVO;
import cn.emoney.service.InviteService;
import cn.emoney.service.LogisticsService;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-22
 */
@Component
@Slf4j
public class AutoCancelOrderJob {

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private InviteService inviteService;

    @XxlJob("AutoCancelOrderJobHandler")
    public ReturnT<String> AutoCancelOrderJobHandler(String param) throws Exception {
        try {
            XxlJobLogger.log("AutoCancelOrderJob, Started:" + DateUtil.formatDateTime(new Date()));
            //获取前2个小时的所有物流订单
            OrderProdListDTO orderProdListDTO = new OrderProdListDTO();
            orderProdListDTO.setORDADD_TIME_Start(DateTime.now().offset(DateField.HOUR, -2).toString("yyyy-MM-dd HH:mm:ss"));
            orderProdListDTO.setORDADD_TIME_End(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
            orderProdListDTO.setRefund_Sign(-1);
            List<OrderProdListVO> orderProdListVOS = logisticsService.queryOrderProdList(orderProdListDTO);
            if (orderProdListVOS != null) {
                HashSet<String> hashSet = new HashSet<String>();
                for (OrderProdListVO orderProdListVO : orderProdListVOS) {
                    if (hashSet.add(orderProdListVO.getORDER_ID())) {
                        InviteAwardDetailDO inviteAwardDetailDO = inviteService.getByOrderId(orderProdListVO.getORDER_ID());
                        if (inviteAwardDetailDO != null && inviteAwardDetailDO.getId() > 0) {
                            XxlJobLogger.log("更新用户退货状态，订单号：" + orderProdListVO.getORDER_ID());
                        }
                    }
                }
            }

            XxlJobLogger.log("AutoCancelOrderJob, End:" + DateUtil.formatDateTime(new Date()));
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("AutoCancelOrderJobHandler error:" + e);
            XxlJobLogger.log("AutoCancelOrderJobHandler error:" + e.toString());
        }
        return ReturnT.FAIL;
    }
}
