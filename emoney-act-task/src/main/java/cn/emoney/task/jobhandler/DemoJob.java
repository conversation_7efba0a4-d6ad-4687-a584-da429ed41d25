package cn.emoney.task.jobhandler;

import cn.emoney.pojo.InviteAwardDetailDO;
import cn.emoney.pojo.bo.OrderProdListDTO;
import cn.emoney.pojo.vo.OrderProdListVO;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021-12-29
 */
@Component
@Slf4j
public class DemoJob {

    @XxlJob("DemoJobHandler")
    public ReturnT<String> DemoJobHandler(String param) throws Exception {
        try {
            XxlJobLogger.log("DemoJob, Started:" + DateUtil.formatDateTime(new Date()));
            XxlJobLogger.log("DemoJob, End:" + DateUtil.formatDateTime(new Date()));
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("DemoJob error:" + e);
            XxlJobLogger.log("DemoJob error:" + e.toString());
        }
        return ReturnT.FAIL;
    }
}
