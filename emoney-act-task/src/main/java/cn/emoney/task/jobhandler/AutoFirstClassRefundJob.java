package cn.emoney.task.jobhandler;

import cn.emoney.pojo.SCMOrderDTO;
import cn.emoney.pojo.SCMOrderQueryDTO;
import cn.emoney.pojo.vo.UserLoginIdInfoVO;
import cn.emoney.service.ActivityQuestService;
import cn.emoney.service.SCMOrderService;
import cn.emoney.service.UserService;
import cn.hutool.core.date.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-12-22
 */
@Slf4j
@Component
public class AutoFirstClassRefundJob {
    private final ActivityQuestService questService;
    private final SCMOrderService scmOrderService;
    private final UserService userService;
    private final RetryTemplate retryTemplate;

    public AutoFirstClassRefundJob(ActivityQuestService questService, SCMOrderService scmOrderService, UserService userService) {
        this.questService = questService;
        this.scmOrderService = scmOrderService;
        this.userService = userService;

        RetryTemplate retryTemplate = new RetryTemplate();
        retryTemplate.setRetryPolicy(new SimpleRetryPolicy(3));

        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        backOffPolicy.setInitialInterval(1000L);
        backOffPolicy.setMultiplier(2);
        retryTemplate.setBackOffPolicy(backOffPolicy);
        this.retryTemplate = retryTemplate;
    }

    @XxlJob("autoFirstClassRefundJob")
    public ReturnT<String> runTask(String param) {
        LocalDate targetDate = StringUtils.hasText(param) ? LocalDate.parse(param) : LocalDate.now().minusDays(1);
        Long questId = 10001L;
        List<SCMOrderDTO> refunds = scmOrderService.listOrderByEntity(SCMOrderQueryDTO.builder()
                .cancelTimeStart(targetDate)
                .cancelTimeEnd(targetDate.plusDays(1))
                .refundSign(-1)
                .productId(Arrays.asList("*********", "*********"))
                .build());

        List<Long> tasks = refunds.stream().map(SCMOrderDTO::getEmCard).distinct()
                .parallel()
                .map(emCard -> {
                    try {
                        return retryTemplate.execute(arg0 -> Objects.requireNonNull(userService.GetLoginIDInfoByAccount(emCard)));
                    } catch (Exception e) {
                        log.error("[{}] 获取用户登录信息失败", questId, e);
                        XxlJobLogger.log("[{}] 获取用户登录信息失败", questId);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .map(UserLoginIdInfoVO::getPID)
                .collect(Collectors.toList());


        int core = Runtime.getRuntime().availableProcessors() * 2;
        ExecutorService executor = Executors.newFixedThreadPool(core);
        log.info("Started: {}, day: {}, tasks: {}, core: {}", DateUtil.formatDateTime(new Date()), targetDate, tasks.size(), core);
        XxlJobLogger.log("Started: {}, day: {}, tasks: {}, core: {}", DateUtil.formatDateTime(new Date()), targetDate, tasks.size(), core);
        try {
            List<CompletableFuture<Boolean>> futures = tasks.stream().map(task ->
                    CompletableFuture.supplyAsync(() -> {
                        try {
                            return retryTemplate.execute(arg0 -> questService.cancel(questId, task));
                        } catch (Exception e) {
                            log.error("[{}] 用户: {} 任务取消失败", questId, task, e);
                            XxlJobLogger.log("[{}] 用户 {} 任务取消失败: {}", questId, task, e.getMessage());
                        }
                        return false;
                    })
            ).collect(Collectors.toList());

            // 等待所有任务完成
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .get(600 + (futures.size() / 5), TimeUnit.SECONDS);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.error("[{}] 取消超时", questId, e);
                XxlJobLogger.log("[{}] 取消超时", questId, e);
            }

            long count = futures.stream().filter(CompletableFuture::isDone)
                    .map(s -> s.getNow(false))
                    .filter(s -> s)
                    .count();
            log.info("[{}] 总人数: {}, 任务取消数： {}", questId, tasks.size(), count);
            XxlJobLogger.log("[{}] 总人数: {}, 任务取消数： {}", questId, tasks.size(), count);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("error: {}", e.getMessage(), e);
            XxlJobLogger.log("error:" + e);
        } finally {
            executor.shutdownNow();
        }
        return ReturnT.FAIL;
    }
}
