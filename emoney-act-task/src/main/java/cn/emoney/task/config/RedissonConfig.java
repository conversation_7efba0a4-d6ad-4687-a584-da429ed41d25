package cn.emoney.task.config;

import cn.emoney.common.utils.RedissonDistributionLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * redis分布式锁配置
 */
@Configuration
public class RedissonConfig {

/// 还原 RedissonAutoConfiguration
/// @see org.redisson.spring.starter.RedissonAutoConfiguration.redisson
//    @Value("${spring.redis.host}")
//    private String redisHostName;
//
//    @Value("${spring.redis.port}")
//    private String redisPort;
//
//    @Value("${spring.redis.database}")
//    private String redisDatabase;
//
//    @Bean
//    public RedissonClient redissonClient() {
//        Config config = new Config();
//        config.useSingleServer().setAddress("redis://" + redisHostName + ":" + redisPort).setDatabase(Integer.parseInt(redisDatabase));
//        return (Redisson) Redisson.create(config);
//    }

    /**
     * 分布式锁
     *
     * @param redissonClient
     * @return
     */
    @Bean
    public RedissonDistributionLock redissonDistributionLock(RedissonClient redissonClient) {
        return new RedissonDistributionLock(redissonClient);
    }
}

