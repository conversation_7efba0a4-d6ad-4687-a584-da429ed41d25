package cn.emoney.task.config.datasource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 主数据源配置
 */
@Configuration
@MapperScan(basePackages = "cn.emoney.mapper.activity", sqlSessionTemplateRef = "activitySqlSessionTemplate")
public class ActivityDataSourceConfig {

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource.activity")
    @Primary
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * It is recommended to use a single JpaTransactionManager for all transactions
     * on a single DataSource, no matter whether JPA or JDBC access.
     */
    @Bean
    @Primary
    public PlatformTransactionManager transactionManager(
            ObjectProvider<TransactionManagerCustomizers> transactionManagerCustomizers) {
        JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManagerCustomizers.ifAvailable((customizers) -> customizers.customize(transactionManager));
        return transactionManager;
    }

    // ---- Mybatis 配置 ----- //

    @Bean(name = "activitySqlSessionFactory")
    @Primary
    public SqlSessionFactory activitySqlSessionFactory(DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mybatis/mapper/activity/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "activitySqlSessionTemplate")
    @Primary
    public SqlSessionTemplate activitySqlSessionTemplate(@Qualifier("activitySqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    // ---- Mybatis 配置 ----- //
}
