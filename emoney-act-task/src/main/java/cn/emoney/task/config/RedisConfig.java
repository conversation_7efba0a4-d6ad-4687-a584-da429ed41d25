package cn.emoney.task.config;

import cn.emoney.common.redis.LettuceConnectionBuilder;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.support.spring.FastJsonRedisSerializer;
import io.lettuce.core.resource.ClientResources;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@EnableCaching
@Configuration
@ConfigurationProperties(prefix = "spring.livevisit")
public class RedisConfig {
    @Setter
    @Getter
    @NestedConfigurationProperty
    private RedisProperties redis;

    /**
     * 配置第一个数据源的RedisTemplate
     * 注意：这里指定使用名称=factory 的 RedisConnectionFactory
     * 并且标识第一个数据源是默认数据源 @Primary
     *
     * @param factory
     * @return
     */
    @Bean("redisTemplate")
    @Primary
    public RedisTemplate redisTemplate(RedisConnectionFactory factory) {
        return getStringStringRedisTemplate(factory);
    }

    /**
     * 配置第一个数据源的RedisTemplate
     * 注意：这里指定使用名称=factory 的 RedisConnectionFactory
     * 并且标识第一个数据源是默认数据源 @Primary
     */
    @Bean("liveVisitRedisTemplate")
    public RedisTemplate liveVisitRedisTemplate(ClientResources clientResources,
                                                ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers) {
        if (redis == null) {
            return null;
        }
        LettuceConnectionFactory factory = LettuceConnectionBuilder.createLettuceConnectionFactory(redis, clientResources, builderCustomizers);
        factory.afterPropertiesSet();
        return getStringStringRedisTemplate(factory);
    }

    /**
     * @param liveVisitRedisTemplate
     * @return
     */
    @Bean("liveVisitRedisManager")
    public RedisService liveVisitRedisManager(@Qualifier("liveVisitRedisTemplate") RedisTemplate liveVisitRedisTemplate) {
        return new RedisService(liveVisitRedisTemplate);
    }

    /**
     * 设置序列化方式
     *
     * @param factory
     * @return
     */
    private RedisTemplate getStringStringRedisTemplate(RedisConnectionFactory factory) {
        RedisTemplate template = new RedisTemplate();
        template.setConnectionFactory(factory);
        // 使用fastjson序列化
        FastJsonRedisSerializer<Object> redisSerialize = new FastJsonRedisSerializer<>(Object.class);
        // 全局开启AutoType，不建议使用
        // ParserConfig.getGlobalInstance().setAutoTypeSupport(true);
        // 建议使用这种方式，小范围指定白名单
        ParserConfig.getGlobalInstance().addAccept("cn.emoney.pojo.");
        // value 值的序列化采用 RedisSerialize
        template.setValueSerializer(redisSerialize);
        template.setHashValueSerializer(redisSerialize);
        // key 的序列化采用 StringRedisSerializer
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setDefaultSerializer(new StringRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }
}
