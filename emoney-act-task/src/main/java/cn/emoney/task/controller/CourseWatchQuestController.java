package cn.emoney.task.controller;

import cn.emoney.service.CourseWatchQuestService;
import cn.emoney.service.impl.CourseWatchQuestServiceImpl;
import cn.emoney.service.redis.RedisTemplateSupplier;
import com.google.common.collect.Streams;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@RestController
@RequestMapping("/quest/course-watch")
public class CourseWatchQuestController {
    private final CourseWatchQuestService service;
    private final HashOperations<String, Long, String> operations;

    public CourseWatchQuestController(RedisTemplateSupplier supplier, CourseWatchQuestService service) {
        this.service = service;
        RedisTemplate<String, String> template = supplier.get(RedisSerializer.string(), "act:task:courseWatchPoint");
        template.setHashKeySerializer(new GenericToStringSerializer<>(Long.class));
        this.operations = template.opsForHash();
    }

    @GetMapping("context")
    public CourseWatchQuestServiceImpl.QuestContext context(Long questId, Long uid) {
        return service.checkQuestByUid(questId, uid);
    }

    @PostMapping("filter/in")
    public Set<Long> filterIn(Long taskId, @RequestBody Set<Long> in) {
        List<String> result = operations.multiGet(String.valueOf(taskId), in);
        return Streams.zip(in.stream(), result.stream(), (uid, value) -> {
                    if (value == null) {
                        return null;
                    }
                    return uid;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    @PostMapping("filter/notin")
    public Set<Long> filterNotIn(Long taskId, @RequestBody Set<Long> notIn) {
        try (Cursor<Map.Entry<Long, String>> scan = operations.scan(String.valueOf(taskId), ScanOptions.NONE)) {
            return StreamSupport.stream(Spliterators.spliteratorUnknownSize(scan, 0), false)
                    .filter(entry -> !notIn.contains(entry.getKey()))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("reset")
    public long reset(Long taskId, @RequestBody List<Long> uid) {
        return operations.delete(String.valueOf(taskId), uid.toArray());
    }
}
