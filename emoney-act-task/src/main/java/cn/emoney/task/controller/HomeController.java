package cn.emoney.task.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021-12-29
 */
@Controller
@RequestMapping
public class HomeController {

    @Value("${xxl.job.admin.addresses}")
    private String xxladdresses;

    @RequestMapping
    public String index(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.sendRedirect(xxladdresses);
        return null;
    }
}
