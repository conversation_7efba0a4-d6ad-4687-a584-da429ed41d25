server.port=8080
spring.application.name=emoney-activity-task

#数据库配置
spring.datasource.activity.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.activity.jdbc-url=***************************************************************
spring.datasource.activity.username=sa
spring.datasource.activity.password=123456

spring.datasource.pay.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.pay.jdbc-url=**********************************************************
spring.datasource.pay.username=sa
spring.datasource.pay.password=123456

spring.datasource.transfer.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
spring.datasource.transfer.jdbc-url=********************************************************
spring.datasource.transfer.username=sa
spring.datasource.transfer.password=123456

spring.datasource.gensee.url=***********************************************************
spring.datasource.gensee.username=sa
spring.datasource.gensee.password=123456

spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=ActivityHikariCP
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1

#mybatis配置
mybatis.config-location=classpath:mybatis/mybatis-config.xml
mapper.not-empty=false
mapper.identity=SQLSERVER

#redis配置
spring.redis.lettuce.pool.max-active=50
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0

spring.redis.database=0
spring.redis.host=*************
spring.redis.port=6379
spring.redis.timeout=600

#展示参会名单数据-来源杨飞
spring.livevisit.redis.database=5
spring.livevisit.redis.host=*************
spring.livevisit.redis.port=6379
spring.livevisit.redis.timeout=600

#kafka配置#####################################
spring.kafka.bootstrap-servers=**************:9092,**************:9092,**************:9092
#END kafka配置##################################


#thymeleaf
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.content-type=text/html

# resources
spring.mvc.servlet.load-on-startup=0
spring.mvc.static-path-pattern=/static/**
spring.web.resources.static-locations=classpath:/static/

#logstash
logstash.url=**************:4569

#sso
ssoparsingurl=http://gosso.emoney.cn/sso/getuserinfo?{0}
#手机号加密
AppID=10013
AplicationId=100006
WebApiEncryptKey=f3259ff5-62d1-4c16-8c19-90c2b1497bde
MobileEncrptApi=http://webapi.emoney.cn/sms/api/Message.MobileEncrypt?appId=10013&mobile={0}&applicationId={1}

#发送短信
SmsEncryptApi=http://webapi.emoney.cn/sms/api/message.send?{0}

#查询物流订单
queryOrderProdListUrl=http://172.28.1.146/api/logistics/1.0/order.queryorderprodlistbyparamsuat?gate_appid=10013&jsonstr={0}

#获取用户的绑定账户列表
getAccountListApiUrl=http://172.28.1.146/api/roboadvisor/1.0/user.boundgroupqrylogin?gate_appid=10013&uid={0}

#获取用户所有账号信息
queryAccountListByAccountApiUrl=http://webapi.emoney.cn/user/api/User.GetAccountList?appid=10060&username={0}

#根据用户名获取pid
getAccountPIDApiUrl=http://webapi.emoney.cn/User/api/User.GetAccountPID?appid=10013&username={0}

#获取用户loginid(uid)
getLoginIdByNameApi=http://172.28.1.146/api/roboadvisor/1.0/user.getloginidbyname?gate_appid=10013&gate_encrypt=&userName={0}&userPasswd=&createLogin=0

#查询账户所有优惠券
queryAllCouponListByAccountURL=http://172.28.1.146/api/logistics/1.0/coupon.queryallcouponlistbyaccount?gate_appid=10013&AccountType={0}&Account={1}&CouponPlatForm=A31001
#优惠券赠送
sendCouponURL=http://172.28.1.146/api/logistics/1.0/coupon.sendcoupon?gate_appid=10013
#物流赠送（特权|延期）
sendPrivilegeURL=http://172.28.1.146/api/logistics/1.0/privilege.sendprivilege?gate_appid=10013

#加积分
pointRecoredURL=http://172.28.1.146/api/pointprod/v1/pointrecord.add?gate_appid=10013
#查询积分
pointQuerySummaryURL=http://172.28.1.146/api/pointprod/v1/pointrecord.querysummary?gate_appid=10013
#积分订单创建
pointOrderAddURL=http://172.28.1.146/api/pointprod/v1/pointorder.add?gate_appid=10013
#积分兑换支付回调
pointOrderExchangeURL=http://172.28.1.146/api/pointprod/v1/pointorder.exanchangepypoint?gate_appid=10013
#根据任务id查询任务状态
QueryByTaskidsURL=http://172.28.1.146/api/pointprod/v1/pointrecord.querybytaskids?gate_appid=10013
#根据productid查询是否订单状态
QueryOrderByProductIdURL=http://172.28.1.146/api/pointprod/v1/pointorder.querybyuidandproductid?gate_appid=10013&uid={0}&productId={1}

#推送cmp-打标签
cmpLabelURL=http://172.25.20.36:50003/Advert/advertinfo

getSurveyTemplateUrl = https://www.wjx.cn/handler/IllustrateApi.ashx?activityID={0}&JSON=1
#消息通知url
swscUrl=http://swsc.emoney.cn

#产品配置获取
productConfigUrl=http://ds.emoney.cn/res/api/config/GetConfig?configkey={0}

#用户领取优惠券配置Key
sendCouponConfigKey=660e7278-2205-41f3-898f-0131e4511d72

#小智盈续费打卡活动配置key
signActivityConfigKey=9fae0f69-6981-41e5-b45e-9bf4c8d86702

#APP获取视频进度
getVideoProgressUrl=http://api.m.emoney.cn/serverapi/videoPcApi/GetProgress?gid={0}&videoIdentity={1}

#APP提交视频进度
saveVideoProgressUrl=http://api.m.emoney.cn/serverapi/videoPcApi/SaveProgress

#获取大师续费活动用户签到信息
getSignInfoUrl=http://apitest.m.emoney.cn/serverapi/Checkin202309/Info

#领取签到奖励
getSignBenefitUrl=http://apitest.m.emoney.cn/serverapi/Checkin202309/reward

#获取领取奖励的历史记录
getSignRecordUrl=http://apitest.m.emoney.cn/serverapi/Checkin202309/records

#补齐奖励
getMissedRewardsUrl=http://apitest.m.emoney.cn/serverapi/Checkin202309/MakeUpForMissedRewards

#大师续费物流包
renewDsLogisticsPkg=ac-1240226171202166:3,ac-1240226171102371:1,ac-1240226170956118:3,ac-1240226170827245:1,ac-1240226170635826:3,ac-1240226170446626:1

#续费物流包对应的可抽奖次数和活动code配置
#renewLogisticsPkg=ac-1241015131704902:3:renewds20241022,ac-1241011110143755:3:renewds20241022,ac-1241011110022198:1:renewds20241022,ac-124101110570798:3:renewds20241022,ac-1241011105244220:1:renewds20241022,ac-1241011104737725:1:renewds20241022,ac-1241010180055935:2:20241014_renew,ac-1241010175310306:1:20241014_renew
renewLogisticsPkg=ac-1240223092910480:1:20241014_renew,ac-1240507102553726:2:20241014_renew,ac-1240221173615220:1:renewds20241022,ac-1240226153530353:3:renewds20241022
renewLogisticsBeginTime=2024-10-11 23:59:59
renewLogisticsEndTime=2024-11-22 23:59:59

#天玑续费物流包
renewTJLogisticsPkg = ac-1241028192559791:1:qyh20241113,ac-1241028120249818:1:qyh20241113,ac-124102812000369:1:qyh20241113,ac-124102417071154:1:qyh20241113,ac-1241024170624145:1:qyh20241113,ac-1241024164509115:3:qyh20241113,ac-1241024164311927:3:qyh20241113,ac-1241010095708281:3:qyh20241113,ac-1241016173727711:3:qyh20241113,ac-1241016164513957:3:qyh20241113,ac-1241016160214787:3:qyh20241113,ac-1241008154832933:3:qyh20241113,ac-1241016164858927:3:qyh20241113
renewTJLogisticsBeginTime=2024-10-14 23:59:59
renewTJLogisticsEndTime=2024-11-30 23:59:59

#报警邮箱
spring.mail.host=smtp.emoney.cn
spring.mail.port=25
spring.mail.username=<EMAIL>
spring.mail.password=8Zrhe@WdIm
spring.mail.default-encoding=UTF-8
spring.mail.properties.mail.stmp.socketFactory.class=javax.net.ssl.SSLSocketFactory
mail.fromMail.addr=<EMAIL>
mail.toMail.addr=<EMAIL>,<EMAIL>,<EMAIL>

#xxl-job-admin
xxl.job.admin.addresses=http://testxxljobadmin.emoney.cn/xxl-job-admin
xxl.job.executor.appname=activityjobexecutor