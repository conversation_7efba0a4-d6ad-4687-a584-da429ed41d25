package cn.emoney.service;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.vo.SignRecordVO;
import cn.emoney.pojo.vo.result.SignActivityConfig;

import java.util.Date;
import java.util.List;

public interface SignRecordService {

    /**
     * 自动签到打卡
     * @param uid
     * @param pid
     * @param date
     * @return
     */
    Result<SignRecordVO> autoSign(String uid, String pid, Date date);

    /**
     * 判断用户是否有打卡权限
     * @param uid
     * @param mobileX
     * @return
     */
    Boolean hasSignPrivilege(String uid, String mobileX, String account,
                             Date now, Date beginTime, List<SignActivityConfig> signActivityConfigList);

    /**
     * 批量更新用户计费信息
     */
    void batchRefreshSignFee();

    /**
     * 批量补发试用期
     */
    void batchCompensateSendPrivilege();

}
