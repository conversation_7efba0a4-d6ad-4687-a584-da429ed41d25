package cn.emoney.service;

import cn.emoney.pojo.CourseWatchDO;
import cn.emoney.pojo.GenseeLiveBasicDO;
import cn.emoney.pojo.GenseeRecordedBasicDO;
import cn.emoney.pojo.bo.CourseWatchDetailDTO;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

public interface GenseeService {
    Optional<GenseeRecordedBasicDO> getRecordedBasic(String lbId);

    Optional<GenseeLiveBasicDO> getLiveBasic(String webCastId);

    /**
     * 流式查询直播观看记录 大于等于duration的用户
     *
     * @param vId       Gensee Video ID
     * @param duration  合计观看时长
     * @param offsetId  分页查询的 offsetId, 填写后时长数据不会合并
     * @param batchSize 分页查询的 batchSize
     */
    Stream<List<CourseWatchDO>> streamLive(String vId, Duration duration,
                                           Integer offsetId, Integer batchSize);

    /**
     * 流式查询录播观看记录 大于等于duration的用户
     *
     * @param vId        Gensee Video ID
     * @param duration   单次观看时长
     * @param inTimeHead 观看开始时间 大于等于
     * @param inTimeTail 观看开始时间 小于等于
     * @param offsetId   分页查询的 offsetId
     * @param batchSize  分页查询的 batchSize
     */
    Stream<List<CourseWatchDO>> streamReplay(String vId, Duration duration,
                                             LocalDateTime inTimeHead, LocalDateTime inTimeTail,
                                             Integer offsetId, Integer batchSize);

    List<CourseWatchDetailDTO> getLiveWatchByUidAndVid(Long uid, String vid);

    List<CourseWatchDetailDTO> getRecordedWatchByUidAndVid(Long uid, String vid);

    List<CourseWatchDO> getUserByLiveWatchTimeSumGte(String vid, Duration duration, Integer offset, Integer limit);

    List<CourseWatchDO> getUserByRecordedWatchDurationGteAndStartTimeLte(String vid, Duration duration, LocalDateTime startTime, Integer offset, Integer limit);
}
