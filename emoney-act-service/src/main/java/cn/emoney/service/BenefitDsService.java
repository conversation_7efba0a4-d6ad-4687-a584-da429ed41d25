package cn.emoney.service;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-06
 */

public interface BenefitDsService {
    /**
     * 赠送签到奖励
     * <AUTHOR>
     * @date 2023/9/6 10:25
     * @param day
     * @param pid
     * @param emCard
     * @param mobileX
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    Result<String> sendSignBenefit(int day, String pid, String emCard, String mobileX);

    /**
     * 获取用户签到信息
     * <AUTHOR>
     * @date 2023/9/6 10:25
     * @param uid
     * @return cn.emoney.pojo.AppApiResult<cn.emoney.pojo.ActDsSignDetail>
     */
    AppApiResult<ActDsSignDetail> getUserSignInfo(String uid,String day);

    /**
     * 签到并领取奖励
     * <AUTHOR>
     * @date 2023/9/6 10:25
     * @param uid
     * @param prizeId
     * @return boolean
     */
    boolean getSignBenefit(String uid, String prizeId,String day);

    /**
     * 获取签到领取奖励记录
     * <AUTHOR>
     * @date 2023/9/6 10:26
     * @param uid
     * @return java.util.List<cn.emoney.pojo.ActDsSignRecordDO>
     */
    List<ActDsSignRecordDO> getSignRecords(String uid);

    void autoSendSignBenefit();

    AppApiResult<ActDsDailyChanceDO> getDailyInfo(String uid, String pid, String day);
    AppApiResult<Boolean> getDailyReward(String uid, String pid, String emAccount, String phone, String prizeId, String day);
    List<ActDsSignRecordDO> getDailyRecords(String uid);
    AppApiResult<Boolean> addDailyUser(String uid);

    AppApiResult<Integer> addUserTag(String mobileX);
}
