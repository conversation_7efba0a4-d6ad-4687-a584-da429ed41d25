package cn.emoney.service;

import lombok.Data;

public interface SMSVerificationService {
    String createCode(String mobile, String client, String useId);

    /**
     * 验证码校验
     *
     * @param key  验证码key
     * @param code 验证码
     * @return -1: 失效， 0: 失败， 1: 成功
     */
    Result verifyCode(String key, String code);

    @Data
    class Result {
        public static final Result INVALID = new Result(-1);
        public static final Result FAIL = new Result(0);
        private final int status;
        private final String mobile;

        private Result(int status) {
            this.status = status;
            this.mobile = null;
        }

        public Result(String mobile) {
            this.status = 1;
            this.mobile = mobile;
        }
    }
}
