package cn.emoney.service;

import cn.emoney.pojo.bo.FirstClassContentDTO;
import cn.emoney.pojo.bo.FirstClassCourseProgressDTO;
import cn.emoney.pojo.bo.FirstClassCourseRewardDTO;
import cn.emoney.pojo.bo.FirstClassProgressDTO;
import cn.emoney.pojo.vo.FirstClassCourseVO;
import org.springframework.data.util.Pair;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface FirstClassRollService {
    Optional<FirstClassContentDTO> findClassContent(Long questId);

    List<FirstClassCourseVO> userCourseList(Long questId, Long uid);

    Set<Integer> userCourseState(Long questId, Long uid);

    Optional<FirstClassProgressDTO> userClassProgress(Long questId, Long uid);

    Optional<FirstClassCourseProgressDTO> userCourseProgress(Long questId, Long uid, Integer subId);

    List<FirstClassCourseRewardDTO> userCourseReward(Long questId, Long uid, Integer subId);

    boolean checkClassCourse(Long questId, Long uid, Integer subId, String platform);

    int completeCourse(Long questId, Long uid, Integer subId, String platform);

    Pair<List<Integer>, List<Integer>> completeCourseDirect(Long questId, Long uid, List<Integer> subId, String platform);

    boolean completeQuest(Long questId, Long acceptId, Long uid);

    int getSpecialFunDays(Long uid);
}
