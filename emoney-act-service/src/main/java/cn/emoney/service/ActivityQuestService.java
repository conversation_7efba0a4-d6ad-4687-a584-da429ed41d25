package cn.emoney.service;

import cn.emoney.pojo.bo.ActivityQuestAcceptDTO;
import cn.emoney.pojo.bo.ActivityQuestDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

public interface ActivityQuestService {

    Optional<ActivityQuestDTO> findQuest(Long questId);

    Optional<ActivityQuestAcceptDTO> findById(Long acceptId);

    Optional<ActivityQuestAcceptDTO> findActive(Long questId, Long uid);

    /**
     * 找到活动的任务，如果没有则创建
     *
     * @param questId     任务ID
     * @param uid         用户ID
     * @param afterAccept 创建监听器 (curr, prev)
     * @throws IllegalArgumentException 任务不存在
     */
    ActivityQuestAcceptDTO findOrAccept(Long questId, Long uid,
                                        Consumer<ActivityQuestDTO> beforeAccept,
                                        BiConsumer<ActivityQuestAcceptDTO, ActivityQuestAcceptDTO> afterAccept) throws IllegalArgumentException;

    default ActivityQuestAcceptDTO findOrAccept(Long questId, Long uid) {
        return findOrAccept(questId, uid, null, null);
    }

    ActivityQuestAcceptDTO accept(Long questId, Long uid);

    boolean complete(Long questId, Long uid);

    boolean cancel(Long questId, Long uid);

    Page<ActivityQuestAcceptDTO> pageAcceptByExample(ActivityQuestAcceptDTO example, Pageable pageable);
}
