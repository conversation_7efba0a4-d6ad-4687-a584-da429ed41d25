package cn.emoney.service;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.LotteryDetailDO;
import cn.emoney.pojo.LotteryInfo;
import cn.emoney.pojo.LotteryPrizeDO;
import cn.emoney.pojo.vo.LotteryInfoVO;

import java.util.List;

public interface LotteryService {

    /**
     * 获取是否有权限抽奖
     * @param uid
     * @param pid
     * @return
     */
    Result<String> getPrivilege1201(String uid,String pid);

    /**
     * 20221217抽奖
     * @param uid
     * @param pid
     */
    Result<LotteryInfoVO> doLottery1201(String uid, String pid);

    /**
     * 获取中奖记录信息
     * @param uid
     * @return
     */
    Result<LotteryInfoVO> getLotteryInfo1201(String uid);

    LotteryPrizeDO doLottery(String mobile0X, String Uid, String batchNo,String ver);

    LotteryInfo getMyLotteryInfo(String mobile0x,String Uid, String batchNo);

    /***
     * 物流赠送逻辑
     * 1、入参：0x手机号、购买活动包、成交时间段
     * 2、入参：EM账号、赠送活动Code
     */
    /***
     * 功能描述:
     * 发送物流活动包奖品
     * @Param: [em, activityCode]
     * @Return: java.lang.Boolean
     * @Author: tengdengming
     * @Date: 2022/5/17 10:47
     */
    Boolean sendPrize(String mobile0x, String activityCode,String em,String orderId,String detId,String remark);

    Boolean recordLotteryDetail(LotteryDetailDO lotteryDetailDO);

    List<LotteryPrizeDO> initPrizeList(String verStr);

    LotteryPrizeDO lotteryRateTest(List<LotteryPrizeDO> prizelist);

    List<LotteryDetailDO> getRecentlyLotteryDetailList(String batchno);

    List<LotteryDetailDO> getLotteryDetailsFromDate(String batchno,String fromdate);

    void freshUserLotteryList(String batchno,String uid);
}
