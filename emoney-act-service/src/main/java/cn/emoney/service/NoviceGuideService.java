package cn.emoney.service;

import cn.emoney.pojo.NoviceGuideUserInfoDO;
import cn.emoney.pojo.TbSyncOaUserInfoDO;

/**
 * <AUTHOR>
 * @date 2022-01-05
 */

public interface NoviceGuideService {
    /**
     * 根据用户uid获取归属业务员信息
     * <AUTHOR>
     * @date 2022/1/5 16:10
     * @param uid
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.TbSyncOaUserInfoDO>
     */
    TbSyncOaUserInfoDO queryByUid(String uid);

    /**
     * 保存新手指引弹出用户信息
     * <AUTHOR>
     * @date 2022/1/6 16:48
     * @param noviceGuideUserInfoDO
     * @return cn.emoney.pojo.NoviceGuideUserInfoDO
     */
    NoviceGuideUserInfoDO insertNoviceGuideUserInfo(NoviceGuideUserInfoDO noviceGuideUserInfoDO);

    /**
     * 根据uid查询新手指引弹出用户信息
     * <AUTHOR>
     * @date 2022/1/6 16:49
     * @param uid
     * @return cn.emoney.pojo.NoviceGuideUserInfoDO
     */
    NoviceGuideUserInfoDO queryNoviceGuideUserInfoByUid(String uid);
}
