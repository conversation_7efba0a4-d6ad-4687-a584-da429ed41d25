package cn.emoney.service.limiter;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.convert.DurationStyle;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.scheduling.support.CronSequenceGenerator;
import org.springframework.stereotype.Component;
import org.springframework.util.ConcurrentReferenceHashMap;
import org.springframework.util.StringUtils;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Aspect
@Component
public class FrequencyLimitAspect {
    private final FrequencyLimiter frequencyLimiter;
    private final FrequencyLimitCachedExpressionEvaluator evaluator;
    private final Map<String, CronSequenceGenerator> cronMap = new ConcurrentReferenceHashMap<>(64);
    private final Map<String, Duration> durationMap = new ConcurrentReferenceHashMap<>(64);

    public FrequencyLimitAspect(FrequencyLimiter frequencyLimiter, FrequencyLimitCachedExpressionEvaluator evaluator) {
        this.frequencyLimiter = frequencyLimiter;
        this.evaluator = evaluator;
    }

    @Around("@annotation(cn.emoney.service.limiter.FrequencyLimit) || @annotation(cn.emoney.service.limiter.FrequencyLimits)")
    public Object isAllowed(ProceedingJoinPoint joinPoint) throws Throwable {
        List<FrequencyLimit> limits = getFrequencyLimits(joinPoint);

        int denied = frequencyLimiter.findDenied(limits.stream()
                .map(limit -> createRequest(joinPoint, limit))
                .collect(Collectors.toList())
        );
        if (denied != -1) {
            String message = limits.get(denied).message();
            if (StringUtils.hasText(message)) {
                String msg = evaluator.parseExpression(joinPoint, message);
                throw new FrequencyLimitException(msg);
            } else {
                throw new FrequencyLimitException();
            }
        }
        return joinPoint.proceed();
    }


    private FrequencyRequest createRequest(ProceedingJoinPoint joinPoint, FrequencyLimit limit) {
        String key = evaluator.parseExpression(joinPoint, limit.value());
        if (StringUtils.hasText(limit.cron())) {
            // FIXED_TIME
            long expireAt = cronMap.computeIfAbsent(limit.cron(), CronSequenceGenerator::new).next(new Date()).getTime();
            return new FrequencyRequest(key, limit.frequency(), expireAt, FrequencyRequest.FrequencyType.FixedTime);
        }
        if (StringUtils.hasText(limit.duration())) {
            long duration = durationMap.computeIfAbsent(limit.duration(), d -> DurationStyle.detect(d).parse(d)).toMillis();
            if (limit.type() == FrequencyRequest.FrequencyType.FixedTime) {
                Logger logger = LoggerFactory.getLogger(joinPoint.getTarget().getClass());
                logger.warn("FixedTime frequency limit without cron expression, use sliding window instead");
                return new FrequencyRequest(key, limit.frequency(), duration, FrequencyRequest.FrequencyType.SlidingWindow);
            }
            return new FrequencyRequest(key, limit.frequency(), duration, limit.type());
        }
        return new FrequencyRequest(key, limit.frequency(), 0, limit.type());
    }

    private List<FrequencyLimit> getFrequencyLimits(ProceedingJoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();

        FrequencyLimits annotations = AnnotationUtils.findAnnotation(method, FrequencyLimits.class);
        if (annotations != null) {
            return Arrays.asList(annotations.value());
        }
        FrequencyLimit annotation = AnnotationUtils.findAnnotation(method, FrequencyLimit.class);
        if (annotation != null) {
            return Collections.singletonList(annotation);
        }
        return Collections.emptyList();
    }
}
