package cn.emoney.service.limiter;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.style.ToStringCreator;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Min;
import java.util.Arrays;
import java.util.List;

@Data
@Slf4j
public class SimpleRedisRateLimiter {
    private final StringRedisTemplate redisTemplate;
    private final RedisScript<List<Long>> script;
    private Config config;

    @SuppressWarnings({"unchecked", "rawtypes"})
    public SimpleRedisRateLimiter(StringRedisTemplate redisTemplate, Config config) {
        this.redisTemplate = redisTemplate;
        this.config = config;
        DefaultRedisScript script = new DefaultRedisScript<>();
        script.setScriptSource(
                new ResourceScriptSource(new ClassPathResource("META-INF/scripts/<EMAIL>")));
        script.setResultType(List.class);
        this.script = script;
    }

    static List<String> getKeys(String id) {
        // use `{}` around keys to use Redis Key hash tags
        // this allows for using redis cluster

        // Make a unique key per user.
        String prefix = "request_rate_limiter.{" + id;

        // You need two Redis keys for Token Bucket.
        String tokenKey = prefix + "}.tokens";
        String timestampKey = prefix + "}.timestamp";
        return Arrays.asList(tokenKey, timestampKey);
    }

    public Response isAllowed(String id) {
        // How many requests per second do you want a user to be allowed to do?
        int replenishRate = config.getReplenishRate();

        // How much bursting do you want to allow?
        int burstCapacity = config.getBurstCapacity();

        // How many tokens are requested per request?
        int requestedTokens = config.getRequestedTokens();

        try {
            List<String> keys = getKeys(id);

            // The arguments to the LUA script. time() returns unixtime in seconds.
            // 该版本的 RedisTemplate 需要传数组，高版本为 List
            // List<String> scriptArgs = Arrays.asList(replenishRate + "", burstCapacity + "", "", requestedTokens + "");
            // allowed, tokens_left = redis.eval(SCRIPT, keys, args)
            try {
                List<Long> results = this.redisTemplate.execute(this.script, keys, replenishRate + "", burstCapacity + "", "", requestedTokens + "");
                if (results != null) {
                    boolean allowed = results.get(0) == 1L;
                    Long tokensLeft = results.get(1);

                    Response response = new Response(allowed, tokensLeft);

                    if (log.isDebugEnabled()) {
                        log.debug("response: " + response);
                    }
                    return response;
                }
            } catch (Exception e) {
                if (log.isDebugEnabled()) {
                    log.debug("Error calling rate limiter lua", e);
                }
            }
        } catch (Exception e) {
            /*
             * We don't want a hard dependency on Redis to allow traffic. Make sure to set
             * an alert so you know if this is happening too much. Stripe's observed
             * failure rate is 0.01%.
             */
            log.error("Error determining if user allowed from redis", e);
        }
        return new Response(true, -1L);
    }

    @Validated
    public static class Config {

        @Min(1)
        private int replenishRate;

        @Min(0)
        private int burstCapacity = 1;

        @Min(1)
        private int requestedTokens = 1;

        public int getReplenishRate() {
            return replenishRate;
        }

        public Config setReplenishRate(int replenishRate) {
            this.replenishRate = replenishRate;
            return this;
        }

        public int getBurstCapacity() {
            return burstCapacity;
        }

        public Config setBurstCapacity(int burstCapacity) {
            Assert.isTrue(burstCapacity >= this.replenishRate, "BurstCapacity(" + burstCapacity
                                                               + ") must be greater than or equal than replenishRate(" + this.replenishRate + ")");
            this.burstCapacity = burstCapacity;
            return this;
        }

        public int getRequestedTokens() {
            return requestedTokens;
        }

        public Config setRequestedTokens(int requestedTokens) {
            this.requestedTokens = requestedTokens;
            return this;
        }

        @Override
        public String toString() {
            return new ToStringCreator(this).append("replenishRate", replenishRate)
                    .append("burstCapacity", burstCapacity).append("requestedTokens", requestedTokens).toString();

        }

    }

    public static class Response {

        private final boolean allowed;

        private final long tokensRemaining;


        public Response(boolean allowed, long tokensRemaining) {
            this.allowed = allowed;
            this.tokensRemaining = tokensRemaining;
        }

        public boolean isAllowed() {
            return allowed;
        }

        @Override
        public String toString() {
            final StringBuffer sb = new StringBuffer("Response{");
            sb.append("allowed=").append(allowed);
            sb.append(", tokensRemaining=").append(tokensRemaining);
            sb.append('}');
            return sb.toString();
        }

    }
}
