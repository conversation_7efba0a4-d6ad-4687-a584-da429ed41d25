package cn.emoney.service.limiter;

import lombok.Data;
import lombok.Getter;

@Data
public class FrequencyRequest {
    private final String key;
    private final int frequency;
    private final long timeout;
    private final FrequencyType type;


    @Getter
    public enum FrequencyType {
        SlidingWindow(1),
        FixedDelay(2),
        FixedTime(3);

        private final int code;

        FrequencyType(int code) {
            this.code = code;
        }
    }
}
