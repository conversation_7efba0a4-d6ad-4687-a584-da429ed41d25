package cn.emoney.service.limiter;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.expression.AnnotatedElementKey;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.context.expression.CachedExpressionEvaluator;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class FrequencyLimitCachedExpressionEvaluator extends CachedExpressionEvaluator {
    private final BeanFactoryResolver resolver;
    private final Map<ExpressionKey, Expression> expressionCache = new ConcurrentHashMap<>(64);

    public FrequencyLimitCachedExpressionEvaluator(BeanFactory beanFactory) {
        this.resolver = new BeanFactoryResolver(beanFactory);
    }

    @SuppressWarnings("unchecked")
    public <T> T parseExpression(ProceedingJoinPoint joinPoint, String expression) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        EvaluationContext context = createEvaluationContext(method, joinPoint.getArgs());
        return (T) parseExpression(expression, new AnnotatedElementKey(method, joinPoint.getTarget().getClass()), context);
    }

    public Object parseExpression(String conditionExpression, AnnotatedElementKey methodKey, EvaluationContext evalContext) {
        return getExpression(this.expressionCache, methodKey, conditionExpression).getValue(evalContext, Object.class);
    }

    public EvaluationContext createEvaluationContext(Method method, Object[] args) {
        MethodBasedEvaluationContext evaluationContext = new MethodBasedEvaluationContext(null, method, args, getParameterNameDiscoverer());
        evaluationContext.setBeanResolver(resolver);
        return evaluationContext;
    }
}
