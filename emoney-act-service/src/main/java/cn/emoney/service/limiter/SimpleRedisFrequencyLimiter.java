package cn.emoney.service.limiter;

import cn.emoney.service.redis.RedisTemplateSupplier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class SimpleRedisFrequencyLimiter implements FrequencyLimiter {
    private final RedisTemplate<String, String> redisTemplate;
    private final RedisScript<Long> script;

    public SimpleRedisFrequencyLimiter(RedisTemplateSupplier supplier) {
        this.redisTemplate = supplier.get("act:frequency:limiter");
        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptSource(
                new ResourceScriptSource(new ClassPathResource("META-INF/scripts/request_frequency_limiter.lua")));
        script.setResultType(Long.class);
        this.script = script;
    }

    /**
     * 查询一个键是否被允许操作
     *
     * @param request 限频请求
     * @return 是否允许
     */
    public boolean isDenied(FrequencyRequest request) {
        return findDenied(Collections.singletonList(request)) != -1;
    }

    /**
     * 查询多个键是否被允许操作
     * <p>
     * 只要其中一个不被允许，就会失败，并释放已经获取的 tokens
     *
     * @param requests 限频请求
     * @return 是否允许，-1表示允许，其他表示获取失败时的下标
     */
    @Override
    public int findDenied(List<FrequencyRequest> requests) {
        List<String> keys = new ArrayList<>(requests.size());
        List<String> args = new ArrayList<>(requests.size() * 3);
        for (int i = 0; i < requests.size(); i++) {
            FrequencyRequest request = requests.get(i);
            if (request.getFrequency() <= 0 || request.getTimeout() <= 0) {
                return i;
            }
            String prefix;
            switch (request.getType()) {
                case SlidingWindow:
                    prefix = "sliding_window:";
                    break;
                case FixedDelay:
                    prefix = "fixed_delay:";
                    break;
                case FixedTime:
                    prefix = "fixed_time:";
                    break;
                default:
                    return -1;
            }
            keys.add(prefix + "{" + request.getKey() + "}");
            args.add("" + request.getFrequency());
            args.add("" + request.getTimeout());
            args.add("" + request.getType().getCode());
        }
        //noinspection DataFlowIssue
        return Math.toIntExact(redisTemplate.execute(script, keys, args.toArray()));
    }

}
