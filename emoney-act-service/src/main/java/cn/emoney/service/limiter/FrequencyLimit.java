package cn.emoney.service.limiter;

import org.intellij.lang.annotations.Language;
import org.intellij.lang.annotations.Pattern;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
@Documented
@Repeatable(FrequencyLimits.class)
public @interface FrequencyLimit {
    @Language("SpEL")
    String value();

    int frequency();

    FrequencyRequest.FrequencyType type() default FrequencyRequest.FrequencyType.SlidingWindow;

    @Language("SpEL")
    String message() default "";

    @Language("CronExp")
    String cron() default "";

    @Pattern("^[0-9]+(ns|us|ms|s|m|h|d)$")
    String duration() default "";
}
