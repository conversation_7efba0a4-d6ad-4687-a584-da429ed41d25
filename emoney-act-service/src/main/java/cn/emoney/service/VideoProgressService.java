package cn.emoney.service;

import cn.emoney.act.client.video.dto.VideoProgressDTO;
import cn.emoney.common.result.MobileResultDTO;
import cn.emoney.pojo.bo.videoProgressRequest;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022-09-05
 */
@Deprecated
public interface VideoProgressService {
    /**
     * 获取视频播放进度
     *
     * @param uid
     * @param videoId
     * @return long
     * <AUTHOR>
     * @date 2022/9/5 11:00
     */
    MobileResultDTO<Long> getVideoProgress(String uid, String videoId);

    /**
     * 保存视频进度
     *
     * @param request
     * @return long
     * <AUTHOR>
     * @date 2022/9/5 13:29
     */
    MobileResultDTO<Long> saveVideoProgress(videoProgressRequest request);

    /**
     * 获取视频播放时长
     *
     * @param uid
     * @param videoId
     * @return long
     * <AUTHOR>
     * @date 2022/9/5 11:00
     */
    Optional<Long> getVideoPlayTime(Long uid, String videoId);

    default Optional<Long> getVideoPlayTime(String uid, String videoId) {
        return getVideoPlayTime(Long.valueOf(uid), videoId);
    }

    Optional<Map<String, VideoProgressDTO>> getVideoPlayDetail(Long uid, List<String> videoId);

    List<VideoProgressDTO> getCoursePlayDetail(Long uid, List<Integer> courseId);

    default Optional<VideoProgressDTO> getCoursePlayDetail(Long uid, Integer courseId) {
        List<VideoProgressDTO> detail = getCoursePlayDetail(uid, Collections.singletonList(courseId));
        return Optional.of(detail).filter(s -> !s.isEmpty()).map(s -> s.get(0));
    }
}
