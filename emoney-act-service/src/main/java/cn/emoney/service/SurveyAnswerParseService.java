package cn.emoney.service;

import cn.emoney.pojo.WjxSurveyResultDO;
import cn.emoney.pojo.vo.survey.SurveyDisplayVO;

/**
 * 问卷答案解析服务接口
 * 用于解析问卷答案数据，转换为页面展示格式
 *
 * <AUTHOR>
 * @date 2025/01/11
 */
public interface SurveyAnswerParseService {

    /**
     * 解析问卷答案数据
     * 
     * @param surveyResult 问卷结果数据
     * @return 页面展示数据
     */
    SurveyDisplayVO parseSurveyAnswer(WjxSurveyResultDO surveyResult);

    /**
     * 解析问卷答案数据（带模板）
     * 
     * @param surveyResult 问卷结果数据
     * @param template 问卷模板（可选）
     * @return 页面展示数据
     */
    SurveyDisplayVO parseSurveyAnswer(WjxSurveyResultDO surveyResult, String template);

    /**
     * 格式化答案文本
     * 
     * @param answerText 原始答案文本
     * @param questionType 题目类型
     * @return 格式化后的答案文本
     */
    String formatAnswerText(String answerText, String questionType);

    /**
     * 从模板中提取题目文本
     * 
     * @param template 问卷模板JSON
     * @param questionNo 题号
     * @return 题目文本
     */
    String extractQuestionText(String template, String questionNo);
}
