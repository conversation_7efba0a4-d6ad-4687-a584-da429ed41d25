package cn.emoney.service;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.Lottery0808PrizeDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-27
 */
public interface Lottery20230808Service {
    Result<Lottery0808PrizeDO> doLottery0808(String actCode, String uid, String pid, String source);
    Result<String> sendPP0808(String actCode,String uid,String pid,String type);
    boolean getTipStatusByDay(String uid,String type);
    Integer getLotteryCountByDay(String actCode,String uid);
    List<Act588BenifitRecordDO> getMyLottery0808List(String actCode, String uid);
    List<Act588BenifitRecordDO> refreshMyLottery0808List(String actCode,String uid);
    Integer refreshLotteryCountByDay(String actCode,String uid);
    Integer getPrizesNum();
}
