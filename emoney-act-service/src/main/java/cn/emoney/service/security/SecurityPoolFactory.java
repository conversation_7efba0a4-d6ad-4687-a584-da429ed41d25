package cn.emoney.service.security;

import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;

import javax.crypto.Cipher;
import java.security.PrivateKey;

public class SecurityPoolFactory extends BasePooledObjectFactory<Cipher> {
    private final PrivateKey privateKey;

    public SecurityPoolFactory(PrivateKey privateKey) {
        this.privateKey = privateKey;
    }

    @Override
    public Cipher create() throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return cipher;
    }

    @Override
    public PooledObject<Cipher> wrap(Cipher obj) {
        return new DefaultPooledObject<>(obj);
    }
}
