package cn.emoney.service.security;

import org.apache.commons.pool2.impl.AbandonedConfig;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;

public class CipherPool extends GenericObjectPool<Cipher> {

    public CipherPool(String privateKey) throws NoSuchAlgorithmException, InvalidKeySpecException {
        this(privateKey, new GenericObjectPoolConfig<>());
        setMinIdle(1);
    }

    public CipherPool(String privateKey, GenericObjectPoolConfig<Cipher> config) throws NoSuchAlgorithmException, InvalidKeySpecException {
        this(createByPem(privateKey), config);
    }

    public CipherPool(String privateKey, GenericObjectPoolConfig<Cipher> config, AbandonedConfig abandonedConfig) throws NoSuchAlgorithmException, InvalidKeySpecException {
        this(createByPem(privateKey), config, abandonedConfig);
    }

    public CipherPool(PrivateKey privateKey) {
        this(privateKey, new GenericObjectPoolConfig<>());
    }

    public CipherPool(PrivateKey privateKey, GenericObjectPoolConfig<Cipher> config) {
        super(new SecurityPoolFactory(privateKey), config);
    }

    public CipherPool(PrivateKey privateKey, GenericObjectPoolConfig<Cipher> config, AbandonedConfig abandonedConfig) {
        super(new SecurityPoolFactory(privateKey), config, abandonedConfig);
    }

    public static PrivateKey createByPem(String pem) throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] keyBytes = Base64.getMimeDecoder().decode(pem);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        KeySpec privateKeySpec = new PKCS8EncodedKeySpec(keyBytes);
        return keyFactory.generatePrivate(privateKeySpec);
    }

    @Override
    public Cipher borrowObject() {
        return this.borrowObject(getMaxWaitMillis());
    }

    @Override
    public Cipher borrowObject(final long borrowMaxWaitMillis) {
        try {
            return super.borrowObject(borrowMaxWaitMillis);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void invalidateObject(Cipher obj) {
        try {
            super.invalidateObject(obj);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
