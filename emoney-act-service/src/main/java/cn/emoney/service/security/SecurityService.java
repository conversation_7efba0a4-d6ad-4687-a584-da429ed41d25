package cn.emoney.service.security;

import cn.emoney.common.utils.RSAUtils;
import cn.emoney.service.redis.RedisTemplateSupplier;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.cache.RemovalListener;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.*;
import java.util.Base64;

@Slf4j
@Component
public class SecurityService {
    private final LoadingCache<String, CipherPool> cipherCachePool = CacheBuilder.newBuilder().maximumSize(2)
            .removalListener((RemovalListener<String, CipherPool>) notification -> {
                if (notification.getValue() != null) {
                    notification.getValue().close();
                }
            })
            .build(new CacheLoader<String, CipherPool>() {
                @Override
                public CipherPool load(String key) throws Exception {
                    return new CipherPool(key);
                }
            });
    private final BoundValueOperations<String, KeyPairDTO> keyPairOps;

    public SecurityService(RedisTemplateSupplier supplier) {
        this.keyPairOps = supplier.get(KeyPairDTO.class).boundValueOps("act:security:keypair");
    }

    /**
     * 获取或选举新的 keyPair，凌晨3点过期
     * RedisTemplate 2.6才支持 EXAT 指令, 由于 redisson-spring-boot-starter 引用的 Redisson 连接不支持, 所以分两步设定时间
     *
     * @return PublicKey
     */
    public String getPublicKey() {
        KeyPairDTO keyPair = keyPairOps.get();
        if (keyPair != null) {
            return keyPair.getPublicKey();
        }

        keyPair = new KeyPairDTO(RSAUtils.getCurKeyPair());

        Boolean ifAbsent = keyPairOps.setIfAbsent(keyPair, Duration.ofMinutes(10));
        if (ifAbsent != null) {
            if (ifAbsent) {
                keyPairOps.expireAt(ZonedDateTime.of(
                        LocalDate.now().plusDays(1),
                        LocalTime.of(3, 0),
                        ZoneId.systemDefault()).toInstant());
                log.info("keypair 选举成功");
                cipherCachePool.refresh(keyPair.getPrivateKey());
                return keyPair.getPublicKey();
            } else {
                log.warn("啊? keypair 选举冲突了, 被人抢先一步");
                return getPublicKey();
            }
        }
        throw new UnsupportedOperationException("无法判断 keypair 选举结果");
    }

    public String decryptToString(String body) {
        return new String(decrypt(body));
    }

    public byte[] decrypt(String body) {
        return decrypt(Base64.getMimeDecoder().decode(body));
    }

    public byte[] decrypt(byte[] body) {
        CipherPool pool = getPool();
        if (pool != null) {
            Cipher cipher = pool.borrowObject();
            try {
                return cipher.doFinal(body);
            } catch (Exception e) {
                try {
                    cipher.doFinal();
                } catch (Exception ex) {
                    pool.invalidateObject(cipher);
                }
                log.error("DECRYPT Error occur", e);
            } finally {
                pool.returnObject(cipher);
            }
        }
        return null;
    }


    public InputStream decrypt(InputStream content) {
        CipherPool pool = getPool();
        if (pool != null) {
            Cipher cipher = pool.borrowObject();
            return new CipherInputStream(content, cipher) {
                @Override
                public void close() throws IOException {
                    super.close();
                    pool.returnObject(cipher);
                }
            };
        }
        return null;
    }

    public CipherPool getPool() {
        KeyPairDTO keyPair = keyPairOps.get();
        if (keyPair == null || StringUtils.isEmpty(keyPair.getPrivateKey())) {
            log.error("can't find private key");
            return null;
        }
        return cipherCachePool.getUnchecked(keyPair.getPrivateKey());
    }

    @Data
    static class KeyPairDTO {
        private final String publicKey;
        private final String privateKey;

        @SuppressWarnings("unused")
        public KeyPairDTO(String publicKey, String privateKey) {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        private KeyPairDTO(KeyPair keyPair) {
            PublicKey kPublic = keyPair.getPublic();
            PrivateKey kPrivate = keyPair.getPrivate();
            this.publicKey = kPublic != null ? Base64.getMimeEncoder().encodeToString(kPublic.getEncoded()) : null;
            this.privateKey = kPrivate != null ? Base64.getMimeEncoder().encodeToString(kPrivate.getEncoded()) : null;
        }
    }
}
