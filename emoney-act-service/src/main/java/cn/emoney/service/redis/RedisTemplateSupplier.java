package cn.emoney.service.redis;

import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.util.Objects;
import java.util.function.Function;

public class RedisTemplateSupplier {
    private final RedisConnectionFactory factory;
    private final RedisSerializerSupplier serializerSupplier;

    public RedisTemplateSupplier(RedisConnectionFactory factory, RedisSerializerSupplier serializerSupplier) {
        this.factory = factory;
        this.serializerSupplier = serializerSupplier;
    }

    /**
     * 获取一个 StringRedisTemplate
     *
     * @param prefix 前缀
     */
    public RedisTemplate<String, String> get(String... prefix) {
        return get(RedisSerializer.string(), prefix);
    }

    /**
     * 获取一个 Jackson JSON 序列化 的 RedisTemplate
     *
     * @param vClass 值类
     * @param prefix 前缀
     */
    public <V> RedisTemplate<String, V> get(Class<V> vClass, String... prefix) {
        return get(serializerSupplier.get(vClass), prefix);
    }

    /**
     * 获取一个 ToString 值序列化的 RedisTemplate
     *
     * @param deserializer String 值反序列化
     * @param prefix       前缀
     */
    public <V> RedisTemplate<String, V> get(Function<String, V> deserializer, String... prefix) {
        return get(Objects::toString, deserializer, prefix);
    }

    /**
     * 获取一个 String 值序列化的 RedisTemplate
     *
     * @param serializer   String 值序列化
     * @param deserializer String 值反序列化
     * @param prefix       前缀
     */
    public <V> RedisTemplate<String, V> get(Function<V, String> serializer, Function<String, V> deserializer, String... prefix) {
        return get(RedisSerializerSupplier.get(serializer, deserializer), prefix);
    }

    /**
     * 获取一个 RedisTemplate
     *
     * @param valueSerializer 值序列化器
     * @param prefix          前缀
     */
    public <V> RedisTemplate<String, V> get(RedisSerializer<V> valueSerializer, String... prefix) {
        return createTemplate(
                serializerSupplier.prefix(prefix),
                valueSerializer,
                RedisSerializer.string(),
                valueSerializer);
    }

    /**
     * 创建一个自定义 RedisTemplate
     *
     * @param keySerializer     key 序列化器
     * @param hashKeySerializer hash-key 序列化器
     * @param valueSerializer   值序列化器
     */
    public <K, V, HK, HV> RedisTemplate<K, V> createTemplate(RedisSerializer<K> keySerializer,
                                                             RedisSerializer<V> valueSerializer,
                                                             RedisSerializer<HK> hashKeySerializer,
                                                             RedisSerializer<HV> hashValueSerializer) {
        RedisTemplate<K, V> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        template.setKeySerializer(keySerializer);
        template.setValueSerializer(valueSerializer);
        template.setHashKeySerializer(hashKeySerializer);
        template.setHashValueSerializer(hashValueSerializer);
        template.afterPropertiesSet();
        return template;
    }

    public <K, V> RedisTemplate<K, V> createTemplate(RedisSerializer<K> keySerializer,
                                                     RedisSerializer<V> valueSerializer) {
        return createTemplate(keySerializer, valueSerializer, null, valueSerializer);
    }
}
