package cn.emoney.service.redis;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

@Configuration
public class RedisSupplierAutoConfiguration {

    @Bean
    @ConditionalOnClass(ObjectMapper.class)
    public RedisSerializerSupplier redisSerializerSupplier(ObjectProvider<ObjectMapper> mapper) {
        return new RedisSerializerSupplier(mapper.getIfAvailable(ObjectMapper::new));
    }

    @Bean
    @ConditionalOnClass(RedisTemplate.class)
    public RedisTemplateSupplier redisTemplateSupplier(RedisConnectionFactory factory, RedisSerializerSupplier serializerSupplier) {
        return new RedisTemplateSupplier(factory, serializerSupplier);
    }
}
