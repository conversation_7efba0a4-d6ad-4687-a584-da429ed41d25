package cn.emoney.service.redis;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Redis 序列化器 提供者
 */
public class RedisSerializerSupplier {
    private final ObjectMapper defaultMapper;
    private final Map<Class<?>, GenericToStringSerializer<?>> genericMap = new ConcurrentReferenceHashMap<>(16, ConcurrentReferenceHashMap.ReferenceType.WEAK);
    private final Map<Class<?>, RedisSerializer<?>> jsonMap = new ConcurrentReferenceHashMap<>(16, ConcurrentReferenceHashMap.ReferenceType.WEAK);
    private final Map<String, RedisSerializer<?>> prefixMap = new ConcurrentReferenceHashMap<>(16, ConcurrentReferenceHashMap.ReferenceType.WEAK);
    private boolean prefixCheck = false;

    public RedisSerializerSupplier(ObjectMapper mapper) {
        this.defaultMapper = mapper.copy().setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public <V> RedisSerializer<V> get(Class<V> vClass) {
        return get(vClass, defaultMapper);
    }

    @SuppressWarnings("unchecked")
    public <V> RedisSerializer<V> get(Class<V> vClass, ObjectMapper mapper) {
        if (vClass == String.class) {
            return (RedisSerializer<V>) RedisSerializer.string();
        }
        return (Jackson2JsonRedisSerializer<V>) jsonMap.computeIfAbsent(vClass, clazz -> {
            Jackson2JsonRedisSerializer<V> serializer = new Jackson2JsonRedisSerializer<>(vClass);
            serializer.setObjectMapper(mapper);
            return serializer;
        });
    }

    public static <V> RedisSerializer<V> get(@NonNull Function<V, String> serializer,
                                             @Nullable Function<String, V> deserializer) {
        return new RedisSerializer<V>() {
            @Override
            public byte[] serialize(V v) throws SerializationException {
                if (v == null) {
                    return null;
                }
                String apply = serializer.apply(v);
                return (apply == null ? null : apply.getBytes(StandardCharsets.UTF_8));
            }

            @Override
            public V deserialize(byte[] bytes) throws SerializationException {
                if (bytes == null || deserializer == null) {
                    return null;
                }
                return deserializer.apply(new String(bytes, StandardCharsets.UTF_8));
            }

            @Override
            public boolean canSerialize(Class<?> type) {
                return true;
            }
        };
    }

    public static <T> RedisSerializer<T> prefix(Function<T, byte[]> serializer,
                                                Function<byte[], T> deserializer,
                                                String... prefixes) {
        String prefix = Arrays.stream(prefixes)
                .peek(part -> Assert.notNull(part, "prefix part can't be null"))
                .map(String::trim)
                .peek(part -> Assert.hasLength(part, "prefix part can't be empty"))
                .collect(Collectors.joining(":"));
        if (prefix.isEmpty()) {
            return new RedisSerializer<T>() {
                @Override
                public byte[] serialize(T t) throws SerializationException {
                    return serializer.apply(t);
                }

                @Override
                public T deserialize(byte[] bytes) throws SerializationException {
                    return deserializer.apply(bytes);
                }
            };
        }
        return new PrefixRedisSerializer<>(prefix.getBytes(StandardCharsets.UTF_8), serializer, deserializer);
    }

    public RedisSerializer<String> prefix(String... prefixes) {
        return prefix(String.class, prefixes);
    }

    @SuppressWarnings("unchecked")
    public <K> RedisSerializer<K> prefix(Class<K> kClass, String... prefixes) {
        String prefix = Arrays.stream(prefixes)
                .peek(part -> Assert.notNull(part, "prefix part can't be null"))
                .map(String::trim)
                .peek(part -> Assert.hasLength(part, "prefix part can't be empty"))
                .collect(Collectors.joining(":", "", ":"));
        if (prefix.isEmpty()) {
            return generic(kClass);
        }

        return (RedisSerializer<K>) prefixMap.computeIfAbsent(prefix, p0 -> {
            byte[] prefixBytes = prefix.getBytes(StandardCharsets.UTF_8);
            RedisSerializer<K> bodySerializer = generic(kClass);
            return new PrefixRedisSerializer<>(prefixBytes, bodySerializer::serialize, bodySerializer::deserialize, prefixCheck);
        });
    }

    @SuppressWarnings("unchecked")
    public <K> RedisSerializer<K> generic(Class<K> kClass) {
        if (String.class.equals(kClass)) {
            return (RedisSerializer<K>) RedisSerializer.string();
        }
        if (byte[].class.equals(kClass)) {
            return (RedisSerializer<K>) RedisSerializer.byteArray();
        }
        return (GenericToStringSerializer<K>) genericMap.computeIfAbsent(kClass, GenericToStringSerializer::new);
    }

    static class PrefixRedisSerializer<T> implements RedisSerializer<T> {
        private final byte[] prefix;
        private final Function<T, byte[]> serializer;
        private final Function<byte[], T> deserializer;
        private final boolean prefixCheck;

        PrefixRedisSerializer(byte[] prefix, @NonNull Function<T, byte[]> serializer, @Nullable Function<byte[], T> deserializer, boolean prefixCheck) {
            this.prefix = prefix;
            this.serializer = Objects.requireNonNull(serializer, "can't create with null");
            this.deserializer = deserializer;
            this.prefixCheck = prefixCheck;
        }

        PrefixRedisSerializer(byte[] prefix, @NonNull Function<T, byte[]> serializer, @Nullable Function<byte[], T> deserializer) {
            this(prefix, serializer, deserializer, false);
        }

        @Override
        public byte[] serialize(@Nullable T key) {
            if (key == null) {
                return null;
            }
            byte[] body = serializer.apply(key);
            if (body == null) {
                return null;
            }
            byte[] wrapKey = new byte[prefix.length + body.length];
            System.arraycopy(prefix, 0, wrapKey, 0, prefix.length);
            System.arraycopy(body, 0, wrapKey, prefix.length, body.length);
            return wrapKey;
        }

        @Override
        public T deserialize(@Nullable byte[] bytes) {
            if (bytes == null) {
                return null;
            }
            if (prefixCheck && bytes.length <= prefix.length || mismatch(prefix, bytes) != prefix.length) {
                throw new IllegalStateException("prefix mismatch");
            }
            byte[] realKey = new byte[bytes.length - prefix.length];
            System.arraycopy(bytes, prefix.length, realKey, 0, realKey.length);
            return deserializer.apply(realKey);
        }

        @Override
        public boolean canSerialize(Class<?> type) {
            // 暂不判断
            return true;
        }

        public static int mismatch(byte[] a, byte[] b) {
            int length = Math.min(a.length, b.length);
            if (a == b)
                return -1;
            for (int i = 0; i < length; i++) {
                if (a[i] != b[i]) {
                    return i;
                }
            }
            return a.length == b.length ? -1 : length;
        }
    }
}
