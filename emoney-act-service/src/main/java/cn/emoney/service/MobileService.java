package cn.emoney.service;

import cn.emoney.common.result.ApiGateWayResult;

/**
 * <AUTHOR>
 * @date 2021-12-14
 */
public interface MobileService {
    /**
     * 加密手机号
     * <AUTHOR>
     * @date 2021/12/14 17:51
     * @param mobile
     * @return java.lang.String
     */
    String mobileEncrpt(String mobile);

    /**
     * 发送短信
     * <AUTHOR>
     * @date 2021/12/17 14:25
     * @param mobile 手机号
     * @param Content 短信内容
     * @param useid 短信用途id
     * @return cn.emoney.activitycommon.result.ApiGateWayResult<java.lang.String>
     */
    ApiGateWayResult<String> sendTextMessage(String mobile, String Content, String useid);

     void sendWarnTextMessage(String account, String Content);

    void ensureUserRegistered(String mobile);

    String mobileEncrpt_Upper(String mobile);
}
