package cn.emoney.service;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.Lottery0808PrizeDO;
import cn.emoney.pojo.LotteryCountDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-28
 */
public interface Lottery20240306Service {
    Result<Lottery0808PrizeDO> doLottery(String actCode, String uid, String pid, String source);
    List<Act588BenifitRecordDO> getMyLotteryList(String actCode, String uid);
    LotteryCountDO refreshLotteryCount(String actCode, String uid,String mobileX);
    List<Act588BenifitRecordDO> refreshMyLotteryList(String actCode,String uid);
    LotteryCountDO getLotteryCount(String actCode, String uid, String mobileX);
    void autoGetUserPackagePCount();
    void autoClearUserPackagePCount();
    Integer getPrizesNum();
}
