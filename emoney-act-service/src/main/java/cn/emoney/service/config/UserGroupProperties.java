package cn.emoney.service.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties("act.service.user-group")
public class UserGroupProperties {
    private String listGroup = "http://api.userradar.emoney.cn/api/GetUserGroupList";
    private String checkGroup = "http://api.userradar.emoney.cn/api/CheckUserGroup";
    private String listGroupUser = "http://api.userradar.emoney.cn/api/GetUserListByGroup?Groupid={0}";
}
