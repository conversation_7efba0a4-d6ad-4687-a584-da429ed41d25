package cn.emoney.service.config.db;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.Optional;
import java.util.stream.Stream;

@Configuration
@ConditionalOnProperty("spring.datasource.gensee.url")
@MapperScan(basePackages = "cn.emoney.mapper.gensee", sqlSessionTemplateRef = "genseeSqlSessionTemplate")
public class GenseeDataSourceConfig {
    private static final ResourcePatternResolver resourceResolver = new PathMatchingResourcePatternResolver();

    @Bean(value = "genseeDataSourceProperties", autowireCandidate = false)
    @ConfigurationProperties(prefix = "spring.datasource.gensee")
    public DataSourceProperties dataSourceProperties() {
        return new DataSourceProperties();
    }

    @Bean("genseeDataSource")
    public DataSource dataSource() {
        return dataSourceProperties().initializeDataSourceBuilder().build();
    }

    @Bean(name = "genseeSqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource());
        bean.setMapperLocations();
        bean.setMapperLocations(resolveMapperLocations("classpath:mybatis/mapper/gensee/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "genseeTransactionManager")
    public DataSourceTransactionManager transferTransactionManager() {
        return new DataSourceTransactionManager(dataSource());
    }

    @Bean(name = "genseeSqlSessionTemplate")
    public SqlSessionTemplate transferSqlSessionTemplate() throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory());
    }

    public Resource[] resolveMapperLocations(String... locations) {
        return Stream.of(Optional.ofNullable(locations).orElse(new String[0]))
                .flatMap(location -> Stream.of(getResources(location))).toArray(Resource[]::new);
    }

    private Resource[] getResources(String location) {
        try {
            return resourceResolver.getResources(location);
        } catch (IOException e) {
            return new Resource[0];
        }
    }

}
