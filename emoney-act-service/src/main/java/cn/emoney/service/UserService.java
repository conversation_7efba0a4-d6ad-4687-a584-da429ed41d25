package cn.emoney.service;

import cn.emoney.common.result.Result;
import cn.emoney.common.result.ResultInfo;
import cn.emoney.pojo.UserPeriodInfoDO;
import cn.emoney.pojo.bo.MobileRegisterResultDTO;
import cn.emoney.pojo.vo.*;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2021/12/14 14:08
 */
public interface UserService {
    /**
     * 获取绑定的账号列表
     * <AUTHOR>
     * @date 2021/12/14 14:09
     * @param uid
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.dto.BindAccountDTO>
     */
    List<BindAccountVO> GetBindAccountList(String uid);

    /**
     * 根据uid和pid获取绑定用户账号(em 手机密文)
     * 未指定pid则获取第一个绑定的em号
     * <AUTHOR>
     * @date 2023/5/22 14:06
     * @param uid
     * @param requestPid
     * @return cn.emoney.pojo.vo.LoginUserInfoVO
     */
    LoginUserInfoVO getBoundUserInfo(String uid,String requestPid);

    /**
     * 获取账号所属PID
     * <AUTHOR>
     * @date 2021/12/14 15:32
     * @param username
     * @return java.lang.String
     */
    String GetAccountPID(String username);

    /**
     * 获取账号LoginID
     * <AUTHOR>
     * @date 2021/12/16 14:30
     * @param username
     * @return cn.emoney.activityweb.repository.dao.entity.dto.UserLoginIDInfo
     */
    UserLoginIdInfoVO GetLoginIDInfoByAccount(String username);

    /**
     * 根据账号获取用户所有账号列表
     * <AUTHOR>
     * @date 2022/1/17 10:06
     * @param account
     * @return java.util.List<cn.emoney.pojo.vo.AccountVO>
     */
    List<AccountVO> queryAccountListByAccount(String account);

    /**
     * 获取用户过期前最高版本
     * <AUTHOR>
     * @date 2023/10/13 15:55
     * @param account
     * @return java.lang.String
     */
    String getHighPIDByAccount(String account);
    /**
     * 根据 UID 获取用户所有账号列表
     * <AUTHOR>
     * @date 2022/1/17 10:06
     * @param uid
     * @return java.util.List<cn.emoney.pojo.vo.AccountVO>
     */
    List<AccountVO> listAccountByUid(String uid);

    /**
     * 活动状态统一方法[倒计名额][是否参加过此活动]等
     * <AUTHOR>
     * @date 2023/1/9 14:38
     * @param actCode
     * @param uid
     * @param value
     * @return java.lang.String
     */
    String AddCountByActCode(String actCode,String uid,String value);

    /**
     * 查询是否参加过此活动
     * <AUTHOR>
     * @date 2023/1/9 15:00
     * @param actCodes(actcode1,actcode2)
     * @param uid
     * @return java.lang.String
     */
    String IsSubmitByActCodes(String actCodes,String uid);
    /**
     * 获取该活动已参加的名额
     * <AUTHOR>
     * @date 2023/1/16 14:08
     * @param actCodes
     * @return java.lang.String
     */
    String GetCountByActCodes(String actCodes);

    String AddUsedCount(String actCode);

    String DecrUsedCount(String actCode);

    Result<String> sendPrivilege_New(LoginUserInfoVO loginUser, String activityID, String reason, Integer accountType,String actCode);

    /**
     * 手机号注册体验版，视频模块中用于获取该手机号的密码( ?
     *
     * <AUTHOR>
     * @date 2023/07/13 16:27
     */
    MobileRegisterResultDTO registerByMobile(String mobile, String sid, String tid, String hardwareInfo, String clientVersion);

    default MobileRegisterResultDTO registerByMobile(String mobile, String sid, String tid) {
        return registerByMobile(mobile, sid, tid, null, "********");
    }

    /**
     * 根据PID验证账号的用户名和密码，并返回账号过期时间
     *
     * <AUTHOR>
     * @date 2023/07/14 15:55
     */
    ResultInfo<ValidateUserVO> validateUserPasswordByPID(String username, String password, String pid);

    /**
     * 检查是否券商开户
     * <AUTHOR>
     * @date 2023/4/23 10:45
     * @param mobileX
     * @return boolean
     */
    boolean checkPresent_QS(String mobileX);

    /**
     * 获取用户软件周期
     * <AUTHOR>
     * @date 2024/4/11 14:39
     * @param uid
     * @return cn.emoney.pojo.UserPeriodInfoDO.Software
     */
    UserPeriodInfoDO.Software getUserPeriodInfo(String uid);

    /**
     * 检查用户是否在过滤器中
     * <AUTHOR>
     * @date 2025/4/23 15:16
     * @param uid
     * @param filterIds
     * @return java.util.Map<java.lang.String,java.lang.Boolean>
     */
    Map<String, Boolean> checkUserFilterMatchs(String uid, String filterIds);
}
