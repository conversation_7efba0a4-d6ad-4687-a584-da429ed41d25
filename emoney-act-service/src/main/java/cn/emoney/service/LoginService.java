package cn.emoney.service;

import cn.emoney.pojo.vo.LoginUserInfoVO;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @date 2021-12-24
 */
public interface LoginService {
    /**
     * 获取登录用户信息
     * <AUTHOR>
     * @date 2021/12/24 17:32
     * @param request
     * @param actCode
     * @return cn.emoney.activityweb.repository.dao.entity.vo.LoginUserInfoVO
     */
    LoginUserInfoVO GetLoginUserInfo(HttpServletRequest request, String actCode);

    /**
     * 登录用户信息保存
     * <AUTHOR>
     * @date 2021/12/24 17:32
     * @param request
     * @param response
     * @param actCode
     * @param loginUserInfoVO
     * @return java.lang.Boolean
     */
    LoginUserInfoVO SetLoginUserInfo(HttpServletRequest request, HttpServletResponse response, String actCode, LoginUserInfoVO loginUserInfoVO);


    String getPublicKey();

    String decryptWithPrivateKey(String encryptText) throws Exception;


}
