package cn.emoney.service;

import cn.emoney.act.quest.CourseWatchQuest;
import cn.emoney.service.impl.CourseWatchQuestServiceImpl;

import java.util.List;
import java.util.Optional;

public interface CourseWatchQuestService {

    /**
     * 获取所有可执行任务
     */
    List<CourseWatchQuest> listQuests();


    /**
     * 获取任务详情
     *
     * @param id 任务id
     */
    Optional<CourseWatchQuest> findQuest(Long id);

    /**
     * 检查用户任务完成情况
     *
     * @param questId 任务id
     * @param uid     用户id
     * @return
     */
    CourseWatchQuestServiceImpl.QuestContext checkQuestByUid(Long questId, Long uid);
}
