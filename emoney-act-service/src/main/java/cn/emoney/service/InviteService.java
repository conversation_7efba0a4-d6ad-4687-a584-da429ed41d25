package cn.emoney.service;

import cn.emoney.pojo.InviteAwardDetailDO;
import cn.emoney.pojo.InviteInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-06
 */
public interface InviteService {
    /**
     * 查询所有未发放用户
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.InviteInfoDO>
     * <AUTHOR>
     * @date 2021-12-6 10:23
     */
    List<InviteInfoDO> queryAllUnSend();

    /**
     * 根据被邀请人查询邀请记录
     * @param beInvited
     * @return cn.emoney.activityweb.repository.dao.entity.InviteInfoDO
     * <AUTHOR>
     * @date 2021/12/14 10:57
     */
    InviteInfoDO queryByBeInvited(String beInvited);

    /**
     * 根据邀请人和被邀请人手机号查询赠送记录
     * <AUTHOR>
     * @date 2021/12/14 13:21
     * @param mobileX
     * @param orderMobileX
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.InviteAwardDetailDO>
     */
    List<InviteAwardDetailDO> getByMobileXAndOrderMobile(String mobileX, String orderMobileX);

    /**
     * 根据订单号查询赠送记录
     * <AUTHOR>
     * @date 2021/12/22 13:29
     * @param orderId
     * @return cn.emoney.activityweb.repository.dao.entity.InviteAwardDetailDO
     */
    InviteAwardDetailDO getByOrderId(String orderId);
}
