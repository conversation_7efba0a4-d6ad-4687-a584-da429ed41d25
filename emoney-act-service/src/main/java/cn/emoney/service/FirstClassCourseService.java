package cn.emoney.service;

import cn.emoney.pojo.bo.FirstClassCourseDTO;
import org.springframework.data.domain.Sort;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

public interface FirstClassCourseService {

    Optional<FirstClassCourseDTO> findById(Integer id);

    /**
     * 按 栏目ID 获取课程列表
     *
     * @param columnId 栏目ID
     */
    List<FirstClassCourseDTO> listCourse(Integer columnId, Sort sort, int size);

    /**
     * 按 栏目ID 获取课程列表
     *
     * @param columnId 栏目ID
     */
    default List<FirstClassCourseDTO> listCourse(Integer columnId, int size) {
        return listCourse(columnId, Sort.by(Sort.Direction.DESC, "beginTime"), size);
    }

    /**
     * 通过栏目ID、动态日期 获取课程列表
     *
     * @param columnId 栏目ID
     * @param size     课程数量
     * @param date     日期
     * @param safeSize 安全数量(向前查找)
     */
    default List<FirstClassCourseDTO> listCourse(Integer columnId, int size, LocalDateTime date, int safeSize) {
        if (columnId == null || size <= 0) {
            return Collections.emptyList();
        }
        if (safeSize > size) {
            throw new IllegalArgumentException("safeSize must be less than size");
        }
        List<FirstClassCourseDTO> result = new ArrayList<>(size);
        if (safeSize > 0) {
            List<FirstClassCourseDTO> before = listByTimeBackward(columnId, date.minusNanos(1), safeSize);
            result.addAll(before);
        }
        if (result.size() < size) {
            List<FirstClassCourseDTO> after = listByTimeForward(columnId, date, size - result.size());
            result.addAll(after);
        }
        return result;
    }

    /**
     * 按时间前向查询课表, 未来时间
     *
     * @param columnId 栏目ID
     * @param date     时间
     * @param size     数量
     */
    List<FirstClassCourseDTO> listByTimeForward(Integer columnId, LocalDateTime date, int size);

    /**
     * 按时间后向查询课表, 历史时间
     *
     * @param columnId 栏目ID
     * @param date     时间
     * @param size     数量
     */
    List<FirstClassCourseDTO> listByTimeBackward(Integer columnId, LocalDateTime date, int size);
}
