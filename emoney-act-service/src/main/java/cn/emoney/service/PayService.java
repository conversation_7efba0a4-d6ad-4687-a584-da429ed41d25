package cn.emoney.service;

import cn.emoney.pojo.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-03-28
 */
public interface PayService {
    /**
     * 根据订单状态和创建时间查询XE订单
     *
     * @param startCreateTime
     * @param endCreateTime
     * @param orderStatus
     * @return java.util.List<cn.emoney.pojo.PayOrderDO>
     * <AUTHOR>
     * @date 2022/3/28 15:25
     */
    List<PayOrderDO> queryByCreateTimeAndOrderStatus(Date startCreateTime, Date endCreateTime, Integer orderStatus);

    /**
     * 获取定金支付状态
     *
     * @param mobilex
     * @return java.lang.String 0 未付定金  1 定金已付  2 尾款已付  -1 已退款
     * <AUTHOR>
     * @date 2023/6/15 17:01
     */
    List<JunePayInfo> getJunePayStatus(String mobilex);

    /**
     * （新支付）获取用户是否付过定金
     * <AUTHOR>
     * @date 2023/10/11 18:59
     * @param request
     * @return cn.emoney.pojo.AppApiResult<java.util.List<cn.emoney.pojo.UserLogisticsPackageDO>>
     */
    AppApiResult<List<UserLogisticsPackageDO>> getPayPackageList(PayRequestDO request);
}
