package cn.emoney.service;

import cn.emoney.pojo.SurveyTagDTO;
import cn.emoney.pojo.WjxSurveyResultDO;
import cn.emoney.pojo.vo.survey.ExtractTagVO;
import cn.emoney.pojo.vo.survey.WjxSurveyReceiptVO;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-01-28
 * @description 问卷星问卷答卷服务
 */
public interface SurveyService {

    /**
     * 功能描述:
     * 提交用户问卷
     * @Param: [userSurvey]
     * @Return: cn.emoney.pojo.vo.survey.WjxSurveyReceiptVO
     * @Author: tengdengming
     * @Date: 2022/1/29 10:18
     */
    WjxSurveyReceiptVO pushUserSurvey(JSONObject userSurvey);

    /**
     * 功能描述:
     * 处理问卷的用户答卷的持久化
     * @Param: [userSurvey]
     * @Return: cn.emoney.pojo.vo.survey.WjxSurveyDealReceiptVO
     * @Author: tengdengming
     * @Date: 2022/1/29 09:43
     */
    Integer dealUserSurvey(WjxSurveyResultDO userSurvey);

    /**
     * 功能描述:
     * 分页获取某用户的所有答卷列表
     * @Param: [uid, pageSize, pageIndex]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2022/1/29 09:43
     */
    List<WjxSurveyResultDO> getUserSurveyListByPage(Long uid,Integer pageSize,Integer pageIndex);

    /**
     * 功能描述:
     * 用流水号获取某答卷
     * @Param: [joinId]
     * @Return: cn.emoney.pojo.WjxSurveyResultDO
     * @Author: tengdengming
     * @Date: 2022/1/29 09:43
     */
    WjxSurveyResultDO getSurvey(String joinId);

    /**
     * 功能描述:
     * 根据问卷获取用户答卷
     * @Param: [activity]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2022/1/29 09:43
     */
    List<WjxSurveyResultDO> getActivitySurveyList(Long activity,Integer pageSize, Integer pageIndex);

    /**
     * 功能描述:
     * 根据用户的微信UUnionID分页获取问卷
     * @Param: [unionId, pageSize, pageIndex]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2023/11/27 13:57
     */
    List<WjxSurveyResultDO> getUserSurveyListByUnionId(String unionId,Integer pageSize, Integer pageIndex);

    /**
     * 功能描述:
     * 根据用户的EM号，分页获取问卷
     * @Param: [emNo, pageSize, pageIndex]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2023/11/27 13:58
     */
    List<WjxSurveyResultDO> getUserSurveyListByEmName(String emNo,Integer pageSize, Integer pageIndex);


    WjxSurveyResultDO getUserSurveyResultByActivityID(Long uid,Long activityID);

    /***
     * 功能描述:
     * 限制最近几条的最大条数
     * @Param:
     * @Return:
     * @Author: tengdengming
     * @Date: 2022/3/9 16:38
     */
    int MAX_LATEST_COUNT = 100;

    List<Long> getUserFinishedSurvey(Long uid, List<Long> activityList);

    /**
     * 功能描述: <br>
     * 获取用户最近的几条问卷 最大100条
     * @Param: [uid, lastCounts]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2022/1/29 09:50
     */
    List<WjxSurveyResultDO> getUserLatestSurvey(Long uid,Integer lastCounts);


    /***
     * 功能描述:
     * 检查信息摘要
     * @Param: [userSurvey]
     * @Return: java.lang.Boolean
     * @Author: tengdengming
     * @Date: 2022/3/14 11:17
     */
    Boolean checkDigest(WjxSurveyResultDO userSurvey);


    /***
     * 功能描述:
     * 消息推送来源场景
     * @Param: [userSurvey]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2022/3/14 11:17
     */
    String noticeScene(WjxSurveyResultDO userSurvey);


    /***
     * 功能描述:
     * 获取对应场景的推送URL
     * @Param: [scene]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2022/3/14 11:24
     */
    String getScenePushUrl(String scene);

    String getWJXActivitytemplate(Long activityId);

    SurveyTagDTO tagsExtract(String userId, String surveyId);
}
