package cn.emoney.service.impl;

import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.mapper.pay.PayOrderMapper;
import cn.emoney.pojo.*;
import cn.emoney.service.PayService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-03-28
 */
@Service
@Slf4j
public class PayServiceImpl implements PayService {

    @Autowired
    private PayOrderMapper payOrderMapper;

    @Value("${GetJunePayStatusCache:}")
    private String GetJunePayStatusUrl;

    @Value("${appApiPreUrl:}")
    private String appApiPreUrl;

    @Value("${appPayApiPreUrl:}")
    private String appPayApiPreUrl;

    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy.MM.dd");
    /**
     * 根据订单状态和创建时间查询XE订单
     *
     * @param startCreateTime
     * @param endCreateTime
     * @param orderStatus
     * @return java.util.List<cn.emoney.pojo.PayOrderDO>
     * <AUTHOR>
     * @date 2022/3/28 15:25
     */
    @Override
    public List<PayOrderDO> queryByCreateTimeAndOrderStatus(Date startCreateTime, Date endCreateTime, Integer orderStatus) {
        return payOrderMapper.queryByCreateTimeAndOrderStatus(startCreateTime, endCreateTime, orderStatus);
    }

    /**
     * 获取定金支付状态
     *
     * @param mobilex
     * @return java.lang.String 0 未付定金  1 定金已付  2 尾款已付  -1 已退款
     * <AUTHOR>
     * @date 2023/6/15 17:01
     */
    @Override
    public List<JunePayInfo> getJunePayStatus(String mobilex) {
        List<JunePayInfo> payInfoList = new ArrayList<>();
        try {
            String targetUrl = MessageFormat.format(GetJunePayStatusUrl, mobilex);
            String res = OkHttpUtil.get(targetUrl, null);

            log.info("send Privilege log! {},{}", mobilex, res);

            JunePayStatusDO junePayStatusDO = JsonUtil.toBean(res, JunePayStatusDO.class);
            if (junePayStatusDO != null) {
                if (junePayStatusDO.OrderId != null) {
                    JunePayInfo junePayInfo = new JunePayInfo();

                    junePayInfo.OrderType = 1;
                    junePayInfo.OrderTypeName = "定金";
                    junePayInfo.OrderID = junePayStatusDO.OrderId;
                    junePayInfo.OrderStatus = junePayStatusDO.OrderStatus;
                    junePayInfo.OrderPrice = junePayStatusDO.OrderPrice;
                    junePayInfo.GoodId = junePayStatusDO.GoodId;
                    junePayInfo.OrderTime = simpleDateFormat.format(junePayStatusDO.OrderTime) ;

                    payInfoList.add(junePayInfo);
                }
                if (junePayStatusDO.SubOrderId != null) {
                    JunePayInfo junePayInfo = new JunePayInfo();

                    junePayInfo.OrderType = 2;
                    junePayInfo.OrderTypeName = "尾款";
                    junePayInfo.OrderID = junePayStatusDO.SubOrderId;
                    junePayInfo.OrderStatus = junePayStatusDO.SubOrderStatus;
                    junePayInfo.OrderPrice = junePayStatusDO.SubOrderPrice;
                    junePayInfo.GoodId = junePayStatusDO.GoodId;
                    junePayInfo.OrderTime = simpleDateFormat.format(junePayStatusDO.SubOrderTime);

                    payInfoList.add(junePayInfo);
                }
                if (junePayStatusDO.FullOrderId != null) {
                    JunePayInfo junePayInfo = new JunePayInfo();

                    junePayInfo.OrderType = 3;
                    junePayInfo.OrderTypeName = "全款";
                    junePayInfo.OrderID = junePayStatusDO.FullOrderId;
                    junePayInfo.OrderStatus = junePayStatusDO.FullOrderStatus;
                    junePayInfo.OrderPrice = junePayStatusDO.FullOrderPrice;
                    junePayInfo.GoodId = junePayStatusDO.GoodId;
                    junePayInfo.OrderTime = simpleDateFormat.format(junePayStatusDO.FullOrderTime);

                    payInfoList.add(junePayInfo);
                }
            }

            return payInfoList;
        } catch (Exception exp) {
            log.warn("getJunePayStatus failed! {},{}", mobilex, exp.getMessage());
        }

        return null;
    }

    /**
     * （新支付）获取用户是否付过定金
     * <AUTHOR>
     * @date 2023/10/11 18:58
     * @param request
     * @return cn.emoney.pojo.AppApiResult<java.util.List<cn.emoney.pojo.UserLogisticsPackageDO>>
     */
    @Override
    public AppApiResult<List<UserLogisticsPackageDO>> getPayPackageList(PayRequestDO request){
        String url = appPayApiPreUrl + "buy/order/GetLogisticsByPids?Emapp-Format=Emoney";
        String ret = OkHttpUtil.postJsonParams(url,JSON.toJSONString(request));

        AppApiResult<List<UserLogisticsPackageDO>> response = JSON.parseObject(ret, new TypeReference<AppApiResult<List<UserLogisticsPackageDO>>>() {
        });

        return response;
    }
}
