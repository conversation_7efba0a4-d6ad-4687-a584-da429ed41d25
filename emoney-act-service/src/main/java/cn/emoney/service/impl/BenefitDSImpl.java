package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.common.utils.RedissonDistributionLock;
import cn.emoney.pojo.*;
import cn.emoney.pojo.bo.*;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.pojo.vo.OrderProdListVO;
import cn.emoney.pojo.vo.UserLoginIdInfoVO;
import cn.emoney.service.BenefitDsService;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.apache.bcel.generic.RET;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.print.DocFlavor;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-09-04
 */
@Service
@Slf4j
public class BenefitDSImpl implements BenefitDsService {
    @Value("${getSignInfoUrl}")
    private String getSignInfoUrl;
    @Value("${getSignBenefitUrl}")
    private String getSignBenefitUrl;
    @Value("${getSignRecordUrl}")
    private String getSignRecordUrl;
    @Value("${getMissedRewardsUrl}")
    private String getMissedRewardsUrl;
    @Value("${appApiPreUrl:}")
    private String appApiPreUrl;

    @Autowired
    private LogisticsService logisticsService;
    @Autowired
    private UserService userService;

    @Autowired
    private RedissonDistributionLock redissonDistributionLock;

    private final String CacheKey_SendBenefitLock = RedisConstants.RedisKey_Pre_Activity + "20230912:DSSignBenefit";

    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
    /**
     * 7天弹射起步使用期
     */
    private final static String PACCode_7day = "PAC1230829142853127";
    /**
     * 1年弹射起步使用期
     */
    private final static String PACCode_1Year = "PAC1230718113245708";
    private final static String endTime = "2023-09-30 23:59:59";
    private final static String ACTIVITYCodes = "ac-1210226160325297,ac-1210226160916171,ac-1210226160620999,ac-1200408184259330,ac-120040211013889,ac-1200401171115746";

    /**
     * 自动任务：每天凌晨6点执行
     * 获取购买大师用户 自动赠送未领取的奖励 + 弹射起步7天使用期
     *
     * <AUTHOR>
     * @date 2023/9/5 10:01
     */
    @Override
    public void autoSendSignBenefit() {
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取前一天的日期
        LocalDate oneDayBefore = today.minusDays(1);
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 格式化日期
        String beginTime = oneDayBefore.format(formatter);

        //获取物流订单
        OrderProdListDTO orderProdListDTO = new OrderProdListDTO();
        orderProdListDTO.setStockUpDate_Start(beginTime);
        orderProdListDTO.setStockUpDate_End(endTime);
        orderProdListDTO.setRefund_Sign(0);
        orderProdListDTO.setACTIVITY_CODE(ACTIVITYCodes);
        List<OrderProdListVO> list = logisticsService.queryOrderProdList(orderProdListDTO);

        for (OrderProdListVO item :
                list) {
            String mobileX = item.getMID();
            String emCard = item.getEmCard();

            //获取用户uid
            UserLoginIdInfoVO loginIdInfoVO = userService.GetLoginIDInfoByAccount(emCard);
            if (loginIdInfoVO != null) {
                Long uid = loginIdInfoVO.PID;

                //1、购买成功用户 一次性补齐发放
                AppApiResult<Boolean> mrResult = getMissedRewards(uid.toString());
                if (mrResult.getCode() == 0 && mrResult.getDetail()) {
                    log.info(MessageFormat.format("[大师续费签到领福利]补齐签到奖励成功 uid:{0} emcard:{1} mobilex:{2}", uid, emCard, mobileX));
                } else {
                    log.info(MessageFormat.format("[大师续费签到领福利]补齐签到奖励失败 uid:{0} emcard:{1} mobilex:{2}", uid, emCard, mobileX));
                }

//                AppApiResult<ActDsSignDetail> apiResult = getUserSignInfo(uid.toString(), "");
//                if (apiResult.getCode() == 0 && apiResult.getDetail() != null) {
//                    for (ActDsSignBenefitDO recordItem :
//                            apiResult.getDetail().dayList) {
//                        //未签到 未领取用户：发放奖励
//                        if (!recordItem.isCurrent && !recordItem.isReceived) {
//                            boolean isSend = getSignBenefit(uid.toString(), recordItem.id);
//                            if (!isSend) {
//                                log.error("[大师续费签到领福利]赠送签到奖励失败 uid:" + uid + " request:" + JSON.toJSONString(recordItem));
//                            } else {
//                                log.info("[大师续费签到领福利]赠送签到奖励成功 uid:" + uid + " request:" + JSON.toJSONString(recordItem));
//                            }
//                        }
//                    }
//
//                    //2、累计签到满5次未领取奖励用户，额外赠送7天使用期
//                    if (apiResult.getDetail().appendPrize != null) {
//                        if (apiResult.getDetail().appendPrize.isCurrent && !apiResult.getDetail().appendPrize.isReceived) {
//                            Result<String> ret = sendPrivilegeCommonNew("大师续费签到领好礼", PACCode_7day, 2, mobileX, emCard);
//                            if (!ret.isSuccess()) {
//                                log.error("[大师续费签到领福利]赠送签到满5赠7奖励失败 uid:" + uid + " ret:" + JSON.toJSONString(ret));
//                            } else {
//                                log.info("[大师续费签到领福利]赠送签到满5赠7奖励成功 uid:" + uid + " ret:" + JSON.toJSONString(ret));
//                            }
//                        }
//                    }
//                } else {
//                    log.error("[大师续费签到领福利]获取用户签到信息失败 uid:" + uid + " ret:" + JSON.toJSONString(apiResult));
//                }
            }
        }
    }

    /**
     * 领取签到奖励
     *
     * @return cn.emoney.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/9/4 17:59
     */
    @Override
    public Result<String> sendSignBenefit(int day, String pid, String emCard, String mobileX) {
        String lockKey = CacheKey_SendBenefitLock;
        Result<String> ret = new Result<>();

        try {
            if (redissonDistributionLock.tryLock(lockKey, TimeUnit.SECONDS, 30)) {
                Lottery0808PrizeDO prizeDO = initSignBenefitDSList().stream().filter(x -> x.id == day).findFirst().get();
                if (prizeDO == null) {
                    return Result.buildErrorResult("-1", "未查到对应奖励信息");
                }

                switch (prizeDO.id) {
                    case 1:
                        //100块优惠券
                        SendCouponRequestDTO req = new SendCouponRequestDTO();
                        req.PRESENT_ACCOUNT_TYPE = 2;
                        req.PRESENT_ACCOUNT = mobileX;
                        req.COUPON_ACTIVITY_ID = prizeDO.taskCode;
                        req.COUPON_RULE_PRICE = (double) prizeDO.pCount;
                        req.PRESENT_PERSON = "大师续费签到领好礼";
                        LogisticsResult<String> retCoupon = logisticsService.sendCoupon(req);
                        if (retCoupon.getCode() == 0) {
                            ret.setCode("200");
                            ret.setSuccess(true);
                            ret.setMsg("优惠券赠送成功");
                        } else {
                            ret.setSuccess(false);
                            ret.setMsg(retCoupon.getMsg());
                        }
                        break;
                    case 2:
                    case 4:
                    case 8:
                        String upid = pid;
                        if (pid == "*********") {
                            upid = "*********";
                        }
                        if (pid == "888020400") {
                            upid = "*********";
                        }
                        //1天使用期
                        String pacCode = "";
                        String[] pacCodeList = prizeDO.taskCode.split("@");
                        for (int i = 0; i < pacCodeList.length; i++) {
                            String[] pid_pacCode = pacCodeList[i].split(":");
                            if (upid.equals(pid_pacCode[0])) {
                                pacCode = pid_pacCode[1];
                            }
                        }
                        ret = sendPrivilegeCommonNew("大师续费签到领好礼-使用期", pacCode, 1, mobileX, emCard);
                        break;
                    case 3:
                    case 5:
                    case 6:
                        //特权类
                        ret = sendPrivilegeCommonNew("大师续费签到领好礼-特权", prizeDO.taskCode, 2, mobileX, emCard);
                        break;
                    case 7:
                        //5大高端功能插件
                        //教学精品课
                        ret.setCode("200");
                        ret.setSuccess(true);
                        ret.setMsg("领取成功 ");
                        ret.setData("https://www.emoney.cn/course/dashi/5dgdcjgn.pdf");
                        break;
                    case 0:
                        //满签5天 赠送7天弹射起步
                        ret = sendPrivilegeCommonNew("大师续费签到领好礼-特权", prizeDO.taskCode, 2, mobileX, emCard);
                        break;
                    default:
                        break;
                }
            } else {
                return Result.buildErrorResult("-1", "领取失败，请联系业务员");
            }
        } finally {
            redissonDistributionLock.unlock(lockKey);
        }
        return ret;
    }


    /**
     * 获取签到奖励列表
     *
     * @return java.util.List<cn.emoney.pojo.Lottery0808PrizeDO>
     * <AUTHOR>
     * @date 2023/9/4 17:48
     */
    public List<Lottery0808PrizeDO> initSignBenefitDSList() {
        List<Lottery0808PrizeDO> list = new ArrayList<>();
        list.add(new Lottery0808PrizeDO(1, 3, "100元抵扣券", "cp-1230824105946744", "100元抵扣券", 100));
        list.add(new Lottery0808PrizeDO(2, 1, "1天使用期", "*********:PAC1220721114237340@*********:PAC1220721114031386", "1天使用期", 1));
        list.add(new Lottery0808PrizeDO(3, 2, "5大赛道指数15天使用权限", "PAC1220816173222112", "5大赛道指数15天使用权限", 15));
        list.add(new Lottery0808PrizeDO(4, 1, "1天使用期", "*********:PAC1220721114237340@*********:PAC1220721114031386", "1天使用期", 1));
        list.add(new Lottery0808PrizeDO(5, 5, "投研内参功能30天使用权限", "PAC1230724093829949", "投研内参功能30天使用权限", 30));
        list.add(new Lottery0808PrizeDO(6, 2, "5大赛道指数15天使用权限", "PAC1220816173222112", "5大赛道指数15天使用权限", 15));
        list.add(new Lottery0808PrizeDO(7, 0, "5大高端功能插件教学精品课", "", "5大高端功能插件教学精品课", 0));
        list.add(new Lottery0808PrizeDO(8, 1, "1天使用期", "*********:PAC1220721114237340@*********:PAC1220721114031386", "1天使用期", 1));
        list.add(new Lottery0808PrizeDO(0, 1, "满足5天签到赠送7天弹射起步", PACCode_7day, "7天弹射起步", 7));
        return list;
    }

    /**
     * 领特权/送使用期  通用接口
     *
     * @param reason
     * @param activityID
     * @param accountType
     * @return cn.emoney.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/9/5 11:21
     */
    public Result<String> sendPrivilegeCommonNew(String reason, String activityID, Integer accountType, String mobileX, String emCard) {
        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(activityID);
        sendPrivilegeDTO.setReason(reason);
        sendPrivilegeDTO.setApplyUserID("scb_public");
        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();

        //2:手机号 1：em号  领取特权指定2  延期指定1
        createActivityGrantApplyAccountDTO.setAccountType(accountType);
        createActivityGrantApplyAccountDTO.setAccount(emCard);
        createActivityGrantApplyAccountDTO.setMID(mobileX);
        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        Result<String> ret = logisticsService.sendPrivilegeResult(sendPrivilegeDTO);
        return ret;
    }


    /**
     * 获取用户签到信息
     *
     * @param uid
     * @return java.util.List<cn.emoney.pojo.ActDsSignBenefitDO>
     * <AUTHOR>
     * @date 2023/9/5 17:04
     */
    @Override
    public AppApiResult<ActDsSignDetail> getUserSignInfo(String uid, String day) {
        String url = MessageFormat.format(getSignInfoUrl + "?Emapp-Format=Emoney&gid={0}&day={1}", uid, day);
        String ret = OkHttpUtil.get(url, null);

        AppApiResult<ActDsSignDetail> response = JSON.parseObject(ret, new TypeReference<AppApiResult<ActDsSignDetail>>() {
        });
        return response;
    }

    /**
     * 签到领取奖励
     *
     * @param uid
     * @return boolean
     * <AUTHOR>
     * @date 2023/9/5 17:11
     */
    @Override
    public boolean getSignBenefit(String uid, String prizeId, String day) {
        String url = MessageFormat.format(getSignBenefitUrl + "?Emapp-Format=Emoney&gid={0}&prizeId={1}&day={2}", uid, prizeId, day);
        String ret = OkHttpUtil.get(url, null);
        AppApiResult<Boolean> result = JSON.parseObject(ret, new TypeReference<AppApiResult<Boolean>>() {
        });
        if (result.getCode() == 0) {
            return result.getDetail();
        } else {
            return false;
        }
    }

    /**
     * 获取领取记录
     *
     * @param uid
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2023/9/5 17:41
     */
    @Override
    public List<ActDsSignRecordDO> getSignRecords(String uid) {
        List<ActDsSignRecordDO> list = new ArrayList<>();
        String url = MessageFormat.format(getSignRecordUrl + "?Emapp-Format=Emoney&gid={0}", uid);
        String ret = OkHttpUtil.get(url, null);
        AppApiResult<List<ActDsSignRecordDO>> result = JSON.parseObject(ret, new TypeReference<AppApiResult<List<ActDsSignRecordDO>>>() {
        });
        if (result.getCode() == 0) {
            for (ActDsSignRecordDO item :
                    result.getDetail()) {
                Date date = new Date(item.createTime);
                String showTime = simpleDateFormat.format(date);

                item.showTime = showTime;

                list.add(item);
            }
        }
        return list;
    }

    /**
     * 补齐奖励
     *
     * @param uid
     * @return cn.emoney.pojo.AppApiResult<java.lang.Boolean>
     * <AUTHOR>
     * @date 2023/9/6 14:39
     */
    public AppApiResult<Boolean> getMissedRewards(String uid) {
        String url = MessageFormat.format(getMissedRewardsUrl + "?Emapp-Format=Emoney&gid={0}", uid);
        String ret = OkHttpUtil.get(url, null);

        AppApiResult<Boolean> result = JSON.parseObject(ret, new TypeReference<AppApiResult<Boolean>>() {
        });
        return result;
    }


    /**
     * 大师众筹鸿运值活动 -获取活动信息
     *
     * @param uid
     * @param day
     * @return cn.emoney.pojo.AppApiResult<java.util.List < cn.emoney.pojo.ActDsDailyInfoDO>>
     * <AUTHOR>
     * @date 2023/9/27 16:54
     */
    @Override
    public AppApiResult<ActDsDailyChanceDO> getDailyInfo(String uid, String pid, String day) {
        String url = MessageFormat.format(appApiPreUrl + "DailyChance/Info?Emapp-Format=Emoney&gid={0}&day={1}&pid={2}", uid, day, pid);
        String ret = OkHttpUtil.get(url, null);

        AppApiResult<ActDsDailyChanceDO> result = JSON.parseObject(ret, new TypeReference<AppApiResult<ActDsDailyChanceDO>>() {
        });
        return result;
    }

    /**
     * 大师众筹鸿运值活动 - 领取鸿运值签到奖励
     *
     * @param uid
     * @param prizeId
     * @param day
     * @return cn.emoney.pojo.AppApiResult<java.lang.Boolean>
     * <AUTHOR>
     * @date 2023/9/27 16:57
     */
    @Override
    public AppApiResult<Boolean> getDailyReward(String uid, String pid, String emAccount, String phone, String prizeId, String day) {
        String url = MessageFormat.format(appApiPreUrl + "DailyChance/reward?Emapp-Format=Emoney&gid={0}&prizeId={1}&day={2}&pid={3}&emAccount={4}&phone={5}", uid, prizeId, day, pid, emAccount, phone);
        String ret = OkHttpUtil.get(url, null);

        AppApiResult<Boolean> result = JSON.parseObject(ret, new TypeReference<AppApiResult<Boolean>>() {
        });
        return result;
    }

    /**
     * 大师众筹鸿运值活动 - 查询奖励记录
     *
     * @param uid
     * @return java.util.List<cn.emoney.pojo.ActDsSignRecordDO>
     * <AUTHOR>
     * @date 2023/9/27 17:44
     */
    @Override
    public List<ActDsSignRecordDO> getDailyRecords(String uid) {
        List<ActDsSignRecordDO> list = new ArrayList<>();
        String url = MessageFormat.format(appApiPreUrl + "DailyChance/records?Emapp-Format=Emoney&gid={0}", uid);
        String ret = OkHttpUtil.get(url, null);
        AppApiResult<List<ActDsSignRecordDO>> result = JSON.parseObject(ret, new TypeReference<AppApiResult<List<ActDsSignRecordDO>>>() {
        });
        if (result.getCode() == 0) {
            for (ActDsSignRecordDO item :
                    result.getDetail()) {
                Date date = new Date(item.createTime);
                String showTime = simpleDateFormat.format(date);

                item.showTime = showTime;

                list.add(item);
            }
        }
        return list;
    }

    /**
     * 大师众筹鸿运值活动 - 增加满足领取条件用户
     *
     * @param uid
     * @return cn.emoney.pojo.AppApiResult<java.lang.Boolean>
     * <AUTHOR>
     * @date 2023/9/27 17:52
     */
    @Override
    public AppApiResult<Boolean> addDailyUser(String uid) {
        String url = MessageFormat.format(appApiPreUrl + "DailyChance/AddUser?Emapp-Format=Emoney&gid={0}", uid);
        String ret = OkHttpUtil.get(url, null);

        AppApiResult<Boolean> result = JSON.parseObject(ret, new TypeReference<AppApiResult<Boolean>>() {
        });
        return result;
    }

    /**
     * app抢攻能-调研掘金
     * <AUTHOR>
     * @date 2023/12/28 17:45
     * @param mobileX
     * @return null
     */
    @Override
    public AppApiResult<Integer> addUserTag(String mobileX){
        String url = MessageFormat.format(appApiPreUrl + "UserTag/Exclude?Emapp-Format=Emoney&excludeType=718&phoneGuid={0}", mobileX);
        String ret = OkHttpUtil.get(url, null);
        AppApiResult<Integer> result = JSON.parseObject(ret, new TypeReference<AppApiResult<Integer>>() {
        });
        return result;
    }
}
