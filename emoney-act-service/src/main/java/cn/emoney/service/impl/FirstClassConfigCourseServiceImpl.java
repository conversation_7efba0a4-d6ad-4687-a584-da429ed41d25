package cn.emoney.service.impl;

import cn.emoney.common.utils.JsonUtil;
import cn.emoney.pojo.bo.FirstClassDTO;
import cn.emoney.pojo.vo.result.ProductConfig;
import cn.emoney.service.FirstClassConfigCourseService;
import cn.emoney.service.ProductConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

@Slf4j
@Component
@EnableScheduling
public class FirstClassConfigCourseServiceImpl implements FirstClassConfigCourseService {
    private String md5 = null;
    private Instant lastUpdateTime = Instant.MIN;
    private Map<Integer, FirstClassDTO> classMap = Collections.emptyMap();
    private Map<Integer, List<FirstClassDTO>> seqMap = Collections.emptyMap();
    private Map<String, List<FirstClassDTO>> videoMap = Collections.emptyMap();
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    private final ProductConfigService configService;

    @Value("${firstClassListConfigKey:}")
    private String firstClassListConfigKey;

    public FirstClassConfigCourseServiceImpl(ProductConfigService configService) {
        this.configService = configService;
    }

//    @Scheduled(initialDelay = 1000 * 60, fixedDelay = 1000 * 60 * 30)
    public void loadData() {
        ProductConfig config = configService.getConfig(firstClassListConfigKey);
        if (config == null) {
            log.error("can't load class data");
            return;
        }
        String md5 = DigestUtils.md5DigestAsHex(config.getConfigContent().getBytes());
        Lock writeLock = lock.writeLock();
        writeLock.lock();
        try {
            if (Objects.equals(this.md5, md5)) {
                log.debug("class data is not changed");
                return;
            }
            // diff list for old gc
            List<FirstClassDTO> classList = JsonUtil.toBeanList(config.getConfigContent(), FirstClassDTO.class).stream()
                    .map(item -> {
                        FirstClassDTO exist = classMap.get(item.getId());
                        if (Objects.equals(exist, item)) {
                            return exist;
                        }
                        return item;
                    }).collect(Collectors.toList());

            this.classMap = classList.stream().collect(Collectors.toMap(FirstClassDTO::getId, c -> c));
            this.seqMap = classList.stream()
                    .filter(c -> c.getActivityID() != null && !c.getActivityID().isEmpty())
                    .collect(Collectors.groupingBy(s -> Integer.parseInt(s.getActivityID())));
            this.videoMap = classList.stream().collect(Collectors.groupingBy(dto -> {
                int pos = dto.getClassUrl().lastIndexOf("-");
                return dto.getClassUrl().substring(pos + 1);
            }));
            this.md5 = md5;
            this.lastUpdateTime = Instant.now();
        } finally {
            writeLock.unlock();
        }
    }

    @Override
    public List<FirstClassDTO> listByActivitySeq(Integer activitySeq) {
        checkLoad();
        return seqMap.getOrDefault(activitySeq, Collections.emptyList());
    }

    @Override
    public Optional<FirstClassDTO> findByClassId(Integer classId) {
        checkLoad();
        return Optional.ofNullable(classMap.get(classId));
    }

    @Override
    public Optional<FirstClassDTO> findByVideoId(String webCastId) {
        checkLoad();
        return Optional.ofNullable(videoMap.get(webCastId)).map(l -> l.get(0));
    }

    private void checkLoad() {
        if (this.lastUpdateTime != Instant.MIN) {
            return;
        }
        Lock writeLock = lock.writeLock();
        writeLock.lock();
        try {
            if (this.lastUpdateTime == Instant.MIN) {
                loadData();
            }
        } finally {
            writeLock.unlock();
        }
    }
}
