package cn.emoney.service.impl;

import cn.emoney.act.client.firstclass.FirstClassMetaClient;
import cn.emoney.act.client.pointprod.dto.PointTaskDetail;
import cn.emoney.act.client.video.dto.VideoProgressDTO;
import cn.emoney.act.dao.FirstClassViewRecordRepository;
import cn.emoney.act.entity.FirstClassViewRecordEntity;
import cn.emoney.act.exception.PointException;
import cn.emoney.act.manager.PointTaskManager;
import cn.emoney.act.quest.logic.QuestReward;
import cn.emoney.act.quest.logic.QuestTarget;
import cn.emoney.act.quest.logic.condition.CountCondition;
import cn.emoney.act.quest.logic.reward.PointReward;
import cn.emoney.act.quest.logic.reward.PrivilegeReward;
import cn.emoney.act.quest.logic.target.PidTarget;
import cn.emoney.act.quest.model.RewardResult;
import cn.emoney.act.quest.utils.QuestUtils;
import cn.emoney.act.reward.PointContent;
import cn.emoney.common.exception.BaseRuntimeException;
import cn.emoney.common.utils.GenseeUtils;
import cn.emoney.mapper.activity.ActFirstclassSpeFunUserMapper;
import cn.emoney.pojo.WjxSurveyResultDO;
import cn.emoney.pojo.bo.*;
import cn.emoney.pojo.vo.AccountVO;
import cn.emoney.pojo.vo.FirstClassCourseVO;
import cn.emoney.service.*;
import com.google.common.collect.Streams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Sort;
import org.springframework.data.util.Pair;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-03-18
 */
@Slf4j
@Service
public class FirstClassRollServiceImpl implements FirstClassRollService {
    private final ActivityQuestService questService;
    private final FirstClassCourseService classCourseService;
    private final FirstClassMetaClient classMetaClient;
    private final CourseService courseService;
    private final FirstClassViewRecordRepository viewRepository;
    private final VideoProgressService videoProgressService;
    private final UserService userService;
    private final LogisticsService logisticsService;
    private final PointService pointService;
    private final PointTaskManager pointTaskManager;
    private final ThreadPoolTaskExecutor executor;
    private final ActFirstclassSpeFunUserMapper speFunUserMapper;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final Cache courseRecordCache;

    public FirstClassRollServiceImpl(ActivityQuestService questService,
                                     FirstClassCourseService classCourseService,
                                     FirstClassMetaClient classMetaClient,
                                     CourseService courseService,
                                     FirstClassViewRecordRepository viewRepository,
                                     VideoProgressService videoProgressService,
                                     UserService userService,
                                     LogisticsService logisticsService,
                                     PointService pointService,
                                     PointTaskManager pointTaskManager,
                                     ThreadPoolTaskExecutor executor,
                                     ActFirstclassSpeFunUserMapper speFunUserMapper,
                                     KafkaTemplate<String, Object> kafkaTemplate,
                                     CacheManager cacheManager) {
        this.questService = questService;
        this.classCourseService = classCourseService;
        this.classMetaClient = classMetaClient;
        this.courseService = courseService;
        this.viewRepository = viewRepository;
        this.videoProgressService = videoProgressService;
        this.userService = userService;
        this.logisticsService = logisticsService;
        this.pointService = pointService;
        this.pointTaskManager = pointTaskManager;
        this.executor = executor;
        this.speFunUserMapper = speFunUserMapper;
        this.kafkaTemplate = kafkaTemplate;
        this.courseRecordCache = cacheManager.getCache("act:first-class:course-record");
    }

    @Override
    @Cacheable(cacheNames = "act:first-class:content", key = "#questId")
    public Optional<FirstClassContentDTO> findClassContent(Long questId) {
        return findClassQuest(questId).map(quest -> {
            FirstClassContentDTO content = new FirstClassContentDTO();
            content.setId(quest.getId());
            content.setName(quest.getName());
            content.setStatus(quest.getStatus());
            content.setStartTime(quest.getStartTime());
            content.setEndTime(quest.getEndTime());
            content.setValid(quest.isValid());
            if (!content.isValid()) {
                return content;
            }
            findClassColumns(quest.getId()).ifPresent(content::setColumns);
            if (questId == 10014L) {
                classMetaClient.schedule(20001L)
                        .ifPresent(schedule -> content.setMeta(Collections.singletonMap("schedule", schedule)));
            }
            if (questId == 10020L || questId == 10021L) {
                classMetaClient.schedule(20003L)
                        .ifPresent(schedule -> content.setMeta(Collections.singletonMap("schedule", schedule)));
            }
            if (questId == 10023L) {
                classMetaClient.schedule(20004L)
                        .ifPresent(schedule -> content.setMeta(Collections.singletonMap("schedule", schedule)));
            }
            if (questId == 10024L) {
                classMetaClient.schedule(20005L)
                        .ifPresent(schedule -> content.setMeta(Collections.singletonMap("schedule", schedule)));
            }
            return content;
        });
    }

    /**
     * 接受任务、获取 课表 与 进度
     */
    @Override
    public List<FirstClassCourseVO> userCourseList(Long questId, Long uid) {
        if (!findClassQuest(questId).filter(ActivityQuestDTO::isValid).isPresent()) {
            return Collections.emptyList();
        }
        ActivityQuestAcceptDTO accept = findOrAccept(questId, uid);
        List<FirstClassCourseDTO> courseList = getCourseList(accept);
        Set<Integer> courseRecord = getCourseRecord(accept);
        return courseList.stream()
                .map(dto -> {
                    FirstClassCourseVO vo = new FirstClassCourseVO();
                    vo.setId(dto.getId());
                    vo.setCourseId(dto.getCourseId());
                    vo.setTitle(dto.getTitle());
                    vo.setSummary(dto.getSummary());
                    vo.setTeacherName(dto.getTeacherName());
                    vo.setVideoCoverApp(dto.getCoverApp());
                    vo.setComment(dto.getComment());
                    vo.setParentId(dto.getParentId());
                    vo.setStartTime(dto.getStartTime());
                    vo.setEndTime(dto.getEndTime());
                    vo.setExtraUrls(dto.getExtraUrls());
                    vo.setBookBaseAmount(dto.getBookBaseAmount());
                    vo.setReplayBaseAmount(dto.getReplayBaseAmount());

                    Optional.ofNullable(dto.getCourseId())
                            .filter(course -> course > 0)
                            .ifPresent(courseId -> {
                                try {
                                    CourseDetailDTO courseDetail = courseService.detail(courseId);
                                    if (courseDetail != null) {
                                        vo.setViewCount(courseDetail.getViewCount());
                                        if (!StringUtils.hasText(vo.getVideoCover())) {
                                            vo.setVideoCover(courseDetail.getCourseImageUrl());
                                        }
                                        if (StringUtils.hasText(courseDetail.getCoursePlayBackUrl())) {
                                            vo.setVideoSource(2);
                                            String url = courseDetail.getCoursePlayBackUrl();
                                            GenseeUtils.findWebCastId(url).ifPresent(vo::setVideoId);
                                            vo.setVideoUrl(url);
                                        } else if (StringUtils.hasText(courseDetail.getCourseLiveUrl())) {
                                            vo.setVideoSource(1);
                                            String url = courseDetail.getCourseLiveUrl();
                                            GenseeUtils.findWebCastId(url).ifPresent(vo::setVideoId);
                                            vo.setVideoUrl(url);
                                        }
                                    } else {
                                        log.warn("课程详情为空, courseId: {}", courseId);
                                    }
                                } catch (Exception e) {
                                    log.error("课程详情请求失败, courseId: {}", courseId, e);
                                }
                            });
                    vo.setPlayDone(courseRecord.contains(dto.getId()));
                    return vo;
                })
                .collect(Collectors.toList());
    }

    private ActivityQuestAcceptDTO findOrAccept(Long questId, Long uid) {
        return questService.findOrAccept(questId, uid,
                (quest) -> {
                    if (!quest.isValid()) {
                        throw new BaseRuntimeException("任务不可用");
                    }
                    List<QuestTarget> questTargets = findClassQuestDefinition(questId)
                            .map(FirstClassQuestDefinitionDTO::getTarget)
                            .orElse(null);
                    if (questTargets != null) {
                        LocalDateTime now = LocalDateTime.now();
                        Set<Long> pidList = userService.listAccountByUid(String.valueOf(uid))
                                .stream()
                                .filter(a -> a.getEndDate() != null && a.getEndDate().isAfter(now))
                                .map(AccountVO::getPid)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toSet());
                        boolean whiteList = questTargets.stream().allMatch(target -> {
                            if (target instanceof PidTarget) {
                                return ((PidTarget) target).stream().anyMatch(pidList::contains);
                            }
                            return false;
                        });
                        if (!whiteList) {
                            log.warn("用户不在白名单, questId: {}, uid: {}, userPid: {}, target: {}",
                                    questId, uid, pidList, questTargets);
                            throw new BaseRuntimeException("任务不可用");
                        }
                    }
                },
                (curr, prev) -> {
                    if (prev != null) {
                        // 草，这个用户取消过任务，要重新判断是否完成
                        try {
                            completeQuest(questId, curr.getId(), uid);
                        } catch (Exception e) {
                            log.error("用户观看记录还原失败, questId: {}, uid: {}", questId, uid, e);
                        }
                    }
                });
    }

    /**
     * 通过任务信息计算课程列表
     */
    private List<FirstClassCourseDTO> getCourseList(ActivityQuestAcceptDTO accept) {
        List<FirstClassContentDTO.ClassColumn> columns = findClassColumns(accept.getQuestId())
                .filter(item -> !item.isEmpty())
                .orElse(null);
        if (columns == null) {
            return Collections.emptyList();
        }
        LocalDateTime date = accept.getCreateTime().toLocalDate().atStartOfDay();
        return columns.stream().flatMap(column -> {
            List<FirstClassCourseDTO> courses;
            if (column.isDynamic()) {
                courses = classCourseService.listCourse(column.getId(), column.getSize(), date, column.getSafeSize());
                // timebase data only timebase sort
                Comparator<FirstClassCourseDTO> comparator = SORT_MAP.get(column.getSort());
                if (comparator != null) {
                    courses.sort(comparator);
                }
            } else {
                Sort sort = Sort.by(Sort.Order.desc("order"));
                if (column.getSort() != 0) {
                    Sort.Direction dir = column.getSort() > 0 ? Sort.Direction.ASC : Sort.Direction.DESC;
                    String property = Math.abs(column.getSort()) == 1 ? "id" : "beginTime";
                    sort = sort.and(Sort.by(dir, property));
                }
                courses = classCourseService.listCourse(column.getId(), sort, column.getSize());
            }
            return courses.stream();
        }).collect(Collectors.toList());
    }

    @Override
    public Optional<FirstClassProgressDTO> userClassProgress(Long questId, Long uid) {
        return questService.findActive(questId, uid).map(this::getClassProgress);
    }

    private FirstClassProgressDTO getClassProgress(ActivityQuestAcceptDTO quest) {
        return getClassProgress(quest, () -> this.getCourseList(quest));
    }

    private FirstClassProgressDTO getClassProgress(ActivityQuestAcceptDTO quest, Supplier<List<FirstClassCourseDTO>> courseListSupplier) {
        List<FirstClassCourseDTO> courseList = courseListSupplier.get();
        Set<Integer> courseRecord = getCourseRecord(quest);
        Date now = new Date();
        LinkedHashMap<Integer, FirstClassCourseProgressDTO> pendingList = new LinkedHashMap<>();
        List<FirstClassCourseProgressDTO> collect = courseList.stream()
                .map(item -> {
                    FirstClassCourseProgressDTO processVO = new FirstClassCourseProgressDTO();
                    processVO.setId(item.getId());
                    processVO.setPlayDone(courseRecord.contains(item.getId()));
                    if (item.getStartTime().before(now) && !processVO.getPlayDone()) {
                        pendingList.put(item.getCourseId(), processVO);
                    }
                    return processVO;
                })
                .collect(Collectors.toList());

        List<VideoProgressDTO> coursePlayDetail = videoProgressService.getCoursePlayDetail(quest.getUid(), new ArrayList<>(pendingList.keySet()));

        Streams.forEachPair(pendingList.values().stream(), coursePlayDetail.stream(), (value, detail) -> {
            if (detail != null) {
                value.setDuration(detail.getDuration());
                value.setPlayTime(detail.getGlobalTotalPlayTime());
            }
        });

        FirstClassProgressDTO progress = new FirstClassProgressDTO();
        progress.setStatus(quest.getStatus());
        progress.setCourseTask(collect);
        return progress;
    }

    @Override
    public Optional<FirstClassCourseProgressDTO> userCourseProgress(Long questId, Long uid, Integer subId) {
        return classCourseService.findById(subId).map(item -> {
            Date now = new Date();
            Set<Integer> records = getCourseRecord(questId, uid);

            FirstClassCourseProgressDTO processVO = new FirstClassCourseProgressDTO();
            processVO.setId(subId);
            processVO.setPlayDone(records.contains(item.getId()));
            if (item.getStartTime().before(now) && !processVO.getPlayDone()) {
                try {
                    videoProgressService.getCoursePlayDetail(uid, item.getCourseId())
                            .ifPresent(detail -> {
                                processVO.setDuration(detail.getDuration());
                                processVO.setPlayTime(detail.getGlobalTotalPlayTime());
                                boolean reached = isCourseReached(detail.getGlobalTotalPlayTime(), 30 * 60_000L, detail.getDuration());
                                if (reached) {
                                    // TODO 课程进度达标
                                    log.info("噫 这个用户达标了但是还没有完成噢, questId: {}, subId: {}, uid: {}", questId, subId, uid);
                                }
                            });
                } catch (Exception e) {
                    log.error("课程进度获取失败, courseId: {}", item.getCourseId(), e);
                }
            }
            return processVO;
        });
    }

    @Override
    public List<FirstClassCourseRewardDTO> userCourseReward(Long questId, Long uid, Integer subId) {
        List<FirstClassCourseDTO.CourseReward> rewards = classCourseService.findById(subId)
                .map(FirstClassCourseDTO::getRewards)
                .orElse(Collections.emptyList());
        if (rewards.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Class<? extends QuestReward>, List<QuestReward>> typeRewards = rewards.stream()
                .map(FirstClassCourseDTO.CourseReward::getReward)
                .collect(Collectors.groupingBy(QuestReward::getClass));
        // 获取积分任务
        List<Long> collect = typeRewards.get(PointReward.class).stream()
                .map(s -> ((PointReward) s).getTaskId())
                .collect(Collectors.toList());
        // 获取任务详情
        Map<Long, PointTaskDetail> taskDetails = pointTaskManager.taskDetailList(collect);
        // 获取任务完成情况
        Map<Long, List<PointDetailDTO>> pointStates = pointService.queryRecordByTaskId(uid, collect);

        return rewards.stream().map(item -> {
            FirstClassCourseRewardDTO dto = new FirstClassCourseRewardDTO();
            dto.setId(item.getId());
            dto.setName(item.getName());
            dto.setType(item.getReward().type());

            if (item.getReward() instanceof PointReward) {
                Long taskId = ((PointReward) item.getReward()).getTaskId();
                List<PointDetailDTO> details = pointStates.get(taskId);
                if (details != null) {
                    Float point = details.stream()
                            .filter(PointDetailDTO::getIsValid)
                            .map(PointDetailDTO::getTaskPoint).reduce(Float::sum).orElse(0F);
                    dto.setStatus(1);
                    dto.setContent(new PointContent().setPoint(point));
                } else {
                    Float point = Optional.ofNullable(taskDetails.get(taskId))
                            .map(PointTaskDetail::getTaskPoints).orElse(0F);
                    dto.setStatus(0);
                    dto.setContent(new PointContent().setPoint(point));
                }
            }
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public Set<Integer> userCourseState(Long questId, Long uid) {
        return questService.findActive(questId, uid)
                .map(this::getCourseTaskState)
                .orElse(Collections.emptySet());
    }

    private Set<Integer> getCourseTaskState(ActivityQuestAcceptDTO accept) {
        Set<Integer> targets = getCourseList(accept).stream().map(FirstClassCourseDTO::getId).collect(Collectors.toSet());
        return getCourseRecord(accept).stream().filter(targets::contains).collect(Collectors.toSet());
    }

    private Set<Integer> getCourseRecord(Long questId, Long uid) {
        return questService.findActive(questId, uid)
                .map(this::getCourseRecord)
                .orElse(Collections.emptySet());
    }

    private Set<Integer> getCourseRecord(ActivityQuestAcceptDTO accept) {
        return courseRecordCache.get(accept.getQuestId() + "@" + accept.getId() + "#" + accept.getUid(),
                () -> viewRepository.findAllSubIdByUidAndQuestId(accept.getUid(), accept.getQuestId())
        );
    }

    private void clearCourseRecord(ActivityQuestAcceptDTO accept) {
        courseRecordCache.evict(accept.getQuestId() + "@" + accept.getId() + "#" + accept.getUid());
    }

    @Override
    public boolean checkClassCourse(Long questId, Long uid, Integer subId, String platform) {
        FirstClassCourseDTO classCourse = classCourseService.findById(subId)
                .orElseThrow(() -> new BaseRuntimeException("-1204", "找不到任务课"));
        Date now = new Date();
        if (now.before(classCourse.getStartTime())) {
            return false;
        }
        boolean result = videoProgressService.getCoursePlayDetail(uid, classCourse.getCourseId())
                .map(detail -> isCourseReached(detail.getGlobalTotalPlayTime(), 30 * 60_000L, detail.getDuration()))
                .orElse(false);
        if (result) {
            executor.execute(() -> completeCourse(questId, uid, subId, platform));
        }
        return result;
    }

    /**
     * 判断是否达标
     */
    private boolean isCourseReached(Long current, Long target, Long total) {
        return current + 65_000 >= (total <= 0 ? target : Math.min(total, target));
    }

    /**
     * 记录大师第一课观看记录
     *
     * @param questId
     * @param uid
     * @param subId
     * @param platform // 1:pc 2:app 3:wechat
     * @return boolean
     * <AUTHOR>
     * @date 2022/3/21 15:53
     */
    @Override
    public int completeCourse(Long questId, Long uid, Integer subId, String platform) {
        if (questId == 10021L || questId == 10023L || questId == 10024L) {
            // 关闭统计上报, 经由展视 T+1 完成
            return 0;
        }
        ActivityQuestAcceptDTO accept = questService.findActive(questId, uid)
                .orElseThrow(() -> new BaseRuntimeException("-1104", "找不到任务"));
        if (getCourseRecord(accept).contains(subId)) {
            return 0;
        }
        return completeCourse(accept, subId, platform);
    }

    /**
     * 直接完成大师第一课任务
     *
     * @param questId
     * @param uid
     * @param subId
     * @param platform
     * @return pair [0: 已完成的子任务, 1: 成功完成的子任务]
     */
    @Override
    public Pair<List<Integer>, List<Integer>> completeCourseDirect(Long questId, Long uid, List<Integer> subId, String platform) {
        ActivityQuestAcceptDTO accept = findOrAccept(questId, uid);
        Set<Integer> records = getCourseRecord(accept);
        List<Integer> success = subId.stream().filter(id -> !records.contains(id))
                .map(id -> {
                    try {
                        return completeCourse(accept, id, platform) >= 0 ? id : null;
                    } catch (BaseRuntimeException e) {
                        log.error("[大师第一课] 子任务完成失败: questId: {}, uid: {}, subId: {}", questId, uid, id, e);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return Pair.of(new ArrayList<>(records), success);
    }

    /**
     * 完成课程
     *
     * @param accept   任务
     * @param subId    子任务ID
     * @param platform 平台
     * @return [0: 重复提交, -1: 失败, 1: 成功]
     */
    private int completeCourse(ActivityQuestAcceptDTO accept, Integer subId, String platform) {
        FirstClassCourseDTO subCourse = classCourseService.findById(subId)
                .orElseThrow(() -> new BaseRuntimeException("-1204", "找不到任务课"));

        Long uid = accept.getUid();
        Long questId = accept.getQuestId();

        // 入库记录, 以 uid + acceptId + subId 为唯一索引
        FirstClassViewRecordEntity record = new FirstClassViewRecordEntity();
        record.setUid(uid);
        record.setSubId(subCourse.getId());
        record.setQuestId(accept.getQuestId());
        record.setAcceptId(accept.getId());
        record.setCourseId(subCourse.getCourseId());
        record.setPlatform(platform);
        try {
            viewRepository.save(record);
        } catch (DataIntegrityViolationException e) {
            log.error("[大师第一课] 子任务完成重复提交", e);
            // 忽略重复记录, 关闭事务了 不需要执行
            if (false) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
            return 0;
        } catch (Exception e) {
            log.error("[大师第一课] 子任务完成失败: {}", record, e);
            return -1;
        } finally {
            clearCourseRecord(accept);
        }
        Long pointTaskId = subCourse.getRewards() == null ? null :
                subCourse.getRewards().stream().filter(r -> r.getId() == 1 && r.getReward() instanceof PointReward)
                        .findFirst().map(r -> ((PointReward) r.getReward()).getTaskId()).orElse(null);

        try {
            FirstClassUserWatchEventDTO event = FirstClassUserWatchEventDTO.builder()
                    .uid(uid)
                    .questId(questId)
                    .acceptId(accept.getId())
                    .subId(subId)
                    .courseId(subCourse.getCourseId())
                    .pointTaskId(pointTaskId)
                    .platform(platform)
                    .build();
            kafkaTemplate.send("first-class-user-watched", event)
                    .completable()
                    .whenComplete((result, ex) -> {
                        if (ex != null) {
                            log.error("[大师第一课] 子任务完成消息发送失败 event：{}", event, ex);
                        }
                    });
        } catch (Exception ex) {
            log.error("[大师第一课] 子任务完成成功, 但奖励发放失败 questId: {}, uid：{}, subId: {}", questId, uid, subId, ex);
            throw new BaseRuntimeException("-2033", "任务成功, 但奖励提交失败: " + ex.getMessage());
        }
        return 1;
    }

    @Override
    public boolean completeQuest(Long questId, Long acceptId, Long uid) {
        // 检查任务状态
        ActivityQuestAcceptDTO accepted = questService.findActive(questId, uid)
                .filter(s -> s.getId().equals(acceptId))
                .orElseThrow(() -> new BaseRuntimeException("找不到可完成任务"));
        if (accepted.getStatus() != ActivityQuestAcceptDTO.ACCEPTED) {
            return false;
        }
        ActivityQuestDTO quest = findClassQuest(questId)
                .filter(ActivityQuestDTO::isValid)
                .orElseThrow(() -> new BaseRuntimeException("任务不可用"));
        FirstClassQuestDefinitionDTO definition = findClassQuestDefinition(accepted.getQuestId())
                .orElseThrow(() -> new BaseRuntimeException("找不到任务内容"));
        // 完课奖励：计算达标
        boolean reached = definition.getCondition() != null &&
                          definition.getCondition().stream().allMatch(condition -> {
                              if (condition instanceof CountCondition) {
                                  return getCourseRecord(accepted).size() >= ((CountCondition) condition).value();
                              }
                              return false;
                          });
        if (!reached) {
            return false;
        }
        log.info("[大师第一课] 任务完成, uid: {}, questId: {}, acceptId: {}", accepted.getUid(), questId, acceptId);
        // 完课奖励：发放奖励
        if (!CollectionUtils.isEmpty(definition.getReward())) {
            List<AccountVO> accounts = userService.listAccountByUid(String.valueOf(accepted.getUid()));
            // 23-11-10 15:23:14 [新手任务大师第一课] 石静霞: 到期日更长的那个，能判断吗？
            // 寻找到期日最长的、符合奖励发放条件的账号
            LocalDateTime now = LocalDateTime.now();
            accounts.stream()
                    .filter(a -> a.getEndDate() != null && a.getEndDate().isAfter(now))
                    .sorted(Comparator.comparing(AccountVO::getEndDate).reversed())
                    .map(account -> {
                        List<QuestReward> rewards = QuestUtils.filterReward(definition.getReward(), target -> {
                            if (target instanceof PidTarget) {
                                return ((PidTarget) target).contains(account.getPid());
                            }
                            return false;
                        }).collect(Collectors.toList());
                        if (rewards.isEmpty()) {
                            return null;
                        }
                        return new AbstractMap.SimpleEntry<>(account, rewards);
                    })
                    .filter(Objects::nonNull)
                    .findFirst()
                    .ifPresent(entry -> {
                        AccountVO account = entry.getKey();
                        List<QuestReward> rewards = entry.getValue();
                        rewards.forEach(reward -> {
                            if (reward instanceof PrivilegeReward) {
                                sendPrivilege(account.getUsername(), (PrivilegeReward) reward, quest.getName());
                            } else {
                                log.error("[大师第一课] 完课奖励类型不支持, uid: {}, reward: {}", accepted.getUid(), reward);
                            }
                        });
                    });
        }
        Assert.state(questService.complete(accepted.getQuestId(), accepted.getUid()), "任务完成失败");
        return true;
    }

    @KafkaListener(groupId = "unifyFirstClassGroup", topics = "first-class-user-watched", autoStartup = "${spring.kafka.listener.enabled:true}")
    public void handleFirstClassUserWatched(FirstClassUserWatchEventDTO event) {
        try {
            // 单节奖励：加积分
            if (event.getPointTaskId() != null &&
                event.getQuestId() != 10014L && // 240322 黄珏：智盈第一课，不发放奖励,
                event.getQuestId() != 10020L && // 同上
                event.getQuestId() != 10021L && // 同上
                event.getQuestId() != 10023L && // 同上
                event.getQuestId() != 10024L // 同上
            ) {
                RewardResult result = sendPointReward(event.getUid(), event.getPointTaskId());
                if (result.isMaySuccess()) {
                    log.info("[大师第一课] 单节奖励成功 result: {}, uid: {}, taskId: {}", result.getMessage(), event.getUid(), event.getPointTaskId());
                } else {
                    log.error("[大师第一课] 单节奖励失败 result: {}, uid: {}, taskId: {}", result.getMessage(), event.getUid(), event.getPointTaskId());
                }
            }
            completeQuest(event.getQuestId(), event.getAcceptId(), event.getUid());
        } catch (Exception exp) {
            log.error("[大师第一课] 观看事件消费失败: {}", event, exp);
        }
    }

    @KafkaListener(groupId = "unifyFirstClassGroup", topics = "first-class-user-survey", autoStartup = "${spring.kafka.listener.enabled:true}")
    public void handleFirstClassUserSurvey(WjxSurveyResultDO survey) {
        Map<String, String> params = Optional.ofNullable(survey.getExt1())
                .filter(StringUtils::hasText)
                .map(query -> Arrays.stream(query.split("&"))
                        .map(item -> item.split("="))
                        .filter(item -> item.length == 2)
                        .collect(Collectors.toMap(s -> s[0], s -> s[1]))
                )
                .orElse(Collections.emptyMap());
        Long uid = Optional.ofNullable(survey.getEmuid()).map(Integer::longValue).orElse(null);
        Long questId = Optional.ofNullable(params.get("questId")).map(Long::parseLong).orElse(null);
        Integer subId = Optional.ofNullable(params.get("subId")).map(Integer::parseInt).orElse(null);

        if (uid == null || questId == null || subId == null) {
            log.error("[大师第一课] 问卷奖励 参数错误, uid: {}, questId: {}, subId: {}, param: {}", uid, questId, subId, survey.getSojumpparam());
            return;
        }
        ActivityQuestAcceptDTO accepted = questService.findActive(questId, uid)
                .orElse(null);
        if (accepted == null) {
            log.error("[大师第一课] 问卷奖励 找不到任务, uid: {}, questId: {}, subId: {}, param: {}", uid, questId, subId, survey.getSojumpparam());
            return;
        }
        Optional<FirstClassCourseDTO> courseOptional = classCourseService.findById(subId);
        // 判断问卷是否符合原始数据
        Optional<String> url = courseOptional.map(FirstClassCourseDTO::getExtraUrls).flatMap(urls ->
                urls.stream().filter(item -> item.getType() == 0).findFirst().map(FirstClassCourseDTO.ExtraUrl::getUrl)
        );
        if (!url.isPresent()) {
            log.error("[大师第一课] 问卷奖励 问卷错误, uid: {}, questId: {}, subId: {}, param: {}", uid, questId, subId, survey.getSojumpparam());
            return;
        }
        // 获取问卷奖励内容
        List<FirstClassCourseDTO.CourseReward> rewards = courseOptional
                .map(FirstClassCourseDTO::getRewards)
                .orElse(Collections.emptyList());

        rewards.stream()
                .filter(r -> r.getId() == 2 && r.getReward() instanceof PointReward)
                .findFirst().map(r -> ((PointReward) r.getReward()).getTaskId())
                .ifPresent(pointTaskId -> {
                    RewardResult result = sendPointReward(uid, pointTaskId);
                    if (result.isMaySuccess()) {
                        log.info("[大师第一课] 问卷奖励成功 {}, uid: {}, taskId: {}", result.getMessage(), uid, pointTaskId);
                    } else {
                        log.error("[大师第一课] 问卷奖励失败 {}, uid: {}, taskId: {}", result.getMessage(), uid, pointTaskId);
                    }
                });
    }

    private boolean sendPrivilege(String emAccount, PrivilegeReward reward, String name) {
        CreateActivityGrantApplyAccountDTO applyAccount = new CreateActivityGrantApplyAccountDTO();
        applyAccount.setAccountType(1);
        applyAccount.setAccount(emAccount);

        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(reward.getCode());
        sendPrivilegeDTO.setReason("活动任务: " + name);
        sendPrivilegeDTO.setApplyUserID("scb_public");
        sendPrivilegeDTO.setAccounts(Collections.singletonList(applyAccount));

        boolean success = Boolean.TRUE.equals(logisticsService.sendPrivilege(sendPrivilegeDTO));
        if (success) {
            log.info("[活动任务] {} 特权赠送成功, em: {}, activityId: {}", name, emAccount, reward.getCode());
        } else {
            log.error("[活动任务] {} 特权赠送失败, em: {}, activityId: {}", name, emAccount, reward.getCode());
        }
        return success;
    }

    private RewardResult sendPointReward(Long uid, Long pointTaskId) {
        try {
            pointService.addPointByTaskId(uid, pointTaskId);
            return RewardResult.success();
        } catch (PointException e) {
            if (e.isConflict()) {
                return RewardResult.maySuccess(e.getMessage());
            }
            return RewardResult.fail("积分发放失败:" + e.getMessage());
        }
    }

    /**
     * 查询特定功能使用天数
     *
     * @param uid
     * @return cn.emoney.pojo.FirstClassSpeFunUserDO
     * <AUTHOR>
     * @date 2023/2/14 11:29
     */
    @Override
    public int getSpecialFunDays(Long uid) {
        return speFunUserMapper.selectByParentid(String.valueOf(uid))
                .map(s -> s.core_use_days)
                .orElse(0);
    }

    private Optional<ActivityQuestDTO> findClassQuest(Long questId) {
        return questService.findQuest(questId)
                .filter(item -> "firstclass".equals(item.getType()));
    }

    private Optional<FirstClassQuestDefinitionDTO> findClassQuestDefinition(Long questId) {
        return Optional.ofNullable(questDefinitionMap.get(questId));
    }

    private final Map<Long, FirstClassQuestDefinitionDTO> questDefinitionMap = Collections.unmodifiableMap(new HashMap<Long, FirstClassQuestDefinitionDTO>() {{
        put(10001L, new FirstClassQuestDefinitionDTO()
                .setTarget(Collections.singletonList(new PidTarget(888020000L, 888080000L)))
                .setCondition(Collections.singletonList(new CountCondition(15)))
                .setReward(QuestReward.oneOf(
                        new PrivilegeReward("PAC1230921131915969").setTarget(new PidTarget(888020000L)),
                        new PrivilegeReward("PAC1230921131909745").setTarget(new PidTarget(888080000L))
                ))
        );
        put(10010L, new FirstClassQuestDefinitionDTO()
                .setTarget(Collections.singletonList(new PidTarget(888204010L, 888224010L)))
        );
        put(10011L, new FirstClassQuestDefinitionDTO()
                .setTarget(Collections.singletonList(new PidTarget(888020000L, 888080000L)))
        );
        put(10012L, new FirstClassQuestDefinitionDTO()
                .setTarget(Collections.singletonList(new PidTarget(888020000L, 888080000L)))
        );
        put(10014L, new FirstClassQuestDefinitionDTO()
                .setTarget(Collections.singletonList(new PidTarget(888010000L)))
        );
        put(10015L, new FirstClassQuestDefinitionDTO()
                .setTarget(Collections.singletonList(new PidTarget(888020000L, 888080000L)))
        );
        put(10016L, new FirstClassQuestDefinitionDTO()
                .setTarget(Collections.singletonList(new PidTarget(888204010L, 888224010L)))
        );
        put(10020L, new FirstClassQuestDefinitionDTO()
                .setTarget(Collections.singletonList(new PidTarget(888010000L)))
        );
        put(10021L, new FirstClassQuestDefinitionDTO()
                .setTarget(Collections.singletonList(new PidTarget(888010000L)))
        );
        put(10023L, new FirstClassQuestDefinitionDTO()
                .setTarget(Collections.singletonList(new PidTarget(888010000L)))
        );
        put(10024L, new FirstClassQuestDefinitionDTO()
                .setTarget(Collections.singletonList(new PidTarget(888010000L)))
        );
    }});

    private Optional<List<FirstClassContentDTO.ClassColumn>> findClassColumns(Long questId) {
        return Optional.ofNullable(columnMap.get(questId));
    }

    private final Map<Long, List<FirstClassContentDTO.ClassColumn>> columnMap = Collections.unmodifiableMap(new HashMap<Long, List<FirstClassContentDTO.ClassColumn>>() {{
        put(10001L, Arrays.asList(
                new FirstClassContentDTO.ClassColumn().setId(1).setName("大师第一课")
                        .setSize(9)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC),
                new FirstClassContentDTO.ClassColumn().setId(3).setName("实战篇")
                        .setSize(20)
                        .setDynamic(true)
                        .setSafeSize(3)
                        .setSort(FirstClassContentDTO.ClassColumn.TIME_DESC)
        ));
        put(10010L, Arrays.asList(
                new FirstClassContentDTO.ClassColumn().setId(12).setName("投资陪伴课")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC),
                new FirstClassContentDTO.ClassColumn().setId(13).setName("功能培训课")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC)
        ));
        put(10011L, Arrays.asList(
                new FirstClassContentDTO.ClassColumn().setId(6).setName("抄底专题")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC),
                new FirstClassContentDTO.ClassColumn().setId(7).setName("大师训练营")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.TIME_DESC)
        ));
        put(10012L, Arrays.asList(
                new FirstClassContentDTO.ClassColumn().setId(1).setName("启航训练")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC),
                new FirstClassContentDTO.ClassColumn().setId(2).setName("大师上手系列")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC),
                new FirstClassContentDTO.ClassColumn().setId(8).setName("大师第一课")
                        .setSize(30)
                        .setSort(FirstClassContentDTO.ClassColumn.TIME_DESC)
        ));
        put(10014L, Arrays.asList(
                new FirstClassContentDTO.ClassColumn().setId(9).setName("智盈第一课")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC)
        ));
        put(10015L, Arrays.asList(
                new FirstClassContentDTO.ClassColumn().setId(10).setName("大师第一课")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC),
                new FirstClassContentDTO.ClassColumn().setId(11).setName("大师训练营")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC)
        ));
        put(10016L, Arrays.asList(
                new FirstClassContentDTO.ClassColumn().setId(12).setName("投资陪伴课")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC),
                new FirstClassContentDTO.ClassColumn().setId(13).setName("功能培训课")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC)
        ));
        put(10020L, Arrays.asList(
                new FirstClassContentDTO.ClassColumn().setId(14).setName("智盈第一课")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC)
        ));
        put(10021L, Arrays.asList(
                new FirstClassContentDTO.ClassColumn().setId(14).setName("智盈第一课")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC)
        ));
        put(10023L, Arrays.asList(
                new FirstClassContentDTO.ClassColumn().setId(15).setName("实战大讲堂")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC)
        ));
        put(10024L, Arrays.asList(
                new FirstClassContentDTO.ClassColumn().setId(16).setName("实战大讲堂")
                        .setSize(99)
                        .setSort(FirstClassContentDTO.ClassColumn.ID_ASC)
        ));
    }});

    private static final Map<Integer, Comparator<FirstClassCourseDTO>> SORT_MAP = new HashMap<Integer, Comparator<FirstClassCourseDTO>>() {{
        put(FirstClassContentDTO.ClassColumn.ID_ASC, Comparator.comparing(FirstClassCourseDTO::getId));
        put(FirstClassContentDTO.ClassColumn.ID_DESC, Comparator.comparing(FirstClassCourseDTO::getId).reversed());
        put(FirstClassContentDTO.ClassColumn.TIME_ASC, Comparator.comparing(FirstClassCourseDTO::getStartTime, Comparator.nullsLast(Comparator.naturalOrder())));
        put(FirstClassContentDTO.ClassColumn.TIME_DESC, Comparator.comparing(FirstClassCourseDTO::getStartTime, Comparator.nullsLast(Comparator.reverseOrder())));
    }};
}
