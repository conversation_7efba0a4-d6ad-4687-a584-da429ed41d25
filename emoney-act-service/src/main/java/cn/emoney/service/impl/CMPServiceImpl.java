package cn.emoney.service.impl;

import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.bo.CmpLableDTO;
import cn.emoney.service.CMPService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021-12-22
 */
@Service
@Slf4j
public class CMPServiceImpl implements CMPService {

    @Value("${cmpLabelURL}")
    private String cmpLabelURL;

    @Override
    public String pushMessageToCMPLabel(CmpLableDTO cmpLableDTO){
        String res = OkHttpUtil.postJsonParams(cmpLabelURL, JSON.toJSONString(cmpLableDTO));
        return res;
    }
}
