package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.bo.EnsureUserRegisteredDTO;
import cn.emoney.service.MobileService;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.HexUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2021-12-14
 */
@Service
@Slf4j
public class MobileImpl implements MobileService {

    @Value("${MobileEncrptApi}")
    private String MobileEncrptApi;

    @Value("${WebApiEncryptKey}")
    private String WebApiEncryptKey;

    @Value("${SmsEncryptApi}")
    private String SmsEncryptApi;

    @Value("${AplicationId}")
    private String AplicationId;

    @Value("${AppID}")
    private String AppID;

    private Integer expirdTime = 60 * 60 * 24;

    @Autowired
    private RedisService redisService;

    @Value("${WeChatNotice:}")
    private String WeChatNotice;


    @Value("${ensureUserRegistered:}")
    private String EnsureUserRegisteredUrl;
    /**
     *
     * <AUTHOR>
     * @date 2023/8/16 15:00
     * @param mobile
     * @return java.lang.String
     */
    @Override
    public String mobileEncrpt_Upper(String mobile){
        String key = RedisConstants.Redis_Pre_Activity + "MobileEncrptUpper." + mobile;

        String val = redisService.get(key, String.class);

        if (val == null) {
            String apiUrl = MessageFormat.format(MobileEncrptApi, mobile, AplicationId);
            String TargetUrl = OkHttpUtil.createUrl(apiUrl, WebApiEncryptKey);

            String res = OkHttpUtil.get(TargetUrl, null);
            ApiGateWayResult<String> apiGateWayResult = JsonUtil.toBean(res, ApiGateWayResult.class);
            if (!apiGateWayResult.Message.isEmpty()) {
                String msg = apiGateWayResult.getMessage();

                val = "0x" + HexUtil.encodeHexStr(Base64.getDecoder().decode(msg)).toUpperCase();
                //val = BitConverter.toHexString(Base64.getDecoder().decode(msg));
                redisService.set(key, val, expirdTime.longValue());
            }
        }
        return val;
    }

    /**
     * 加密手机号
     * <AUTHOR>
     * @date 2021/12/14 17:51
     * @param mobile
     * @return java.lang.String
     */
    @Override
    public String mobileEncrpt(String mobile){
        String key = RedisConstants.Redis_Pre_Activity + "MobileEncrpt." + mobile;

        String val = redisService.get(key, String.class);

        if (val == null) {
            String apiUrl = MessageFormat.format(MobileEncrptApi, mobile, AplicationId);
            String TargetUrl = OkHttpUtil.createUrl(apiUrl, WebApiEncryptKey);

            String res = OkHttpUtil.get(TargetUrl, null);
            ApiGateWayResult<String> apiGateWayResult = JsonUtil.toBean(res, ApiGateWayResult.class);
            if (!apiGateWayResult.Message.isEmpty()) {
                String msg = apiGateWayResult.getMessage();

                val = "0x" + HexUtil.encodeHexStr(Base64.getDecoder().decode(msg)).toUpperCase();
                //val = BitConverter.toHexString(Base64.getDecoder().decode(msg));
                redisService.set(key, val, expirdTime.longValue());
            }
        }
        return val;
    }

    /**
     * 发送短信
     * <AUTHOR>
     * @date 2021/12/17 14:21
     * @param mobile
     * @param Content
     * @return java.lang.String
     */
    @Override
    public ApiGateWayResult<String> sendTextMessage(String mobile, String Content, String useid){
        String param=MessageFormat.format("appId={0}&ApplicationId={1}&content={2}&departmentId={3}&description={4}&level={5}&remark={6}",
                AppID,
                Content.contains("验证码")?"100013":AplicationId,
                Content,
                "1",
                "30|*********|"+useid,
                "1",
                "");
        param = param+"&phone="+ URLEncoder.encode(mobile);

        String TargetUrl = MessageFormat.format(SmsEncryptApi,param);
        String res = OkHttpUtil.get(TargetUrl, null);
        ApiGateWayResult<String> apiGateWayResult = JsonUtil.toBean(res, ApiGateWayResult.class);
        return apiGateWayResult;
    }

    @Override
    public void sendWarnTextMessage(String account, String content){
      try {
          String [] m= account.split(",");
          for (String x : m) {
              String json="{\"ToUser\":\""+x+"\",\"Text\":{\"Content\":\""+content+"\"}}";
              String res = OkHttpUtil.postJsonParams(WeChatNotice, json);
              log.info("sendWarnTextMessage:"+res);
          }
      }catch (Exception e) {
          log.warn(account+"sendWarnTextMessage:"+e.getMessage());
      }
    }

    @Override
    public void ensureUserRegistered(String mobile){
        //调用登录接口
        try {
            String encryptMobile = mobileEncrpt_Upper(mobile);
            String maskphone= DesensitizedUtil.mobilePhone(mobile);
            EnsureUserRegisteredDTO dto=new EnsureUserRegisteredDTO();
            dto.setEncryptedPhone(encryptMobile);
            dto.setMask(maskphone);
            String res= OkHttpUtil.postJsonParams(EnsureUserRegisteredUrl, JSON.toJSONString(dto));
            log.info(maskphone+":"+encryptMobile+"-调用了用户登录接口-结果："+res);
        }catch (Exception e) {
            log.warn("ensureUserRegistered:"+e.getMessage());
        }

    }


}
