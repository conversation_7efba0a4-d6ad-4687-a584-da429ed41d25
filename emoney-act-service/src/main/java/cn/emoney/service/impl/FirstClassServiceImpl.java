package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.mapper.activity.ActFirstclassSpeFunUserMapper;
import cn.emoney.mapper.activity.FirstClassViewRecordMapper;
import cn.emoney.pojo.FirstClassSpeFunUserDO;
import cn.emoney.pojo.FirstClassViewRecordDO;
import cn.emoney.pojo.bo.FirstClassDTO;
import cn.emoney.pojo.bo.PointRecordAddRequestDTO;
import cn.emoney.pojo.vo.result.ProductConfig;
import cn.emoney.pojo.vo.result.ProductConfigResult;
import cn.emoney.service.FirstClassService;
import cn.emoney.service.PointService;
import cn.emoney.service.SignRecordService;
import cn.emoney.service.VideoProgressService;
import cn.emoney.service.kafka.producer.ProducerService;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022-03-18
 */
@Service
@Slf4j
public class FirstClassServiceImpl implements FirstClassService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private PointService pointService;

    @Autowired(required = false)
    private FirstClassViewRecordMapper firstClassViewRecordMapper;

    @Autowired(required = false)
    private ActFirstclassSpeFunUserMapper firstclassspefunuserMapper;

    @Autowired
    private ProducerService producerService;

    @Autowired
    private VideoProgressService videoProgressService;

    @Value("${productConfigUrl}")
    private String productConfigUrl;

    @Value("${firstClassListConfigKey:}")
    private String firstClassListConfigKey;

    private String rediskey_classrecord= RedisConstants.Redis_Pre_Activity + "firstclass:classrecord:";

    /**
     * 完成课程用户入队列s
     * <AUTHOR>
     * @date 2022/3/28 15:30c
     * @param uid
     */
    public void pushUserClassRecord(String uid){
        producerService.sendMessage("firstClassUserComing",uid);
    }

    /**
     * 获取大师第一课课程列表
     * <AUTHOR>
     * @date 2022/3/18 14:46
     * @param uid
     * @return java.util.List<cn.emoney.pojo.bo.FirstClassDTO>
     */
    @Override
    public List<FirstClassDTO> getUserClassList(String uid,String activityID,String classID) {
        List<FirstClassDTO> list = getClassList();
        List<FirstClassDTO> retlist = new ArrayList<>();

        if (activityID == null || activityID.isEmpty()) {
            activityID = "1";
        }

        for (FirstClassDTO item :
                list) {
            if (item.activityID.equals(activityID)) {
                retlist.add(item);
            }
        }


        //用户状态
        if (!StringUtils.isEmpty(uid)) {
            for (FirstClassDTO item :
                    retlist) {
                item.isDone = HasClassRecord(uid, item.id.toString(), activityID);

                if(!StringUtils.isEmpty(classID)){
                    //指定课程，只查询特定课程时长
                    if(item.id.toString().equals(classID) && !item.isDone){
                        String webCastId = StringUtils.substringAfter(item.getClassUrl(), "-");
                        videoProgressService.getVideoPlayTime(uid, webCastId)
                                .ifPresent(response -> item.playTime = response);
                    }
                }else{
                    //未指定课程，则全部查询时长
                    if (!item.isDone) {
                        String webCastId = StringUtils.substringAfter(item.getClassUrl(), "-");
                        videoProgressService.getVideoPlayTime(uid, webCastId)
                                .ifPresent(response -> item.playTime = response);
                    }
                }

            }
        }
        return retlist;
    }
    @Override
    public boolean HasClassRecord(String uid,String classid,String activityID) {
        String key = "";
        if (activityID == null || activityID.isEmpty()) {
            key = rediskey_classrecord + uid;
            activityID = "1";
        } else {
            key = rediskey_classrecord + uid + ":" + activityID;
        }

        Object obj = redisService.hashGet(key, classid);
        if (obj != null && obj.toString().length() > 0) {
            return true;
        }
        return false;
    }

    /**
     * 记录大师第一课观看记录
     * <AUTHOR>
     * @date 2022/3/21 15:53
     * @param uid
     * @param pid
     * @param classid
     * @param platform // 1:pc 2:app 3:wechat
     * @return boolean
     */
    @Override
    public boolean SetClassRecord(String uid,String pid,String classid,String platform,String activityID) {
        String key = "";
        long donelength = activityID.equals("3") ? 8 : activityID.equals("4") ? 7 : 13;

        if(activityID.equals("5") || activityID.equals("7")|| activityID.equals("8")) {
            donelength = 15;
        }

        if (activityID == null || activityID.isEmpty()) {
            key = rediskey_classrecord + uid;
            activityID = "1";
        } else {
            key = rediskey_classrecord + uid + ":" + activityID;
        }

        //入库记录
        FirstClassViewRecordDO firstClassViewRecordDO = new FirstClassViewRecordDO();
        firstClassViewRecordDO.uid = uid;
        firstClassViewRecordDO.classid = Integer.parseInt(classid);
        firstClassViewRecordDO.platform = platform;
        firstClassViewRecordDO.createtime = new Date();

        List<FirstClassDTO> retlist = new ArrayList<>();
        int ret = firstClassViewRecordMapper.insert(firstClassViewRecordDO);
        if (ret > 0) {
            redisService.hashSet(key, classid, "1_" + platform);

            //判断是否加积分
            List<FirstClassDTO> list = getClassList();
            for (FirstClassDTO item :
                    list) {
                if (item.activityID.equals(activityID)) {
                    retlist.add(item);
                }
            }
            for (FirstClassDTO item :
                    retlist) {
                if (item.isHasPoint() && item.id.toString().equals(classid)) {
                    String taskid = item.pointTaskID;
                    //加积分
                    PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                    requestDTO.platform = "1";
                    requestDTO.pid = pid;
                    requestDTO.uid = uid;
                    requestDTO.subId = "";
                    requestDTO.taskId = taskid;

                    pointService.pointRecordAdd(requestDTO);
                }
            }
        } else {
            log.info("大师第一课看课记录失败SetClassRecord->request：" + JSON.toJSONString(firstClassViewRecordDO) + " response:" + ret);
            return false;
        }

        //第6期活动不赠送延期
        if(activityID.equals("6")){
            return true;
        }

        //判断是否看完课程，看完则存入队列
        Map<String, String> classList = (Map<String, String>) redisService.hashGetAll(key);
        if (classList != null && classList.size() >= donelength) {
            pushUserClassRecord(uid + "_" + activityID);
        }
        return true;
    }

    public List<FirstClassDTO> getClassList() {
        String parentType1 = "1", parentType2 = "2", parentType3 = "3", parentType4 = "4", parentType5 = "5", parentType6 = "6";
        String parentName1 = "学大师用大师成大师", parentName2 = "看盘不疑", parentName3 = "选股不难", parentName4 = "买入不急", parentName5 = "持股不焦卖出不悔", parentName6 = "仓位控制与心态", parentName7 = "直播", parentName8 = "录播";
        Object[][] classStr = {
//                {1, "学大师，用大师，成大师", "俞湧", "https://emoney.gensee.com/webcast/site/vod/play-d836f09e5d61458fb7632c8447f7b7c0", "2022-04-06 19:30:00", "2022-04-06 20:30:00", false, parentType1, true, "1506178774595670016", "", parentName1, 50,"","1",""},
//                {2, "什么时候赚结构底部的钱？", "褚伟锋", "https://emoney.gensee.com/webcast/site/vod/play-53fa3b277d7340988b81fba83d96962a", "2022-04-07 19:30:00", "2022-04-07 20:30:00", false, parentType2, false, "", "https://ks.wjx.top/vj/PpvtQEc.aspx", parentName2, 0,"","1",""},
//                {3, "什么时候赚市场情绪的钱？", "陈培树", "https://emoney.gensee.com/webcast/site/vod/play-15e6589a56d344b0bb3e6e5f553b9554", "2022-04-08 19:30:00", "2022-04-08 20:30:00", false, parentType2, true, "1506181663904894976", "", parentName2, 50,"","1",""},
//                {4, "不同市场环境下的选股要素", "王会圆", "https://emoney.gensee.com/webcast/site/vod/play-2a6e0128bea54c408c317734dd8d94f3", "2022-04-11 19:30:00", "2022-04-11 20:30:00", false, parentType3, false, "", "https://ks.wjx.top/vj/mPgiRJL.aspx", parentName3, 0,"","1",""},
//                {5, "求稳有方，<br>识别主力顺势为王", "朱峰", "https://emoney.gensee.com/webcast/site/vod/play-fc978eb709624223b7c21185faf7db51", "2022-04-12 19:30:00", "2022-04-12 20:30:00", false, parentType3, false, "", "http://www.emoney.cn/dianjin/bb/qwyf.pdf", parentName3, 0,"4月12领福利","1",""},
//                {6, "放眼未来，<br>浇灌成长之花", "孙文丰", "https://emoney.gensee.com/webcast/site/vod/play-ffcd947eed554ae69427223f43a28a4e", "2022-04-13 19:30:00", "2022-04-13 20:30:00", false, parentType3, false, "", "http://www.emoney.cn/dianjin/bb/lzyybk.pdf", parentName3, 0,"4月13领福利","1",""},
//                {7, "投资长跑，<br>找这样的公司活得久", "孙文丰", "https://emoney.gensee.com/webcast/site/vod/play-5804a565cc5d4d0190ae2079a22e672e", "2022-04-14 19:30:00", "2022-04-14 20:30:00", false, parentType3, false, "", "http://www.emoney.cn/dianjin/bb/yjbqy.pdf", parentName3, 0,"4月14领福利","1",""},
//                {8, "学会这一招，潜伏底部尖兵", "朱峰", "https://emoney.gensee.com/webcast/site/vod/play-b85083d9849e469285ebc43e77ab0a42", "2022-04-15 19:30:00", "2022-04-15 20:30:00", false, parentType4, false, "", "https://ks.wjx.top/vj/wbKlk3p.aspx", parentName4, 0,"","1",""},
//                {9, "梅开二度，<br>空中加油巧上车", "刘洋", "https://emoney.gensee.com/webcast/site/vod/play-340db90b61654d8780ef22af6e223073", "2022-04-18 19:30:00", "2022-04-18 20:30:00", false, parentType4, false, "", "https://ks.wjx.top/vj/Otf5LS2.aspx", parentName4, 0,"","1",""},
//                {10, "快马加鞭，<br>踏浪强势主升", "陈玉峰", "https://emoney.gensee.com/webcast/site/vod/play-578f1ab3cec3482b98903dd855f2628b", "2022-04-19 19:30:00", "2022-04-19 20:30:00", false, parentType4, false, "", "https://ks.wjx.top/vj/PecQ5UL.aspx", parentName4, 0,"","1",""},
//                {11, "守正耐心，<br>等一个好价格上车", "孙文丰", "https://emoney.gensee.com/webcast/site/vod/play-4572278a8b89466ab8898197466d86c2", "2022-04-20 19:30:00", "2022-04-20 20:30:00", false, parentType4, true, "1506182252395106304", "", parentName4, 70,"","1",""},
//                {12, "以退为进，三步卖出锁定利润", "陈培树", "https://emoney.gensee.com/webcast/site/vod/play-8be2062fe25a47bbbc16964f229b9681", "2022-04-21 19:30:00", "2022-04-21 20:30:00", false, parentType5, false, "", "http://www.emoney.cn/dianjin/bb/ytwj.pdf", parentName5, 0,"4月21领福利","1",""},
//                {13, "告别满仓被套，仓位管理早点学", "褚伟锋", "https://emoney.gensee.com/webcast/site/vod/play-5a8b956d23ed42e0950ffbfc028f88bc", "2022-04-22 19:30:00", "2022-04-22 20:30:00", false, parentType6, false, "", "", parentName6, 0,"","1",""},
//
//                {14, "学大师，用大师，成大师", "俞湧", "https://emoney.gensee.com/webcast/site/vod/play-92ad1052e7f44f67884e9d8fb086969e", "2022-07-18 19:30:00", "2022-07-18 20:30:00", false, parentType1, true, "1544874307547697152", "", parentName1, 50,"","2","A0880611010001"},
//                {15, "什么时候赚结构底部的钱？", "褚伟锋", "https://emoney.gensee.com/webcast/site/vod/play-051b158d207f4f0baafbdea6ded3e4cf", "2022-07-19 19:30:00", "2022-07-19 20:30:00", false, parentType2, false, "", "https://ks.wjx.top/vj/PpvtQEc.aspx", parentName2, 0,"","2","A0880613120002"},
//                {16, "什么时候赚市场情绪的钱？", "陈培树", "https://emoney.gensee.com/webcast/site/vod/play-761d2cfe0f45429d85b3ecab192fd2fc", "2022-07-20 19:30:00", "2022-07-20 20:30:00", false, parentType2, true, "1544874694417715200", "", parentName2, 50,"","2","A0880616070007"},
//                {17, "不同市场环境下的选股要素", "王会圆", "https://emoney.gensee.com/webcast/site/vod/play-e1ff7c9edced458a9ae548b8a7b4e2c1", "2022-07-21 19:30:00", "2022-07-21 20:30:00", false, parentType3, false, "", "https://ks.wjx.top/vj/PpvtQEc.aspx", parentName3, 0,"","2","A0880619080003"},
//                {18, "求稳有方，识别主力顺势为王", "朱峰", "https://emoney.gensee.com/webcast/site/vod/play-1ae115286f694eb6af1d1cca69bd2bbe", "2022-07-25 19:30:00", "2022-07-25 20:30:00", false, parentType3, false, "", "http://www.emoney.cn/dianjin/bb/qwyf.pdf", parentName3, 0,"7月25领福利","2","A0880618080005"},
//                {19, "放眼未来，浇灌成长之花", "孙文丰", "https://emoney.gensee.com/webcast/site/vod/play-f64d32dc28964c0dac9f7390ef818bbe", "2022-07-26 19:30:00", "2022-07-26 20:30:00", false, parentType3, false, "", "http://www.emoney.cn/dianjin/bb/lzyybk.pdf", parentName3, 0,"7月26领福利","2","A0880620010002"},
//                {20, "投资长跑，找这样的公司活得久", "孙文丰", "https://emoney.gensee.com/webcast/site/vod/play-10cc52b23bda4b4392844a5caebffc20", "2022-07-27 19:30:00", "2022-07-27 20:30:00", false, parentType3, false, "", "http://www.emoney.cn/dianjin/bb/yjbqy.pdf", parentName3, 0,"7月27领福利","2","A0880620010002"},
//                {21, "学会这一招，潜伏底部尖兵", "朱峰", "https://emoney.gensee.com/webcast/site/vod/play-22efa854422c46139b19684df0467551", "2022-07-28 19:30:00", "2022-07-28 20:30:00", false, parentType4, false, "", "https://ks.wjx.top/vm/h5kxkXo.aspx", parentName4, 0,"","2","A0880618080005"},
//                {22, "梅开二度，空中加油巧上车", "刘洋", "https://emoney.gensee.com/webcast/site/vod/play-a3e6e0cfde7345e1a56d0f153b5ce28f", "2022-08-01 19:30:00", "2022-08-01 20:30:00", false, parentType4, false, "", "https://ks.wjx.top/vm/rfkcGWK.aspx", parentName4, 0,"","2","A0880618070006"},
//                {23, "快马加鞭，踏浪强势主升", "陈玉峰", "https://emoney.gensee.com/webcast/site/vod/play-c341e1867ad04817884f1b528bfb8c25", "2022-08-02 19:30:00", "2022-08-02 20:30:00", false, parentType4, false, "", "https://ks.wjx.top/vm/rfkcGWK.aspx", parentName4, 0,"","2","A0880618060005"},
//                {24, "守正耐心，等一个好价格上车", "孙文丰", "https://emoney.gensee.com/webcast/site/vod/play-6bd717bebc024038aca166fed4dff0bb", "2022-08-03 19:30:00", "2022-08-03 20:30:00", false, parentType4, true, "1544875093547683840", "", parentName4, 70,"","2","A0880620010002"},
//                {25, "以退为进，三步卖出锁定利润", "陈培树", "https://emoney.gensee.com/webcast/site/vod/play-5c55db8194a54f16b42b7952c1ee2d59", "2022-08-04 19:30:00", "2022-08-04 20:30:00", false, parentType5, false, "", "http://www.emoney.cn/dianjin/bb/ytwj.pdf", parentName5, 0,"8月4号领福利","2","A0880616070007"},
//                {26, "告别满仓被套，仓位管理早点学", "褚伟锋", "https://emoney.gensee.com/webcast/site/vod/play-2ff05c891a6c41a29e047091ed674747", "2022-08-08 19:30:00", "2022-08-08 20:30:00", false, parentType6, false, "", "", parentName6, 0,"","2","A0880613120002"},

                // {27, "首席开篇", "俞湧", "https://emoney.gensee.com/webcast/site/vod/play-425558d5fec74cc385b4a08f98e1f8ea", "2022-09-30 19:30:00", "2022-09-30 20:30:00", false, parentType3, true, "1581944308402098176", "https://www.emoney.cn/dianjin/bb/ktbj1.pdf", parentName3, 50,"报告","3","A0880620010002","9782","学大师，用大师，成大师"},
                // {28, "看大势做决策", "王会圆", "http://emoney.gensee.com/webcast/site/vod/play-c981107aec6245289b5179c717201487", "2022-09-30 19:30:00", "2022-09-30 20:30:00", false, parentType3, true, "1581945019261128704", "https://www.emoney.cn/dianjin/bb/ktbj2.pdf", parentName3, 50,"笔记","3","A0880620010002","9783","学看盘、辨环境、选策略"},
                // {29, "识别主力资金", "朱峰", "http://emoney.gensee.com/webcast/site/vod/play-eb47b569114e4bb582461411e15fdfc7", "2022-09-30 19:30:00", "2022-09-30 20:30:00", false, parentType4, true, "1581945244570750976", "https://www.emoney.cn/dianjin/bb/ktbj3.pdf", parentName4, 50,"笔记","3","A0880618080005","9784","学用资金指标、分辨主力意图"},
                // {30, "潜伏底部尖兵", "朱峰", "http://emoney.gensee.com/webcast/site/vod/play-bb24e7c00121414ba3c9b085c778ab8a", "2022-09-30 19:30:00", "2022-09-30 20:30:00", false, parentType4, true, "1581945457444261888", "https://www.emoney.cn/dianjin/bb/ktbj4.pdf", parentName4, 50,"笔记","3","A0880618070006","9785","学拐点抄底、抄底三剑客策略"},
                // {31, "顺势巧上车", "刘洋", "http://emoney.gensee.com/webcast/site/vod/play-6a5fce5d70f9449fbd8503a12227ba3d", "2022-09-30 19:30:00", "2022-09-30 20:30:00", false, parentType4, true, "1581945679708819456", "https://www.emoney.cn/dianjin/bb/ktbj5.pdf", parentName4, 50,"笔记","3","A0880618060005","9786","学量王叠现、黄金回踩策略"},
                // {32, "踏浪强势股", "陈玉峰", "http://emoney.gensee.com/webcast/site/vod/play-458596ea0076491b855c03a3ededaf93", "2022-09-30 19:30:00", "2022-09-30 20:30:00", false, parentType4, true, "1581946235684786176", "https://www.emoney.cn/dianjin/bb/ktbj6.pdf", parentName4, 50,"笔记","3","A0880620010002","9787","学突破策略、突破出击点、回踩确认技巧"},
                // {33, "把握“三好”公司", "孙文丰", "http://emoney.gensee.com/webcast/site/vod/play-916d5f980b9e439e959440be7b432bec", "2022-09-30 19:30:00", "2022-09-30 20:30:00", false, parentType5, true, "1581946053412917248", "https://www.emoney.cn/dianjin/bb/ktbj7.pdf", parentName5, 50,"笔记","3","A0880616070007","9788","学基本面2.5分法则，怎么选成长股与价值股"},
                // {34, "三步卖出法", "陈培树", "http://emoney.gensee.com/webcast/site/vod/play-ceda98b98f1a45cfb1ca420374969ba3", "2022-09-30 19:30:00", "2022-09-30 20:30:00", false, parentType6, true, "1581945889059115008", "https://www.emoney.cn/dianjin/bb/ktbj8.pdf", parentName6, 50,"笔记","3","A0880613120002","9789","学止盈、止损方法与指标技巧"},
//
                // {35, "我的投资习惯：K线里的故事（上）", "陈玉峰", "https://emoney.gensee.com/webcast/site/vod/play-e0db575199bc42fb85a3453054e0cec8", "2023-01-06 19:30:00", "2023-01-06 20:30:00", false, parentType3, true, "", "https://www.emoney.cn/dianjin/bb/kxgs1.pdf", parentName3, 0,"报告","4","A0880620010002","10563","学大师，用大师，成大师"},
                // {36, "我的投资习惯：K线里的故事（下）", "陈玉峰", "https://emoney.gensee.com/webcast/site/vod/play-f585bf935cdd4902ae4a0b1feab760c1", "2023-01-06 19:30:00", "2023-01-06 20:30:00", false, parentType3, true, "", "https://www.emoney.cn/dianjin/bb/kxgs2.pdf", parentName3, 0,"报告","4","A0880620010002","10564","学看盘、辨环境、选策略"},
                // {37, "轻松抄底，玩转波段", "陈培树", "https://emoney.gensee.com/webcast/site/vod/play-a204a426b9fb40d0ae996d06c3a30f20", "2023-02-06 19:30:00", "2023-02-06 20:30:00", false, parentType4, true, "", "https://www.emoney.cn/dianjin/bb/dszxk.pdf", parentName4, 0,"笔记","4","A0880618080005","10565","学用资金指标、分辨主力意图"},
                // {38, "开资金天眼，识主力洗盘", "陈培树", "https://emoney.gensee.com/webcast/site/vod/play-524b3a4da3ed4417856af3050e8f490d", "2023-02-13 19:30:00", "2023-02-13 20:30:00", false, parentType4, true, "", "https://www.emoney.cn/dianjin/bb/kaizijintianyan.pdf", parentName4, 0,"笔记","4","A0880618070006","10566","学拐点抄底、抄底三剑客策略"},
                // {39, "向阳而行，稳中求进", "陈培树", "https://emoney.gensee.com/webcast/site/vod/play-ae835507d5f04e8d9f06af2b21096faf", "2023-01-30 19:30:00", "2023-01-30 20:30:00", false, parentType4, true, "", "https://www.emoney.cn/dianjin/bb/kainiandiyikeketangbijixiangyangersheng.pdf", parentName4, 0,"笔记","4","A0880618060005","10567","学量王叠现、黄金回踩策略"},
                // {40, "紧跟聪明，布局新主线", "刘宗鑫", "https://emoney.gensee.com/webcast/site/vod/play-38ec5b009cce4610a1d69a4cd7da76b7", "2023-02-07 19:30:00", "2023-02-07 20:30:00", false, parentType4, true, "", "https://www.emoney.cn/dianjin/bb/aigh.pdf", parentName4, 0,"笔记","4","A0880620010002","10568","学突破策略、突破出击点、回踩确认技巧"},
                // {41, "瞄准风口，狙击强势", "王春雷", "https://emoney.gensee.com/webcast/site/vod/play-ab5014ed79ab44988b49588c25e96d02", "2023-02-15 19:30:00", "2023-02-15 20:30:00", false, parentType5, true, "", "https://www.emoney.cn/dianjin/bb/wurenji.pdf", parentName5, 0,"笔记","4","A0880616070007","10569","学基本面2.5分法则，怎么选成长股与价值股"},
                // {42, "掘金中国好公司", "林一鸣", "https://emoney.gensee.com:443/webcast/site/vod/play-e892b33469fd42e4bc285b0fe0f9d004", "2023-01-06 19:30:00", "2023-01-06 20:30:00", false, parentType6, true, "", "https://www.emoney.cn/dianjin/bb/baogao3.pdf", parentName6, 0,"报告","4","A0880613120002","10570","学止盈、止损方法与指标技巧"},
                // {43, "稳健增仓，追击北上", "刘洋", "https://emoney.gensee.com/webcast/site/vod/play-74fd095d982d4eea90fe21fab96710ea", "2023-02-14 21:30:00", "2023-02-14 22:30:00", false, parentType5, true, "", "https://www.emoney.cn/dianjin/bb/wenjianjiancang.pdf", parentName5, 0,"报告","4","A0880616070007","10571","学基本面2.5分法则，怎么选成长股与价值股"},
                // {44, "风口狂飙，引爆赛道", "林一鸣", "https://emoney.gensee.com/webcast/site/vod/play-3851998235d245848310b12a0292c12e", "2023-02-16 19:30:00", "2023-02-16 20:30:00", false, parentType6, true, "", "https://www.emoney.cn/dianjin/bb/bandaoti.pdf", parentName6, 0,"报告","4","A0880613120002","10572","学止盈、止损方法与指标技巧"},

        };


        List<FirstClassDTO> list = new ArrayList<>();
        try {
//            for (int i = 0; i < classStr.length; i++) {
//                FirstClassDTO classDTO = new FirstClassDTO();
//                classDTO.id = Integer.parseInt(classStr[i][0].toString());
//                classDTO.className = classStr[i][1].toString();
//                classDTO.teacherName = classStr[i][2].toString();
//                classDTO.classUrl = classStr[i][3].toString();
//
//                DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                Date beginTime = dateFormat.parse(classStr[i][4].toString());
//                Date endTime = dateFormat.parse(classStr[i][5].toString());
//                classDTO.beginTime = beginTime;
//                classDTO.endTime = endTime;
//                classDTO.isDone = (Boolean) classStr[i][6];
//                classDTO.parentID = Integer.parseInt(classStr[i][7].toString());
//                classDTO.hasPoint = (Boolean) classStr[i][8];
//                classDTO.pointTaskID = classStr[i][9].toString();
//                classDTO.voteUrl = classStr[i][10].toString();
//                classDTO.parentName = classStr[i][11].toString();
//                if (classStr[i][12] != null) {
//                    classDTO.point = Integer.parseInt(classStr[i][12].toString());
//                }
//                classDTO.tipName = classStr[i][13].toString();
//                classDTO.activityID = classStr[i][14].toString();
//                classDTO.tgNO = classStr[i][15].toString();
//
//                if(classStr[i].length>16){
//                    classDTO.liveVideoID = classStr[i][16].toString();
//                }
//                if(classStr[i].length>17){
//                    classDTO.classSummary = classStr[i][17].toString();
//                }
//                list.add(classDTO);
//            }

            //20230213-后续新增活动数据来源产品配置后台
            ProductConfig config =  getProductConfig(firstClassListConfigKey);
            if(config!=null) {
                String classlistStr = config.getConfigContent();
                List<FirstClassDTO> listnew = JsonUtil.toBeanList(classlistStr, FirstClassDTO.class);
                if (listnew != null && listnew.size() > 0) {
                    for (FirstClassDTO item :
                            listnew) {
                        list.add(item);
                    }
                }
            }

        } catch (Exception e) {
            return null;
        }
        return list;
    }

    /**
     * 获取产品配置
     */
    public ProductConfig getProductConfig(String configKey) {
        String rediskey = RedisConstants.Redis_Pre_Activity + "getProductConfig:" + configKey;
        ProductConfig config = null;
        config = redisService.get(rediskey,ProductConfig.class);
        if (config != null) {
            return config;
        }

        String url = MessageFormat.format(productConfigUrl, configKey);
        String ret = OkHttpUtil.get(url, null);
        ProductConfigResult result = JSON.parseObject(ret, ProductConfigResult.class);
        if (result != null && "0".equals(result.getRetCode()) && result.getMessage() != null) {
            config = result.getMessage();

            redisService.set(rediskey, config, 5L, TimeUnit.MINUTES);
        }
        return config;
    }

    /**
     * 查询特定功能使用天数
     * <AUTHOR>
     * @date 2023/2/14 11:29
     * @param uid
     * @return cn.emoney.pojo.FirstClassSpeFunUserDO
     */
    @Override
    public Integer getSpecialFunDays(String uid) {
        return firstclassspefunuserMapper.selectByParentid(uid)
                .map(s -> s.core_use_days)
                .orElse(0);
    }
}
