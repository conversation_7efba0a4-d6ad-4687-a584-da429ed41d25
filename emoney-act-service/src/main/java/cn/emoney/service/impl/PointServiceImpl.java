package cn.emoney.service.impl;

import cn.emoney.act.exception.PointException;
import cn.emoney.common.result.PointResult;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.bo.*;
import cn.emoney.service.PointService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021-12-15
 */
@Service
@Slf4j
public class PointServiceImpl implements PointService {
    private final RestTemplate forceJsonTemplate;

    @Value("${pointRecoredURL}")
    private String pointRecoredURL;

    @Value("${pointQuerySummaryURL}")
    private String pointQuerySummaryURL;

    @Value("${pointOrderAddURL}")
    private String pointOrderAddURL;

    @Value("${pointOrderExchangeURL}")
    private String pointOrderExchangeURL;

    @Value("${QueryByTaskidsURL}")
    private String pointQueryByTaskidsURL;

    @Value("${QueryOrderByProductIdURL}")
    private String queryOrderByProductIdURL;

    public PointServiceImpl(@Qualifier("forceJsonTemplate") RestTemplate forceJsonTemplate) {
        this.forceJsonTemplate = forceJsonTemplate;
    }

    /**
     * 加积分
     * <AUTHOR>
     * @date 2021/12/16 13:22
     * @param requestDTO 
     * @return java.lang.Boolean
     */
    @Override
    public Boolean pointRecordAdd(PointRecordAddRequestDTO requestDTO) {
        String res = OkHttpUtil.postJsonParams(pointRecoredURL, JSON.toJSONString(requestDTO));

        log.info("加积分pointRecordAdd->request：" + JSON.toJSONString(requestDTO) + " response:" + res);

        PointResult<String> pointResult = JsonUtil.toBean(res, PointResult.class);
        if (pointResult != null && pointResult.isSuccess()) {
            return true;
        }
        return false;
    }

    @Override
    public PointAddResult addPointByTaskId(Long uid, Long taskId) throws PointException {
        PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
        requestDTO.platform = "1";
        requestDTO.pid = "";
        requestDTO.uid = String.valueOf(uid);
        requestDTO.subId = "";
        requestDTO.taskId = String.valueOf(taskId);
        PointAddResponse response = forceJsonTemplate.postForObject(pointRecoredURL, requestDTO, PointAddResponse.class);
        Assert.state(response != null, "积分API调用失败");
        if (!response.isSuccess()) {
            throw new PointException(response);
        }
        return response.getData();
    }

    private static class PointAddResponse extends PointResult<PointAddResult> {
    }

    /**
     * 积分查询
     * <AUTHOR>
     * @date 2021/12/16 13:28
     * @param uid
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.dto.PointQueryDataDTO>
     */
    @Override
    public List<PointQueryDataDTO> pointQuerySummary(String uid) {
        List<PointQueryDataDTO> pointQueryDataDTOList = new ArrayList<>();
        String url = pointQuerySummaryURL + "&uid=" + uid;
        String res = OkHttpUtil.get(url, null);
        PointResult<List<PointQueryDataDTO>> pointResult = JsonUtil.toBean(res, PointResult.class);
        if (pointResult != null && pointResult.isSuccess()) {
            pointQueryDataDTOList = pointResult.getData();
        }

        return JsonUtil.toBeanList(JSON.toJSONString(pointQueryDataDTOList),PointQueryDataDTO.class);
    }

    /**
     * 积分订单创建
     * <AUTHOR>
     * @date 2021/12/16 13:31
     * @param requestDTO 
     * @return cn.emoney.activityweb.repository.dao.entity.dto.PointOrderAddDataDTO
     */
    @Override
    public PointResult<PointOrderAddDataDTO> pointOrderAdd(PointOrderAddRequestDTO requestDTO){
        PointOrderAddDataDTO pointOrderAddDataDTO = new PointOrderAddDataDTO();

        String res = OkHttpUtil.postJsonParams(pointOrderAddURL, JSON.toJSONString(requestDTO));

        log.info("积分订单创建pointOrderAdd->request：" + JSON.toJSONString(requestDTO) + " response:" + res);

        PointResult<PointOrderAddDataDTO> pointResult = JsonUtil.toBean(res, PointResult.class);
        return pointResult;
        //return JsonUtil.toBean(JSON.toJSONString(pointOrderAddDataDTO),PointOrderAddDataDTO.class);
    }
    
    /**
     * 积分兑换支付回调
     * <AUTHOR>
     * @date 2021/12/16 13:34
     * @param requestDTO 
     * @return cn.emoney.activityweb.repository.dao.entity.dto.PointOrderAddDataDTO
     */
    @Override
    public PointResult<PointOrderAddDataDTO> pointOrderExchange(PointOrderExchangeRequestDTO requestDTO){
        PointOrderAddDataDTO pointOrderAddDataDTO = new PointOrderAddDataDTO();

        String res = OkHttpUtil.postJsonParams(pointOrderExchangeURL, JSON.toJSONString(requestDTO));

        log.info("积分兑换支付回调pointOrderExchange->request：" + JSON.toJSONString(requestDTO) + " response:" + res);

        PointResult<PointOrderAddDataDTO> pointResult = JsonUtil.toBean(res, PointResult.class);

        return pointResult;
        //return JsonUtil.toBean(JSON.toJSONString(pointOrderAddDataDTO),PointOrderAddDataDTO.class);
    }

    /**
     * 根据积分任务id查询任务状态
     * <AUTHOR>
     * @date 2022/1/25 11:11
     * @param requestDTO
     * @return java.util.List<cn.emoney.pojo.bo.PointQueryByTaskIDDataDTO>
     */
    @Override
    public List<PointQueryByTaskIDDataDTO> pointQueryByTaskID(PointQueryByTaskIDRequestDTO requestDTO){
        List<PointQueryByTaskIDDataDTO>  list = new ArrayList<>();
        String res = OkHttpUtil.postJsonParams(pointQueryByTaskidsURL, JSON.toJSONString(requestDTO));
        PointResult<List<PointQueryByTaskIDDataDTO>> pointResult = JsonUtil.toBean(res, PointResult.class);
        if (pointResult != null && pointResult.isSuccess()) {
            list = pointResult.getData();
        }
        return JsonUtil.toBeanList(JSON.toJSONString(list),PointQueryByTaskIDDataDTO.class);
    }

    @Override
    public Map<Long, List<PointDetailDTO>> queryRecordByTaskId(Long uid, List<Long> taskId) {
        if (taskId == null || taskId.isEmpty()) {
            return Collections.emptyMap();
        }
        PointQueryByTaskIDRequestDTO requestDTO = new PointQueryByTaskIDRequestDTO();
        requestDTO.uid = uid;
        requestDTO.taskIds = taskId.toArray(new Long[0]);
        QueryByTaskIdResponse response = forceJsonTemplate.postForObject(pointQueryByTaskidsURL, requestDTO, QueryByTaskIdResponse.class);
        Assert.state(response != null, "积分API调用失败");
        Assert.state(response.isSuccess(), "积分API调用失败: " + response.getMsg());
        if (response.getData() != null) {
            return response.getData().stream().collect(Collectors.groupingBy(PointDetailDTO::getTaskId));
        }
        return Collections.emptyMap();
    }

    @Override
    public List<PointDetailDTO> queryRecordByTaskId(Long uid, Long taskId) {
        return queryRecordByTaskId(uid, Collections.singletonList(taskId)).getOrDefault(taskId, Collections.emptyList());
    }

    private static class QueryByTaskIdResponse extends PointResult<List<PointDetailDTO>> {
    }

    /**
     * 根据uid和商品id查询订单
     * <AUTHOR>
     * @date 2022/3/15 15:33
     * @param uid
     * @param productId 
     * @return cn.emoney.common.result.PointResult<cn.emoney.pojo.bo.PointOrderAddDataDTO>
     */
    @Override
    public PointResult<List<PointOrderAddDataDTO>> pointOrderQueryByUidAndProId(String uid,String productId){
        String res = OkHttpUtil.get(MessageFormat.format(queryOrderByProductIdURL, uid,productId),null);

        PointResult<List<PointOrderAddDataDTO>> pointResult = JsonUtil.toBean(res, PointResult.class);

        return pointResult;
    }
}
