package cn.emoney.service.impl;

import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.common.utils.RSAUtils;
import cn.emoney.pojo.bo.CmpLableDTO;
import cn.emoney.pojo.bo.SendCouponRequestDTO;
import cn.emoney.pojo.vo.result.CouponConfig;
import cn.emoney.pojo.vo.result.ProductConfig;
import cn.emoney.pojo.vo.result.ProductConfigResult;
import cn.emoney.service.CMPService;
import cn.emoney.service.CouponService;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.MobileService;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.security.PrivateKey;
import java.text.MessageFormat;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2022/12/12 13:23
 * @version: V1.0
 * @description:
 **/
@Service
public class CouponServiceImpl implements CouponService {

    @Value("${productConfigUrl}")
    private String productConfigUrl;

    @Value("${sendCouponConfigKey}")
    private String sendCouponConfigKey;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private MobileService mobileService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CMPService cmpService;

    private final String CacheKey_SendCouponFlag = "EMoney:Activity:Coupon:";
    private final String CacheKey_SendCouponLock = "EMoney:Activity:SendCouponLock:";
    // EMoney:Activity:SendCouponLock:0x50165C2BD04B4787B7E92FDFB1D6DA3B
    /**
     * 发送优惠券
     * @param actCode
     * @param mobile
     */
    public Result<String> sendCoupon(String actCode, String mobile) throws Exception{
        // 获取私钥
        InputStream resourceAsStream = RSAUtils.class.getClassLoader().getResourceAsStream("security/key.pem");
        String privateKeyStr = IoUtil.read(resourceAsStream, CharsetUtil.UTF_8);
        // 获取加密手机号
        mobile = RSAUtils.decrypt(mobile,privateKeyStr);
        String mobileX = mobileService.mobileEncrpt(mobile);
        Long hasSend = redisService.incrBy(CacheKey_SendCouponLock + mobileX);
        if(hasSend > 1){
            return Result.buildErrorResult("领取失败：请勿重复领取");
        }
        List<CouponConfig> configList = getCouponConfigList(actCode);
        SendCouponRequestDTO req = null;
        LogisticsResult<String> result = null;
        if(configList!=null && configList.size() > 0){
            for(CouponConfig config : configList){
                String flag = (String) redisService.hashGet(CacheKey_SendCouponFlag + actCode + ":" + config.getCouponBagId(), mobileX);
                if(!StringUtils.isBlank(flag) && "1".equals(flag)){
                    redisService.remove(CacheKey_SendCouponLock + mobileX);
                    return Result.buildErrorResult("领取失败：优惠券已发放");
                }
                req = new SendCouponRequestDTO();
                req.PRESENT_ACCOUNT_TYPE = 2;
                req.PRESENT_ACCOUNT = mobileX;
                req.COUPON_ACTIVITY_ID = config.getCouponBagId();
                req.COUPON_RULE_PRICE = config.getCouponPrice();
                req.PRESENT_PERSON = config.getActCode();
                result = logisticsService.sendCoupon(req);
                if(result.getCode() == 0){
                    // 设置已发送优惠券标识
                    redisService.hashSet(CacheKey_SendCouponFlag + actCode + ":" + config.getCouponBagId(),mobileX,"1");
                    // 优惠券发送成功推送cmp
                    CmpLableDTO label = new CmpLableDTO();
                    label.setAdcode("JoinAct20220910");
                    label.setLogtype("sendCoupon");
                    label.setUname(mobileX);
                    cmpService.pushMessageToCMPLabel(label);
                }else{
                    redisService.remove(CacheKey_SendCouponLock + mobileX);
                    return Result.buildErrorResult("领取失败：" + result.getMsg());
                }
            }
            redisService.remove(CacheKey_SendCouponLock + mobileX);
            return Result.buildSuccessResult();
        }else{
            redisService.remove(CacheKey_SendCouponLock + mobileX);
            return Result.buildErrorResult("领取失败：未获取到优惠券配置信息");
        }
    }

    /**
     * 移除领取缓存标识
     * @param actCode
     * @param mobile
     * @return
     */
    public Result<String> remove(String actCode,String mobile){
        List<CouponConfig> configList = getCouponConfigList(actCode);
        SendCouponRequestDTO req = null;
        LogisticsResult<String> result = null;
        String mobileX = mobileService.mobileEncrpt(mobile);
        redisService.remove(CacheKey_SendCouponLock + mobileX);
        if(configList!=null && configList.size() > 0) {
            for (CouponConfig config : configList) {
                redisService.hashDel(CacheKey_SendCouponFlag + actCode + ":" + config.getCouponBagId(), mobileX);
            }
        }
        return Result.buildSuccessResult();
    }

    /**
     * 获取优惠券配置
     * @param actCode
     * @return
     */
    private List<CouponConfig> getCouponConfigList(String actCode){
        List<CouponConfig> list = null;
        ProductConfig productConfig = getProductConfig(sendCouponConfigKey);
        if(productConfig!=null &&
                !StringUtils.isBlank(productConfig.getConfigContent())){
            list = JsonUtil.toBeanList(productConfig.getConfigContent(),CouponConfig.class);
            if(list!=null && list.size() > 0){
                list = list.stream().filter(x->x.getActCode().equals(actCode)).collect(Collectors.toList());
            }
        }
        return list;
    }

    /**
     * 获取产品配置
     */
    private ProductConfig getProductConfig(String configKey){
        ProductConfig config = null;
        String url = MessageFormat.format(productConfigUrl, configKey);
        String ret = OkHttpUtil.get(url, null);
        ProductConfigResult result = JSON.parseObject(ret, ProductConfigResult.class);
        if(result!=null && "0".equals(result.getRetCode()) && result.getMessage() != null){
            config = result.getMessage();
        }
        return config;
    }

}
