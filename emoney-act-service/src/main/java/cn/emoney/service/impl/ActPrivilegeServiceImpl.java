package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.mapper.activity.Act588benifitrecordMapper;
import cn.emoney.mapper.activity.ActPrivilegeConfigMapper;
import cn.emoney.mapper.activity.ActPrivilegeRecordMapper;
import cn.emoney.pojo.Act_PrivilegeConfigDO;
import cn.emoney.pojo.Act_PrivilegeRecordDO;
import cn.emoney.pojo.PrivilegeClientInd;
import cn.emoney.service.ActPrivilegeSevice;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.date.DateTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-22
 */
@Slf4j
@Service
public class ActPrivilegeServiceImpl implements ActPrivilegeSevice {
    @Autowired(required = false)
    private ActPrivilegeRecordMapper actPrivilegeRecordMapper;

    @Autowired(required = false)
    private ActPrivilegeConfigMapper actPrivilegeConfigMapper;

    @Autowired
    private RedisService redisService;

    private static String cacheKey_userPrivilege = RedisConstants.RedisKey_Pre_Activity + "getUserPrivilegeRecord:";
    private static String cacheKey_privilegeConfig = RedisConstants.RedisKey_Pre_Activity + "getPrivilegeConfig:";
    private final static Long ExpriedTime = 60 * 60L;


    /**
     * 获取用户资金指标列表
     * <AUTHOR>
     * @date 2024/1/22 17:36
     * @param uid
     * @param actCode
     * @return java.util.List<cn.emoney.pojo.Act_PrivilegeConfigDO>
     */
    @Override
    public List<PrivilegeClientInd> getUserPrivilegeConfig(String uid, String actCode) {
        List<PrivilegeClientInd> privilegeClientIndList = new ArrayList<>();
        //1 获取用户领取记录
        Act_PrivilegeRecordDO recordDO = getUserPrivilegeRecord(uid, actCode);

        //2 获取资金指标配置列表
        List<Act_PrivilegeConfigDO> configDOList = getPrivilegeConfig(actCode);

        for (Act_PrivilegeConfigDO item :
                configDOList) {

            PrivilegeClientInd privilegeClientInd = new PrivilegeClientInd();
            privilegeClientInd.setName(item.getPrivilegeName());

            //3 指标可见
            if (item.getIsShow()) {
                //4 有领取记录-判断可用时间后标记能否显示
                if (recordDO != null && !StringUtils.isEmpty(recordDO.getUid())) {
                    LocalDateTime startTime = recordDO.getStartTime();
                    LocalDateTime nowTime = LocalDateTime.now();

                    //5 计算指标到期日
                    LocalDateTime newStartTime = startTime.plusDays(item.getValidDays());

                    if (newStartTime.isAfter(nowTime)) {
                        privilegeClientInd.setEnable("1");
                    } else {
                        privilegeClientInd.setEnable("0");
                    }
                } else {
                    //6 无领取记录-所有资金指标配置列表标记为不显示
                    privilegeClientInd.setEnable("0");
                }
            } else {
                //指标不可见
                privilegeClientInd.setEnable("0");
            }

            privilegeClientIndList.add(privilegeClientInd);
        }

        return privilegeClientIndList;
    }

    /**
     * 获取资金指标特权配置列表
     *
     * @param actCode
     * @return java.util.List<cn.emoney.pojo.Act_PrivilegeConfigDO>
     * <AUTHOR>
     * @date 2024/1/22 17:14
     */
    @Override
    public List<Act_PrivilegeConfigDO> getPrivilegeConfig(String actCode) {
        String key = cacheKey_privilegeConfig + actCode;
        List<Act_PrivilegeConfigDO> configDOList = redisService.getList(key, Act_PrivilegeConfigDO.class);
        if (configDOList == null || configDOList.size() == 0) {
            configDOList = actPrivilegeConfigMapper.selectByActCode(actCode);
            redisService.set(key, configDOList, ExpriedTime);
        }
        return configDOList;
    }

    /**
     * 获取用户领取记录
     *
     * @param uid
     * @param actCode
     * @return cn.emoney.pojo.Act_PrivilegeRecordDO
     * <AUTHOR>
     * @date 2024/1/22 16:59
     */
    @Override
    public Act_PrivilegeRecordDO getUserPrivilegeRecord(String uid, String actCode) {
        String key = cacheKey_userPrivilege + uid + ":" + actCode;

        Act_PrivilegeRecordDO privilegeRecordDO = redisService.get(key, Act_PrivilegeRecordDO.class);
        if (privilegeRecordDO == null) {
            privilegeRecordDO = refresh_UserPrivilegeRecord(uid, actCode);
        }

        return privilegeRecordDO;
    }

    /**
     * 刷新用户领取记录缓存
     * <AUTHOR>
     * @date 2024/1/22 17:40
     * @param uid
     * @param actCode
     * @return cn.emoney.pojo.Act_PrivilegeRecordDO
     */
    public Act_PrivilegeRecordDO refresh_UserPrivilegeRecord(String uid, String actCode){
        String key = cacheKey_userPrivilege + uid + ":" + actCode;
        Act_PrivilegeRecordDO privilegeRecordDO = new Act_PrivilegeRecordDO();
        List<Act_PrivilegeRecordDO> list = actPrivilegeRecordMapper.selectByUidAndActCode(uid, actCode);
        if (list != null && list.size() > 0) {
            privilegeRecordDO = list.get(0);
            redisService.set(key, privilegeRecordDO, ExpriedTime);
        }
        return privilegeRecordDO;
    }


    /**
     * 抢攻能记录保存
     *
     * @param uid
     * @param actCode
     * @param source  1:pc 2:app
     * @return int
     * <AUTHOR>
     * @date 2024/1/22 15:30
     */
    @Override
    public int AddUserPrivilegeRecord(String uid, String actCode, Integer source) {
        try {
            //已有领取记录返回1
            Act_PrivilegeRecordDO privilegeRecord = getUserPrivilegeRecord(uid, actCode);
            if (privilegeRecord != null && !StringUtils.isEmpty(privilegeRecord.getUid())) {
                return 1;
            }

            Act_PrivilegeRecordDO privilegeRecordDO = new Act_PrivilegeRecordDO();
            privilegeRecordDO.setUid(uid);
            privilegeRecordDO.setActCode(actCode);
            privilegeRecordDO.setStartTime(LocalDateTime.now());
            privilegeRecordDO.setCreateTime(LocalDateTime.now());
            privilegeRecordDO.setSource(source);
            int ret = actPrivilegeRecordMapper.insert(privilegeRecordDO);
            if (ret > 0) {
                refresh_UserPrivilegeRecord(uid, actCode);
            }
            return ret;
        } catch (Exception exp) {
            log.error("privilege/AddUserPrivilegeRecord error:" + exp);
            return 0;
        }
    }
}
