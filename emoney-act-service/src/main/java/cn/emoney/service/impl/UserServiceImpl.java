package cn.emoney.service.impl;

import cn.emoney.act.client.roboadvisor.RoboAdvisorUserClient;
import cn.emoney.act.client.user.UserClient;
import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.result.ResultInfo;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.common.utils.RedissonDistributionLock;
import cn.emoney.pojo.ActDsSignDetail;
import cn.emoney.pojo.AppApiResult;
import cn.emoney.pojo.UserPeriodInfoDO;
import cn.emoney.pojo.bo.BindAccountResposeDTO;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.MobileRegisterResultDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.*;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/14 11:56
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {
    private final UserClient userClient;
    private final RoboAdvisorUserClient advisorUserClient;
    private final RestTemplate restTemplate;

    @Value("${getAccountListApiUrl}")
    private String getAccountListApiUrl;

    @Value("${getAccountPIDApiUrl}")
    private String getAccountPIDApiUrl;

    @Value("${getLoginIdByNameApi}")
    private String getLoginIdByNameApi;

    @Value("${queryAccountListByAccountApiUrl}")
    private String queryAccountListByAccountApiUrl;

    @Value("${CheckPresentQSUrl:}")
    private String checkPresentQSUrl;

    @Value("${getUserPeriodUrl:}")
    private String getUserPeriodUrl;

    @Value("${UserFilterMatchsUrl:}")
    private String UserFilterMatchsUrl;

    @Autowired
    private RedissonDistributionLock redissonDistributionLock;

    private Integer expirdTime = 2 * 60;

    @Autowired
    private RedisService redisService;

    @Autowired
    private LogisticsService logisticsService;

    private String Rediskey_UnifiedState = RedisConstants.Redis_Pre_Activity + "UnifiedState:";
    private String Rediskey_UnifiedState_count = RedisConstants.Redis_Pre_Activity + "UnifiedState:count:";
    private String CacheKey_SendPrivilegeLock = RedisConstants.Redis_Pre_Activity + "SendPrivilege";
    private String redisKey_CheckPresent_QS = RedisConstants.Redis_Pre_Activity + "CheckPresent_QS";
    private String redisKey_UserPeriodInfo = RedisConstants.Redis_Pre_Activity + "UserPeriodInfo";

    public UserServiceImpl(UserClient userClient,
                           RoboAdvisorUserClient advisorUserClient,
                           RestTemplate restTemplate) {
        this.userClient = userClient;
        this.advisorUserClient = advisorUserClient;
        this.restTemplate = restTemplate;
    }

    /**
     * 获取用户的绑定账户列表
     *
     * @param uid
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.dto.BindAccountDTO>
     * <AUTHOR>
     * @date 2021/12/14 11:42
     */
    @Override
    public List<BindAccountVO> GetBindAccountList(String uid) {
        String key = RedisConstants.Redis_Pre_Activity + "GetBindAccountList1." + uid;

        List<BindAccountVO> bindAccountList = redisService.getList(key, BindAccountVO.class);
        if (bindAccountList != null) {
            return bindAccountList;
        }

        String res = OkHttpUtil.get(MessageFormat.format(getAccountListApiUrl, uid), null);
        ApiGateWayResult<String> apiGateWayResult = JsonUtil.toBean(res, ApiGateWayResult.class);
        if (StringUtils.hasText(apiGateWayResult.Message)) {
            BindAccountResposeDTO resposeDTO = JsonUtil.toBean(apiGateWayResult.Message, BindAccountResposeDTO.class);
            if (resposeDTO != null && resposeDTO.getMessage() != null && !resposeDTO.getMessage().isEmpty()) {
                redisService.set(key, resposeDTO.getMessage(), expirdTime.longValue());
                return resposeDTO.getMessage();
            }
        }
        return Collections.emptyList();
    }

    /**
     * 根据uid和pid获取绑定用户账号(em 手机密文)
     * 未指定pid则获取第一个绑定的em号
     * <AUTHOR>
     * @date 2023/5/22 14:06
     * @param uid
     * @param requestPid
     * @return cn.emoney.pojo.vo.LoginUserInfoVO
     */
    @Override
    public LoginUserInfoVO getBoundUserInfo(String uid,String requestPid) {
        LoginUserInfoVO loginUserInfoVO = null;

        List<BindAccountVO> bindAccountList = GetBindAccountList(uid);
        if (bindAccountList != null) {
            loginUserInfoVO = new LoginUserInfoVO();
            loginUserInfoVO.setUid(uid);
            for (BindAccountVO item : bindAccountList) {
                if (item.AccountType.equals(0)) {
                    String pid = GetAccountPID(item.AccountName);
                    if (pid.indexOf("888") > -1) {
                        if (!StringUtils.isEmpty(requestPid)) {
                            if (pid != null && pid.equals(requestPid)) {
                                loginUserInfoVO.setAccount(item.AccountName);
                                loginUserInfoVO.setPid(requestPid);
                            }
                        } else {
                            loginUserInfoVO.setAccount(item.AccountName);
                            loginUserInfoVO.setPid(pid);
                        }
                    }
                }

                if (item.AccountType.equals(1)) {
                    loginUserInfoVO.setMobileX(item.EncryptMobile);
                    loginUserInfoVO.setMaskMobile(item.AccountName);
                }
            }
        }

        return loginUserInfoVO;
    }

    /**
     * 根据用户名获取pid
     *
     * @param username
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/12/14 15:24
     */
    @Override
    public String GetAccountPID(String username) {
        String key = RedisConstants.Redis_Pre_Activity + "GetAccountPID." + username;
        String val = redisService.get(key, String.class);
        if (val == null) {
            String res = OkHttpUtil.get(MessageFormat.format(getAccountPIDApiUrl, username), null);
            ApiGateWayResult<String> apiGateWayResult = JsonUtil.toBean(res, ApiGateWayResult.class);
            if (apiGateWayResult.Message != null && !apiGateWayResult.Message.isEmpty()) {
                val = apiGateWayResult.getMessage();
                redisService.set(key, val, expirdTime.longValue());
            }
        }
        return val;
    }


    /**
     * 获取账号LoginID
     *
     * @param username
     * @return cn.emoney.activityweb.repository.dao.entity.dto.UserLoginIDInfo
     * <AUTHOR>
     * @date 2021/12/16 14:29
     */
    @Override
    public UserLoginIdInfoVO GetLoginIDInfoByAccount(String username) {
        String key = RedisConstants.Redis_Pre_Activity + "GetLoginIDByAccount:" + username;

        UserLoginIdInfoVO val = redisService.get(key, UserLoginIdInfoVO.class);

        if (val == null || val.PID == null) {
            String res = OkHttpUtil.get(MessageFormat.format(getLoginIdByNameApi, username), null);
            ApiGateWayResult<String> apiGateWayResult = JsonUtil.toBean(res, ApiGateWayResult.class);
            if (apiGateWayResult != null) {
                ApiGateWayResult<UserLoginIdInfoVO> result = JsonUtil.toBean(apiGateWayResult.getMessage(), ApiGateWayResult.class);
                if (result != null && result.getMessage() != null) {
                    val = JSON.parseObject(JSON.toJSONString(result.getMessage()), UserLoginIdInfoVO.class);
                    if (val != null && val.PID != null) {
                        redisService.set(key, val, expirdTime.longValue());
                    }
                }
            }
        }

        return val;
    }

    /**
     * 根据账号获取用户所有账号列表
     *
     * @param account
     * @return java.util.List<cn.emoney.pojo.vo.AccountVO>
     * <AUTHOR>
     * @date 2022/1/17 10:06
     */
    @Override
    public List<AccountVO> queryAccountListByAccount(String account) {
        String key = RedisConstants.Redis_Pre_Activity + "queryAccountListByAccount:" + account;
        List<AccountVO> val = redisService.getList(key, AccountVO.class);
        if (val != null) {
            return val;
        }
        AccountListResponse response = restTemplate.getForObject(queryAccountListByAccountApiUrl, AccountListResponse.class, account);
        if (response != null && response.getMessage() != null) {
            redisService.set(key, response.getMessage(), expirdTime.longValue());
            return response.getMessage();
        }
        return Collections.emptyList();
    }

    private static class AccountListResponse extends ResultInfo<List<AccountVO>> {
    }

    /**
     * 获取用户过期前最高版本
     * <AUTHOR>
     * @date 2023/10/13 15:53
     * @param account
     * @return java.lang.String
     */
    @Override
    public String getHighPIDByAccount(String account){
        List<AccountVO> emList = queryAccountListByAccount(account);
        String highPID = "";
        for (AccountVO item : emList) {
            String getPid = item.getPid().toString();

            if (getPid.indexOf("888") > -1) {
                highPID = getPid;
                break;
            } else {
                if (item.getPid() > *********) {
                    highPID = getPid;
                }
            }
        }
        return highPID;
    }

    @Override
    public List<AccountVO> listAccountByUid(String uid) {
        return GetBindAccountList(uid).stream()
                .filter(x -> x.getAccountType() == 0)
                .findFirst()
                .map(x -> queryAccountListByAccount(x.getAccountName()))
                .orElse(Collections.emptyList());
    }

    /**
     * 活动状态统一方法[倒计名额][是否参加过此活动]等
     *
     * @param actCode
     * @param uid
     * @param value
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/1/9 14:38
     */
    @Override
    public String AddCountByActCode(String actCode, String uid, String value) {
        String rediskey_hash = Rediskey_UnifiedState + actCode;
        String rediskey = Rediskey_UnifiedState_count + actCode;
        Object obj = redisService.hashGet(rediskey_hash, uid);

        if (obj == null) {
            redisService.hashSet(rediskey_hash, uid, value);
            redisService.incrBy(rediskey);
        } else {
            if (!obj.toString().equals(value)) {
                redisService.hashSet(rediskey_hash, uid, value);
            }
            return "1";
        }

        return "0";
    }

    /**
     * 增加数量
     *
     * @param actCode
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/1/16 14:32
     */
    @Override
    public String AddUsedCount(String actCode) {
        String rediskey = Rediskey_UnifiedState_count + actCode;
        Long count = redisService.incrBy(rediskey);
        return count.toString();
    }

    /**
     * 减少数量
     *
     * @param actCode
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/1/16 14:32
     */
    @Override
    public String DecrUsedCount(String actCode) {
        String rediskey = Rediskey_UnifiedState_count + actCode;
        Long count = redisService.decrBy(rediskey);
        return count.toString();
    }

    /**
     * 查询是否参加过此活动
     *
     * @param actCodes(actcode1,actcode2)
     * @param uid
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/1/9 15:00
     */
    @Override
    public String IsSubmitByActCodes(String actCodes, String uid) {
        String retcount = "";
        for (String actCode :
                actCodes.split(",")) {

            String rediskey = Rediskey_UnifiedState + actCode;
            Object obj = redisService.hashGet(rediskey, uid);
            String value = "";
            if (obj != null) {
                value = obj.toString();
            }
            retcount += value + ",";
        }

        return retcount;
    }

    /**
     * 获取该活动已参加的名额
     *
     * @param actCodes
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/1/16 14:07
     */
    @Override
    public String GetCountByActCodes(String actCodes) {
        String retcount = "";
        for (String actCode :
                actCodes.split(",")) {

            String rediskey = Rediskey_UnifiedState_count + actCode;
            Object obj = redisService.get(rediskey);
            String value = "";
            if (obj != null) {
                value = obj.toString();
            }
            retcount += value + ",";
        }

        return retcount;
    }

    /**
     * 延期/赠送特权
     *
     * @param loginUser
     * @param activityID
     * @param reason
     * @param accountType
     * @return boolean
     * <AUTHOR>
     * @date 2023/3/24 18:01
     */
    @Override
    public Result<String> sendPrivilege_New(LoginUserInfoVO loginUser, String activityID, String reason, Integer accountType, String actCode) {
        if (loginUser == null) {
            return Result.buildErrorResult("未登录");
        }

        String lockKey = CacheKey_SendPrivilegeLock + activityID + loginUser.getUid();
        try {
            if (redissonDistributionLock.tryLock(lockKey, TimeUnit.SECONDS, 30)) {
                //重复领取判断
                String isSubmit = IsSubmitByActCodes(actCode, loginUser.getUid());
                isSubmit = (isSubmit != null && isSubmit.length() > 0) ? isSubmit.substring(0, isSubmit.length() - 1) : "";
                if (!StringUtils.isEmpty(isSubmit)) {
                    return Result.buildErrorResult("已领取过，请勿重复领取");
                }

                List<BindAccountVO> list = GetBindAccountList(loginUser.getUid());
                if (list == null) {
                    return Result.buildErrorResult("未绑定手机号");
                }

                for (BindAccountVO item : list) {
                    if (item.AccountType.equals(0)) {
                        String pid = GetAccountPID(item.AccountName);
                        if (pid != null && pid.equals(loginUser.getPid())) {
                            loginUser.account = item.AccountName;
                        }
                    }
                    if (item.AccountType.equals(1)) {
                        loginUser.mobileX = item.EncryptMobile;
                        loginUser.maskMobile = item.AccountName;
                    }
                }

                if (loginUser.mobileX == null) {
                    return Result.buildErrorResult("未绑定手机号");
                }
                if (loginUser.account == null) {
                    return Result.buildErrorResult("em账号查询异常");
                }

                SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
                sendPrivilegeDTO.setAppId("A009");
                sendPrivilegeDTO.setActivityID(activityID);
                sendPrivilegeDTO.setReason(reason);
                sendPrivilegeDTO.setApplyUserID("scb_public");
                List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
                CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();

                //2:手机号 1：em号  领取特权指定2  延期指定1
                createActivityGrantApplyAccountDTO.setAccountType(accountType);
                createActivityGrantApplyAccountDTO.setAccount(loginUser.getAccount());
                createActivityGrantApplyAccountDTO.setMID(loginUser.getMobileX());
                createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
                sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

                Result<String> ret = logisticsService.sendPrivilegeResult(sendPrivilegeDTO);
                if (ret.isSuccess()) {
                    //领取成功记录领取状态
                    AddCountByActCode(actCode, loginUser.getUid(), String.valueOf(System.currentTimeMillis()));
                    return Result.buildSuccessResult();
                } else {
                    return Result.buildErrorResult(ret.getMsg());
                }
            } else {
                return Result.buildErrorResult("赠送失败");
            }
        } finally {
            redissonDistributionLock.unlock(lockKey);
        }
    }

    @Override
    public MobileRegisterResultDTO registerByMobile(String mobile, String sid, String tid, String hardwareInfo, String clientVersion) {
        return advisorUserClient.regByMobile(mobile, sid, tid, hardwareInfo, clientVersion);
    }

    @Override
    public ResultInfo<ValidateUserVO> validateUserPasswordByPID(String username, String password, String pid) {
        return userClient.validateUserPasswordByPID(username, password, pid);
    }

    /**
     * 检查是否券商开户
     * <AUTHOR>
     * @date 2023/4/23 9:58
     * @param mobileX
     * @return boolean
     */
    @Override
    public boolean checkPresent_QS(String mobileX){
        String redisKey = redisKey_CheckPresent_QS + mobileX;
        Object obj = redisService.get(redisKey);
        if(obj!=null && obj.toString().equals("1")) {
            return true;
        }

        String res = OkHttpUtil.get(MessageFormat.format(checkPresentQSUrl, mobileX), null);
        ApiGateWayResult<String> apiGateWayResult = JsonUtil.toBean(res, ApiGateWayResult.class);
        if (apiGateWayResult.Message != null && !apiGateWayResult.Message.isEmpty()) {

            CheckPresentVO checkPresentVO = JsonUtil.toBean(apiGateWayResult.Message, CheckPresentVO.class);
            if(checkPresentVO!=null &&  checkPresentVO.Flag!=null && checkPresentVO.Flag.equals("1")){
                redisService.set(redisKey,"1",expirdTime.longValue());
                return true;
            }
        }
        return false;
    }

    /**
     * 获取用户软件周期
     * <AUTHOR>
     * @date 2024/4/11 14:39
     * @param uid
     * @return cn.emoney.pojo.UserPeriodInfoDO.Software
     */
    @Override
    public UserPeriodInfoDO.Software getUserPeriodInfo(String uid) {
        String redisKey = redisKey_UserPeriodInfo + uid;
        UserPeriodInfoDO.Software retObj = redisService.get(redisKey, UserPeriodInfoDO.Software.class);

        if (retObj == null) {
            String res = OkHttpUtil.get(MessageFormat.format(getUserPeriodUrl, uid), null);
            if (!StringUtils.isEmpty(res)) {

                UserPeriodInfoDO userPeriodInfoDO = JsonUtil.toBean(res, UserPeriodInfoDO.class);
                if (userPeriodInfoDO != null) {

                    if (userPeriodInfoDO.code == 1 && userPeriodInfoDO.data != null) {
                        String software = userPeriodInfoDO.data.software.toString();
                        if (!StringUtils.isEmpty(software)) {

                            retObj = JsonUtil.toBean(software, UserPeriodInfoDO.Software.class);
                            redisService.set(redisKey, retObj, 1L, TimeUnit.DAYS);
                        }
                    }

                }
            }
        }
        return retObj;
    }

    /**
     * 验证用户是否在过滤中
     * <AUTHOR>
     * @date 2025/4/23 14:49
     * @param uid
     * @param filterIds
     * @return boolean
     */
    public Map<String, Boolean> checkUserFilterMatchs(String uid, String filterIds) {
        String res = OkHttpUtil.get(MessageFormat.format(UserFilterMatchsUrl,filterIds, uid), null);
        if (!StringUtils.isEmpty(res)) {
            AppApiResult<Boolean[]> appApiResult = JSON.parseObject(res, new TypeReference<AppApiResult<Boolean[]>>() {
            });
            Boolean[] retData = appApiResult.getDetail();
            String[] filterIdArray = filterIds.split(",");

            Map<String, Boolean> filterMap = new HashMap<>();
            for (int i = 0; i < filterIdArray.length; i++) {
                String filterId = filterIdArray[i];
                Boolean isMatch = retData[i];

                filterMap.put(filterId,isMatch);
            }

            return filterMap;
        }
        return null;
    }

}
