package cn.emoney.service.impl;

import cn.emoney.common.utils.StreamUtils;
import cn.emoney.mapper.gensee.GenseeMapper;
import cn.emoney.pojo.CourseWatchDO;
import cn.emoney.pojo.GenseeLiveBasicDO;
import cn.emoney.pojo.GenseeRecordedBasicDO;
import cn.emoney.pojo.bo.CourseWatchDetailDTO;
import cn.emoney.service.GenseeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

@Slf4j
@Service
public class GenseeServiceImpl implements GenseeService {
    private final GenseeMapper mapper;

    public GenseeServiceImpl(@Autowired(required = false) GenseeMapper mapper) {
        this.mapper = mapper;
        if (mapper == null) {
            log.warn("Can't found Gensee Datasource");
        }
    }

    @Override
    @Cacheable("gensee:video:recorded")
    public Optional<GenseeRecordedBasicDO> getRecordedBasic(String lbId) {
        return mapper.getRecordedBasic(lbId);
    }

    @Override
    @Cacheable("gensee:video:live")
    public Optional<GenseeLiveBasicDO> getLiveBasic(String webCastId) {
        return mapper.getLiveBasic(webCastId);
    }

    @Override
    public Stream<List<CourseWatchDO>> streamLive(String vId, Duration duration,
                                                  Integer offsetId, Integer batchSize) {
        return StreamUtils.<List<CourseWatchDO>, Integer>page(
                (offset) -> mapper.getUserByLiveWatchTimeSumGteByOffset(vId, duration.getSeconds(), offset, batchSize),
                (result) -> result.stream().map(CourseWatchDO::getId).max(Integer::compareTo),
                offsetId, batchSize);
    }

    @Override
    public Stream<List<CourseWatchDO>> streamReplay(String vId, Duration duration,
                                                    LocalDateTime inTimeHead, LocalDateTime inTimeTail,
                                                    Integer offsetId, Integer batchSize) {
        return StreamUtils.<List<CourseWatchDO>, Integer>page(
                (offset) -> mapper.getUserByRecordedWatchDurationGteAndStartTimeGteLte(vId, duration.getSeconds(), inTimeHead, inTimeTail, offset, batchSize),
                (result) -> result.stream().map(CourseWatchDO::getId).max(Integer::compareTo),
                offsetId, batchSize);
    }

    @Override
    public List<CourseWatchDetailDTO> getLiveWatchByUidAndVid(Long uid, String vid) {
        return mapper.getLiveWatchByUidAndVid(uid, vid);
    }

    @Override
    public List<CourseWatchDetailDTO> getRecordedWatchByUidAndVid(Long uid, String vid) {
        return mapper.getRecordedWatchByUidAndVid(uid, vid);
    }

    @Override
    public List<CourseWatchDO> getUserByLiveWatchTimeSumGte(String vid, Duration duration, Integer offset, Integer limit) {
        return mapper.getUserByLiveWatchTimeSumGteByOffset(vid, duration.getSeconds(), offset, limit);
    }

    @Override
    public List<CourseWatchDO> getUserByRecordedWatchDurationGteAndStartTimeLte(String vid, Duration duration, LocalDateTime startTime, Integer offset, Integer limit) {
        return mapper.getUserByRecordedWatchDurationGteAndStartTimeLte(vid, duration.getSeconds(), startTime, offset, limit);
    }
}