package cn.emoney.service.impl;

import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.service.SsoAuthService;
import cn.emoney.service.config.SSOAuthProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.web.util.UriUtils;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

@Service
@EnableConfigurationProperties(SSOAuthProperties.class)
public class SsoAuthServiceImpl implements SsoAuthService {
    private final SSOAuthProperties properties;
    private final RestTemplate restTemplate;

    public SsoAuthServiceImpl(SSOAuthProperties properties, RestTemplate restTemplate) {
        this.properties = properties;
        this.restTemplate = restTemplate;
    }

    @Override
    @Cacheable(cacheNames = "act:auth:sso", key = "#token")
    public Optional<SSOResult> auth(String token) {
        token = UriUtils.encode(token, StandardCharsets.UTF_8);
        URI uri = UriComponentsBuilder.fromUriString(properties.getUrl())
                .queryParam("token", token)
                .build(true).toUri();
        SSOResult result = restTemplate.getForObject(uri, SSOResult.class);
        return Optional.ofNullable(result)
                .filter(r -> StringUtils.hasText(r.getUid()));
    }
}
