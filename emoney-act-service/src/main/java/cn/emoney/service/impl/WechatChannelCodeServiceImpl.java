package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.mapper.transfer.WechatChannelCodeMapper;
import cn.emoney.pojo.TbSyncOaUserInfoDO;
import cn.emoney.pojo.WechatChannelCodeDO;
import cn.emoney.service.WechatChannelCodeService;
import cn.emoney.service.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @date 2022-02-15
 */
@Service
@Slf4j
public class WechatChannelCodeServiceImpl implements WechatChannelCodeService {

    @Autowired
    private WechatChannelCodeMapper WechatChannelCodeMapper;

    @Autowired
    private RedisService redisService;

    /**
     * 根据sid获取二维码
     * <AUTHOR>
     * @date 2022/2/15 17:22
     * @param sid
     * @return cn.emoney.pojo.WechatChannelCodeDO
     */
    @Override
    public WechatChannelCodeDO queryBySid(String sid) {
        WechatChannelCodeDO wechatChannelCodeDO = redisService.get(MessageFormat.format(RedisConstants.REDISKEY_WechatChannelCode_queryBySid, sid), WechatChannelCodeDO.class);
        if (wechatChannelCodeDO == null) {
            wechatChannelCodeDO = WechatChannelCodeMapper.queryBySid(sid);
            if (wechatChannelCodeDO != null) {
                redisService.set(MessageFormat.format(RedisConstants.REDISKEY_WechatChannelCode_queryBySid, sid), wechatChannelCodeDO, (long) 60 * 60);
            }
        }
        return wechatChannelCodeDO;
    }
}
