package cn.emoney.service.impl;

import cn.emoney.act.client.video.VideoProgressClient;
import cn.emoney.act.client.video.dto.VideoProgressDTO;
import cn.emoney.common.exception.BaseRuntimeException;
import cn.emoney.common.result.MobileResultDTO;
import cn.emoney.common.utils.GenseeUtils;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.bo.CourseDetailDTO;
import cn.emoney.pojo.bo.videoProgressRequest;
import cn.emoney.service.CourseService;
import cn.emoney.service.VideoProgressService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Streams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Optionals;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-09-05
 */
@Slf4j
@Service
@Deprecated
public class VideoProgressImpl implements VideoProgressService {
    private final CourseService courseService;
    private final VideoProgressClient client;

    @Value("${getVideoProgressUrl}")
    private String getVideoProgressUrl;

    @Value("${saveVideoProgressUrl}")
    private String saveVideoProgressUrl;

    public VideoProgressImpl(CourseService courseService,
                             VideoProgressClient client) {
        this.courseService = courseService;
        this.client = client;
    }

    /**
     * 获取视频播放进度
     *
     * @param uid
     * @param videoId
     * @return long
     * <AUTHOR>
     * @date 2022/9/5 11:00
     */
    @Override
    public MobileResultDTO<Long> getVideoProgress(String uid, String videoId) {
        String res = OkHttpUtil.get(MessageFormat.format(getVideoProgressUrl, uid, videoId), null);
        if (!StringUtils.isEmpty(res)) {
            return JsonUtil.toBean(res, VideoProgressResponse.class);
        }
        return null;
    }

    /**
     * 保存视频进度
     *
     * @param request
     * @return long
     * <AUTHOR>
     * @date 2022/9/5 13:29
     */
    @Override
    public MobileResultDTO<Long> saveVideoProgress(videoProgressRequest request) {
        String res = OkHttpUtil.postJsonParams(saveVideoProgressUrl, JSON.toJSONString(request));
        if (!StringUtils.isEmpty(res)) {
            return JsonUtil.toBean(res, VideoProgressResponse.class);
        }
        return null;
    }

    /**
     * 临时用移动端风格 API 返回结果
     */
    static class VideoProgressResponse extends MobileResultDTO<Long> {
    }

    /**
     * 获取视频播放时长
     *
     * @param uid
     * @param videoId
     * @return long
     * <AUTHOR>
     * @date 2022/9/5 11:00
     */
    @Override
    public Optional<Long> getVideoPlayTime(Long uid, String videoId) {
        MobileResultDTO<Long> result = client.getGlobalPlayTime(uid, videoId);
        return Optional.ofNullable(result).map(MobileResultDTO::getDetail);
    }

    @Override
    public Optional<Map<String, VideoProgressDTO>> getVideoPlayDetail(Long uid, List<String> videoId) {
        if (videoId.isEmpty()) {
            return Optional.empty();
        }
        MobileResultDTO<Map<String, VideoProgressDTO>> result = client.getUserPlayProgress(uid, videoId);
        return Optional.ofNullable(result).map(MobileResultDTO::getDetail);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<VideoProgressDTO> getCoursePlayDetail(Long uid, List<Integer> courseId) {
        if (courseId.isEmpty()) {
            return Collections.emptyList();
        }

        CompletableFuture<Optional<CourseDetailDTO>>[] futures = (CompletableFuture<Optional<CourseDetailDTO>>[])
                courseId.stream().map(courseService::asyncDetail).toArray(CompletableFuture[]::new);
        CompletableFuture.allOf(futures).join();

        List<String> videoIds = Streams.zip(courseId.stream(), Arrays.stream(futures),
                (id, future) -> {
                    try {
                        return future.get().flatMap(course ->
                                Optionals.firstNonEmpty(
                                        () -> GenseeUtils.findWebCastId(course.getCoursePlayBackUrl()),
                                        () -> GenseeUtils.findWebCastId(course.getCourseLiveUrl())
                                )
                        ).orElse(null);
                    } catch (InterruptedException | ExecutionException e) {
                        log.error("获取课程详情失败, id: {}", id, e);
                    }
                    return null;
                }).collect(Collectors.toList());

        List<String> checkList = videoIds.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (checkList.isEmpty()) {
            return new AbstractList<VideoProgressDTO>() {
                @Override
                public VideoProgressDTO get(int index) {
                    return null;
                }

                @Override
                public int size() {
                    return courseId.size();
                }
            };
        }
        Map<String, VideoProgressDTO> result = getVideoPlayDetail(uid, checkList).orElseThrow(() -> new BaseRuntimeException("获取视频播放进度失败"));
        return videoIds.stream()
                .map(id -> id == null ? null : result.get(id))
                .collect(Collectors.toList());
    }
}
