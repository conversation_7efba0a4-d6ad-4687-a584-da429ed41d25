package cn.emoney.service.impl;

import cn.emoney.pojo.WjxSurveyResultDO;
import cn.emoney.pojo.vo.survey.QuestionAnswerVO;
import cn.emoney.pojo.vo.survey.SurveyDisplayVO;
import cn.emoney.service.SurveyAnswerParseService;
import cn.emoney.service.SurveyService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 问卷答案解析服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/11
 */
@Service
@Slf4j
public class SurveyAnswerParseServiceImpl implements SurveyAnswerParseService {

    @Autowired
    private SurveyService surveyService;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public SurveyDisplayVO parseSurveyAnswer(WjxSurveyResultDO surveyResult) {
        return parseSurveyAnswer(surveyResult, null);
    }

    @Override
    public SurveyDisplayVO parseSurveyAnswer(WjxSurveyResultDO surveyResult, String template) {
        SurveyDisplayVO displayVO = new SurveyDisplayVO();

        try {
            // 1. 设置基本信息
            setBasicInfo(displayVO, surveyResult);

            // 2. 获取问卷模板（如果没有提供）
            if (template == null && surveyResult.getActivity() != null) {
                try {
                    template = surveyService.getWJXActivitytemplate(surveyResult.getActivity());
                } catch (Exception e) {
                    log.warn("获取问卷模板失败，使用默认解析: {}", e.getMessage());
                }
            }

            // 3. 解析答案
            List<QuestionAnswerVO> answers = parseAnswers(surveyResult, template);
            displayVO.setAnswers(answers);

            displayVO.setStatus("success");
            displayVO.setMessage("问卷提交成功！");

        } catch (Exception e) {
            log.error("解析问卷答案失败", e);
            displayVO.setStatus("error");
            displayVO.setMessage("解析问卷数据时发生错误：" + e.getMessage());
        }

        return displayVO;
    }

    /**
     * 设置基本信息
     */
    private void setBasicInfo(SurveyDisplayVO displayVO, WjxSurveyResultDO surveyResult) {
        displayVO.setSurveyName(surveyResult.getName());
        displayVO.setUserName(surveyResult.getRealname());
        displayVO.setNickName(surveyResult.getNickname());
        displayVO.setIpAddress(surveyResult.getIpaddress());
        displayVO.setTotalValue(surveyResult.getTotalvalue());
        displayVO.setTimeTaken(surveyResult.getTimetaken());
        displayVO.setProvince(surveyResult.getProvince());
        displayVO.setCity(surveyResult.getCity());
        displayVO.setSource(surveyResult.getSource());
        displayVO.setActivityId(surveyResult.getActivity());
        displayVO.setJoinId(surveyResult.getJoinid());

        // 格式化提交时间
        if (surveyResult.getSubmittime() != null) {
            displayVO.setSubmitTime(dateFormat.format(surveyResult.getSubmittime()));
        }
    }

    /**
     * 解析答案数据
     */
    private List<QuestionAnswerVO> parseAnswers(WjxSurveyResultDO surveyResult, String template) {
        List<QuestionAnswerVO> answers = new ArrayList<>();

        try {
            String answerJson = surveyResult.getAnswer();
            if (StringUtils.isEmpty(answerJson)) {
                log.warn("答案数据为空，joinId: {}", surveyResult.getJoinid());
                return answers;
            }

            // 解析answer字段的JSON数据
            Map<String, String> answerMap = JSON.parseObject(answerJson,
                    new TypeReference<Map<String, String>>() {});

            if (answerMap == null || answerMap.isEmpty()) {
                log.warn("答案数据解析为空，joinId: {}", surveyResult.getJoinid());
                return answers;
            }

            // 解析每个答案
            for (Map.Entry<String, String> entry : answerMap.entrySet()) {
                QuestionAnswerVO answerVO = new QuestionAnswerVO();
                answerVO.setQuestionNo(entry.getKey());
                answerVO.setAnswerText(entry.getValue());

                // 尝试从模板获取题目文本
                String questionText = extractQuestionText(template, entry.getKey());
                if (StringUtils.isEmpty(questionText)) {
                    questionText = "题目 " + answerVO.getDisplayQuestionNo();
                }
                answerVO.setQuestionText(questionText);

                // 格式化答案文本
                String formattedAnswer = formatAnswerText(entry.getValue(), null);
                answerVO.setFormattedAnswerText(formattedAnswer);

                // 设置题目序号（用于排序）
                try {
                    if (entry.getKey().startsWith("q")) {
                        int order = Integer.parseInt(entry.getKey().substring(1));
                        answerVO.setQuestionOrder(order);
                    }
                } catch (NumberFormatException e) {
                    answerVO.setQuestionOrder(999); // 无法解析的放到最后
                }

                answers.add(answerVO);
            }

            // 按题目序号排序
            answers = answers.stream()
                    .sorted(Comparator.comparing(QuestionAnswerVO::getQuestionOrder))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("解析答案数据失败，joinId: {}", surveyResult.getJoinid(), e);
        }

        return answers;
    }

    @Override
    public String formatAnswerText(String answerText, String questionType) {
        if (StringUtils.isEmpty(answerText)) {
            return "未填写";
        }

        // 处理多选答案（逗号分隔）
        if (answerText.contains(",")) {
            String[] answers = answerText.split(",");
            List<String> formattedAnswers = new ArrayList<>();
            for (String answer : answers) {
                String trimmed = answer.trim();
                if (StringUtils.isNotEmpty(trimmed)) {
                    // 处理带^符号的答案（问卷星格式：选项^填空内容）
                    if (trimmed.contains("^")) {
                        String[] parts = trimmed.split("\\^");
                        if (parts.length > 1) {
                            formattedAnswers.add(parts[0] + "（" + parts[1] + "）");
                        } else {
                            formattedAnswers.add(parts[0]);
                        }
                    } else {
                        formattedAnswers.add(trimmed);
                    }
                }
            }
            return String.join("、", formattedAnswers);
        }

        // 处理单个答案
        if (answerText.contains("^")) {
            String[] parts = answerText.split("\\^");
            if (parts.length > 1) {
                return parts[0] + "（" + parts[1] + "）";
            }
        }

        return answerText.trim();
    }

    @Override
    public String extractQuestionText(String template, String questionNo) {
        if (StringUtils.isEmpty(template) || StringUtils.isEmpty(questionNo)) {
            return null;
        }

        try {
            // 这里可以根据实际的问卷模板格式来解析题目文本
            // 由于问卷星的模板格式比较复杂，这里提供一个基础实现
            // 实际使用时可能需要根据具体的模板格式进行调整

            // 简单的JSON解析尝试
            Map<String, Object> templateMap = JSON.parseObject(template, Map.class);
            if (templateMap != null && templateMap.containsKey("questions")) {
                // 假设模板中有questions字段
                // 具体实现需要根据实际的模板结构调整
            }

        } catch (Exception e) {
            log.debug("从模板提取题目文本失败，questionNo: {}, error: {}", questionNo, e.getMessage());
        }

        return null;
    }
}
