package cn.emoney.service.impl;

import cn.emoney.common.result.SCMResult;
import cn.emoney.pojo.SCMOrderDTO;
import cn.emoney.pojo.SCMOrderQueryDTO;
import cn.emoney.service.SCMOrderService;
import cn.emoney.service.config.SCMOrderProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;

@Service
@EnableConfigurationProperties(SCMOrderProperties.class)
public class SCMOrderServiceImpl implements SCMOrderService {
    private final SCMOrderProperties properties;
    private final RestTemplate restTemplate;
    private final ObjectMapper mapper;

    public SCMOrderServiceImpl(SCMOrderProperties properties, RestTemplate restTemplate, ObjectMapper mapper) {
        this.properties = properties;
        this.restTemplate = restTemplate;
        this.mapper = mapper;
    }

    @Override
    public List<SCMOrderDTO> listOrderByEntity(SCMOrderQueryDTO queryDTO) {
        try {
            SCMOrderList orderList = restTemplate.getForObject(properties.getListOrderProd(),
                    SCMOrderList.class,
                    mapper.writeValueAsString(queryDTO));
            if (orderList != null && orderList.getData() != null) {
                return orderList.getData();
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return Collections.emptyList();
    }


    private static class SCMOrderList extends SCMResult<List<SCMOrderDTO>> {
    }
}
