package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.DateFormatUtil;
import cn.emoney.common.utils.RedissonDistributionLock;
import cn.emoney.mapper.activity.LotteryDetailMapper;
import cn.emoney.mapper.activity.LotteryPrizelimitMapper;
import cn.emoney.mapper.activity.LotteryWhitelistMapper;
import cn.emoney.pojo.*;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.OrderProdListDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.pojo.vo.LotteryInfoVO;
import cn.emoney.pojo.vo.OrderProdListVO;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.LotteryService;
import cn.emoney.service.MobileService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/05/19 19:37
 **/
@Service
@Slf4j
public class LotteryServiceImpl implements LotteryService {

    @Value("$logisticsQueryOrderProdListURL}")
    private String logisticsQueryOrderProdListURL;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserService userService;

    @Autowired
    private LotteryWhitelistMapper lotteryWhitelistMapper;

    @Autowired
    private LotteryDetailMapper lotteryDetailMapper;

    @Autowired
    private LotteryPrizelimitMapper lotteryPrizelimitMapper;

    @Autowired
    private MobileService mobileService;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;

    @Autowired
    private RedissonDistributionLock redissonDistributionLock;

    private final String rediskey_mylotteryinfo= RedisConstants.Redis_Pre_Activity + "lottery:mylotteryinfo:";
    private final String CacheKey_PrizesSet =  RedisConstants.RedisKey_Pre_Activity + "PrizesSet";
    private final String CacheKey_LogisticOrderDetail = RedisConstants.RedisKey_Pre_Activity + "LogisticOrderDetail:";
    private final String CacheKey_PrizesDetail = RedisConstants.RedisKey_Pre_Activity + "PrizesDetail:";
    private final String CacheKey_LatestPrizesDetailList = RedisConstants.RedisKey_Pre_Activity + "LatestPrizesDetailList:";
    private final String CacheKey_DoLotteryLock = RedisConstants.RedisKey_Pre_Activity + "DoLotteryLock";

    // 设置奖品编号数组长度
    private final static Integer ARRAY_SIZE = 100;
    // 设置活动批次号
    private final static String batchNo20221201 = "20221201";
    // 初始化奖品编号数组
    private static Integer[] prizesArray = new Integer[ARRAY_SIZE];
    static{
        for(int i = 0;i<ARRAY_SIZE;i++){
            prizesArray[i] = i+1;
        }
    }

    /**
     * 获取是否有权限抽奖
     * @param uid
     * @param pid
     * @return
     */
    @Override
    public Result<String> getPrivilege1201(String uid,String pid){
        Result<String> result = getLotteryPrivilege(uid,pid);
        if(!result.isSuccess()){
            return Result.buildSuccessResult("0");
        }else{
            return Result.buildSuccessResult("1");
        }
    }

    /**
     * 20221201活动抽奖
     * @param uid
     * @param pid
     */
    @Override
    public Result<LotteryInfoVO> doLottery1201(String uid, String pid){
        String lockKey = CacheKey_DoLotteryLock;
        try{
            if(redissonDistributionLock.tryLock(lockKey,TimeUnit.SECONDS,10)){
                // 0.初始化变量
                List<LotteryDetailDO> myLotteryInfo = null;
                List<LotteryDetailDO> latestLotteryInfo = null;

                // 1.判断是否有抽奖权限
                Result<String> result = getLotteryPrivilege(uid,pid);
                if(!result.isSuccess()){
                    return Result.buildErrorResult(result.getMsg());
                }
                // 2.获取用户信息
                String[] accountInfo = result.getData().split(",");
                String emAccount = accountInfo[0];
                String mobileX = accountInfo[1];
                String mobileMask = accountInfo[2];

                // 3.从Redis Set集合中随机抽取一个奖品编号
                Integer level = drawPrizesLevel();
                if(level == -1){
                    return Result.buildErrorResult("抽奖失败，请联系客服");
                }

                // 4.获取用户订单明细
                OrderProdListVO orderDetail = getOrderDetailInfo(uid,mobileX);
                LotteryInfoVO lotteryInfoVO = null;
                String pacCode = "",prizeCode = "",prizeName = "";
                if(orderDetail!=null){
                    String orderId = orderDetail.getORDER_ID();
                    String detId = orderDetail.getDetID();
                    if(level == 1){
                        prizeCode = "92Days";
                        prizeName = "3个月使用期";
                    }else if(level == 2){
                        prizeCode = "183Days";
                        prizeName = "6个月使用期";
                    }else{
                        prizeCode = "275Days";
                        prizeName = "9个月使用期";
                    }

                    // 5. 写入中奖记录明细
                    LotteryDetailDO lotteryDetailDO = new LotteryDetailDO();
                    lotteryDetailDO.setUid(uid);
                    lotteryDetailDO.setEm(emAccount);
                    lotteryDetailDO.setMobile0x(mobileX);
                    lotteryDetailDO.setBatchno(batchNo20221201);
                    lotteryDetailDO.setLotterytarget(detId);
                    lotteryDetailDO.setMobilemask(mobileMask);
                    lotteryDetailDO.setPrizecode(prizeCode);
                    lotteryDetailDO.setPrizename(prizeName);
                    lotteryDetailDO.setPrizeconfirm("0");
                    lotteryDetailDO.setLevel(level);
                    Integer writeRet = lotteryDetailMapper.insert(lotteryDetailDO);
                    if(writeRet > 0){
                        Integer id = lotteryDetailDO.getId();
                        myLotteryInfo = refreshMyLotteryInfo1201(uid,batchNo20221201);
                        latestLotteryInfo = refreshLatestLotteryDetailList(batchNo20221201);
                        lotteryInfoVO = new LotteryInfoVO();
                        lotteryInfoVO.setMyLotteryInfo(myLotteryInfo);
                        lotteryInfoVO.setLatestLotteryInfo(latestLotteryInfo);

                        // 6.线程池异步发放奖品及发送短信通知
                        CompletableFuture.runAsync(() -> {
                            String tempPacCode = "",tempPrizeName = "";
                            if(level == 1){
                                tempPacCode = "PAC122121216310850";
                                tempPrizeName = "3个月使用期";
                            }else if(level == 2){
                                tempPacCode = "PAC1221212165044713";
                                tempPrizeName = "6个月使用期";
                            }else{
                                tempPacCode = "PAC1221212165220747";
                                tempPrizeName = "9个月使用期";
                            }
                            // 7. 物流发放奖品
                            Boolean isSendOK = sendPrize(mobileX, tempPacCode, emAccount, orderId, detId,"12月新增抽奖活动");
                            if(isSendOK){
                                // 8.更新奖品发放记录状态
                                lotteryDetailMapper.updatePrizeConfirm(id);
                                // 9.发送短信通知
                                String message = MessageFormat.format("尊敬的益盟{0}用户，您好！您参与的新购智盈抽奖活动，奖品为{1}，已发放至你的账户，请查收！", mobileMask,tempPrizeName);
                                mobileService.sendTextMessage(mobileX, message, "181");
                            }else{
                                log.error("奖品发放失败："+"mobileX--"+mobileX+","+"PacCode--"+tempPacCode+","+"emAccount--"+emAccount+","
                                        +"orderId--"+orderId+","+"detId--"+detId+","+"remark--"+"12月新增抽奖活动");
                            }
                        }, executor);
                    }else{
                        return Result.buildErrorResult("抽奖失败，请联系客服");
                    }
                }
                return Result.buildSuccessResult(lotteryInfoVO);
            }else{
                return Result.buildErrorResult("抽奖失败，请联系业务员");
            }
        }finally {
            redissonDistributionLock.unlock(lockKey);
        }
    }

    /**
     * 获取中奖记录信息
     * @param uid
     * @return
     */
    @Override
    public Result<LotteryInfoVO> getLotteryInfo1201(String uid){
        List<LotteryDetailDO> myLotteryInfo = refreshMyLotteryInfo1201(uid,batchNo20221201);
        List<LotteryDetailDO> latestLotteryInfo = refreshLatestLotteryDetailList(batchNo20221201);
        LotteryInfoVO lotteryInfoVO = new LotteryInfoVO();
        lotteryInfoVO.setMyLotteryInfo(myLotteryInfo);
        lotteryInfoVO.setLatestLotteryInfo(latestLotteryInfo);
        return Result.buildSuccessResult(lotteryInfoVO);
    }

    /**
     * 获取最新中奖记录
     * @param batchNo
     * @return
     */
    private List<LotteryDetailDO> getLatestLotteryDetailList(String batchNo){
        //获取最新中奖记录
        String cacheKey = CacheKey_LatestPrizesDetailList + batchNo;
        List<LotteryDetailDO> list = redisService.getList(cacheKey, LotteryDetailDO.class);
        if(list == null){
            list = refreshLatestLotteryDetailList(batchNo);
        }
        return list;
    }

    /**
     * 刷新最新中奖记录
     * @param batchNo
     * @return
     */
    private List<LotteryDetailDO> refreshLatestLotteryDetailList(String batchNo){
        String cacheKey = CacheKey_LatestPrizesDetailList + batchNo;
        List<LotteryDetailDO> list = lotteryDetailMapper.getLatestLotteryDetailList(batchNo);
        if(list!=null && list.size() > 0){
            redisService.set(cacheKey,list,1L,TimeUnit.DAYS);
            return list;
        }
        return null;
    }

    /**
     * 获取我的中奖记录
     * @param uid
     * @param batchNo
     * @return
     */
    private List<LotteryDetailDO> getMyLotteryInfo1201(String uid,String batchNo){
        //获取用户已获奖信息
        String cacheKey = CacheKey_PrizesDetail + batchNo + ":" + uid;
        List<LotteryDetailDO> list = redisService.getList(cacheKey, LotteryDetailDO.class);
        if(list == null){
            list = refreshMyLotteryInfo1201(uid,batchNo);
        }
        return list;
    }

    /**
     * 刷新我的中奖记录缓存
     * @param uid
     * @param batchNo
     */
    private List<LotteryDetailDO> refreshMyLotteryInfo1201(String uid,String batchNo){
        String cacheKey = CacheKey_PrizesDetail + batchNo + ":" + uid;
        List<LotteryDetailDO> userLotteryHistory = lotteryDetailMapper.getUserLotteryInfoListByUid(uid,batchNo);
        if(userLotteryHistory!=null && userLotteryHistory.size() > 0){
            // 刷新我的中奖记录缓存，活动结束缓存自动失效
            redisService.set(cacheKey,userLotteryHistory,90L,TimeUnit.DAYS);
            return userLotteryHistory;
        }
        return null;
    }

    /**
     * 判断是否有抽奖权限
     * @param uid
     * @param pid
     * @return
     */
    private Result<String> getLotteryPrivilege(String uid, String pid){
        // 1.判断活动日期 2022-12-01~2023-02-28
        DateTime now = DateTime.now();
        if(now.isAfter(DateFormatUtil.string2Date("2023-03-01",DateFormatUtil.DATE_FORMAT))){
            return Result.buildErrorResult("对不起，当前活动已下线");
        }

        // 2.判断是否小智盈用户
        if(!"888010000".equals(pid)){
            return Result.buildErrorResult("对不起，您无权限参与活动");
        }

        // 3.判断用户是否已参与过抽奖活动
        List<LotteryDetailDO> myLotteryList = getMyLotteryInfo1201(uid, batchNo20221201);
        if(myLotteryList != null && myLotteryList.size() > 0){
            return Result.buildErrorResult("对不起，您已参与抽奖活动");
        }

        // 4.判断用户是否购买产品
        //获取用户加密手机号和em帐号
        String account = "";
        String mobileX = "";
        String mobileMask = "";
        List<BindAccountVO> list = userService.GetBindAccountList(uid);
        if (list != null && list.size() > 0) {
            for (BindAccountVO item : list) {
                if (item.AccountType.equals(0) &&
                        userService.GetAccountPID(item.AccountName).equals(pid)) {
                    account = item.AccountName;
                }

                if (item.AccountType.equals(1)) {
                    mobileX = item.EncryptMobile;
                    mobileMask = item.AccountName;
                }
            }
        }

        // 判断用户是否已绑定手机号
        if(StringUtils.isBlank(mobileX)){
            return Result.buildErrorResult("您的软件帐号未绑定手机号");
        }

        // 判断用户是否已绑定em帐号
        if(StringUtils.isBlank(account)){
            return Result.buildErrorResult("您的软件帐号未绑定EM帐号");
        }
        String accountInfoString = account + "," + mobileX + "," + mobileMask;
        // 获取订单信息
        OrderProdListVO orderDetail = getOrderDetailInfo(uid,mobileX);
        if(orderDetail == null){
            return Result.buildErrorResult("对不起，您无权限参与活动");
        }
        return Result.buildSuccessResult(accountInfoString);
    }

    /**
     * 抽取奖品 奖品编号1~75--3个月使用期  76~90--6个月使用期  91~100--9个月使用期
     * @return 1--3个月使用期 2--6个月使用期 3--9个月使用期
     */
    private Integer drawPrizesLevel(){
        Long count = redisService.sSize(CacheKey_PrizesSet);
        if(count <= 0){
            redisService.setAdd(CacheKey_PrizesSet,prizesArray);
        }
        Integer prizesIndex = (Integer) redisService.sPop(CacheKey_PrizesSet);
        if(prizesIndex >= 1 && prizesIndex <= 75){
            return 1;
        }else if(prizesIndex >= 76 && prizesIndex <= 90){
            return 2;
        }else if(prizesIndex >= 91 && prizesIndex <=100 ){
            return 3;
        }else{
            return -1;
        }
    }

    /**
     * 获取用户订单信息
     * @param uid
     * @param mobileX
     * @return
     */
    private OrderProdListVO getOrderDetailInfo(String uid, String mobileX){
        OrderProdListVO orderProdListVO = redisService.get(CacheKey_LogisticOrderDetail + uid, OrderProdListVO.class);
        if(orderProdListVO!=null){
            return orderProdListVO;
        }else{
            String actCodeConfig = "ac-122112316490287";
            String actBeginTimeConfig = "2022-12-01 00:00:00";
            String actEndTimeConfig = "2023-01-14 23:59:59";
            String[] arrayActCode = actCodeConfig.split(",");
            if(arrayActCode!=null && arrayActCode.length > 0){
                for (String actCode : arrayActCode) {
                    //获取物流订单
                    OrderProdListDTO orderProdListDTO = new OrderProdListDTO();
                    orderProdListDTO.setStockUpDate_Start(actBeginTimeConfig);
                    orderProdListDTO.setStockUpDate_End(actEndTimeConfig);
                    orderProdListDTO.setRefund_Sign(0);
                    orderProdListDTO.setMIDPWD(mobileX);
                    orderProdListDTO.setACTIVITY_CODE(actCode);

                    //查询订单信息
                    List<OrderProdListVO> orderProdListVOS = logisticsService.queryOrderProdList(orderProdListDTO);
                    if (orderProdListVOS != null && orderProdListVOS.size() > 0) {
                        redisService.set(CacheKey_LogisticOrderDetail + uid, orderProdListVOS.get(0), 10L, TimeUnit.MINUTES);
                        return orderProdListVOS.get(0);
                    }
                }
            }
            return null;
        }
    }

    @Override
    public void freshUserLotteryList(String batchno, String uid) {
        redisService.remove(rediskey_mylotteryinfo+batchno+"_"+uid);
    }

    /*
     * 功能描述:
     * 用户抽奖
     * @Param:
     * @Return:
     * @Author: tengdengming
     * @Date: 2022/5/25 08:47
     */
    @Override
    public LotteryPrizeDO doLottery(String mobile0X,String uid, String batchNo,String ver) {

        List<LotteryPrizeDO> prizeList = initPrizeList(ver);

        LotteryPrizeDO award = lottery(prizeList);

        //抽中大奖处理
        if (award.getLevel()<4){

            //获取白名单信息
            LotteryWhitelistDO myWhitelistDO = isInWhiteList(uid,batchNo);

            //不在白名单
            if (myWhitelistDO==null){
                award = prizeList.get(3);
                award.setBelongGroup("");
                return award;
                //return doLottery(mobile0X,uid,batchNo,ver);
            }else {
                //在白名单中，指定所属组Group关系
                award.setBelongGroup(myWhitelistDO.getGroup());
                //判断是否已经抽到过大奖
                List<LotteryDetailDO> userHistoryDetailList = lotteryDetailMapper.getUserLotteryInfoListByUid(uid,batchNo);

                for (LotteryDetailDO item: userHistoryDetailList) {
                    //如果已经中过大奖，直接返回三等奖
                    if (item.getPrizecode().equals("200Days")){return prizeList.get(3);}
                    if (item.getPrizecode().equals("160Days")){return prizeList.get(3);}
                    if (item.getPrizecode().equals("80Days")){return prizeList.get(3);}
                }

                //获取库存配置
                LotteryPrizelimitDO lotteryPrizelimitDO = lotteryPrizelimitMapper.getLotteryPrizeLimitByBatchNo(batchNo, award.getBelongGroup(),award.getLevel(),DateTime.now());

                //检查是否还有库存
                if (lotteryPrizelimitDO!=null && lotteryPrizelimitDO.getPrizelimit() - lotteryPrizelimitDO.getPrizesend() >= 1) {

                    //扣减库存  发送完成再扣减
                    //lotteryPrizelimitMapper.increasePrizeSend(batchNo,award.getBelongGroup(), DateTime.now(),award.getLevel());

                    return award;
                } else {
                    //库存不够，重抽
                    return doLottery(mobile0X, uid, batchNo,ver);
                }

            }

        }else{
            award.setBelongGroup("");
            return award;
        }

    }


    /*
     * 功能描述:
     * 〈〉测试概率
     * @Param:
     * @Return:
     * @Author: tengdengming
     * @Date: 2022/5/27 09:54
     */
    @Override
    public LotteryPrizeDO lotteryRateTest(List<LotteryPrizeDO> prizelist){

        LotteryPrizeDO award = lottery(prizelist);
        return award;

    }

    /*
     * 功能描述:
     * 〈〉最近中奖
     * @Param:
     * @Return:
     * @Author: tengdengming
     * @Date: 2022/5/28 10:31
     */

    @Override
    public List<LotteryDetailDO> getRecentlyLotteryDetailList(String batchno) {
        String key = RedisConstants.Redis_Pre_Activity + "lottery:recentlylotterydetaillist";
        List<LotteryDetailDO> cachedata = redisService.getList(key,LotteryDetailDO.class);

        if (cachedata != null){return cachedata;}
        cachedata = lotteryDetailMapper.getRecentlyLotteryDetailList(batchno);

        redisService.set(key,cachedata,(long)2*60);

        return cachedata;
    }


    @Override
    public List<LotteryDetailDO> getLotteryDetailsFromDate(String batchno, String fromdate) {
        try {

            List<LotteryDetailDO> cachedata = lotteryDetailMapper.getLotteryDetailsFromDate(batchno,fromdate);

            if(cachedata!=null){
                return cachedata;
            }
        }catch (Exception e) {

            log.error("获取指定日期后的抽奖明细发生错误：" + e.getMessage());
        }

        return null;

    }

    /*
     * 功能描述: 
     * 爆率抽奖
     * @Param:
     * @Return: 
     * @Author: tengdengming
     * @Date: 2022/5/25 08:28
     */
    public static LotteryPrizeDO lottery(List<LotteryPrizeDO> prizelist) {
        if(prizelist.isEmpty()){
            //throw new AwardListIsEmptyException();
        }

        //奖品总数
        int size = prizelist.size();

        //计算总概率
        double sumProbability = 0d;
        for(LotteryPrizeDO award : prizelist) {
            sumProbability += award.getProbability();
        }

        //计算每个奖品的概率区间
        //例如奖品A概率区间0-0.1 奖品B概率区间 0.1-0.5 奖品C概率区间0.5-1
        //每个奖品的中奖率越大，所占的概率区间就越大
        List<Double> sortAwardProbabilityList = new ArrayList<Double>(size);
        Double tempSumProbability = 0d;
        for (LotteryPrizeDO award : prizelist) {
            tempSumProbability += award.getProbability();
            sortAwardProbabilityList.add(tempSumProbability / sumProbability);
        }

        //产生0-1之间的随机数
        //随机数在哪个概率区间内，则是哪个奖品
        double randomDouble = Math.random();
        //加入到概率区间中，排序后，返回的下标则是awardList中中奖的下标
        sortAwardProbabilityList.add(randomDouble);
        Collections.sort(sortAwardProbabilityList);
        int lotteryIndex = sortAwardProbabilityList.indexOf(randomDouble);
        return prizelist.get(lotteryIndex);

    }

    @Override
    public List<LotteryPrizeDO> initPrizeList(String verStr){

        List<LotteryPrizeDO> prizeList = new ArrayList<>();
        /*
                                                                    PAC1220523173214877	【20周年庆大促】抽奖200天/深度资金版大师续费
                                                                    PAC122052317253891	【20周年庆大促】抽奖160天/深度资金版大师续费
                                                                    PAC1220523165646620	【20周年庆大促】抽奖80天/深度资金版大师续费
                                                                    PAC1220523163102129	【20周年庆大促】抽奖20天/深度资金版大师续费

                                                                    PAC1220523173029920	【20周年庆大促】抽奖200天/掘金版大师续费
                                                                    PAC1220523172703142	【20周年庆大促】抽奖160天/掘金版大师续费
                                                                    PAC122052316460721	【20周年庆大促】抽奖80天/掘金版大师续费
                                                                    PAC1220523163345133	【20周年庆大促】抽奖20天/掘金版大师续费

         */
        if (verStr.equals("深度")){
            prizeList.add(new LotteryPrizeDO(1,"深度资金版续费-200天","200Days","888020000","PAC1220523173214877",0.20));
            prizeList.add(new LotteryPrizeDO(2,"深度资金版续费-160天","160Days","888020000","PAC122052317253891",0.20));
            prizeList.add(new LotteryPrizeDO(3,"深度资金版续费- 80天","80Days","888020000","PAC1220523165646620",0.25));
            prizeList.add(new LotteryPrizeDO(4,"深度资金版续费- 20天","20Days","888020000","PAC1220523163102129",0.35));

        }else{
            prizeList.add(new LotteryPrizeDO(1,"掘金版大师续费-200天","200Days","888080000","PAC1220523173029920",0.20));
            prizeList.add(new LotteryPrizeDO(2,"掘金版大师续费-160天","160Days","888080000","PAC1220523172703142",0.20));
            prizeList.add(new LotteryPrizeDO(3,"掘金版大师续费- 80天","80Days","888080000","PAC122052316460721",0.25));
            prizeList.add(new LotteryPrizeDO(4,"掘金版大师续费- 20天","20Days","888080000","PAC1220523163345133",0.35));
        }

        return prizeList;
    }

    /***
     * 功能描述:
     * 查询用户是否在本批次的白名单中
     * @Param:
     * @Return:
     * @Author: tengdengming
     * @Date: 2022/5/24 16:50
     */
    public LotteryWhitelistDO isInWhiteList(String uid,String batchNo){
        LotteryWhitelistDO queryobj = lotteryWhitelistMapper.checkInWhiteList(batchNo,uid);
        if (queryobj!=null){return queryobj;}
        return null;
    }

    /*
     * 功能描述:
     * 〈〉是否在活动包范围内
     * @Param:
     * @Return:
     * @Author: tengdengming
     * @Date: 2022/5/24 22:09
     */
    private Boolean inAcCodeRange(String acCode){
        /*
         *  ac-****************
            ac-1220523181912609
            ac-1220523181757901
            ac-122052318164285
            ac-1220523181536737
            ac-1220523181423432
            *
            *   ac-1220523155136753
                ac-1220523155859122
                ac-122052316012658
                ac-1220523160540259
                ac-1220************
                ac-1220523161240350

         */
        //测试物流活动包号
        String[] acCodeArray = {"ac-****************",
                "ac-1220523181912609",
                "ac-1220523181757901",
                "ac-122052318164285",
                "ac-1220523181536737",
                "ac-1220523181423432",
                "ac-1220523155136753",
                "ac-1220523155859122",
                "ac-122052316012658",
                "ac-1220523160540259",
                "ac-1220************",
                "ac-1220523161240350",};

        for (int i = 0; i < acCodeArray.length; i++) {
            if (acCode.equals(acCodeArray[i])){return true;}
        }

        return false;
    }

    /***
     * 功能描述:
     *  获取物流订单号
     * @Param: [mobile0x, startDate, endDate, activityCodes]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2022/5/19 20:08
     */
    @Override
    public LotteryInfo getMyLotteryInfo(String mobile0x, String uid,String batchNo) {

        LotteryInfo cachedata = redisService.get(rediskey_mylotteryinfo+batchNo+"_"+uid,LotteryInfo.class);//60秒

        if (cachedata!=null){return cachedata;}

        //获取用户已获奖信息
        List<LotteryDetailDO> userLotteryHistory = lotteryDetailMapper.getUserLotteryInfoListByUid(uid,batchNo);

        //mobile0x = "0x88C343F513AA1850529D34E71C536427";
        //获取物流订单
        OrderProdListDTO orderProdListDTO = new OrderProdListDTO();
        orderProdListDTO.setStockUpDate_Start("2022-01-01");
        orderProdListDTO.setStockUpDate_End("2022-08-07");
        orderProdListDTO.setRefund_Sign(1);
        orderProdListDTO.setMIDPWD(mobile0x);
        orderProdListDTO.setACTIVITY_CODE("");

        //查询订单信息
        List<OrderProdListVO> orderProdListVOS = logisticsService.queryOrderProdList(orderProdListDTO);

        LotteryInfo lotteryInfo = new LotteryInfo();
        lotteryInfo.setBatchNo(batchNo);

        List<JSONObject> lotteryInfoList = new ArrayList<>();

        if (orderProdListVOS != null) {
            HashSet<String> hashSet = new HashSet<String>();

            for (OrderProdListVO item : orderProdListVOS) {

                /*过滤上拽订单未创建账号的订单，无权益赋予对象，只处理充值成功的*/
                if (item.getLOGINDATE()!=null && item.getLOGINDATE().equals("")){ continue;}

                if (inAcCodeRange(item.getACTIVITY_CODE())){
                    JSONObject li = new JSONObject();
                    li.put("detId",item.getDetID());
                    li.put("orderNo",item.getORDER_ID());
                    li.put("mobile0x",item.getMIDPWD());

                    li.put("activityNo",item.getACTIVITY_CODE());
                    li.put("activityName",item.getACTIVITY_NAME());
                    li.put("activityNameShort",getShortActivityName(item.getACTIVITY_CODE()));
                    li.put("prizeResult","");
                    li.put("orderStatus",item.getRefund_Sign());

                    Integer currLotteryTimes = 0;

                    //扣减历史已抽奖数量
                    if (userLotteryHistory.size()>0){

                        for (int i = 0; i < userLotteryHistory.size(); i++) {
                            //判断该订单是否已经抽奖
                            if (userLotteryHistory.get(i).getLotterytarget().equals(item.getDetID())){
                                currLotteryTimes++;
                                li.put("prizeResult",li.get("prizeResult")+userLotteryHistory.get(i).getPrizecode().trim()+",");
                            }
                        }
                    }

                    if (item.getACTIVITY_NAME().indexOf("3年")>-1){
                        li.put("timesAllowed",3);
                        li.put("timesLeft",3-currLotteryTimes);
                    }else{
                        li.put("timesAllowed",1);
                        li.put("timesLeft",1-currLotteryTimes);
                    }

                    /*if (item.getRefund_Sign()==-1){
                        li.put("timesLeft",0);
                    }*/

                    lotteryInfoList.add(li);
                }
            }

            lotteryInfo.setLotteryInfoList(lotteryInfoList);

            redisService.set(rediskey_mylotteryinfo+batchNo+"_"+uid,lotteryInfo,(long)2*60);
        }

        return lotteryInfo;
    }

    /**
     * 功能描述:
     * 〈〉发送奖品
     * @Param: [mobile0x, activityCode]
     * @Return: java.lang.Boolean
     * @Author: tengdengming
     * @Date: 2022/5/24 17:31
     */
    @Override
    public Boolean sendPrize(String mobile0x, String activityCode,String em,String orderId,String detId,String remark) {

        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(activityCode);
        sendPrivilegeDTO.setReason(remark);
        sendPrivilegeDTO.setApplyUserID("scb_public");

        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();
        createActivityGrantApplyAccountDTO.setAccountType(1);//关联账号类型（1：EM号，2：手机号）
        createActivityGrantApplyAccountDTO.setAccount(em);
        createActivityGrantApplyAccountDTO.setMID(mobile0x);
        createActivityGrantApplyAccountDTO.setOrderID(orderId);
        createActivityGrantApplyAccountDTO.setOrderDetailID(detId);

        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        try {
            Boolean resultSenddPrivilege = logisticsService.sendPrivilege(sendPrivilegeDTO);

            if (resultSenddPrivilege) {
                //redisService.hashSet(key, account.trim(), "1");
                log.info("接口调用赠送成功,mobile0x号:" + mobile0x);
                return true;
            } else {
                log.info("接口调用赠送失败,mobile0x号:" + mobile0x);
            }
        }catch (Exception e) {
            log.error("抽奖发送奖品失败 . Error:"+e.getMessage());
        }finally {

        }

        return false;
    }

    @Override
    public Boolean recordLotteryDetail(LotteryDetailDO lotteryDetailDO) {

        Integer writeRet = lotteryDetailMapper.insert(lotteryDetailDO);

        //更新库存
        lotteryPrizelimitMapper.increasePrizeSend(lotteryDetailDO.getBatchno(),lotteryDetailDO.getGroup(),DateTime.now(),lotteryDetailDO.getLevel());
        if (writeRet>0){return true;}else{return false;}
    }

    private String getShortActivityName(String acCode){
        Map<String,String> dic = new HashMap<String,String>();
        dic.put("ac-****************","掘金版续1年");
        dic.put("ac-1220523181912609","深度资金版续1年");
        dic.put("ac-1220523181757901","深度资金版续1年");
        dic.put("ac-122052318164285","掘金版续3年");
        dic.put("ac-1220523181536737","深度资金版续3年");
        dic.put("ac-1220523181423432","深度资金版续3年");

        dic.put("ac-1220523155136753","掘金版续1年");
        dic.put("ac-1220523155859122","深度资金版续1年");
        dic.put("ac-122052316012658","深度资金版续1年");
        dic.put("ac-1220523160540259","掘金版续3年");
        dic.put("ac-1220************","深度资金版续3年");
        dic.put("ac-1220523161240350","深度资金版续3年");

        //ac-122052318164285
        if (dic.get(acCode)==null){
            return "acCode="+acCode+"未配置";
        }else{

        }

        return dic.get(acCode);
    }

}
