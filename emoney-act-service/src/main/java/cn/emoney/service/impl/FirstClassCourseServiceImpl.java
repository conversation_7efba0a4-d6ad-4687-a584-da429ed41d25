package cn.emoney.service.impl;

import cn.emoney.act.client.firstclass.FirstClassCourseClient;
import cn.emoney.act.client.firstclass.dto.FirstClassCourseRequestBody;
import cn.emoney.act.client.firstclass.dto.FirstClassCourseTempDTO;
import cn.emoney.act.quest.logic.reward.PointReward;
import cn.emoney.common.result.MobilePageDTO;
import cn.emoney.pojo.bo.FirstClassCourseDTO;
import cn.emoney.service.FirstClassCourseService;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FirstClassCourseServiceImpl implements FirstClassCourseService {
    private final FirstClassCourseClient courseClient;
    private final Cache remoteCourseCache, remoteColumnCache;
    private final AsyncLoadingCache<Integer, Optional<FirstClassCourseDTO>> courseCache;
    private final AsyncLoadingCache<ColumnTableKey, List<FirstClassCourseDTO>> columnCache;
    private final AsyncLoadingCache<LocalTableKey, LocalTable> hotTables;

    public FirstClassCourseServiceImpl(FirstClassCourseClient courseClient,
                                       ThreadPoolTaskExecutor executor,
                                       CacheManager cacheManager) {
        this.courseClient = courseClient;
        this.remoteColumnCache = cacheManager.getCache("act:first-class:course-column");
        this.remoteCourseCache = cacheManager.getCache("act:first-class:course");
        this.courseCache = Caffeine.newBuilder()
                .maximumSize(1024)
                .expireAfterWrite(Duration.ofDays(1))
                .refreshAfterWrite(Duration.ofMinutes(1))
                .executor(executor)
                .buildAsync(this::loadCourse);
        this.columnCache = Caffeine.newBuilder()
                .maximumSize(128)
                .expireAfterWrite(Duration.ofDays(1))
                .refreshAfterWrite(Duration.ofMinutes(1))
                .executor(executor)
                .buildAsync(this::loadColumn);
        this.hotTables = Caffeine.newBuilder()
                .maximumSize(16)
                .expireAfterWrite(Duration.ofDays(1))
                .refreshAfterWrite(Duration.ofMinutes(1))
                .executor(executor)
                .buildAsync(this::createLocalTable);
    }

    @Override
    public Optional<FirstClassCourseDTO> findById(Integer id) {
        try {
            return courseCache.get(id).join();
        } catch (CompletionException ex) {
            if (ex.getCause() instanceof RuntimeException) {
                throw (RuntimeException) ex.getCause();
            }
            throw ex;
        }
    }

    @Override
    public List<FirstClassCourseDTO> listCourse(Integer columnId, Sort sort, int size) {
        if (columnId == null || size <= 0) {
            return Collections.emptyList();
        }
        try {
            return columnCache.get(new ColumnTableKey(columnId, sort, size)).join();
        } catch (CompletionException ex) {
            if (ex.getCause() instanceof RuntimeException) {
                throw (RuntimeException) ex.getCause();
            }
            throw ex;
        }
    }

    @Override
    public List<FirstClassCourseDTO> listByTimeForward(Integer columnId, LocalDateTime date, int size) {
        if (size <= 0) {
            return Collections.emptyList();
        }
        // 超过现在15天 不再查询
        LocalDate cutLine = LocalDate.now().plusDays(15);
        LocalDate targetDate = date.toLocalDate();
        int tableCount = 0, expectCount = 2 + (size / 30);
        List<FirstClassCourseDTO> result = new ArrayList<>(size);
        while (result.size() < size && tableCount < expectCount) {
            LocalDate nextDate = targetDate.plusMonths(tableCount++).withDayOfMonth(1);
            if (nextDate.isAfter(cutLine)) {
                return result;
            }
            LocalTable localTable = hotTables.get(new LocalTableKey(columnId,
                    nextDate.getYear(),
                    nextDate.getMonthValue())
            ).join();
            result.addAll(localTable.listByTimeForward(date, size - result.size()));
        }
        if (result.size() == size) {
            return result;
        }
        return loadCourse(FirstClassCourseRequestBody.builder().columnId(columnId)
                .beginTimeStart(date)
                .sort(Sort.by(Sort.Direction.ASC, "beginTime"))
                .rows(size)
                .build());
    }

    /**
     * 按时间后向查询课表, 历史时间
     *
     * @param columnId
     * @param date     时间
     * @param size     数量
     */
    @Override
    public List<FirstClassCourseDTO> listByTimeBackward(Integer columnId, LocalDateTime date, int size) {
        if (size <= 0) {
            return Collections.emptyList();
        }
        int tableCount = 0, expectCount = 2 + (size / 30);
        List<FirstClassCourseDTO> result = new ArrayList<>(size);
        while (result.size() < size && tableCount < expectCount) {
            LocalDateTime prevDate = date.minusMonths(tableCount++);
            LocalTable localTable = hotTables.get(new LocalTableKey(columnId,
                    prevDate.getYear(),
                    prevDate.getMonthValue())
            ).join();
            result.addAll(localTable.listByTimeBackward(date, size - result.size()));
        }
        if (result.size() == size) {
            return result;
        }
        return loadCourse(FirstClassCourseRequestBody.builder().columnId(columnId)
                .beginTimeEnd(date)
                .sort(Sort.by(Sort.Direction.DESC, "beginTime"))
                .rows(size)
                .build());
    }

    private LocalTable createLocalTable(LocalTableKey key) {
        LocalDateTime headTime = LocalDate.of(key.getYear(), key.getMonth(), 1).atStartOfDay();
        LocalDateTime tailTime = headTime.plusMonths(1).minusNanos(1);

        int expectSize = 100;
        List<FirstClassCourseDTO> table = loadCourse(FirstClassCourseRequestBody.builder()
                .columnId(key.getColumnId())
                .beginTimeStart(headTime)
                .beginTimeEnd(tailTime)
                .sort(Sort.by(Sort.Direction.DESC, "beginTime"))
                .rows(expectSize + 1)
                .build()
        );
        if (table.size() > expectSize) {
            log.error("热点表数据量过大, 请检查数据源或需求是否有问题, headDate: {}, tailDate: {}, size: {}", headTime, tailTime, table.size());
        }
        return new LocalTable(table);
    }

    private List<FirstClassCourseDTO> loadColumn(ColumnTableKey key) {
        return remoteColumnCache.get(key, () ->
                loadCourse(FirstClassCourseRequestBody.builder()
                        .columnId(key.getColumnId())
                        .sort(key.getSort())
                        .rows(key.getSize())
                        .build())
        );
    }

    private List<FirstClassCourseDTO> loadCourse(FirstClassCourseRequestBody requestBody) {
        MobilePageDTO<FirstClassCourseTempDTO> body = courseClient.list(requestBody);
        log.info("课表请求: {}, size: {}", requestBody, body.getData().size());
        return body.getData().stream()
                .map(this::convert)
                .peek(dto -> courseCache.put(dto.getId(), CompletableFuture.completedFuture(Optional.of(dto))))
                .collect(Collectors.toList());
    }

    private Optional<FirstClassCourseDTO> loadCourse(Integer id) {
        return Optional.ofNullable(remoteCourseCache.get(id, () -> {
            return courseClient.get(id).map(this::convert).orElse(null);
        }));
    }

    @Data
    private static class ColumnTableKey {
        private final Integer columnId;
        private final Sort sort;
        private final Integer size;

        @Override
        public String toString() {
            return "columnId=" + columnId + ", sort=" + sort + ", size=" + size;
        }
    }

    @Data
    private static class LocalTableKey {
        private final Integer columnId;
        private final Integer year;
        private final Integer month;

        @Override
        public String toString() {
            return "columnId=" + columnId + ", year=" + year + ", month=" + month;
        }
    }

    private static class LocalTable {
        private NavigableMap<LocalDateTime, List<FirstClassCourseDTO>> timeMap = Collections.emptyNavigableMap();

        public LocalTable(List<FirstClassCourseDTO> courses) {
            if (courses.isEmpty()) {
                return;
            }
            this.timeMap = courses.stream().collect(Collectors.groupingBy(
                    dto -> LocalDateTime.ofInstant(dto.getStartTime().toInstant(), ZoneId.systemDefault()),
                    () -> new TreeMap<>(Comparator.<LocalDateTime>naturalOrder()),
                    Collectors.toList()));
        }


        public List<FirstClassCourseDTO> listByTimeBackward(LocalDateTime date, int size) {
            return this.timeMap.descendingMap().tailMap(date, true).values()
                    .stream().flatMap(Collection::stream)
                    .limit(size)
                    .collect(Collectors.toList());
        }

        public List<FirstClassCourseDTO> listByTimeForward(LocalDateTime date, int size) {
            return this.timeMap.tailMap(date, true).values()
                    .stream().flatMap(Collection::stream)
                    .limit(size)
                    .collect(Collectors.toList());
        }
    }

    private FirstClassCourseDTO convert(FirstClassCourseTempDTO item) {
        FirstClassCourseDTO courseDTO = new FirstClassCourseDTO();
        courseDTO.setId(item.getId());
        courseDTO.setTitle(item.getClassName());
        courseDTO.setSummary(item.getClassSummary());
        courseDTO.setTeacherName(item.getTeacherName());
        courseDTO.setCoverApp(item.getAppCoverImg());
        courseDTO.setComment(item.getWeekComment());
        courseDTO.setParentId(item.getParentId());
        courseDTO.setStartTime(item.getBeginTime());
        courseDTO.setEndTime(item.getEndTime());
        courseDTO.setBookBaseAmount(item.getBookBaseAmount());
        courseDTO.setReplayBaseAmount(item.getReplayBaseAmount());
        courseDTO.setOrder(item.getOrder());

        try {
            Optional.ofNullable(item.getLiveVideoId())
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .ifPresent(courseDTO::setCourseId);
            Optional.ofNullable(item.getPointTaskId())
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::parseLong)
                    .map(PointReward::new)
                    .ifPresent(reward -> courseDTO.addReward(1, "看课送积分", reward));
            Optional.ofNullable(item.getPracticeTaskId())
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::parseLong)
                    .map(PointReward::new)
                    .ifPresent(reward -> courseDTO.addReward(2, "练习送积分", reward));

            List<FirstClassCourseDTO.ExtraUrl> extraUrls = new ArrayList<>(2);
            Optional.ofNullable(item.getVoteUrl1())
                    .filter(s -> !s.isEmpty())
                    .map(test -> new FirstClassCourseDTO.ExtraUrl(0, test))
                    .ifPresent(extraUrls::add);
            Optional.ofNullable(item.getVoteUrl())
                    .filter(s -> !s.isEmpty())
                    .map(test -> new FirstClassCourseDTO.ExtraUrl(EXTRA_URLS_TYPES.indexOf(item.getTipName()), test))
                    .ifPresent(extraUrls::add);
            courseDTO.setExtraUrls(extraUrls);

            Optional.ofNullable(item.getActivityId())
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Integer::parseInt)
                    .ifPresent(courseDTO::setActivitySeq);
        } catch (Exception e) {
            log.error("课程详情转换失败: {}", item, e);
        }
        return courseDTO;
    }

    private final static List<String> EXTRA_URLS_TYPES = Arrays.asList("练习", "笔记", "报告");
}
