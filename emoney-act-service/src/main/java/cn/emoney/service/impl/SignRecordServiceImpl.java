package cn.emoney.service.impl;

import cn.emoney.common.enums.BaseResultCodeEnum;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.DateFormatUtil;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.common.utils.RedissonDistributionLock;
import cn.emoney.mapper.activity.SignFeeCycleMapper;
import cn.emoney.mapper.activity.SignReceiveRecordMapper;
import cn.emoney.mapper.activity.SignRecordMapper;
import cn.emoney.pojo.SignFeeCycle;
import cn.emoney.pojo.SignReceiveRecord;
import cn.emoney.pojo.SignRecord;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.OrderProdListDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.*;
import cn.emoney.pojo.vo.result.ProductConfig;
import cn.emoney.pojo.vo.result.ProductConfigResult;
import cn.emoney.pojo.vo.result.SignActivityConfig;
import cn.emoney.pojo.vo.result.SignActivityConfigVO;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.SignRecordService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SignRecordServiceImpl implements SignRecordService {

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private UserService userService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SignRecordMapper recordMapper;

    @Autowired
    private SignReceiveRecordMapper receiveRecordMapper;

    @Autowired
    private SignFeeCycleMapper feeCycleMapper;

    @Autowired
    private RedissonDistributionLock redissonDistributionLock;

    @Value("${productConfigUrl}")
    private String productConfigUrl;

    @Value("${signActivityConfigKey}")
    private String signActivityConfigKey;

    private final String CacheKey_HasSignPrivilege = "EMoney:Activity:HasSignPrivilege:";
    private final String CacheKey_SignRecordNum = "EMoney:Activity:SignRecordNum:";
    private final String CacheKey_TodaySignRecordFlag = "EMoney:Activity:TodaySignRecordFlag:";
    private final String CacheKey_SignReceiveRecordList = "EMoney:Activity:SignReceiveRecordList:";
    private final String CacheKey_LogisticOrderList = "EMoney:Activity:LogisticOrderList:";
    private final String CacheKey_CurrentWeekSignReceiveRecordFlag = "EMoney:Activity:CurrentWeekSignReceiveRecordFlag:";
    private final String CacheKey_SignRecordLock = "EMoney:Activity:SignRecordLock:";
    private final String CacheKey_SignActivityConfig = "EMoney:Activity:SignActivityConfig:";
    private final String CacheKey_SignActivityConfigList = "EMoney:Activity:SignActivityConfigList";

    /**
     * 自动签到打卡
     * @param uid
     * @param pid
     * @param date
     * @return
     */
    public Result<SignRecordVO> autoSign(String uid,String pid,Date date){
        SignRecordVO recordVO = null;

        //获取用户加密手机号和em帐号
        String account = "";
        String mobileX = "";
        List<BindAccountVO> list = userService.GetBindAccountList(uid);
        if (list != null && list.size() > 0) {
            for (BindAccountVO item : list) {
                if (item.AccountType.equals(0) &&
                        userService.GetAccountPID(item.AccountName).equals(pid)) {
                    account = item.AccountName;
                }

                if (item.AccountType.equals(1)) {
                    mobileX = item.EncryptMobile;
                }
            }
        }

        // 判断用户是否已绑定手机号
        if(StringUtils.isBlank(mobileX)){
            return Result.buildErrorResult("自动打卡异常：您的软件帐号未绑定手机号");
        }

        // 判断用户是否已绑定em帐号
        if(StringUtils.isBlank(account)){
            return Result.buildErrorResult("自动打卡异常：您的软件帐号未绑定EM帐号");
        }

        // 获取签到周期时间
        Calendar cld = Calendar.getInstance(Locale.CHINA);
        cld.setFirstDayOfWeek(Calendar.MONDAY);//以周一为首日
        cld.setTimeInMillis(date.getTime());//当前时间
        cld.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);//周一
        String beginTime = DateFormatUtil.date2String(cld.getTime(),DateFormatUtil.TIME_FORMAT_E);
        Date beginDate  = DateFormatUtil.string2Date(DateFormatUtil.date2String(cld.getTime(),DateFormatUtil.DATE_FORMAT),DateFormatUtil.DATE_FORMAT);
        cld.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);//周日
        String endTime = DateFormatUtil.date2String(cld.getTime(),DateFormatUtil.TIME_FORMAT_E);
        Integer year = cld.get(Calendar.YEAR);
        Integer week = cld.get(Calendar.WEEK_OF_YEAR);
        String signRecordNumKey = CacheKey_SignRecordNum + year + ":" + week + ":" + uid;
        String todaySignRecordFlag = CacheKey_TodaySignRecordFlag + DateFormatUtil.date2String(date,DateFormatUtil.TIME_FORMAT_D) + ":" + uid;
        String currentWeekSignReceiveRecordFlag = CacheKey_CurrentWeekSignReceiveRecordFlag + year + ":" + week + ":" + uid;

        // 判断用户本周是否已打卡三次
        Integer currentRecordNum = redisService.get(signRecordNumKey, Integer.class);
        if(currentRecordNum != null && currentRecordNum >= 3){
            recordVO = getSignRecordInfo(uid,currentRecordNum,null);
            return Result.buildSuccessResult("201","本周已领取7天使用期，请下周继续参与！",recordVO);
        }

        // 判断用户当日是否已打卡
        String recordFlag = redisService.get(todaySignRecordFlag, String.class);
        if(!StringUtils.isBlank(recordFlag) && recordFlag.equals("1")){
            recordVO = getSignRecordInfo(uid,currentRecordNum,null);
            return Result.buildSuccessResult(recordVO);
        }

        // 获取活动配置列表
        List<SignActivityConfig> signActivityConfigList = getSignActivityConfigList();
        //判断用户是否有打卡权限
        if(!hasSignPrivilege(uid,mobileX,account,date,beginDate,signActivityConfigList)){
            // 判断是否有正在进行中的活动
            List<SignActivityConfig> tempConfigList = signActivityConfigList.stream().filter(x -> x.getStatus().equals(1)).collect(Collectors.toList());
            if(tempConfigList!=null && tempConfigList.size() > 0){
                return Result.buildErrorResult(BaseResultCodeEnum.NO_OPERATE_PERMISSION.getCode(),"1");
            }else{
                return Result.buildErrorResult(BaseResultCodeEnum.NO_OPERATE_PERMISSION.getCode(),"0");
            }

        }

        String lockKey = CacheKey_SignRecordLock + uid;
        Long recordNum = null;
        try{
            if(redissonDistributionLock.tryLock(CacheKey_SignRecordLock + uid,TimeUnit.SECONDS, 10, 10)){
                // 设置打卡
                SignActivityConfigVO configVO = redisService.get(CacheKey_SignActivityConfig + uid, SignActivityConfigVO.class);
                if(configVO == null){
                    return Result.buildErrorResult("自动打卡异常：未获取到打卡活动配置信息");
                }
                SignActivityConfig signActivityConfig = configVO.getConfig();
                if(signActivityConfig == null){
                    return Result.buildErrorResult("自动打卡异常：未获取到打卡活动配置信息");
                }
                SignRecord record = new SignRecord();
                record.setUid(uid);
                record.setAccount(account);
                record.setMobile(mobileX);
                record.setActivityId(signActivityConfig.getActivityId());
                record.setYear(year);
                record.setWeek(week);
                record.setSignTime(date);
                record.setSignTimeZone(beginTime + "-" + endTime);
                int id = recordMapper.insert(record);
                String orderId = "";
                String detId = "";
                Date endDate = configVO.getSignEndDate();
                String logicCode = "";
                Integer useAge = 0;
                if(date.before(endDate) &&
                        DateFormatUtil.getDaysDiff(date,endDate) < 7){
                    logicCode = signActivityConfig.getPacCode8Days();
                    useAge = 8;
                }else{
                    logicCode = signActivityConfig.getPacCode7Days();
                    useAge = 7;
                }
                if(id > 0){
                    // 设置当天打卡标记
                    redisService.set(todaySignRecordFlag, "1",1L,TimeUnit.DAYS);
                    // 标记当前周打卡次数
                    recordNum = redisService.incrBy(signRecordNumKey);
                    redisService.expire(signRecordNumKey,10L,TimeUnit.DAYS);
                    // 满足打卡3次则赠送续费期
                    if(recordNum.equals(3L)){
                        // 判断当前周是否已领取过
                        String receiveRecordFlag = redisService.get(currentWeekSignReceiveRecordFlag, String.class);
                        if(!StringUtils.isBlank(receiveRecordFlag) && receiveRecordFlag.equals("1")){
                            recordVO = getSignRecordInfo(uid,recordNum == null ? 0 : recordNum.intValue(),null);
                            return Result.buildSuccessResult(recordVO);
                        }
                        // 获取订单详情
                        List<OrderProdListVO> orderProdList = redisService.getList(CacheKey_LogisticOrderList + uid, OrderProdListVO.class);
                        OrderProdListVO order = null;
                        if(orderProdList!=null && orderProdList.size() > 0){
                            order = orderProdList.get(0);
                            orderId = order.getORDER_ID();
                            detId = order.getDetID();
                            Boolean isSendOK = sendPrize(mobileX, logicCode, account, orderId, detId,signActivityConfig.getRemark());
                            if(!isSendOK){
                                recordVO = getSignRecordInfo(uid,recordNum == null ? 0 : recordNum.intValue(),null);
                                return Result.buildSuccessResult(recordVO);
                            }
                            SignReceiveRecord receiveRecord = new SignReceiveRecord();
                            receiveRecord.setUid(uid);
                            receiveRecord.setAccount(account);
                            receiveRecord.setMobile(mobileX);
                            receiveRecord.setActivityId(signActivityConfig.getActivityId());
                            receiveRecord.setYear(year);
                            receiveRecord.setWeek(week);
                            receiveRecord.setReceiveTime(date);
                            receiveRecord.setReceiveTimeZone(beginTime + "-" + endTime);
                            receiveRecord.setUseAge(useAge);
                            receiveRecord.setProductId(pid);
                            receiveRecord.setRemark(signActivityConfig.getRemark());
                            receiveRecord.setOrderId(orderId);
                            receiveRecord.setDetId(detId);
                            receiveRecord.setLogisticCode(logicCode);
                            int receiveId = receiveRecordMapper.insert(receiveRecord);
                            if(receiveId > 0){
                                redisService.set(currentWeekSignReceiveRecordFlag, "1",7L,TimeUnit.DAYS);
                                refreshSignReceiveRecordInfo(uid);
                            }
                        }
                    }
                }
            }
        }catch (Exception ex){
            log.error("SignRecordServiceImpl.autoSign打卡异常",ex);
        }finally {
            redissonDistributionLock.unlock(lockKey);
        }
        recordVO = getSignRecordInfo(uid,recordNum == null ? 0 : recordNum.intValue(),null);
        return Result.buildSuccessResult(recordVO);
    }

    /**
     * 续费延期7天
     * @param mobile0x
     * @param activityCode
     * @param em
     * @param orderId
     * @param detId
     * @return
     */
    public Boolean sendPrize(String mobile0x, String activityCode, String em, String orderId, String detId,String remark) {

        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(activityCode);
        sendPrivilegeDTO.setReason(remark);
        sendPrivilegeDTO.setApplyUserID("scb_public");

        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();
        createActivityGrantApplyAccountDTO.setAccountType(1);//关联账号类型（1：EM号，2：手机号）
        createActivityGrantApplyAccountDTO.setAccount(em);
        createActivityGrantApplyAccountDTO.setMID(mobile0x);
        createActivityGrantApplyAccountDTO.setOrderID(orderId);
        createActivityGrantApplyAccountDTO.setOrderDetailID(detId);

        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        try {
            Boolean resultSendPrivilege = logisticsService.sendPrivilege(sendPrivilegeDTO);

            if (resultSendPrivilege) {
                log.info("接口调用赠送成功,mobile0x号:" + mobile0x);
                return true;
            } else {
                log.info("接口调用赠送失败,mobile0x号:" + mobile0x);
            }
        }catch (Exception e) {
            log.error("抽奖发送奖品失败 . Error:"+e.getMessage());
        }
        return false;
    }

    /**
     *
     * @param uid
     * @param recordNum
     * @param list
     * @return
     */
    private SignRecordVO getSignRecordInfo(String uid, Integer recordNum,
                                           List<SignReceiveRecordVO> list){
        SignRecordVO record = new SignRecordVO();
        record.setRecordNum(recordNum);
        if(list == null){
            list = getSignReceiveRecordInfo(uid);
        }
        record.setReceiveRecordList(list);
        return record;
    }

    /**
     * 刷新领取记录信息
     * @param uid
     * @return
     */
    private List<SignReceiveRecordVO> getSignReceiveRecordInfo(String uid){
        String cacheKey = CacheKey_SignReceiveRecordList + uid;
        List<SignReceiveRecordVO> list = redisService.getList(cacheKey,SignReceiveRecordVO.class);
        if(list == null){
            list = refreshSignReceiveRecordInfo(uid);
        }
        return list;
    }

    /**
     * 刷新领取记录信息
     * @param uid
     * @return
     */
    private List<SignReceiveRecordVO> refreshSignReceiveRecordInfo(String uid){
        String cacheKey = CacheKey_SignReceiveRecordList + uid;
        List<SignReceiveRecordVO> list = receiveRecordMapper.findAllByUId(uid);
        if(list!=null && list.size() > 0){
            redisService.set(cacheKey,list,1L,TimeUnit.DAYS);
        }
        return list;
    }

    /**
     * 判断用户是否有打卡权限
     * @param uid
     * @param mobileX
     * @param beginTime
     * @return
     */
    @Override
    public Boolean hasSignPrivilege(String uid, String mobileX, String account,Date now,
                                    Date beginTime,List<SignActivityConfig> signActivityConfigList){
        String hasPrivilege = redisService.get(CacheKey_HasSignPrivilege + uid,String.class);
        if(!StringUtils.isBlank(hasPrivilege) && "1".equals(hasPrivilege)){
            return true;
        }else if(!StringUtils.isBlank(hasPrivilege) && "0".equals(hasPrivilege)){
            return false;
        }else {
            List<SignActivityConfig> tempConfigList = null;
            SignActivityConfig config = null;
            SignActivityConfigVO configVO = new SignActivityConfigVO();
            if(signActivityConfigList != null && signActivityConfigList.size() > 0) {
                //获取最新计费周期信息
                SignFeeCycleVO info = feeCycleMapper.findLatestFeeCycleByUId(uid);
                // 判断当前计费周期是已过期
                // 未过期用户
                if(info != null && now.before(info.getFeeEndTime())) {
                    // 未过期用户
                    tempConfigList = signActivityConfigList.stream().filter(x -> x.getActivityId().equals(info.getActivityId())).collect(Collectors.toList());
                    if (tempConfigList != null && tempConfigList.size() > 0) {
                        config = tempConfigList.get(0);
                    }
                    // 获取是否购买物流包
                    if(hasBuyOrder(uid,mobileX,config)){
                        // 已购买物流包用户
                        configVO.setConfig(config);
                        configVO.setSignEndDate(info.getFeeEndTime());
                        redisService.set(CacheKey_HasSignPrivilege + uid, "1", 10L, TimeUnit.MINUTES);
                        redisService.set(CacheKey_SignActivityConfig + uid,configVO,10L, TimeUnit.MINUTES);
                        return true;
                    }
                }

                // 未获取到计费卡信息用户或者过期用户
                tempConfigList = signActivityConfigList.stream().filter(x -> x.getActivityId() > (info == null ? 0 : info.getActivityId())).collect(Collectors.toList());
                if(tempConfigList != null && tempConfigList.size() > 0){
                    for(SignActivityConfig tConfig : tempConfigList){
                        if(hasBuyOrder(uid,mobileX,tConfig)){
                            // 已购买物流包用户
                            String strEndTime = DateFormatUtil.date2String(DateFormatUtil.addDays(beginTime, tConfig.getSignWeek() * 7 - 1), DateFormatUtil.DATE_FORMAT) + " 23:59:59";
                            Date endTime = DateFormatUtil.string2Date(strEndTime,DateFormatUtil.TIME_FORMAT_O);
                            // 生成计费表
                            generateFeeCycle(uid,mobileX,account,now,beginTime,endTime, tConfig);
                            configVO.setConfig(tConfig);
                            configVO.setSignEndDate(endTime);
                            redisService.set(CacheKey_HasSignPrivilege + uid, "1", 10L, TimeUnit.MINUTES);
                            redisService.set(CacheKey_SignActivityConfig + uid,configVO,10L, TimeUnit.MINUTES);
                            return true;
                        }
                    }
                }
                redisService.set(CacheKey_HasSignPrivilege + uid, "0", 10L, TimeUnit.MINUTES);
            }
            return false;
        }
    }

    /**
     * 生成计费周期信息
     * @param uid
     * @param mobileX
     * @param beginTime
     * @param tConfig
     * @return
     */
    private Integer generateFeeCycle(String uid,String mobileX,String account,Date date,
                                     Date beginTime, Date endTime, SignActivityConfig tConfig){
        // 判断当期活动是否已写入计费信息
        Map<String,Object> map = new HashMap<>();
        map.put("uid",uid);
        map.put("actId",tConfig.getActivityId());
        Integer count = feeCycleMapper.selectFeeCount(map);
        if(count > 0){
            return 1;
        }
        SignFeeCycle feeCycle = new SignFeeCycle();
        feeCycle.setActivityId(tConfig.getActivityId());
        feeCycle.setUid(uid);
        feeCycle.setMobile(mobileX);
        feeCycle.setAccount(account);
        feeCycle.setActivityCode(tConfig.getActCode());
        feeCycle.setFeeBeginTime(beginTime);
        feeCycle.setFeeEndTime(endTime);
        feeCycle.setFeeWeek(tConfig.getSignWeek());
        feeCycle.setCreateTime(date);
        feeCycle.setUpdateTime(date);
        feeCycle.setIsValid(1);
        return feeCycleMapper.insert(feeCycle);
    }

    /**
     * 批量补发试用期
     */
    @Override
    public void batchCompensateSendPrivilege(){
        // 获取本周时间范围
        Date date = new Date();//DateFormatUtil.string2Date("2022-07-06",DateFormatUtil.DATE_FORMAT);
        Calendar cld = Calendar.getInstance(Locale.CHINA);
        cld.setFirstDayOfWeek(Calendar.MONDAY);//以周一为首日
        cld.setTimeInMillis(date.getTime());//当前时间
        cld.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);//周一
        String beginTimeStr = DateFormatUtil.date2String(cld.getTime(),DateFormatUtil.TIME_FORMAT_E);
        cld.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);//周日
        String endTimeStr = DateFormatUtil.date2String(cld.getTime(),DateFormatUtil.TIME_FORMAT_E);
        Integer year = cld.get(Calendar.YEAR);
        Integer week = cld.get(Calendar.WEEK_OF_YEAR);
        // 获取本周已打卡3次但未领取使用期用户
        String timeZone = beginTimeStr + "-" + endTimeStr;
        List<NoReceiveRecordVO> noReceiveRecordList = recordMapper.selectNoReceiveRecordList(timeZone);
        String currentWeekSignReceiveRecordFlag = "";
        // 获取打卡活动配置列表
        List<SignActivityConfig> signActivityConfigList = getSignActivityConfigList();
        List<SignActivityConfig> tempConfigList = null;
        SignActivityConfig config = null;
        OrderProdListVO order = null;
        SignFeeCycleVO feeCycle = null;
        String logicCode = "";
        Integer useAge = 0;
        // 遍历未发放使用期用户列表并执行发放逻辑
        if(noReceiveRecordList!=null && noReceiveRecordList.size() > 0){
            for(NoReceiveRecordVO record : noReceiveRecordList){
                // 判断当前周是否已领取过
                currentWeekSignReceiveRecordFlag = CacheKey_CurrentWeekSignReceiveRecordFlag + year + ":" + week + ":" + record.getUid();
                String receiveRecordFlag = redisService.get(currentWeekSignReceiveRecordFlag, String.class);
                if(!StringUtils.isBlank(receiveRecordFlag) && receiveRecordFlag.equals("1")){
                    continue;
                }
                // 获取用户当前周活动配置信息
                tempConfigList = signActivityConfigList.stream().filter(x -> x.getActivityId().equals(record.getActivityId())).collect(Collectors.toList());
                if (tempConfigList != null && tempConfigList.size() > 0) {
                    config = tempConfigList.get(0);
                    //获取物流订单
                    String[] arrayActCode = config.getActCode().split(",");
                    List<OrderProdListVO> orderProdList = null;
                    if(arrayActCode !=null && arrayActCode.length > 0){
                        for(String actCode : arrayActCode){
                            OrderProdListDTO orderProdListDTO = new OrderProdListDTO();
                            orderProdListDTO.setStockUpDate_Start(config.getBeginTime());
                            orderProdListDTO.setStockUpDate_End(config.getEndTime());
                            orderProdListDTO.setRefund_Sign(0);
                            orderProdListDTO.setMIDPWD(record.getMobile());
                            orderProdListDTO.setACTIVITY_CODE(actCode);
                            //查询订单信息
                            orderProdList = logisticsService.queryOrderProdList(orderProdListDTO);
                            if (orderProdList != null && orderProdList.size() > 0){
                                break;
                            }
                        }
                    }

                    if (orderProdList != null && orderProdList.size() > 0){
                        order = orderProdList.get(0);
                        String orderId = order.getORDER_ID();
                        String detId = order.getDetID();
                        //获取最新计费周期信息
                        feeCycle = feeCycleMapper.findLatestFeeCycleByUId(record.getUid());
                        if(feeCycle!=null){
                            Date signEndDate = feeCycle.getFeeEndTime();
                            if(date.before(signEndDate) &&
                                    DateFormatUtil.getDaysDiff(date,signEndDate) < 7){
                                logicCode = config.getPacCode8Days();
                                useAge = 8;
                            }else{
                                logicCode = config.getPacCode7Days();
                                useAge = 7;
                            }
                            Boolean isSendOK = sendPrize(record.getMobile(), logicCode, record.getAccount(), orderId, detId,config.getRemark());
                            if(isSendOK){
                                SignReceiveRecord receiveRecord = new SignReceiveRecord();
                                receiveRecord.setUid(record.getUid());
                                receiveRecord.setAccount(record.getAccount());
                                receiveRecord.setMobile(record.getMobile());
                                receiveRecord.setActivityId(config.getActivityId());
                                receiveRecord.setYear(year);
                                receiveRecord.setWeek(week);
                                receiveRecord.setReceiveTime(new Date());
                                receiveRecord.setReceiveTimeZone(timeZone);
                                receiveRecord.setUseAge(useAge);
                                receiveRecord.setProductId("*********");
                                receiveRecord.setRemark(config.getRemark());
                                receiveRecord.setOrderId(orderId);
                                receiveRecord.setDetId(detId);
                                receiveRecord.setLogisticCode(logicCode);
                                int receiveId = receiveRecordMapper.insert(receiveRecord);
                                if(receiveId > 0){
                                    redisService.set(currentWeekSignReceiveRecordFlag, "1",7L,TimeUnit.DAYS);
                                    refreshSignReceiveRecordInfo(record.getUid());
                                }
                            }
                        }
                    }
                }
            }
        }

    }

    /**
     * 批量更新用户计费信息
     */
    public void batchRefreshSignFee(){
        List<String> list = recordMapper.selectAllUId();
        List<SignActivityConfig> signActivityConfigList = getSignActivityConfigList();
        SignActivityConfig config = signActivityConfigList.get(0);
        SignRecord record = null;
        if(list!=null && list.size() > 0){
            for(String uid : list){
                record = recordMapper.selectOneByUId(uid);
                if(record!=null){
                    Date date = new Date();
                    Date signTime = record.getSignTime();
                    Calendar cld = Calendar.getInstance(Locale.CHINA);
                    cld.setFirstDayOfWeek(Calendar.MONDAY);//以周一为首日
                    cld.setTimeInMillis(signTime.getTime());//当前时间
                    cld.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);//周一
                    Date beginTime = cld.getTime();
                    beginTime = DateFormatUtil.string2Date(DateFormatUtil.date2String(beginTime,DateFormatUtil.DATE_FORMAT),DateFormatUtil.DATE_FORMAT);
                    String strEndTime = DateFormatUtil.date2String(DateFormatUtil.addDays(beginTime, config.getSignWeek() * 7 - 1), DateFormatUtil.DATE_FORMAT) + " 23:59:59";
                    Date endTime = DateFormatUtil.string2Date(strEndTime,DateFormatUtil.TIME_FORMAT_O);
                    generateFeeCycle(uid,record.getMobile(),record.getAccount(),date,beginTime,endTime,config);
                }

            }
        }
    }

    /**
     * 判断用户是否购买物流订单
     * @param uid
     * @param mobileX
     * @param config
     * @return
     */
    private Boolean hasBuyOrder(String uid, String mobileX, SignActivityConfig config){
        // 判断配置是否为空
        if(config == null){
            return false;
        }
        String[] arrayActCode = config.getActCode().split(",");
        if(arrayActCode!=null && arrayActCode.length > 0){
            for (String actCode : arrayActCode) {
                //获取物流订单
                OrderProdListDTO orderProdListDTO = new OrderProdListDTO();
                orderProdListDTO.setStockUpDate_Start(config.getBeginTime());
                orderProdListDTO.setStockUpDate_End(config.getEndTime());
                orderProdListDTO.setRefund_Sign(0);
                orderProdListDTO.setMIDPWD(mobileX);
                orderProdListDTO.setACTIVITY_CODE(actCode);

                //查询订单信息
                List<OrderProdListVO> orderProdListVOS = logisticsService.queryOrderProdList(orderProdListDTO);
                if (orderProdListVOS != null && orderProdListVOS.size() > 0) {
                    redisService.set(CacheKey_LogisticOrderList + uid, orderProdListVOS, 10L, TimeUnit.MINUTES);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取小智盈打卡续费活动配置列表
     * @return
     */
    private List<SignActivityConfig> getSignActivityConfigList(){

        List<SignActivityConfig> list = redisService.getList(CacheKey_SignActivityConfigList,SignActivityConfig.class);
        if(list == null){
            ProductConfig productConfig = getProductConfig(signActivityConfigKey);
            if(productConfig!=null &&
                    !StringUtils.isBlank(productConfig.getConfigContent())){
                list = JsonUtil.toBeanList(productConfig.getConfigContent(),SignActivityConfig.class);
                redisService.set(CacheKey_SignActivityConfigList,list,10L,TimeUnit.MINUTES);
            }
        }
        return list;
    }

    /**
     * 获取产品配置
     */
    private ProductConfig getProductConfig(String configKey){
        ProductConfig config = null;
        String url = MessageFormat.format(productConfigUrl, configKey);
        String ret = OkHttpUtil.get(url, null);
        ProductConfigResult result = JSON.parseObject(ret, ProductConfigResult.class);
        if(result!=null && "0".equals(result.getRetCode()) && result.getMessage() != null){
            config = result.getMessage();
        }
        return config;
    }
}
