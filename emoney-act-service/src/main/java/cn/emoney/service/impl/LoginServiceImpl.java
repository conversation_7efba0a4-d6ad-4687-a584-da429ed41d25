package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.utils.CookieUtils;
import cn.emoney.common.utils.RSAUtils;
import cn.emoney.pojo.vo.AccountVO;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.pojo.vo.UserLoginIdInfoVO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LoginService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.*;
import java.security.spec.KeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-24
 */
@Slf4j
@Service
public class LoginServiceImpl implements LoginService {
    @Autowired
    private RedisService redisService;

    @Autowired
    private UserService userService;

    private String cookie_pre = "EMoney.Activity.";

    private int cookieAge = 60 * 30;
    private int redisDuration = 60 * 30;

    private KeyPair keyPair;

    private final String DOMAIN = "emoney.cn";

    /**
     * 获取用户登录信息
     *
     * @param actCode
     * @return cn.emoney.activityweb.repository.dao.entity.vo.LoginUserInfoVO
     * <AUTHOR>
     * @date 2021/12/24 16:21
     */
    @Override
    public LoginUserInfoVO GetLoginUserInfo(HttpServletRequest request, String actCode) {
        String cookieKey = cookie_pre + actCode;
        String currUid = request.getParameter("uid");
        String val = CookieUtils.getValue(request, cookieKey);

        if (val != null && !val.isEmpty()) {
            if(StringUtils.isEmpty(currUid) || val.equals(currUid)){
                String redisKey = RedisConstants.Redis_Pre_Activity + actCode + ":" + val;
                return redisService.get(redisKey, LoginUserInfoVO.class);
            }
        }
        return null;
    }

    /**
     * 登录
     * <AUTHOR>
     * @date 2023/9/25 19:52
     * @param request
     * @param response
     * @param actCode
     * @param loginUserInfoVO
     * @return cn.emoney.pojo.vo.LoginUserInfoVO
     */
    @Override
    public LoginUserInfoVO SetLoginUserInfo(HttpServletRequest request, HttpServletResponse response, String actCode, LoginUserInfoVO loginUserInfoVO) {
        if (loginUserInfoVO == null) {
            return null;
        }
        if ("********".equals(actCode)) {
            cookieAge = 60 * 60 * 24;
            redisDuration = 60 * 60 * 24;
        }
        String cookieKey = cookie_pre + actCode;
        String uid = loginUserInfoVO.uid;
        String mobileX = loginUserInfoVO.mobileX==null?loginUserInfoVO.account:loginUserInfoVO.mobileX;
        String pid = loginUserInfoVO.pid;
        String redisKey = RedisConstants.Redis_Pre_Activity + actCode + ":";
        //传入uid
        if (uid != null && !StringUtils.isEmpty(uid)) {
            redisKey = redisKey + uid;
            CookieUtils.set(response, cookieKey, uid, true, cookieAge);

            List<BindAccountVO> list = userService.GetBindAccountList(loginUserInfoVO.uid);
            if (list != null) {
                for (BindAccountVO item : list) {
                    if (item.AccountType.equals(0)) {
                        loginUserInfoVO.account = item.AccountName;
                    }
                    if (item.AccountType.equals(1)) {
                        loginUserInfoVO.mobileX = item.EncryptMobile;
                        loginUserInfoVO.maskMobile = item.AccountName;
                    }
                }
            }
        }

        //未传入uid 根据密文手机/em号获取uid
        if (!StringUtils.isEmpty(mobileX) && (uid == null || StringUtils.isEmpty(uid))) {
            //根据密文手机号获取用户uid
            UserLoginIdInfoVO loginIDInfo = userService.GetLoginIDInfoByAccount(mobileX);
            if (loginIDInfo != null) {
                loginUserInfoVO.uid = loginIDInfo.getPID().toString();
                redisKey = redisKey + loginUserInfoVO.uid;
                CookieUtils.set(response, cookieKey, loginUserInfoVO.uid, true, cookieAge);

                List<BindAccountVO> list = userService.GetBindAccountList(loginUserInfoVO.uid);
                if (list != null) {
                    for (BindAccountVO item : list) {
                        if (item.AccountType.equals(0)) {
                            loginUserInfoVO.account = item.AccountName;
                        }
                        if (item.AccountType.equals(1)) {
                            loginUserInfoVO.mobileX = item.EncryptMobile;
                            loginUserInfoVO.maskMobile = item.AccountName;
                        }
                    }
                }
            } else {
                redisKey = redisKey + mobileX;
                CookieUtils.set(response, cookieKey, mobileX, true, cookieAge);
            }
        }

        //无pid 有密文手机 查pid
        if ((pid == null || StringUtils.isEmpty(pid)) && !StringUtils.isEmpty(mobileX)) {
            List<AccountVO> emList = userService.queryAccountListByAccount(mobileX);

            for (AccountVO item : emList) {
                String maskMobile = item.getMaskMobile();
                LocalDateTime endDateStr = item.getEndDate();
                String getPid = item.getPid().toString();
                if (!StringUtils.isEmpty(maskMobile)) {
                    loginUserInfoVO.maskMobile = item.getMaskMobile();
                }

                if (endDateStr != null) {
                    try {
                        if (endDateStr.isAfter(LocalDateTime.now())) {
                            pid = getPid;
                            break;
                        } else {
                            if (getPid.indexOf("888") > -1) {
                                pid = getPid;
                                break;
                            } else {
                                if (item.getPid() > *********) {
                                    pid = getPid;
                                }
                            }
                        }
                    } catch (Exception exp) {
                    }
                }
            }
            loginUserInfoVO.pid = pid;
        }

        redisService.set(redisKey, loginUserInfoVO, (long) redisDuration);

        return loginUserInfoVO;
    }

    /***
     * 功能描述:
     * 获取公钥信息，并存储对应的私钥
     * @Param: []
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2022/3/28 09:52
     */
    @Override
    public String getPublicKey() {
        String rsaCacheKey = "DynamicRSAKey";
        String curPubKey = "";

        try {
            String uniKey = redisService.get(rsaCacheKey+"_Pub",String.class);
            if (uniKey!=null && !"".equals(uniKey)){
                return uniKey;
            }

            curPubKey = RSAUtils.getPublicKey();
            keyPair = RSAUtils.getCurKeyPair();

            PrivateKey privateKey = keyPair.getPrivate();
            String privateKeyStr = new String(Base64.encodeBase64(privateKey.getEncoded()));

            if (!"".equals(curPubKey) && keyPair!=null){
                redisService.set(rsaCacheKey+"_Pub",curPubKey, (long) (60 * 5));
                redisService.set(rsaCacheKey+"_Pri",privateKeyStr, (long) (60 * 5));
                return curPubKey;
            }

        }catch (Exception e) {
            log.error("Store RSA key data failed",e);
        }

        return curPubKey;
    }


    /***
     * 功能描述:
     * 按照约定的当前最新，获取私钥后解密密文
     * @Param: [encryptText]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2022/3/28 09:52
     */

    @Override
    public String decryptWithPrivateKey(String encryptText) throws Exception {

        String rsaCacheKey = "DynamicRSAKey";

        String rsaPriKey = redisService.get(rsaCacheKey+"_Pri",String.class);

        return decrypt(encryptText,rsaPriKey);
/*
        if (encryptText==null || "".equals(encryptText)) {
            return null;
        }

        byte[] en_byte = Base64.decodeBase64(encryptText.getBytes());

        Provider provider = new org.bouncycastle.jce.provider.BouncyCastleProvider();
        Security.addProvider(provider);
        Cipher ci = Cipher.getInstance("RSA/ECB/PKCS1Padding", provider);

        keyPair = redisService.get(rsaCacheKey+"_KP",KeyPair.class);
        PrivateKey privateKey = keyPair.getPrivate();
        ci.init(Cipher.DECRYPT_MODE, privateKey);

        byte[] res = ci.doFinal(en_byte);
        return new String(res);*/

    }

    /***
     * 功能描述:
     * PrivateKey + 密文 解密
     * @Param: str,privateKey
     * @Return: str
     * @Author: tengdengming
     * @Date: 2022/3/28 09:50
     */
    public static String decrypt(String str, String privateKey) throws Exception{
        //64位解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(str.getBytes("UTF-8"));
        //base64编码的私钥
        byte[] decoded = Base64.decodeBase64(privateKey);

        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        KeySpec privateKeySpec = new PKCS8EncodedKeySpec(decoded);
        PrivateKey priKey = keyFactory.generatePrivate(privateKeySpec);
        //RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(decoded));

        try {
            //RSA解密
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, (Key)priKey);
            String outStr = new String(cipher.doFinal(inputByte));
            return outStr;
        }catch (Exception e) {
            log.error("DECRYPT Error occur",e);
        }
        return null;
    }

}
