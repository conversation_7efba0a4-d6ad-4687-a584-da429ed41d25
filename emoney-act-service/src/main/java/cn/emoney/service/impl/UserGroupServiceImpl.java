package cn.emoney.service.impl;

import cn.emoney.common.result.UserRadarResult;
import cn.emoney.service.UserGroupService;
import cn.emoney.service.config.UserGroupProperties;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Streams;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@EnableConfigurationProperties(UserGroupProperties.class)
public class UserGroupServiceImpl implements UserGroupService {
    private final UserGroupProperties properties;
    private final RestTemplate restTemplate;
    private final LoadingCache<Integer, Set<Long>> groupUsersCache;

    public UserGroupServiceImpl(UserGroupProperties properties, RestTemplate restTemplate) {
        this.properties = properties;
        this.restTemplate = restTemplate;
        this.groupUsersCache = Caffeine.newBuilder()
                .maximumSize(1024)
                .initialCapacity(128)
                .softValues()
                .expireAfterWrite(Duration.ofMinutes(60))
                .refreshAfterWrite(Duration.ofMinutes(10))
                .build(this::getGroupUsers);
    }

    @Override
    public Map<Integer, Boolean> checkGroupsWithResult(Long uid, List<Integer> groups) {
        if (groups == null || groups.isEmpty()) {
            return Collections.emptyMap();
        }
        LinkedHashSet<Integer> groupSet = new LinkedHashSet<>(groups);
        // 本地缓存检查
        List<Set<Long>> caches = groupSet.stream().map(groupUsersCache::getIfPresent)
//                .takeWhile(n -> n != null)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (groupSet.size() == caches.size()) {
            return Streams.zip(groupSet.stream(), caches.stream(),
                            (groupId, cache) -> new AbstractMap.SimpleEntry<>(groupId, cache.contains(uid))
                    )
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        }

        CheckUserGroupResponse response = restTemplate.postForObject(properties.getCheckGroup(),
                new CheckUserGroupRequest(uid, groupSet.stream().map(UserGroup::new).collect(Collectors.toList())),
                CheckUserGroupResponse.class);
        Assert.state(response != null && response.isSuccess(), "API调用失败");
        return Objects.requireNonNull(response.getData(), "API返回数据为空")
                .getUserGroupList().stream().collect(Collectors.toMap(UserGroup::getGroupId, UserGroup::getCheckResult));
    }

    @Override
    public Set<Long> getGroupUsers(Integer group) {
        ListGroupUserResponse response = restTemplate.getForObject(properties.getListGroupUser(), ListGroupUserResponse.class, group);
        Assert.state(response != null && response.isSuccess(), "API调用失败");
        return Objects.requireNonNull(response.getData(), "API返回数据为空").stream()
                .map(str -> {
                    try {
                        return Long.parseLong(str);
                    } catch (NumberFormatException e) {
                        log.error("无法解析用户ID: {}", str);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    @Override
    public boolean anyMatch(Long uid, List<Integer> groups) {
        boolean localMatch = groups.stream().map(groupUsersCache::getIfPresent)
                .filter(Objects::nonNull)
                .anyMatch(s -> s.contains(uid));
        if (localMatch) {
            return true;
        }
        return UserGroupService.super.anyMatch(uid, groups);
    }

    @Override
    public void preloadGroupUsers(Integer group) {
//        this.groupUsersCache.refresh(group);
        this.groupUsersCache.get(group);
    }

    @Data
    private static class UserGroup {
        private Integer groupId;
        private Boolean checkResult;

        public UserGroup() {
        }

        public UserGroup(Integer groupId) {
            this.groupId = groupId;
            this.checkResult = false;
        }
    }

    @Data
    private static class CheckUserGroupRequest {
        private String uid;
        private List<UserGroup> userGroupList;

        public CheckUserGroupRequest() {
        }

        public CheckUserGroupRequest(Long uid, List<UserGroup> userGroupList) {
            this.uid = String.valueOf(uid);
            this.userGroupList = userGroupList;
        }
    }

    private static class CheckUserGroupResponse extends UserRadarResult<CheckUserGroupRequest> {
    }

    private static class ListGroupUserResponse extends UserRadarResult<List<String>> {
    }
}
