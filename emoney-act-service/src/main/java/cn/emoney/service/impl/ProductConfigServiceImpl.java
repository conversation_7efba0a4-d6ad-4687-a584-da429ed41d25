package cn.emoney.service.impl;

import cn.emoney.common.result.ResultInfo;
import cn.emoney.pojo.vo.result.ProductConfig;
import cn.emoney.service.ProductConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class ProductConfigServiceImpl implements ProductConfigService {
    private final RestTemplate restTemplate;

    @Value("${productConfigUrl}")
    private String productConfigUrl;

    public ProductConfigServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    /**
     * 获取产品配置
     * 对端没有协商缓存, 请求资源消耗很大
     *
     * @param configKey
     * @return
     */
    @Override
    public ProductConfig getConfig(String configKey) {
        ResponseEntity<ResultInfo<ProductConfig>> exchange = restTemplate.exchange(
                productConfigUrl,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<ResultInfo<ProductConfig>>() {
                },
                configKey);
        ResultInfo<ProductConfig> result = exchange.getBody();
        if (result != null && result.isSuccess()) {
            return result.getMessage();
        }
        log.error("get product config error, configKey: {}, result: {}", configKey, result);
//        throw new RuntimeException("get product config error");
        return null;
    }
}
