package cn.emoney.service.impl;

import cn.emoney.mapper.activity.GroupInfoMapper;
import cn.emoney.pojo.GroupInfoDO;
import cn.emoney.service.GroupInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-11-29
 */
@Service
public class GroupInfoServiceImpl implements GroupInfoService {

    @Autowired
    private GroupInfoMapper groupInfoMapper;

    @Override
    public List<GroupInfoDO> getAll() {
        return groupInfoMapper.getAll();
    }

    @Override
    public GroupInfoDO getById(Integer id) {
        return groupInfoMapper.getById(id);
    }
}
