package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.PointResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.common.utils.RedissonDistributionLock;
import cn.emoney.mapper.activity.Act588benifitrecordMapper;
import cn.emoney.mapper.activity.FirstClassViewRecordMapper;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.bo.*;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.pojo.vo.result.ProductConfig;
import cn.emoney.pojo.vo.result.ProductConfigResult;
import cn.emoney.service.*;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023-05-18
 */
@Service
@Slf4j
public class Benefit588Impl implements Benefit588Service {

    @Autowired
    private RedisService redisService;

//    @Value("${benefitListConfigKey}")
//    private String benefitListConfigKey;
    @Value("${benefitListConfigKeyNew:}")
    private String benefitListConfigKey;

    @Autowired
    private PointService pointService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private ProductConfigService productConfigService;

    @Autowired
    private UserService userService;

    @Autowired(required = false)
    private Act588benifitrecordMapper act588benifitrecordMapper;

    @Autowired
    private RedissonDistributionLock redissonDistributionLock;

    private String rediskey_getrecord= RedisConstants.Redis_Pre_Activity + "benefit588:getrecord:";
    private String CacheKey_getBenefitLock = RedisConstants.Redis_Pre_Activity + "benefit588:getBenefitLock:";
    private final String CacheKey_SendPPLock = RedisConstants.RedisKey_Pre_Activity + "20231127:SendPPLock";
    private final String CacheKey_SendPPLock_renew588 = RedisConstants.RedisKey_Pre_Activity + "renew588:SendPPLock";
    /**领取30积分和30优惠券标识code*/
    private final static String isSendPP_CODE = "renew20231127_issendpp";
    private final static String point30TaskID = "1725055552973324288";
    private final static String activityID_coupon30 = "cp-1231116153751114";


    /**
     * 获取福利列表
     * <AUTHOR>
     * @date 2023/5/18 14:00
     * @param uid
     * @param actCode
     * @return java.util.List<cn.emoney.pojo.bo.Benefit588DTO>
     */
    @Override
    public List<Benefit588DTO> getBenefitList(String uid, String actCode) {
        List<Benefit588DTO> list = new ArrayList<>();
        ProductConfig config = productConfigService.getConfig(benefitListConfigKey);
        if(config!=null) {
            String classlistStr = config.getConfigContent();
            list = JsonUtil.toBeanList(classlistStr, Benefit588DTO.class);
        }
        //用户状态
        if (!StringUtils.isEmpty(uid)) {
            for (Benefit588DTO item :
                    list) {
                item.hasBenefitRecord = HasBenefitRecord(uid, item.id.toString(),actCode,item.dayOfMonth);
            }
        }
        return list;
    }

    /**
     * 是否有领取记录
     * <AUTHOR>
     * @date 2023/5/19 14:22
     * @param uid
     * @param benefitID
     * @param actCode
     * @return boolean
     */
    public boolean HasBenefitRecord(String uid,String benefitID,String actCode,String dayOfMonth) {
        String key = rediskey_getrecord + uid + ":" + actCode;

        Object obj = redisService.hashGet(key, benefitID);
        if (obj != null && obj.toString().length() > 0 && obj.toString().equals(dayOfMonth)) {
            return true;
        }
        return false;
    }
    /**
     * 领取记录存入redis
     * <AUTHOR>
     * @date 2023/5/22 16:16
     * @param uid
     * @param benefitID
     * @param actCode
     * @param dayOfMonth
     */
    public void SetBenefitRecord(String uid,String benefitID,String actCode,String dayOfMonth) {
        String key = rediskey_getrecord + uid + ":" + actCode;

        redisService.hashSet(key, benefitID,dayOfMonth);
    }

    /**
     * 领取福利
     * <AUTHOR>
     * @date 2023/5/19 14:23
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @Override
    public Result<String> getBenefit(String uid,String pid,String benefitID,String actCode,String dayOfMonth,String source) {
        String lockKey = CacheKey_getBenefitLock + actCode + uid;
        Result<String> ret = new Result<>();

        try {
            if (redissonDistributionLock.tryLock(lockKey, TimeUnit.SECONDS, 30)) {

                log.info("getBenefit线程名称:" + Thread.currentThread().getName() + " 进入时间:" + System.currentTimeMillis() + " uid:" + uid);

                //判断是否已领取当天福利
                if (HasBenefitRecord(uid, benefitID, actCode, dayOfMonth)) {
                    return Result.buildErrorResult("-1","当日已领取");
                }

                String benefitType = "";
                //获取福利类型-根据天数
                List<Benefit588DTO> list = getBenefitList("", actCode);
                Benefit588DTO benefit588DTO = list.stream().filter(x -> x.dayOfMonth.equals(dayOfMonth)).findFirst().get();
                if (benefit588DTO != null) {
                    benefitType = benefit588DTO.benefitType.toString();
                } else {
                    return Result.buildErrorResult("-1","未获取到福利信息");
                }

                ///发送福利
                //1：送积分
                //2：送特权
                //3：送优惠券
                //4：积分兑换商品
                //5：使用期
                switch (benefitType) {
                    case "1":
                        PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                        requestDTO.platform = "1";
                        requestDTO.pid = pid;
                        requestDTO.uid = uid;
                        requestDTO.subId = "";
                        requestDTO.taskId = benefit588DTO.benefitTaskCode;

                        boolean pointRet = pointService.pointRecordAdd(requestDTO);
                        if (pointRet) {
                            ret.setCode("200");
                            ret.setSuccess(true);
                            ret.setMsg("积分赠送成功");
                        } else {
                            ret.setSuccess(false);
                            ret.setMsg("积分赠送失败");
                        }
                        break;
                    case "2":
                        ret = sendPrivilegeCommon(uid, pid, "小智盈续费领福利_" + benefit588DTO.benefitName, benefit588DTO.benefitTaskCode, 2);
                        break;
                    case "3":
                        ret = sendCouponCommon(uid,pid, benefit588DTO.benefitTaskCode, Double.parseDouble(benefit588DTO.benefitName1), actCode);
                        break;
                    case "4":
                        //20230721-需求变更
                        //兑换商品之前发放10积分-领取成功兑换商品 领取失败提示兑换失败
                        PointRecordAddRequestDTO requestDTO1 = new PointRecordAddRequestDTO();
                        requestDTO1.platform = "1";
                        requestDTO1.pid = pid;
                        requestDTO1.uid = uid;
                        requestDTO1.subId = "";
                        requestDTO1.taskId = "1681941420455366656";

                        boolean ret1 = pointService.pointRecordAdd(requestDTO1);
                        if(ret1){
                            ret = pointExChange(uid,pid,benefit588DTO.benefitTaskCode);
                        }else {
                            ret.setCode("-1");
                            ret.setSuccess(false);
                            ret.setMsg("兑换失败");
                        }

                        break;
                    case "5":
                        ret = sendPrivilegeCommon(uid, pid, "小智盈续费领福利_" + benefit588DTO.benefitName, benefit588DTO.benefitTaskCode, 1);
                        break;
                    default:
                        break;
                }

                //领取福利记录存入数据库
                if(ret.isSuccess()){
                    SetBenefitRecord(uid,benefit588DTO.id.toString(),actCode,dayOfMonth);

                    Act588BenifitRecordDO act588BenifitRecordDO = new Act588BenifitRecordDO();
                    act588BenifitRecordDO.uid = uid;
                    act588BenifitRecordDO.benefitId = benefit588DTO.id;
                    act588BenifitRecordDO.benefitName = benefit588DTO.benefitTypeRemark;
                    act588BenifitRecordDO.source = source;
                    act588BenifitRecordDO.actCode = actCode;
                    act588BenifitRecordDO.writeTime = new Date();
                    act588BenifitRecordDO.benefitConfirm = 0;
                    act588benifitrecordMapper.insert(act588BenifitRecordDO);
                }

                return ret;
            } else {
                return Result.buildErrorResult("-1","领取福利失败");
            }
        } finally {
            redissonDistributionLock.unlock(lockKey);
        }
    }

    /**
     * 获取该用户领取福利记录列表
     * <AUTHOR>
     * @date 2023/5/19 14:35
     * @param uid
     * @return null
     */
    @Override
    public List<Act588BenifitRecordDO> getUserBenefitRecord(String uid,String actCode){
         return act588benifitrecordMapper.selectByUidAndActCode(uid,actCode);
    }


    /**
     *
     * <AUTHOR>
     * @date 2023/5/22 14:12
     * @param uid
     * @param pid
     * @param productId
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    public Result<String> pointExChange(String uid,String pid,String productId) {
        LoginUserInfoVO loginUserInfoVO = userService.getBoundUserInfo(uid, pid);
        if (loginUserInfoVO == null) {
            return Result.buildErrorResult("未获取到绑定账号信息");
        }

        PointOrderAddRequestDTO orderAddRequestDTO = new PointOrderAddRequestDTO();
        orderAddRequestDTO.emNo = loginUserInfoVO.getAccount();
        orderAddRequestDTO.mobile = loginUserInfoVO.getMobileX();
        orderAddRequestDTO.mobileMask = loginUserInfoVO.getMaskMobile();
        orderAddRequestDTO.platform = 1;
        orderAddRequestDTO.productId = Integer.parseInt(productId);
        orderAddRequestDTO.productQty = 1;
        orderAddRequestDTO.uid = Integer.parseInt(uid);

        PointResult<PointOrderAddDataDTO> orderAddResult = pointService.pointOrderAdd(orderAddRequestDTO);
        if (orderAddResult.isSuccess() && orderAddResult.getData() != null) {
            PointOrderAddDataDTO orderAddDataDTO = JsonUtil.toBean(JSON.toJSONString(orderAddResult.getData()), PointOrderAddDataDTO.class);
            if (orderAddDataDTO != null) {
                String orderNo = orderAddDataDTO.getOrderNo();

                PointOrderExchangeRequestDTO orderExchangeRequestDTO = new PointOrderExchangeRequestDTO();
                orderExchangeRequestDTO.orderNo = orderNo;
                orderExchangeRequestDTO.uid = Integer.parseInt(uid);
                orderExchangeRequestDTO.payType = "0";

                PointResult<PointOrderAddDataDTO> result = pointService.pointOrderExchange(orderExchangeRequestDTO);
                if (result.isSuccess()) {
                    return Result.buildSuccessResult("兑换成功");
                } else {
                    return Result.buildErrorResult("-1", result.getMsg());
                }
            }
        }
        return Result.buildErrorResult(orderAddResult.getMsg());
    }

    /**
     * 赠送优惠券
     * <AUTHOR>
     * @date 2023/5/22 13:53
     * @param uid
     * @param activityID
     * @param price
     * @param actCode
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    public Result<String>  sendCouponCommon(String uid,String pid,String activityID,double price,String actCode) {
        LoginUserInfoVO loginUserInfoVO =  userService.getBoundUserInfo(uid,pid);
        if (loginUserInfoVO == null) {
            return Result.buildErrorResult("未获取到绑定账号信息");
        }

        SendCouponRequestDTO req = new SendCouponRequestDTO();
        req.PRESENT_ACCOUNT_TYPE = 2;
        req.PRESENT_ACCOUNT = loginUserInfoVO.getMobileX();
        req.COUPON_ACTIVITY_ID = activityID;
        req.COUPON_RULE_PRICE = price;
        req.PRESENT_PERSON = actCode;
        LogisticsResult<String> result = logisticsService.sendCoupon(req);
        if (result != null && result.getCode() == 0) {
            return Result.buildSuccessResult();
        } else {
            return Result.buildErrorResult(result.getMsg());
        }
    }

    /**
     * 赠送特权或者使用期
     * <AUTHOR>
     * @date 2023/5/22 11:25
     * @param uid
     * @param pid
     * @param reason
     * @param activityID
     * @param accountType 领取特权指定2  延期指定1
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    public Result<String> sendPrivilegeCommon(String uid,String pid,String reason,String activityID,Integer accountType){
        LoginUserInfoVO loginUserInfoVO =  userService.getBoundUserInfo(uid,pid);
        if (loginUserInfoVO == null) {
            return Result.buildErrorResult("-1","未获取到绑定账号信息");
        }
        if (loginUserInfoVO.getMobileX() == null) {
            return Result.buildErrorResult("-1","未绑定手机号");
        }
        if (loginUserInfoVO.getAccount() == null) {
            return Result.buildErrorResult("-1","em账号查询异常");
        }

        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(activityID);
        sendPrivilegeDTO.setReason(reason);
        sendPrivilegeDTO.setApplyUserID("scb_public");
        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();

        //2:手机号 1：em号  领取特权指定2  延期指定1
        createActivityGrantApplyAccountDTO.setAccountType(accountType);
        createActivityGrantApplyAccountDTO.setAccount(loginUserInfoVO.getAccount());
        createActivityGrantApplyAccountDTO.setMID(loginUserInfoVO.getMobileX());
        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        Result<String> ret = logisticsService.sendPrivilegeResult(sendPrivilegeDTO);
        return ret;
    }

    /**
     * 领取30积分+30优惠券
     * <AUTHOR>
     * @date 2023/11/22 14:44
     * @param actCode
     * @param uid
     * @param pid
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @Override
    public Result<String> sendPP_20231127(String actCode,String uid,String pid) {
        String lockKey = CacheKey_SendPPLock;
        try {
            if (redissonDistributionLock.tryLock(lockKey, TimeUnit.SECONDS, 10)) {
                String isSendPP_key = isSendPP_CODE;
                //重复领取判断
                String isSubmit = userService.IsSubmitByActCodes(isSendPP_key, uid);
                isSubmit = (isSubmit != null && isSubmit.length() > 0) ? isSubmit.substring(0, isSubmit.length() - 1) : "";
                if (!StringUtils.isEmpty(isSubmit)) {
                    return Result.buildErrorResult("-1", "已领取过，请勿重复领取");
                }

                LoginUserInfoVO userInfoVO = userService.getBoundUserInfo(uid, pid);
                if (userInfoVO == null || userInfoVO.getMobileX() == null) {
                    return Result.buildErrorResult("-1", "未绑定手机号");
                }

                //赠送30积分
                PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                requestDTO.platform = "1";
                requestDTO.pid = pid;
                requestDTO.uid = uid;
                requestDTO.subId = "";
                requestDTO.taskId = point30TaskID;
                boolean ret = pointService.pointRecordAdd(requestDTO);

                //赠送30元优惠券
                SendCouponRequestDTO req = new SendCouponRequestDTO();
                req.PRESENT_ACCOUNT_TYPE = 2;
                req.PRESENT_ACCOUNT = userInfoVO.getMobileX();
                req.COUPON_ACTIVITY_ID = activityID_coupon30;
                req.COUPON_RULE_PRICE = 30;
                req.PRESENT_PERSON = actCode;
                LogisticsResult<String> retCoupon = logisticsService.sendCoupon(req);

                if (ret && retCoupon.getCode() == 0) {
                    //积分+优惠券全部领取成功：领取记录存入redis
                    userService.AddCountByActCode(isSendPP_key, uid, String.valueOf(System.currentTimeMillis()));
                } else {
                    //第一次未全部发放成功，再次发放后判断（接口发送失败特殊情况下使用）
                    //查询是否发过88积分
                    Boolean hasPoint = false;
                    Long[] taskids = new Long[1];
                    taskids[0] = Long.parseLong(point30TaskID);

                    PointQueryByTaskIDRequestDTO queryByTaskIDRequestDTO = new PointQueryByTaskIDRequestDTO();
                    queryByTaskIDRequestDTO.uid = Long.parseLong(uid);
                    queryByTaskIDRequestDTO.taskIds = taskids;

                    List<PointQueryByTaskIDDataDTO> list = pointService.pointQueryByTaskID(queryByTaskIDRequestDTO);
                    for (PointQueryByTaskIDDataDTO item : list) {
                        if (item.taskId.equals(taskids[0])) {
                            hasPoint = true;
                        }
                    }

                    //查询是否发过优惠券
                    Boolean hasCoupon = false;
                    //查询是否有优惠券
                    List<QueryCouponListDTO> couponList = logisticsService.queryCouponList(2, userInfoVO.getMobileX());
                    Date now = new Date();
                    for (QueryCouponListDTO item : couponList) {
                        if (item.COUPON_ISENABLE == 0 && activityID_coupon30.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                            hasCoupon = true;
                        }
                    }

                    if (hasPoint && hasCoupon) {
                        //积分+优惠券全部领取成功：领取记录存入redis
                        userService.AddCountByActCode(isSendPP_key, uid, String.valueOf(System.currentTimeMillis()));
                    } else {
                        if(!hasPoint){
                            return Result.buildErrorResult("-2", "积分发放失败，请联系业务员");
                        }
                        if(!hasCoupon){
                            return Result.buildErrorResult("-3", "优惠券发放失败，请联系业务员");
                        }
                    }
                }

                return Result.buildSuccessResult();
            } else {
                return Result.buildErrorResult("-1", "发放失败，请联系业务员");
            }
        } finally {
            redissonDistributionLock.unlock(lockKey);
        }
    }


    /**
     * 积分+优惠券
     * <AUTHOR>
     * @date 2024/4/23 下午5:28
     * @param null
     * @return null
     */
    @Override
    public Result<String> sendPointAndCoupon(String actCode,String uid,String pid,String pointTaskID,String couponActivityID,double couponPrice) {
        String lockKey = CacheKey_SendPPLock_renew588 + ":" +actCode;
        try {
            if (redissonDistributionLock.tryLock(lockKey, TimeUnit.SECONDS, 10)) {
                //重复领取判断
                String isSubmit = userService.IsSubmitByActCodes(actCode, uid);
                isSubmit = (isSubmit != null && isSubmit.length() > 0) ? isSubmit.substring(0, isSubmit.length() - 1) : "";
                if (!StringUtils.isEmpty(isSubmit)) {
                    return Result.buildErrorResult("-1", "已领取过，请勿重复领取");
                }

                LoginUserInfoVO userInfoVO = userService.getBoundUserInfo(uid, pid);
                if (userInfoVO == null || userInfoVO.getMobileX() == null) {
                    return Result.buildErrorResult("-1", "未绑定手机号");
                }

                //赠送30积分
                PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                requestDTO.platform = "1";
                requestDTO.pid = pid;
                requestDTO.uid = uid;
                requestDTO.subId = "";
                requestDTO.taskId = pointTaskID;
                boolean ret = pointService.pointRecordAdd(requestDTO);

                //赠送30元优惠券
                SendCouponRequestDTO req = new SendCouponRequestDTO();
                req.PRESENT_ACCOUNT_TYPE = 2;
                req.PRESENT_ACCOUNT = userInfoVO.getMobileX();
                req.COUPON_ACTIVITY_ID = couponActivityID;
                req.COUPON_RULE_PRICE = couponPrice;
                req.PRESENT_PERSON = actCode;
                LogisticsResult<String> retCoupon = logisticsService.sendCoupon(req);

                if (ret && retCoupon.getCode() == 0) {
                    //积分+优惠券全部领取成功：领取记录存入redis
                    userService.AddCountByActCode(actCode, uid, String.valueOf(System.currentTimeMillis()));
                } else {
                    //第一次未全部发放成功，再次发放后判断（接口发送失败特殊情况下使用）
                    //查询是否发过88积分
                    Boolean hasPoint = false;
                    Long[] taskids = new Long[1];
                    taskids[0] = Long.parseLong(pointTaskID);

                    PointQueryByTaskIDRequestDTO queryByTaskIDRequestDTO = new PointQueryByTaskIDRequestDTO();
                    queryByTaskIDRequestDTO.uid = Long.parseLong(uid);
                    queryByTaskIDRequestDTO.taskIds = taskids;

                    List<PointQueryByTaskIDDataDTO> list = pointService.pointQueryByTaskID(queryByTaskIDRequestDTO);
                    for (PointQueryByTaskIDDataDTO item : list) {
                        if (item.taskId.equals(taskids[0])) {
                            hasPoint = true;
                        }
                    }

                    //查询是否发过优惠券
                    Boolean hasCoupon = false;
                    //查询是否有优惠券
                    List<QueryCouponListDTO> couponList = logisticsService.queryCouponList(2, userInfoVO.getMobileX());
                    Date now = new Date();
                    for (QueryCouponListDTO item : couponList) {
                        if (item.COUPON_ISENABLE == 0 && couponActivityID.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                            hasCoupon = true;
                        }
                    }

                    if (hasPoint && hasCoupon) {
                        //积分+优惠券全部领取成功：领取记录存入redis
                        userService.AddCountByActCode(actCode, uid, String.valueOf(System.currentTimeMillis()));
                    } else {
                        if(!hasPoint){
                            return Result.buildErrorResult("-2", "积分发放失败，请联系业务员");
                        }
                        if(!hasCoupon){
                            return Result.buildErrorResult("-3", "优惠券发放失败，请联系业务员");
                        }
                    }
                }

                return Result.buildSuccessResult();
            } else {
                return Result.buildErrorResult("-1", "发放失败，请联系业务员");
            }
        } finally {
            redissonDistributionLock.unlock(lockKey);
        }
    }
}
