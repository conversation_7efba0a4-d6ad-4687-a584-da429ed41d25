package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.mapper.activity.NoviceGuideUserInfoMapper;
import cn.emoney.mapper.transfer.TbSyncOaUserInfoMapper;
import cn.emoney.pojo.NoviceGuideUserInfoDO;
import cn.emoney.pojo.TbSyncOaUserInfoDO;
import cn.emoney.service.NoviceGuideService;
import cn.emoney.service.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @date 2022-01-05
 */
@Service
@Slf4j
public class NoviceGuideServiceImpl implements NoviceGuideService {

    @Autowired
    private TbSyncOaUserInfoMapper tbSyncOaUserInfoMapper;

    @Autowired
    private NoviceGuideUserInfoMapper noviceGuideUserInfoMapper;

    @Autowired
    private RedisService redisService;

    /**
     * 根据用户uid获取归属业务员信息(新手指引)
     *
     * @param uid
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.TbSyncOaUserInfoDO>
     * <AUTHOR>
     * @date 2022/1/5 16:10
     */
    @Override
    public TbSyncOaUserInfoDO queryByUid(String uid) {
        TbSyncOaUserInfoDO tbSyncOaUserInfoDO = redisService.get(MessageFormat.format(RedisConstants.REDISKEY_NoviceGuide_queryByUid, uid), TbSyncOaUserInfoDO.class);
        if (tbSyncOaUserInfoDO == null) {
            tbSyncOaUserInfoDO = tbSyncOaUserInfoMapper.queryByUid(uid);
            if (tbSyncOaUserInfoDO != null) {
                redisService.set(MessageFormat.format(RedisConstants.REDISKEY_NoviceGuide_queryByUid, uid), tbSyncOaUserInfoDO, (long) 60 * 60);
            }
        }
        return tbSyncOaUserInfoDO;
    }

    /**
     * 保存新手指引弹出用户信息
     *
     * @param noviceGuideUserInfoDO
     * @return cn.emoney.pojo.NoviceGuideUserInfoDO
     * <AUTHOR>
     * @date 2022/1/6 16:48
     */
    @Override
    public NoviceGuideUserInfoDO insertNoviceGuideUserInfo(NoviceGuideUserInfoDO noviceGuideUserInfoDO) {
        Integer res = noviceGuideUserInfoMapper.insert(noviceGuideUserInfoDO);
        if (res > 0) {
            redisService.set(MessageFormat.format(RedisConstants.REDISKEY_NoviceGuide_queryIsShow, noviceGuideUserInfoDO.getUid()), noviceGuideUserInfoDO, (long) 7 * 24 * 60 * 60);
        }
        return noviceGuideUserInfoDO;
    }

    /**
     * 根据uid查询新手指引弹出用户信息
     *
     * @param uid
     * @return cn.emoney.pojo.NoviceGuideUserInfoDO
     * <AUTHOR>
     * @date 2022/1/6 16:49
     */
    @Override
    public NoviceGuideUserInfoDO queryNoviceGuideUserInfoByUid(String uid) {
        NoviceGuideUserInfoDO noviceGuideUserInfoDO = redisService.get(MessageFormat.format(RedisConstants.REDISKEY_NoviceGuide_queryIsShow, uid), NoviceGuideUserInfoDO.class);
        if (noviceGuideUserInfoDO == null) {
            noviceGuideUserInfoDO = noviceGuideUserInfoMapper.queryByUid(uid);
            if (noviceGuideUserInfoDO != null) {
                redisService.set(MessageFormat.format(RedisConstants.REDISKEY_NoviceGuide_queryIsShow, uid), noviceGuideUserInfoDO, (long) 7 * 24 * 60 * 60);
            }
        }
        return noviceGuideUserInfoDO;
    }
}
