package cn.emoney.service.impl;

import cn.emoney.act.quest.CourseWatchQuest;
import cn.emoney.act.quest.logic.QuestReward;
import cn.emoney.act.quest.logic.reward.PointReward;
import cn.emoney.pojo.bo.UserCourseWatchDTO;
import cn.emoney.service.CourseWatchQuestService;
import cn.emoney.service.CourseWatchService;
import cn.emoney.service.UserGroupService;
import cn.emoney.service.redis.RedisSerializerSupplier;
import cn.emoney.service.redis.RedisTemplateSupplier;
import lombok.Data;
import org.apache.kafka.common.utils.CopyOnWriteMap;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class CourseWatchQuestServiceImpl implements CourseWatchQuestService {
    private final CourseWatchService watchService;
    private final UserGroupService userGroupService;
    private final Map<Long, CourseWatchQuest> questMap;
    private final HashOperations<Long, Long, String> operations;

    public CourseWatchQuestServiceImpl(CourseWatchService watchService,
                                       UserGroupService userGroupService,
                                       RedisTemplateSupplier supplier,
                                       RedisSerializerSupplier serializerSupplier) {
        this.watchService = watchService;
        this.userGroupService = userGroupService;
        this.questMap = new CopyOnWriteMap<>();
        this.operations = supplier.createTemplate(
                serializerSupplier.prefix(Long.class, "act:task:courseWatchPoint"),
                null,
                new GenericToStringSerializer<>(Long.class),
                RedisSerializer.string()
        ).opsForHash();
        init();
    }

    @Override
    public List<CourseWatchQuest> listQuests() {
        return new ArrayList<>(questMap.values());
    }

    @Override
    public Optional<CourseWatchQuest> findQuest(Long id) {
        return Optional.ofNullable(questMap.get(id));
    }

    @Override
    public QuestContext checkQuestByUid(Long questId, Long uid) {
        return findQuest(questId).map(quest -> {
            Boolean groupFlag = Optional.ofNullable(quest.getUserGroups())
                    .map(groups -> userGroupService.anyMatch(uid, groups))
                    .orElse(null);
            List<TaskContext> collect = quest.stream().map(task -> {
                TaskContext context = new TaskContext();
                context.setCourseId(task.getCourseId());
                UserCourseWatchDTO record = watchService.getCourseWatchDetail(task.getCourseId(), uid);
                context.setRecord(record);
                context.setRewardDetail(task.getRewards());
                context.setRewardFlag(operations.get(task.getId(), uid) != null);
                return context;
            }).collect(Collectors.toList());

            QuestContext context = new QuestContext();
            context.setGroupFlag(groupFlag);
            context.setTasks(collect);
            return context;
        }).orElse(null);
    }

    @Data
    public static class QuestContext {
        private Boolean groupFlag;
        private List<TaskContext> tasks;
    }

    @Data
    public static class TaskContext {
        private Integer courseId;
        private UserCourseWatchDTO record;
        private Boolean rewardFlag;
        private List<QuestReward> rewardDetail;
    }


    private void init() {
        Map<Long, CourseWatchQuest> localTable = Stream.of(
                        // 新用户体验-完播短视频奖励积分
                        new CourseWatchQuest()
                                .setQuestId(10042L)
                                .setStartTime(LocalDate.of(2024, 9, 1).atStartOfDay())
                                .setTasks(
                                        new CourseWatchQuest.Task(10042001, 101274)
                                                .setRecordDuration(Duration.ofSeconds(203))
                                                .setRewards(new PointReward(1896736307923472384L)),
                                        new CourseWatchQuest.Task(10042002, 101005)
                                                .setRecordDuration(Duration.ofSeconds(162))
                                                .setRewards(new PointReward(1896736164074221568L)),
                                        new CourseWatchQuest.Task(10042003, 101002)
                                                .setRecordDuration(Duration.ofSeconds(171))
                                                .setRewards(new PointReward(1896736136904503296L)),
                                        new CourseWatchQuest.Task(10042004, 101006)
                                                .setRecordDuration(Duration.ofSeconds(155))
                                                .setRewards(new PointReward(1896736110565249024L)),
                                        new CourseWatchQuest.Task(10042005, 101004)
                                                .setRecordDuration(Duration.ofSeconds(237))
                                                .setRewards(new PointReward(1896736081613647872L)),
                                        new CourseWatchQuest.Task(10042006, 101003)
                                                .setRecordDuration(Duration.ofSeconds(158))
                                                .setRewards(new PointReward(1896736048750579712L)),
                                        new CourseWatchQuest.Task(10042007, 101255)
                                                .setRecordDuration(Duration.ofSeconds(189))
                                                .setRewards(new PointReward(1896736013145411584L)),
                                        new CourseWatchQuest.Task(10042008, 101275)
                                                .setRecordDuration(Duration.ofSeconds(212))
                                                .setRewards(new PointReward(1896735964953841664L)),
                                        new CourseWatchQuest.Task(10042009, 101276)
                                                .setRecordDuration(Duration.ofSeconds(226))
                                                .setRewards(new PointReward(1896504722131062784L)),
                                        new CourseWatchQuest.Task(10042010, 101279)
                                                .setRecordDuration(Duration.ofSeconds(176))
                                                .setRewards(new PointReward(1896735835772768256L)),
                                        new CourseWatchQuest.Task(10042011, 101280)
                                                .setRecordDuration(Duration.ofSeconds(209))
                                                .setRewards(new PointReward(1896735643411918848L)),
                                        new CourseWatchQuest.Task(10042012, 101281)
                                                .setRecordDuration(Duration.ofSeconds(199))
                                                .setRewards(new PointReward(1896735585958629376L)),
                                        new CourseWatchQuest.Task(10042013, 101282)
                                                .setRecordDuration(Duration.ofSeconds(218))
                                                .setRewards(new PointReward(1896735542663204864L)),
                                        new CourseWatchQuest.Task(10042014, 101260)
                                                .setRecordDuration(Duration.ofSeconds(270))
                                                .setRewards(new PointReward(1896735489748144128L)),
                                        new CourseWatchQuest.Task(10042015, 101642)
                                                .setRecordDuration(Duration.ofSeconds(208))
                                                .setRewards(new PointReward(1896504796380311552L)),
                                        new CourseWatchQuest.Task(10042016, 101643)
                                                .setRecordDuration(Duration.ofSeconds(208))
                                                .setRewards(new PointReward(1896504842955255808L)),
                                        new CourseWatchQuest.Task(10042017, 101257)
                                                .setRecordDuration(Duration.ofSeconds(280))
                                                .setRewards(new PointReward(1896504886480392192L)),
                                        new CourseWatchQuest.Task(10042018, 101660)
                                                .setRecordDuration(Duration.ofSeconds(197))
                                                .setRewards(new PointReward(1896504926734381056L)),
                                        new CourseWatchQuest.Task(10042019, 101661)
                                                .setRecordDuration(Duration.ofSeconds(208))
                                                .setRewards(new PointReward(1896504965838581760L)),
                                        new CourseWatchQuest.Task(10042020, 101662)
                                                .setRecordDuration(Duration.ofSeconds(164))
                                                .setRewards(new PointReward(1896504996453781504L)),
                                        new CourseWatchQuest.Task(10042021, 101663)
                                                .setRecordDuration(Duration.ofSeconds(213))
                                                .setRewards(new PointReward(1896505031770247168L)),
                                        new CourseWatchQuest.Task(10042022, 101664)
                                                .setRecordDuration(Duration.ofSeconds(204))
                                                .setRewards(new PointReward(1896505063358664704L)),
                                        new CourseWatchQuest.Task(10042023, 101665)
                                                .setRecordDuration(Duration.ofSeconds(209))
                                                .setRewards(new PointReward(1896505100651204608L)),
                                        new CourseWatchQuest.Task(10042024, 102090)
                                                .setRecordDuration(Duration.ofSeconds(177))
                                                .setRewards(new PointReward(1896505132834795520L)),
                                        new CourseWatchQuest.Task(10042025, 102093)
                                                .setRecordDuration(Duration.ofSeconds(166))
                                                .setRewards(new PointReward(1896505170416177152L)),
                                        new CourseWatchQuest.Task(10042026, 102096)
                                                .setRecordDuration(Duration.ofSeconds(196))
                                                .setRewards(new PointReward(1896505209109049344L)),
                                        new CourseWatchQuest.Task(10042027, 102114)
                                                .setRecordDuration(Duration.ofSeconds(188))
                                                .setRewards(new PointReward(1896505248192131072L)),
                                        new CourseWatchQuest.Task(10042028, 102115)
                                                .setRecordDuration(Duration.ofSeconds(204))
                                                .setRewards(new PointReward(1896505278491930624L)),
                                        new CourseWatchQuest.Task(10042029, 102116)
                                                .setRecordDuration(Duration.ofSeconds(250))
                                                .setRewards(new PointReward(1896505307915390976L)),
                                        new CourseWatchQuest.Task(10042030, 102187)
                                                .setRecordDuration(Duration.ofSeconds(165))
                                                .setRewards(new PointReward(1896505338400288768L)),
                                        new CourseWatchQuest.Task(10042031, 102340)
                                                .setRecordDuration(Duration.ofSeconds(200))
                                                .setRewards(new PointReward(1896505374433832960L)),
                                        new CourseWatchQuest.Task(10042032, 102341)
                                                .setRecordDuration(Duration.ofSeconds(172))
                                                .setRewards(new PointReward(1896505407276912640L)),
                                        new CourseWatchQuest.Task(10042033, 102457)
                                                .setRecordDuration(Duration.ofSeconds(144))
                                                .setRewards(new PointReward(1896505441276637184L)),
                                        new CourseWatchQuest.Task(10042034, 102458)
                                                .setRecordDuration(Duration.ofSeconds(162))
                                                .setRewards(new PointReward(1896505470528557056L)),
                                        new CourseWatchQuest.Task(10042035, 102467)
                                                .setRecordDuration(Duration.ofSeconds(156))
                                                .setRewards(new PointReward(1897229312750014464L)),
                                        new CourseWatchQuest.Task(10042036, 102466)
                                                .setRecordDuration(Duration.ofSeconds(193))
                                                .setRewards(new PointReward(1897229350151041024L))
                                ),
                        // [10045] 五星新投教积分需求
                        new CourseWatchQuest()
                                .setQuestId(10045L)
                                .setStartTime(LocalDate.of(2025, 5, 26).atStartOfDay())
                                .setEndTime(LocalDate.of(2025, 7, 31).plusDays(1).atStartOfDay().minusSeconds(1))
                                .setLiveDuration(Duration.ofMinutes(45))
                                .setRecordDuration(Duration.ofMinutes(45))
                                .setTasks(
                                        new CourseWatchQuest.Task(10045001, 103077).setRewards(new PointReward(1925419424567607296L)),
                                        new CourseWatchQuest.Task(10045002, 103087).setRewards(new PointReward(1925476098756976640L)),
                                        new CourseWatchQuest.Task(10045003, 103078).setRewards(new PointReward(1925419517936312320L)),
                                        new CourseWatchQuest.Task(10045004, 103088).setRewards(new PointReward(1925476211903217664L)),
                                        new CourseWatchQuest.Task(10045005, 103079).setRewards(new PointReward(1925419560819712000L)),
                                        new CourseWatchQuest.Task(10045006, 103090).setRewards(new PointReward(1925476249916891136L)),
                                        new CourseWatchQuest.Task(10045007, 103080).setRewards(new PointReward(1925419598738038784L)),
                                        new CourseWatchQuest.Task(10045008, 103091).setRewards(new PointReward(1925476296701558784L)),
                                        new CourseWatchQuest.Task(10045009, 103081).setRewards(new PointReward(1925419633452195840L)),
                                        new CourseWatchQuest.Task(10045010, 103092).setRewards(new PointReward(1925476333906436096L)),
                                        new CourseWatchQuest.Task(10045011, 103082).setRewards(new PointReward(1925419666915614720L)),
                                        new CourseWatchQuest.Task(10045012, 103093).setRewards(new PointReward(1925476368523280384L)),
                                        // 250606 更新
                                        new CourseWatchQuest.Task(10045013, 103244).setRewards(new PointReward(1930896220117909504L)),
                                        new CourseWatchQuest.Task(10045014, 103334).setRewards(new PointReward(1931916576467025920L)),
                                        new CourseWatchQuest.Task(10045015, 103245).setRewards(new PointReward(1930896299987804160L)),
                                        new CourseWatchQuest.Task(10045016, 103335).setRewards(new PointReward(1931916626290466816L)),
                                        new CourseWatchQuest.Task(10045017, 103246).setRewards(new PointReward(1930896340950360064L)),
                                        new CourseWatchQuest.Task(10045018, 103336).setRewards(new PointReward(1931916657159987200L)),
                                        new CourseWatchQuest.Task(10045019, 103247).setRewards(new PointReward(1930896378008289280L)),
                                        new CourseWatchQuest.Task(10045020, 103337).setRewards(new PointReward(1931916688272072704L)),
                                        new CourseWatchQuest.Task(10045021, 103248).setRewards(new PointReward(1930896412063522816L)),
                                        new CourseWatchQuest.Task(10045022, 103338).setRewards(new PointReward(1931916726578511872L)),
                                        new CourseWatchQuest.Task(10045023, 103249).setRewards(new PointReward(1930896445333446656L)),
                                        new CourseWatchQuest.Task(10045024, 103339).setRewards(new PointReward(1931916764599738368L)),
                                        new CourseWatchQuest.Task(10045025, 103250).setRewards(new PointReward(1930896478725554176L)),
                                        new CourseWatchQuest.Task(10045026, 103340).setRewards(new PointReward(1931916796785426432L)),
                                        new CourseWatchQuest.Task(10045027, 103251).setRewards(new PointReward(1930896511991275520L)),
                                        new CourseWatchQuest.Task(10045028, 103341).setRewards(new PointReward(1931916826031054848L)),
                                        new CourseWatchQuest.Task(10045029, 103252).setRewards(new PointReward(1930896545441554432L)),
                                        new CourseWatchQuest.Task(10045030, 103342).setRewards(new PointReward(1931916855825989632L)),
                                        new CourseWatchQuest.Task(10045031, 103253).setRewards(new PointReward(1930896576417464320L)),
                                        new CourseWatchQuest.Task(10045032, 103343).setRewards(new PointReward(1931916922877255680L)),
                                        new CourseWatchQuest.Task(10045033, 103254).setRewards(new PointReward(1930896609361068032L)),
                                        new CourseWatchQuest.Task(10045034, 103344).setRewards(new PointReward(1931916957848379392L)),
                                        new CourseWatchQuest.Task(10045035, 103255).setRewards(new PointReward(1930896642203164672L)),
                                        new CourseWatchQuest.Task(10045036, 103345).setRewards(new PointReward(1931916991700746240L)),
                                        new CourseWatchQuest.Task(10045037, 103256).setRewards(new PointReward(1930896674722586624L)),
                                        new CourseWatchQuest.Task(10045038, 103346).setRewards(new PointReward(1931917025963180032L)),
                                        new CourseWatchQuest.Task(10045039, 103257).setRewards(new PointReward(1930896707367690240L)),
                                        new CourseWatchQuest.Task(10045040, 103347).setRewards(new PointReward(1931917072196714496L)),
                                        new CourseWatchQuest.Task(10045041, 103258).setRewards(new PointReward(1930896739417628672L)),
                                        new CourseWatchQuest.Task(10045042, 103348).setRewards(new PointReward(1931917105359536128L)),
                                        new CourseWatchQuest.Task(10045043, 103259).setRewards(new PointReward(1930896774189383680L)),
                                        new CourseWatchQuest.Task(10045044, 103349).setRewards(new PointReward(1931917154773049344L)),
                                        new CourseWatchQuest.Task(10045045, 103260).setRewards(new PointReward(1930896804771454976L)),
                                        new CourseWatchQuest.Task(10045046, 103350).setRewards(new PointReward(1931917187735109632L)),
                                        // 250627 更新
                                        new CourseWatchQuest.Task(10045047, 103545).setRewards(new PointReward(1938473071956836352L)),
                                        new CourseWatchQuest.Task(10045048, 103551).setRewards(new PointReward(1938473310292713472L)),
                                        new CourseWatchQuest.Task(10045049, 103546).setRewards(new PointReward(1938473168081616896L)),
                                        new CourseWatchQuest.Task(10045050, 103552).setRewards(new PointReward(1938473346256773120L)),
                                        new CourseWatchQuest.Task(10045051, 103547).setRewards(new PointReward(1938473199490244608L)),
                                        new CourseWatchQuest.Task(10045052, 103553).setRewards(new PointReward(1938473381389664256L)),
                                        new CourseWatchQuest.Task(10045053, 103548).setRewards(new PointReward(1938473234384756736L)),
                                        new CourseWatchQuest.Task(10045054, 103554).setRewards(new PointReward(1938473413872164864L)),
                                        new CourseWatchQuest.Task(10045055, 103549).setRewards(new PointReward(1938473271699660800L)),
                                        new CourseWatchQuest.Task(10045056, 103555).setRewards(new PointReward(1938473456483360768L)),
                                        // 250704 更新
                                        new CourseWatchQuest.Task(10045057, 103585).setRewards(new PointReward(1941068348577333248L)),
                                        new CourseWatchQuest.Task(10045058, 103592).setRewards(new PointReward(1941068549903228928L)),
                                        new CourseWatchQuest.Task(10045059, 103586).setRewards(new PointReward(1941068396456427520L)),
                                        new CourseWatchQuest.Task(10045060, 103593).setRewards(new PointReward(1941068585274490880L)),
                                        new CourseWatchQuest.Task(10045061, 103587).setRewards(new PointReward(1941068426592927744L)),
                                        new CourseWatchQuest.Task(10045062, 103594).setRewards(new PointReward(1941068618552655872L)),
                                        new CourseWatchQuest.Task(10045063, 103588).setRewards(new PointReward(1941068458566524928L)),
                                        new CourseWatchQuest.Task(10045064, 103595).setRewards(new PointReward(1941068648848531456L)),
                                        new CourseWatchQuest.Task(10045065, 103589).setRewards(new PointReward(1941068488258281472L)),
                                        new CourseWatchQuest.Task(10045066, 103596).setRewards(new PointReward(1941068680935522304L)),
                                        new CourseWatchQuest.Task(10045067, 103590).setRewards(new PointReward(1941068517187092480L)),
                                        new CourseWatchQuest.Task(10045068, 103597).setRewards(new PointReward(1941068712556797952L)),
                                        // 250711 更新
                                        new CourseWatchQuest.Task(10045069, 103618).setRewards(new PointReward(1943491901520830464L)),
                                        new CourseWatchQuest.Task(10045070, 103619).setRewards(new PointReward(1943491958842703872L)),
                                        new CourseWatchQuest.Task(10045071, 103620).setRewards(new PointReward(1943491993841098752L)),
                                        new CourseWatchQuest.Task(10045072, 103621).setRewards(new PointReward(1943492030647377920L)),
                                        new CourseWatchQuest.Task(10045073, 103622).setRewards(new PointReward(1943492064891625472L)),
                                        new CourseWatchQuest.Task(10045074, 103623).setRewards(new PointReward(1943492104399732736L))
                                ),
                        // [10046]《信国运，抓主线，战未来》系列课 听课送积分
                        new CourseWatchQuest()
                                .setQuestId(10046L)
                                .setStartTime(LocalDate.of(2025, 7, 28).atStartOfDay())
                                .setEndTime(LocalDate.of(2025, 8, 31).plusDays(1).atStartOfDay().minusSeconds(1))
                                .setLiveDuration(Duration.ofMinutes(30))
                                .setRecordDuration(Duration.ofMinutes(30))
                                .setTasks(
                                        new CourseWatchQuest.Task(10046001, 103720).setRewards(new PointReward(1949699371242962944L)),
                                        new CourseWatchQuest.Task(10046002, 103721).setRewards(new PointReward(1949701778842943488L)),
                                        new CourseWatchQuest.Task(10046003, 103722).setRewards(new PointReward(1949701806266146816L)),
                                        new CourseWatchQuest.Task(10046004, 103723).setRewards(new PointReward(1949701833475432448L)),
                                        new CourseWatchQuest.Task(10046005, 103724).setRewards(new PointReward(1949701878980698112L)),
                                        new CourseWatchQuest.Task(10046006, 103725).setRewards(new PointReward(1949701966153293824L)),
                                        new CourseWatchQuest.Task(10046007, 103726).setRewards(new PointReward(1949702002161811456L)),
                                        new CourseWatchQuest.Task(10046008, 103727).setRewards(new PointReward(1949702026145538048L)),
                                        new CourseWatchQuest.Task(10046009, 103728).setRewards(new PointReward(1949702049873420288L)),
                                        new CourseWatchQuest.Task(10046010, 103729).setRewards(new PointReward(1949702074125303808L)),
                                        new CourseWatchQuest.Task(10046011, 103730).setRewards(new PointReward(1949702243579797504L)),
                                        new CourseWatchQuest.Task(10046012, 103731).setRewards(new PointReward(1949702266867417088L)),
                                        new CourseWatchQuest.Task(10046013, 103732).setRewards(new PointReward(1949702308899094528L)),
                                        new CourseWatchQuest.Task(10046014, 103733).setRewards(new PointReward(1949702329472851968L)),
                                        new CourseWatchQuest.Task(10046015, 103734).setRewards(new PointReward(1949702384357695488L)),
                                        new CourseWatchQuest.Task(10046016, 103735).setRewards(new PointReward(1949702475778981888L)),
                                        new CourseWatchQuest.Task(10046017, 103736).setRewards(new PointReward(1949702499707912192L)),
                                        new CourseWatchQuest.Task(10046018, 103737).setRewards(new PointReward(1949702524240814080L)),
                                        new CourseWatchQuest.Task(10046019, 103738).setRewards(new PointReward(1949702551643176960L)),
                                        new CourseWatchQuest.Task(10046020, 103739).setRewards(new PointReward(1949702574703738880L)),
                                        new CourseWatchQuest.Task(10046021, 103740).setRewards(new PointReward(1949702660600848384L)),
                                        new CourseWatchQuest.Task(10046022, 103741).setRewards(new PointReward(1949702682650869760L)),
                                        new CourseWatchQuest.Task(10046023, 103742).setRewards(new PointReward(1949702702348017664L)),
                                        new CourseWatchQuest.Task(10046024, 103743).setRewards(new PointReward(1949702723542392832L)),
                                        new CourseWatchQuest.Task(10046025, 103744).setRewards(new PointReward(1949702750168956928L)),
                                        new CourseWatchQuest.Task(10046026, 103745).setRewards(new PointReward(1949704015844368384L)),
                                        new CourseWatchQuest.Task(10046027, 103746).setRewards(new PointReward(1949704314413236224L)),
                                        new CourseWatchQuest.Task(10046028, 103747).setRewards(new PointReward(1949704375718862848L)),
                                        new CourseWatchQuest.Task(10046029, 103748).setRewards(new PointReward(1949704404064813056L)),
                                        new CourseWatchQuest.Task(10046030, 103749).setRewards(new PointReward(1949704430602452992L)),
                                        new CourseWatchQuest.Task(10046031, 103751).setRewards(new PointReward(1949704527258370048L)),
                                        new CourseWatchQuest.Task(10046032, 103752).setRewards(new PointReward(1949704553087451136L)),
                                        new CourseWatchQuest.Task(10046033, 103753).setRewards(new PointReward(1949704574928035840L)),
                                        new CourseWatchQuest.Task(10046034, 103754).setRewards(new PointReward(1949704597473116160L)),
                                        new CourseWatchQuest.Task(10046035, 103755).setRewards(new PointReward(1949704626632474624L)),
                                        new CourseWatchQuest.Task(10046036, 103756).setRewards(new PointReward(1949704714178641920L)),
                                        new CourseWatchQuest.Task(10046037, 103757).setRewards(new PointReward(1949704740679098368L)),
                                        new CourseWatchQuest.Task(10046038, 103758).setRewards(new PointReward(1949704771721977856L)),
                                        new CourseWatchQuest.Task(10046039, 103759).setRewards(new PointReward(1949704794245947392L)),
                                        new CourseWatchQuest.Task(10046040, 103760).setRewards(new PointReward(1949704816871006208L)),
                                        new CourseWatchQuest.Task(10046041, 103761).setRewards(new PointReward(1949704884431454208L)),
                                        new CourseWatchQuest.Task(10046042, 103762).setRewards(new PointReward(1949704905676308480L)),
                                        new CourseWatchQuest.Task(10046043, 103763).setRewards(new PointReward(1949704936451309568L)),
                                        new CourseWatchQuest.Task(10046044, 103764).setRewards(new PointReward(1949704962893602816L)),
                                        new CourseWatchQuest.Task(10046045, 103765).setRewards(new PointReward(1949705010546212864L)),
                                        new CourseWatchQuest.Task(10046046, 103766).setRewards(new PointReward(1949705095936090112L)),
                                        new CourseWatchQuest.Task(10046047, 103767).setRewards(new PointReward(1949705136605036544L)),
                                        new CourseWatchQuest.Task(10046048, 103768).setRewards(new PointReward(1949705158948372480L)),
                                        new CourseWatchQuest.Task(10046049, 103769).setRewards(new PointReward(1949705183616475136L)),
                                        new CourseWatchQuest.Task(10046050, 103770).setRewards(new PointReward(1949705210955644928L))
                                )
                )
                .collect(Collectors.toMap(CourseWatchQuest::getQuestId, quest -> quest));
        questMap.putAll(localTable);
    }
}
