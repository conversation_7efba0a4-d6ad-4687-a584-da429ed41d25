package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.RedissonDistributionLock;
import cn.emoney.mapper.activity.Act588benifitrecordMapper;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.Lottery0808PrizeDO;
import cn.emoney.pojo.bo.*;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.Lottery20230808Service;
import cn.emoney.service.PointService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-07-26
 */
@Service
@Slf4j
public class Lottery20230808ServiceImpl implements Lottery20230808Service {
    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserService userService;

    @Autowired
    private PointService pointService;

    @Autowired(required = false)
    private Act588benifitrecordMapper act588benifitrecordMapper;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;

    @Autowired
    private RedissonDistributionLock redissonDistributionLock;

    private final static String cacheKey_LotteryCountByDay = RedisConstants.Redis_Pre_Activity + "LotteryCountByDay1031:";
    private final static String cacheKey_lotteryIsTipByDay = RedisConstants.Redis_Pre_Activity + "lotteryIsTipByDay1031:";
    private final static String cacheKey_getMyLotteryList = RedisConstants.Redis_Pre_Activity + "getMyLotteryList1031:";
    private final static String cacheKey_PrizeSet = RedisConstants.Redis_Pre_Activity + "20231031:PrizeSet:";
    private final String CacheKey_DoLotteryLock = RedisConstants.RedisKey_Pre_Activity + "20231031:DoLotteryLock";
    private final String CacheKey_SendPPLock = RedisConstants.RedisKey_Pre_Activity + "20231031:SendPPLock";
    private final String CacheKey_AllCount = "renew20231031_count";

    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
    /**领取88积分和100优惠券标识code*/
    private final static String isSendPP_CODE = "renew20231031_issendpp";
    private final static String point88TaskID = "1715285019037143040";
    private final static String activityID_coupon100="cp-1231020094929730";
    private final static String activityID_coupon200="cp-1231020104426161";
    /**扣除20积分任务id*/
    private final static String point20TaskID_De = "1715288201385611264";
    /**返还20积分任务id*/
    private final static String point20TaskID_Re = "1715288037002448896";
    /**设置每天可抽奖的次数*/
    private final static Integer LOTTERY_COUNT = 2;
    /**设置奖品编号数组长度*/
    private final static Integer ARRAY_SIZE = 100;
    /**初始化奖品编号数组*/
    private static Integer[] prizesArray = new Integer[ARRAY_SIZE];

    static {
        for (int i = 0; i < ARRAY_SIZE; i++) {
            prizesArray[i] = i + 1;
        }
    }

    /**
     * 周年庆抽奖
     * <AUTHOR>
     * @date 2023/7/27 14:29
     * @param actCode
     * @param uid
     * @param pid
     * @param source
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.Lottery0808PrizeDO>
     */
    @Override
    public Result<Lottery0808PrizeDO> doLottery0808(String actCode,String uid,String pid,String source) {
        String lockKey = CacheKey_DoLotteryLock;
        try {
            if (redissonDistributionLock.tryLock(lockKey, TimeUnit.SECONDS, 10)) {
                LoginUserInfoVO userInfoVO = userService.getBoundUserInfo(uid, pid);
                if (userInfoVO == null || userInfoVO.getMobileX() == null) {
                    return Result.buildErrorResult("-1", "未绑定手机号，无法抽奖");
                }

                //判断当天是否还有抽奖次数
                Integer count = getLotteryCountByDay(actCode, uid);
                if (count < 1) {
                    return Result.buildErrorResult("-1", "今日抽奖次数已用完，请明日再来");
                }

                //非首次抽奖，扣除20积分抽取一次；首次免费
                if (count < LOTTERY_COUNT) {
                    Double allPoint = QueryPoint(uid);
                    if (allPoint < 20) {
                        return Result.buildErrorResult("-1", "可用积分不足20");
                    }
                    PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                    requestDTO.platform = "1";
                    requestDTO.pid = pid;
                    requestDTO.uid = uid;
                    requestDTO.subId = "";
                    requestDTO.taskId = point20TaskID_De;
                    boolean ret = pointService.pointRecordAdd(requestDTO);
                    if (!ret) {
                        return Result.buildErrorResult("-1", "积分扣除失败，请联系客服");
                    }
                }

                //从Redis Set集合中随机抽取一个奖品编号
                Integer pNum = getPrizesNum();
                if (pNum == -1) {
                    return Result.buildErrorResult("-1", "抽奖失败，请联系客服");
                }

                //根据奖品编号查询对应的奖品详情
                Lottery0808PrizeDO prizeDO = initLottery0808List().stream().filter(x -> x.id.equals(pNum)).findFirst().get();
                if (prizeDO == null) {
                    return Result.buildErrorResult("-1", "找不到奖品，请联系客服");
                }

                //记录中奖明细
                Act588BenifitRecordDO benifitRecordDO = new Act588BenifitRecordDO();
                benifitRecordDO.uid = uid;
                benifitRecordDO.actCode = actCode;
                benifitRecordDO.benefitId = pNum;
                benifitRecordDO.benefitName = prizeDO.name;
                benifitRecordDO.source = source;
                benifitRecordDO.writeTime = new Date();
                benifitRecordDO.benefitConfirm = 0;
                int ret = act588benifitrecordMapper.insert(benifitRecordDO);
                if (ret > 0) {
                    //异步发奖品
                    //CompletableFuture.runAsync(() -> {
                    //发奖
                    Result<String> result = sendPrize(uid, pid, prizeDO, userInfoVO);
                    if (result.isSuccess()) {
                        //修改领取状态为已领取
                        act588benifitrecordMapper.updateBenefitConfirm(benifitRecordDO.id);
                        //刷新中奖记录
                        refreshMyLottery0808List(actCode, uid);
                        //刷新当日抽奖次数
                        refreshLotteryCountByDay(actCode, uid);
                        //本次活动总抽奖次数+1
                        userService.AddUsedCount(CacheKey_AllCount);
                    } else {
                        //非首次抽奖，发放失败，返还20积分。
                        if (count < LOTTERY_COUNT) {
                            PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                            requestDTO.platform = "1";
                            requestDTO.pid = pid;
                            requestDTO.uid = uid;
                            requestDTO.subId = "";
                            requestDTO.taskId = point20TaskID_Re;
                            pointService.pointRecordAdd(requestDTO);
                        }

                        log.error("小智盈续费发放奖品失败：uid:" + uid + ",剩余抽奖次数：" + count + ",prize:" + prizeDO + ",ret:" + ret);
                        return Result.buildErrorResult("-1", "抽奖失败，请联系业务员");
                    }
                    //}, executor);
                } else {
                    return Result.buildErrorResult("-1", "抽奖失败，请联系业务员");
                }
                return Result.buildSuccessResult(prizeDO);
            } else {
                return Result.buildErrorResult("-1", "抽奖失败，请联系业务员");
            }
        } finally {
            redissonDistributionLock.unlock(lockKey);
        }
    }

    /**
     * 发放奖品(奖品类型 1：积分类 2:使用期  3：特权类  4：优惠券)
     * <AUTHOR>
     * @date 2023/7/27 13:55
     * @param uid
     * @param pid
     * @param prizeDO
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    public Result<String> sendPrize(String uid,String pid,Lottery0808PrizeDO prizeDO,LoginUserInfoVO userInfoVO) {
        Result<String> ret = new Result<>();
        switch (prizeDO.type) {
            case 1:
                PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                requestDTO.platform = "1";
                requestDTO.pid = pid;
                requestDTO.uid = uid;
                requestDTO.subId = "";
                requestDTO.taskId = prizeDO.taskCode;

                boolean pointRet = pointService.pointRecordAdd(requestDTO);
                if (pointRet) {
                    ret.setCode("200");
                    ret.setSuccess(true);
                    ret.setMsg("积分赠送成功");
                } else {
                    ret.setSuccess(false);
                    ret.setMsg("积分赠送失败");
                }
                break;
            case 2:
                ret = sendPrivilegeCommonNew(uid, pid, "小智盈续费周年庆抽奖赠送使用期", prizeDO.taskCode, 1, userInfoVO);
                break;
            case 3:
                ret = sendPrivilegeCommonNew(uid, pid, "小智盈续费周年庆抽奖赠送功能", prizeDO.taskCode, 2, userInfoVO);
                break;
            case 4:
                SendCouponRequestDTO req = new SendCouponRequestDTO();
                req.PRESENT_ACCOUNT_TYPE = 2;
                req.PRESENT_ACCOUNT = userInfoVO.getMobileX();
                req.COUPON_ACTIVITY_ID = prizeDO.taskCode;
                req.COUPON_RULE_PRICE = (double)prizeDO.pCount;
                req.PRESENT_PERSON = "小智盈续费周年庆抽奖";
                LogisticsResult<String> retCoupon = logisticsService.sendCoupon(req);
                if (retCoupon.getCode() == 0) {
                    ret.setCode("200");
                    ret.setSuccess(true);
                    ret.setMsg("优惠券赠送成功");
                } else {
                    ret.setSuccess(false);
                    ret.setMsg("优惠券赠送失败");
                }
                break;
            default:
                break;
        }
        return ret;
    }
    /**
     * 抽取奖品
     * 10积分	30%
     * 30积分	20%
     * 21天使用期	1%
     * 30天北上资金功能	2%
     * 7天量王叠现	20%
     * 7天五星研报	20%
     * 30天五星研报	2%
     * 30元优惠券	5%
     * <AUTHOR>
     * @date 2023/7/26 17:35
     * @return java.lang.Integer
     */
    @Override
    public Integer getPrizesNum() {
        Long count = redisService.sSize(cacheKey_PrizeSet);
        if (count <= 0) {
            redisService.setAdd(cacheKey_PrizeSet, prizesArray);
        }
        Integer prizesIndex = (Integer) redisService.sPop(cacheKey_PrizeSet);
        if (prizesIndex >= 1 && prizesIndex <= 30) {
            return 1;
        } else if (prizesIndex >= 31 && prizesIndex <= 45) {
            return 2;
        } else if (prizesIndex >= 46 && prizesIndex <= 60) {
            return 3;
        } else if (prizesIndex >= 61 && prizesIndex <= 75) {
            return 4;
        } else if (prizesIndex >= 76 && prizesIndex <= 90) {
            return 5;
        } else if (prizesIndex >= 91 && prizesIndex <= 92) {
            return 6;
        } else if (prizesIndex >= 93 && prizesIndex <= 96) {
            return 7;
        } else if (prizesIndex >= 97 && prizesIndex <= 100) {
            return 8;
        } else {
            return -1;
        }
    }

    /**
     * 领取88积分+优惠券(100/200)
     * <AUTHOR>
     * @date 2023/7/26 17:22
     * @param actCode
     * @param uid
     * @param pid
     * @param type:1:100优惠券  2：200优惠券
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @Override
    public Result<String> sendPP0808(String actCode,String uid,String pid,String type) {
        if (StringUtils.isEmpty(type)) {
            type = "1";
        }
        String lockKey = CacheKey_SendPPLock + type;
        try {
            if (redissonDistributionLock.tryLock(lockKey, TimeUnit.SECONDS, 10)) {
                String isSendPP_key = isSendPP_CODE + type;
                //重复领取判断
                String isSubmit = userService.IsSubmitByActCodes(isSendPP_key, uid);
                isSubmit = (isSubmit != null && isSubmit.length() > 0) ? isSubmit.substring(0, isSubmit.length() - 1) : "";
                if (!StringUtils.isEmpty(isSubmit)) {
                    return Result.buildErrorResult("-1", "已领取过，请勿重复领取");
                }

                LoginUserInfoVO userInfoVO = userService.getBoundUserInfo(uid, pid);
                if (userInfoVO == null || userInfoVO.getMobileX() == null) {
                    return Result.buildErrorResult("-1", "未绑定手机号");
                }

                //赠送88积分
                PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                requestDTO.platform = "1";
                requestDTO.pid = pid;
                requestDTO.uid = uid;
                requestDTO.subId = "";
                requestDTO.taskId = point88TaskID;
                boolean ret = pointService.pointRecordAdd(requestDTO);

                //赠送100元优惠券
                SendCouponRequestDTO req = new SendCouponRequestDTO();
                req.PRESENT_ACCOUNT_TYPE = 2;
                req.PRESENT_ACCOUNT = userInfoVO.getMobileX();
                req.COUPON_ACTIVITY_ID = type.equals("1") ? activityID_coupon100 : activityID_coupon200;
                req.COUPON_RULE_PRICE = type.equals("1") ? 100 : 200;
                req.PRESENT_PERSON = actCode;
                LogisticsResult<String> retCoupon = logisticsService.sendCoupon(req);

                if (ret && retCoupon.getCode() == 0) {
                    //积分+优惠券全部领取成功：领取记录存入redis
                    userService.AddCountByActCode(isSendPP_key, uid, String.valueOf(System.currentTimeMillis()));
                } else {
                    //第一次未全部发放成功，再次发放后判断（接口发送失败特殊情况下使用）
                    //查询是否发过88积分
                    Boolean has88Point = false;
                    Long[] taskids = new Long[1];
                    taskids[0] = Long.parseLong(point88TaskID);

                    PointQueryByTaskIDRequestDTO queryByTaskIDRequestDTO = new PointQueryByTaskIDRequestDTO();
                    queryByTaskIDRequestDTO.uid = Long.parseLong(uid);
                    queryByTaskIDRequestDTO.taskIds = taskids;

                    List<PointQueryByTaskIDDataDTO> list = pointService.pointQueryByTaskID(queryByTaskIDRequestDTO);
                    for (PointQueryByTaskIDDataDTO item : list) {
                        if (item.taskId.equals(taskids[0])) {
                            has88Point = true;
                        }
                    }

                    //查询是否发过优惠券
                    Boolean hasCoupon = false;
                    //查询是否有优惠券
                    List<QueryCouponListDTO> couponList = logisticsService.queryCouponList(2, userInfoVO.getMobileX());
                    Date now = new Date();
                    for (QueryCouponListDTO item : couponList) {
                        if (item.COUPON_ISENABLE == 0 && (type.equals("1") ? activityID_coupon100 : activityID_coupon200).indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                            hasCoupon = true;
                        }
                    }

                    if (has88Point && hasCoupon) {
                        //积分+优惠券全部领取成功：领取记录存入redis
                        userService.AddCountByActCode(isSendPP_key, uid, String.valueOf(System.currentTimeMillis()));
                    } else {
                        return Result.buildErrorResult("-1", "发放失败，请联系业务员");
                    }
                }

                return Result.buildSuccessResult();
            } else {
                return Result.buildErrorResult("-1", "发放失败，请联系业务员");
            }
        } finally {
            redissonDistributionLock.unlock(lockKey);
        }
    }

    /**
     * 获取弹出状态（当天只弹出一次）
     * <AUTHOR>
     * @date 2023/7/26 16:27
     * @param uid
     * @return boolean
     */
    @Override
    public boolean getTipStatusByDay(String uid,String type) {
        String nowDate = simpleDateFormat.format(System.currentTimeMillis());
        String key = cacheKey_lotteryIsTipByDay + type  + nowDate + ":" + uid;

        Object obj = redisService.get(key);
        if (obj == null) {
            //活动结束缓存自动失效
            redisService.set(key, true, 60L, TimeUnit.DAYS);
            return false;
        } else {
            return true;
        }
    }
    /**
     * 获取当天可抽奖的次数
     * <AUTHOR>
     * @date 2023/7/26 15:02
     * @param actCode
     * @param uid
     * @return java.lang.Integer
     */
    @Override
    public Integer getLotteryCountByDay(String actCode,String uid) {
        String nowDate = simpleDateFormat.format(System.currentTimeMillis());
        String key = cacheKey_LotteryCountByDay + nowDate + ":" + uid;
        Object count = redisService.get(key);
        if (count == null) {
            return refreshLotteryCountByDay(actCode, uid);
        }
        return (Integer) count;
    }

    /**
     * 刷新当天可抽奖的次数 缓存
     * <AUTHOR>
     * @date 2023/7/26 14:57
     * @param actCode
     * @param uid
     * @return java.lang.Integer
     */
    @Override
    public Integer refreshLotteryCountByDay(String actCode,String uid) {
        String nowDate = simpleDateFormat.format(System.currentTimeMillis());
        String key = cacheKey_LotteryCountByDay + nowDate + ":" + uid;
        Integer syCount = LOTTERY_COUNT;

        List<Act588BenifitRecordDO> list = getMyLottery0808List(actCode, uid);
        if(list!=null){
            List<Act588BenifitRecordDO> listByDay = list.stream().filter(x -> simpleDateFormat.format(x.writeTime).equals(nowDate) && x.benefitConfirm == 1).collect(Collectors.toList());
            if (listByDay != null && listByDay.size() > 0) {
                //活动结束缓存自动失效
                syCount = LOTTERY_COUNT - listByDay.size();
            }
        }
        redisService.set(key, syCount, 60L, TimeUnit.DAYS);
        return syCount;
    }
    /**
     * 获取我的中奖记录
     * <AUTHOR>
     * @date 2023/7/26 14:40
     * @param actCode
     * @param uid
     * @return java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>
     */
    @Override
    public List<Act588BenifitRecordDO> getMyLottery0808List(String actCode,String uid) {
        //获取我的中奖记录
        String key = cacheKey_getMyLotteryList + actCode + ":" + uid;
        List<Act588BenifitRecordDO> list = redisService.getList(key, Act588BenifitRecordDO.class);
        if (list == null) {
            list = refreshMyLottery0808List(actCode, uid);
        }
        Collections.sort(list,Collections.reverseOrder());
        return list;
    }

    /**
     * 刷新我的中奖记录缓存
     * <AUTHOR>
     * @date 2023/7/26 14:37
     * @param actCode
     * @param uid
     * @return java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>
     */
    @Override
    public List<Act588BenifitRecordDO> refreshMyLottery0808List(String actCode,String uid) {
        String key = cacheKey_getMyLotteryList + actCode + ":" + uid;

        List<Act588BenifitRecordDO> list = act588benifitrecordMapper.selectByUidAndActCode(uid, actCode);
        if (list != null) {
            List<Act588BenifitRecordDO> filterList = list.stream().filter(x -> x.benefitConfirm == 1).collect(Collectors.toList());
            if (filterList != null) {
                //60天结束后 缓存自动失效
                redisService.set(key, filterList, 60L, TimeUnit.DAYS);
                return filterList;
            }
        }

        return null;
    }

    /**
     * 获取奖品列表
     *
     * @param
     * @return null
     * <AUTHOR>
     * @date 2023/7/26 13:40
     */
    public List<Lottery0808PrizeDO> initLottery0808List() {
        List<Lottery0808PrizeDO> list = new ArrayList<>();
        list.add(new Lottery0808PrizeDO(1, 1, "10积分", "1715287339581968384", "10积分", 10));
        list.add(new Lottery0808PrizeDO(2, 3, "3天买卖频谱", "PAC1231018135311414", "3天买卖频谱", 3));
        list.add(new Lottery0808PrizeDO(3, 3, "3天估值带", "PAC1231018135509412", "3天估值带", 3));
        list.add(new Lottery0808PrizeDO(4, 3, "3天K线故事", "PAC1231018135617474", "3天K线故事", 3));
        list.add(new Lottery0808PrizeDO(5, 3, "3天五星研报", "PAC1230512154328301", "3天五星研报", 3));
        list.add(new Lottery0808PrizeDO(6, 2, "21天智盈使用期", "PAC1230724141926204", "21天智盈", 21));
        list.add(new Lottery0808PrizeDO(7, 3, "30天北上资金", "PAC1230724135904378", "30天北上资金", 30));
        list.add(new Lottery0808PrizeDO(8, 3, "30天五星研报", "PAC123072414001365", "30天五星研报", 30));
        return list;
    }

    /**
     * 领特权/送使用期  通用接口
     * <AUTHOR>
     * @date 2023/7/27 16:22
     * @param uid
     * @param pid
     * @param reason
     * @param activityID
     * @param accountType
     * @param loginUserInfoVO
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    public Result<String> sendPrivilegeCommonNew(String uid,String pid,String reason,String activityID,Integer accountType,LoginUserInfoVO loginUserInfoVO){
        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(activityID);
        sendPrivilegeDTO.setReason(reason);
        sendPrivilegeDTO.setApplyUserID("scb_public");
        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();

        //2:手机号 1：em号  领取特权指定2  延期指定1
        createActivityGrantApplyAccountDTO.setAccountType(accountType);
        createActivityGrantApplyAccountDTO.setAccount(loginUserInfoVO.getAccount());
        createActivityGrantApplyAccountDTO.setMID(loginUserInfoVO.getMobileX());
        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        Result<String> ret = logisticsService.sendPrivilegeResult(sendPrivilegeDTO);
        return ret;
    }

    /**
     * 获取可用积分
     *
     * @param uid
     * @return java.lang.Double
     * <AUTHOR>
     * @date 2022/2/17 14:17
     */
    public Double QueryPoint(String uid) {
        Double point = Double.parseDouble("0");
        if (uid != null) {
            List<PointQueryDataDTO> list = pointService.pointQuerySummary(uid);
            for (PointQueryDataDTO item : list) {
                int pointStatus = item.pointStatus;
                if (pointStatus == 2 || pointStatus == 3) {
                    point += item.pointTotal;
                }
            }
        }
        return point;
    }
}
