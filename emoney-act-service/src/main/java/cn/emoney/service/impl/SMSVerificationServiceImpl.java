package cn.emoney.service.impl;

import cn.emoney.service.MobileService;
import cn.emoney.service.SMSVerificationService;
import cn.emoney.service.limiter.FrequencyLimit;
import cn.emoney.service.redis.RedisTemplateSupplier;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collections;
import java.util.Objects;
import java.util.function.BiFunction;

@Slf4j
@Service
public class SMSVerificationServiceImpl implements SMSVerificationService {
    private static final Duration SMS_CODE_TTL = Duration.ofMinutes(5);
    private final MobileService mobileService;
    private final ValueOperations<String, String[]> codeCacheOps;
    private final BiFunction<String, String[], String[]> codeCacheSetNxGetExat;
    private final ValueOperations<String, Long> codeLimitOps;

    public SMSVerificationServiceImpl(MobileService mobileService,
                                      RedisTemplateSupplier supplier) {
        this.mobileService = mobileService;

        // 短信验证码存储
        RedisTemplate<String, String[]> codeCacheTemplate = supplier.get(String[].class, "act:sms:code:cache");
        this.codeCacheOps = codeCacheTemplate.opsForValue();
        DefaultRedisScript<String[]> script = new DefaultRedisScript<>();
        script.setScriptSource(
                new ResourceScriptSource(new ClassPathResource("META-INF/scripts/set_nx_get_exat.lua")));
        script.setResultType(String[].class);
        this.codeCacheSetNxGetExat = (key, value) ->
                codeCacheTemplate.execute(script, Collections.singletonList(key), value, SMS_CODE_TTL.toMillis());

        // 短信验证码重试
        this.codeLimitOps = supplier.get(Long.class, "act:sms:code:limit").opsForValue();
    }

    @Override
    @FrequencyLimit(value = "'act:sms-verify:'+#mobile+'@'+#useId", frequency = 1, duration = "1m")
    @FrequencyLimit(value = "'act:sms-verify:'+#client", frequency = 5, duration = "1m")
    @FrequencyLimit(value = "'act:sms-verify:'+#client", frequency = 99, cron = "0 0 3 * * ?")
    @FrequencyLimit(value = "'act:sms-verify:'+#mobile", frequency = 50, cron = "0 0 3 * * ?")
    public String createCode(String mobile, String client, String useId) {
        String key = mobile + "@" + useId;
        String code = RandomUtil.randomNumbers(4);
        String[] apply = codeCacheSetNxGetExat.apply(key, new String[]{code, mobile});
        if (apply != null) {
            code = apply[0];
        }
        mobileService.sendTextMessage(mobile, "验证码: " + code + ", 5分钟内有效", useId);
        return key;
    }

    @Override
    public Result verifyCode(String key, String code) {
        String[] codeCache = codeCacheOps.get(key);
        if (codeCache == null) {
            return Result.INVALID;
        }
        if (Objects.equals(code, codeCache[0])) {
            codeCacheOps.getOperations().delete(key);
            codeLimitOps.getOperations().delete(key);
            return new Result(codeCache[1]);
        }

        Long checkTimes = codeLimitOps.increment(key);
        if (checkTimes != null && checkTimes >= 3) {
            codeCacheOps.getOperations().delete(key);
            codeLimitOps.getOperations().delete(key);
            return Result.INVALID;
        }
        codeLimitOps.getOperations().expire(key, SMS_CODE_TTL);
        return Result.FAIL;
    }
}
