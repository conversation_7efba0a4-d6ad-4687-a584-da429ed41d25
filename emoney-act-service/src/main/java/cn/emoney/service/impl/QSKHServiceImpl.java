package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.common.utils.RedissonDistributionLock;
import cn.emoney.mapper.activity.Act588benifitrecordMapper;
import cn.emoney.pojo.*;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.QSKHService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-04-10
 */
@Service
@Slf4j
public class QSKHServiceImpl implements QSKHService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private UserService userService;

    @Autowired
    private RedissonDistributionLock redissonDistributionLock;

    @Autowired(required = false)
    private Act588benifitrecordMapper act588benifitrecordMapper;

    @Value("${queryStatusQSUrl:}")
    private String queryStatusQSUrl;

    @Value("${pushBlackListQSUrl:}")
    private String pushBlackListQSUrl;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;

    private static final String cacheKey_PrizeSet = RedisConstants.Redis_Pre_Activity + "renew20240412:cacheKey_PrizeSet";
    private static final String cacheKey_PrizeSet_ds = RedisConstants.Redis_Pre_Activity + "renew20240412:cacheKey_PrizeSet_ds";
    private final String CacheKey_DoLotteryLock = RedisConstants.RedisKey_Pre_Activity + "renew20240412:DoLotteryLock";
    private final static String cacheKey_getMyLotteryList = RedisConstants.Redis_Pre_Activity + "renew20240412:getMyLotteryList";
    private final static String cacheKey_hasOnlyPrize = RedisConstants.RedisKey_Pre_Activity + "renew20240412:hasOnlyPrize";

    private static final String canParticipate_MaxDate = "2023-10-08 00:00:00:000";
    private static final String canParticipate_PID = "888010000";
    private static final String canLottery_MinDate_before = "2024-04-23 00:00:00";
    private static final String canLottery_MaxDate_before = "2024-05-31 23:59:59";

    private static final String canParticipate_lineDate = "2024-05-01 00:00:00:000";

    private static final String canLottery_MinDate_after = "2024-05-01 00:00:00";
    private static final String canLottery_MaxDate_after = "2024-05-31 23:59:59";

    private static final String pid_588="888010000";//小智盈收费
    private static final String pid_ds="888020000,888080000";//大师收费
    private static final String pid_tj="888204010,888224010";//天玑收费

    /**设置奖品编号数组长度*/
    private final static Integer ARRAY_SIZE = 100;
    /**初始化奖品编号数组*/
    private static Integer[] prizesArray = new Integer[ARRAY_SIZE];

    static {
        for (int i = 0; i < ARRAY_SIZE; i++) {
            prizesArray[i] = i + 1;
        }
    }
    /**
     * 是否可参与活动、抽奖
     * <AUTHOR>
     * @date 2024/4/11 15:26
     * @param uid
     * @param pid
     * @param mobile
     * @return java.lang.Boolean
     */
    @Override
    public QSKHInfoDO.Status getUserStatus(String uid,String pid,String mobile) {
        QSKHInfoDO.Status userStatus = new QSKHInfoDO.Status();
        userStatus.canParticipate = false;
        userStatus.canLottery = false;
        userStatus.isOpened = false;

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date maxDate = formatter.parse(canParticipate_MaxDate);
            String openTime = queryOpenTimeQS(mobile);
            if(!StringUtils.isEmpty(openTime)){
                userStatus.isOpened = true;
            }

            //20240604-需求变更：无参与条件限制、5月1号之后开户均可参与抽奖
            userStatus.canParticipate = true;
            userStatus.canLottery = canLottery(openTime, "after");

//                //1、是否是小智盈收费用户
//                if(pid.equals(canParticipate_PID)){
//                    //2、是否已开户
//                    openTime = queryOpenTimeQS(mobile);
//                    if(!StringUtils.isEmpty(openTime)){
//                        userStatus.isOpened = true;
//                    }
//
//                    if(date.getTime()<maxDate.getTime()){
//                        //3、2023.10.8号之前购买的用户
//                        if(userStatus.isOpened){
//                            //4、已开户-是否可参与抽奖
//                            userStatus.canLottery = canLottery(openTime,"before");
//                            //5、是否符合参与条件（同抽奖条件）
//                            userStatus.canParticipate = userStatus.canLottery;
//                        }else{
//                            //6、未开户-可参与开户
//                            userStatus.canParticipate = true;
//                        }
//                    }else{
//                        //7、2023.10.8号之后购买的用户
//                        if(userStatus.isOpened) {
//                            //8、已开户-是否可参与抽奖
//                            userStatus.canLottery = canLottery(openTime,"after");
//                            //9、是否符合参与条件（同抽奖条件）
//                            userStatus.canParticipate = userStatus.canLottery;
//                        }
//                        else{
//                            //10、当前日期是5月1号之后未开户的用户可以参与开户
//                            if(System.currentTimeMillis()>formatter.parse(canParticipate_lineDate).getTime()){
//                                userStatus.canParticipate = true;
//                            }
//                        }
//                    }
//                }
        } catch (Exception e) {
            e.printStackTrace();
        }


        return userStatus;
    }


    /**
     * 是否可抽奖
     * <AUTHOR>
     * @date 2024/4/11 15:26
     * @param openTime
     * @return java.lang.Boolean
     */
    public Boolean canLottery(String openTime,String type) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date date = formatter.parse(openTime);
            Date minDate_before = formatter.parse(canLottery_MinDate_before);
            Date maxDate_before = formatter.parse(canLottery_MaxDate_before);


            Date minDate_after = formatter.parse(canLottery_MinDate_after);
            Date maxDate_after = formatter.parse(canLottery_MaxDate_after);

            if(type.equals("before")) {
                if (date.getTime() > minDate_before.getTime() && date.getTime() < maxDate_before.getTime()) {
                    return true;
                }
            }
            if(type.equals("after")){
//                if (date.getTime() > minDate_after.getTime() && date.getTime() < maxDate_after.getTime()) {
//                    return true;
//                }
                //20240604需求变更 5月1号开户后可抽奖
                if (date.getTime() > minDate_after.getTime()) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 抽奖
     * <AUTHOR>
     * @date 2024/4/10 16:18
     * @param actCode
     * @param uid
     * @param pid
     * @param source
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.Lottery0808PrizeDO>
     */
    @Override
    public Result<Lottery0808PrizeDO> doLottery(String actCode,String uid,String pid,String source) {
        String lockKey = CacheKey_DoLotteryLock;
        try {
            if (redissonDistributionLock.tryLock(lockKey, TimeUnit.SECONDS, 10)) {
                LoginUserInfoVO userInfoVO = userService.getBoundUserInfo(uid, pid);
                if (userInfoVO == null || userInfoVO.getMobileX() == null) {
                    return Result.buildErrorResult("-1", "未绑定手机号，无法抽奖");
                }

                //判断是否抽过奖
                List<Act588BenifitRecordDO> recordList = getMyLotteryList(actCode, uid);
                if (recordList != null && recordList.size() > 0) {
                    return Result.buildErrorResult("-1", "已抽过奖");
                }

                //从Redis Set集合中随机抽取一个奖品编号
                Integer pNum = pid_588.indexOf(pid) > -1 ? getPrizesNum() : getPrizesNum_ds();
                if (pNum == -1) {
                    return Result.buildErrorResult("-1", "抽奖失败，请联系客服");
                }

                //根据奖品编号查询对应的奖品详情
                Lottery0808PrizeDO prizeDO = initLotteryList().stream().filter(x -> x.id.equals(pNum)).findFirst().get();
                if (prizeDO == null) {
                    return Result.buildErrorResult("-1", "找不到奖品，请联系客服");
                }

                //记录中奖明细
                Act588BenifitRecordDO benifitRecordDO = new Act588BenifitRecordDO();
                benifitRecordDO.uid = uid;
                benifitRecordDO.actCode = actCode;
                benifitRecordDO.benefitId = pNum;
                benifitRecordDO.benefitName = prizeDO.name;
                benifitRecordDO.source = source;
                benifitRecordDO.writeTime = new Date();
                benifitRecordDO.benefitConfirm = 0;
                int ret = act588benifitrecordMapper.insert(benifitRecordDO);
                if (ret > 0) {
                    if (pid_tj.indexOf(pid) > -1) {
                        //天玑用户抽奖，不赠送，直接推送至机构黑名单
                        pushBlackListQS(userInfoVO.getAccount(), prizeDO.taskCode, prizeDO.name, pid);
                    } else {
                        //异步发奖品
                        CompletableFuture.runAsync(() -> {
                            //发奖
                            Result<String> result = sendPrize(uid, pid, prizeDO, userInfoVO);
                            if (result.isSuccess()) {
                                //修改领取状态为已领取
                                act588benifitrecordMapper.updateBenefitConfirm(benifitRecordDO.id);
                                //刷新中奖记录
                                refreshMyLotteryList(actCode, uid);
                                //推送名单至机构黑名单
                                pushBlackListQS(userInfoVO.getAccount(), prizeDO.taskCode, prizeDO.name, pid);
                            } else {
                                log.error("湘财开户发放奖品失败：uid:" + uid + ",prize:" + prizeDO + ",ret:" + ret);
                            }
                        }, executor);
                    }
                } else {
                    return Result.buildErrorResult("-1", "抽奖失败，请联系业务员");
                }
                return Result.buildSuccessResult(prizeDO);
            } else {
                return Result.buildErrorResult("-1", "抽奖失败，请联系业务员");
            }
        } finally {
            redissonDistributionLock.unlock(lockKey);
        }
    }

    /**
     * 发放奖品(奖品类型 1：积分类 2:使用期  3：特权类  4：优惠券 5:线下赠送)
     * <AUTHOR>
     * @date 2024/4/10 16:16
     * @param uid
     * @param pid
     * @param prizeDO
     * @param userInfoVO
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    public Result<String> sendPrize(String uid,String pid,Lottery0808PrizeDO prizeDO,LoginUserInfoVO userInfoVO) {
        Result<String> ret = new Result<>();
        switch (prizeDO.type) {
            case 2:
                String pacCode = getTaskCodeByPid(prizeDO.taskCode,pid);

                ret = sendPrivilegeCommonNew(uid, pid, "湘财开户抽奖活动", pacCode, 1, userInfoVO);
                break;
            case 5:
                //线下赠送奖品是否已抽中
                redisService.set(cacheKey_hasOnlyPrize,true);
                ret.setSuccess(true);
                ret.setCode("200");
                break;
            default:
                break;
        }
        return ret;
    }

    /**
     * 小智盈3个月使用期：40%
     * 小智盈6个月使用期：30%
     * 小智盈9个月使用期：20%
     * 小智盈12个月使用期：9%
     * 大师12个月使用期：1%
     * 随机抽取一个奖品
     * <AUTHOR>
     * @date 2024/4/10 11:39
     * @return null
     */
    @Override
    public Integer getPrizesNum() {
        Long count = redisService.sSize(cacheKey_PrizeSet);
        if (count <= 0) {
            redisService.setAdd(cacheKey_PrizeSet, prizesArray);

            //奖品5 只中一次
//            Object obj = redisService.get(cacheKey_hasOnlyPrize);
//            if (obj != null && (Boolean) obj) {
//                redisService.sRem(cacheKey_PrizeSet, 100);
//            }
        }
        Integer prizesIndex = (Integer) redisService.sPop(cacheKey_PrizeSet);
        if (prizesIndex >= 1 && prizesIndex <= 40) {
            return 1;
        } else if (prizesIndex >= 41 && prizesIndex <= 70) {
            return 2;
        } else if (prizesIndex >= 71 && prizesIndex <= 90) {
            return 3;
        } else if (prizesIndex >= 91 && prizesIndex <= 99) {
            return 4;
        } else if (prizesIndex >= 100) {
            return 5;
        } else {
            return -1;
        }
    }

    /**
     * 大师1个月60%
     * 大师2个月30%
     * 大师3个月7%
     * 大师6个月2%
     * 大师12个月1%
     * 随机抽取一个奖品
     * <AUTHOR>
     * @date 2024/6/3 15:58
     * @return java.lang.Integer
     */

    public Integer getPrizesNum_ds() {
        Long count = redisService.sSize(cacheKey_PrizeSet_ds);
        if (count <= 0) {
            redisService.setAdd(cacheKey_PrizeSet_ds, prizesArray);
        }
        Integer prizesIndex = (Integer) redisService.sPop(cacheKey_PrizeSet_ds);
        if (prizesIndex >= 1 && prizesIndex <= 60) {
            return 6;
        } else if (prizesIndex >= 61 && prizesIndex <= 90) {
            return 7;
        } else if (prizesIndex >= 91 && prizesIndex <= 97) {
            return 8;
        } else if (prizesIndex >= 98 && prizesIndex <= 99) {
            return 9;
        } else if (prizesIndex >= 100) {
            return 10;
        } else {
            return -1;
        }
    }

    /**
     * 获取我的中奖记录
     * <AUTHOR>
     * @date 2023/7/26 14:40
     * @param actCode
     * @param uid
     * @return java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>
     */
    @Override
    public List<Act588BenifitRecordDO> getMyLotteryList(String actCode,String uid) {
        //获取我的中奖记录
        String key = cacheKey_getMyLotteryList + actCode + ":" + uid;
        List<Act588BenifitRecordDO> list = redisService.getList(key, Act588BenifitRecordDO.class);
        if (list == null) {
            list = refreshMyLotteryList(actCode, uid);
        }
        Collections.sort(list,Collections.reverseOrder());
        return list;
    }

    /**
     * 刷新中奖记录
     * <AUTHOR>
     * @date 2024/4/10 16:31
     * @param actCode
     * @param uid
     * @return java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>
     */
    @Override
    public List<Act588BenifitRecordDO> refreshMyLotteryList(String actCode,String uid) {
        String key = cacheKey_getMyLotteryList + actCode + ":" + uid;

        List<Act588BenifitRecordDO> list = act588benifitrecordMapper.selectByUidAndActCode(uid, actCode);
        if (list != null) {
            List<Act588BenifitRecordDO> filterList = list.stream().filter(x -> x.benefitConfirm == 1).collect(Collectors.toList());
            if (filterList != null) {
                //60天结束后 缓存自动失效
                redisService.set(key, filterList, 60L, TimeUnit.DAYS);
                return filterList;
            }
        }

        return null;
    }

    /**
     * 获取用户开户时间
     * <AUTHOR>
     * @date 2024/4/10 13:55
     * @param mobile
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @Override
    public String queryOpenTimeQS(String mobile) {
        String url = MessageFormat.format(queryStatusQSUrl, mobile);
        if (url != null) {
            String result = OkHttpUtil.get(url, null);
            if (result != null) {
                QSKHInfoDO qsKHInfoDO = JsonUtil.toBean(result, QSKHInfoDO.class);
                if (qsKHInfoDO.data != null && qsKHInfoDO.data.openTime != null) {
                    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return formatter.format(qsKHInfoDO.data.openTime);
                }
            }
        }
        return null;
    }

    /**
     * 中奖用户推送湘财接口
     * <AUTHOR>
     * @date 2024/4/10 14:29
     * @param account
     * @param pacCode
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    public Result<String> pushBlackListQS(String account,String taskCode,String name,String pid) {
        try {
            String pacCode = getTaskCodeByPid(taskCode,pid);

            QSKHGiftBlackInfoDO qsKHGiftBlackInfoDO = new QSKHGiftBlackInfoDO();
            qsKHGiftBlackInfoDO.caller = "小智盈抽奖";
            //1-EM账号;2-手机号（0x字符串）
            qsKHGiftBlackInfoDO.accountType = 1;
            qsKHGiftBlackInfoDO.account = account;
            qsKHGiftBlackInfoDO.batchNumber = pacCode;
            qsKHGiftBlackInfoDO.ignore = 3;
            qsKHGiftBlackInfoDO.source = 7;
            qsKHGiftBlackInfoDO.remark = pacCode + name;
            return Result.buildSuccessResult(OkHttpUtil.postJsonParams(pushBlackListQSUrl, JSON.toJSONString(qsKHGiftBlackInfoDO)));
        } catch (Exception e) {
            log.error(MessageFormat.format("pushBlackListQS推送失败 account:{0},ex:{1}", account, e.getMessage()));
            return Result.buildErrorResult("-1", "推送失败");
        }
    }

    /**
     * 领特权/送使用期  通用接口
     * <AUTHOR>
     * @date 2023/7/27 16:22
     * @param uid
     * @param pid
     * @param reason
     * @param activityID
     * @param accountType
     * @param loginUserInfoVO
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    public Result<String> sendPrivilegeCommonNew(String uid, String pid, String reason, String activityID, Integer accountType, LoginUserInfoVO loginUserInfoVO){
        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(activityID);
        sendPrivilegeDTO.setReason(reason);
        sendPrivilegeDTO.setApplyUserID("scb_public");
        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();

        //2:手机号 1：em号  领取特权指定2  延期指定1
        createActivityGrantApplyAccountDTO.setAccountType(accountType);
        createActivityGrantApplyAccountDTO.setAccount(loginUserInfoVO.getAccount());
        createActivityGrantApplyAccountDTO.setMID(loginUserInfoVO.getMobileX());
        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        Result<String> ret = logisticsService.sendPrivilegeResult(sendPrivilegeDTO);
        return ret;
    }



    /**
     * 获取奖品列表
     *
     * @param
     * @return null
     * <AUTHOR>
     * @date 2023/7/26 13:40
     */
    public List<Lottery0808PrizeDO> initLotteryList() {
        //生产环境：3：PAC1240410144538351 6：PAC1240410144657879 9：PAC1240410144801527 12：PAC1240410144845894

        //测试环境：3：PAC1240407175451220 6：PAC124040717555063 9：PAC1240407175625616 12：PAC1240410144845894

        List<Lottery0808PrizeDO> list = new ArrayList<>();
        list.add(new Lottery0808PrizeDO(1, 2, "小智盈3个月", "PAC1240410144538351", "小智盈3个月", 3));
        list.add(new Lottery0808PrizeDO(2, 2, "小智盈6个月", "PAC1240410144657879", "小智盈6个月", 6));
        list.add(new Lottery0808PrizeDO(3, 2, "小智盈9个月", "PAC1240410144801527", "小智盈9个月", 9));
        list.add(new Lottery0808PrizeDO(4, 2, "小智盈12个月", "PAC1240410144845894", "小智盈12个月", 12));
        list.add(new Lottery0808PrizeDO(5, 5, "智盈大师12个月", "", "智盈大师12个月", 12));

        //大师
        list.add(new Lottery0808PrizeDO(6, 2, "智盈大师1个月", "888020000:PAC1240604111107206||888080000:PAC1240604103228544", "智盈大师1个月", 1));
        list.add(new Lottery0808PrizeDO(7, 2, "智盈大师2个月", "888020000:PAC1240604111214380||888080000:PAC1240604103358634", "智盈大师2个月", 2));
        list.add(new Lottery0808PrizeDO(8, 2, "智盈大师3个月", "888020000:PAC124060411125813||888080000:PAC1240604103501471", "智盈大师3个月", 3));
        list.add(new Lottery0808PrizeDO(9, 2, "智盈大师6个月", "888020000:PAC1240604111335656||888080000:PAC1240604103602389", "智盈大师6个月", 6));
        list.add(new Lottery0808PrizeDO(10, 2, "智盈大师12个月", "888020000:PAC1240604111419258||888080000:PAC1240604103654259", "智盈大师12个月", 12));
        return list;
    }

    /**
     * 根据pid获取对应的pacCode
     * <AUTHOR>
     * @date 2024/6/4 11:36
     * @param taskCode
     * @param pid
     * @return java.lang.String
     */
    public String getTaskCodeByPid(String taskCode,String pid){
        String pacCode = "";
        if(taskCode.indexOf("||")>-1){
            //888020000:PAC1240604111107206||888080000:PAC1240604103228544
            String[] taskCodes = taskCode.split("\\|\\|");
            for (String tc : taskCodes) {
                String[] taskCodeArr = tc.split(":");
                if (taskCodeArr.length == 2) {
                    String pid_ = taskCodeArr[0];
                    String taskCode_ = taskCodeArr[1];
                    if(pid.equals(pid_)){
                        pacCode = taskCode_;
                    }
                }
            }
        }else{
            pacCode = taskCode;
        }
        return pacCode;
    }
}
