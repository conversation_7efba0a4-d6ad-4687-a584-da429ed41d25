package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.RedissonDistributionLock;
import cn.emoney.mapper.activity.Act588benifitrecordMapper;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.Lottery0808PrizeDO;
import cn.emoney.pojo.LotteryCountDO;
import cn.emoney.pojo.LotteryLogisticsPackageConfDO;
import cn.emoney.pojo.bo.*;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.pojo.vo.OrderProdListVO;
import cn.emoney.pojo.vo.UserLoginIdInfoVO;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.Lottery20240615Service;
import cn.emoney.service.PointService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-06-11
 */
@Service
@Slf4j
public class Lottery20240615ServiceImpl implements Lottery20240615Service {
    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserService userService;

    @Autowired
    private PointService pointService;

    @Value("${renewDsLogisticsPkg}")
    private String renewDsLogisticsPkg;

    @Value("${renewLogisticsPkg}")
    private String renewLogisticsPkg;

    @Value("${renewLogisticsBeginTime}")
    private String renewLogisticsBeginTime;

    @Value("${renewLogisticsEndTime}")
    private String renewLogisticsEndTime;

    @Value("${renewTJLogisticsPkg}")
    private String renewTJLogisticsPkg;

    @Value("${renewTJLogisticsBeginTime}")
    private String renewTJLogisticsBeginTime;

    @Value("${renewTJLogisticsEndTime}")
    private String renewTJLogisticsEndTime;

    @Autowired(required = false)
    private Act588benifitrecordMapper act588benifitrecordMapper;

    @Autowired
    private RedissonDistributionLock redissonDistributionLock;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;

    private final static String cacheKey_LotteryCount = RedisConstants.Redis_Pre_Activity + "LotteryCount20240615:";
    private final static String cacheKey_ValidLotteryCount = RedisConstants.Redis_Pre_Activity + "ValidLotteryCount20240615:";
    private final static String cacheKey_LotteryCount_common = RedisConstants.Redis_Pre_Activity + "LotteryCount:Common:";
    private final static String cacheKey_ValidLotteryCount_common = RedisConstants.Redis_Pre_Activity + "ValidLotteryCount:";
    private final static String cacheKey_getMyLotteryList = RedisConstants.Redis_Pre_Activity + "getMyLotteryList20240615:";
    private final static String cacheKey_PrizeSet = RedisConstants.Redis_Pre_Activity + "20240615:PrizeSet:";
    private final String CacheKey_DoLotteryLock = RedisConstants.RedisKey_Pre_Activity + "20240615:DoLotteryLock";
    private final String CacheKey_AllCount = "renewds20240615_count";
    /**
     * 设置奖品编号数组长度
     */
    private final static Integer ARRAY_SIZE = 1000;
    /**
     * 初始化奖品编号数组
     */
    private static Integer[] prizesArray = new Integer[ARRAY_SIZE];

    static {
        for (int i = 0; i < ARRAY_SIZE; i++) {
            prizesArray[i] = i + 1;
        }
    }

    /**
     * 大师续费抽奖
     *
     * @param actCode
     * @param uid
     * @param pid
     * @param source
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.Lottery0808PrizeDO>
     * <AUTHOR>
     * @date 2024/2/29 13:36
     */
    @Override
    public Result<Lottery0808PrizeDO> doLottery(String actCode, String uid, String pid, String source) {
        String lockKey = CacheKey_DoLotteryLock;

        try {
            if (redissonDistributionLock.tryLock(lockKey, TimeUnit.SECONDS, 10)) {
                LoginUserInfoVO userInfoVO = userService.getBoundUserInfo(uid, pid);
                if (userInfoVO == null || userInfoVO.getMobileX() == null) {
                    return Result.buildErrorResult("-1", "未绑定手机号，无法抽奖");
                }

                //判断是否还有抽奖次数
                LotteryCountDO lotteryCountDO = getLotteryCount(actCode, uid, userInfoVO.getMobileX());
                if (lotteryCountDO == null || lotteryCountDO.pCount < 1) {
                    return Result.buildErrorResult("-1", "抽奖次数已用完");
                }

                //获取物流订单号(赠送特权/使用期/积分时绑定使用，解决退货后赠品一并退)
                String orderID = lotteryCountDO.packageList.stream().findFirst().get().orderId;
                String detID = lotteryCountDO.packageList.stream().findFirst().get().detId;

                //从Redis Set集合中随机抽取一个奖品编号
                Integer pNum = getPrizesNum();
                if (pNum == -1) {
                    return Result.buildErrorResult("-1", "抽奖失败，请联系客服");
                }

                //根据奖品编号查询对应的奖品详情
                Lottery0808PrizeDO prizeDO = initLotteryList().stream().filter(x -> x.id.equals(pNum)).findFirst().get();
                if (prizeDO == null) {
                    return Result.buildErrorResult("-1", "找不到奖品，请联系客服");
                }

                //记录中奖明细
                Act588BenifitRecordDO benifitRecordDO = new Act588BenifitRecordDO();
                benifitRecordDO.uid = uid;
                benifitRecordDO.actCode = actCode;
                benifitRecordDO.benefitId = pNum;
                benifitRecordDO.benefitName = prizeDO.name;
                benifitRecordDO.source = source;
                benifitRecordDO.writeTime = new Date();
                benifitRecordDO.benefitConfirm = 0;
                int ret = act588benifitrecordMapper.insert(benifitRecordDO);
                if (ret > 0) {
                    //异步发奖品
                    //CompletableFuture.runAsync(() -> {
                    Result<String> result = sendPrize(uid, pid, prizeDO, userInfoVO, orderID, detID);
                    if (result.isSuccess()) {
                        //修改领取状态为已领取
                        act588benifitrecordMapper.updateBenefitConfirm(benifitRecordDO.id);
                        //刷新中奖记录
                        refreshMyLotteryList(actCode, uid);
                        //刷新抽奖次数
                        refreshLotteryCount(actCode, uid, userInfoVO.getMobileX());
                        //本次活动总抽奖次数+1
                        userService.AddUsedCount(CacheKey_AllCount);
                    } else {
                        log.error("小智盈续费发放奖品失败：uid:" + uid + ",剩余抽奖次数：" + lotteryCountDO.pCount + ",prize:" + prizeDO + ",ret:" + ret);
                        return Result.buildErrorResult("-1", "抽奖失败，请联系业务员");
                    }
                    //}, executor);

                } else {
                    return Result.buildErrorResult("-1", "抽奖失败，请联系业务员");
                }
                return Result.buildSuccessResult(prizeDO);

            } else {
                return Result.buildErrorResult("-1", "抽奖失败，请联系业务员");
            }
        } finally {
            redissonDistributionLock.unlock(lockKey);
        }

    }

    /**
     * 发放奖品(奖品类型 1：积分类 2:使用期  3：特权类 )
     *
     * @param uid
     * @param pid
     * @param prizeDO
     * @param userInfoVO
     * @return cn.emoney.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2024/2/29 13:45
     */
    public Result<String> sendPrize(String uid, String pid, Lottery0808PrizeDO prizeDO, LoginUserInfoVO userInfoVO, String orderID, String detID) {
        Result<String> ret = new Result<>();
        switch (prizeDO.type) {
            case 1:
                PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                requestDTO.platform = "1";
                requestDTO.pid = pid;
                requestDTO.uid = uid;
                requestDTO.subId = "";
                requestDTO.taskId = prizeDO.taskCode;
                boolean addRet = pointService.pointRecordAdd(requestDTO);
                if (addRet) {
                    ret.setCode("200");
                    ret.setSuccess(true);
                    ret.setMsg("积分赠送成功");
                } else {
                    ret.setSuccess(false);
                    ret.setMsg("积分赠送失败");
                }
                break;
            case 2:
                //赠送优惠券
                SendCouponRequestDTO req = new SendCouponRequestDTO();
                req.PRESENT_ACCOUNT_TYPE = 2;
                req.PRESENT_ACCOUNT = userInfoVO.getMobileX();
                req.COUPON_ACTIVITY_ID = prizeDO.taskCode;
                req.COUPON_RULE_PRICE = (double) prizeDO.pCount;
                req.PRESENT_PERSON = "618大师续费抽奖活动";
                LogisticsResult<String> retCoupon = logisticsService.sendCoupon(req);
                if (retCoupon.getCode() == 0) {
                    ret.setCode("200");
                    ret.setSuccess(true);
                    ret.setMsg("优惠券赠送成功");
                } else {
                    ret.setSuccess(false);
                    ret.setMsg("优惠券赠送失败");
                }
                break;
            case 3:
                String upid = pid;
                if (pid == "*********") {
                    upid = "*********";
                }
                if (pid == "*********") {
                    upid = "*********";
                }

                String pacCode = "";
                String[] pacCodeList = prizeDO.taskCode.split("@");
                for (int i = 0; i < pacCodeList.length; i++) {
                    String[] pid_pacCode = pacCodeList[i].split(":");
                    if (upid.equals(pid_pacCode[0])) {
                        pacCode = pid_pacCode[1];
                    }
                }
                ret = sendPrivilegeCommonNew(uid, upid, "618大师续费抽奖赠送使用期", pacCode, 1, userInfoVO, orderID, detID);
                break;
            case 4:
                //记录状态
                userService.AddCountByActCode("renewDs20240615_6", uid, System.currentTimeMillis() + "");

                ret.setSuccess(true);
                ret.setCode("0");
                break;
            case 5:
                //手动赠送
                ret.setSuccess(true);
                ret.setCode("0");
            default:
                break;
        }
        return ret;
    }

    /**
     * 500积分 30%(300‰)
     * 1000积分 13%(130‰)
     * 500代金券 13%(130‰)
     * 1000代金券 7%(70‰)
     * 大师使用期1个月 7%(70‰)
     * 益研究7月每日内部报告 29.7%(297‰) 调整为30%
     * 1v1对话益盟名师 0.2%(2‰) 调整为0%
     * 账号升级免费直通天玑 0.1%(1‰) 调整为0%
     *
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2024/2/29 13:46
     */
    @Override
    public Integer getPrizesNum() {
        Long count = redisService.sSize(cacheKey_PrizeSet);
        if (count <= 0) {
            redisService.setAdd(cacheKey_PrizeSet, prizesArray);
        }
        Integer prizesIndex = (Integer) redisService.sPop(cacheKey_PrizeSet);
        if (prizesIndex >= 1 && prizesIndex <= 300) {
            return 1;
        } else if (prizesIndex >= 301 && prizesIndex <= 430) {
            return 2;
        } else if (prizesIndex >= 431 && prizesIndex <= 560) {
            return 3;
        } else if (prizesIndex >= 561 && prizesIndex <= 630) {
            return 4;
        } else if (prizesIndex >= 631 && prizesIndex <= 700) {
            return 5;
        } else if (prizesIndex >= 701 && prizesIndex <= 1000) {
            return 6;
        } else {
            return -1;
        }

//        else if (prizesIndex >= 998 && prizesIndex <= 999) {
//            return 7;
//        } else if (prizesIndex >= 1000) {
//            return 8;
//        }
    }

    /**
     * 获取可用抽奖次数
     *
     * @param actCode
     * @param uid
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2024/2/29 14:24
     */
    @Override
    public LotteryCountDO getLotteryCount(String actCode, String uid, String mobileX) {
        String key = cacheKey_ValidLotteryCount + mobileX;
        LotteryCountDO count = redisService.get(key, LotteryCountDO.class);
        if (count == null || StringUtils.isEmpty(count.mobileX)) {
            return refreshLotteryCount(actCode, uid, mobileX);
        }
        return count;
    }

    /**
     * 刷新可用抽奖次数
     *
     * @param actCode
     * @param uid
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2024/2/29 14:25
     */
    @Override
    public LotteryCountDO refreshLotteryCount(String actCode, String uid, String mobileX) {
        String key = cacheKey_ValidLotteryCount + mobileX;
        //根据购买物流包获取可抽奖总次数
        LotteryCountDO lotteryCountDO = getLogisticsLotteryCount(mobileX);
        if (lotteryCountDO == null || StringUtils.isEmpty(lotteryCountDO.mobileX)) {
            return null;
        }

        Integer syCount = 0;
        List<Act588BenifitRecordDO> list = getMyLotteryList(actCode, uid);
        if (list != null) {
            List<Act588BenifitRecordDO> list1 = list.stream().filter(x -> x.benefitConfirm == 1).collect(Collectors.toList());
            if (list1 != null && list1.size() > 0) {
                //活动结束缓存自动失效
                syCount = lotteryCountDO.pCount - list1.size();

                lotteryCountDO.pCount = syCount;
            }
        }
        redisService.set(key, lotteryCountDO, 60L, TimeUnit.DAYS);
        return lotteryCountDO;
    }

    /**
     * 获取物流包对应的抽奖次数
     *
     * @param mobileX
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2024/2/29 18:43
     */
    public LotteryCountDO getLogisticsLotteryCount(String mobileX) {
        String key = cacheKey_LotteryCount + mobileX;
        Integer count = 0;

        LotteryCountDO lotteryCountDO = new LotteryCountDO();
        List<LotteryLogisticsPackageConfDO> getBuyPackageList = redisService.getList(key, LotteryLogisticsPackageConfDO.class);
        if (getBuyPackageList != null && getBuyPackageList.size() > 0) {
            for (LotteryLogisticsPackageConfDO item : getBuyPackageList) {
                count += item.pCount;
            }
            lotteryCountDO.packageList = getBuyPackageList;
            lotteryCountDO.pCount = count;
            lotteryCountDO.mobileX = mobileX;
        }

        return lotteryCountDO;
    }

    /**
     * 获取我的中奖记录
     *
     * @param actCode
     * @param uid
     * @return java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>
     * <AUTHOR>
     * @date 2024/2/28 17:19
     */
    @Override
    public List<Act588BenifitRecordDO> getMyLotteryList(String actCode, String uid) {
        //获取我的中奖记录
        String key = cacheKey_getMyLotteryList + actCode + ":" + uid;
        List<Act588BenifitRecordDO> list = redisService.getList(key, Act588BenifitRecordDO.class);
        if (list == null) {
            list = refreshMyLotteryList(actCode, uid);
        }
        Collections.sort(list, Collections.reverseOrder());
        return list;
    }

    /**
     * 刷新我的中奖记录缓存
     *
     * @param actCode
     * @param uid
     * @return java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>
     * <AUTHOR>
     * @date 2024/2/28 17:19
     */
    @Override
    public List<Act588BenifitRecordDO> refreshMyLotteryList(String actCode, String uid) {
        String key = cacheKey_getMyLotteryList + actCode + ":" + uid;

        List<Act588BenifitRecordDO> list = act588benifitrecordMapper.selectByUidAndActCode(uid, actCode);
        if (list != null) {
            List<Act588BenifitRecordDO> filterList = list.stream().filter(x -> x.benefitConfirm == 1).collect(Collectors.toList());
            if (filterList != null) {
                //60天结束后 缓存自动失效
                redisService.set(key, filterList, 60L, TimeUnit.DAYS);
                return filterList;
            }
        }

        return null;
    }

    /**
     * 获取奖品列表
     *
     * @return java.util.List<cn.emoney.pojo.Lottery0808PrizeDO>
     * <AUTHOR>
     * @date 2024/2/29 13:29
     */
    public List<Lottery0808PrizeDO> initLotteryList() {
        List<Lottery0808PrizeDO> list = new ArrayList<>();
        list.add(new Lottery0808PrizeDO(1, 1, "500积分", "1800815196993925120", "500积分", 500));
        list.add(new Lottery0808PrizeDO(2, 1, "1000积分", "1800815087418011648", "1000积分", 1000));
        list.add(new Lottery0808PrizeDO(3, 2, "500代金券", "cp-1240612181558203", "500代金券", 500));
        list.add(new Lottery0808PrizeDO(4, 2, "1000代金券", "cp-1240612181401984", "1000代金券", 1000));
        list.add(new Lottery0808PrizeDO(5, 3, "大师使用期+30天", "*********:PAC1240612172021570@*********:PAC1240612172024284", "大师使用期1个月", 30));
        list.add(new Lottery0808PrizeDO(6, 4, "益研究内部每日报告", "", "益研究内部每日报告", 0));
        list.add(new Lottery0808PrizeDO(7, 5, "1v1对话益盟名师", "", "1v1对话益盟名师", 0));
        list.add(new Lottery0808PrizeDO(8, 5, "账号升级免费直通天玑", "", "账号升级免费直通天玑", 0));
        return list;
    }

    /**
     * 领特权/送使用期  通用接口
     *
     * @param uid
     * @param pid
     * @param reason
     * @param activityID
     * @param accountType
     * @param loginUserInfoVO
     * @return cn.emoney.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/7/27 16:22
     */
    public Result<String> sendPrivilegeCommonNew(String uid, String pid, String reason, String activityID, Integer accountType, LoginUserInfoVO loginUserInfoVO, String orderID, String detID) {
        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(activityID);
        sendPrivilegeDTO.setReason(reason);
        sendPrivilegeDTO.setApplyUserID("scb_public");
        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();

        //2:手机号 1：em号  领取特权指定2  延期指定1
        createActivityGrantApplyAccountDTO.setAccountType(accountType);
        createActivityGrantApplyAccountDTO.setAccount(loginUserInfoVO.getAccount());
        createActivityGrantApplyAccountDTO.setMID(loginUserInfoVO.getMobileX());
        createActivityGrantApplyAccountDTO.setOrderID(orderID);
        createActivityGrantApplyAccountDTO.setOrderDetailID(detID);
        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        Result<String> ret = logisticsService.sendPrivilegeResult(sendPrivilegeDTO);
        return ret;
    }


    /**
     * 获取物流包对应的抽奖次数和活动code配置
     *
     * @return java.util.List<cn.emoney.pojo.LotteryLogisticsPackageConfDO>
     * <AUTHOR>
     * @date 2024/2/29 15:06
     */
    public List<LotteryLogisticsPackageConfDO> initLogisticsPackageConfList() {
        List<LotteryLogisticsPackageConfDO> list = new ArrayList<>();
        String[] pkgList = renewLogisticsPkg.split(",");
        int i = 1;
        for (String item : pkgList) {
            String acCode = item.split(":")[0];
            String pCount = item.split(":")[1];
            String actCode = item.split(":")[2];
            list.add(new LotteryLogisticsPackageConfDO(i, acCode, renewLogisticsBeginTime, renewLogisticsEndTime, Integer.parseInt(pCount), "", "", actCode, ""));
            i++;
        }
        return list;
    }

    /**
     * 获取acCodes
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/3/4 15:06
     */
    public String initLogisticsPackages() {
        List<LotteryLogisticsPackageConfDO> list = new ArrayList<>();
        String[] pkgList = renewLogisticsPkg.split(",");
        String acCodes = "";
        for (String item : pkgList) {
            String acCode = item.split(":")[0];
            acCodes += acCode + ",";
        }
        return acCodes.substring(0, acCodes.length() - 1);
    }

    /**
     * 自动获取用户购买的物流包抽奖次数配置
     * 1、获取购买指定物流包的用户合集（5分钟执行一次）
     * 2、判断物流包对应的可抽奖配置包 存入redis
     *
     * <AUTHOR>
     * @date 2024/2/29 15:43
     */
    @Override
    public void autoGetUserPackagePCount_0615() {
        try {
            // 定义日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat simpleFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            LocalDateTime renewBeginTime = LocalDateTime.parse(renewLogisticsBeginTime, formatter);
            LocalDateTime renewEndTime = LocalDateTime.parse(renewLogisticsEndTime, formatter);
            // 获取当前日期
            LocalDateTime nowTime = LocalDateTime.now();

            //物流包购买时间结束-退出
            if (nowTime.isAfter(renewEndTime)) {
                return;
            }

            // 获取前2小时的时间
            LocalDateTime fiveMinBefore = nowTime.minusHours(2);
            // 格式化日期
            String beginTime = fiveMinBefore.format(formatter);
            String endTime = nowTime.format(formatter);

            //新增逻辑，批量拉取物流订单，一次拉取15个物流包
            List<String> logisticsPackagesList = Arrays.asList(initLogisticsPackages().split(","));
            for (int i = 0; i < logisticsPackagesList.size(); i += 15) {
                int end = Math.min(i + 15, logisticsPackagesList.size());
                List<String> subList = logisticsPackagesList.subList(i, end);
                String activityCodes = String.join(",", subList);

                //获取物流订单
                OrderProdListDTO orderProdListDTO = new OrderProdListDTO();
                orderProdListDTO.setStockUpDate_Start(beginTime);
                orderProdListDTO.setStockUpDate_End(endTime);
                orderProdListDTO.setRefund_Sign(0);
                orderProdListDTO.setACTIVITY_CODE(activityCodes);
                List<OrderProdListVO> list = logisticsService.queryOrderProdList(orderProdListDTO);

                List<LotteryLogisticsPackageConfDO> packageConfDOList = initLogisticsPackageConfList();
                for (OrderProdListVO item :
                        list) {
                    String emCard = item.getEmCard().toLowerCase();
                    String mobileX = item.getMIDPWD();
                    String orderId = item.getORDER_ID();
                    String detId = item.getDetID();

                    String actCode = "";
                    //1、获取购买的物流包抽奖次数配置
                    LotteryLogisticsPackageConfDO filterPackageConfDO = packageConfDOList.stream().filter(x -> x.acCode.indexOf(item.ACTIVITY_CODE) > -1).findFirst().get();
                    //log.info("autoGetUserPackagePCount 查询订单号：" + JSON.toJSONString(filterPackageConfDO));
                    if (filterPackageConfDO != null) {
                        filterPackageConfDO.orderId = orderId;
                        filterPackageConfDO.detId = detId;
                        filterPackageConfDO.buyTime = simpleFormatter.format(item.StockUpDate);
                        actCode = filterPackageConfDO.actCode;
                    }

                    //String key = cacheKey_LotteryCount_common  + mobileX;
                    String key = cacheKey_LotteryCount_common + emCard;
                    //2、查询是否已有购买的物流包
                    List<LotteryLogisticsPackageConfDO> getBuyPackageRedisList = redisService.getList(key, LotteryLogisticsPackageConfDO.class);
                    if (getBuyPackageRedisList == null || getBuyPackageRedisList.size() <= 0) {
                        //3、无购买记录：存入购买列表
                        if (getBuyPackageRedisList == null) {
                            getBuyPackageRedisList = new ArrayList<>();
                        }
                        getBuyPackageRedisList.add(filterPackageConfDO);

                        //log.info("autoGetUserPackagePCount 购买物流包抽奖配置1 mobile:" + mobileX + " package:" + JSON.toJSONString(filterPackageConfDO));
                        log.info("autoGetUserPackagePCount 购买物流包抽奖配置1 emcard:" + emCard + " package:" + JSON.toJSONString(filterPackageConfDO));
                    } else {
                        //4、有购买记录
                        List<LotteryLogisticsPackageConfDO> buyList = getBuyPackageRedisList;
                        int buyCount = 0;
                        for (int j = 0; j < buyList.size(); j++) {
                            LotteryLogisticsPackageConfDO buyItem = buyList.get(j);
                            if (buyItem.orderId.equals(item.getORDER_ID()) && buyItem.detId.equals(item.getDetID())) {
                                buyCount++;
                            }
                        }
                        //判断当前订单是否在购买列表中,不在已购买列表则累加购买次数
                        if (buyCount == 0) {
                            getBuyPackageRedisList.add(filterPackageConfDO);

                            //log.info("autoGetUserPackagePCount 购买物流包抽奖配置2 mobile:" + mobileX + " package:" + JSON.toJSONString(filterPackageConfDO));
                            log.info("autoGetUserPackagePCount 购买物流包抽奖配置2 emcard:" + emCard + " package:" + JSON.toJSONString(filterPackageConfDO));
                        }
                    }

                    redisService.set(key, getBuyPackageRedisList, 90L, TimeUnit.DAYS);

                }

                try {
                    Thread.sleep(2000); // 暂停 2 秒（2000 毫秒）
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }


        } catch (Exception exp) {
            log.error("autoGetUserPackagePCount:" + exp.getMessage());
        }
    }

    /**
     * 退货订单清空抽奖次数
     *
     * <AUTHOR>
     * @date 2024/2/29 16:58
     */
    @Override
    public void autoClearUserPackagePCount_0615() {
        try {
            // 定义日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            LocalDateTime renewBeginTime = LocalDateTime.parse(renewLogisticsBeginTime, formatter);
            LocalDateTime renewEndTime = LocalDateTime.parse(renewLogisticsEndTime, formatter);
            // 获取当前日期
            LocalDateTime nowTime = LocalDateTime.now();

            //物流包购买时间结束-退出
            if (nowTime.isAfter(renewEndTime)) {
                return;
            }

            // 获取前10分钟的时间
            LocalDateTime fiveMinBefore = nowTime.minusHours(2);//nowTime.minusMinutes(5);
            // 格式化日期
            String beginTime = fiveMinBefore.format(formatter);
            String endTime = nowTime.format(formatter);

            //新增逻辑，批量拉取物流订单，一次拉取15个物流包
            List<String> logisticsPackagesList = Arrays.asList(initLogisticsPackages().split(","));
            for (int i = 0; i < logisticsPackagesList.size(); i += 15) {
                int end = Math.min(i + 15, logisticsPackagesList.size());
                List<String> subList = logisticsPackagesList.subList(i, end);
                String activityCodes = String.join(",", subList);

                //获取物流订单
                OrderProdListDTO orderProdListDTO = new OrderProdListDTO();
                orderProdListDTO.setCancel_Time_Start(beginTime);
                orderProdListDTO.setCancel_Time_End(endTime);
                orderProdListDTO.setRefund_Sign(-1);
                orderProdListDTO.setACTIVITY_CODE(activityCodes);
                List<OrderProdListVO> list = logisticsService.queryOrderProdList(orderProdListDTO);

                for (OrderProdListVO item :
                        list) {
                    String mobileX = item.getMIDPWD();
                    String emCard = item.getEmCard().toLowerCase();
                    //String key = cacheKey_LotteryCount_common + mobileX;
                    String key = cacheKey_LotteryCount_common + emCard;
                    String actCode = "";

                    //1、获取已购买的物流包列表
                    List<LotteryLogisticsPackageConfDO> getBuyPackageRedisList = redisService.getList(key, LotteryLogisticsPackageConfDO.class);
                    if (getBuyPackageRedisList == null) {
                        //2、无购买记录：忽略
                    } else {
                        //3、有购买记录
                        List<LotteryLogisticsPackageConfDO> cancelList = getBuyPackageRedisList;
                        for (LotteryLogisticsPackageConfDO item1 : cancelList) {
                            if (item1.acCode.equals(item.ACTIVITY_CODE)) {
                                actCode = item1.actCode;
                                //4、购买记录为当前退货包：从redis中移除
                                getBuyPackageRedisList.remove(item1);

                                redisService.set(key, getBuyPackageRedisList, 90L, TimeUnit.DAYS);

                                //log.info("autoClearUserPackagePCount 退货清空抽奖次数 mobile:" + mobileX + " getBuyPackageRedisList:" + JSON.toJSONString(getBuyPackageRedisList));
                                log.info("autoClearUserPackagePCount 退货清空抽奖次数 emcard:" + emCard + " getBuyPackageRedisList:" + JSON.toJSONString(getBuyPackageRedisList));
                            } else {
                                //5、购买记录非当前退货包：忽略
                            }
                        }
                    }

                }

                try {
                    Thread.sleep(2000); // 暂停 2 秒（2000 毫秒）
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        } catch (
                Exception exp) {
            log.error("autoClearUserPackagePCount:" + exp.getMessage());
        }
    }

    /**
     * 获取物流包对应的抽奖次数和活动code配置
     * @return java.util.List<cn.emoney.pojo.LotteryLogisticsPackageConfDO>
     * <AUTHOR>
     * @date 2024/2/29 15:06
     */
    public List<LotteryLogisticsPackageConfDO> initLogisticsPackageConfList_tj() {
        List<LotteryLogisticsPackageConfDO> list = new ArrayList<>();
        String[] pkgList = renewTJLogisticsPkg.split(",");
        int i = 1;
        for (String item : pkgList) {
            String acCode = item.split(":")[0];
            String pCount = item.split(":")[1];
            String actCode = item.split(":")[2];
            list.add(new LotteryLogisticsPackageConfDO(i, acCode, renewTJLogisticsBeginTime, renewTJLogisticsEndTime, Integer.parseInt(pCount),"","",actCode,""));
            i++;
        }
        return list;
    }

    /**
     * 获取acCodes
     * <AUTHOR>
     * @date 2024/3/4 15:06
     * @return java.lang.String
     */
    public String initLogisticsPackages_tj() {
        List<LotteryLogisticsPackageConfDO> list = new ArrayList<>();
        String[] pkgList = renewTJLogisticsPkg.split(",");
        String acCodes = "";
        for (String item : pkgList) {
            String acCode = item.split(":")[0];
            acCodes += acCode + ",";
        }
        return acCodes.substring(0, acCodes.length() - 1);
    }
    /**
     * 自动获取用户购买的物流包抽奖次数配置
     * 1、获取购买指定物流包的用户合集（5分钟执行一次）
     * 2、判断物流包对应的可抽奖配置包 存入redis
     *
     * <AUTHOR>
     * @date 2024/2/29 15:43
     */
    @Override
    public void autoGetUserPackagePCount_tj() {
        try {
            // 定义日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat simpleFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            LocalDateTime renewBeginTime = LocalDateTime.parse(renewTJLogisticsBeginTime,formatter);
            LocalDateTime renewEndTime = LocalDateTime.parse(renewTJLogisticsEndTime,formatter);
            // 获取当前日期
            LocalDateTime nowTime = LocalDateTime.now();

            //物流包购买时间结束-退出
            if(nowTime.isAfter(renewEndTime)){
                return;
            }

            // 获取前10分钟的时间
            LocalDateTime fiveMinBefore = nowTime.minusHours(2);
            // 格式化日期
            String beginTime = fiveMinBefore.format(formatter);
            String endTime =  nowTime.format(formatter);

            //获取物流订单
            OrderProdListDTO orderProdListDTO = new OrderProdListDTO();
            orderProdListDTO.setStockUpDate_Start(beginTime);
            orderProdListDTO.setStockUpDate_End(endTime);
            orderProdListDTO.setRefund_Sign(0);
            orderProdListDTO.setACTIVITY_CODE(initLogisticsPackages_tj());
            List<OrderProdListVO> list = logisticsService.queryOrderProdList(orderProdListDTO);

            List<LotteryLogisticsPackageConfDO> packageConfDOList = initLogisticsPackageConfList_tj();
            for (OrderProdListVO item :
                    list) {
                String mobileX = item.getMIDPWD();
                String emCard = item.getEmCard().toLowerCase();
                String orderId = item.getORDER_ID();
                String detId = item.getDetID();
                String prodType = item.getProdType();//A23004 换购订单不累计抽奖次数
//                if(prodType.equals("A23004")){
//                    continue;
//                }

                String actCode = "";
                //1、获取购买的物流包抽奖次数配置
                LotteryLogisticsPackageConfDO filterPackageConfDO = packageConfDOList.stream().filter(x -> x.acCode.indexOf(item.ACTIVITY_CODE) > -1).findFirst().get();
                //log.info("autoGetUserPackagePCount 查询订单号：" + JSON.toJSONString(filterPackageConfDO));
                if (filterPackageConfDO != null) {
                    filterPackageConfDO.orderId = orderId;
                    filterPackageConfDO.detId = detId;
                    filterPackageConfDO.buyTime = simpleFormatter.format(item.StockUpDate);
                    actCode = filterPackageConfDO.actCode;
                }

                //String key = cacheKey_LotteryCount_common + mobileX;
                String key = cacheKey_LotteryCount_common + emCard;
                //2、查询是否已有购买的物流包
                List<LotteryLogisticsPackageConfDO> getBuyPackageRedisList = redisService.getList(key, LotteryLogisticsPackageConfDO.class);
                if (getBuyPackageRedisList == null || getBuyPackageRedisList.size() <= 0) {
                    //3、无购买记录：存入购买列表
                    if (getBuyPackageRedisList == null) {
                        getBuyPackageRedisList = new ArrayList<>();
                    }
                    getBuyPackageRedisList.add(filterPackageConfDO);

                    //log.info("autoGetUserPackagePCount_tj 购买物流包抽奖配置1 mobile:" + mobileX + " package:" + JSON.toJSONString(filterPackageConfDO));
                    log.info("autoGetUserPackagePCount_tj 购买物流包抽奖配置1 emcard:" + emCard + " package:" + JSON.toJSONString(filterPackageConfDO));
                } else {
                    //4、有购买记录
                    List<LotteryLogisticsPackageConfDO> buyList = getBuyPackageRedisList;
                    int buyCount = 0;
                    for (int i = 0; i < buyList.size(); i++) {
                        LotteryLogisticsPackageConfDO buyItem = buyList.get(i);
                        if(buyItem.orderId.equals(item.getORDER_ID())){
                            buyCount++;
                        }
                    }
                    //判断当前订单是否在购买列表中,不在已购买列表则累加购买次数
                    if (buyCount == 0) {
                        getBuyPackageRedisList.add(filterPackageConfDO);

                        //log.info("autoGetUserPackagePCount_tj 购买物流包抽奖配置2 mobile:" + mobileX + " package:" + JSON.toJSONString(filterPackageConfDO));
                        log.info("autoGetUserPackagePCount_tj 购买物流包抽奖配置2 emcard:" + emCard + " package:" + JSON.toJSONString(filterPackageConfDO));
                    }
                }

                redisService.set(key, getBuyPackageRedisList, 90L, TimeUnit.DAYS);

            }
        } catch (Exception exp) {
            log.error("autoGetUserPackagePCount_tj:" + exp.getMessage());
        }
    }

    /**
     * 退货订单清空抽奖次数
     *
     * <AUTHOR>
     * @date 2024/2/29 16:58
     */
    @Override
    public void autoClearUserPackagePCount_tj() {
        try {
            // 定义日期格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            LocalDateTime renewBeginTime = LocalDateTime.parse(renewTJLogisticsBeginTime,formatter);
            LocalDateTime renewEndTime = LocalDateTime.parse(renewTJLogisticsEndTime,formatter);
            // 获取当前日期
            LocalDateTime nowTime = LocalDateTime.now();

            //物流包购买时间结束-退出
            if(nowTime.isAfter(renewEndTime)){
                return;
            }

            // 获取前10分钟的时间
            LocalDateTime fiveMinBefore = nowTime.minusHours(2);
            // 格式化日期
            String beginTime = fiveMinBefore.format(formatter);
            String endTime = nowTime.format(formatter);

            //获取物流订单
            OrderProdListDTO orderProdListDTO = new OrderProdListDTO();
            orderProdListDTO.setCancel_Time_Start(beginTime);
            orderProdListDTO.setCancel_Time_End(endTime);
            orderProdListDTO.setRefund_Sign(-1);
            orderProdListDTO.setACTIVITY_CODE(initLogisticsPackages_tj());
            List<OrderProdListVO> list = logisticsService.queryOrderProdList(orderProdListDTO);

            for (OrderProdListVO item :
                    list) {
                String mobileX = item.getMIDPWD();
                String emCard = item.getEmCard().toLowerCase();
                String key = cacheKey_LotteryCount_common + emCard;
                String actCode = "";

                //1、获取已购买的物流包列表
                List<LotteryLogisticsPackageConfDO> getBuyPackageRedisList = redisService.getList(key, LotteryLogisticsPackageConfDO.class);
                if (getBuyPackageRedisList == null) {
                    //2、无购买记录：忽略
                } else {
                    //3、有购买记录
                    List<LotteryLogisticsPackageConfDO> cancelList = getBuyPackageRedisList;
                    for (LotteryLogisticsPackageConfDO item1 : cancelList) {
                        if (item1.acCode.equals(item.ACTIVITY_CODE)) {
                            actCode = item1.actCode;
                            //4、购买记录为当前退货包：从redis中移除
                            getBuyPackageRedisList.remove(item1);

                            redisService.set(key, getBuyPackageRedisList, 90L, TimeUnit.DAYS);

                            log.info("autoClearUserPackagePCount_tj 退货清空抽奖次数 emCard:" + emCard + " getBuyPackageRedisList:" + JSON.toJSONString(getBuyPackageRedisList));
                        } else {
                            //5、购买记录非当前退货包：忽略
                        }
                    }
                }

            }
        } catch (Exception exp) {
            log.error("autoClearUserPackagePCount_tj:" + exp.getMessage());
        }
    }
}
