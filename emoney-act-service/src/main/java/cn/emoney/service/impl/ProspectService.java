package cn.emoney.service.impl;

import cn.emoney.common.utils.HttpUtils;
import cn.emoney.pojo.bo.ClientIdentifier;
import cn.emoney.pojo.bo.ProspectUserDTO;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisTemplateSupplier;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Pattern;

@Service
public class ProspectService {
    private static final Pattern PHONE_PATTERN = java.util.regex.Pattern.compile("^1[3-9]\\d{9}$");
    private final UserService userService;
    private final ListOperations<String, ProspectUserDTO> prospectOps;

    public ProspectService(UserService userService, RedisTemplateSupplier supplier) {
        this.userService = userService;
        this.prospectOps = supplier.get(ProspectUserDTO.class, "act:prospect:mobile").opsForList();
    }

    public void addMobile(String mobile, String source, boolean trust) {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (attributes instanceof ServletRequestAttributes) {
            addMobile(mobile, source, trust, ((ServletRequestAttributes) attributes).getRequest());
        } else {
            addMobile(mobile, source, trust, (ClientIdentifier) null);
        }
    }

    public void addMobile(String mobile, String source, boolean trust, HttpServletRequest request) {
        ClientIdentifier identifier = new ClientIdentifier();
        identifier.setUserAgent(request.getHeader("User-Agent"));
        identifier.setIpAddress(HttpUtils.streamIpAddr(request::getHeader)
                .findFirst().orElse(request.getRemoteHost()));
        identifier.setIpLocation("unknown");
        addMobile(mobile, source, trust, identifier);
    }

    public void addMobile(String mobile, String source, boolean trust, ClientIdentifier ClientIdentifier) {
        if (trust || PHONE_PATTERN.matcher(mobile).matches()) {
            String pid = userService.GetAccountPID(mobile);
            if (StringUtils.isEmpty(pid) || "*********".equals(pid)) {
                prospectOps.rightPush(mobile, new ProspectUserDTO() {{
                    setMobile(mobile);
                    setSource(source);
                    setTrust(trust);
                    setTime(System.currentTimeMillis());
                    setClientIdentifier(ClientIdentifier);
                }});
            }
        }
    }
}
