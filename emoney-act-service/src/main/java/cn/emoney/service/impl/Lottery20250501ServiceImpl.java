package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.RedissonDistributionLock;
import cn.emoney.mapper.activity.Act588benifitrecordMapper;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.Lottery0808PrizeDO;
import cn.emoney.pojo.LotteryCountDO;
import cn.emoney.pojo.LotteryLogisticsPackageConfDO;
import cn.emoney.pojo.bo.*;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.pojo.vo.OrderProdListVO;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.Lottery20250501Service;
import cn.emoney.service.PointService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-06-11
 */
@Service
@Slf4j
public class Lottery20250501ServiceImpl implements Lottery20250501Service {
    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserService userService;

    @Autowired
    private PointService pointService;

    @Autowired(required = false)
    private Act588benifitrecordMapper act588benifitrecordMapper;

    @Autowired
    private RedissonDistributionLock redissonDistributionLock;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;

    private final static String cacheKey_LotteryCount = RedisConstants.Redis_Pre_Activity + "LotteryCount20250501:";
    private final static String cacheKey_ValidLotteryCount = RedisConstants.Redis_Pre_Activity + "ValidLotteryCount20250501:";
    private final static String cacheKey_LotteryCount_common = RedisConstants.Redis_Pre_Activity + "LotteryCount:Common:";
    private final static String cacheKey_ValidLotteryCount_common = RedisConstants.Redis_Pre_Activity + "ValidLotteryCount:";
    private final static String cacheKey_getMyLotteryList = RedisConstants.Redis_Pre_Activity + "getMyLotteryList20250501:";
    private final static String cacheKey_getAllLotteryList = RedisConstants.Redis_Pre_Activity + "getAllLotteryList20250501:";
    private final static String cacheKey_PrizeSet = RedisConstants.Redis_Pre_Activity + "20250501:PrizeSet:";
    private final String CacheKey_DoLotteryLock = RedisConstants.RedisKey_Pre_Activity + "20250501:DoLotteryLock";
    private final String CacheKey_AllCount = "renewds20250501_count";

    private final String FilterId = "2476";
    /**
     * 设置奖品编号数组长度
     */
    private final static Integer ARRAY_SIZE = 100;
    /**
     * 初始化奖品编号数组
     */
    private static Integer[] prizesArray = new Integer[ARRAY_SIZE];

    static {
        for (int i = 0; i < ARRAY_SIZE; i++) {
            prizesArray[i] = i + 1;
        }
    }

    /**
     * 大师续费抽奖
     *
     * @param actCode
     * @param uid
     * @param pid
     * @param source
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.Lottery0808PrizeDO>
     * <AUTHOR>
     * @date 2024/2/29 13:36
     */
    @Override
    public Result<Lottery0808PrizeDO> doLottery(String actCode, String uid, String pid, String source) {
        String lockKey = CacheKey_DoLotteryLock;

        try {
            if (redissonDistributionLock.tryLock(lockKey, TimeUnit.SECONDS, 10)) {
                LoginUserInfoVO userInfoVO = userService.getBoundUserInfo(uid, pid);
                if (userInfoVO == null || userInfoVO.getMobileX() == null) {
                    return Result.buildErrorResult("-1", "未绑定手机号，无法抽奖");
                }

                //判断是否还有抽奖次数
                LotteryCountDO lotteryCountDO = getLotteryCount(actCode, uid, userInfoVO.getMobileX());
                if (lotteryCountDO == null || lotteryCountDO.pCount < 1) {
                    return Result.buildErrorResult("-1", "抽奖次数已用完");
                }

                //获取物流订单号(赠送特权/使用期/积分时绑定使用，解决退货后赠品一并退)
                String orderID = "";
                String detID = "";

                //从Redis Set集合中随机抽取一个奖品编号
                Integer pNum = getPrizesNum();
                if (pNum == -1) {
                    return Result.buildErrorResult("-1", "抽奖失败，请联系客服");
                }

                //根据奖品编号查询对应的奖品详情
                Lottery0808PrizeDO prizeDO = initLotteryList().stream().filter(x -> x.id.equals(pNum)).findFirst().get();
                if (prizeDO == null) {
                    return Result.buildErrorResult("-1", "找不到奖品，请联系客服");
                }

                //记录中奖明细
                Act588BenifitRecordDO benifitRecordDO = new Act588BenifitRecordDO();
                benifitRecordDO.uid = uid;
                benifitRecordDO.uname = userInfoVO.getMaskMobile();
                benifitRecordDO.actCode = actCode;
                benifitRecordDO.benefitId = pNum;
                benifitRecordDO.benefitName = prizeDO.name;
                benifitRecordDO.source = source;
                benifitRecordDO.writeTime = new Date();
                benifitRecordDO.benefitConfirm = 0;
                int ret = act588benifitrecordMapper.insert(benifitRecordDO);
                if (ret > 0) {
                    //异步发奖品
                    //CompletableFuture.runAsync(() -> {
                    Result<String> result = sendPrize(uid, pid, prizeDO, userInfoVO, orderID, detID);
                    if (result.isSuccess()) {
                        //修改领取状态为已领取
                        act588benifitrecordMapper.updateBenefitConfirm(benifitRecordDO.id);
                        //刷新中奖记录
                        refreshMyLotteryList(actCode, uid);
                        //刷新抽奖次数
                        refreshLotteryCount(actCode, uid, userInfoVO.getMobileX());
                        //本次活动总抽奖次数+1
                        userService.AddUsedCount(CacheKey_AllCount);
                    } else {
                        log.error("小智盈续费发放奖品失败：uid:" + uid + ",剩余抽奖次数：" + lotteryCountDO.pCount + ",prize:" + prizeDO + ",ret:" + ret);
                        return Result.buildErrorResult("-1", "抽奖失败，请联系业务员");
                    }
                    //}, executor);

                } else {
                    return Result.buildErrorResult("-1", "抽奖失败，请联系业务员");
                }
                return Result.buildSuccessResult(prizeDO);

            } else {
                return Result.buildErrorResult("-1", "抽奖失败，请联系业务员");
            }
        } finally {
            redissonDistributionLock.unlock(lockKey);
        }

    }

    /**
     * 发放奖品(奖品类型 1：积分类 2:使用期  3：特权类 )
     *
     * @param uid
     * @param pid
     * @param prizeDO
     * @param userInfoVO
     * @return cn.emoney.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2024/2/29 13:45
     */
    public Result<String> sendPrize(String uid, String pid, Lottery0808PrizeDO prizeDO, LoginUserInfoVO userInfoVO,String orderID,String detID) {
        Result<String> ret = new Result<>();
        switch (prizeDO.type) {
            case 1:
                PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                requestDTO.platform = "1";
                requestDTO.pid = pid;
                requestDTO.uid = uid;
                requestDTO.subId = "";
                requestDTO.taskId = prizeDO.taskCode;
                boolean addRet = pointService.pointRecordAdd(requestDTO);
                if (addRet) {
                    ret.setCode("200");
                    ret.setSuccess(true);
                    ret.setMsg("积分赠送成功");
                } else {
                    ret.setSuccess(false);
                    ret.setMsg("积分赠送失败");
                }
                break;
            case 2:
                //赠送优惠券
                SendCouponRequestDTO req = new SendCouponRequestDTO();
                req.PRESENT_ACCOUNT_TYPE = 2;
                req.PRESENT_ACCOUNT = userInfoVO.getMobileX();
                req.COUPON_ACTIVITY_ID = prizeDO.taskCode;
                req.COUPON_RULE_PRICE = (double)prizeDO.pCount;
                req.PRESENT_PERSON = "";
                LogisticsResult<String> retCoupon = logisticsService.sendCoupon(req);
                if (retCoupon.getCode() == 0) {
                    ret.setCode("200");
                    ret.setSuccess(true);
                    ret.setMsg("优惠券赠送成功");
                } else {
                    ret.setSuccess(false);
                    ret.setMsg("优惠券赠送失败");
                }
                break;
            case 3:
                String upid = pid;
                if (pid == "*********") {
                    upid = "*********";
                }
                if (pid == "*********") {
                    upid = "*********";
                }

                String pacCode = "";
                String[] pacCodeList = prizeDO.taskCode.split("@");
                for (int i = 0; i < pacCodeList.length; i++) {
                    String[] pid_pacCode = pacCodeList[i].split(":");
                    if (upid.equals(pid_pacCode[0])) {
                        pacCode = pid_pacCode[1];
                    }
                }
                ret = sendPrivilegeCommonNew(uid, upid, "大师征文抽奖赠送使用期", pacCode, 1, userInfoVO, orderID, detID);
                break;
            case 4:
                //pdf研报类
                ret.setSuccess(true);
                ret.setCode("0");
                break;
            case 5:
                //手动赠送
                ret.setSuccess(true);
                ret.setCode("0");
            default:
                break;
        }
        return ret;
    }

    /**
     1  5积分	15%
     2  10积分	15%
     3  30积分	15%
     4  50积分	15%
     5  大师使用期5天	10%
     6  全新研报	10%
     7  解套指南	10%
     8  ETF专题报告	10%
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2025/4/23 13:46
     */
    @Override
    public Integer getPrizesNum() {
        Long count = redisService.sSize(cacheKey_PrizeSet);
        if (count <= 0) {
            redisService.setAdd(cacheKey_PrizeSet, prizesArray);
        }
        Integer prizesIndex = (Integer) redisService.sPop(cacheKey_PrizeSet);
        if (prizesIndex >= 1 && prizesIndex <= 15) {
            return 1;
        } else if (prizesIndex >= 16 && prizesIndex <= 30) {
            return 2;
        } else if (prizesIndex >= 31 && prizesIndex <= 45) {
            return 3;
        } else if (prizesIndex >= 46 && prizesIndex <= 60) {
            return 4;
        } else if (prizesIndex >= 61 && prizesIndex <= 70) {
            return 5;
        } else if (prizesIndex >= 71 && prizesIndex <= 80) {
            return 6;
        } else if (prizesIndex >= 81 && prizesIndex <= 90) {
            return 7;
        } else if (prizesIndex >= 91 && prizesIndex <= 100) {
            return 8;
        } else {
            return -1;
        }
    }

    /**
     * 获取可用抽奖次数
     *
     * @param actCode
     * @param uid
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2024/2/29 14:24
     */
    @Override
    public LotteryCountDO   getLotteryCount(String actCode, String uid, String mobileX) {
        String key = cacheKey_ValidLotteryCount  + uid;
        LotteryCountDO count = redisService.get(key,LotteryCountDO.class);
        if (count == null) {
            return refreshLotteryCount(actCode, uid, mobileX);
        }
        return count;
    }
    /**
     * 刷新可用抽奖次数
     *
     * @param actCode
     * @param uid
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2024/2/29 14:25
     */
    @Override
    public LotteryCountDO refreshLotteryCount(String actCode, String uid,String mobileX) {
        String key = cacheKey_ValidLotteryCount + uid;

        LotteryCountDO lotteryCountDO = new LotteryCountDO();
        lotteryCountDO.uid = uid;
        //获取是否在过滤器中
        Map<String, Boolean> matchs = userService.checkUserFilterMatchs(uid, FilterId);
        if (!matchs.get(FilterId)) {
            return null;
        } else {
            lotteryCountDO.pCount = 1;
        }

        List<Act588BenifitRecordDO> list = getMyLotteryList(actCode, uid);
        if (list != null) {
            List<Act588BenifitRecordDO> list1 = list.stream().filter(x -> x.benefitConfirm == 1).collect(Collectors.toList());
            if (list1 != null && list1.size() > 0) {
                lotteryCountDO.pCount = 0;
            }
        }
        redisService.set(key, lotteryCountDO, 200L, TimeUnit.DAYS);
        return lotteryCountDO;
    }

    /**
     * 获取我的中奖记录
     *
     * @param actCode
     * @param uid
     * @return java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>
     * <AUTHOR>
     * @date 2024/2/28 17:19
     */
    @Override
    public List<Act588BenifitRecordDO> getMyLotteryList(String actCode, String uid) {
        //获取我的中奖记录
        String key = cacheKey_getMyLotteryList + actCode + ":" + uid;
        List<Act588BenifitRecordDO> list = redisService.getList(key, Act588BenifitRecordDO.class);
        if (list == null) {
            list = refreshMyLotteryList(actCode, uid);
        }
        Collections.sort(list, Collections.reverseOrder());
        return list;
    }

    /**
     * 刷新我的中奖记录缓存
     *
     * @param actCode
     * @param uid
     * @return java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>
     * <AUTHOR>
     * @date 2024/2/28 17:19
     */
    @Override
    public List<Act588BenifitRecordDO> refreshMyLotteryList(String actCode, String uid) {
        String key = cacheKey_getMyLotteryList + actCode + ":" + uid;

        List<Act588BenifitRecordDO> list = act588benifitrecordMapper.selectByUidAndActCode(uid, actCode);
        if (list != null) {
            List<Act588BenifitRecordDO> filterList = list.stream().filter(x -> x.benefitConfirm == 1).collect(Collectors.toList());
            if (filterList != null) {
                //60天结束后 缓存自动失效
                redisService.set(key, filterList, 60L, TimeUnit.DAYS);
                return filterList;
            }
        }

        return null;
    }

    /**
     *
     * <AUTHOR>
     * @date 2025/4/23 16:11
     * @param actCode
     * @return java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>
     */
    @Override
    public List<Act588BenifitRecordDO> getLotteryList(Integer top, String actCode) {
        String key = cacheKey_getAllLotteryList + top + actCode;

        List<Act588BenifitRecordDO> list = redisService.getList(key, Act588BenifitRecordDO.class);
        if (list == null || list.size() == 0) {
            list = act588benifitrecordMapper.selectByActCode(top, actCode);
            List<Act588BenifitRecordDO> filterList = list.stream().filter(x -> x.benefitConfirm == 1).collect(Collectors.toList());
            if (filterList != null) {
                //5分钟缓存失效
                redisService.set(key, filterList, 2L, TimeUnit.MINUTES);
                return filterList;
            }
        }

        return list;
    }

    /**
     * 获取奖品列表
     *
     * @return java.util.List<cn.emoney.pojo.Lottery0808PrizeDO>
     * <AUTHOR>
     * @date 2024/2/29 13:29
     */
    public List<Lottery0808PrizeDO> initLotteryList() {
        List<Lottery0808PrizeDO> list = new ArrayList<>();
        list.add(new Lottery0808PrizeDO(1, 1, "5积分", "1915275894580641792", "5积分", 5));
        list.add(new Lottery0808PrizeDO(2, 1, "10积分", "1915275963562680320", "10积分", 10));
        list.add(new Lottery0808PrizeDO(3, 1, "30积分", "1915276021903491072", "30积分", 30));
        list.add(new Lottery0808PrizeDO(4, 1, "50积分", "1915276060223496192", "50积分", 50));
        list.add(new Lottery0808PrizeDO(5, 3, "大师使用期5天", "*********:PAC1241021134837292@*********:PAC1241021134657951", "大师使用期5天", 5));
        list.add(new Lottery0808PrizeDO(6, 4, "全新研报", "https://www.emoney.cn/dianjin/bb/quanxinuanbao.pdf", "全新研报", 0));
        list.add(new Lottery0808PrizeDO(7, 4, "解套指南", "https://www.emoney.cn/dianjin/bb/jietaozhinan.pdf","解套指南", 0));
        list.add(new Lottery0808PrizeDO(8, 4, "ETF专题报告", "https://www.emoney.cn/dianjin/bb/etf.pdf","ETF专题报告", 0));
        return list;
    }

    /**
     * 领特权/送使用期  通用接口
     * <AUTHOR>
     * @date 2023/7/27 16:22
     * @param uid
     * @param pid
     * @param reason
     * @param activityID
     * @param accountType
     * @param loginUserInfoVO
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    public Result<String> sendPrivilegeCommonNew(String uid,String pid,String reason,String activityID,Integer accountType,LoginUserInfoVO loginUserInfoVO,String orderID,String detID){
        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(activityID);
        sendPrivilegeDTO.setReason(reason);
        sendPrivilegeDTO.setApplyUserID("scb_public");
        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();

        //2:手机号 1：em号  领取特权指定2  延期指定1
        createActivityGrantApplyAccountDTO.setAccountType(accountType);
        createActivityGrantApplyAccountDTO.setAccount(loginUserInfoVO.getAccount());
        createActivityGrantApplyAccountDTO.setMID(loginUserInfoVO.getMobileX());
        createActivityGrantApplyAccountDTO.setOrderID(orderID);
        createActivityGrantApplyAccountDTO.setOrderDetailID(detID);
        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        Result<String> ret = logisticsService.sendPrivilegeResult(sendPrivilegeDTO);
        return ret;
    }

}
