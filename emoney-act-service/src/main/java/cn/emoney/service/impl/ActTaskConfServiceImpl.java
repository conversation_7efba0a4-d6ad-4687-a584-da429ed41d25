package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.mapper.activity.ActTaskConfMapper;
import cn.emoney.pojo.ActTaskConfDO;
import cn.emoney.service.ActTaskConfService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023-11-23
 */
@Slf4j
@Service
public class ActTaskConfServiceImpl implements ActTaskConfService {

    @Autowired
    private ActTaskConfMapper actTaskConfMapper;
    private final ObjectMapper mapper;
    private final Cache confCache;

    public ActTaskConfServiceImpl(ObjectMapper mapper, CacheManager cacheManager) {
        this.mapper = mapper;
        this.confCache = cacheManager.getCache(RedisConstants.Redis_Pre_Activity + "getTaskConf");
    }


    /**
     * @return java.util.List<cn.emoney.pojo.ActTaskConfDO>
     * <AUTHOR>
     * @date 2023/11/23 14:09
     */
    @Override
    public <T> T getTaskConf(String actCode, Class<T> contentType) {
        ActTaskConfDO confDO = confCache.get(actCode, () -> actTaskConfMapper.selectByActCode(actCode));
        return Optional.ofNullable(confDO)
                .map(entity -> {
                    try {
                        return mapper.readValue(entity.getContext(), contentType);
                    } catch (JsonProcessingException e) {
                        log.error("解析奖励失败: {}", entity.getContext(), e);
                        return null;
                    }
                })
                .orElse(null);
    }
}
