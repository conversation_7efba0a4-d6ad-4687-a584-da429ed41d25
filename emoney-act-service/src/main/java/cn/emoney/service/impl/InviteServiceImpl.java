package cn.emoney.service.impl;

import cn.emoney.mapper.activity.InviteAwardDetailMapper;
import cn.emoney.mapper.activity.InviteInfoMapper;
import cn.emoney.pojo.InviteAwardDetailDO;
import cn.emoney.pojo.InviteInfoDO;
import cn.emoney.service.InviteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-06
 */
@Slf4j
@Service
public class InviteServiceImpl implements InviteService {

    @Autowired
    private InviteInfoMapper inviteInfoMapper;

    @Autowired
    private InviteAwardDetailMapper inviteAwardDetailMapper;

    /**
     * 获取所有未发放完毕的用户
     * <AUTHOR>
     * @date 2021/12/14 13:18
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.InviteInfoDO>
     */
    @Override
    public List<InviteInfoDO> queryAllUnSend() {
        return inviteInfoMapper.getUnSendUser();
    }

    /**
     * 根据被邀请人手机号获取邀请信息
     * <AUTHOR>
     * @date 2021/12/14 13:19
     * @param beInvited
     * @return cn.emoney.activityweb.repository.dao.entity.InviteInfoDO
     */
    @Override
    public InviteInfoDO queryByBeInvited(String beInvited) {
        return inviteInfoMapper.getByBeInvited(beInvited);
    }

    /**
     * 根据邀请人和被邀请人手机号获取赠送信息
     * <AUTHOR>
     * @date 2021/12/14 13:19
     * @param mobileX
     * @param orderMobileX
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.InviteAwardDetailDO>
     */
    @Override
    public List<InviteAwardDetailDO> getByMobileXAndOrderMobile(String mobileX, String orderMobileX) {
        return inviteAwardDetailMapper.getByMobileXAndOrderMobile(mobileX, orderMobileX);
    }

    /**
     * 根据订单号查询赠送记录
     * <AUTHOR>
     * @date 2021/12/22 13:29
     * @param orderId
     * @return cn.emoney.activityweb.repository.dao.entity.InviteAwardDetailDO
     */
    @Override
    public InviteAwardDetailDO getByOrderId(String orderId){
        return inviteAwardDetailMapper.getByOrderId(orderId);
    }
}
