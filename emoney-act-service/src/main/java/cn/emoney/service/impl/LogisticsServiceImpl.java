package cn.emoney.service.impl;

import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.bo.OrderProdListDTO;
import cn.emoney.pojo.bo.QueryCouponListDTO;
import cn.emoney.pojo.bo.SendCouponRequestDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.OrderProdListVO;
import cn.emoney.service.LogisticsService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-03
 */
@Service
@Slf4j
public class LogisticsServiceImpl implements LogisticsService {

    @Value("${queryorderprodlisturl}")
    private String queryOrderProdListUrl;

    @Value("${queryAllCouponListByAccountURL}")
    private String queryAllCouponListByAccountURL;

    @Value("${sendCouponURL}")
    private String sendCouponURL;

    @Value("${sendPrivilegeURL}")
    private String sendPrivilegeURL;

    /**
     * 根据条件集合查询物流订单
     *
     * @param orderProdListDTO
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.vo.OrderProdListVO>
     * <AUTHOR>
     * @date 2021-12-3 15:46
     */
    @Override
    public List<OrderProdListVO> queryOrderProdList(OrderProdListDTO orderProdListDTO) {
        List<OrderProdListVO> orderProdListVOS = new ArrayList<>();
        String res = OkHttpUtil.get(MessageFormat.format(queryOrderProdListUrl, JSON.toJSONString(orderProdListDTO)), null);
        ApiGateWayResult<String> apiGateWayResult = JsonUtil.toBean(res, ApiGateWayResult.class);

        if (apiGateWayResult!=null && !apiGateWayResult.Message.isEmpty()) {
            LogisticsResult<List<OrderProdListVO>> logisticsResult = JsonUtil.toBean(apiGateWayResult.Message, LogisticsResult.class);
            orderProdListVOS = logisticsResult.getData();
        }

        return JsonUtil.toBeanList(JSON.toJSONString(orderProdListVOS), OrderProdListVO.class);
    }

    /**
     * 获取用户所有的
     *
     * @param AccountType 1：em账号 2：手机号
     * @param Account
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.dto.QueryCouponListDTO>
     * <AUTHOR>
     * @date 2021/12/15 13:14
     */
    @Override
    public List<QueryCouponListDTO> queryCouponList(Integer AccountType, String Account) {
        List<QueryCouponListDTO> queryCouponListDTOList = new ArrayList<>();
        String url = MessageFormat.format(queryAllCouponListByAccountURL, AccountType, Account);
        String res = OkHttpUtil.get(url, null);
        ApiGateWayResult<String> apiGateWayResult = JsonUtil.toBean(res, ApiGateWayResult.class);
        if (!apiGateWayResult.Message.isEmpty()) {
            LogisticsResult<List<QueryCouponListDTO>> logisticsResult = JsonUtil.toBean(apiGateWayResult.Message, LogisticsResult.class);
            queryCouponListDTOList = logisticsResult.getData();
        }
        return JsonUtil.toBeanList(JSON.toJSONString(queryCouponListDTOList), QueryCouponListDTO.class);
    }

    /**
     * 赠送优惠券
     *
     * @param requestDTO
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/12/15 13:37
     */
    @Override
    public LogisticsResult<String> sendCoupon(SendCouponRequestDTO requestDTO) {
        String res = OkHttpUtil.postJsonParams(sendCouponURL, JSON.toJSONString(requestDTO));
        ApiGateWayResult<String> apiGateWayResult = JsonUtil.toBean(res, ApiGateWayResult.class);
        log.info("赠送优惠券SendCoupon->request：" + JSON.toJSONString(requestDTO) + " response:" + res);
        if (!apiGateWayResult.Message.isEmpty()) {
            LogisticsResult<String> logisticsResult = JsonUtil.toBean(apiGateWayResult.Message, LogisticsResult.class);
            return logisticsResult;
        }
        return null;
    }

    /**
     * 赠送特权||产品延期
     * <AUTHOR>
     * @date 2021/12/22 10:19
     * @param sendPrivilegeDTO
     * @return java.lang.Boolean
     */
    @Override
    public Boolean sendPrivilege(SendPrivilegeDTO sendPrivilegeDTO) {
        String ret = OkHttpUtil.postJsonParams(sendPrivilegeURL, JSON.toJSONString(sendPrivilegeDTO));
        if (!ret.isEmpty()) {
            ApiGateWayResult<String> apiGateWayResult = JSON.parseObject(ret, ApiGateWayResult.class);
            if (apiGateWayResult != null && apiGateWayResult.getRetCode().equals(0)) {
                LogisticsResult<String> logisticsResultInfo = JsonUtil.toBean(apiGateWayResult.getMessage(), LogisticsResult.class);

                boolean sendret = (logisticsResultInfo != null && logisticsResultInfo.getCode().equals(0)) ? true : false;

                if(!sendret){
                    log.warn("send Privilege failed! {},{}",logisticsResultInfo.getMsg(),sendPrivilegeDTO);
                }

                return sendret;
            }
        }
        return false;
    }

    /**
     * 赠送特权||产品延期
     * <AUTHOR>
     * @date 2021/12/22 10:19
     * @param sendPrivilegeDTO
     * @return java.lang.Boolean
     */
    @Override
    public Result<String> sendPrivilegeResult(SendPrivilegeDTO sendPrivilegeDTO) {
        String ret = OkHttpUtil.postJsonParams(sendPrivilegeURL, JSON.toJSONString(sendPrivilegeDTO));
        if (!ret.isEmpty()) {
            ApiGateWayResult<String> apiGateWayResult = JSON.parseObject(ret, ApiGateWayResult.class);
            if (apiGateWayResult != null && apiGateWayResult.getRetCode().equals(0)) {
                LogisticsResult<String> logisticsResultInfo = JsonUtil.toBean(apiGateWayResult.getMessage(), LogisticsResult.class);

                boolean sendret = (logisticsResultInfo != null && logisticsResultInfo.getCode().equals(0)) ? true : false;

                log.info("send Privilege log! {},{}", logisticsResultInfo, sendPrivilegeDTO);

                if(!sendret){
                    log.warn("send Privilege failed! {},{}",logisticsResultInfo,sendPrivilegeDTO);
                    return Result.buildErrorResult((logisticsResultInfo != null? logisticsResultInfo.getMsg() : "领取失败"));
                }else{
                    return Result.buildSuccessResult("领取成功");
                }

            }
        }
        return Result.buildErrorResult("领取失败");
    }
}
