package cn.emoney.service.impl;

import cn.emoney.common.utils.GenseeUtils;
import cn.emoney.pojo.bo.CourseDetailDTO;
import cn.emoney.pojo.bo.CourseWatchDetailDTO;
import cn.emoney.pojo.bo.UserCourseWatchDTO;
import cn.emoney.service.CourseService;
import cn.emoney.service.CourseWatchService;
import cn.emoney.service.GenseeService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class GenseeCourseWatchServiceImpl implements CourseWatchService {
    private final CourseService courseService;
    private final GenseeService genseeService;

    public GenseeCourseWatchServiceImpl(CourseService courseService, GenseeService genseeService) {
        this.courseService = courseService;
        this.genseeService = genseeService;
    }

    @Override
    public UserCourseWatchDTO getCourseWatchDetail(Integer courseId, Long uid) {
        CourseDetailDTO course = courseService.detail(courseId);
        if (course != null) {
            List<CourseWatchDetailDTO> lives = GenseeUtils.findWebCastId(course.getCourseLiveUrl())
                    .map(vid -> genseeService.getLiveWatchByUidAndVid(uid, vid))
                    .orElse(Collections.emptyList());
            List<CourseWatchDetailDTO> replays = GenseeUtils.findWebCastId(course.getCoursePlayBackUrl())
                    .map(vid -> genseeService.getRecordedWatchByUidAndVid(uid, vid))
                    .orElse(Collections.emptyList());
            UserCourseWatchDTO dto = new UserCourseWatchDTO();
            dto.setLive(lives);
            dto.setReplay(replays);
            return dto;
        }
        return null;
    }
}
