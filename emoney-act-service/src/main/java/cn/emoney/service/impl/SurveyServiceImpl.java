package cn.emoney.service.impl;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.PointResult;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.mapper.activity.WjxNoticeSceneMapper;
import cn.emoney.mapper.activity.WjxSurveyResultMapper;
import cn.emoney.pojo.SurveyTagDTO;
import cn.emoney.pojo.WjxSurveyNoticeDO;
import cn.emoney.pojo.WjxSurveyResultDO;
import cn.emoney.pojo.vo.survey.ExtractTagVO;
import cn.emoney.pojo.vo.survey.WjxSurveyReceiptVO;
import cn.emoney.service.SurveyService;
import cn.emoney.service.kafka.producer.ProducerService;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/01/29 10:22
 **/
@Service
@Slf4j
public class SurveyServiceImpl implements SurveyService {

    @Autowired
    private ProducerService producerService;

    @Autowired
    private WjxSurveyResultMapper wjxSurveyResultMapper;

    @Autowired
    private WjxNoticeSceneMapper wjxNoticeSceneMapper;

    @Autowired
    private RedisService redisService;


    @Override
    public WjxSurveyReceiptVO pushUserSurvey(JSONObject userSurvey) {

        try {
            producerService.sendMessage("newSurveyComing", userSurvey.toJSONString());
            return null;
        } catch (Exception e) {
            log.error("处理问卷星问卷异常:", e);
        }

        return new WjxSurveyReceiptVO();
    }


    /***
     * 功能描述:
     * 处理问卷推送数据
     * @Param: [userSurvey]
     * @Return: java.lang.Integer
     * @Author: tengdengming
     * @Date: 2022/3/16 14:25
     */

    @Override
    public Integer dealUserSurvey(WjxSurveyResultDO userSurvey) {
        try {

            if (!checkDigest(userSurvey)) {
                log.info("dealUserSurvey found illegal data", userSurvey);
                return -1;
            }

            //防止问卷名字超过30个字写入溢出
            if (userSurvey.getName().length()>30) {
                userSurvey.setName(userSurvey.getName().substring(0, 30));
            }

            Integer result = wjxSurveyResultMapper.insert(userSurvey);

            if (result > 0) {
                log.info("SurveyService dealUserSurvey() insert db success. JoinId={}", userSurvey.getJoinid());
                userSurvey.setId(result);
                userSurvey.setReceivetime(DateTime.now());

                String jsonString = JSONObject.toJSONString(userSurvey);
                producerService.sendMessage("surveyNotice", jsonString);

                return result;
            } else {
                return 0;
            }

        } catch (Exception e) {
            log.error(this.getClass().getName() + " method:" + Thread.currentThread().getStackTrace()[1].getMethodName() + " 写入问卷数据到数据库失败", e);
        }
        return null;
    }

    @Override
    public List<WjxSurveyResultDO> getUserSurveyListByPage(Long uid, Integer pageSize, Integer pageIndex) {
        try {
            return wjxSurveyResultMapper.getUserSurveyList(uid, pageSize, pageSize * pageIndex);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " method:" + Thread.currentThread().getStackTrace()[1].getMethodName() + " 获取用户的问卷列表失败", e);
        }
        return null;
    }

    @Override
    public WjxSurveyResultDO getSurvey(String joinId) {

        try {
            return wjxSurveyResultMapper.getSurvey(joinId);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " method:" + Thread.currentThread().getStackTrace()[1].getMethodName() + " 根据JoinId获取答卷失败", e);
        }
        return null;
    }


    @Override
    public List<WjxSurveyResultDO> getActivitySurveyList(Long activity, Integer pageSize, Integer pageIndex) {

        try {
            return wjxSurveyResultMapper.getActivitySurveyList(activity, pageSize, pageSize * pageIndex);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " method:" + Thread.currentThread().getStackTrace()[1].getMethodName() + " 获取用户的问卷列表失败", e);
        }
        return null;
    }

    @Override
    public List<WjxSurveyResultDO> getUserSurveyListByUnionId(String unionId, Integer pageSize, Integer pageIndex) {
        try {
            return wjxSurveyResultMapper.getUserSurveyListByUnionId(unionId, pageSize, pageSize * pageIndex);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " method:" + Thread.currentThread().getStackTrace()[1].getMethodName() + " 获取用户的问卷列表失败", e);
        }
        return null;
    }

    @Override
    public List<WjxSurveyResultDO> getUserSurveyListByEmName(String emNo, Integer pageSize, Integer pageIndex) {
        try {
            return wjxSurveyResultMapper.getUserSurveyListByEmName(emNo, pageSize, pageSize * pageIndex);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " method:" + Thread.currentThread().getStackTrace()[1].getMethodName() + " 获取用户的问卷列表失败", e);
        }
        return null;
    }

    @Override
    public WjxSurveyResultDO getUserSurveyResultByActivityID(Long uid, Long activityID) {
        try {
            return wjxSurveyResultMapper.getUserSurveyResultByActivityID(uid, activityID);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " method:" + Thread.currentThread().getStackTrace()[1].getMethodName() + "根据问卷ID获取用户答卷数据失败", e);
        }
        return null;
    }

    @Override
    public List<Long> getUserFinishedSurvey(Long uid, List<Long> activityList) {
        try {
            return wjxSurveyResultMapper.getUserFinishedSurvey(uid, activityList);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " method:" + Thread.currentThread().getStackTrace()[1].getMethodName() + "查询用户做过的问卷", e);
        }
        return null;
    }

    @Override
    public List<WjxSurveyResultDO> getUserLatestSurvey(Long uid, Integer lastCounts) {

        try {
            if (lastCounts > MAX_LATEST_COUNT) {
                lastCounts = MAX_LATEST_COUNT;
            }
            return wjxSurveyResultMapper.getUserLatestSurvey(uid, lastCounts);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " method:" + Thread.currentThread().getStackTrace()[1].getMethodName() + " 获取用户最近几条答卷失败", e);
        }

        return null;
    }

    @Override
    public Boolean checkDigest(WjxSurveyResultDO userSurvey) {

        //检测SHA1信息摘要 , 签名规则：sign=sha1(activity+index+推送密钥)
        String digest = DigestUtils.sha1Hex(userSurvey.getActivity().toString() + userSurvey.getIndex().toString() + "7ba007cb-7068-4040-8e3d-42ce8cc0c3d7");
        if (digest.equals(userSurvey.getSign())) {
            return true;
        } else {
            return false;
        }
    }


    /***
     * 功能描述:
     * 通知应用场景接口，新的消息到了。
     * @Param: [userSurvey]
     * @Return: java.lang.Integer
     * @Author: tengdengming
     * @Date: 2022/3/14 10:12
     */
    @Override
    public String noticeScene(WjxSurveyResultDO userSurvey) {

        String pushUrl = getScenePushUrl(userSurvey.getEmscene());

        Map<String, Object> postVal = new HashMap<>();
        postVal.put("activity", userSurvey.getActivity().toString());
        postVal.put("joinId", userSurvey.getJoinid());
        JSONObject json = new JSONObject(postVal);

        try {
            //System.out.println("***Notice Url :pushUrl=" + pushUrl);

            //String postBack = OkHttpUtil.postJsonParams(pushUrl,json.toJSONString());

            String content = JSON.toJSONString(userSurvey);
            String postBack = OkHttpUtil.postJsonParams(pushUrl, content);

            log.info("noticeScene() 通知场景成功，请求{} 数据{} 返回{}", pushUrl, content,postBack);

            //System.out.println("***noticeScene() 通知场景成功， 返回=" + postBack);
            return postBack;

        } catch (Exception e) {
            log.error("push surveyNotice failed", e, userSurvey);
        }

        return "0";

    }


    /***
     * 功能描述:
     * 获取业务场景推送Url
     * @Param: [scene]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2022/3/14 13:22
     */

    @Override
    public String getScenePushUrl(String scene) {
        //Map<String,String> subScene = new HashMap<>();
        //subScene.put("QiWei","http://static.emoney.cn/");


        List<WjxSurveyNoticeDO> scenes = wjxNoticeSceneMapper.getNoticeSceneList();

        for (WjxSurveyNoticeDO item : scenes) {
            if (item.getScene().equals(scene)) {
                return item.getPushurl();
            }
        }

        /*for(Map.Entry<String,String> entry:subScene.entrySet()){
            if (entry.getKey()==scene){
               return entry.getValue();
            }
        }*/

        return "http://act.emoney.cn/api/survey/sceneWait";
    }

    @Value("${getSurveyTemplateUrl}")
    private String getSurveyTemplateURL;

    private Integer expirdTime = 60 * 60 * 1;

    @Override
    public String getWJXActivitytemplate(Long activityId) {

        try {
            String key = RedisConstants.Redis_Pre_Activity + ":survey:template:activity_" + activityId.toString();

            String val = redisService.get(key, String.class);

            if (val == null || "".equals(val)) {
                val = OkHttpUtil.get(MessageFormat.format(getSurveyTemplateURL, activityId.toString()), null);
                redisService.set(key, val, expirdTime.longValue());
            }

            return val;

        } catch (Exception e) {
            log.error("get WJX activity template failed", e);
        }

        return null;

    }

    // 保证页面展示时有序
    private static final Map<String, Map<String, String>> TAGS_MAP_239811538 = new LinkedHashMap<>();
    private static final Map<String, Map<String, String>> TAGS_MAP_258993788 = new LinkedHashMap<>();

    private static final Map<String, String> TAGS_TITLE_MAP_239811538 = new HashMap<>();

    private static final Map<String, String> TAGS_TITLE_MAP_258993788 = new HashMap<>();

    static {

        Map<String, String> q2 = new HashMap<>();
        Map<String, String> q4 = new HashMap<>();
        Map<String, String> q14 = new HashMap<>();
        Map<String, String> q15 = new HashMap<>();
        Map<String, String> q17 = new HashMap<>();
        Map<String, String> q12 = new HashMap<>();
        Map<String, String> q13 = new HashMap<>();
        Map<String, String> q9 = new HashMap<>();
        Map<String, String> q10 = new HashMap<>();
        Map<String, String> q11 = new HashMap<>();
        Map<String, String> q16 = new HashMap<>();

        q2.put("1", "否");
        q2.put("2", "是");
        q2.put("3", "是");
        q2.put("4", "是");
        q2.put("5", "是");
        q2.put("6", "否");
        q2.put("7", "否");

        q4.put("1", "每天1小时");
        q4.put("2", "每周3小时");
        q4.put("3", "每月5小时");
        q4.put("4", "偶尔学习");

        q14.put("1", "基本面");
        q14.put("2", "技术面");
        q14.put("3", "消息面");
        q14.put("4", "综合面");
        q14.put("5", "感觉型");

        q15.put("1", "超短线");
        q15.put("2", "短线");
        q15.put("3", "中线");
        q15.put("4", "长线");

        q17.put("1", "不易");
        q17.put("2", "轻度");
        q17.put("3", "中度");
        q17.put("4", "中高度");
        q17.put("5", "高度");

        q12.put("1", "考虑竞品");
        q12.put("2", "强");
        q12.put("3", "较弱");
        q12.put("4", "无");

        q13.put("1", "5千");
        q13.put("2", "5千到1万");
        q13.put("3", "1万以上");
        q13.put("4", "产品好价格其次");

        q9.put("1", "无需联系");
        q9.put("2", "会主动来电");
        q9.put("3", "1次/周");
        q9.put("4", "1次/月");
        q9.put("5", "1次/季");

        q10.put("1", "电话");
        q10.put("2", "微信");
        q10.put("3", "盟盟");
        q10.put("4", "在线客服");
        q10.put("5", "会主动来电");

        q11.put("1", "会推荐");
        q11.put("2", "不会推荐");
        q11.put("3", "可能推荐");

        q16.put("1", "低风险低回报");
        q16.put("2", "中低风险中低回报");
        q16.put("3", "中等风险中等回报");
        q16.put("4", "中高风险中高回报");
        q16.put("5", "高风险高回报");


        TAGS_MAP_239811538.put("q2", q2); // 这个要特殊处理
        TAGS_MAP_239811538.put("q17", q17);
        TAGS_MAP_239811538.put("q12", q12);
        TAGS_MAP_239811538.put("q13", q13);
        TAGS_MAP_239811538.put("q16", q16);
        TAGS_MAP_239811538.put("q14", q14); // q14 q15要合并
        TAGS_MAP_239811538.put("q15", q15); // q14 q15要合并
        TAGS_MAP_239811538.put("q4", q4);
        TAGS_MAP_239811538.put("q9", q9);
        TAGS_MAP_239811538.put("q10", q10);
        TAGS_MAP_239811538.put("q11", q11);

        TAGS_TITLE_MAP_239811538.put("q2", "是否需指导");
        TAGS_TITLE_MAP_239811538.put("q17", "退费倾向");
        TAGS_TITLE_MAP_239811538.put("q12", "升级意愿");
        TAGS_TITLE_MAP_239811538.put("q13", "升级价格预期");
        TAGS_TITLE_MAP_239811538.put("q16", "盈亏预期");
        TAGS_TITLE_MAP_239811538.put("q14", "方法偏好");
        TAGS_TITLE_MAP_239811538.put("q15", "操作偏好");
        TAGS_TITLE_MAP_239811538.put("q4", "听课频次");
        TAGS_TITLE_MAP_239811538.put("q9", "服务频次");
        TAGS_TITLE_MAP_239811538.put("q10", "服务方式");
        TAGS_TITLE_MAP_239811538.put("q11", "是否会介绍朋友");

        // 第二个问卷 258993788

        TAGS_TITLE_MAP_258993788.put("q1", "今年收益");
        TAGS_TITLE_MAP_258993788.put("q2", "亏钱原因");
        TAGS_TITLE_MAP_258993788.put("q6", "是否有续费意愿");
        TAGS_TITLE_MAP_258993788.put("q8", "不愿续费原因");
        TAGS_TITLE_MAP_258993788.put("q10", "优惠政策");
        TAGS_TITLE_MAP_258993788.put("q11", "是否会推荐朋友");

        Map<String, String> q1_258993788 = new HashMap<>();
        Map<String, String> q2_258993788 = new HashMap<>();
        Map<String, String> q6_258993788 = new HashMap<>();
        Map<String, String> q8_258993788 = new HashMap<>();
        Map<String, String> q10_258993788 = new HashMap<>();
        Map<String, String> q11_258993788 = new HashMap<>();

        TAGS_MAP_258993788.put("q1", q1_258993788);
        TAGS_MAP_258993788.put("q2", q2_258993788);
        TAGS_MAP_258993788.put("q6", q6_258993788);
        TAGS_MAP_258993788.put("q8", q8_258993788);
        TAGS_MAP_258993788.put("q10", q10_258993788);
        TAGS_MAP_258993788.put("q11", q11_258993788);

        q1_258993788.put("1", "赚10%以上");
        q1_258993788.put("2", "小赚0-5%");
        q1_258993788.put("3", "亏-10%以内");
        q1_258993788.put("4", "亏-10%以上");
        q1_258993788.put("5", "踏空了");

        q2_258993788.put("1", "没按操盘线操作");
        q2_258993788.put("2", "选股方法用错");
        q2_258993788.put("3", "没有持续听课");
        q2_258993788.put("4", "其他");

        q6_258993788.put("1", "有");
        q6_258993788.put("2", "考虑投顾产品");
        q6_258993788.put("3", "考虑天玑产品");
        q6_258993788.put("4", "无");

        q8_258993788.put("1", "价格过高");
        q8_258993788.put("2", "功能不满足需求");
        q8_258993788.put("3", "课程不吸引");
        q8_258993788.put("4", "服务不满意");
        q8_258993788.put("5", "其他");

        q10_258993788.put("1", "有吸引力");
        q10_258993788.put("2", "不太关注");
        q10_258993788.put("3", "其他");

        q11_258993788.put("1", "不会推荐");
        q11_258993788.put("2", "不会推荐");
        q11_258993788.put("3", "不会推荐");
        q11_258993788.put("4", "不确定");
        q11_258993788.put("5", "不确定");
        q11_258993788.put("6", "会推荐");
        q11_258993788.put("7", "会推荐");
        q11_258993788.put("8", "会推荐");
        q11_258993788.put("9", "会推荐");
        q11_258993788.put("10", "会推荐");



    }

    private static final String DEFAULT_DETAIL_STR = "无";

    private static final String DEFAULT_EMPTY_STR = "--";

    private static final String ANSWER_SPLITTER = ",";

    // 以下5个String 均为需要特殊处理的值

    private static final String Q2_STR = "q2";

    private static final String Q14_STR = "q14";

    private static final String Q15_STR = "q15";

    private static final String YES_STR = "是";

    private static final String Q14_15_TITLE = "投资风格";

    private static final String SURVEY_DETAIL_URL = "https://act.emoney.cn/api/survey/getUserResultByActivityID?uid=%s&activityId=%s";

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Override
    public SurveyTagDTO tagsExtract(String userId, String surveyId) {
        String url = String.format(SURVEY_DETAIL_URL, userId, surveyId);
        String resString;
        try {
            resString = OkHttpUtil.get(url, null);
        } catch (Exception e) {
            log.error("问卷详情请求失败 >>> " + url, e);
            return null;
        }
        PointResult<WjxSurveyResultDO> res = JSON.parseObject(resString, new TypeReference<PointResult<WjxSurveyResultDO>>() {
        });
        WjxSurveyResultDO result = res.getData();
        if (result == null) {
            return null;
        }
        SurveyTagDTO dto = new SurveyTagDTO();
        dto.setSubmitTime(sdf.format(result.getSubmittime()));
        List<ExtractTagVO> tags = null;
        if (Objects.equals(surveyId, "239811538")) {
            tags = parseTags_239811538(result.getAnswer());
        } else if (Objects.equals(surveyId, "258993788")){
            tags = parseTags_258993788(result.getAnswer());
        }
        dto.setTags(tags);
        return dto;
    }

    private static List<String> parseAnswerStr(String answerStr) {
        List<String> list = new ArrayList<>();
        // 先以逗号进行切分
        String[] strings = answerStr.split(ANSWER_SPLITTER);
        for (String s : strings) {
            String answer = s;
            if (s.contains("^")) {
                String[] split = s.split("\\^");
                answer = split[0];
            }
            list.add(answer);
        }
        return list;
    }

    private static List<ExtractTagVO> parseTags_258993788(String answerJson) {
        List<ExtractTagVO> list = new ArrayList<>();
        Map<String, String> answer = JSON.parseObject(answerJson, new TypeReference<HashMap<String, String>>() {
        });
        for (Map.Entry<String, Map<String, String>> entry : TAGS_MAP_258993788.entrySet()) {
            String questionNoStr = entry.getKey();
            Map<String, String> tagMap = entry.getValue();

            String answerStr = answer.get(questionNoStr);
            ExtractTagVO vo = new ExtractTagVO();
            vo.setTitle(TAGS_TITLE_MAP_258993788.get(questionNoStr));
            if (answerStr != null && !answerStr.isEmpty()) {
                List<String> selected = parseAnswerStr(answerStr);
                StringJoiner joiner = new StringJoiner(",");
                for (String s : selected) {
                    String string = tagMap.get(s);
                    if (string != null) {
                        joiner.add(string);
                    }
                }
                if (joiner.length() == 0) {
                    joiner.add(DEFAULT_EMPTY_STR);
                }
                vo.setDetail(joiner.toString());
            }
            list.add(vo);
        }
        return list;
    }

    private List<ExtractTagVO> parseTags_239811538(String answerJson) {
        List<ExtractTagVO> list = new ArrayList<>();
        List<ExtractTagVO> q14_15List = new ArrayList<>();

        Map<String, String> answer = JSON.parseObject(answerJson, new TypeReference<HashMap<String, String>>() {
        });
        for (Map.Entry<String, Map<String, String>> entry : TAGS_MAP_239811538.entrySet()) {
            String questionNoStr = entry.getKey();
            Map<String, String> tagMap = entry.getValue();

            String answerStr = answer.get(questionNoStr);
            ExtractTagVO vo = new ExtractTagVO();
            vo.setTitle(TAGS_TITLE_MAP_239811538.get(questionNoStr));
            if (answerStr != null && !answerStr.isEmpty()) {
                String[] selected = answerStr.split(ANSWER_SPLITTER);
                String tagDetail = null;
                // 按照业务需求对q2做特殊处理
                for (String s : selected) {
                    tagDetail = tagMap.get(s);
                    if (Q2_STR.equals(questionNoStr) && YES_STR.equals(tagDetail)) {
                        break;
                    }
                }
                vo.setDetail(tagDetail);
            }
            if (vo.getDetail() == null) {
                vo.setDetail(DEFAULT_DETAIL_STR);
            }
            // 按照业务需求对q14 q15做特殊处理
            if (Q14_STR.equals(questionNoStr) || Q15_STR.equals(questionNoStr)) {
                q14_15List.add(vo);
                // 位置固定
                if (q14_15List.size() == 2) {
                    list.add(new ExtractTagVO().setTitle(Q14_15_TITLE).setChildren(q14_15List));
                }
            } else {
                list.add(vo);
            }
        }
        return list;
    }

}
