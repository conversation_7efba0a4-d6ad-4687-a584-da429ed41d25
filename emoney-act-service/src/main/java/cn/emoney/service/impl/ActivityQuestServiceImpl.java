package cn.emoney.service.impl;

import cn.emoney.act.dao.ActivityQuestAcceptRepository;
import cn.emoney.act.dao.ActivityQuestRepository;
import cn.emoney.act.entity.ActivityQuestAcceptEntity;
import cn.emoney.act.entity.ActivityQuestEntity;
import cn.emoney.pojo.bo.ActivityQuestAcceptDTO;
import cn.emoney.pojo.bo.ActivityQuestDTO;
import cn.emoney.service.ActivityQuestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

@Slf4j
@Service
public class ActivityQuestServiceImpl implements ActivityQuestService {
    private final ActivityQuestRepository repository;
    private final ActivityQuestAcceptRepository acceptRepository;

    public ActivityQuestServiceImpl(ActivityQuestRepository repository, ActivityQuestAcceptRepository acceptRepository) {
        this.repository = repository;
        this.acceptRepository = acceptRepository;
    }

    @Override
    @Cacheable(value = "act:activity-quest", key = "#questId")
    public Optional<ActivityQuestDTO> findQuest(Long questId) {
        return repository.findById(questId).map(this::convert);
    }

    @Override
    public Optional<ActivityQuestAcceptDTO> findById(Long acceptId) {
        return acceptRepository.findById(acceptId).map(this::convert);
    }

    @Override
    @Cacheable(value = "act:activity-quest-user", key = "#questId + '@' + #uid", unless = "#result == null")
    public Optional<ActivityQuestAcceptDTO> findActive(Long questId, Long uid) {
        return acceptRepository.findTopByUidAndQuestIdOrderByCreateTimeDesc(uid, questId)
                .filter(q -> q.getStatus() >= 0)
                .map(this::convert);
    }

    @Override
    @Cacheable(value = "act:activity-quest-user", key = "#questId + '@' + #uid")
    @Transactional(rollbackFor = Exception.class)
    public ActivityQuestAcceptDTO findOrAccept(Long questId, Long uid,
                                               Consumer<ActivityQuestDTO> beforeAccept,
                                               BiConsumer<ActivityQuestAcceptDTO, ActivityQuestAcceptDTO> afterAccept) {
        ActivityQuestAcceptDTO accepted = acceptRepository.findTopByUidAndQuestIdOrderByCreateTimeDesc(uid, questId)
                .map(this::convert).orElse(null);
        if (accepted != null && accepted.getStatus() >= 0) {
            return accepted;
        }
        if (beforeAccept != null) {
            beforeAccept.accept(findQuest(questId).orElseThrow(() -> new IllegalArgumentException("quest not found: " + questId)));
        }
        ActivityQuestAcceptDTO curr = accept(questId, uid);
        if (afterAccept != null) {
            afterAccept.accept(curr, accepted);
        }
        return curr;
    }

    @Override
    @CachePut(value = "act:activity-quest-user", key = "#questId + '@' + #uid")
    public ActivityQuestAcceptDTO accept(Long questId, Long uid) {
        ActivityQuestAcceptEntity quest = new ActivityQuestAcceptEntity();
        quest.setUid(uid);
        quest.setQuestId(questId);
        acceptRepository.save(quest);
        return convert(quest);
    }

    @Override
    @CacheEvict(value = "act:activity-quest-user", key = "#questId + '@' + #uid")
    @Transactional(rollbackFor = Exception.class, timeout = 5)
    public boolean complete(Long questId, Long uid) {
        return acceptRepository.finishQuest(uid, questId) > 0;
    }

    @Override
    @CacheEvict(value = "act:activity-quest-user", key = "#questId + '@' + #uid")
    @Transactional(rollbackFor = Exception.class, timeout = 5)
    public boolean cancel(Long questId, Long uid) {
        return acceptRepository.cancelQuest(uid, questId) > 0;
    }

    @Override
    public Page<ActivityQuestAcceptDTO> pageAcceptByExample(ActivityQuestAcceptDTO example, Pageable pageable) {
        return acceptRepository.findAll(Example.of(convert(example)), pageable)
                .map(this::convert);
    }

    private ActivityQuestDTO convert(ActivityQuestEntity entity) {
        ActivityQuestDTO dto = new ActivityQuestDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setType(entity.getType());
        dto.setContent(entity.getContent());
        dto.setStatus(entity.getStatus());
        dto.setCreateTime(entity.getCreateTime());
        dto.setStartTime(entity.getStartTime());
        dto.setEndTime(entity.getEndTime());
        return dto;
    }

    private ActivityQuestAcceptDTO convert(ActivityQuestAcceptEntity entity) {
        ActivityQuestAcceptDTO dto = new ActivityQuestAcceptDTO();
        dto.setId(entity.getId());
        dto.setUid(entity.getUid());
        dto.setQuestId(entity.getQuestId());
        dto.setStatus(entity.getStatus());
        ZoneId zoneId = ZoneId.systemDefault();
        Optional.ofNullable(entity.getCreateTime())
                .filter(s -> s.isAfter(Instant.EPOCH))
                .map(t -> ZonedDateTime.ofInstant(t, zoneId))
                .ifPresent(dto::setCreateTime);
        Optional.ofNullable(entity.getFinishTime())
                .filter(s -> s.isAfter(Instant.EPOCH))
                .map(t -> ZonedDateTime.ofInstant(t, zoneId))
                .ifPresent(dto::setFinishTime);
        Optional.ofNullable(entity.getCancelTime())
                .filter(s -> s.isAfter(Instant.EPOCH))
                .map(t -> ZonedDateTime.ofInstant(t, zoneId))
                .ifPresent(dto::setCancelTime);
        return dto;
    }

    private ActivityQuestAcceptEntity convert(ActivityQuestAcceptDTO dto) {
        ActivityQuestAcceptEntity entity = new ActivityQuestAcceptEntity();
        entity.setId(dto.getId());
        entity.setUid(dto.getUid());
        entity.setQuestId(dto.getQuestId());
        entity.setStatus(dto.getStatus());
        ZoneId zoneId = ZoneId.systemDefault();
        Optional.ofNullable(dto.getCreateTime())
                .map(t -> t.toInstant().atZone(zoneId).toInstant())
                .ifPresent(entity::setCreateTime);
        Optional.ofNullable(dto.getFinishTime())
                .map(t -> t.toInstant().atZone(zoneId).toInstant())
                .ifPresent(entity::setFinishTime);
        Optional.ofNullable(dto.getCancelTime())
                .map(t -> t.toInstant().atZone(zoneId).toInstant())
                .ifPresent(entity::setCancelTime);
        return entity;
    }
}
