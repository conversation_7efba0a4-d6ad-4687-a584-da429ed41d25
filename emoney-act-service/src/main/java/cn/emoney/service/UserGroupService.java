package cn.emoney.service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public interface UserGroupService {
    /**
     * 检查用户是否在指定的用户画像组
     *
     * @return key:用户画像组id，value: checkResult
     */
    Map<Integer, Boolean> checkGroupsWithResult(Long uid, List<Integer> groups);

    default Set<Integer> checkGroups(Long uid, List<Integer> groups) {
        return checkGroupsWithResult(uid, groups).entrySet().stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
    }

    default Set<Integer> checkGroups(Long uid, Integer... groups) {
        return checkGroups(uid, Arrays.asList(groups));
    }

    default boolean allMatch(Long uid, List<Integer> groups) {
        return checkGroupsWithResult(uid, groups).values().stream().allMatch(Boolean::booleanValue);
    }

    default boolean allMatch(Long uid, Integer... groups) {
        return allMatch(uid, Arrays.asList(groups));
    }

    default boolean anyMatch(Long uid, List<Integer> groups) {
        return checkGroupsWithResult(uid, groups).values().stream().anyMatch(Boolean::booleanValue);
    }

    default boolean anyMatch(Long uid, Integer... groups) {
        return anyMatch(uid, Arrays.asList(groups));
    }

    Set<Long> getGroupUsers(Integer group);

    /**
     * 预加载用户画像组用户，适用于批量操作，预计消耗 10s 以上, 单次请求约 15ms
     *
     * @param group
     */
    void preloadGroupUsers(Integer group);
}
