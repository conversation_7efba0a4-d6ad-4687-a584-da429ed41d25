package cn.emoney.service;

import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.Result;
import cn.emoney.pojo.bo.OrderProdListDTO;
import cn.emoney.pojo.bo.QueryCouponListDTO;
import cn.emoney.pojo.bo.SendCouponRequestDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.OrderProdListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-03
 */
public interface LogisticsService {
    /**
     * 根据条件集合查询物流订单
     *
     * @param orderProdListDTO
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.vo.OrderProdListVO>
     * <AUTHOR>
     * @date 2021-12-3 17:09
     */
    List<OrderProdListVO> queryOrderProdList(OrderProdListDTO orderProdListDTO);

    /**
     * 查询账号下所有的优惠券
     *
     * @param AccountType
     * @param Account
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.dto.QueryCouponListDTO>
     * <AUTHOR>
     * @date 2021/12/15 13:45
     */
    List<QueryCouponListDTO> queryCouponList(Integer AccountType, String Account);

    /**
     * 赠送优惠券
     *
     * @param requestDTO
     * @return cn.emoney.activitycommon.result.LogisticsResult<java.lang.String>
     * <AUTHOR>
     * @date 2021/12/15 13:46
     */
    LogisticsResult<String> sendCoupon(SendCouponRequestDTO requestDTO);

    /**
     * 赠送特权||产品延期
     * <AUTHOR>
     * @date 2021/12/22 10:19
     * @param sendPrivilegeDTO
     * @return java.lang.Boolean
     */
    Boolean sendPrivilege(SendPrivilegeDTO sendPrivilegeDTO);

    /**
     * 赠送特权||产品延期 带消息返回
     * <AUTHOR>
     * @date 2021/12/22 10:19
     * @param sendPrivilegeDTO
     * @return java.lang.Boolean
     */
    Result<String> sendPrivilegeResult(SendPrivilegeDTO sendPrivilegeDTO) ;
}
