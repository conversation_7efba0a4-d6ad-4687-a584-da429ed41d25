package cn.emoney.service;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.Lottery0808PrizeDO;
import cn.emoney.pojo.QSKHInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-04-10
 */
public interface QSKHService {
    List<Act588BenifitRecordDO> getMyLotteryList(String actCode, String uid);
    List<Act588BenifitRecordDO> refreshMyLotteryList(String actCode,String uid);
    String queryOpenTimeQS(String mobile);
    Result<Lottery0808PrizeDO> doLottery(String actCode, String uid, String pid, String source);
    Integer getPrizesNum();
    QSKHInfoDO.Status getUserStatus(String uid, String pid, String mobile);
}
