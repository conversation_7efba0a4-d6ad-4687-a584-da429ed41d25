package cn.emoney.service;

import cn.emoney.act.exception.PointException;
import cn.emoney.common.result.PointResult;
import cn.emoney.pojo.bo.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-12-15
 */
public interface PointService {
    /**
     * 加积分
     * <AUTHOR>
     * @date 2021/12/16 13:41
     * @param requestDTO
     * @return java.lang.Boolean
     */
    @Deprecated
    Boolean pointRecordAdd(PointRecordAddRequestDTO requestDTO);

    /**
     * 加积分
     * @param uid 用户id
     * @param taskId 任务id
     * @return 积分添加结果
     * @throws PointException 积分异常, isConflict: 历史有添加过
     */
    PointAddResult addPointByTaskId(Long uid, Long taskId) throws PointException;
    /**
     * 积分查询
     * <AUTHOR>
     * @date 2021/12/16 13:41
     * @param uid
     * @return java.util.List<cn.emoney.activityweb.repository.dao.entity.dto.PointQueryDataDTO>
     */
    List<PointQueryDataDTO> pointQuerySummary(String uid);

    /**
     * 积分订单创建
     * <AUTHOR>
     * @date 2021/12/16 13:42
     * @param requestDTO
     * @return cn.emoney.activityweb.repository.dao.entity.dto.PointOrderAddDataDTO
     */
    PointResult<PointOrderAddDataDTO> pointOrderAdd(PointOrderAddRequestDTO requestDTO);

    /**
     * 积分兑换支付回调
     * <AUTHOR>
     * @date 2021/12/16 13:42
     * @param requestDTO
     * @return cn.emoney.activityweb.repository.dao.entity.dto.PointOrderAddDataDTO
     */
    PointResult<PointOrderAddDataDTO> pointOrderExchange(PointOrderExchangeRequestDTO requestDTO);

    /**
     * 根据积分任务id查询任务状态
     * <AUTHOR>
     * @date 2022/1/25 11:12
     * @param requestDTO
     * @return java.util.List<cn.emoney.pojo.bo.PointQueryByTaskIDDataDTO>
     */
    @Deprecated
    List<PointQueryByTaskIDDataDTO> pointQueryByTaskID(PointQueryByTaskIDRequestDTO requestDTO);

    /**
     * 查询积分任务状态
     *
     * @param uid    用户id
     * @param taskId 任务id
     * @return 积分任务状态
     */
    Map<Long, List<PointDetailDTO>> queryRecordByTaskId(Long uid, List<Long> taskId);
    /**
     * 查询积分任务状态
     *
     * @param uid    用户id
     * @param taskId 任务id
     * @return 积分任务状态
     */
    List<PointDetailDTO> queryRecordByTaskId(Long uid, Long taskId);

    /**
     * 根据uid和商品id查询订单
     * <AUTHOR>
     * @date 2022/3/15 15:46
     * @param uid
     * @param productId
     * @return cn.emoney.common.result.PointResult<cn.emoney.pojo.bo.PointOrderAddDataDTO>
     */
    PointResult<List<PointOrderAddDataDTO>> pointOrderQueryByUidAndProId(String uid,String productId);
}
