package cn.emoney.service;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.bo.Benefit588DTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-18
 */
public interface Benefit588Service {
    List<Benefit588DTO> getBenefitList(String uid, String actCode);
    Result<String> getBenefit(String uid,String pid,String benefitID,String actCode,String dayOfMonth,String source);
    List<Act588BenifitRecordDO> getUserBenefitRecord(String uid,String actCode);
    Result<String> sendPP_20231127(String actCode,String uid,String pid);
    Result<String> sendPointAndCoupon(String actCode,String uid,String pid,String pointTaskID,String couponActivityID,double couponPrice);
}
