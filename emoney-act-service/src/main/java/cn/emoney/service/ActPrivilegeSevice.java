package cn.emoney.service;

import cn.emoney.pojo.Act_PrivilegeConfigDO;
import cn.emoney.pojo.Act_PrivilegeRecordDO;
import cn.emoney.pojo.PrivilegeClientInd;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-22
 */
public interface ActPrivilegeSevice {
    int AddUserPrivilegeRecord(String uid,String actCode,Integer source);
    Act_PrivilegeRecordDO getUserPrivilegeRecord(String uid, String actCode);
    List<Act_PrivilegeConfigDO> getPrivilegeConfig(String actCode);
    List<PrivilegeClientInd> getUserPrivilegeConfig(String uid, String actCode);
}
