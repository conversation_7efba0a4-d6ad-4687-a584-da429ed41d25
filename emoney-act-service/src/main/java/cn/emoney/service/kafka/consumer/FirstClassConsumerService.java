package cn.emoney.service.kafka.consumer;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.AccountVO;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-03-28
 */
@Service
@Slf4j
public class FirstClassConsumerService {
    @Autowired
    private RedisService redisService;

    @Autowired
    private UserService userService;

    @Autowired
    private LogisticsService logisticsService;
    private String rediskey_issend= RedisConstants.Redis_Pre_Activity + "firstclass:issend:";

    /**
     * 看完课程用户-延期15天
     * <AUTHOR>
     * @date 2022/3/25 16:09
     */
    @KafkaListener(groupId = "unifyFirstClassGroup",topics = "firstClassUserComing", containerFactory = "firstClassUserContainerFactory", autoStartup = "${spring.kafka.listener.enabled:true}")
    public void handleFirstClassUserMessage(ConsumerRecord record, Acknowledgment acknowledgment) {
        try {
            log.info("大师第一课topic->{},value->{},offset->{},partition->{}", record.topic(), record.value(), record.offset(), record.partition());
            //log.info("i'm here"+JSON.toJSONString(record));
            String rediskey = rediskey_issend;
            String message = (String) record.value();
            String uid = message;
            String activityID = "";
            if(message.indexOf("_")>0){
                uid = message.split("_")[0];
                activityID = message.split("_")[1];
            }

            if(!StringUtils.isEmpty(activityID)) {
                if (Integer.parseInt(activityID) > 2) {
                    rediskey = rediskey + activityID + ":";
                }
            }

            String isSend = (String) redisService.hashGet(rediskey, uid.trim());
            if (!StrUtil.isEmpty(isSend)) {
                return;
            }
            String account = userService.listAccountByUid(uid).stream()
                    .filter(vo -> vo.getPid().toString().contains("88808") || vo.getPid().toString().contains("88802"))
                    .findFirst()
                    .map(AccountVO::getUsername)
                    .orElse(null);
            if (account == null) {
                return;
            }
            //根据uid获取用户版本
            String pid = userService.GetAccountPID(account);
            if (pid!=null && (pid.contains("88802") || pid.contains("88808"))) {
                SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
                sendPrivilegeDTO.setAppId("A009");
                if(activityID.equals("4")){
                    //********-智学豪礼 盈享1年
                    sendPrivilegeDTO.setActivityID(pid.startsWith("88802") ? "PAC1230105110632994" : "PAC1230105110518254");
                }else if(activityID.equals("5")){
                    sendPrivilegeDTO.setActivityID(pid.startsWith("88802") ? "PAC1230216113555949" : "PAC123021611371737");
                }else if(activityID.equals("7")){
                    sendPrivilegeDTO.setActivityID(pid.startsWith("88802") ? "PAC1230529120034187" : "PAC1230529120030583");
                }else if(activityID.equals("8")){
                    sendPrivilegeDTO.setActivityID(pid.startsWith("88802") ? "PAC123052912074362" : "PAC1230529120739583");
                }else{
                    sendPrivilegeDTO.setActivityID(pid.startsWith("88802") ? "PAC1220328155102842" : "PAC1220328154950240");
                }
                sendPrivilegeDTO.setReason("大师第一课完课赠送15天");
                sendPrivilegeDTO.setApplyUserID("scb_public");
                List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
                CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();
                createActivityGrantApplyAccountDTO.setAccountType(1);
                createActivityGrantApplyAccountDTO.setAccount(account);
                createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
                sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

                Boolean resultSendPrivilege = logisticsService.sendPrivilege(sendPrivilegeDTO);
                if (resultSendPrivilege) {
                    redisService.hashSet(rediskey, uid.trim(), "1");
                    log.info("大师第一课完课赠送15天使用期成功,uid:" + uid);
                } else {
                    log.info("大师第一课完课赠送15天使用期失败,uid:" + uid);
                }
            }
        }catch (Exception exp){
            log.error(exp.getMessage(), exp);
        }
        finally {
            // 手动提交 offset
            acknowledgment.acknowledge();
        }
    }

}
