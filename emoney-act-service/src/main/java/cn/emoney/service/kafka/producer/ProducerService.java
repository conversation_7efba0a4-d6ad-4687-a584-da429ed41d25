package cn.emoney.service.kafka.producer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

/**
 * 发送Kafka消息服务
 *
 * <AUTHOR>
 * @date 2022/02/18 14:50
 **/

@Slf4j
@Service
public class ProducerService {

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    /**
     * 功能描述: 发送kafka消息
     * 〈〉
     * @Param: topic
     * @Return:
     * @Author: tengdengming
     * @Date: 2022/2/18 15:34
     */
    public void sendMessage(String topic,String message){

        try {
            log.info("send message to kafka");
            kafkaTemplate.send(topic, message);
        }catch (Exception e) {
            log.error("发送kafka消息异常",e);
        }


    }

    /**
    * 功能描述: 异步发送
    * 〈〉
    * @Param: [topic, message]
    * @Return: void
    * @Author: tengdengming
    * @Date: 2022/2/18 15:53
    */
    public void sendMessageAsync(String topic,String message){
        try {

            log.info("kafka send msg start");
            ListenableFuture<SendResult<String, String>> send = kafkaTemplate.send(topic, message);
            send.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
                @Override
                public void onFailure(Throwable throwable) {
                    log.error("kafka send msg err, ex = {}, topic = {}, data = {}", throwable, topic, message);
                }

                @Override
                public void onSuccess(SendResult<String, String> integerStringSendResult) {
                    log.info("kafka send msg success, topic = {}, data = {}", topic, message);
                }
            });

            log.info("kafka send msg end");


        }catch (Exception e) {

        }
    }


    /***
     * 功能描述: 发送kafka消息，并添加执行回调
     * 〈〉
     * @Param: [topic, message, callback]
     * @Return: void
     * @Author: tengdengming
     * @Date: 2022/2/18 16:55
     */

    public void sendMessageCallback(String topic,String message,ListenableFutureCallback<SendResult> callback){
        try {

            log.info("kafka send msg start");
            ListenableFuture<SendResult<String, String>> send = kafkaTemplate.send(topic, message);
            send.addCallback(callback);
            log.info("kafka sendMessageCallback end");

        }catch (Exception e) {
            log.error("sendMessageCallback发送消息异常",e);
        }

    }

}
