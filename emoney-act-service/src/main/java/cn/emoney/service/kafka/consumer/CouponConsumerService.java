package cn.emoney.service.kafka.consumer;

import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.bo.CmpCustBaseInfo;
import cn.emoney.pojo.bo.SendCouponRequestDTO;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.MobileService;
import cn.emoney.service.UserService;
import cn.emoney.service.kafka.producer.ProducerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @date 2023-05-12
 */
@Service
@Slf4j
public class CouponConsumerService {
    @Value("${GetCustBaseInfoUrl:}")
    private String GetCustBaseInfoUrl;

    @Autowired
    private MobileService mobileService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private ProducerService producerService;

    @Autowired
    private UserService userService;

    @KafkaListener(groupId = "unifySendCouponGroup",topics = "sendCouponUserComing", containerFactory = "sendCouponUserContainerFactory", autoStartup = "${spring.kafka.listener.enabled:true}")
    public void handleSendCouponUserMessage(ConsumerRecord record, Acknowledgment acknowledgment) {
        try {
            log.info("topic->{},value->{},offset->{},partition->{}", record.topic(), record.value(), record.offset(), record.partition());
            //赠送优惠券
            String message = (String) record.value();
            SendCouponRequestDTO req = JsonUtil.toBean(message, SendCouponRequestDTO.class);
            if(req!=null){
                LogisticsResult<String> result = logisticsService.sendCoupon(req);
                if (result.getCode() == 0) {
                    //领取成功记录领取状态
                    userService.AddCountByActCode(req.COUPON_ACTIVITY_ID, req.getUid(), String.valueOf(System.currentTimeMillis()));
                }
            }
        }catch (Exception exp){
            log.error(exp.getMessage(), exp);
        }
        finally {
            // 手动提交 offset
            acknowledgment.acknowledge();
        }
    }
}
