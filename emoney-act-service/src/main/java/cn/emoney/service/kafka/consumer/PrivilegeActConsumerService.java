package cn.emoney.service.kafka.consumer;

import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.bo.CmpCustBaseInfo;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.AccountVO;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.MobileService;
import cn.emoney.service.UserService;
import cn.emoney.service.kafka.producer.ProducerService;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-12
 */
@Service
@Slf4j
public class PrivilegeActConsumerService {
    @Value("${GetCustBaseInfoUrl:}")
    private String GetCustBaseInfoUrl;

    @Autowired
    private MobileService mobileService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private ProducerService producerService;

    @Autowired
    private UserService userService;
    /**
     * 特权领取成功获取专员并发送短信
     * <AUTHOR>
     * @date 2023/5/15 13:15
     * @param record
     * @param acknowledgment
     */
    @KafkaListener(groupId = "unifyPrivilegeActGroup",topics = "privilegeActUserComing", containerFactory = "privilegeActUserContainerFactory", autoStartup = "${spring.kafka.listener.enabled:true}")
    public void handlePrivilegeActUserMessage(ConsumerRecord record, Acknowledgment acknowledgment) {
        try {
            log.info("topic->{},value->{},offset->{},partition->{}", record.topic(), record.value(), record.offset(), record.partition());
            //获取用户cmp专员  并发送短信

            String message = (String) record.value();
            String identifyType = "3";
            String uid = "";
            String mobileX = "";
            String actcode = "";
            if(message.indexOf("_")>0){
                uid = message.split("_")[0];
                mobileX = message.split("_")[1];
                actcode = message.split("_")[2];
            }
            if(StringUtils.isEmpty(uid)){
                return;
            }
            if(!actcode.equals("privilege20230601") && !actcode.equals("privilege20230809")&& !actcode.equals("privilege20231011")){
                return;
            }

            String msgContent = "尊敬的用户您好，您已成功领取【五大盯盘利器】——四大资金VIP指标31天+量王叠现VIP指标31天；带您看大势，跟资金，炒股步步升！6月9日《战龙头炒股夏令营》开班！";

            if(actcode.equals("privilege20230809")){
                msgContent = "尊敬的用户您好，您已成功领取【五大盯盘利器】——四大资金VIP指标14天+量王叠现VIP指标14天；带您看大势，跟资金，炒股步步升！8月14日《决战大波段》开班！";
            }
            if(actcode.equals("privilege20231011")){
                msgContent = "您已成功领取高端利器：资金VIP指标组31天+量王叠现VIP指标31天；助你多维度洞悉资金动向，监控量能异动，洞悉起爆拉升信号！领取成功后，请下载智盈最新版软件查看使用高端VIP指标组，10月11日《决战大波段》系列课重磅开幕！";
            }

            //获取cmp专员信息
            String res = OkHttpUtil.get(MessageFormat.format(GetCustBaseInfoUrl,identifyType,uid),null);
            if(!StringUtils.isEmpty(res)) {
                CmpCustBaseInfo cmpCustBaseInfo = JsonUtil.toBean(res, CmpCustBaseInfo.class);
                if (cmpCustBaseInfo != null) {
                    String serviceOnlyName = cmpCustBaseInfo.getServiceOnlyName();
                    String servicePhone = cmpCustBaseInfo.getPhone();

                    if (!StringUtils.isEmpty(serviceOnlyName)) {
                        msgContent += "更多指标干货与课程信息请联系您的服务专员" + serviceOnlyName;
                        if (!StringUtils.isEmpty(servicePhone)) {
                            msgContent += "，联系方式" + servicePhone + "，祝您股市长虹！";
                        }
                    }
                }
            }

            //发送短信
            ApiGateWayResult<String> agr = mobileService.sendTextMessage(mobileX, URLEncoder.encode(msgContent,"UTF-8"), "181");
            if (agr.getRetCode() == 0) {
                log.info("特权领取成功获取专员并发送短信,uid:" + message + ",专员信息:" + res);
            } else {
                log.info("特权领取失败获取专员并发送短信,uid:" + message + ",专员信息:" + res);
            }

        }catch (Exception exp){
            log.error(exp.getMessage(), exp);
        }
        finally {
            // 手动提交 offset
            acknowledgment.acknowledge();
        }
    }


    @KafkaListener(groupId = "unifySendPrivilegeGroup",topics = "sendPrivilegeUserComing", containerFactory = "sendPrivilegeUserContainerFactory", autoStartup = "${spring.kafka.listener.enabled:true}")
    public void handleSendPrivilegeUserMessage(ConsumerRecord record, Acknowledgment acknowledgment) {
        try {
            log.info("topic->{},value->{},offset->{},partition->{}", record.topic(), record.value(), record.offset(), record.partition());
            //赠送特权或使用期

            String message = (String) record.value();
            SendPrivilegeDTO sendPrivilegeDTO = JsonUtil.toBean(message, SendPrivilegeDTO.class);

            if(sendPrivilegeDTO!=null){
                Result<String> ret = logisticsService.sendPrivilegeResult(sendPrivilegeDTO);
                if (ret.isSuccess()) {
                    //领取成功记录领取状态
                    userService.AddCountByActCode(sendPrivilegeDTO.getActCode(), sendPrivilegeDTO.getUid(), String.valueOf(System.currentTimeMillis()));
                }
            }
        }catch (Exception exp){
            log.error(exp.getMessage(), exp);
        }
        finally {
            // 手动提交 offset
            acknowledgment.acknowledge();
        }
    }
}
