package cn.emoney.service;

import cn.emoney.common.result.Result;

public interface CouponService {

    /**
     * 发送优惠券
     * @param actCode
     * @param mobile
     */
    Result<String> sendCoupon(String actCode, String mobile) throws Exception;

    /**
     * 移除领取缓存标识
     * @param actCode
     * @param mobileX
     * @return
     */
    Result<String> remove(String actCode,String mobileX);

}
