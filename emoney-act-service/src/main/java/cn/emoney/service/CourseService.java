package cn.emoney.service;

import cn.emoney.pojo.bo.CourseDetailDTO;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

public interface CourseService {

    default CourseDetailDTO detail(Integer id) {
        try {
            return asyncDetail(id).join().orElse(null);
        } catch (CompletionException ex) {
            if (ex.getCause() instanceof RuntimeException) {
                throw (RuntimeException) ex.getCause();
            }
            throw ex;
        }
    }

    CompletableFuture<Optional<CourseDetailDTO>> asyncDetail(Integer id);
}
