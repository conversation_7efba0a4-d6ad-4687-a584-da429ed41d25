package cn.emoney.act.core.request;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import okio.BufferedSource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

@Slf4j
@Component
public class LoggingInterceptor implements Interceptor {
    private Integer maxBodySize = 512;

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        if (log.isInfoEnabled()) {
            long t1 = System.currentTimeMillis();
            Response response = chain.proceed(request);
            long t2 = System.currentTimeMillis();
            try {
                if (response.body() == null) {
                    log.info("request cost: {}ms\n" +
                             "{} {}\n" +
                             "{} {} {}\n" +
                             "{}",
                            (t2 - t1),
                            response.request().method(), response.request().url(),
                            response.protocol(), response.code(), response.message(),
                            response.headers());
                } else {
                    log.info("request cost: {}ms\n" +
                             "{} {}\n" +
                             "{} {} {}\n" +
                             "{}\n" +
                             "{}",
                            (t2 - t1),
                            response.request().method(), response.request().url(),
                            response.protocol().toString().toUpperCase(), response.code(), response.message(),
                            response.headers(),
                            stringResponseBody(response, maxBodySize));
                }
            } catch (Exception e) {
                log.error("okhttp3 log error", e);
            }
            return response;
        } else {
            return chain.proceed(request);
        }
    }

    private String stringResponseBody(Response response, int maxBytes) {
        ResponseBody body = response.body();
        if (body == null) {
            return null;
        }
        try {
            BufferedSource source = body.source();
            boolean cut = source.request(maxBytes + 1);
            try (Buffer buffer = source.getBuffer().clone()) {
                Charset charset = Optional.ofNullable(body.contentType()).map(MediaType::charset).orElse(StandardCharsets.UTF_8);
                if (cut) {
                    return buffer.readString(maxBytes, charset) + "...";
                } else {
                    return buffer.readString(charset);
                }
            }
        } catch (Exception e) {
            log.error("okhttp3 logResponse error >> ex", e);
        }
        return null;
    }
}