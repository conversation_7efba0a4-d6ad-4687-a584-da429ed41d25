package cn.emoney.act.support.kafka.serializer;

import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.kafka.support.serializer.JsonDeserializer;

import java.util.Map;

public class JsonOrStringDeserializer<T> extends JsonDeserializer<T> {
    private final StringDeserializer deserializer = new StringDeserializer();

    @Override
    public void configure(Map<String, ?> configs, boolean isKey) {
        super.configure(configs, isKey);
        this.deserializer.configure(configs, isKey);
    }

    @Override
    public T deserialize(String topic, Headers headers, byte[] data) {
        if (headers == null || typeMapper.toJavaType(headers) == null) {
            return (T) deserializer.deserialize(topic, data);
        }
        return super.deserialize(topic, headers, data);
    }

    @Override
    public void close() {
        super.close();
        this.deserializer.close();
    }
}
