package cn.emoney.act.support.kafka.serializer;

import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.kafka.support.serializer.JsonSerializer;
import org.springframework.lang.Nullable;

import java.util.Map;

public class JsonOrStringSerializer<T> extends JsonSerializer<T> {
    private final StringSerializer stringSerializer = new StringSerializer();

    @Override
    public void configure(Map<String, ?> configs, boolean isKey) {
        super.configure(configs, isKey);
        this.stringSerializer.configure(configs, isKey);
    }

    @Nullable
    @Override
    public byte[] serialize(String topic, @Nullable T data) {
        if (data instanceof String) {
            return this.stringSerializer.serialize(topic, (String) data);
        }
        return super.serialize(topic, data);
    }

    @Nullable
    @Override
    public byte[] serialize(String topic, Headers headers, @Nullable T data) {
        if (data instanceof String) {
            return this.stringSerializer.serialize(topic, (String) data);
        }
        return super.serialize(topic, headers, data);
    }

    @Override
    public void close() {
        super.close();
        this.stringSerializer.close();
    }
}
