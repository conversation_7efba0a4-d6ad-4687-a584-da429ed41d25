package cn.emoney.act.client.firstclass.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class FirstClassCourseTempDTO {
    @JsonAlias("Id")
    private Integer id;
    @JsonAlias("ActivityId")
    private String activityId;
    @JsonAlias("LiveVideoId")
    private String liveVideoId;
    @JsonAlias("ClassName")
    private String className;
    @JsonAlias("ClassSummary")
    private String classSummary;
    @JsonAlias("TeacherName")
    private String teacherName;
    @JsonAlias("ClassUrl")
    private String classUrl;
    @JsonAlias("BeginTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;
    @JsonAlias("EndTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    @JsonAlias("ParentId")
    private Integer parentId;
    @JsonAlias("ParentName")
    private String parentName;
    @JsonAlias("PointTaskId")
    private String pointTaskId;

    /**
     * 预约基数
     */
    @JsonAlias("BookBaseAmount")
    private Integer bookBaseAmount;
    /**
     * 回放基数
     */
    @JsonAlias("ReplayBaseAmount")
    private Integer replayBaseAmount;
    /**
     * 课程封面图
     */
    @JsonAlias("AppCoverImg")
    private String appCoverImg;
    /**
     * 周评, groupBy string
     */
    @JsonAlias("WeekComment")
    private String weekComment;

    // 可变链接 start
    @JsonAlias("TipName")
    private String tipName;
    @JsonAlias("VoteUrl")
    private String voteUrl;
    // 可变链接 end
    // 练习
    @JsonAlias("VoteUrl1")
    private String voteUrl1;
    // 练习积分奖励
    @JsonAlias("PracticeTaskId")
    private String practiceTaskId;
    @JsonAlias("TgNO")
    private String tgNo;
    @JsonAlias("Order")
    private Integer order;
}
