package cn.emoney.act.client.user;

import cn.emoney.common.result.ResultInfo;
import cn.emoney.pojo.vo.ValidateUserVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "user", contextId = "api-m/user", url = "http://webapi.emoney.cn", path = "/User/api/")
public interface UserClient {
    @GetMapping("User.ValidateUserPasswordByPID?appid=10013")
    ResultInfo<ValidateUserVO> validateUserPasswordByPID(@RequestParam String username,
                                                         @RequestParam String password,
                                                         @RequestParam String pid);
}
