package cn.emoney.act.client.pointprod.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class PointTaskDetail {
    private Long id;
    private Long taskId;
    private Long subId;
    private Integer taskType;
    /**
     * 任务备名称
     * language html
     */
    private String taskName;
    private Float taskPoints;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskEndTime;
    private Boolean isDirectional;
    private Boolean isDailyTask;
    //    private List<Long> productVersion;
//    private List<Integer> publishPlatFormType;
    private Boolean isShowInHomePage;
    private Integer taskOrder;
    private Integer dailyJoinTimes;
    private String pcRedirectUrl;
    private String appRedirectUrl;
    private String wechatRedirectUrl;
    /**
     * 任务备注
     * language html
     */
    private String taskRemark;
    private String taskButtonText;
    private Boolean isBigImg;
    private String pcTaskImgUrl;
    private String appTaskImgUrl;
    private String wechatTaskImgUrl;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 创建人：<EMAIL>
     */
    private String createBy;
    /**
     * 修改人：<EMAIL>
     */
    private String updateBy;
    private String remark;
    private String userGroup;
    private Integer sendType;
    private String statisticalClassification;

//    /**
//     * @param productVersion "888010000,888010400,888194010,888194020,",
//     */
//    public void setProductVersion(String productVersion) {
//        this.productVersion = Arrays.stream(productVersion.split(",")).map(Long::parseLong).collect(Collectors.toList());
//    }
//
//    /**
//     * @param publishPlatFormType "1,2,3,",
//     */
//    public void setPublishPlatFormType(String publishPlatFormType) {
//        this.publishPlatFormType = Arrays.stream(publishPlatFormType.split(",")).map(Integer::parseInt).collect(Collectors.toList());
//    }
}
