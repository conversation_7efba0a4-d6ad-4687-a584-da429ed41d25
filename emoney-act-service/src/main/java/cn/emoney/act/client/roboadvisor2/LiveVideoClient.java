package cn.emoney.act.client.roboadvisor2;

import cn.emoney.boot.starter.webapi.annotation.WebApiClient;
import cn.emoney.boot.starter.webapi.annotation.WebApiDefinition;
import cn.emoney.common.result.ResultInfo;
import cn.emoney.pojo.bo.CourseDetailDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@WebApiClient(name = "roboadvisor2", version = "1.0", contextId = "roboadvisor2/livevideo")
public interface LiveVideoClient {
    @WebApiDefinition("获取课程详情")
    @GetMapping("livevideo.getcoursedetail")
    ResultInfo<CourseDetailDTO> getCourseDetail(@RequestParam("courseid") Integer courseId);
}