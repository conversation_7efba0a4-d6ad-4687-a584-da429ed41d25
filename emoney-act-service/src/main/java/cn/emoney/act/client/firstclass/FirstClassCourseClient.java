package cn.emoney.act.client.firstclass;

import cn.emoney.act.client.firstclass.dto.FirstClassCourseRequestBody;
import cn.emoney.act.client.firstclass.dto.FirstClassCourseTempDTO;
import cn.emoney.common.result.MobilePageDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Optional;

@FeignClient(name = "firstclass", contextId = "firstclass/course", url = "${act.service.m-api.url-base:}")
public interface FirstClassCourseClient {
    @PostMapping("ServerApi/MasterFirstCourse/List?IssueNumber=&api.protocol=rawjson")
    MobilePageDTO<FirstClassCourseTempDTO> list(@RequestBody FirstClassCourseRequestBody body);

    @GetMapping("ServerApi/MasterFirstCourse/Get?IssueNumber=&api.protocol=rawjson")
    Optional<FirstClassCourseTempDTO> get(@RequestParam("Id") Integer id);
}
