package cn.emoney.act.client.video.dto;

import lombok.Data;

import java.time.Duration;
import java.util.Date;

@Data
public class UserLiveStatisticsDTO {
    /**
     * Id
     * e.g., 66ad9a82f0d975439c9422dd
     */
    private String id;

    /**
     * 视频id
     * e.g., 4f8fb29f7386417b860af770b421c2d7
     */
    private String liveId;

    /**
     * 用户id
     * e.g., 404161452
     */
    private long userId;

    /**
     * 首次观看时间
     * e.g., 1722653314919
     */
    private Date createTime;

    /**
     * 最后观看时间
     * e.g., 1722656606275
     */
    private Date updateTime;

    /**
     * 开始时间
     * e.g., 1722653313339
     */
    private Date startTime;

    /**
     * 离开时间
     * e.g., 1722656604749
     */
    private Date endTime;

    /**
     * 总计观看时长(秒)
     * e.g., 3291
     */
    private Duration totalDuration;

    /**
     * 计算的观看时长（秒）
     * e.g., 3143
     */
    private Duration totalPlayDuration;

    public void setLiveId(String liveId) {
        this.liveId = liveId.intern();
    }
}
