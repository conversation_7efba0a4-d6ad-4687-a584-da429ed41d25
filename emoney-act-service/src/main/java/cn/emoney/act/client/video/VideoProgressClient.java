package cn.emoney.act.client.video;

import cn.emoney.act.client.video.dto.VideoProgressDTO;
import cn.emoney.common.result.MobileResultDTO;
import lombok.Data;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 视频进度获取
 *
 * @see <a href="https://cfc.emoney.cn/pages/viewpage.action?pageId=79761572">智盈App / 视频 / 五行波段课程学习</a>
 */
@FeignClient(name = "video",
        contextId = "video/progress",
        url = "${act.service.m-api.url-base:}"
)
@Deprecated
public interface VideoProgressClient {

    /**
     * 获取用户播放时长信息
     *
     * @param uid     用户id
     * @param videoId 视频id
     * @return 用户播放时长信息
     */
    @GetMapping("serverapi/videoPcApi/GetGlobalPlayTime")
    MobileResultDTO<Long> getGlobalPlayTime(@RequestParam("gid") Long uid,
                                            @RequestParam("videoIdentity") String videoId);

    @PostMapping("serverapi/videoPcApi/GetUserPlayProgress")
    MobileResultDTO<Map<String, VideoProgressDTO>> getUserPlayProgress(@RequestParam("gid") Long uid,
                                                                       @RequestBody VideoProgressRequest request);

    @Data
    class VideoProgressRequest {
        // uid由参数传递
        // private Long gid;
        private List<String> videoIds;
    }

    /**
     * 获取用户播放详情信息
     *
     * @param uid      用户id
     * @param videoIds 视频id
     * @return 用户播放详情
     */
    default MobileResultDTO<Map<String, VideoProgressDTO>> getUserPlayProgress(Long uid, List<String> videoIds) {
        VideoProgressRequest request = new VideoProgressRequest();
        request.setVideoIds(videoIds);
        return getUserPlayProgress(uid, request);
    }
}
