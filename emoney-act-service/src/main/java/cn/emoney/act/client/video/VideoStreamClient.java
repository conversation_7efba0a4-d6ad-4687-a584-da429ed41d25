package cn.emoney.act.client.video;

import cn.emoney.act.client.video.dto.UserLiveStatisticsDTO;
import cn.emoney.act.client.video.dto.UserVodStatisticsDTO;
import cn.emoney.common.result.MobileResultDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@FeignClient(name = "video",
        contextId = "m-api/video-stream",
        url = "${act.service.m-api.url-base:}",
        path = "servicecenter/VideoStream"
)
public interface VideoStreamClient {

    /**
     * 查询用户 vod 统计
     *
     * @param vodId     vodId
     * @param startTime <= updateTime
     * @param endTime   >= updateTime
     */
    @GetMapping("UserVod/QueryUserVodStatistics")
    MobileResultDTO<List<UserVodStatisticsDTO>> queryUserVodStatistics(
            @RequestParam("vodId")
            String vodId,
            @RequestParam("startTime")
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            LocalDateTime startTime,
            @RequestParam("endTime")
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            LocalDateTime endTime
    );

    /**
     * 查询用户 vod 统计
     *
     * @param vodId     vodId
     * @param startDate <= updateTime
     * @param endDate   >= updateTime
     */
    default MobileResultDTO<List<UserVodStatisticsDTO>> queryUserVodStatistics(
            String vodId,
            LocalDate startDate,
            LocalDate endDate
    ) {
        return queryUserVodStatistics(
                vodId,
                startDate.atStartOfDay(),
                endDate.plusDays(1).atStartOfDay().minusSeconds(1)
        );
    }

    /**
     * 查询用户直播统计
     *
     * @param liveId    liveId
     * @param startTime <= updateTime
     * @param endTime   >= updateTime
     */
    @GetMapping("UserLive/QueryUserLiveStatistics")
    MobileResultDTO<List<UserLiveStatisticsDTO>> queryUserLiveStatistics(
            @RequestParam("liveId")
            String liveId,
            @RequestParam("startTime")
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            LocalDateTime startTime,
            @RequestParam("endTime")
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            LocalDateTime endTime
    );

    /**
     * 查询用户直播统计
     *
     * @param liveId    liveId
     * @param startDate <= updateTime
     * @param endDate   >= updateTime
     */
    default MobileResultDTO<List<UserLiveStatisticsDTO>> queryUserLiveStatistics(
            String liveId,
            LocalDate startDate,
            LocalDate endDate
    ) {
        return queryUserLiveStatistics(
                liveId,
                startDate.atStartOfDay(),
                endDate.plusDays(1).atStartOfDay().minusSeconds(1)
        );
    }
}
