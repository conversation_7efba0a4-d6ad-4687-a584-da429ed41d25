package cn.emoney.act.client.roboadvisor;

import cn.emoney.boot.starter.webapi.annotation.WebApiClient;
import cn.emoney.boot.starter.webapi.annotation.WebApiDefinition;
import cn.emoney.pojo.bo.MobileRegisterResultDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@WebApiClient(name = "roboadvisor", version = "1.0", contextId = "roboadvisor/user")
public interface RoboAdvisorUserClient {
    @WebApiDefinition("手机号注册")
    @GetMapping("user.regmobile")
    MobileRegisterResultDTO regByMobile(@RequestParam String mobile,
                                        @RequestParam String sid,
                                        @RequestParam String tid,
                                        @RequestParam String hardwareInfo,
                                        @RequestParam String clientVersion);
}