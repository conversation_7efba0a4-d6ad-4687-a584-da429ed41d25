package cn.emoney.act.client.firstclass;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Optional;

@FeignClient(name = "firstclass", contextId = "firstclass/meta", url = "${act.service.m-api.url-base:}")
public interface FirstClassMetaClient {

    /**
     * 获取课程的排课信息，
     *
     * @param id 活动ID
     * @return [20240401, 20240408, 20240415, 20240422]
     * @since 智盈第一课 2024年3月26日
     */
    @Cacheable(value = "act:first-class:class-meta:schedule", key = "#id")
    @GetMapping("ServerApi/MasterFirstCourse/Schedule?api.protocol=rawjson")
    Optional<List<Long>> schedule(@RequestParam("activityId") Long id);
}
