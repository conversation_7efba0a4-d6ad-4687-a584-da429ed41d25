package cn.emoney.act.client.firstclass.dto;

import com.fasterxml.jackson.annotation.*;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.domain.Sort;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FirstClassCourseRequestBody {
    @JsonProperty("ColumnId")
    private Integer columnId;
    private Integer page;
    private Integer rows;
    @JsonProperty("BeginTimeStart")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTimeStart;
    @JsonProperty("BeginTimeEnd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime beginTimeEnd;
    private Sort sort;

    /**
     * ?sort=flied1,asc&sort=flied2,desc
     */
    @JsonGetter("sort")
    public List<String> getSortParam() {
        if (sort == null || sort.isUnsorted()) {
            return null;
        }
        return sort.stream()
                .map(order -> order.getProperty() + "," + order.getDirection().name().toLowerCase())
                .collect(Collectors.toList());
    }

    @JsonSetter("sort")
    public void setSortParam(List<String> sortParam) {
        if (sortParam == null || sortParam.isEmpty()) {
            return;
        }
        this.sort = Sort.by(sortParam.stream()
                .map(s -> s.split(",", 2))
                .map(s -> new Sort.Order(Sort.Direction.fromString(s[1]), s[0]))
                .collect(Collectors.toList()));
    }
}
