package cn.emoney.act.client.video.dto;

import lombok.Data;

import java.time.Duration;
import java.util.Date;

@Data
public class UserVodStatisticsDTO {
    /**
     * Id
     * e.g., 66ad9a82f0d975439c9422dd
     */
    private String id;

    /**
     * 视频id
     * e.g., 4f8fb29f7386417b860af770b421c2d7
     */
    private String vodId;

    /**
     * 用户id
     * e.g., 404161452
     */
    private long userId;

    /**
     * 创建时间
     * e.g., 1722653314919
     */
    private Date createTime;

    /**
     * 更新时间
     * e.g., 1722656606275
     */
    private Date updateTime;

    /**
     * 首次观看时间
     * e.g., 1722653313339
     */
    private Date firstPlayTime;

    /**
     * 末次观看时间
     * e.g., 1722656604749
     */
    private Date lastPlayTime;

    /**
     * 总时长
     * e.g., 3291
     */
    private Duration totalDuration;

    /**
     * 心跳期间的播放时长
     * e.g., 3291
     */
    private Duration totalPlayDuration;

    /**
     * 观看时长
     * e.g., 3143
     */
    private Duration totalPlaybackDuration;

    /**
     * 等效观看时长
     * e.g., 3143
     */
    private Duration totalEquivalentDuration;

    public void setVodId(String vodId) {
        this.vodId = vodId.intern();
    }
}
