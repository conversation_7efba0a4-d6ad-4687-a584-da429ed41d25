package cn.emoney.act.client.pointprod;

import cn.emoney.act.client.pointprod.dto.PointTaskDetail;
import cn.emoney.boot.starter.webapi.annotation.WebApiClient;
import cn.emoney.boot.starter.webapi.annotation.WebApiDefinition;
import cn.emoney.common.result.PointResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;

@WebApiClient(name = "pointprod", version = "v1", contextId = "pointprod/pointtask")
public interface PointTaskClient {

    @WebApiDefinition(value = "查询积分任务详情", appId = 10199, unwrap = false)
    @GetMapping("pointtask.queryallbytaskids")
    PointResult<List<PointTaskDetail>> queryAllByTaskIds(@RequestParam List<Long> taskIds);

    default PointResult<List<PointTaskDetail>> queryAllByTaskIds(Collection<Long> taskId) {
        if (taskId instanceof List) {
            return queryAllByTaskIds((List<Long>) taskId);
        }
        return queryAllByTaskIds(new ArrayList<>(taskId));
    }

    default Optional<PointTaskDetail> queryByTaskId(Long taskId) {
        PointResult<List<PointTaskDetail>> result = queryAllByTaskIds(Collections.singleton(taskId));
        if (result.isSuccess()) {
            if (result.getData() != null && !result.getData().isEmpty()) {
                return Optional.of(result.getData().get(0));
            }
            return Optional.empty();
        }
        throw new IllegalStateException("积分服务请求失败, taskId: " + taskId + ", message:" + result);
    }
}
