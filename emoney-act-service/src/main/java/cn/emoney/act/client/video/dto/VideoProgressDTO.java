package cn.emoney.act.client.video.dto;

import lombok.Data;

@Data
@Deprecated
public class VideoProgressDTO {
    private Long uuid;
    /**
     * 用户uid
     */
    private Long gid;
    /**
     * 展示id
     */
    private String videoIdentity;
    /**
     * 直播id,可能为空
     */
    private String webCastId;
    /**
     * 最大播放进度，单位毫秒
     */
    private Long maxProgress;
    /**
     * 当前播放进度，单位毫秒
     */
    private Long currentProgress;
    /**
     * 内容时长，如果是直播此字段为0。 单位是毫秒
     */
    private Long duration;
    private Long updateTime;
    /**
     * pc学习时长，单位毫秒
     */
    private Long totalPlayTime;
    /**
     * 全局播放时长，单位毫秒
     */
    private Long globalTotalPlayTime;

    public void setUpdateTime(Long updateTime) {
        if (updateTime >= 0) {
            this.updateTime = updateTime;
        }
    }
}
