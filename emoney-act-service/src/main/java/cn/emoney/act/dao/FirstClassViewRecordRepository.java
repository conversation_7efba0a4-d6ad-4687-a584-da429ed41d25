package cn.emoney.act.dao;

import cn.emoney.act.entity.FirstClassViewRecordEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface FirstClassViewRecordRepository extends CrudRepository<FirstClassViewRecordEntity, Long> {

    List<FirstClassViewRecordEntity> findAllByUidAndQuestId(Long uid, Long questId);

    List<FirstClassViewRecordEntity> findAllByUidAndAcceptId(Long uid, Long acceptId);

    @Query("select distinct subId from FirstClassViewRecordEntity where uid = :uid and questId = :questId")
    Set<Integer> findAllSubIdByUidAndQuestId(Long uid, Long questId);

    @Query("select distinct subId from FirstClassViewRecordEntity where uid = :uid and subId in :subIds")
    Set<Integer> findAllSubIdByUidAndSubIds(Long uid, List<Integer> subIds);
}
