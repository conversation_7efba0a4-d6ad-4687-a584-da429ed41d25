package cn.emoney.act.dao;

import cn.emoney.act.entity.ActivityQuestAcceptEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ActivityQuestAcceptRepository extends JpaRepository<ActivityQuestAcceptEntity, Long> {
    Optional<ActivityQuestAcceptEntity> findTopByUidAndQuestIdOrderByCreateTimeDesc(Long uid, Long questId);

    @Modifying(clearAutomatically = true)
    @Query("update ActivityQuestAcceptEntity t " +
           "set t.status = 1, t.finishTime = getdate() " +
           "where t.questId = :questId and t.uid = :uid and t.status = 0")
    int finishQuest(@Param("uid") Long uid, @Param("questId") Long questId);

    @Modifying(clearAutomatically = true)
    @Query("update ActivityQuestAcceptEntity t " +
           "set t.status = -1, t.cancelTime = getdate() " +
           "where t.questId = :questId and t.uid = :uid and t.status != -1")
    int cancelQuest(@Param("uid") Long uid, @Param("questId") Long questId);
}
