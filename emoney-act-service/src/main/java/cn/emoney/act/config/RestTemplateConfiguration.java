package cn.emoney.act.config;

import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Configuration
public class RestTemplateConfiguration {
    @Bean
    @Primary
    public OkHttp3ClientHttpRequestFactory clientHttpRequestFactory(OkHttpClient client) {
        return new OkHttp3ClientHttpRequestFactory(client);
    }

    @Bean
    @Primary
    public RestTemplate restTemplate(RestTemplateBuilder builder, ClientHttpRequestFactory factory) {
        return builder.requestFactory(() -> factory).build();
    }

    @Bean
    public RestTemplate forceJsonTemplate(RestTemplateBuilder builder, ClientHttpRequestFactory factory) {
        return builder.requestFactory(() -> factory)
                .interceptors((request, body, execution) -> {
                    ClientHttpResponse response = execution.execute(request, body);
                    // 网关错误返回：以 text/plain 返回 json 数据，强制改为 json，可能会造成解析错误
                    response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
                    return response;
                })
                .build();
    }
}
