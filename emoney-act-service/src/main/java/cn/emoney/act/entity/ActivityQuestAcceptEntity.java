package cn.emoney.act.entity;

import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.time.Instant;

/**
 * 用户参加活动任务记录表
 */
@Data
@Entity
@Table(name = "Act_quest_accept")
@EntityListeners(AuditingEntityListener.class)
public class ActivityQuestAcceptEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long uid;
    private Long questId;
    /**
     * 0:未完成, 1:已完成, -1: 取消
     */
    private Integer status = 0;

    @CreatedDate
    private Instant createTime;
    @Column(insertable = false)
    private Instant finishTime;
    @Column(insertable = false)
    private Instant cancelTime;
}
