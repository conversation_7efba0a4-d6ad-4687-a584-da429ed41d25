package cn.emoney.act.entity;

import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "Act_quest")
@EntityListeners(AuditingEntityListener.class)
public class ActivityQuestEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;

    private String type;
    private String content;
    private Integer status;

    private Date startTime;
    private Date endTime;

    @CreatedDate
    private Date createTime;
//    @LastModifiedDate
//    private Date updateTime;
}
