package cn.emoney.act.entity;

import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "Act_firstclassviewrecord_func")
@EntityListeners(AuditingEntityListener.class)
public class FirstClassViewRecordEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long uid;
    /**
     * 课表项ID
     */
    private Integer subId;
    /**
     * 课程ID
     */
    private Integer courseId;
    /**
     * 任务ID
     */
    private Long questId;
    /**
     * 任务接受ID
     */
    private Long acceptId;
    private String platform;

    @CreatedDate
    private Date createTime;
}