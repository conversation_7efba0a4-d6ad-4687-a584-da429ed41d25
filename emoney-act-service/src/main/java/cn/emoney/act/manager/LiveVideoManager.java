package cn.emoney.act.manager;

import cn.emoney.act.client.roboadvisor2.LiveVideoClient;
import cn.emoney.common.result.ResultInfo;
import cn.emoney.pojo.bo.CourseDetailDTO;
import cn.emoney.service.CourseService;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Service
public class LiveVideoManager implements CourseService {
    private final LiveVideoClient liveVideo;
    private final AsyncLoadingCache<Integer, Optional<CourseDetailDTO>> courseCache;

    public LiveVideoManager(LiveVideoClient liveVideo,
                            ThreadPoolTaskExecutor executor,
                            CacheManager cacheManager) {
        this.liveVideo = liveVideo;
        Cache cacheL2 = cacheManager.getCache("act:live-video:course-detail");
        this.courseCache = Caffeine.newBuilder()
                .maximumSize(1_000)
                .initialCapacity(128)
                .executor(executor)
                .expireAfterWrite(Duration.ofMinutes(10))
                .refreshAfterWrite(Duration.ofMinutes(5))
                .buildAsync(cacheL2 != null ?
                        (id) -> Optional.ofNullable(cacheL2.get(id, () -> courseDetail(id))) :
                        (id) -> Optional.ofNullable(courseDetail(id)));
    }

    @Override
    public CompletableFuture<Optional<CourseDetailDTO>> asyncDetail(Integer id) {
        return courseCache.get(id);
    }

    private CourseDetailDTO courseDetail(Integer id) {
        if (Objects.requireNonNull(id) <= 0) {
            return null;
        }
        ResultInfo<CourseDetailDTO> result = liveVideo.getCourseDetail(id);
        if (result.isSuccess()) {
            return result.getMessage();
        }
        // 上游返回错误, 可能是课程不存在
        if ("unexpected end of JSON input".equals(result.getRetMsg())) {
            return null;
        }
        throw new IllegalStateException("课程服务请求失败, courseId: " + id + ", message:" + result);
    }
}
