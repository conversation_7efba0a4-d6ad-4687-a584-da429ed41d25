package cn.emoney.act.manager;

import cn.emoney.act.client.pointprod.PointTaskClient;
import cn.emoney.act.client.pointprod.dto.PointTaskDetail;
import cn.emoney.common.result.PointResult;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PointTaskManager {
    private final PointTaskClient taskClient;
    private final AsyncLoadingCache<Long, Optional<PointTaskDetail>> cache;

    public PointTaskManager(PointTaskClient taskClient,
                            ThreadPoolTaskExecutor executor,
                            CacheManager cacheManager) {
        this.taskClient = taskClient;
        Cache cacheL2 = cacheManager.getCache("act:point-task:task-detail");
        this.cache = Caffeine.newBuilder()
                .maximumSize(1_000)
                .initialCapacity(128)
                .executor(executor)
                .expireAfterWrite(Duration.ofMinutes(10))
                .refreshAfterWrite(Duration.ofMinutes(5))
                .buildAsync(cacheL2 != null ?
                        (id) -> Optional.ofNullable(cacheL2.get(id, () -> taskClient.queryByTaskId(id).orElse(null))) :
                        taskClient::queryByTaskId);
    }

    public CompletableFuture<Optional<PointTaskDetail>> asyncDetail(Long id) {
        return cache.get(id);
    }

    public Map<Long, PointTaskDetail> taskDetailList(List<Long> ids) {
        Map<Long, CompletableFuture<Optional<PointTaskDetail>>> pending = new HashMap<>();

        @SuppressWarnings("unchecked")
        CompletableFuture<Optional<PointTaskDetail>>[] futures = ids.stream().distinct().map(id -> {
            CompletableFuture<Optional<PointTaskDetail>> future = this.cache.getIfPresent(id);
            if (future == null) {
                future = new CompletableFuture<>();
                pending.put(id, future);
                cache.put(id, future);
            }
            return future;
        }).toArray(CompletableFuture[]::new);

        if (!pending.isEmpty()) {
            try {
                PointResult<List<PointTaskDetail>> result = taskClient.queryAllByTaskIds(pending.keySet());
                if (result.isSuccess() && result.getData() != null) {
                    result.getData().forEach(task -> {
                        CompletableFuture<Optional<PointTaskDetail>> future = pending.remove(task.getTaskId());
                        if (future != null) {
                            future.complete(Optional.of(task));
                        }
                    });
                    pending.forEach((id, future) -> future.complete(Optional.empty()));
                } else {
                    log.error("积分任务批量查询失败: {}", result);
                    pending.forEach((id, future) -> future.completeExceptionally(new IllegalStateException("积分任务批量查询失败" + result.getMsg())));
                }

                CompletableFuture.allOf(futures).get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("积分任务批量查询出错 ids: {}", ids, e);
                pending.forEach((id, future) -> {
                    if (!future.isDone()) {
                        future.completeExceptionally(e);
                    }
                });
            }
        }


        return Arrays.stream(futures)
                .filter(CompletableFuture::isDone)
                .map(future -> future.getNow(Optional.empty()))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toMap(PointTaskDetail::getTaskId, Function.identity()));
    }
}
