emoney:
  webapi:
    instances:
      default:
        - uri: http://************
act:
  service:
    m-api:
      url-base: http://apitest.m.emoney.cn
    scm-order:
      list-order-prod: http://*************:8090/SCMAPI/Order/QueryOrderProdListByParams?jsonStr={0}
spring:
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true
logging:
  level:
    org.hibernate.type.descriptor.sql.BasicBinder: trace