spring:
  data:
    redis:
      repositories:
        enabled: false
  cache:
    type: redis
    redis:
      time-to-live: 5m
  kafka:
    producer:
      retries: 0
      batch-size: 16384
      buffer-memory: 33554432
      value-serializer: cn.emoney.act.support.kafka.serializer.JsonOrStringSerializer
      acks: 1
    consumer:
      group-id: act-group
      enable-auto-commit: false
      auto-offset-reset: latest
      max-poll-records: 50
      value-deserializer: cn.emoney.act.support.kafka.serializer.JsonOrStringDeserializer
      properties:
        spring.json.trusted.packages: cn.emoney*
    listener:
      concurrency: 4
      ack-mode: record
      missing-topics-fatal: false
act:
  service:
    auth:
      sso:
        url: http://gosso.emoney.cn/sso/getuserinfo