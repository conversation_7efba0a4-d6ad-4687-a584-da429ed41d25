--[[
在低版本 redis 实现 SET key value NX GET EXAT expireAt
Starting with Redis version 7.0.0: Allowed the NX and GET options to be used together.
--]]

-- 由于调用了时间函数，因此需要调用此函数，让 Redis 只复制写命令，避免主从不一致
redis.replicate_commands()

-- 获取当前时间
local now = redis.call('TIME')
local currentTime = tonumber(now[1]) * 1000 + math.ceil(tonumber(now[2]) / 1000)

local v = redis.call('GET', KEYS[1])
if v then
    return v
end

redis.call('SET', KEYS[1], ARGV[1])
local expireAt = currentTime + tonumber(ARGV[2])
redis.call('PEXPIREAT', KEYS[1], expireAt)
return nil
