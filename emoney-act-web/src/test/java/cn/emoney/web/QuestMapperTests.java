package cn.emoney.web;

import cn.emoney.act.quest.logic.QuestReward;
import cn.emoney.act.quest.logic.QuestTarget;
import cn.emoney.act.quest.logic.condition.CountCondition;
import cn.emoney.act.quest.logic.reward.PointReward;
import cn.emoney.act.quest.logic.reward.PrivilegeReward;
import cn.emoney.act.quest.logic.target.PidTarget;
import cn.emoney.act.quest.utils.QuestUtils;
import cn.emoney.pojo.bo.FirstClassQuestDefinitionDTO;
import cn.emoney.service.ActTaskConfService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest
public class QuestMapperTests {

    @Autowired
    ActTaskConfService confService;

    @Autowired
    ObjectMapper mapper;


    @Test
    void testByDb() {
        QuestReward reward = confService.getTaskConf("DSFresher20231124", QuestReward.class);
        System.out.println(reward);
    }

    @Test
    void testReward() throws JsonProcessingException {
        // obj
        String objJson = mapper.writeValueAsString(new PointReward(55555L));
        System.out.println(objJson);
        QuestReward reward = mapper.readValue(objJson, QuestReward.class);
        System.out.println(reward);

        // obj[]
        QuestReward rewards = QuestReward.allOf(Arrays.asList(
                new PointReward(123456L),
                new PointReward(789012L),
                QuestReward.oneOf(
                        new PointReward(345678L),
                        new PointReward(901234L)
                ),
                new PrivilegeReward("PAC88888").setTarget(new PidTarget(888L)),
                new PrivilegeReward("PAC88889").setTarget(new PidTarget(889L))
        ));
        String s = mapper.writeValueAsString(rewards);
        System.out.println(s);
        QuestReward rewards2 = mapper.readValue(s, QuestReward.class);
        System.out.println(rewards2);


        List<QuestReward> sss = QuestUtils.filterReward(rewards, target -> {
            if (target instanceof PidTarget) {
                return ((PidTarget) target).contains(888L);
            }
            return false;
        }).collect(Collectors.toList());

        System.out.println(sss);
    }

    @Test
    void questContentSerializer() throws JsonProcessingException {
        FirstClassQuestDefinitionDTO definition = new FirstClassQuestDefinitionDTO();
        definition.setReward(Arrays.asList(
                new PointReward(123456L),
                new PointReward(789012L),
                QuestReward.oneOf(
                        new PointReward(345678L),
                        new PointReward(901234L)
                ),
                new PrivilegeReward("PAC88888"),
                new PrivilegeReward("PAC88889")
        ));
        definition.setTarget(Arrays.asList(
                new PidTarget(Arrays.asList(123456L, 789012L)),
                QuestTarget.oneOf(
                        new PidTarget(Arrays.asList(345678L, 901234L)),
                        new PidTarget(Arrays.asList(567890L, 123456L))
                ),
                new PidTarget(Arrays.asList(789012L, 345678L))
        ));
        definition.setCondition(Arrays.asList(
                new CountCondition(555),
                new CountCondition(666)
        ));

        String string = mapper.writeValueAsString(definition);
        System.out.println(string);
        FirstClassQuestDefinitionDTO quest1 = mapper.readValue(string, FirstClassQuestDefinitionDTO.class);
        Assertions.assertEquals(definition, quest1);
    }

    @Test
    void questRewardTest() {
        FirstClassQuestDefinitionDTO definition = new FirstClassQuestDefinitionDTO()
                .setCondition(Collections.singletonList(new CountCondition(15)))
                .setReward(Collections.singletonList(QuestReward.oneOf(
//                        QuestReward.allOf(
//                                new PrivilegeReward("PAC1230921131915969").setTarget(new PidTarget(888020000L)),
//                                new PrivilegeReward("PAC1230921131909745").setTarget(new PidTarget(888080000L))
//                        ),
                        new PrivilegeReward("PAC1230921131915969").setTarget(new PidTarget(888020000L)),
                        new PrivilegeReward("PAC1230921131909745").setTarget(new PidTarget(888080000L))
                )));

        QuestUtils.filterReward(definition.getReward(), target -> {
                    if (target instanceof PidTarget) {
                        return ((PidTarget) target).contains(888080000L) || ((PidTarget) target).contains(888020000L);
                    }
                    return false;
                })
                .forEach(System.out::println);
    }
}
