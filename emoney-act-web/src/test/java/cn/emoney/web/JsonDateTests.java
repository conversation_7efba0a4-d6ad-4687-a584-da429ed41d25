package cn.emoney.web;

import cn.emoney.pojo.vo.ValidateUserVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

@SpringBootTest
public class JsonDateTests {
    private final ObjectMapper objectMapper;

    public JsonDateTests(@Autowired ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Test
    public void dateDeserializeTest() throws JsonProcessingException {
        // language=json
        ValidateUserVO vo = objectMapper.readValue("{\n" +
                                                   "  \"endDate\": \"2023/9/24 10:35:00\",\n" +
                                                   "  \"endDatel2sh\": \"2023/9/24 10:35:00\",\n" +
                                                   "  \"endDatel2sz\": \"2023/9/24 10:35:00\",\n" +
                                                   "  \"msg\": \"登录成功\"\n" +
                                                   "}", ValidateUserVO.class);
        Assertions.assertEquals(vo.getEndDate(),
                LocalDateTime.of(2023, 9, 24, 10, 35, 0),
                "日期反序列化错误"
        );
        Assertions.assertEquals(vo.getEndDateL2sh(),
                LocalDateTime.of(2023, 9, 24, 10, 35, 0),
                "日期反序列化错误"
        );
        Assertions.assertEquals(vo.getEndDateL2sz(),
                LocalDateTime.of(2023, 9, 24, 10, 35, 0),
                "日期反序列化错误"
        );
    }

}
