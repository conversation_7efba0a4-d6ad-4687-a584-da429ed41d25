package cn.emoney.web;

import cn.emoney.common.result.Result;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.QSKHInfoDO;
import cn.emoney.pojo.vo.AccountVO;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.service.*;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.codec.Base32;
import cn.hutool.core.codec.Base62;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.StrUtil;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import lombok.extern.slf4j.Slf4j;
import org.hashids.Hashids;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

@SpringBootTest
@Slf4j
class EmoneyActWebApplicationTests {

    @Autowired
    private UserService userService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private Lottery20240615Service lottery20240615Service;

    @Autowired
    private LotteryService lotteryService;

    @Autowired
    private PointService pointService;

    @Test
    void contextLoads() {
        //String uidStr = "";
        //uidStr = uidStr.replace("\n", "");
        //String[] accountArr = uidStr.split(",");
        //String key = RedisConstants.Redis_Pre_Activity + "QuestionnaireInfo:********";
        //for (String account : accountArr) {
        //    String isSend = (String) redisService.hashGet(key, account.trim());
        //    if (!StrUtil.isEmpty(isSend)) {
        //        continue;
        //    }
        //    //根据uid获取用户版本
        //    String pid = userService.GetAccountPID(account);
        //    if (pid.contains("88802") || pid.contains("88808")) {
        //        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        //        sendPrivilegeDTO.setAppId("A009");
        //        sendPrivilegeDTO.setActivityID(pid.startsWith("88802") ? "PAC1220328155102842" : "PAC1220328154950240");
        //        sendPrivilegeDTO.setReason("智盈大师问卷填写");
        //        sendPrivilegeDTO.setApplyUserID("scb_public");
        //        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        //        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();
        //        createActivityGrantApplyAccountDTO.setAccountType(1);
        //        createActivityGrantApplyAccountDTO.setAccount(account);
        //        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        //        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);
        //
        //        Boolean resultSenddPrivilege = logisticsService.sendPrivilege(sendPrivilegeDTO);
        //        if (resultSenddPrivilege) {
        //            redisService.hashSet(key, account.trim(), "1");
        //            log.info("赠送成功,em号:" + account);
        //        } else {
        //            log.info("赠送失败,em号:" + account);
        //        }
        //    }
        //}

    }

    @Test
    void Base64() {
        String encode = Base32.encode("0x2AA8DD932157F76926ABEE8DC61AF33F");
        System.out.println(encode);
    }

    /**
     * 可以自定义盐值，增加安全性
     */
    private static final String SALT = "custService";

    /**
     * 设置生成的ID长度
     */
    private static final int LENGTH = 16;
    @Test
    public void getUniqueID(){
        Hashids hashids = new Hashids(SALT + new Random(), LENGTH);
        String uniqueID = hashids.encode(1234567890L);
        System.out.println(uniqueID);
    }

    @Test
    public void testJob() {
        //lottery20240615Service.autoGetUserPackagePCount_tj();
        lottery20240615Service.autoGetUserPackagePCount_0615();
//        int allCount = ********;
//        int batchCount =400000;
//        int loopCount = allCount / batchCount;
//
//        // 处理可能的余数，确保最后一个批次正确计算
//        if (allCount % batchCount != 0) {
//            loopCount++;
//        }
//
//        // 从loopCount-1开始倒序遍历，直到0（包含）
//        for (int i = loopCount - 1; i >= 0; i--) {
//            int minNum = batchCount * i;
//            // 对于最后一个批次，确保maxNum不超过allCount
//            int maxNum = (i == loopCount - 1) ? allCount : batchCount * (i + 1) - 1;
//
//            System.out.println("minNum:" + minNum + ",maxNum:" + maxNum);
//        }
    }


    @Test
    public void testUserPeriod(){
        QSKHInfoDO.Status qkhInfoDO = new QSKHInfoDO.Status();
        qkhInfoDO.canParticipate = true;
        qkhInfoDO.canLottery = true;

        System.out.println(Result.buildSuccessResult(qkhInfoDO));
    }
}
