package cn.emoney.web.controller;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.RSAUtils;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.pojo.vo.UserLoginIdInfoVO;
import cn.emoney.service.LoginService;
import cn.emoney.service.MobileService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.security.PrivateKey;
import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @date 2021-12-16
 */
@Slf4j
@Controller
@RequestMapping("/login")
public class LoginController {

    @Autowired
    private MobileService mobileService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private UserService userService;

    private static String rediskey_sendnum = RedisConstants.Redis_Pre_Activity + "sendnumKey:";
    private static String rediskey_smscode = RedisConstants.Redis_Pre_Activity + "smscode:";
    private static String rediskey_loginerrnum = RedisConstants.Redis_Pre_Activity + "loginerrnum:";

    private Integer expirdTime_smscode = 60 * 20;
    private Integer expridTime_sendnum = 60 * 60 * 24;
    private Integer expridTime_loginerrnum = 60 * 60 * 24;

    @RequestMapping("/getPublicKey")
    @ResponseBody
    public String getPublicKey(){
        //return RSAUtils.getPublicKey();
        return loginService.getPublicKey();
    }

    @RequestMapping("/userSMSLogin")
    @ResponseBody
    public Result<String> UserSMSLogin(HttpServletRequest request, HttpServletResponse response) {
        String uname = request.getParameter("uname");
        String pass = request.getParameter("pass");
        String realName = request.getParameter("realname");
        String actCode = request.getParameter("actcode");
        String isCheckCode = request.getParameter("ischeckcode");//ischeckcode=0

        try {
            //uname = RSAUtils.decryptWithPrivate(uname);
            //pass = RSAUtils.decryptWithPrivate(pass);
            uname = loginService.decryptWithPrivateKey(uname);

            if (isCheckCode!=null && isCheckCode.equals("0")){
                //无验证码
                if (uname == null) {
                    return Result.buildErrorResult("-1", "uname解密失败");
                }
                //登录认证通过,登录态保存
                LoginUserInfoVO loginUserInfoVO = new LoginUserInfoVO();
                if(StringUtils.isNumeric(uname)){
                    String mobileX = mobileService.mobileEncrpt(uname);
                    loginUserInfoVO.setMobileX(mobileX);
                }else{
                    loginUserInfoVO.setAccount(uname);
                }

                loginUserInfoVO.setRealName(realName);
                //获取用户pid
                loginUserInfoVO.pid = userService.GetAccountPID(uname);

                loginUserInfoVO = loginService.SetLoginUserInfo(request, response, actCode, loginUserInfoVO);

                return Result.buildSuccessResult();

            }else{
                pass = loginService.decryptWithPrivateKey(pass);
                String loginerrnumkey = rediskey_loginerrnum + DateUtil.today() + uname;
                String smscodeKey = rediskey_smscode + uname;
                Integer loginerrnum = redisService.get(loginerrnumkey, Integer.class);

                if (loginerrnum == null) {
                    loginerrnum = 0;
                }
                if (loginerrnum > 10) {
                    return Result.buildErrorResult("-1", "验证码错误次数过多，请稍后重试");
                }

                String smscode = redisService.get(smscodeKey, String.class);
                if (smscode == null) {
                    return Result.buildErrorResult("-1", "您还没有获取验证码，请获取后登录。");
                }
                if (smscode.equals(pass)) {
                    //登录认证通过,登录态保存
                    LoginUserInfoVO loginUserInfoVO = new LoginUserInfoVO();
                    String mobileX = mobileService.mobileEncrpt(uname);
                    loginUserInfoVO.setMobileX(mobileX);
                    loginUserInfoVO.setRealName(realName);
                    //获取用户pid
                    loginUserInfoVO.pid = userService.GetAccountPID(uname);

                    loginService.SetLoginUserInfo(request, response, actCode, loginUserInfoVO);
                    return Result.buildSuccessResult();

                } else {
                    redisService.set(loginerrnumkey, loginerrnum + 1, expridTime_loginerrnum.longValue());
                    return Result.buildErrorResult("-1", "您的验证码有误，请重新输入。");
                }
            }
        } catch (Exception e) {
            return Result.buildErrorResult(e.getMessage());
        }
    }

    @RequestMapping("/sendMobileCode")
    @ResponseBody
    public Result<String> SendMobileCode(HttpServletRequest request) {
        String mobile = request.getParameter("mobile");
        if (mobile.isEmpty()) {
            return Result.buildErrorResult("-1","缺少mobile");
        }

        try {
            //String res = RSAUtils.decryptWithPrivate(mobile);
            String res = loginService.decryptWithPrivateKey(mobile);

            String sendnumKey = rediskey_sendnum + DateUtil.today() + res;
            String smscodeKey = rediskey_smscode + res;
            Integer sendnum = redisService.get(sendnumKey, Integer.class);

            if(sendnum==null){
                sendnum = 0;
            }
            if (sendnum > 10) {
                return Result.buildErrorResult("-1", "当日发送短信次数已达上限");
            }
            String code = RandomUtil.randomNumbers(4);
            //发送短信
            ApiGateWayResult<String> agr = mobileService.sendTextMessage(res, MessageFormat.format("您的验证码为{0}，请在20分钟内输入", code), "181");
            if (agr.getRetCode() == 0) {
                redisService.set(smscodeKey, code, expirdTime_smscode.longValue());
                redisService.set(sendnumKey, sendnum + 1, expridTime_sendnum.longValue());

                return Result.buildSuccessResult("发送成功");
            } else {
                return Result.buildErrorResult("-1","发送失败");
            }

        } catch (Exception e) {
            return Result.buildErrorResult(e.getMessage());
        }
    }



    /**
     * 获取publicKey  Jsonp
     * <AUTHOR>
     * @date 2022/3/17 14:56
     * @param request
     * @return java.lang.String
     */
    @RequestMapping("/getPublicKeyJsonp")
    @ResponseBody
    public String getPublicKeyJsonp(HttpServletRequest request){
        String jsonpCallback = request.getParameter("callback");

        //return jsonpCallback + "(\"" +RSAUtils.getPublicKey() + "\")";
        return  jsonpCallback + "(\"" +loginService.getPublicKey() + "\")";
    }

    /**
     * 登录 Jsonp
     * <AUTHOR>
     * @date 2022/3/17 14:56
     * @param request
     * @param response
     * @return java.lang.String
     */
    @RequestMapping("/userloginjsonp")
    @ResponseBody
    public String UserSMSLoginJsonp(HttpServletRequest request, HttpServletResponse response) {
        String uname = request.getParameter("uname");
        String pass = request.getParameter("pass");
        String realName = request.getParameter("realname");
        String actCode = request.getParameter("actcode");
        //密文手机+uid自动登录
        String phoneEncrypt = request.getParameter("phoneEncrypt");
        String UPcid = request.getParameter("UPcid");

        String isCheckCode = request.getParameter("ischeckcode");//ischeckcode=0

        String jsonpCallback = request.getParameter("callback");
        try {
            if (actCode == null || actCode.isEmpty()) {
                return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少actcode")) + ")";
            }
            //密文手机+uid自动登录
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                LoginUserInfoVO loginUser = new LoginUserInfoVO();
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "登录成功", loginUser)) + ")";
            }

            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                //SSO登录认证
                LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
                if (loginUser == null) {
                    SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                    if (ssoResult != null) {
                        loginUser = new LoginUserInfoVO();
                        String pid = request.getParameter("pid");
                        loginUser.setUid(ssoResult.getUid());
                        loginUser.setPid(pid);
                        loginUser.setAccount(ssoResult.getUserName());

                        loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);

                        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "登录成功", loginUser)) + ")";

                    } else {
                        return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "sso认证失败")) + ")";
                    }
                } else {
                    return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("-1", "登录失败", loginUser)) + ")";
                }
            } else if (isCheckCode!=null && isCheckCode.equals("0")){
                //无验证码
                uname = loginService.decryptWithPrivateKey(uname);
                if (uname == null) {
                    return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "uname解密失败")) + ")";
                }
                //登录认证通过,登录态保存
                LoginUserInfoVO loginUserInfoVO = new LoginUserInfoVO();
                String mobileX = mobileService.mobileEncrpt(uname);
                loginUserInfoVO.setMobileX(mobileX);
                loginUserInfoVO.setRealName(realName);
                //获取用户pid
                loginUserInfoVO.pid = userService.GetAccountPID(uname);

                loginUserInfoVO = loginService.SetLoginUserInfo(request, response, actCode, loginUserInfoVO);

                return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "登录成功", loginUserInfoVO)) + ")";

            }else{
                //手机号验证码登录认证
                if (uname == null || uname.isEmpty()) {
                    return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少uname")) + ")";
                }
                if (pass == null || pass.isEmpty()) {
                    return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少pass")) + ")";
                }


                //uname = RSAUtils.decryptWithPrivate(uname);
                uname = loginService.decryptWithPrivateKey(uname);
                //pass = RSAUtils.decryptWithPrivate(pass);
                if (uname == null) {
                    return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "uname解密失败")) + ")";
                }

                String loginerrnumkey = rediskey_loginerrnum + DateUtil.today() + uname;
                String smscodeKey = rediskey_smscode + uname;
                Integer loginerrnum = redisService.get(loginerrnumkey, Integer.class);

                if (loginerrnum == null) {
                    loginerrnum = 0;
                }
                if (loginerrnum > 10) {
                    return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "验证码错误次数过多，请稍后重试")) + ")";
                }

                String smscode = redisService.get(smscodeKey, String.class);
                if (smscode == null) {
                    return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "您还没有获取验证码，请获取后登录。")) + ")";
                }
                if (smscode.equals(pass)) {
                    //登录认证通过,登录态保存
                    LoginUserInfoVO loginUserInfoVO = new LoginUserInfoVO();
                    String mobileX = mobileService.mobileEncrpt(uname);
                    loginUserInfoVO.setMobileX(mobileX);
                    loginUserInfoVO.setRealName(realName);
                    //获取用户pid
                    loginUserInfoVO.pid = userService.GetAccountPID(uname);

                    loginUserInfoVO = loginService.SetLoginUserInfo(request, response, actCode, loginUserInfoVO);

                    return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "登录成功", loginUserInfoVO)) + ")";

                } else {
                    redisService.set(loginerrnumkey, loginerrnum + 1, expridTime_loginerrnum.longValue());
                    return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "您的验证码有误，请重新输入。")) + ")";
                }
            }
        } catch (Exception e) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1",e.getMessage()+"exception",null)) + ")";
        }
    }

    @RequestMapping("/getuserinfojsonp")
    @ResponseBody
    public String GetLoginUserInfoJsonp(HttpServletRequest request) {
        String actCode = request.getParameter("actcode");
        String jsonpCallback = request.getParameter("callback");
        if (actCode == null || actCode.isEmpty()) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少actcode")) + ")";
        }

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "获取登录信息成功", loginUser)) + ")";
    }

    /**
     * 获取验证码 Jsonp
     * <AUTHOR>
     * @date 2022/3/17 14:57
     * @param request
     * @return java.lang.String
     */
    @RequestMapping("/sendmobilecodejsonp")
    @ResponseBody
    public String SendMobileCodeJsonp(HttpServletRequest request) {
        String mobile = request.getParameter("mobile");
        String jsonpCallback = request.getParameter("callback");
        if (mobile == null || mobile.isEmpty()) {
            return  jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1","缺少mobile")) + ")";
        }

        try {
            //String  mobile = URLDecoder.decode(mobileStr);
            //String res = RSAUtils.decryptWithPrivate(mobile);
            String res = loginService.decryptWithPrivateKey(mobile);

            String sendnumKey = rediskey_sendnum + DateUtil.today() + res;
            String smscodeKey = rediskey_smscode + res;
            Integer sendnum = redisService.get(sendnumKey, Integer.class);

            if(sendnum==null){
                sendnum = 0;
            }
            if (sendnum > 10) {
                return  jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "当日发送短信次数已达上限")) + ")";
            }
            String code = RandomUtil.randomNumbers(4);
            //发送短信
            ApiGateWayResult<String> agr = mobileService.sendTextMessage(res, MessageFormat.format("您的验证码为{0}，请在20分钟内输入", code), "181");
            if (agr.getRetCode() == 0) {
                redisService.set(smscodeKey, code, expirdTime_smscode.longValue());
                redisService.set(sendnumKey, sendnum + 1, expridTime_sendnum.longValue());

                return  jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("发送成功")) + ")";
            } else {
                return  jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1","发送失败")) + ")";
            }

        } catch (Exception e) {
            return  jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult(e.getMessage())) +")";
        }
    }



}
