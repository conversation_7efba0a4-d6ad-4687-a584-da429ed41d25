package cn.emoney.web.controller;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.service.MobileService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import cn.emoney.web.interceptor.TakeCount;
import cn.hutool.core.codec.Base62;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.*;
import cn.hutool.http.useragent.UserAgentUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description UserLoginController
 * @createTime 2022年08月22日
 */
@Controller
@RequestMapping("/userlogin")
@Validated
@Slf4j
public class UserLoginController {

    private static String rediskey_sendnum = RedisConstants.Redis_Pre_Activity + "sendNumKey:";

    private static String rediskey_smscode = RedisConstants.Redis_Pre_Activity + "smsCode:";

    @Autowired
    private RedisService redisService;

    @Autowired
    private MobileService mobileService;

    @Autowired
    private UserService userService;

    @RequestMapping("/login")
    public String login(String callback, Model model, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String token = request.getParameter("token");
        if (StrUtil.isNotEmpty(token)) {
            SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
            if (ssoResult != null && StrUtil.isNotEmpty(ssoResult.getUid())) {
                List<BindAccountVO> bindAccountVOS = userService.GetBindAccountList(ssoResult.getUid());
                if (bindAccountVOS != null) {
                    if (bindAccountVOS.stream().anyMatch(c -> c.AccountType.equals(1))) {
                        BindAccountVO bindAccountVO = bindAccountVOS.stream().filter(c -> c.AccountType.equals(1)).findFirst().get();
                        String ticket = getTicketBySso(bindAccountVO.getEncryptMobile(), bindAccountVO.getAccountName());
                        callback = URLUtil.decode(callback) + (URLUtil.decode(callback).contains("?") ? "&" : "?") + "ticket=" + ticket;
                        response.sendRedirect(callback);
                    }
                }
            }
        }
        Long incrNum = redisService.incrBy("EMoney.Activity:incrNum");
        model.addAttribute("incrNum", incrNum);
        model.addAttribute("callback", URLUtil.decode(callback));
        String userAgent = request.getHeader("user-agent");
        if (UserAgentUtil.parse(userAgent).isMobile()) {
            return "userlogin/login_app";
        } else {
            return "userlogin/login_pc";
        }
    }


    @TakeCount()
    @PostMapping("/mobileLogin")
    @ResponseBody
    public Result<Object> mobileLogin(@NotNull(message = "手机号不能为空") String mobile,
                                      String jobNum,
                                      String keepNotExpire) {

        if (!StringUtils.isNumeric(mobile) || mobile.length() != 11) {
            return Result.buildSuccessResult("201", "请输入正确的手机号", null);
        }
        String ticket = getTicketLogin(mobile, jobNum, StrUtil.isEmpty(keepNotExpire) ? 30 * 60L : 7 * 24 * 60 * 60L);
        return Result.buildSuccessResult(ticket);
    }



    public String getTicketLogin(String mobile, String jobNum, Long expire) {
        String encryptMobile = mobileService.mobileEncrpt_Upper(mobile);
        String ticket = Base62.encode(encryptMobile);
        Map<String, Object> params = new HashMap<String, Object>(16);
        params.put("mobile", encryptMobile);
        params.put("mobileMask", DesensitizedUtil.mobilePhone(mobile));
        params.put("jobNum", jobNum);
        redisService.set("EMoney.Activity.loginTicket:" + ticket, JSON.toJSONString(params), expire);
        return ticket;
    }
    @RequestMapping("/sendSms")
    @ResponseBody
    public Result<Object> sendSms(@NotNull(message = "手机号不能为空") String mobile) {
        if (!StringUtils.isNumeric(mobile) || mobile.length() != 11) {
            return Result.buildSuccessResult("201", "请输入正确的手机号", null);
        }
        String sendNumKey = rediskey_sendnum + DateUtil.today() + mobile;
        String smsCodeKey = rediskey_smscode + mobile;
        int sendNum = Optional.ofNullable(redisService.get(sendNumKey, Integer.class)).orElse(0);
        if (sendNum > 10) {
            return Result.buildErrorResult("-1", "当日发送短信次数已达上限");
        }
        String code = RandomUtil.randomNumbers(4);
        //发送短信
        ApiGateWayResult<String> agr = mobileService.sendTextMessage(mobile, MessageFormat.format("您的验证码为{0}，请在20分钟内输入", code), "181");
        if (agr.getRetCode().equals(0)) {
            redisService.set(smsCodeKey, code, 20 * 60L);
            redisService.set(sendNumKey, sendNum + 1, 24 * 60 * 60L);
            return Result.buildSuccessResult("发送成功");
        } else {
            return Result.buildErrorResult("-1", "发送失败");
        }
    }

    /**
     * 查询ticket信息
     *
     * @param ticket
     * @return
     */
    @RequestMapping("/queryTicket")
    @ResponseBody
    public Result<Object> queryTicket(@NotNull(message = "ticket不能为空") String ticket) {
        String ticketInfo = redisService.get("EMoney.Activity.loginTicket:" + ticket, String.class);
        return Result.buildSuccessResult(ticketInfo);
    }

    /**
     * 短信登录
     *
     * @param mobile 手机号
     * @param code   验证码
     * @return Result
     */
    @PostMapping("/smsLogin")
    @ResponseBody
    public Result<Object> smsLogin(@NotNull(message = "手机号不能为空") String mobile,
                                   @NotNull(message = "验证码不能为空") String code,
                                   String jobNum,
                                   String keepNotExpire) {
        if (!StringUtils.isNumeric(mobile) || mobile.length() != 11) {
            return Result.buildSuccessResult("201", "请输入正确的手机号", null);
        }
        String smsCodeKey = rediskey_smscode + mobile;

        String smsCode = redisService.get(smsCodeKey, String.class);
        if (smsCode.equals(code)) {
            String ticket = getTicket(mobile, jobNum, StrUtil.isEmpty(keepNotExpire) ? 30 * 60L : 7 * 24 * 60 * 60L);
            return Result.buildSuccessResult(ticket);
        } else {
            return Result.buildErrorResult("-1", "您的验证码有误，请重新输入。");
        }
    }

    /**
     * 接收扫码消息
     *
     * @param eventid
     * @param action
     * @param mobile
     * @return
     */
    @RequestMapping("/recieveMessage")
    @ResponseBody
    public Result<Object> recieveMessage(@NotNull(message = "eventid不能为空") String eventid,
                                         @NotNull(message = "action不能为空") String action,
                                         @NotNull(message = "mobile不能为空") String mobile) {
        String ticket = "-1";
        if (StrUtil.isNotEmpty(mobile)) {
            ticket = getTicket(mobile, "", 24 * 60 * 60L);
        }
        String result = OkHttpUtil.get(MessageFormat.format("https://swsc.emoney.cn/pushmessage?uid={0}&group={1}&message={2}", eventid, action, ticket), null);
        log.info("扫码回调结果：" + result);
        return Result.buildSuccessResult();
    }

    /**
     * 返回ticket
     *
     * @param mobile
     * @return
     */
    public String getTicket(String mobile, String jobNum, Long expire) {
        String encryptMobile = mobileService.mobileEncrpt_Upper(mobile);
        String ticket = Base62.encode(encryptMobile);
        Map<String, Object> params = new HashMap<String, Object>(16);
        params.put("mobile", encryptMobile);
        params.put("mobileMask", DesensitizedUtil.mobilePhone(mobile));
        params.put("jobNum", jobNum);
        redisService.set("EMoney.Activity.loginTicket:" + ticket, JSON.toJSONString(params), expire);
        return ticket;
    }

    public String getTicketBySso(String encryptMobile, String mobileMask) {
        String ticket = Base62.encode(encryptMobile);
        Map<String, Object> params = new HashMap<String, Object>(16);
        params.put("mobile", encryptMobile);
        params.put("mobileMask", mobileMask);
        params.put("jobNum", "");
        redisService.set("EMoney.Activity.loginTicket:" + ticket, JSON.toJSONString(params), 30 * 60L);
        return ticket;
    }
}
