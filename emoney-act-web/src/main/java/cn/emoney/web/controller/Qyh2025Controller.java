package cn.emoney.web.controller;

import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LoginService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025-02-18
 */
@Controller
@RequestMapping("/qyh2025")
public class Qyh2025Controller extends BaseController {
    @Autowired
    private LoginService loginService;
    @RequestMapping("/index0225")
    public String index0225(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "qyh20250225";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qyh2025/20250225";
    }

    @RequestMapping("/index0312")
    public String index0312(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "qyh20250312";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qyh2025/20250312";
    }

    @RequestMapping("/index0318")
    public String index0318(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "qyh20250318";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qyh2025/20250318";
    }

    @RequestMapping("/index0409")
    public String index0409(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "qyh20250409";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qyh2025/20250409";
    }

    @RequestMapping("/index0415")
    public String index0415(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "qyh20250415";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qyh2025/20250415";
    }

    @RequestMapping("/index0527")
    public String index0527(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "qyh20250527";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qyh2025/20250527";
    }

    @RequestMapping("/index0606")
    public String index0606(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "qyh20250606";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qyh2025/20250606";
    }
    @RequestMapping("/index0624")
    public String index0624(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "qyh20250624";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qyh2025/20250624";
    }

    @RequestMapping("/index0707")
    public String index0707(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "tjqyh20250707";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qyh2025/20250707";
    }


    @RequestMapping("/index0715")
    public String index0715(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "qyh20250715";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qyh2025/20250715";
    }

    @RequestMapping("/index0725")
    public String index0725(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "qyh20250725";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qyh2025/20250725";
    }

}
