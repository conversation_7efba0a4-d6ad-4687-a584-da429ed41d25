package cn.emoney.web.controller;

import cn.emoney.common.enums.BaseResultCodeEnum;
import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.*;
import cn.emoney.pojo.vo.AccountVO;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LoginService;
import cn.emoney.service.MobileService;
import cn.emoney.service.QSKHService;
import cn.emoney.service.UserService;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ser.Serializers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.print.DocFlavor;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-06
 */
@Controller
@RequestMapping("/kh")
public class KHController extends BaseController {
    @Value("${GetEmAppClientUrl}")
    private String getEmAppClientUrl;

    @Autowired
    private MobileService mobileService;

    @Autowired
    private UserService userService;

    @Autowired
    private QSKHService qsKHService;

    @Autowired
    private LoginService loginService;

    @RequestMapping("/index")
    public String index(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "kh/index";
    }

    @RequestMapping("/getpermission")
    @ResponseBody
    @CrossOrigin
    public Result<String> GetPermission(HttpServletRequest request, HttpServletResponse response) {
        String token = request.getParameter("token");
        String clientType = request.getParameter("clienttype");
        String em = "";
        String mobileX = "";
        String uid = "";
        String source = "";

        if (token != null && !token.isEmpty()) {
            if (StringUtils.isEmpty(clientType)) {
                //app访问
                String res = OkHttpUtil.get(MessageFormat.format(getEmAppClientUrl, token), null);
                AppTokenDataDO appTokenDataDO = JsonUtil.toBean(res, AppTokenDataDO.class);
                if (appTokenDataDO != null) {
                    DetailData detailData = appTokenDataDO.getDetail();
                    if (detailData != null) {
                        List<LoginAccount> loginAccountList = detailData.logins;
                        if (loginAccountList != null && loginAccountList.stream().count() > 0) {

                            LoginAccount loginAccount = loginAccountList.stream().filter(h -> h.accountType == 2).findFirst().get();
                            if(loginAccount!=null){
                                em = loginAccount.key;
                                source = "app";
                            }
                        }
                    }
                }
            } else {
                //客户端访问
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    em = ssoResult.getUserName();
                    uid = ssoResult.getUid();
                }
            }
        }
        if(StringUtils.isEmpty(em)){
            return Result.buildErrorResult("您未登录，请登录后进入");
        }

        if(StringUtils.isNumeric(em)) {
            mobileX = mobileService.mobileEncrpt(em);
        }else {
            if(StringUtils.isEmpty(source)){
                //获取绑定手机号
                List<BindAccountVO> accountVOS = userService.GetBindAccountList(uid);
                if (accountVOS != null) {
                    BindAccountVO accountVO = accountVOS.stream().filter(h -> h.AccountType.equals(1)).findFirst().get();
                    if(accountVO!=null){
                        mobileX = accountVO.getEncryptMobile();
                    }
                }
            }else {
                mobileX = em;
            }
        }
        if(userService.checkPresent_QS(mobileX)){
            return Result.buildSuccessResult();
        }

        return Result.buildErrorResult("您账号没有权限，请开户后再来");
    }


    @RequestMapping("/refreshtestinfo")
    @ResponseBody
    public String refreshTestInfo(String uid,String actcode) {
        if (uid == null || uid.length() == 0) {
            return "缺少uid";
        }
        if (actcode == null || actcode.length() == 0) {
            return "缺少actcode";
        }
        List<Act588BenifitRecordDO> list = qsKHService.refreshMyLotteryList(actcode, uid);

        return JSON.toJSONString(list);
    }

    /**
     * 获取开户时间（空则表示未开户）
     * <AUTHOR>
     * @date 2024/4/11 14:04
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.String>d
     */
    @CrossOrigin
    @RequestMapping("/getuserstatus")
    @ResponseBody
    public Result<Object> GetUserStatus(HttpServletRequest request, HttpServletResponse response) {
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String mobile = request.getParameter("mobile");
        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult(BaseResultCodeEnum.NULL_ARGUMENT.getCode(), "缺少uid");
        }
        if (StringUtils.isEmpty(pid)) {
            return Result.buildErrorResult(BaseResultCodeEnum.NULL_ARGUMENT.getCode(), "缺少pid");
        }
        if (StringUtils.isEmpty(mobile)) {
            return Result.buildErrorResult(BaseResultCodeEnum.NULL_ARGUMENT.getCode(), "缺少mobile");
        }

        QSKHInfoDO.Status qkhInfoDO = qsKHService.getUserStatus(uid,pid,mobile);

        return Result.buildSuccessResult(qkhInfoDO);
    }

    /**
     * 抽奖
     * <AUTHOR>
     * @date 2024/4/11 13:45
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.Object>
     */
    @CrossOrigin
    @RequestMapping("/dolottery")
    @ResponseBody
    public Result<Object> doLottery(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String platform = request.getParameter("platform");
        String mobile = request.getParameter("mobile");
        if (StringUtils.isEmpty(mobile)) {
            return Result.buildErrorResult(BaseResultCodeEnum.NULL_ARGUMENT.getCode(),"缺少mobile");
        }
        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult(BaseResultCodeEnum.NULL_ARGUMENT.getCode(),"缺少uid");
        }
        if (StringUtils.isEmpty(pid)) {
            return Result.buildErrorResult(BaseResultCodeEnum.NULL_ARGUMENT.getCode(),"缺少pid");
        }
        if (StringUtils.isEmpty(actCode)) {
            return Result.buildErrorResult(BaseResultCodeEnum.NULL_ARGUMENT.getCode(),"缺少actCode");
        }
        if (StringUtils.isEmpty(platform)) {
            return Result.buildErrorResult(BaseResultCodeEnum.NULL_ARGUMENT.getCode(),"缺少platform");
        }
        QSKHInfoDO.Status qkhInfoDO = qsKHService.getUserStatus(uid,pid,mobile);

        //是否符合参与条件
        if(!qkhInfoDO.canParticipate){
            return Result.buildErrorResult("-1","不符合参与条件");
        }

        //是否符合抽奖条件
        if(!qkhInfoDO.canLottery){
            return Result.buildErrorResult("-2","不符合抽奖条件");
        }

        Result<Lottery0808PrizeDO> ret = qsKHService.doLottery(actCode, uid, pid, platform);
        return Result.buildSuccessResult(ret.getData());
    }

    /**
     * 获取中奖信息
     * <AUTHOR>
     * @date 2024/4/11 13:44
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.Object>
     */
    @CrossOrigin
    @RequestMapping("/getmylotteryinfo")
    @ResponseBody
    public Result<Object> getMyLotteryList(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");

        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult(BaseResultCodeEnum.NULL_ARGUMENT.getCode(),"缺少uid");
        }
        if (StringUtils.isEmpty(actCode)) {
            return Result.buildErrorResult(BaseResultCodeEnum.NULL_ARGUMENT.getCode(),"缺少actCode");
        }

        List<Act588BenifitRecordDO> list = qsKHService.getMyLotteryList(actCode,uid);
        return Result.buildSuccessResult(list);
    }



}
