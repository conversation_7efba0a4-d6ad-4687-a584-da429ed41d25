package cn.emoney.web.controller;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.mapper.activity.AugustdrawMapper;
import cn.emoney.mapper.activity.FirstClassViewRecordMapper;
import cn.emoney.pojo.AugustdrawDO;
import cn.emoney.pojo.bo.CmpLableDTO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.CMPService;
import cn.emoney.service.LoginService;
import cn.emoney.service.MobileService;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.MessageFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-04-12
 */
@Controller
@RequestMapping("/new2023")
public class New2023Controller extends BaseController  {
    @Autowired
    private MobileService mobileService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private LoginService loginService;

    @Autowired(required = false)
    private AugustdrawMapper augustdrawMapper;

    @Autowired
    private CMPService cmpService;

    private static String redisKey_sendNum = RedisConstants.Redis_Pre_Activity + "new2023:sendNumKey:";
    private static String redisKey_smsCode = RedisConstants.Redis_Pre_Activity + "new2023:smsCode:";
    private static String redisKey_IsHelped = RedisConstants.Redis_Pre_Activity + "new2023:isHelped";
    private static String redisKey_helpCode = RedisConstants.Redis_Pre_Activity + "new2023:helpCode";
    private static String PayPIDs = "888010000,888020000,888080000";
    private Integer expirdTime_smsCode = 60 * 20;
    private Integer expridTime_sendNum = 60 * 60 * 24;
    private Integer expridTime_isHelped = 60 * 60 * 24 * 30;

    @RequestMapping("/index0429")
    public String index_20230412(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "new20250701";
        String isHelp = "0";
        String helpCode = "1235";
        String pid = request.getParameter("pid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (PayPIDs.indexOf(loginUser.getPid()) > -1) {
                isHelp = "1";
            } else {
                String isHelpedKey = redisKey_IsHelped + actCode + loginUser.getMobileX();
                String helpCodeKey = redisKey_helpCode + actCode + loginUser.getMobileX();
                Object obj = redisService.get(isHelpedKey);
                if (obj != null && obj.toString().equals("1")) {
                    isHelp = "1";

                    Object redisCode = redisService.get(helpCodeKey);
                    if (redisCode != null) {
                        helpCode = redisCode.toString();
                    }
                } else {
                    Object redisCode = redisService.get(helpCodeKey);
                    if (redisCode == null) {
                        helpCode = RandomUtil.randomNumbers(4);
                        redisService.set(helpCodeKey, helpCode, expridTime_sendNum.longValue());
                    } else {
                        helpCode = redisCode.toString();
                    }
                }
            }
        }


        model.addAttribute("redisCode", helpCode);
        model.addAttribute("isHelp", isHelp);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        return "new2023/20230429";
    }

    @RequestMapping("/index0504")
    public String index_20230504(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "nw20250801";
        String isHelp = "0";
        String helpCode = "1235";
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    String pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (PayPIDs.indexOf(loginUser.getPid()) > -1) {
                isHelp = "1";
            } else {
                String isHelpedKey = redisKey_IsHelped + actCode + loginUser.getMobileX();
                String helpCodeKey = redisKey_helpCode + actCode + loginUser.getMobileX();
                Object obj = redisService.get(isHelpedKey);
                if (obj != null && obj.toString().equals("1")) {
                    isHelp = "1";

                    Object redisCode = redisService.get(helpCodeKey);
                    if (redisCode != null) {
                        helpCode = redisCode.toString();
                    }
                } else {
                    Object redisCode = redisService.get(helpCodeKey);
                    if (redisCode == null) {
                        helpCode = RandomUtil.randomNumbers(4);
                        redisService.set(helpCodeKey, helpCode, expridTime_sendNum.longValue());
                    } else {
                        helpCode = redisCode.toString();
                    }
                }
            }
        }

        model.addAttribute("redisCode", helpCode);
        model.addAttribute("isHelp", isHelp);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "new2023/20230504";
    }

    @RequestMapping("/index0520")
    public String index_20230520(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "new20250718";
        String isHelp = "0";
        String helpCode = "1235";
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    String pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (PayPIDs.indexOf(loginUser.getPid()) > -1) {
                isHelp = "1";
            } else {
                String isHelpedKey = redisKey_IsHelped + actCode + loginUser.getMobileX();
                String helpCodeKey = redisKey_helpCode + actCode + loginUser.getMobileX();
                Object obj = redisService.get(isHelpedKey);
                if (obj != null && obj.toString().equals("1")) {
                    isHelp = "1";

                    Object redisCode = redisService.get(helpCodeKey);
                    if (redisCode != null) {
                        helpCode = redisCode.toString();
                    }
                } else {
                    Object redisCode = redisService.get(helpCodeKey);
                    if (redisCode == null) {
                        helpCode = RandomUtil.randomNumbers(4);
                        redisService.set(helpCodeKey, helpCode, expridTime_sendNum.longValue());
                    } else {
                        helpCode = redisCode.toString();
                    }
                }
            }
        }

        model.addAttribute("redisCode", helpCode);
        model.addAttribute("isHelp", isHelp);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "new2023/20230520";
    }

    @RequestMapping("/index0516")
    public String index0516(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "new2025051602";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "new2023/20250516";
    }

    @RequestMapping("/index0531")
    public String index0531(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "new2025053102";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "new2023/20250531";
    }

    @RequestMapping("/index0619")
    public String index0619(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "new2025061902";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "new2023/20250619";
    }

    @RequestMapping("/index0701")
    public String index0701(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "new2025070102";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "new2023/20250701";
    }

    @RequestMapping("/index0718")
    public String index0718(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "new2025071802";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "new2023/20250718";
    }

    @RequestMapping("/index0801")
    public String index0801(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "new2025080102";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "new2023/20250801";
    }
    /**
     * 发短信
     * <AUTHOR>
     * @date 2023/4/12 14:05
     * @param request
     * @param response
     * @param model 
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/sendcode")
    @ResponseBody
    public Result<String> SendCode(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = request.getParameter("actCode");
        String groupPrc = request.getParameter("groupPrc");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }
        if(groupPrc == null){
            groupPrc = "188";
        }

        try {
            String mobile = loginUser.getMobileX();

            //用户是否已助力判断
            String isHelped = redisKey_IsHelped + actCode + mobile;
            Object obj = redisService.get(isHelped);
            if (obj != null && obj.toString().equals("1")) {
                return Result.buildErrorResult("-1", "用户已助力过");
            }

            String sendNumKey = redisKey_sendNum + DateUtil.today() + mobile;
            String smsCodeKey = redisKey_smsCode + actCode + mobile;
            String helpCodeKey = redisKey_helpCode + actCode + mobile;
            Integer sendNum = redisService.get(sendNumKey, Integer.class);

            if (sendNum == null) {
                sendNum = 0;
            }
            if (sendNum > 10) {
                return Result.buildErrorResult("-1", "当日发送短信次数已达上限");
            }
            Object redisCode = redisService.get(helpCodeKey);
            if (redisCode == null) {
                redisCode = RandomUtil.randomNumbers(4);
                redisService.set(helpCodeKey, redisCode, expridTime_sendNum.longValue());
            }
            //发送短信
            ApiGateWayResult<String> agr = mobileService.sendTextMessage(mobile, MessageFormat.format("尊敬的益盟用户你好，你的￥{1}智盈年版参团码为:{0}，请正确填写，并锁定你的参团资格！拒收请回复R", redisCode,groupPrc), "181");
            if (agr.getRetCode() == 0) {
                redisService.set(smsCodeKey, redisCode, expirdTime_smsCode.longValue());
                redisService.set(sendNumKey, sendNum + 1, expridTime_sendNum.longValue());

                return Result.buildSuccessResult("发送成功");
            } else {
                return Result.buildErrorResult("-1", "发送失败");
            }
        } catch (Exception e) {
            return Result.buildErrorResult(e.getMessage());
        }
    }

    /**
     * 确认助力
     * <AUTHOR>
     * @date 2023/4/12 14:16
     * @param request
     * @param response
     * @param model
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/conformhelp")
    @ResponseBody
    public Result<String> conformHelp(HttpServletRequest request, HttpServletResponse response, Model model) {
        String yzm = request.getParameter("yzm");
        String actCode = request.getParameter("actCode");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        if (yzm.isEmpty()) {
            return Result.buildErrorResult("-1", "缺少yzm");
        }
        try {
            String mobile = loginUser.getMobileX();

            //用户是否已助力判断
            String isHelped = redisKey_IsHelped + actCode + mobile;
            Object obj = redisService.get(isHelped);
            if (obj != null && obj.toString().equals("1")) {
                return Result.buildErrorResult("-1", "用户已助力过");
            }

            String smsCodeKey = redisKey_smsCode + actCode + mobile;
            String smsCode = redisService.get(smsCodeKey, String.class);
            if (smsCode == null) {
                return Result.buildErrorResult("-1", "您还没有获取验证码，请获取后登录。");
            }
            if (!smsCode.equals(yzm)) {
                return Result.buildErrorResult("-1", "验证码错误");
            }

            //助力过存入redis
            redisService.set(isHelped, "1", expridTime_isHelped.longValue());

            String remark = "";
            try {
                //推送cmp
                CmpLableDTO cmpLableDTO = new CmpLableDTO();
                cmpLableDTO.setAdcode("ACBanner20200318");
                cmpLableDTO.setLogtype("click");
                cmpLableDTO.setUname(mobile);
                String res = cmpService.pushMessageToCMPLabel(cmpLableDTO);
                remark = "打标签：" + res;
            } catch (Exception exp) {
                remark = "打标签失败";
            }

            //记录活动库
            AugustdrawDO augustdrawDO = new AugustdrawDO();
            augustdrawDO.setActivityid(actCode);
            augustdrawDO.setEmaccount(mobile);
            augustdrawDO.setAward(yzm);
            augustdrawDO.setRemark(remark);
            augustdrawDO.setIsgrant(0);
            augustdrawDO.setCreatetime(new Date());
            Integer ret = augustdrawMapper.insert(augustdrawDO);

        } catch (Exception e) {
            return Result.buildErrorResult(e.getMessage());
        }
        return Result.buildSuccessResult();
    }



}
