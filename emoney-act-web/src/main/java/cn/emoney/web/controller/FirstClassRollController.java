package cn.emoney.web.controller;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.bo.FirstClassCourseProgressDTO;
import cn.emoney.pojo.bo.FirstClassCourseRewardDTO;
import cn.emoney.pojo.bo.FirstClassDTO;
import cn.emoney.pojo.bo.FirstClassProgressDTO;
import cn.emoney.pojo.vo.FirstClassContentVO;
import cn.emoney.pojo.vo.FirstClassCourseVO;
import cn.emoney.service.FirstClassRollService;
import cn.emoney.service.VideoProgressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-03-21
 */
@Slf4j
@Validated
@CrossOrigin
@RestController
@RequestMapping("/first-class")
public class FirstClassRollController {
    private final FirstClassRollService service;
    private final VideoProgressService videoProgressService;

    public FirstClassRollController(FirstClassRollService service, VideoProgressService videoProgressService) {
        this.service = service;
        this.videoProgressService = videoProgressService;
    }

    @GetMapping("/getuserclasslist")
    public Result<List<FirstClassDTO>> courseListCompatible(@NotNull Long questId,
                                                            @NotNull Long uid) {
        List<FirstClassDTO> list = service.userCourseList(questId, uid).stream()
                .map(vo -> {
                    FirstClassDTO compatible = new FirstClassDTO();

                    compatible.setId(vo.getId());
                    compatible.setClassName(vo.getTitle());
                    compatible.setClassSummary(vo.getSummary());
                    compatible.setTeacherName(vo.getTeacherName());
                    compatible.setWeekComment(vo.getComment());
                    compatible.setClassUrl(vo.getVideoUrl());
                    compatible.setClassCover(vo.getVideoCover());
                    compatible.setAppCoverImg(vo.getVideoCoverApp());
                    compatible.setBeginTime(vo.getStartTime());
                    compatible.setEndTime(vo.getEndTime());
                    compatible.setParentID(vo.getParentId());
                    compatible.setLiveVideoID(String.valueOf(vo.getCourseId()));
                    compatible.setViewCount(vo.getViewCount());
                    compatible.setBookBaseAmount(vo.getBookBaseAmount());
                    compatible.setReplayBaseAmount(vo.getReplayBaseAmount());

                    if (vo.getExtraUrls() != null) {
                        vo.getExtraUrls().forEach(url -> {
                            switch (url.getType()) {
                                case 0:
                                    // 练习
                                    compatible.setVoteUrl1(url.getUrl());
                                    break;
                                case 1:
                                    compatible.setTipName("笔记");
                                    compatible.setVoteUrl(url.getUrl());
                                    break;
                                case 2:
                                    compatible.setTipName("报告");
                                    compatible.setVoteUrl(url.getUrl());
                                    break;
                                default:
                                    break;
                            }
                        });
                    }
                    if (vo.getPlayDone() != null) {
                        compatible.setDone(vo.getPlayDone());
                        if (!vo.getPlayDone()) {
                            videoProgressService.getVideoPlayTime(uid, vo.getVideoId())
                                    .ifPresent(compatible::setPlayTime);
                        }
                    }
                    return compatible;
                })
                .collect(Collectors.toList());
        return Result.buildSuccessResult(list);
    }

    @GetMapping("/content")
    public Result<FirstClassContentVO> content(@NotNull Long questId) {
        return service.findClassContent(questId)
                .map(dto -> {
                    FirstClassContentVO vo = new FirstClassContentVO();
                    vo.setId(dto.getId());
                    vo.setName(dto.getName());
                    Optional.ofNullable(dto.getColumns()).ifPresent(columns -> {
                        vo.setColumns(columns.stream().map(c -> {
                            FirstClassContentVO.ClassColumn column = new FirstClassContentVO.ClassColumn();
                            column.setId(c.getId());
                            column.setName(c.getName());
                            return column;
                        }).collect(Collectors.toList()));
                    });
                    vo.setMeta(dto.getMeta());
                    vo.setStartTime(dto.getStartTime());
                    vo.setEndTime(dto.getEndTime());
                    vo.setStatus(dto.getStatus());
                    vo.setValid(dto.isValid());
                    return vo;
                })
                .map(Result::buildSuccessResult)
                .orElseGet(() -> Result.buildErrorResult("-1", "找不到任务"));
    }

    @GetMapping("/courseList")
    public Result<List<FirstClassCourseVO>> courseList(@NotNull Long questId,
                                                       @NotNull Long uid) {
        List<FirstClassCourseVO> list = service.userCourseList(questId, uid);
        return Result.buildSuccessResult(list);
    }

    @GetMapping("/classProgress")
    public Result<FirstClassProgressDTO> classProgress(@NotNull Long questId,
                                                       @NotNull Long uid) {
        return service.userClassProgress(questId, uid)
                .map(Result::buildSuccessResult)
                .orElseGet(() -> Result.buildErrorResult("-1", "找不到任务"));
    }

    @PostMapping("/courseProgress")
    public Result<Boolean> checkClassCourseProgress(@NotNull Long questId,
                                                    @NotNull Long uid,
                                                    @NotNull Integer subId) {
        return Result.buildSuccessResult(service.checkClassCourse(questId, uid, subId, "pc"));
    }

    @GetMapping("/courseProgress")
    public Result<FirstClassCourseProgressDTO> courseProgress(@NotNull Long questId,
                                                              @NotNull Long uid,
                                                              @NotNull Integer subId) {
        return service.userCourseProgress(questId, uid, subId)
                .map(Result::buildSuccessResult)
                .orElseGet(() -> Result.buildErrorResult("-1", "找不到课程"));
    }

    @GetMapping("/courseReward")
    public Result<List<FirstClassCourseRewardDTO>> courseReward(@NotNull Long questId,
                                                                @NotNull Long uid,
                                                                @NotNull Integer subId) {
        return Result.buildSuccessResult(service.userCourseReward(questId, uid, subId));
    }


    @GetMapping("/courseRecord")
    public Set<Integer> getCourseRecord(@NotNull Long questId,
                                        @NotNull Long uid) {
        return service.userCourseState(questId, uid);
    }

    @GetMapping("/courseWatchTimes")
    public Result<Integer> courseWatchTimes(@NotNull Long questId, @NotNull Long uid) {
        Integer times = service.userCourseState(questId, uid).size();
        return Result.buildSuccessResult(times);
    }

    @GetMapping("/specialFunDays")
    public Result<Integer> specialFunDays(@NotNull Long uid) {
        Integer days = service.getSpecialFunDays(uid);
        return Result.buildSuccessResult(days);
    }
}
