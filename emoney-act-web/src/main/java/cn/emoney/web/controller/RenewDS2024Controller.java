package cn.emoney.web.controller;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.Lottery0808PrizeDO;
import cn.emoney.pojo.LotteryCountDO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LoginService;
import cn.emoney.service.Lottery20240306Service;
import cn.emoney.service.Lottery20240615Service;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.thymeleaf.util.DateUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2024-02-28
 */
@Controller
@RequestMapping("/renewds2024")
public class RenewDS2024Controller extends BaseController {
    @Autowired
    private LoginService loginService;

    @Autowired
    private Lottery20240306Service lottery20240306Service;
    @Autowired
    private Lottery20240615Service lottery20240615Service;

    @RequestMapping("/test_sendprize")
    @ResponseBody
    public Integer sendPrize(){
        return lottery20240615Service.getPrizesNum();
    }

    @RequestMapping("/refreshtestinfo")
    @ResponseBody
    public String refreshTestInfo(String uid,String actcode,String  mobileX) {
        if (uid == null || uid.length() == 0) {
            return "缺少uid";
        }
        if (actcode == null || actcode.length() == 0) {
            return "缺少actcode";
        }
        if (mobileX == null || mobileX.length() == 0) {
            return "缺少mobileX";
        }
        List<Act588BenifitRecordDO> list = lottery20240615Service.refreshMyLotteryList(actcode, uid);
        LotteryCountDO count = lottery20240615Service.refreshLotteryCount(actcode, uid,mobileX);

        return JSON.toJSONString(count) + "||" + JSON.toJSONString(list);
    }

    @RequestMapping("/index0306")
    public String index(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewds20240306";
        String pid = request.getParameter("pid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS2024/20240306";
    }

    @RequestMapping("/index0531")
    public String index0531(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewds20240531";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS2024/20240531";
    }

    @RequestMapping("/index0615")
    public String index0615(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewds20240615";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS2024/20240615";
    }

    @RequestMapping("/index0621")
    public String index0621(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewds20240615";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS2024/20240621";
    }

    @RequestMapping("/index0906")
    public String index0906(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewds20240906";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS2024/20240906";
    }

    @RequestMapping("/index1022")
    public String index1022(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewds20241022";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS2024/20241022";
    }

    @RequestMapping("/index1204")
    public String index1204(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewds20241204";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS2024/20241204";
    }

    @RequestMapping("/index1212")
    public String index1212(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewds20241212";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS2024/20241212";
    }


    @RequestMapping("/index1223")
    public String index1223(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewds20241223";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS2024/20241223";
    }

    @RequestMapping("/index1223new")
    public String index1223new(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewds20241223";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS2024/20241223new";
    }

    @RequestMapping("/index1225")
    public String index1225(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "classds";
        String pid = request.getParameter("pid");
        String date = request.getParameter("date");
        //获取系统时间 yyyyMMdd 格式
        String nowDate = DateUtils.format(new Date(), "yyyyMMdd", Locale.CHINA);

        if(!StringUtils.isEmpty(date)){
            nowDate = date;
        }
        actCode = actCode + nowDate;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }


        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("nowDate", nowDate);
        return "renewDS2024/20241225";
    }
    /**
     * 抽奖
     * <AUTHOR>
     * @date 2024/3/1 15:36
     * @param request
     * @param response
     * @return java.lang.String
     */
    @RequestMapping("/dolottery")
    @ResponseBody
    public String doLottery(HttpServletRequest request , HttpServletResponse response) {
        String jsonpCallback = request.getParameter("callback");
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String platform = request.getParameter("platform");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("用户未登录")) + ")";
        }

        if (loginUser.getUid() == null || loginUser.getUid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少uid")) + ")";
        }
        if (loginUser.getPid() == null || loginUser.getPid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少pid")) + ")";
        }
        if (actCode == null || actCode.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少actCode")) + ")";
        }
        if (platform == null || platform.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少platform")) + ")";
        }

        Result<Lottery0808PrizeDO> ret = new Result<Lottery0808PrizeDO>();
        if(actCode.equals("renewds20240615")){
            ret = lottery20240615Service.doLottery(actCode, uid, pid, platform);
        }else{
            ret = lottery20240306Service.doLottery(actCode, uid, pid, platform);
        }

        return jsonpCallback + "(" + JSON.toJSONString(ret) + ")";
    }

    /**
     * 获取可用抽奖次数
     * <AUTHOR>
     * @date 2024/3/1 15:35
     * @param request
     * @param response
     * @return java.lang.String
     */
    @RequestMapping("/getlotterycount")
    @ResponseBody
    public String getLotteryCount(HttpServletRequest request , HttpServletResponse response) {
        String jsonpCallback = request.getParameter("callback");
        String actCode = request.getParameter("actcode");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("用户未登录")) + ")";
        }

        if (loginUser.getUid() == null || loginUser.getUid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少uid")) + ")";
        }
        if (actCode == null || actCode.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少actCode")) + ")";
        }
        LotteryCountDO ret = new LotteryCountDO();
        if(actCode.equals("renewds20240615")){
            ret = lottery20240615Service.getLotteryCount(actCode, loginUser.getUid(), loginUser.getMobileX());
        }else{
            ret = lottery20240306Service.getLotteryCount(actCode, loginUser.getUid(), loginUser.getMobileX());
        }
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult(ret)) + ")";
    }

    /**
     * 获取我的中将记录
     * <AUTHOR>
     * @date 2024/3/1 15:33
     * @param request
     * @param response
     * @return java.lang.String
     */
    @RequestMapping("/getmylotteryinfo")
    @ResponseBody
    public String getMyLotteryList(HttpServletRequest request , HttpServletResponse response) {
        String jsonpCallback = request.getParameter("callback");
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("用户未登录")) + ")";
        }

        if (loginUser.getUid() == null || loginUser.getUid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少uid")) + ")";
        }
        if (actCode == null || actCode.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少actCode")) + ")";
        }

        List<Act588BenifitRecordDO> list =new ArrayList<>();
        if(actCode.equals("renewds20240615")){
            list = lottery20240615Service.getMyLotteryList(actCode,uid);
        }else{
            list = lottery20240306Service.getMyLotteryList(actCode,uid);
        }
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult(list)) + ")";
    }

}
