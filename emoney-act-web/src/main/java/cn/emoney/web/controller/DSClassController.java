package cn.emoney.web.controller;

import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LoginService;
import cn.emoney.service.redis.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022-01-19
 */
@Controller
@RequestMapping("/dsclass")
public class DSClassController {
    @Autowired
    private RedisService redisService;

    @Autowired
    private LoginService loginService;

    private String actCode = "20220119_dsclass";

    @RequestMapping
    public String index(HttpServletRequest request, HttpServletResponse response, Model model) {
        String pid = "";

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
            SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
            if (ssoResult != null) {
                pid = request.getParameter("pid");
                loginUser.setUid(ssoResult.getUid());
                loginUser.setPid(pid);
                loginUser.setAccount(ssoResult.getUserName());

                loginService.SetLoginUserInfo(request, response, actCode, loginUser);

                loginUser = loginService.GetLoginUserInfo(request, actCode);
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode",actCode);
        return "dsclass/index";
    }


}
