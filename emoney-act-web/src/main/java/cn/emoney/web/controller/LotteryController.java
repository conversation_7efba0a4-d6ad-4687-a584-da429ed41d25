package cn.emoney.web.controller;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.pojo.vo.LotteryInfoVO;
import cn.emoney.service.LoginService;
import cn.emoney.service.LotteryService;
import cn.emoney.service.MobileService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Controller
@RequestMapping("/lottery")
public class LotteryController extends BaseController{

    @Autowired
    private LotteryService lotteryService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private UserService userService;

    @Autowired
    private MobileService mobileService;

    @Autowired
    private RedisService redisService;

    private final String actCode20221201 = "20221201";


    private static String rediskey_sendnum = RedisConstants.Redis_Pre_Activity + "sendnumKey:";
    private static String rediskey_smscode = RedisConstants.Redis_Pre_Activity + "smscode:";
    private static String rediskey_loginerrnum = RedisConstants.Redis_Pre_Activity + "loginerrnum:";

    private Integer expirdTime_smscode = 60 * 20;
    private Integer expridTime_sendnum = 60 * 60 * 24;
    private Integer expridTime_loginerrnum = 60 * 60 * 24;

    @GetMapping("/index")
    public String index(HttpServletRequest request, HttpServletResponse response, Model model){
        String pid = "";
         LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode20221201);
         if (loginUser == null) {
              if(!StringUtils.isBlank(request.getQueryString())){
                loginUser = new LoginUserInfoVO();
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());
                    loginService.SetLoginUserInfo(request, response, actCode20221201, loginUser);
                    //loginUser = loginService.GetLoginUserInfo(request, actCode20221201);
                }
            }
        }
         String hasPrivilege = "0";
         if(loginUser!=null){
             Result<String> result = lotteryService.getPrivilege1201(loginUser.getUid(), loginUser.getPid());
             if(result.getData().equals("1")){
                 hasPrivilege = "1";
             }
         }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser == null ? "0" : "1");
        model.addAttribute("hasPrivilege",hasPrivilege);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("actcode",actCode20221201);
        return "lottery/index";
    }

    /**
     * 获取用户是否有抽奖权限
     * @param request
     * @return
     */
    @GetMapping("/privilege1201")
    @ResponseBody
    public String privilege1201(HttpServletRequest request) {
        Result<String> result = null;
        String jsonpCallback = "";
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        jsonpCallback = request.getParameter("callback");

        if (StringUtils.isBlank(uid)) {
            result = Result.buildErrorResult("uid信息为空");
            return responseResult(JSON.toJSONString(result), jsonpCallback);
        }

        if (StringUtils.isBlank(pid)) {
            result = Result.buildErrorResult("pid信息为空");
            return responseResult(JSON.toJSONString(result), jsonpCallback);
        }

        result = lotteryService.getPrivilege1201(uid, pid);
        return responseResult(JSON.toJSONString(result), jsonpCallback);
    }

    /**
     * 活动抽奖
     * @param request
     * @return
     */
    @GetMapping("/doLottery1201")
    @ResponseBody
    public String doLottery1201(HttpServletRequest request) {
        Result<LotteryInfoVO> result = null;
        String jsonpCallback = "";
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        jsonpCallback = request.getParameter("callback");

        if (StringUtils.isBlank(uid)) {
            result = Result.buildErrorResult("uid信息为空");
            return responseResult(JSON.toJSONString(result), jsonpCallback);
        }

        if (StringUtils.isBlank(pid)) {
            result = Result.buildErrorResult("pid信息为空");
            return responseResult(JSON.toJSONString(result), jsonpCallback);
        }

        result = lotteryService.doLottery1201(uid, pid);
        return responseResult(JSON.toJSONString(result), jsonpCallback);
    }

    /**
     * 获取抽奖记录
     * @param request
     * @return
     */
    @GetMapping("/getLotteryInfo1201")
    @ResponseBody
    public String getLotteryInfo1201(HttpServletRequest request) {
        Result<LotteryInfoVO> result = null;
        String jsonpCallback = "";
        String uid = request.getParameter("uid");
        jsonpCallback = request.getParameter("callback");

        if (StringUtils.isBlank(uid)) {
            result = Result.buildErrorResult("uid信息为空");
            return responseResult(JSON.toJSONString(result), jsonpCallback);
        }

        result = lotteryService.getLotteryInfo1201(uid);
        return responseResult(JSON.toJSONString(result), jsonpCallback);
    }
}
