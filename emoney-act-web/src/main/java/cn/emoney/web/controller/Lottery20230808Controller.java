package cn.emoney.web.controller;

import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.Lottery0808PrizeDO;
import cn.emoney.pojo.bo.Benefit588DTO;
import cn.emoney.pojo.bo.QueryCouponListDTO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LoginService;
import cn.emoney.service.Lottery20230808Service;
import cn.emoney.service.UserService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-28
 */
@Controller
@RequestMapping("/lottery0808")
public class Lottery20230808Controller extends BaseController {
    @Autowired
    private Lottery20230808Service lottery20230808Service;

    @Autowired
    private UserService userService;

    @Autowired
    private LoginService loginService;

    @RequestMapping("/index")
    public String index(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renew20230808";
        String pid = request.getParameter("pid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "lottery0808/index";
    }

    @RequestMapping("/index103101")
    public String index103101(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renew20231031";
        String pid = request.getParameter("pid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "lottery0808/2023103101";
    }

    @RequestMapping("/index103102")
    public String index103102(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renew20231031";
        String pid = request.getParameter("pid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "lottery0808/2023103102";
    }

    @RequestMapping("/refreshtestinfo")
    @ResponseBody
    public String refreshTestInfo(String uid,String actcode) {
        if (uid == null || uid.length() == 0) {
            return "缺少uid";
        }
        if (actcode == null || actcode.length() == 0) {
            return "缺少actcode";
        }
        List<Act588BenifitRecordDO> list = lottery20230808Service.refreshMyLottery0808List(actcode, uid);
        Integer count = lottery20230808Service.refreshLotteryCountByDay(actcode, uid);

        return count + "||" + JSON.toJSONString(list);
    }

    @RequestMapping("/sendprize")
    @ResponseBody
    public Integer sendPrize(){
        return lottery20230808Service.getPrizesNum();
    }
    /**
     * 抽奖
     * <AUTHOR>
     * @date 2023/7/28 9:41
     * @param request
     * @param response 
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.Lottery0808PrizeDO>
     */
    @RequestMapping("/dolottery")
    @ResponseBody
    public String doLottery0808(HttpServletRequest request , HttpServletResponse response) {
        String jsonpCallback = request.getParameter("callback");
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String platform = request.getParameter("platform");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("用户未登录")) + ")";
        }

        if (loginUser.getUid() == null || loginUser.getUid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少uid")) + ")";
        }
        if (loginUser.getPid() == null || loginUser.getPid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少pid")) + ")";
        }
        if (actCode == null || actCode.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少actCode")) + ")";
        }
        if (platform == null || platform.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少platform")) + ")";
        }

        Result<Lottery0808PrizeDO> ret = lottery20230808Service.doLottery0808(actCode, uid, pid, platform);
        return jsonpCallback + "(" + JSON.toJSONString(ret) + ")";
    }

    /**
     * 赠送88积分+100优惠券
     * <AUTHOR>
     * @date 2023/7/28 9:45
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/sendpp")
    @ResponseBody
    public String sendPP0808(HttpServletRequest request , HttpServletResponse response) {
        String jsonpCallback = request.getParameter("callback");
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String type = request.getParameter("type");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("用户未登录")) + ")";
        }

        if (loginUser.getUid() == null || loginUser.getUid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少uid")) + ")";
        }
        if (actCode == null || actCode.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少actCode")) + ")";
        }
        if (loginUser.getPid() == null || loginUser.getPid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少pid")) + ")";
        }
        Result<String> ret = lottery20230808Service.sendPP0808(actCode,uid,pid,type);

        return jsonpCallback + "(" + JSON.toJSONString(ret) + ")";
    }

    /**
     * 获取当天是否弹出过窗口
     * <AUTHOR>
     * @date 2023/7/28 9:48
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/gettipstatusbyday")
    @ResponseBody
    public String getTipStatusByDay(HttpServletRequest request , HttpServletResponse response) {
        String jsonpCallback = request.getParameter("callback");
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String type = request.getParameter("type");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("用户未登录")) + ")";
        }

        if (loginUser.getUid() == null || loginUser.getUid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少uid")) + ")";
        }
        if (actCode == null || actCode.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少actCode")) + ")";
        }

        boolean ret = lottery20230808Service.getTipStatusByDay(uid, type);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult(String.valueOf(ret))) + ")";
    }

    /**
     * 获取当天抽奖次数
     * <AUTHOR>
     * @date 2023/7/28 9:53
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/getlotterycountbyday")
    @ResponseBody
    public String getLotteryCountByDay(HttpServletRequest request , HttpServletResponse response) {
        String jsonpCallback = request.getParameter("callback");
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("用户未登录")) + ")";
        }

        if (loginUser.getUid() == null || loginUser.getUid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少uid")) + ")";
        }
        if (actCode == null || actCode.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少actCode")) + ")";
        }

        Integer ret = lottery20230808Service.getLotteryCountByDay(actCode,uid);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult(String.valueOf(ret))) + ")";
    }

    /**
     * 获取中奖记录
     * <AUTHOR>
     * @date 2023/7/28 9:53
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>>
     */
    @RequestMapping("/getmylotteryinfo")
    @ResponseBody
    public String getMyLottery0808List(HttpServletRequest request , HttpServletResponse response) {
        String jsonpCallback = request.getParameter("callback");
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("用户未登录")) + ")";
        }

        if (loginUser.getUid() == null || loginUser.getUid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少uid")) + ")";
        }
        if (actCode == null || actCode.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少actCode")) + ")";
        }

        List<Act588BenifitRecordDO> list = lottery20230808Service.getMyLottery0808List(actCode,uid);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult(list)) + ")";
    }
}
