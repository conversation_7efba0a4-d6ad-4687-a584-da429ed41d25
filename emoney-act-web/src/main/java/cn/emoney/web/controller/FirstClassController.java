package cn.emoney.web.controller;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.Result;
import cn.emoney.pojo.WjxSurveyResultDO;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.FirstClassDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.AccountVO;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.service.FirstClassService;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.UserService;
import cn.emoney.service.kafka.producer.ProducerService;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-03-17
 */

@Controller
@RequestMapping("/firstclass")
@Slf4j
public class FirstClassController extends BaseController {

    @Autowired
    private FirstClassService firstClassService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ProducerService producerService;

    @Autowired
    private UserService userService;


    @Autowired
    private LogisticsService logisticsService;
    private String rediskey_issend= RedisConstants.Redis_Pre_Activity + "firstclass:issend:";

    private String rediskey_userdirection = RedisConstants.Redis_Pre_Activity + "firstclass:userdirection:";

    @RequestMapping("/test_handlemsg")
    @ResponseBody
    public String test_handlemsg(String uid,String message){
        String rediskey = rediskey_issend;
        String activityID = "";
        if(message.indexOf("_")>0){
            uid = message.split("_")[0];
            activityID = message.split("_")[1];
        }

        if(!StringUtils.isEmpty(activityID)) {
            if (Integer.parseInt(activityID) > 2) {
                rediskey = rediskey + activityID + ":";
            }
        }

        String isSend = (String) redisService.hashGet(rediskey, uid.trim());
        String account = "";
        if (!StrUtil.isEmpty(isSend)) {
            return "已赠送";
        }
        List<BindAccountVO> bindAccountVOS = userService.GetBindAccountList(uid);
        if (bindAccountVOS != null) {
            account = bindAccountVOS.stream().filter(x -> x.AccountType.equals(0)).findFirst().get().AccountName;
            List<AccountVO> accountVOS = userService.queryAccountListByAccount(account);
            if (accountVOS != null) {
                account = accountVOS.stream().filter(h -> h.getPid().toString().contains("88808") || h.getPid().toString().contains("88802")).findFirst().get().getUsername();
            }
        }
        //根据uid获取用户版本
        String pid = userService.GetAccountPID(account);
        if (pid!=null && (pid.contains("88802") || pid.contains("88808"))) {
            SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
            sendPrivilegeDTO.setAppId("A009");
            sendPrivilegeDTO.setActivityID(pid.startsWith("88802") ? "PAC1220328155102842" : "PAC1220328154950240");
            sendPrivilegeDTO.setReason("大师第一课完课赠送15天");
            sendPrivilegeDTO.setApplyUserID("scb_public");
            List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
            CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();
            createActivityGrantApplyAccountDTO.setAccountType(1);
            createActivityGrantApplyAccountDTO.setAccount(account);
            createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
            sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

            Boolean resultSendPrivilege = logisticsService.sendPrivilege(sendPrivilegeDTO);
            if (resultSendPrivilege) {
                redisService.hashSet(rediskey, uid.trim(), "1");
                return "成功";
                //log.info("大师第一课完课赠送15天使用期成功,uid:" + uid);
            } else {
                return "失败";
                //log.info("大师第一课完课赠送15天使用期失败,uid:" + uid);
            }
        }
        return "未命中";
    }
    @RequestMapping("/test_pushmsg")
    @ResponseBody
    public String test_pushmessage(String uid){
        producerService.sendMessage("firstClassUserComing",uid);
        return "1";
    }

    /**
     * 获取特定功能使用天数
     * <AUTHOR>
     * @date 2023/2/14 11:34
     * @param request
     * @return java.lang.String
     */
    @RequestMapping("/getspecialfundays")
    @ResponseBody
    public String GetSpecialFunDays(HttpServletRequest request){
        String jsonpCallback = request.getParameter("callback");
        String uid = request.getParameter("uid");


        Integer days = firstClassService.getSpecialFunDays(uid);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "成功", days)) + ")";
    }

    /**
     * 获取课程列表
     *
     * @param request
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/3/18 13:27
     */
    @RequestMapping("/getuserclasslist")
    @ResponseBody
    public String GetUserClassList(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String uid = request.getParameter("uid");
        String activityID = request.getParameter("activityID");
        String classID = request.getParameter("classID");

        List<FirstClassDTO> list = firstClassService.getUserClassList(uid,activityID,classID);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "成功", list)) + ")";
    }

    /**
     * 学习方向查询
     *
     * @param request
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/3/22 15:03
     */
    @RequestMapping("/getuserstudydirection")
    @ResponseBody
    public String GetUserStudyDirection(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String uid = request.getParameter("uid");
        if (uid == null || uid.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少uid")) + ")";
        }
        String val = (String) redisService.hashGet(rediskey_userdirection, uid);
        if (val ==null || val.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("-1", "无数据",val)) + ")";
        } else {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "成功", val)) + ")";
        }
    }

    /**
     * 学习方向保存
     *
     * @param request
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/3/22 15:03
     */
    @RequestMapping("/setuserstudydirection")
    @ResponseBody
    public String SetUserStudyDirection(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String uid = request.getParameter("uid");
        String val = request.getParameter("type");
        if (uid == null || uid.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少uid")) + ")";
        }
        if (val == null || val.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少type")) + ")";
        }
        redisService.hashSet(rediskey_userdirection, uid, val);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200","成功",null)) + ")";
    }

    @RequestMapping("/adduserclassrecord")
    @ResponseBody
    public String AddUserClassRecord(HttpServletRequest request) {

        String jsonpCallback = request.getParameter("callback");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String classid = request.getParameter("classid");
        String platform = request.getParameter("platform");
        String activityID = request.getParameter("activityID");

        if (uid == null || uid.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少uid")) + ")";
        }
        if (pid == null || pid.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少pid")) + ")";
        }
        if (classid == null || classid.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少classid")) + ")";
        }
        if (platform == null || platform.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少platform")) + ")";
        }
        boolean hasRecord = firstClassService.HasClassRecord(uid, classid,activityID);
        if (hasRecord) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "已有观看记录")) + ")";
        } else {
            //观看记录保存
            firstClassService.SetClassRecord(uid, pid, classid, platform,activityID);

            return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "成功", "")) + ")";
        }
    }

    /**
     *
     * <AUTHOR>
     * @date 2023/1/9 13:38
     * @param request activityID：PAC1230105105503537-春节问卷调查送15天【智盈大师掘金版】,PAC1230105105823388-春节问卷调查送15天【智盈大师深度资金版】
     * @return java.lang.String
     */
    @RequestMapping("/getprivilege")
    @ResponseBody
    public String SendPrivilege(HttpServletRequest request){
        String content = getPostData(request);
        if(StringUtils.isEmpty(content)){
            return JSON.toJSONString(Result.buildErrorResult("-1","参数缺失",null));
        }
        String uid = "";
        String pid = "";
        String scene = "";
        String actcode = "firstclass20230116";

        log.info("大师第一课完成问卷回调：" + content);
        WjxSurveyResultDO obj = JSON.parseObject(content, WjxSurveyResultDO.class);
        if(obj != null) {
            uid = obj.getEmuid().toString();
            pid = obj.getPid();
        }else{
            return JSON.toJSONString(Result.buildErrorResult("-1","参数反序列化失败",null));
        }

        String isSubmit = userService.IsSubmitByActCodes(actcode,uid);
        if(isSubmit.equals("1,")){
            return JSON.toJSONString(Result.buildErrorResult("-1","已领取过使用期，请勿重复领取",null));
        }

        String account = "";
        String mobileX = "";

        //根据uid获取绑定的em账号和手机号
        if (!uid.isEmpty()) {
            List<BindAccountVO> list = userService.GetBindAccountList(uid);
            if (list != null) {
                for (BindAccountVO item : list) {
                    if (item.AccountType.equals(0)) {

                        //过滤非大师账号
                        String username = item.AccountName;
                        String checkPid = userService.GetAccountPID(username);
                        if(checkPid.equals(pid)){
                            account = item.AccountName;
                        }
                    }
                    if (item.AccountType.equals(1)) {
                        mobileX = item.EncryptMobile;
                    }
                }
            }
        }
        if(StringUtils.isEmpty(account)){
            log.info("大师第一课完成问卷回调[查询绑定em号失败]：" + content);
            return JSON.toJSONString(Result.buildErrorResult("-1","参数缺失",null));
        }

        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(pid.startsWith("88802") ? "PAC1230105105823388" : "PAC1230105105503537");
        sendPrivilegeDTO.setReason("大师第一课随堂测");
        sendPrivilegeDTO.setApplyUserID("scb_public");
        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();

        createActivityGrantApplyAccountDTO.setAccountType(1);
        createActivityGrantApplyAccountDTO.setAccount(account);
        createActivityGrantApplyAccountDTO.setMID(mobileX);
        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        Boolean resultSendPrivilege = logisticsService.sendPrivilege(sendPrivilegeDTO);

        if(resultSendPrivilege){
            //已领取过记录领取状态
            userService.AddCountByActCode(actcode,uid,"1");
        }else{
            return JSON.toJSONString(Result.buildErrorResult("-1","领取使用期失败",null));
        }
        return JSON.toJSONString(Result.buildSuccessResult("200","成功",null));
    }



}
