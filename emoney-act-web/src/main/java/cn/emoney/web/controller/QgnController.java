package cn.emoney.web.controller;

import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.AppTokenDataDO;
import cn.emoney.pojo.DetailData;
import cn.emoney.pojo.LoginAccount;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LoginService;
import cn.emoney.service.MobileService;
import cn.emoney.service.UserService;
import cn.emoney.service.impl.BenefitDSImpl;
import cn.emoney.service.kafka.producer.ProducerService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-09-25
 */
@Controller
@RequestMapping("/qgn")
public class QgnController extends BaseController {
    @Autowired
    private LoginService loginService;
    @Autowired
    private UserService userService;
    @Autowired
    private MobileService mobileService;
    @Autowired
    private ProducerService producerService;
    @Autowired
    private BenefitDSImpl benefitDSService;

    @Value("${GetEmAppClientUrl}")
    private String getEmAppClientUrl;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping("/20231011")
    public String index(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20231011";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20231011";
    }

    @RequestMapping("/20231109")
    public String index1109(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20231109";
        String pid = request.getParameter("pid");
        String clientType = request.getParameter("clienttype");
        String token = request.getParameter("token");
        String isSubmit = "";
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            if (StringUtils.isEmpty(clientType)) {
                loginUser = new LoginUserInfoVO();
                //app访问
                String res = OkHttpUtil.get(MessageFormat.format(getEmAppClientUrl, token), null);
                AppTokenDataDO appTokenDataDO = JsonUtil.toBean(res, AppTokenDataDO.class);
                if (appTokenDataDO != null) {
                    DetailData detailData = appTokenDataDO.getDetail();
                    if (detailData != null) {
                        List<LoginAccount> loginAccountList = detailData.logins;
                        if (loginAccountList != null && loginAccountList.stream().count() > 0) {

                            LoginAccount loginAccount = loginAccountList.stream().filter(h -> h.accountType == 2).findFirst().get();
                            if (loginAccount != null) {
                                loginUser.mobileX = loginAccount.key;
                                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                            }
                        }
                    }
                }
            } else {
                //sso登录
                if (token != null && !token.isEmpty()) {
                    loginUser = SSOLogin(request, response, actCode, pid);
                }
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        } else {
            //重复领取判断
            isSubmit = userService.IsSubmitByActCodes(actCode, loginUser.getUid());
            isSubmit = (isSubmit != null && isSubmit.length() > 0) ? isSubmit.substring(0, isSubmit.length() - 1) : "";
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("isSubmit", isSubmit);
        return "qgn/20231109";
    }

    @RequestMapping("/20240103")
    public String index0101(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240103";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20240103";
    }

    @RequestMapping("/20240119")
    public String index0119(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240119";
        String pid = request.getParameter("pid");
        String date = request.getParameter("date");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("showCard", getShowCard(date));
        return "qgn/20240119";
    }

    @RequestMapping("/20240201")
    public String index0201(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240218";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20240201";
    }

    @RequestMapping("/20240201-em-link")
    public String index0201EMLink(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240218";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20240201-em-link";
    }

    @RequestMapping("/20240201mini")
    public String index0201mini(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240218";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20240201mini";
    }

    @RequestMapping("/20240508")
    public String index0508(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240508";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20240508";
    }

    @RequestMapping("/20240508mini")
    public String index0508mini(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240508";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20240508mini";
    }

    @RequestMapping("/20240618")
    public String index0618(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240618";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20240618";
    }

    @RequestMapping("/20240705")
    public String index0705(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240705";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20240705";
    }

    @RequestMapping("/20240801")
    public String index0801(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240801";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20240801";
    }

    @RequestMapping("/20240812")
    public String index0812(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240812";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20240812";
    }

    @RequestMapping("/20240812mini")
    public String index0812mini(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240812";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20240812mini";
    }

    @RequestMapping("/20241009tj")
    public String index1009tj(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilegeTJ20241009";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20241009tj";
    }

    @RequestMapping("/20241009")
    public String index1009(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240926";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20241009";
    }
    @RequestMapping("/20241009mini")
    public String index1009mini(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20240926";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20241009mini";
    }

    @RequestMapping("/20241108tj")
    public String index1108tj(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilegeTJ20241108";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20241108tj";
    }

    @RequestMapping("/20241125")
    public String index1125(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20241115";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20241125";
    }
    @RequestMapping("/20241125mini")
    public String index1125mini(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20241115";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20241125mini";
    }

    @RequestMapping("/20250210")
    public String index0210(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250210";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20250210";
    }
    @RequestMapping("/20250210mini")
    public String index0210mini(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250210";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn/20250210mini";
    }
    public Integer getShowCard(String date) {
        long nowTime = System.currentTimeMillis();
        long card1EndTime = 0L;
        long card2EndTime = 0L;
        long card3EndTime = 0L;
        long card4EndTime = 0L;
        String card1EndTime_str = "2024-01-28 23:59:59";
        String card2EndTime_str = "2024-02-04 23:59:59";
        String card3EndTime_str = "2024-02-11 23:59:59";
        String card4EndTime_str = "2024-02-28 23:59:59";
        Integer showCard = 0;
        if (!StringUtils.isEmpty(date)) {
            try {
                nowTime = df.parse(date).getTime();
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }

        try {
            card1EndTime = df.parse(card1EndTime_str).getTime();
            card2EndTime = df.parse(card2EndTime_str).getTime();
            card3EndTime = df.parse(card3EndTime_str).getTime();
            card4EndTime = df.parse(card4EndTime_str).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }

        if (nowTime <= card1EndTime) {
            showCard = 1;
        } else if (nowTime > card1EndTime && nowTime <= card2EndTime) {
            showCard = 2;
        } else if (nowTime > card2EndTime && nowTime <= card3EndTime) {
            showCard = 3;
        } else {
            showCard = 4;
        }

        return showCard;
    }

    /**
     * 
     * <AUTHOR>
     * @date 2023/12/28 17:22
     * @param request
     * @param response
     * @param model 
     * @return cn.emoney.common.result.Result<java.lang.Boolean>
     */
    @CrossOrigin
    @RequestMapping("/getdyjj")
    @ResponseBody
    public Result<Boolean> getdyjj(HttpServletRequest request, HttpServletResponse response, Model model) throws ParseException {
        String mobileX = request.getParameter("mobilex");
        String uid = request.getParameter("uid");
        String actCode = request.getParameter("actcode");
        String date = request.getParameter("date");
        String val = "";
        if (StringUtils.isEmpty(date)) {
            val = String.valueOf(System.currentTimeMillis());
        } else {
            val = String.valueOf(sdf.parse(date).getTime());
        }
        if (StringUtils.isEmpty(actCode)) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }
        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (StringUtils.isEmpty(mobileX)) {
            return Result.buildErrorResult("-1", "缺少mobilex");
        }

        userService.AddCountByActCode(actCode, uid, val);
        benefitDSService.addUserTag(mobileX);
        return Result.buildSuccessResult("200", "领取成功", null);
    }
    /**
     * 领取特权/赠送使用期
     *
     * @param request
     * @return null
     * <AUTHOR>
     * @date 2023/1/12 15:43
     */
    @RequestMapping("/sendprivilege_qgn")
    @ResponseBody
    public Result<String> sendPrivilege_Qgn(HttpServletRequest request) {
        String actCode = request.getParameter("actcode");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        String activityID = request.getParameter("activityID");
        //领取特权指定2  延期指定1
        String type = request.getParameter("actType");
        String reason = request.getParameter("reason");
        Integer accountType = 1;
        if (!StringUtils.isEmpty(type)) {
            accountType = Integer.parseInt(type);
        }

        if (StringUtils.isEmpty(actCode)) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }

        //activityID需物流提前申请-PACCode
        if (StringUtils.isEmpty(activityID)) {
            return Result.buildErrorResult("-1", "缺少activityID");
        }

        Result<String> resultSendPrivilege = userService.sendPrivilege_New(loginUser, activityID, reason, accountType, actCode);
        if (!resultSendPrivilege.isSuccess()) {
            return Result.buildSuccessResult("-1", resultSendPrivilege.getMsg(), null);
        } else {
            if (actCode.equals("privilege20231011")) {
                //********-波段抢攻能活动领取成功 获取专员信息并发送短信
                producerService.sendMessage("privilegeActUserComing", loginUser.getUid() + "_" + loginUser.getMobileX() + "_" + actCode);
            }

            return Result.buildSuccessResult("200", "领取成功", null);
        }
    }

    @RequestMapping("/sendmsg20231120")
    @ResponseBody
    public String sendMsg_20231120(HttpServletRequest request, HttpServletResponse response) {
        String jsonpCallback = request.getParameter("callback");
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String mobileX = request.getParameter("mobilex");
        String token = request.getParameter("token");

        if (StringUtils.isEmpty(actCode)) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少actCode")) + ")";
        }

        if (StringUtils.isEmpty(uid)) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少uid")) + ")";
        }

        String isSubmit = userService.IsSubmitByActCodes(actCode, uid);
        isSubmit = (isSubmit != null && isSubmit.length() > 0) ? isSubmit.substring(0, isSubmit.length() - 1) : "";
        if (StringUtils.isEmpty(isSubmit)) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "未领取过功能")) + ")";
        }

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
                if (loginUser == null || StringUtils.isEmpty(loginUser.mobileX)) {
                    return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "未查询到绑定的手机号")) + ")";
                }else{
                    mobileX = loginUser.getMobileX();
                }
            }
        }

        if (!StringUtils.isEmpty(mobileX)) {
            mobileService.sendTextMessage(mobileX, "尊敬的用户您好，恭喜您成功获取【金股榜】高端功能，请前往电脑PC端——必读界面查询，具体功能讲解请锁定每晚19:30实战大直播《左手价值 右手趋势》系列课。", "230");
        }

        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "发送成功", null)) + ")";
    }

    /**
    * 领取特权/赠送使用期（kafka消息队列）
    * <AUTHOR>
    * @date 2024/12/18 11:23
    * @param request
    * @return cn.emoney.common.result.Result<java.lang.String>
    */
    @CrossOrigin
    @RequestMapping("/sendprivilege_mq")
    @ResponseBody
    public Result<String> sendPrivilege_MQ(HttpServletRequest request) {
        String actCode = request.getParameter("actcode");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        if(StringUtils.isEmpty(loginUser.getMobileX())){
            return Result.buildErrorResult("用户未绑定手机号");
        }

        String activityID = request.getParameter("activityID");
        //领取特权指定2  延期指定1
        String type = request.getParameter("actType");
        String reason = request.getParameter("reason");
        String orderId = request.getParameter("orderID");
        String detId = request.getParameter("detID");
        Integer accountType = 1;
        if (!StringUtils.isEmpty(type)) {
            accountType = Integer.parseInt(type);
        }

        if (StringUtils.isEmpty(actCode)) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }

        //activityID需物流提前申请-PACCode
        if (StringUtils.isEmpty(activityID)) {
            return Result.buildErrorResult("-1", "缺少activityID");
        }

        String isSubmit = userService.IsSubmitByActCodes(actCode,loginUser.getUid());
        isSubmit = (isSubmit != null && isSubmit.length() > 0) ? isSubmit.substring(0, isSubmit.length() - 1) : "";
        if (!StringUtils.isEmpty(isSubmit)) {
            return Result.buildErrorResult("-1", "已领取过，请勿重复领取");
        }

        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(activityID);
        sendPrivilegeDTO.setReason(reason);
        sendPrivilegeDTO.setApplyUserID("scb_public");
        sendPrivilegeDTO.setActCode(actCode);
        sendPrivilegeDTO.setUid(loginUser.getUid());
        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();

        //2:手机号 1：em号  领取特权指定2  延期指定1
        createActivityGrantApplyAccountDTO.setAccountType(accountType);
        createActivityGrantApplyAccountDTO.setAccount(loginUser.getAccount());
        createActivityGrantApplyAccountDTO.setMID(loginUser.getMobileX());
        if(!StringUtils.isEmpty(detId)){
            createActivityGrantApplyAccountDTO.setOrderDetailID(detId);
        }
        if(!StringUtils.isEmpty(orderId)){
            createActivityGrantApplyAccountDTO.setOrderID(orderId);
        }
        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        //入kafka队列
        producerService.sendMessage("sendPrivilegeUserComing", JSONObject.toJSONString(sendPrivilegeDTO));

        return Result.buildSuccessResult("200", "领取成功", null);
    }
}
