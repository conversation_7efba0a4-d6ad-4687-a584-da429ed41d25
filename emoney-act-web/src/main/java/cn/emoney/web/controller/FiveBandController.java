package cn.emoney.web.controller;

import cn.emoney.common.result.MobileResultDTO;
import cn.emoney.common.result.Result;
import cn.emoney.mapper.activity.FirstClassViewRecordMapper;
import cn.emoney.pojo.FirstClassViewRecordDO;
import cn.emoney.pojo.bo.FirstClassDTO;
import cn.emoney.pojo.bo.videoProgressRequest;
import cn.emoney.service.SurveyService;
import cn.emoney.service.VideoProgressService;
import cn.emoney.service.impl.VideoProgressImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022-09-05
 */
@Controller
@RequestMapping("/fiveband")
public class FiveBandController extends BaseController {

    @Autowired
    private VideoProgressService videoProgressService;

    @Autowired(required = false)
    private FirstClassViewRecordMapper firstClassViewRecordMapper;

    @RequestMapping("/index")
    public String index(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "fiveband/index";
    }

    @RequestMapping("/addclickrecord")
    @ResponseBody
    public Result<String> addClickRecord(HttpServletRequest request, HttpServletResponse response, Model model) {
        String uid = request.getParameter("uid");
        String classid = request.getParameter("classid");
        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult("缺少uid");
        }
        if (StringUtils.isEmpty(classid)) {
            return Result.buildErrorResult("缺少classid");
        }

        List<FirstClassViewRecordDO> list = firstClassViewRecordMapper.selectByClassIdAndUid(uid, 10000 + Integer.parseInt(classid));
        if (list != null && list.size() > 0) {
            return Result.buildErrorResult("1", "已点击过");
        }

        FirstClassViewRecordDO firstClassViewRecordDO = new FirstClassViewRecordDO();
        firstClassViewRecordDO.uid = uid;
        firstClassViewRecordDO.classid = 10000 + Integer.parseInt(classid);
        firstClassViewRecordDO.platform = "1";
        firstClassViewRecordDO.createtime = new Date();

        int ret = firstClassViewRecordMapper.insert(firstClassViewRecordDO);
        if (ret > 0) {
            return Result.buildErrorResult("0", "SUCCESS");
        }
        return Result.buildErrorResult("-1", "FAILED");
    }

    @RequestMapping("/getvideoprogress")
    @ResponseBody
    public Result<String> getVideoProgress(HttpServletRequest request, HttpServletResponse response) {
        String uid = request.getParameter("uid");
        String videoid = request.getParameter("videoid");
        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult("缺少uid");
        }
        if (StringUtils.isEmpty(videoid)) {
            return Result.buildErrorResult("缺少videoid");
        }
        MobileResultDTO<Long> ret = videoProgressService.getVideoProgress(uid, videoid);

        if (ret != null) {
            MobileResultDTO.Result result = ret.getResult();
            if (ret.getResult() != null) {
                if (result.getCode() == 0) {
                    return Result.buildSuccessResult("0", "success", Long.toString(ret.getDetail()));
                } else {
                    return Result.buildErrorResult(Integer.toString(result.getCode()), result.getMsg());
                }
            }

        }
        return Result.buildErrorResult("-2", "未查询到数据");
    }

    @RequestMapping("/savevideoprogress")
    @ResponseBody
    public String saveVideoProgress(HttpServletRequest request, HttpServletResponse response) {
        String jsonpCallback = request.getParameter("callback");
        String uid = request.getParameter("uid");
        String videoid = request.getParameter("videoid");
        String playtime = request.getParameter("playtime");
        String pid = request.getParameter("pid");
        if (StringUtils.isEmpty(uid)) {
            return jsonpCallback + "(" + Result.buildErrorResult("缺少uid") + ")";
        }
        if (StringUtils.isEmpty(videoid)) {
            return jsonpCallback + "(" + Result.buildErrorResult("缺少videoid") + ")";
        }

        UUID uuid = UUID.randomUUID();

        videoProgressRequest request1 = new videoProgressRequest();
        request1.setVideoIdentity(videoid);
        request1.setGid(Long.valueOf(uid));
        request1.setProgress(0);
        request1.setPlayId(uuid.toString());
        request1.setPlayTime(Long.valueOf(playtime));
        request1.setPid(pid);

        MobileResultDTO<Long> ret = videoProgressService.saveVideoProgress(request1);

        if (ret != null) {
            MobileResultDTO.Result result = ret.getResult();
            if (result != null) {
                if (result.getCode() == 0) {
                    return jsonpCallback + "(" + Result.buildSuccessResult("0", "success", null) + ")";
                } else {
                    return jsonpCallback + "(" + Result.buildErrorResult(Integer.toString(result.getCode()), result.getMsg()) + ")";
                }
            }
        }

        return jsonpCallback + "(" + Result.buildErrorResult("-2", "未查询到数据") + ")";
    }


}
