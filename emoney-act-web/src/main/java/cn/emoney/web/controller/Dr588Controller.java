package cn.emoney.web.controller;

import cn.emoney.service.redis.RedisService;
import cn.hutool.core.lang.Console;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Array;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.logging.SimpleFormatter;

/**
 * <AUTHOR>
 * @date 2023-03-30
 */
@Controller
@RequestMapping("/dr588")
public class Dr588Controller extends BaseController {

    @RequestMapping("/index0410")
    public String index_20230410(HttpServletRequest request, HttpServletResponse response, Model model) {
        String date = request.getParameter("date");
        String currentWeek = "1";
        String currentDateStr = "";
        Date currentDate = null;

        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        //时间，可以为具体的某一时间
        Date nowDate = new Date();
        calendar.setTime(nowDate);
        int weekDay = calendar.get(Calendar.DAY_OF_WEEK);
        weekDay = (weekDay == 1 ? 7 : weekDay - 1);

        Date week1_end = null;
        Date week2_start = null;
        Date week2_end = null;
        Date week3_start = null;
        Date week3_end = null;

        try {
            week1_end = sdf.parse("2023-04-14 23:59:59");
            week2_start = sdf.parse("2023-04-16 23:59:59");
            week2_end = sdf.parse("2023-04-21 23:59:59");
            week3_start = sdf.parse("2023-04-23 23:59:59");
            week3_end = sdf.parse("2023-04-28 23:59:59");

            if (date == null || date.isEmpty()) {
                currentDate = nowDate;
                currentDateStr = sdf1.format(nowDate);
            } else {
                currentDate = sdf2.parse(date);
                currentDateStr = sdf1.format(sdf2.parse(date));
            }

        } catch (Exception exp) {
        }

        if (currentDate.before(week2_start)) {
            currentWeek = "1";
        }
        if (currentDate.after(week2_start) && nowDate.before(week3_start)) {
            currentWeek = "2";
        }
        if (currentDate.after(week3_start)) {
            currentWeek = "3";
        }

        model.addAttribute("currentdate", currentDateStr);
        model.addAttribute("dayofweek", weekDay);
        model.addAttribute("currentweek", currentWeek);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "dr588/20230410";
    }


    @RequestMapping("/index0504")
    public String index_20230504(HttpServletRequest request, HttpServletResponse response, Model model) {
        String date = request.getParameter("date");
        String currentWeek = "1";
        String currentDateStr = "";
        Date currentDate = null;

        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        //时间，可以为具体的某一时间
        Date nowDate = new Date();
        calendar.setTime(nowDate);
        int weekDay = calendar.get(Calendar.DAY_OF_WEEK);
        weekDay = (weekDay == 1 ? 7 : weekDay - 1);

        Date week1_end = null;
        Date week2_start = null;
        Date week2_end = null;
        Date week3_start = null;
        Date week3_end = null;

        try {
            week1_end = sdf.parse("2023-05-12 23:59:59");
            week2_start = sdf.parse("2023-05-14 23:59:59");
            week2_end = sdf.parse("2023-05-19 23:59:59");
            week3_start = sdf.parse("2023-05-21 23:59:59");
            week3_end = sdf.parse("2023-05-26 23:59:59");

            if (date == null || date.isEmpty()) {
                currentDate = nowDate;
                currentDateStr = sdf1.format(nowDate);
            } else {
                currentDate = sdf2.parse(date);
                currentDateStr = sdf1.format(sdf2.parse(date));
            }

        } catch (Exception exp) {
        }

        if (currentDate.before(week2_start)) {
            currentWeek = "1";
        }
        if (currentDate.after(week2_start) && nowDate.before(week3_start)) {
            currentWeek = "2";
        }
        if (currentDate.after(week3_start)) {
            currentWeek = "3";
        }

        model.addAttribute("currentdate", currentDateStr);
        model.addAttribute("dayofweek", weekDay);
        model.addAttribute("currentweek", currentWeek);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "dr588/20230504";
    }


    @RequestMapping("/index0808")
    public String index_20230808(HttpServletRequest request, HttpServletResponse response, Model model) {
        String date = request.getParameter("date");
        String currentWeek = "1";
        String currentDateStr = "";
        Date currentDate = null;

        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
        //时间，可以为具体的某一时间
        Date nowDate = new Date();
        calendar.setTime(nowDate);
        int weekDay = calendar.get(Calendar.DAY_OF_WEEK);
        weekDay = (weekDay == 1 ? 7 : weekDay - 1);

        String[][] dateArray = new String[8][2];
        dateArray[0][0] = "2023-08-07";
        dateArray[0][1] = "2023-08-12";

        dateArray[1][0] = "2023-08-13";
        dateArray[1][1] = "2023-08-19";

        dateArray[2][0] = "2023-08-20";
        dateArray[2][1] = "2023-08-26";

        dateArray[3][0] = "2023-08-27";
        dateArray[3][1] = "2023-09-02";

        dateArray[4][0] = "2023-09-03";
        dateArray[4][1] = "2023-09-09";

        dateArray[5][0] = "2023-09-10";
        dateArray[5][1] = "2023-09-16";

        dateArray[6][0] = "2023-09-17";
        dateArray[6][1] = "2023-09-22";

        dateArray[7][0] = "2023-09-24";
        dateArray[7][1] = "2023-09-29";


        try {
            if (date == null || date.isEmpty()) {
                currentDate = nowDate;
                currentDateStr = sdf1.format(nowDate);
            } else {
                currentDate = sdf2.parse(date);
                currentDateStr = sdf1.format(sdf2.parse(date));
            }

        } catch (Exception exp) {
        }

        for (int i = 0; i <= dateArray.length; i++) {
            try {
                Date startDate = sdf2.parse(dateArray[i][0]);
                Date endDate = sdf2.parse(dateArray[i][1]);

                if (currentDate.after(startDate) && nowDate.before(endDate)) {
                    currentWeek = String.valueOf(i + 1);
                }
            } catch (Exception exp) {
            }
        }

        model.addAttribute("currentdate", currentDateStr);
        model.addAttribute("dayofweek", weekDay);
        model.addAttribute("currentweek", currentWeek);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "dr588/20230808";
    }
}
