package cn.emoney.web.controller;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.PointResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.bo.*;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.*;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022-12-29
 */
@Controller
@RequestMapping("/renew2023")
public class Renew2023Controller extends BaseController {
    @Autowired
    private LoginService loginService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private PointService pointService;

    @Autowired
    private Benefit588Service benefit588Service;

    @Autowired
    private RedisService redisService;

    private String redisKey_pointRecord = RedisConstants.Redis_Pre_Activity + "pointrecord";

    private String redisKey_exchangeRecord = RedisConstants.Redis_Pre_Activity + "exchangerecord";

    private String couponFilter_20230421 = "cp-****************,cp-1220125103947953,cp-1201221151151107";

    static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    static SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy年MM月dd日");

    private String hasPointRecordVal = "1";

    @RequestMapping("/index0103")
    public String index_20230103(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20230103_renew2023";
        String point30Taskid = "1605020398020333568";
        String pid = "";
        boolean hasPointRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {

            if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
                hasPointRecord = hasPointRecord(loginUser.getUid(), point30Taskid);
            }

            if(loginUser.getPid().equals("*********") || loginUser.getPid().equals("*********")) {

                if (!hasPointRecord) {
                    //赠送30积分
                    PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                    requestDTO.platform = "1";
                    requestDTO.pid = pid;
                    requestDTO.uid = loginUser.getUid();
                    requestDTO.subId = "";
                    requestDTO.taskId = point30Taskid;
                    boolean ret = pointService.pointRecordAdd(requestDTO);
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("hasPoint30Record", hasPointRecord);
        return "renew2023/20230103";
    }


    @RequestMapping("/index0131")
    public String index_20230131(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20230131_renew2023";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasCoupon30 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }else{
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && "cp-****************".indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                    if (item.COUPON_ISENABLE == 0 && "cp-****************".indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon30 = true;
                    }
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("hasCoupon30", hasCoupon30);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2023/20230131";
    }


    @RequestMapping("/index0313")
    public String index_20230313(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20230313_renew2023";
        String pid = "";
        boolean hasCoupon200 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }else{
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有200元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && "cp-****************".indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon200 = true;
                    }
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon200", hasCoupon200);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2023/20230313";
    }


    @RequestMapping("/index0421")
    public String index_20230421(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20230421_renew2023";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasCoupon30 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }else{
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && couponFilter_20230421.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                    if (item.COUPON_ISENABLE == 0 && "cp-****************".indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon30 = true;
                    }
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("hasCoupon30", hasCoupon30);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2023/20230421";
    }

    @RequestMapping("/index0601")
    public String index_20230601(HttpServletRequest request, HttpServletResponse response, Model model){
        Date currDate = new Date();
        String date = request.getParameter("date");//访问页面可指定日期-仅供测试使用
        if(!StringUtils.isEmpty(date)){
            try{
                currDate = sdf.parse(date);
            }catch (Exception exp){}
        }
        int dayOfMonth = GetDayOfMonth(currDate);
        int monthOfYear = GetMonthOfYear(currDate);
        int maximumDay = GetMaximum(currDate);

        String actCode = "renew2023" + (monthOfYear < 10 ? "0" + monthOfYear : monthOfYear) + "01";

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    String pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        //获取福利列表
        List<Benefit588DTO> benefitList = benefit588Service.getBenefitList(loginUser.getUid(), actCode);
        if (benefitList != null && benefitList.size() >= maximumDay) {
            benefitList = benefitList.subList(0, maximumDay);
        }
        //获取领取福利记录
        List<Act588BenifitRecordDO> benefitRecord = benefit588Service.getUserBenefitRecord(loginUser.getUid(), actCode);


        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("benefitList", benefitList);
        model.addAttribute("benefitRecord", benefitRecord);
        model.addAttribute("dayOfMonth", String.valueOf(dayOfMonth));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2023/20230601";
    }

    @RequestMapping("/index0630")
    public String index_20230630(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20230630_renew2023";
        String pid = "";
        boolean hasCoupon30 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }else{
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有30元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && "cp-****************".indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon30 = true;
                    }
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon30", hasCoupon30);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2023/20230630";
    }


    @RequestMapping("/index102001")
    public String index_2023102001(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20231020_renew2023";
        String pid = request.getParameter("pid");
        boolean hasCoupon30 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon30", hasCoupon30);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2023/2023102001";
    }
    @RequestMapping("/index102002")
    public String index_2023102002(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20231020_renew2023";
        String pid = request.getParameter("pid");
        boolean hasCoupon30 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon30", hasCoupon30);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2023/2023102002";
    }

    @RequestMapping("/index112701")
    public String index_2023112701(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20231127_renew2023";
        String pid = request.getParameter("pid");
        boolean hasCoupon30 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon30", hasCoupon30);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2023/2023112701";
    }
    @RequestMapping("/index112702")
    public String index_2023112702(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20231127_renew2023";
        String pid = request.getParameter("pid");
        boolean hasCoupon30 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon30", hasCoupon30);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2023/2023112702";
    }
    /**
     * 获取领取福利记录
     * <AUTHOR>
     * @date 2023/5/26 16:10
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>>
     */
    @RequestMapping("/getbenefitrecord")
    @ResponseBody
    public Result<List<Act588BenifitRecordDO>> getBenefitRecord(HttpServletRequest request, HttpServletResponse response) {
        String actCode = request.getParameter("actcode");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        List<Act588BenifitRecordDO> recordDOList = benefit588Service.getUserBenefitRecord(loginUser.getUid(), actCode);
        for (Act588BenifitRecordDO record:
                recordDOList) {
            record.showTime = sdf1.format(record.writeTime);
        }

        return Result.buildSuccessResult(recordDOList);
    }
    /**
     * 赠送体验期
     * <AUTHOR>
     * @date 2022/12/29 13:37
     * @return null
     */
    @RequestMapping("/getBenefits")
    @ResponseBody
    public Result<String> getBenefits(HttpServletRequest request, HttpServletResponse response) {
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String actCode = request.getParameter("actcode");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        if (!pid.contains("88801")) {
            return Result.buildErrorResult("非小智盈用户不能参与此活动");
        }

        String redisKey = RedisConstants.Redis_Pre_Activity + "UserBenefits:PAC1220901150913717";
        Object obj = redisService.hashGet(redisKey, uid);
        if (obj != null && "1".equals(obj)) {
            return Result.buildErrorResult("已申请过请勿重复申请");
        }

        //赠送7天使用期
        boolean resultSendPrivilege1 = sendPrivilege("PAC1220901150913717", loginUser.account, "小智盈续费20周年庆", loginUser.getMobileX(), 1);
        if (resultSendPrivilege1) {
            redisService.hashSet(redisKey, uid, "1");
        }

        return Result.buildSuccessResult();
    }

    /**
     * 领取优惠券
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/sendcoupon")
    @ResponseBody
    public Result<String> sendCoupon(HttpServletRequest request, HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String activityID = request.getParameter("activityID");
        String couponprice = request.getParameter("couponprice");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }
        if (StringUtils.isEmpty(activityID)) {
            return Result.buildErrorResult("缺少activityID");
        }
        if (StringUtils.isEmpty(couponprice)) {
            return Result.buildErrorResult("缺少couponprice");
        }

        SendCouponRequestDTO req = new SendCouponRequestDTO();
        req.PRESENT_ACCOUNT_TYPE = 2;
        req.PRESENT_ACCOUNT = loginUser.getMobileX();
        req.COUPON_ACTIVITY_ID = activityID;
        req.COUPON_RULE_PRICE = Integer.parseInt(couponprice);
        req.PRESENT_PERSON = actCode;
        LogisticsResult<String> result = logisticsService.sendCoupon(req);
        if (result.getCode() == 0) {
            return Result.buildSuccessResult();
        } else {
            return Result.buildErrorResult("领取失败," + result.getMsg());
        }
    }
    /**
     * 领取福利
     *
     * @param request
     * @return cn.emoney.common.result.Result<java.util.List < cn.emoney.pojo.Act588BenifitRecordDO>>
     * <AUTHOR>
     * @date 2023/5/23 10:05
     */
    @RequestMapping("/getbenefit20230601")
    @ResponseBody
    public Result<String> GetBenefit20230601(HttpServletRequest request) {
        String benefitId = request.getParameter("benefitid");
        String actCode = request.getParameter("actcode");
        String day = request.getParameter("day");
        String source = request.getParameter("source");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }
        if (StringUtils.isEmpty(benefitId)) {
            return Result.buildErrorResult("-1", "缺少benefitId");
        }
        if (StringUtils.isEmpty(day)) {
            return Result.buildErrorResult("-1", "缺少day");
        }
        if (StringUtils.isEmpty(source)) {
            return Result.buildErrorResult("-1", "缺少source");
        }

        return benefit588Service.getBenefit(loginUser.getUid(), loginUser.getPid(), benefitId, actCode, day, source);
    }


    @RequestMapping("/getcouponpop")
    @ResponseBody
    public Result<String> GetCouponPop(HttpServletRequest request) {
        String actCode = request.getParameter("actcode");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }
        boolean hasCoupon = false;
        //是否 有30元或者100元优惠券
        List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
        Date now = new Date();
        for (QueryCouponListDTO item : list) {
            if (item.COUPON_ISENABLE == 0 && "cp-****************".indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                hasCoupon = true;
            }
            if (item.COUPON_ISENABLE == 0 && "cp-****************".indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                hasCoupon = true;
            }
        }

        Double point = QueryPoint(loginUser.getUid());

        if (hasCoupon || point < 60) {
            return Result.buildSuccessResult("200", "继续参与", "pop_1_0||" + point.intValue());
        } else {
            if (point < 100) {
                return Result.buildSuccessResult("200", "可兑换", "pop_1_60||" + point.intValue());
            } else {
                return Result.buildSuccessResult("200", "可兑换", "pop_1_100||" + point.intValue());
            }
        }
    }

    /**
     * 物流延期(特权|使用期)
     *
     * @param activityID
     * @param account
     * @param reason
     * @param mobilex
     * @param accountType 1:EM 2:MOBILE
     * @return boolean
     * <AUTHOR>
     * @date 2022/8/22 14:10
     */
    public boolean sendPrivilege(String activityID, String account, String reason, String mobilex, int accountType) {
        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(activityID);
        sendPrivilegeDTO.setReason(reason);
        sendPrivilegeDTO.setApplyUserID("scb_public");
        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();

        createActivityGrantApplyAccountDTO.setAccountType(accountType);
        createActivityGrantApplyAccountDTO.setAccount(account);
        createActivityGrantApplyAccountDTO.setMID(mobilex);
        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        Boolean resultSenddPrivilege = logisticsService.sendPrivilege(sendPrivilegeDTO);
        return resultSenddPrivilege;
    }

    /**
     * 是否领取过积分
     *
     * @param uid
     * @return boolean
     * <AUTHOR>
     * @date 2022/1/25 11:28
     */
    public boolean hasPointRecord(String uid, String taskid) {
        if (uid != null) {
            Object obj = redisService.hashGet(redisKey_pointRecord + taskid, uid);
            if (obj != null && obj.toString().equals(hasPointRecordVal)) {
                return true;
            } else {
                Long[] taskids = new Long[1];
                taskids[0] = Long.parseLong(taskid);
                PointQueryByTaskIDRequestDTO requestDTO = new PointQueryByTaskIDRequestDTO();
                requestDTO.uid = Long.parseLong(uid);
                requestDTO.taskIds = taskids;

                List<PointQueryByTaskIDDataDTO> list = pointService.pointQueryByTaskID(requestDTO);
                for (PointQueryByTaskIDDataDTO item : list) {
                    if (item.taskId.equals(taskids[0])) {
                        SetPointRecordToRedis(uid);
                        return true;
                    }
                }
            }
        }

        return false;
    }


/**
 * 查询可用总积分
 * <AUTHOR>
 * @date 2023/7/31 16:23
 * @param request
 * @return cn.emoney.common.result.Result<java.lang.Double>
 */
    @RequestMapping("/querypoint")
    @ResponseBody
    public String QueryPointJsonp(HttpServletRequest request) {
        String uid = request.getParameter("uid");
        String jsonpCallback = request.getParameter("callback");

        if (StringUtils.isEmpty(uid)) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少uid")) + ")";
        }
        Double point = QueryPoint(uid);
        return jsonpCallback + "(" +  JSON.toJSONString(Result.buildSuccessResult("200","查询成功", point)) + ")";
    }

    @RequestMapping("/sendpp20231127")
    @ResponseBody
    public String sendPP23231127(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("用户未登录")) + ")";
        }

        if (loginUser.getUid() == null || loginUser.getUid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少uid")) + ")";
        }
        if (actCode == null || actCode.length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少actCode")) + ")";
        }
        if (loginUser.getPid() == null || loginUser.getPid().length() == 0) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("缺少pid")) + ")";
        }
        Result<String> ret = benefit588Service.sendPP_20231127(actCode,uid,pid);

        return jsonpCallback + "(" + JSON.toJSONString(ret) + ")";
    }

    /**
     * 获取可用积分
     *
     * @param uid
     * @return java.lang.Double
     * <AUTHOR>
     * @date 2022/2/17 14:17
     */
    public Double QueryPoint(String uid) {
        Double point = Double.parseDouble("0");
        if (uid != null) {
            List<PointQueryDataDTO> list = pointService.pointQuerySummary(uid);
            for (PointQueryDataDTO item : list) {
                int pointStatus = item.pointStatus;
                if (pointStatus == 2 || pointStatus == 3) {
                    point += item.pointTotal;
                }
            }
        }
        return point;
    }

    public void SetPointRecordToRedis(String uid) {
        redisService.hashSet(redisKey_pointRecord, uid, hasPointRecordVal);
    }

    public void SetExChangeRecordToRedis(String uid, String productid) {
        redisService.hashSet(redisKey_exchangeRecord + productid, uid, hasPointRecordVal);
    }
}
