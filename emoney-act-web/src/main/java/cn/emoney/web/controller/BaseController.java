package cn.emoney.web.controller;

import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LoginService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 * <AUTHOR>
 * @date 2022-02-17
 */
@Controller
@Slf4j
public class BaseController {
    @Autowired
    private LoginService loginService;

    @Value("${staticPath}")
    public String staticPath;

    @Value("${resourceVersion}")
    public String resourceVersion;

    public String responseResult(String result, String jsonpCallback) {
        if (StringUtils.isBlank(jsonpCallback)) {
            return result;
        } else {
            return jsonpCallback + "(" + result + ")";
        }
    }

    public static String getPostData(HttpServletRequest request) {
        StringBuffer data = new StringBuffer();
        String line = null;
        BufferedReader reader = null;
        try {
            reader = request.getReader();
            while (null != (line = reader.readLine())) {
                data.append(line);
            }
        } catch (IOException e) {
        } finally {
        }
        return data.toString();
    }


    /**
     * 获取指定日期的日期部分
     * <AUTHOR>
     * @date 2023/5/25 16:58
     * @param date
     * @return int
     */
    public int GetDayOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        LocalDate currentDate = LocalDate.of(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1, cal.get(Calendar.DATE));
        return currentDate.getDayOfMonth();
    }
    /**
     * 获取指定日期的月份
     * <AUTHOR>
     * @date 2023/5/25 16:58
     * @param date
     * @return int
     */
    public int GetMonthOfYear(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        LocalDate currentDate = LocalDate.of(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1, cal.get(Calendar.DATE));
        return currentDate.getMonthValue();
    }
    /**
     * 获取指定日期的月份有多少天
     * <AUTHOR>
     * @date 2023/5/25 16:57
     * @param date
     * @return int
     */
    public int GetMaximum(Date date){
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(date);
        return cal.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * SSO登录
     * <AUTHOR>
     * @date 2023/9/25 19:15
     * @param request
     * @param response
     * @param actCode
     * @param pid
     * @return cn.emoney.pojo.vo.LoginUserInfoVO
     */
    public LoginUserInfoVO SSOLogin(HttpServletRequest request, HttpServletResponse response,String actCode, String pid){
        LoginUserInfoVO loginUser = new LoginUserInfoVO();
        SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
        if (ssoResult != null) {
            loginUser = new LoginUserInfoVO();
            loginUser.setUid(ssoResult.getUid());
            loginUser.setPid(pid);
            loginUser.setAccount(ssoResult.getUserName());

            loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
        }
        return loginUser;
    }
}
