package cn.emoney.web.controller;

import cn.emoney.common.enums.BaseResultCodeEnum;
import cn.emoney.common.result.Result;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.SendCouponRequestDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.CouponService;
import cn.emoney.service.LoginService;
import cn.emoney.service.UserService;
import cn.emoney.service.kafka.producer.ProducerService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RequestMapping("/coupon")
@RestController
@Slf4j
public class CouponController {

    @Autowired
    private CouponService couponService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private ProducerService producerService;

    @Autowired
    private UserService userService;

    @GetMapping("/receive")
    public String receive(HttpServletRequest request){
        Result<String> result = null;
        String jsonpCallback = "";
        try{
            String mobile = request.getParameter("mobile");
            String actCode = request.getParameter("actcode");
            jsonpCallback = request.getParameter("callback");

            if(StringUtils.isBlank(mobile)){
                result = Result.buildErrorResult(BaseResultCodeEnum.SYSTEM_ERROR.getCode(), "手机号为空");
                return jsonpCallback + "(" + JSON.toJSONString(result) + ")";
            }

            if(StringUtils.isBlank(actCode)){
                result = Result.buildErrorResult(BaseResultCodeEnum.SYSTEM_ERROR.getCode(), "活动编号为空");
                return jsonpCallback + "(" + JSON.toJSONString(result) + ")";
            }

            result = couponService.sendCoupon(actCode, mobile);
            return jsonpCallback + "(" + JSON.toJSONString(result) + ")";
        }catch(Exception ex){
            log.error("CouponController.receive Error:" + ex);
            result = Result.buildErrorResult(BaseResultCodeEnum.SYSTEM_ERROR.getCode(),ex.getMessage());
            return jsonpCallback + "(" + JSON.toJSONString(result) + ")";
        }
    }

    @GetMapping("/remove")
    public Result<String> remove(HttpServletRequest request){
        String mobile = request.getParameter("mobile");
        String actCode = request.getParameter("actcode");
        Result<String> result = couponService.remove(actCode,mobile);
        return result;
    }

    /**
     * 领取优惠券（kafka消息队列）
     * <AUTHOR>
     * @date 2024/12/18 11:23
     * @param request
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/sendcoupon_mq")
    @ResponseBody
    public Result<String> sendcoupon_MQ(HttpServletRequest request) {
        String actCode = request.getParameter("actcode");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        if(StringUtils.isEmpty(loginUser.getMobileX())){
            return Result.buildErrorResult("用户未绑定手机号");
        }

        String activityID = request.getParameter("activityID");
        String price = request.getParameter("price");
        String orderID = request.getParameter("orderID");

        if (!StringUtils.isNumeric(price)) {
            return Result.buildErrorResult("-1", "price不正确");
        }

        if (StringUtils.isEmpty(actCode)) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }

        //activityID需物流提前申请-PACCode
        if (StringUtils.isEmpty(activityID)) {
            return Result.buildErrorResult("-1", "缺少activityID");
        }

        String isSubmit = userService.IsSubmitByActCodes(activityID,loginUser.getUid());
        isSubmit = (isSubmit != null && isSubmit.length() > 0) ? isSubmit.substring(0, isSubmit.length() - 1) : "";
        if (!StringUtils.isEmpty(isSubmit)) {
            return Result.buildErrorResult("-1", "已领取过，请勿重复领取");
        }

        SendCouponRequestDTO req = new SendCouponRequestDTO();
        req.PRESENT_ACCOUNT_TYPE = 2;
        req.PRESENT_ACCOUNT = loginUser.getMobileX();
        req.COUPON_ACTIVITY_ID = activityID;
        req.COUPON_RULE_PRICE = Integer.parseInt(price);
        req.PRESENT_PERSON = actCode;
        if (!StringUtils.isEmpty(orderID)) {
            req.PRESENT_FROM_ORDERID = orderID;
        }

        //入kafka队列
        producerService.sendMessage("sendCouponUserComing", JSONObject.toJSONString(req));

        return Result.buildSuccessResult("200", "领取成功", null);
    }

}
