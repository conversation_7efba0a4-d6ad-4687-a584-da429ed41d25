package cn.emoney.web.controller;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.GroupInfoDO;
import cn.emoney.pojo.LiveVisitNoticeDO;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.AccountVO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.GroupInfoService;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;
import sun.misc.BASE64Encoder;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021-12-28
 */
@Controller
@Slf4j
public class DemoController {

    @Autowired
    private GroupInfoService groupInfoService;

    @Autowired
    private RedisService redisService;

    @Autowired
    @Qualifier("liveVisitRedisManager")
    private RedisService liveVisitRedisManager;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private UserService userService;

    // 设置奖品编号数组长度
    private final static Integer ARRAY_SIZE = 100;
    // 初始化奖品编号数组
    private static Integer[] prizesArray = new Integer[ARRAY_SIZE];

    static {
        for (int i = 0; i < ARRAY_SIZE; i++) {
            prizesArray[i] = i + 1;
        }
    }

    @RequestMapping
    @ResponseBody
    public String index() {
        return "hello world";
    }

    @RequestMapping("/db")
    @ResponseBody
    public String db() {
        List<GroupInfoDO> groupInfoDOS = groupInfoService.getAll();
        return JSON.toJSONString(groupInfoDOS);
    }

    @RequestMapping("/http")
    @ResponseBody
    public Result<Object> http() {
        List<AccountVO> accountVOS = userService.queryAccountListByAccount("***********");
        return Result.buildSuccessResult(accountVOS);
    }

    @RequestMapping("/redis_del")
    @ResponseBody
    public String redis_del(String key) {
        //String key = "";
        redisService.remove(key);
        return key;
    }

    @RequestMapping("/redis_get")
    @ResponseBody
    public String redis_get(String key) {
        //String key = "";
        return JSON.toJSONString(redisService.get(key));
    }

    @RequestMapping("/redis_set")
    @ResponseBody
    public String redis_set(String key, String val) {
        //String key = "";
        return JSON.toJSONString(redisService.set(key, Integer.valueOf(val)));
    }

    @RequestMapping("/redis_incrby")
    @ResponseBody
    public String redis_incrBy(String key) {
        Long count = redisService.incrBy(key);
        return count.toString();
    }

    @RequestMapping("/redis_hget")
    @ResponseBody
    public String redis_hget(String key, String val) {
        //String key = "";
        return JSON.toJSONString(redisService.hashGet(key, val));
    }

    @RequestMapping("/redis_hdel")
    @ResponseBody
    public String redis_hdel(String key, String val) {
        redisService.hashDel(key, val);
        return key + val;
    }

    @RequestMapping("/redis_hashgetall")
    @ResponseBody
    public Object redis_hashgetall(String key) {
        return redisService.hashGetAll(key);
    }

    @RequestMapping("/redis_qgnop")
    @ResponseBody
    public Object redis_qgnOp(String fromKey, String toKey) {
        if (!fromKey.equals("EMoney.Activity.UnifiedState:privilege20231011")) {
            return "";
        }
        Object fromObj = redisService.hashGetAll(fromKey);
        String ret = "";
        if (fromObj != null) {
            Map<Object, Object> obj = (Map<Object, Object>) fromObj;

            Iterator it = obj.keySet().iterator();
            while (it.hasNext()) {
                // 遍历 Map
                Object key = it.next();
                Object val = obj.get(key);
                if (val.equals("1")) {
                    redisService.hashDel(fromKey, key);
                    ret += "key：" + key + "，value:" + val + "\r\n";
                }

                //redisService.hashSet(toKey,key.toString(),val.toString());
            }
        }
        return ret;
    }

    @RequestMapping("/redisnew_get")
    @ResponseBody
    public Object redisnew_get(String key) {
        Object obj = liveVisitRedisManager.get(key);
        return obj;
    }

    @RequestMapping("/redisnew_set")
    @ResponseBody
    public void redisnew_set(String key) {
        liveVisitRedisManager.setAdd(key, "1");
    }

    @RequestMapping("/redisnew_leftsize")
    @ResponseBody
    public Object redisnew_leftSize(String key) {
        return liveVisitRedisManager.leftSize(key);
    }

    @RequestMapping("/redisnew_listadd")
    @ResponseBody
    public void redisnew_setAdd(String key) {
        LiveVisitNoticeDO liveVisitNoticeDO = new LiveVisitNoticeDO();
        liveVisitNoticeDO.UID = "1000467308";
        liveVisitNoticeDO.MID = "a6e27d84956749178a5983118bc00d43";
        liveVisitNoticeDO.OpTime = "2023-06-15T17:47:25.8978216+08:00";
        liveVisitNoticeDO.OpType = 0;
        liveVisitRedisManager.leftPush(key, liveVisitNoticeDO);
    }

    @RequestMapping("/testGetLotteryNum")
    @ResponseBody
    public Object testGetLotteryNum(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        Long count = redisService.sSize(key);
        if (count <= 0) {
            redisService.setAdd(key, prizesArray);
        }

        return redisService.sPop(key);
    }

    @RequestMapping("/log")
    @ResponseBody
    public String log() {
        log.info("记录日志成功");
        log.error("记录错误日志");
        return "success";
    }

    @RequestMapping("/demo")
    public String demo(Model model) {
        model.addAttribute("msg", "这是一段测试文字");
        byte[] bytes = QrCodeUtil.generatePng("https://www.emoney.cn/", 300, 300);
        String byteStr = new BASE64Encoder().encodeBuffer(bytes).trim();
        model.addAttribute("qr", "data:image/png;base64," + byteStr);

        return "demo";
    }
    @RequestMapping("/bad-gateway")
    public ResponseEntity<String> getBadGatewayResponse() {
        // 创建一个ResponseEntity对象，传入响应体和HttpStatus对象
        return ResponseEntity
                .status(HttpStatus.BAD_GATEWAY) // 设置状态码为502
                .body("Bad Gateway Error"); // 设置响应体
    }

//    @PostMapping("/reSendPrivilege")
//    @ResponseBody
//    public Object reSendPrivilege() {
//        String jsonStr = "[{\"account\":\"EMY1768369\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1779837\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1232115\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1772831\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1808695\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1809917\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0566523\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1808572\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1685019\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1809631\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1557613\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1789009\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1779582\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1773351\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1798328\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1785337\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1773059\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1796927\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1806630\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1811888\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1298156\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1802030\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMJN0160863\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1258319\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1796159\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1780133\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1776510\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1811862\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1282737\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1771796\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1779099\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1519528\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1817687\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1811089\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1805582\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1780975\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1791305\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1783931\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1778861\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1707089\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1779931\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1809151\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1775676\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1812592\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMJQ0187531\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1813605\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1769508\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1780580\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1769017\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0092002\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1793053\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1778601\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1810602\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1788260\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0916005\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY0203357\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1768255\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0899598\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1807993\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1661616\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1809157\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1815618\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1665109\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1789275\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1782872\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1751569\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1778217\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1798778\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1800052\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1789165\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0693958\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1751729\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1810652\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1765725\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1786087\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1761611\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1798756\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0133127\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1795221\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EM194047\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1765182\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1801129\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1678927\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1797195\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1766785\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1772519\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1278921\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1355012\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1816727\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1782556\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1788053\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1781073\",\"uid\":\"2024421062\",\"pid\":\"*********\"},{\"account\":\"EMY1821152\",\"uid\":\"2024554978\",\"pid\":\"*********\"},{\"account\":\"EMY0691000\",\"uid\":\"501476894\",\"pid\":\"*********\"},{\"account\":\"EMY0371566\",\"uid\":\"2020014567\",\"pid\":\"*********\"},{\"account\":\"EM010376\",\"uid\":\"1000240615\",\"pid\":\"*********\"},{\"account\":\"EMY1776116\",\"uid\":\"2024230744\",\"pid\":\"*********\"},{\"account\":\"EMY1019698\",\"uid\":\"2021541983\",\"pid\":\"*********\"},{\"account\":\"EM065229\",\"uid\":\"1000119860\",\"pid\":\"*********\"},{\"account\":\"EMY1806132\",\"uid\":\"2024507589\",\"pid\":\"*********\"},{\"account\":\"EMY1796086\",\"uid\":\"2024472769\",\"pid\":\"*********\"},{\"account\":\"EMY0211625\",\"uid\":\"500502596\",\"pid\":\"*********\"},{\"account\":\"EMY1560755\",\"uid\":\"2023535004\",\"pid\":\"*********\"},{\"account\":\"EMY1779596\",\"uid\":\"2007940763\",\"pid\":\"*********\"},{\"account\":\"EMY0279765\",\"uid\":\"2019265306\",\"pid\":\"*********\"},{\"account\":\"EMY1780028\",\"uid\":\"2024240777\",\"pid\":\"*********\"},{\"account\":\"EMY1788528\",\"uid\":\"2023568592\",\"pid\":\"*********\"},{\"account\":\"EMY1162600\",\"uid\":\"2022622045\",\"pid\":\"*********\"},{\"account\":\"EMY1793321\",\"uid\":\"2024449329\",\"pid\":\"*********\"},{\"account\":\"EMY1817131\",\"uid\":\"2024542347\",\"pid\":\"*********\"},{\"account\":\"EMY1808876\",\"uid\":\"2024517328\",\"pid\":\"*********\"},{\"account\":\"EMY1815159\",\"uid\":\"2024537413\",\"pid\":\"*********\"},{\"account\":\"EMY1082183\",\"uid\":\"2019993245\",\"pid\":\"*********\"},{\"account\":\"EMY1797888\",\"uid\":\"2022010776\",\"pid\":\"*********\"},{\"account\":\"EMY1799608\",\"uid\":\"2024486098\",\"pid\":\"*********\"},{\"account\":\"EMY0221719\",\"uid\":\"500609664\",\"pid\":\"*********\"},{\"account\":\"EMY1172817\",\"uid\":\"2021458550\",\"pid\":\"*********\"},{\"account\":\"EMY0293918\",\"uid\":\"2019721842\",\"pid\":\"*********\"},{\"account\":\"EMY1763231\",\"uid\":\"2023680029\",\"pid\":\"*********\"},{\"account\":\"EMY1762855\",\"uid\":\"2024188407\",\"pid\":\"*********\"},{\"account\":\"EMY1790116\",\"uid\":\"2022826908\",\"pid\":\"*********\"},{\"account\":\"EMY1783936\",\"uid\":\"2024431017\",\"pid\":\"*********\"},{\"account\":\"EMY1821075\",\"uid\":\"2024554605\",\"pid\":\"*********\"},{\"account\":\"EMY1791031\",\"uid\":\"2023001034\",\"pid\":\"*********\"},{\"account\":\"EMY1783771\",\"uid\":\"2024434020\",\"pid\":\"*********\"},{\"account\":\"EMY1632397\",\"uid\":\"2021799720\",\"pid\":\"*********\"},{\"account\":\"EMY1821018\",\"uid\":\"2002265080\",\"pid\":\"*********\"},{\"account\":\"EMY1127160\",\"uid\":\"2022556340\",\"pid\":\"*********\"},{\"account\":\"EMY0698562\",\"uid\":\"1000783295\",\"pid\":\"*********\"},{\"account\":\"EMY1733079\",\"uid\":\"2011469237\",\"pid\":\"*********\"},{\"account\":\"EMY1806352\",\"uid\":\"2023690600\",\"pid\":\"*********\"},{\"account\":\"EMY1698716\",\"uid\":\"2002037853\",\"pid\":\"*********\"},{\"account\":\"EMY1737170\",\"uid\":\"2023719911\",\"pid\":\"*********\"},{\"account\":\"EMY1109928\",\"uid\":\"2022393434\",\"pid\":\"*********\"},{\"account\":\"EMY1818262\",\"uid\":\"2024530027\",\"pid\":\"*********\"},{\"account\":\"EMY1798001\",\"uid\":\"2013846255\",\"pid\":\"*********\"},{\"account\":\"EMY1789578\",\"uid\":\"1000224388\",\"pid\":\"*********\"},{\"account\":\"EMY1729536\",\"uid\":\"2020938109\",\"pid\":\"*********\"},{\"account\":\"EMY1817807\",\"uid\":\"2000364174\",\"pid\":\"*********\"},{\"account\":\"EMY1820003\",\"uid\":\"2019392627\",\"pid\":\"*********\"},{\"account\":\"EMY1782028\",\"uid\":\"2024421168\",\"pid\":\"*********\"},{\"account\":\"EMY0172762\",\"uid\":\"1000032191\",\"pid\":\"*********\"},{\"account\":\"EMY1535313\",\"uid\":\"500225496\",\"pid\":\"*********\"},{\"account\":\"EMY1806275\",\"uid\":\"2020847680\",\"pid\":\"*********\"},{\"account\":\"EMY1818633\",\"uid\":\"2024548291\",\"pid\":\"*********\"},{\"account\":\"EMY1786698\",\"uid\":\"2024440629\",\"pid\":\"*********\"},{\"account\":\"EMY0551295\",\"uid\":\"2020324481\",\"pid\":\"*********\"},{\"account\":\"EMY1811856\",\"uid\":\"2024498168\",\"pid\":\"*********\"},{\"account\":\"EMY1795379\",\"uid\":\"2024470128\",\"pid\":\"*********\"},{\"account\":\"EMY1813687\",\"uid\":\"2024536112\",\"pid\":\"*********\"},{\"account\":\"EMY1278532\",\"uid\":\"2016340447\",\"pid\":\"*********\"},{\"account\":\"EMY1181573\",\"uid\":\"2018196737\",\"pid\":\"*********\"},{\"account\":\"EMY1799879\",\"uid\":\"2024488066\",\"pid\":\"*********\"},{\"account\":\"EMY1711998\",\"uid\":\"2023931561\",\"pid\":\"*********\"},{\"account\":\"EMY0882623\",\"uid\":\"2021401533\",\"pid\":\"*********\"},{\"account\":\"EMY1786572\",\"uid\":\"2013523692\",\"pid\":\"*********\"},{\"account\":\"EMY1782120\",\"uid\":\"2024424356\",\"pid\":\"*********\"},{\"account\":\"EMY0955370\",\"uid\":\"2022042066\",\"pid\":\"*********\"},{\"account\":\"EMY1818531\",\"uid\":\"2024548079\",\"pid\":\"*********\"},{\"account\":\"EMY1808608\",\"uid\":\"2024516552\",\"pid\":\"*********\"},{\"account\":\"EMY1771163\",\"uid\":\"2022056422\",\"pid\":\"*********\"},{\"account\":\"EMY1711309\",\"uid\":\"2023924252\",\"pid\":\"*********\"},{\"account\":\"EMY1807906\",\"uid\":\"2024514112\",\"pid\":\"*********\"},{\"account\":\"EMY0927876\",\"uid\":\"2018407271\",\"pid\":\"*********\"},{\"account\":\"EMY1793330\",\"uid\":\"2018488736\",\"pid\":\"*********\"},{\"account\":\"EMY1807200\",\"uid\":\"2024464201\",\"pid\":\"*********\"},{\"account\":\"EMY1815220\",\"uid\":\"2014917876\",\"pid\":\"*********\"},{\"account\":\"EMY1330302\",\"uid\":\"2022368255\",\"pid\":\"*********\"},{\"account\":\"EMY1809621\",\"uid\":\"2024517413\",\"pid\":\"*********\"},{\"account\":\"EMY1810591\",\"uid\":\"2024523534\",\"pid\":\"*********\"},{\"account\":\"EMY1800892\",\"uid\":\"2008664854\",\"pid\":\"*********\"},{\"account\":\"EMY0398659\",\"uid\":\"500998130\",\"pid\":\"*********\"},{\"account\":\"EMY1789803\",\"uid\":\"2013930665\",\"pid\":\"*********\"},{\"account\":\"EMY1798078\",\"uid\":\"602045135\",\"pid\":\"*********\"},{\"account\":\"EMY0566802\",\"uid\":\"2020378259\",\"pid\":\"*********\"},{\"account\":\"EMY1771786\",\"uid\":\"2024217444\",\"pid\":\"*********\"},{\"account\":\"EMY1728761\",\"uid\":\"2023979757\",\"pid\":\"*********\"},{\"account\":\"EMY1800688\",\"uid\":\"2024479545\",\"pid\":\"*********\"},{\"account\":\"EMY1302725\",\"uid\":\"1000281218\",\"pid\":\"*********\"},{\"account\":\"EMY1372216\",\"uid\":\"2020557374\",\"pid\":\"*********\"},{\"account\":\"EM627921\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1809123\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1803606\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1779287\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1618119\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1792255\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1809959\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1732582\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1776760\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1377936\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1813683\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1731505\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1803521\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1791809\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0110300\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1810799\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1782092\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMJQ0186617\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1797198\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1305589\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1787828\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0172727\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1803761\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1776819\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1767709\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1793715\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1792536\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1800891\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1031120\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1805771\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1805113\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1801707\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1616169\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1125971\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1233680\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0512225\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1095131\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1332613\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1190670\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1257528\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1359355\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0987667\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1278013\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1390999\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1515992\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1525311\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1803320\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1133505\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EM113991\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1550328\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1200106\",\"uid\":\"2002205585\",\"pid\":\"*********\"},{\"account\":\"EMY1279513\",\"uid\":\"2022989416\",\"pid\":\"*********\"},{\"account\":\"EMY1185811\",\"uid\":\"2022703337\",\"pid\":\"*********\"},{\"account\":\"EMY1196329\",\"uid\":\"2019024652\",\"pid\":\"*********\"},{\"account\":\"EMY1321067\",\"uid\":\"2022490409\",\"pid\":\"*********\"},{\"account\":\"EMY1320221\",\"uid\":\"2023091753\",\"pid\":\"*********\"},{\"account\":\"EMY1309253\",\"uid\":\"2018452481\",\"pid\":\"*********\"},{\"account\":\"EMY1169877\",\"uid\":\"2022619049\",\"pid\":\"*********\"},{\"account\":\"EMY0601226\",\"uid\":\"1001233312\",\"pid\":\"*********\"},{\"account\":\"EMY1573089\",\"uid\":\"1000228963\",\"pid\":\"*********\"},{\"account\":\"EMY1812621\",\"uid\":\"1000186991\",\"pid\":\"*********\"},{\"account\":\"EMY0193368\",\"uid\":\"2001445029\",\"pid\":\"*********\"},{\"account\":\"EMY1239771\",\"uid\":\"2022891490\",\"pid\":\"*********\"},{\"account\":\"EMY1519072\",\"uid\":\"2023078379\",\"pid\":\"*********\"},{\"account\":\"EMY1261028\",\"uid\":\"2022910388\",\"pid\":\"*********\"},{\"account\":\"EMY1217503\",\"uid\":\"1000294427\",\"pid\":\"*********\"},{\"account\":\"EMY1531890\",\"uid\":\"2023430302\",\"pid\":\"*********\"},{\"account\":\"EMY0935088\",\"uid\":\"2000379960\",\"pid\":\"*********\"},{\"account\":\"EMY0823689\",\"uid\":\"2021647922\",\"pid\":\"*********\"},{\"account\":\"EMY0283552\",\"uid\":\"2015899721\",\"pid\":\"*********\"},{\"account\":\"EMY0953373\",\"uid\":\"2022011769\",\"pid\":\"*********\"},{\"account\":\"EMY0680196\",\"uid\":\"2020923817\",\"pid\":\"*********\"},{\"account\":\"EMY1537125\",\"uid\":\"2023359822\",\"pid\":\"*********\"},{\"account\":\"EMY1292983\",\"uid\":\"2023030025\",\"pid\":\"*********\"},{\"account\":\"EMY1016157\",\"uid\":\"2019355913\",\"pid\":\"*********\"},{\"account\":\"EMY1028698\",\"uid\":\"2022090004\",\"pid\":\"*********\"},{\"account\":\"EMY1352696\",\"uid\":\"2023194412\",\"pid\":\"*********\"},{\"account\":\"EMY1209506\",\"uid\":\"2022757535\",\"pid\":\"*********\"},{\"account\":\"EMY1337366\",\"uid\":\"2013113266\",\"pid\":\"*********\"},{\"account\":\"EMY1301031\",\"uid\":\"2023063646\",\"pid\":\"*********\"},{\"account\":\"EMY1115822\",\"uid\":\"2021320632\",\"pid\":\"*********\"},{\"account\":\"EMY1713730\",\"uid\":\"2019735747\",\"pid\":\"*********\"},{\"account\":\"EMY1552572\",\"uid\":\"1000322921\",\"pid\":\"*********\"},{\"account\":\"EMY1028556\",\"uid\":\"2020471057\",\"pid\":\"*********\"},{\"account\":\"EMY1728262\",\"uid\":\"2016258123\",\"pid\":\"*********\"},{\"account\":\"EMY1528399\",\"uid\":\"2019302782\",\"pid\":\"*********\"},{\"account\":\"EMY1528525\",\"uid\":\"1000004905\",\"pid\":\"*********\"},{\"account\":\"EMY1807219\",\"uid\":\"2024512053\",\"pid\":\"*********\"},{\"account\":\"EMY0166688\",\"uid\":\"1000671576\",\"pid\":\"*********\"},{\"account\":\"EMY0896537\",\"uid\":\"2020979873\",\"pid\":\"*********\"},{\"account\":\"EMY1169726\",\"uid\":\"2022649921\",\"pid\":\"*********\"},{\"account\":\"EMY0672615\",\"uid\":\"1000471967\",\"pid\":\"*********\"},{\"account\":\"EMY1187566\",\"uid\":\"2022665439\",\"pid\":\"*********\"},{\"account\":\"EMY1212576\",\"uid\":\"2022740369\",\"pid\":\"*********\"},{\"account\":\"EMY0911353\",\"uid\":\"2021936104\",\"pid\":\"*********\"},{\"account\":\"EMY1020126\",\"uid\":\"2022245357\",\"pid\":\"*********\"},{\"account\":\"EMY1285161\",\"uid\":\"2020918937\",\"pid\":\"*********\"},{\"account\":\"EMY1328615\",\"uid\":\"2021602662\",\"pid\":\"*********\"},{\"account\":\"EMY1530268\",\"uid\":\"2003154320\",\"pid\":\"*********\"},{\"account\":\"EMY0978791\",\"uid\":\"2022075093\",\"pid\":\"*********\"},{\"account\":\"EMY1377528\",\"uid\":\"2023270098\",\"pid\":\"*********\"},{\"account\":\"EMY1292976\",\"uid\":\"500489359\",\"pid\":\"*********\"},{\"account\":\"EMY0997375\",\"uid\":\"2021850935\",\"pid\":\"*********\"},{\"account\":\"EMY1339722\",\"uid\":\"2001027902\",\"pid\":\"*********\"},{\"account\":\"EMY1558788\",\"uid\":\"2023526276\",\"pid\":\"*********\"},{\"account\":\"EMY1799706\",\"uid\":\"2024486799\",\"pid\":\"*********\"},{\"account\":\"EMY1263112\",\"uid\":\"2022901400\",\"pid\":\"*********\"},{\"account\":\"EMY1125596\",\"uid\":\"2018548158\",\"pid\":\"*********\"},{\"account\":\"EMY0968757\",\"uid\":\"501011546\",\"pid\":\"*********\"},{\"account\":\"EMY1309559\",\"uid\":\"2022030428\",\"pid\":\"*********\"},{\"account\":\"EMY0125167\",\"uid\":\"2019171927\",\"pid\":\"*********\"},{\"account\":\"EMY1059060\",\"uid\":\"2021396809\",\"pid\":\"*********\"},{\"account\":\"EMY1288012\",\"uid\":\"2023016947\",\"pid\":\"*********\"},{\"account\":\"EMY1556072\",\"uid\":\"2022005718\",\"pid\":\"*********\"},{\"account\":\"EMY1398182\",\"uid\":\"2016669710\",\"pid\":\"*********\"},{\"account\":\"EMY1275831\",\"uid\":\"2022972488\",\"pid\":\"*********\"},{\"account\":\"EMY1612892\",\"uid\":\"1000587367\",\"pid\":\"*********\"},{\"account\":\"EMY1289891\",\"uid\":\"2022797550\",\"pid\":\"*********\"},{\"account\":\"EMY1337688\",\"uid\":\"2023182443\",\"pid\":\"*********\"},{\"account\":\"EMY0170607\",\"uid\":\"2019172895\",\"pid\":\"*********\"},{\"account\":\"EMY1277321\",\"uid\":\"2015758626\",\"pid\":\"*********\"},{\"account\":\"EMY1329779\",\"uid\":\"2000476021\",\"pid\":\"*********\"},{\"account\":\"EMY0111773\",\"uid\":\"2019015983\",\"pid\":\"*********\"},{\"account\":\"EMY1371615\",\"uid\":\"2023216874\",\"pid\":\"*********\"},{\"account\":\"EMY0553128\",\"uid\":\"1000194614\",\"pid\":\"*********\"},{\"account\":\"EMY1597971\",\"uid\":\"1000252461\",\"pid\":\"*********\"},{\"account\":\"EMY1395305\",\"uid\":\"2020889367\",\"pid\":\"*********\"},{\"account\":\"EMY1558676\",\"uid\":\"2013787977\",\"pid\":\"*********\"},{\"account\":\"EMY0879528\",\"uid\":\"2013356770\",\"pid\":\"*********\"},{\"account\":\"EMY1028650\",\"uid\":\"2022068154\",\"pid\":\"*********\"},{\"account\":\"EMY1260000\",\"uid\":\"1000794403\",\"pid\":\"*********\"},{\"account\":\"EMY1717598\",\"uid\":\"2023942411\",\"pid\":\"*********\"},{\"account\":\"EMY0990160\",\"uid\":\"2022148196\",\"pid\":\"*********\"},{\"account\":\"EMY1160621\",\"uid\":\"2008693005\",\"pid\":\"*********\"},{\"account\":\"EMY0336396\",\"uid\":\"2019883680\",\"pid\":\"*********\"},{\"account\":\"EMY0135820\",\"uid\":\"1001217465\",\"pid\":\"*********\"},{\"account\":\"EMY0976362\",\"uid\":\"2000218346\",\"pid\":\"*********\"},{\"account\":\"EMY1297216\",\"uid\":\"2023019741\",\"pid\":\"*********\"},{\"account\":\"EMY1297251\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1300933\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0918025\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0907152\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1390393\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1232265\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1270330\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0689816\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1071766\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1306105\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1200821\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0169168\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1215075\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1260983\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0572028\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1178971\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1235721\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1199193\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1528675\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1076006\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1203031\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1176705\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0206963\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1335279\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1207871\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY0655858\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1306516\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1239515\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0902022\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1331501\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0621837\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1229173\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1267715\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0786325\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1229851\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0021137\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1180560\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1332080\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1068206\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0957726\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1536165\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EM223331\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0987020\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1535511\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1270859\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1131108\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1557063\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0631708\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1033792\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1280267\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1166167\",\"uid\":\"2022376937\",\"pid\":\"*********\"},{\"account\":\"EMY0989875\",\"uid\":\"1000233035\",\"pid\":\"*********\"},{\"account\":\"EMY1709718\",\"uid\":\"2023923493\",\"pid\":\"*********\"},{\"account\":\"EMY1311519\",\"uid\":\"2012575455\",\"pid\":\"*********\"},{\"account\":\"EMY1365261\",\"uid\":\"2020518550\",\"pid\":\"*********\"},{\"account\":\"EMY0729301\",\"uid\":\"1000614091\",\"pid\":\"*********\"},{\"account\":\"EMY1552198\",\"uid\":\"1000520039\",\"pid\":\"*********\"},{\"account\":\"EMY1287761\",\"uid\":\"2022970207\",\"pid\":\"*********\"},{\"account\":\"EMY1288735\",\"uid\":\"2023018938\",\"pid\":\"*********\"},{\"account\":\"EMY1230286\",\"uid\":\"2021819340\",\"pid\":\"*********\"},{\"account\":\"EMY0960269\",\"uid\":\"2022050060\",\"pid\":\"*********\"},{\"account\":\"EMY1562796\",\"uid\":\"2023542108\",\"pid\":\"*********\"},{\"account\":\"EMY1111871\",\"uid\":\"2022516215\",\"pid\":\"*********\"},{\"account\":\"EMY1230581\",\"uid\":\"2022858671\",\"pid\":\"*********\"},{\"account\":\"EMY1230073\",\"uid\":\"2022836084\",\"pid\":\"*********\"},{\"account\":\"EMY1191875\",\"uid\":\"2021325969\",\"pid\":\"*********\"},{\"account\":\"EMY1286023\",\"uid\":\"2022939616\",\"pid\":\"*********\"},{\"account\":\"EMY1216253\",\"uid\":\"2012620692\",\"pid\":\"*********\"},{\"account\":\"EMY1307521\",\"uid\":\"2020203532\",\"pid\":\"*********\"},{\"account\":\"EMY1217979\",\"uid\":\"2022779576\",\"pid\":\"*********\"},{\"account\":\"EMY1228353\",\"uid\":\"1000446802\",\"pid\":\"*********\"},{\"account\":\"EMY1735859\",\"uid\":\"2024012578\",\"pid\":\"*********\"},{\"account\":\"EMY1031985\",\"uid\":\"2022274525\",\"pid\":\"*********\"},{\"account\":\"EMY1189792\",\"uid\":\"2022716238\",\"pid\":\"*********\"},{\"account\":\"EMY1205088\",\"uid\":\"2022756244\",\"pid\":\"*********\"},{\"account\":\"EMY1566026\",\"uid\":\"2023546853\",\"pid\":\"*********\"},{\"account\":\"EMY1333369\",\"uid\":\"2017629012\",\"pid\":\"*********\"},{\"account\":\"EMY1105931\",\"uid\":\"601740640\",\"pid\":\"*********\"},{\"account\":\"EMY1272239\",\"uid\":\"2014300994\",\"pid\":\"*********\"},{\"account\":\"EM637095\",\"uid\":\"1000262872\",\"pid\":\"*********\"},{\"account\":\"EMY1399313\",\"uid\":\"2021533739\",\"pid\":\"*********\"},{\"account\":\"EMY0927213\",\"uid\":\"2021967730\",\"pid\":\"*********\"},{\"account\":\"EMY1560165\",\"uid\":\"501698996\",\"pid\":\"*********\"},{\"account\":\"EMY1288189\",\"uid\":\"2021600454\",\"pid\":\"*********\"},{\"account\":\"EMY1533778\",\"uid\":\"2022744866\",\"pid\":\"*********\"},{\"account\":\"EMY0970730\",\"uid\":\"2022078678\",\"pid\":\"*********\"},{\"account\":\"EMY1350035\",\"uid\":\"1000152452\",\"pid\":\"*********\"},{\"account\":\"EMY1320755\",\"uid\":\"2020641184\",\"pid\":\"*********\"},{\"account\":\"EMY1021826\",\"uid\":\"2022224784\",\"pid\":\"*********\"},{\"account\":\"EMY0502732\",\"uid\":\"1001470385\",\"pid\":\"*********\"},{\"account\":\"EMY1372265\",\"uid\":\"2022337565\",\"pid\":\"*********\"},{\"account\":\"EMY1561279\",\"uid\":\"2023537798\",\"pid\":\"*********\"},{\"account\":\"EMY1770032\",\"uid\":\"2001346117\",\"pid\":\"*********\"},{\"account\":\"EMY0176002\",\"uid\":\"2019323797\",\"pid\":\"*********\"},{\"account\":\"EMY1256708\",\"uid\":\"2022841607\",\"pid\":\"*********\"},{\"account\":\"EMY1805727\",\"uid\":\"2017471348\",\"pid\":\"*********\"},{\"account\":\"EMY1216016\",\"uid\":\"2022396108\",\"pid\":\"*********\"},{\"account\":\"EMY0633708\",\"uid\":\"2020723820\",\"pid\":\"*********\"},{\"account\":\"EMY1107835\",\"uid\":\"2022507421\",\"pid\":\"*********\"},{\"account\":\"EMY1358578\",\"uid\":\"2023212343\",\"pid\":\"*********\"},{\"account\":\"EMY1183178\",\"uid\":\"2022675926\",\"pid\":\"*********\"},{\"account\":\"EMY1521662\",\"uid\":\"2022527438\",\"pid\":\"*********\"},{\"account\":\"EMY1318272\",\"uid\":\"2023115227\",\"pid\":\"*********\"},{\"account\":\"EMY1309656\",\"uid\":\"2017537422\",\"pid\":\"*********\"},{\"account\":\"EMY0105832\",\"uid\":\"1000175037\",\"pid\":\"*********\"},{\"account\":\"EMY1520867\",\"uid\":\"2023217864\",\"pid\":\"*********\"},{\"account\":\"EMY1769189\",\"uid\":\"2024206965\",\"pid\":\"*********\"},{\"account\":\"EMY1031536\",\"uid\":\"2022232554\",\"pid\":\"*********\"},{\"account\":\"EMY0679529\",\"uid\":\"1000532220\",\"pid\":\"*********\"},{\"account\":\"EMY0807607\",\"uid\":\"2021572273\",\"pid\":\"*********\"},{\"account\":\"EMY1267008\",\"uid\":\"2021950806\",\"pid\":\"*********\"},{\"account\":\"EMY0327135\",\"uid\":\"2018968161\",\"pid\":\"*********\"},{\"account\":\"EMY1267831\",\"uid\":\"2016709447\",\"pid\":\"*********\"},{\"account\":\"EMY1092796\",\"uid\":\"2021929078\",\"pid\":\"*********\"},{\"account\":\"EMY1810687\",\"uid\":\"2024498595\",\"pid\":\"*********\"},{\"account\":\"EMY1816007\",\"uid\":\"2024540338\",\"pid\":\"*********\"},{\"account\":\"EMY0876317\",\"uid\":\"2021836799\",\"pid\":\"*********\"},{\"account\":\"EMY1255711\",\"uid\":\"2022902894\",\"pid\":\"*********\"},{\"account\":\"EMY1330605\",\"uid\":\"2012672443\",\"pid\":\"*********\"},{\"account\":\"EM240671\",\"uid\":\"1000200897\",\"pid\":\"*********\"},{\"account\":\"EMY1056553\",\"uid\":\"2020819648\",\"pid\":\"*********\"},{\"account\":\"EMY1558079\",\"uid\":\"2023300895\",\"pid\":\"*********\"},{\"account\":\"EMY1813006\",\"uid\":\"2024529045\",\"pid\":\"*********\"},{\"account\":\"EMY1283797\",\"uid\":\"2023004626\",\"pid\":\"*********\"},{\"account\":\"EMY1352601\",\"uid\":\"2023195713\",\"pid\":\"*********\"},{\"account\":\"EMY1162369\",\"uid\":\"2022629657\",\"pid\":\"*********\"},{\"account\":\"EMY1235261\",\"uid\":\"2022393558\",\"pid\":\"*********\"},{\"account\":\"EMY0961397\",\"uid\":\"2022059125\",\"pid\":\"*********\"},{\"account\":\"EMY0360862\",\"uid\":\"2019967610\",\"pid\":\"*********\"},{\"account\":\"EMY1072630\",\"uid\":\"2022331416\",\"pid\":\"*********\"},{\"account\":\"EMY0510068\",\"uid\":\"1000795826\",\"pid\":\"*********\"},{\"account\":\"EMY1521708\",\"uid\":\"2023425790\",\"pid\":\"*********\"},{\"account\":\"EMY0050655\",\"uid\":\"2013010069\",\"pid\":\"*********\"},{\"account\":\"EMY1210019\",\"uid\":\"2022771870\",\"pid\":\"*********\"},{\"account\":\"EMY1150216\",\"uid\":\"2021506218\",\"pid\":\"*********\"},{\"account\":\"EMY1311556\",\"uid\":\"2020639430\",\"pid\":\"*********\"},{\"account\":\"EMY0206929\",\"uid\":\"1000312352\",\"pid\":\"*********\"},{\"account\":\"EMY1359133\",\"uid\":\"1000335976\",\"pid\":\"*********\"},{\"account\":\"EMY0586368\",\"uid\":\"1000534295\",\"pid\":\"*********\"},{\"account\":\"EMY1235873\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1325939\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1576502\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1309589\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1207125\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1179096\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1350336\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0722003\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1526500\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1169752\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1071862\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1196221\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1275272\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1568903\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1156673\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1625252\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1293762\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0819579\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1070809\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1369910\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1218091\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1502918\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1229061\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1377890\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1093902\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1190101\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1258071\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1177991\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0601578\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1319690\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1363072\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EM019040\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1067627\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMPB1001666\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1090783\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1328095\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1627120\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1178795\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1376288\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1310157\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1255827\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0606930\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMJN0160231\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0979828\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1133273\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1218920\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1236293\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1001953\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1317260\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0099257\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1272971\",\"uid\":\"2009787902\",\"pid\":\"*********\"},{\"account\":\"EMY1582096\",\"uid\":\"2021268695\",\"pid\":\"*********\"},{\"account\":\"EMY1616083\",\"uid\":\"2022649742\",\"pid\":\"*********\"},{\"account\":\"EMY1152856\",\"uid\":\"2002720257\",\"pid\":\"*********\"},{\"account\":\"EMY1360069\",\"uid\":\"2023213895\",\"pid\":\"*********\"},{\"account\":\"EMY0700699\",\"uid\":\"2002396493\",\"pid\":\"*********\"},{\"account\":\"EMY1309256\",\"uid\":\"2023039531\",\"pid\":\"*********\"},{\"account\":\"EMY0911182\",\"uid\":\"2021933483\",\"pid\":\"*********\"},{\"account\":\"EMY0360529\",\"uid\":\"2019959518\",\"pid\":\"*********\"},{\"account\":\"EMY1362628\",\"uid\":\"2012737916\",\"pid\":\"*********\"},{\"account\":\"EMY0398081\",\"uid\":\"1000839920\",\"pid\":\"*********\"},{\"account\":\"EMY1379861\",\"uid\":\"2023282539\",\"pid\":\"*********\"},{\"account\":\"EMY1217539\",\"uid\":\"2022803576\",\"pid\":\"*********\"},{\"account\":\"EMY0860663\",\"uid\":\"1000193741\",\"pid\":\"*********\"},{\"account\":\"EMY1236082\",\"uid\":\"2017973727\",\"pid\":\"*********\"},{\"account\":\"EMY1565571\",\"uid\":\"2021010718\",\"pid\":\"*********\"},{\"account\":\"EMY1268970\",\"uid\":\"2022926972\",\"pid\":\"*********\"},{\"account\":\"EMY1285096\",\"uid\":\"2022968692\",\"pid\":\"*********\"},{\"account\":\"EMY0981115\",\"uid\":\"2018816311\",\"pid\":\"*********\"},{\"account\":\"EMY1161858\",\"uid\":\"2022626340\",\"pid\":\"*********\"},{\"account\":\"EMY1331129\",\"uid\":\"1000543895\",\"pid\":\"*********\"},{\"account\":\"EM109525\",\"uid\":\"1000939543\",\"pid\":\"*********\"},{\"account\":\"EMY0099536\",\"uid\":\"2019056434\",\"pid\":\"*********\"},{\"account\":\"EMY1156777\",\"uid\":\"2019887630\",\"pid\":\"*********\"},{\"account\":\"EMY1199523\",\"uid\":\"2022741581\",\"pid\":\"*********\"},{\"account\":\"EMY0757292\",\"uid\":\"2021041515\",\"pid\":\"*********\"},{\"account\":\"EMY1336075\",\"uid\":\"1000735543\",\"pid\":\"*********\"},{\"account\":\"EMY1516797\",\"uid\":\"2012504082\",\"pid\":\"*********\"},{\"account\":\"EMY0907352\",\"uid\":\"2021867035\",\"pid\":\"*********\"},{\"account\":\"EMY1108162\",\"uid\":\"2022506589\",\"pid\":\"*********\"},{\"account\":\"EMY0997863\",\"uid\":\"2022081221\",\"pid\":\"*********\"},{\"account\":\"EM607398\",\"uid\":\"1000286955\",\"pid\":\"*********\"},{\"account\":\"EMY1306000\",\"uid\":\"2023079614\",\"pid\":\"*********\"},{\"account\":\"EMY0269670\",\"uid\":\"2017458167\",\"pid\":\"*********\"},{\"account\":\"EMY1160020\",\"uid\":\"2022618558\",\"pid\":\"*********\"},{\"account\":\"EMY1135351\",\"uid\":\"2014456025\",\"pid\":\"*********\"},{\"account\":\"EMY1280265\",\"uid\":\"2022994828\",\"pid\":\"*********\"},{\"account\":\"EM133428\",\"uid\":\"1000321980\",\"pid\":\"*********\"},{\"account\":\"EMY1122215\",\"uid\":\"1000279892\",\"pid\":\"*********\"},{\"account\":\"EMY0895828\",\"uid\":\"2018021560\",\"pid\":\"*********\"},{\"account\":\"EMY0263361\",\"uid\":\"1000838187\",\"pid\":\"*********\"},{\"account\":\"EMY1155629\",\"uid\":\"1000632803\",\"pid\":\"*********\"},{\"account\":\"EMY1226791\",\"uid\":\"2022841535\",\"pid\":\"*********\"},{\"account\":\"EMY1566226\",\"uid\":\"2023545919\",\"pid\":\"*********\"},{\"account\":\"EMY1667200\",\"uid\":\"2011037427\",\"pid\":\"*********\"},{\"account\":\"EMY1081353\",\"uid\":\"2000813205\",\"pid\":\"*********\"},{\"account\":\"EMY1309982\",\"uid\":\"2015786278\",\"pid\":\"*********\"},{\"account\":\"EMY0920253\",\"uid\":\"501673821\",\"pid\":\"*********\"},{\"account\":\"EMY1236279\",\"uid\":\"2003767239\",\"pid\":\"*********\"},{\"account\":\"EMY1503313\",\"uid\":\"2014082911\",\"pid\":\"*********\"},{\"account\":\"EMY1070082\",\"uid\":\"2002574479\",\"pid\":\"*********\"},{\"account\":\"EMY1339972\",\"uid\":\"2012312939\",\"pid\":\"*********\"},{\"account\":\"EMY0697131\",\"uid\":\"1000518205\",\"pid\":\"*********\"},{\"account\":\"EMY1050901\",\"uid\":\"2016523110\",\"pid\":\"*********\"},{\"account\":\"EMY1530656\",\"uid\":\"2023454938\",\"pid\":\"*********\"},{\"account\":\"EMY0525106\",\"uid\":\"1001484025\",\"pid\":\"*********\"},{\"account\":\"EMY1225990\",\"uid\":\"2018267594\",\"pid\":\"*********\"},{\"account\":\"EMY1259863\",\"uid\":\"2017711766\",\"pid\":\"*********\"},{\"account\":\"EMY1683288\",\"uid\":\"1000221341\",\"pid\":\"*********\"},{\"account\":\"EMY1263309\",\"uid\":\"2003615956\",\"pid\":\"*********\"},{\"account\":\"EMY1635117\",\"uid\":\"2023736470\",\"pid\":\"*********\"},{\"account\":\"EMY1203239\",\"uid\":\"2022758620\",\"pid\":\"*********\"},{\"account\":\"EMY1559917\",\"uid\":\"2023523963\",\"pid\":\"*********\"},{\"account\":\"EMY1367361\",\"uid\":\"2023213427\",\"pid\":\"*********\"},{\"account\":\"EMY0670072\",\"uid\":\"501452707\",\"pid\":\"*********\"},{\"account\":\"EMY0375011\",\"uid\":\"2019160862\",\"pid\":\"*********\"},{\"account\":\"EMY1081261\",\"uid\":\"2022420243\",\"pid\":\"*********\"},{\"account\":\"EMY1315739\",\"uid\":\"2012558178\",\"pid\":\"*********\"},{\"account\":\"EMY1615320\",\"uid\":\"2023660053\",\"pid\":\"*********\"},{\"account\":\"EMY1369370\",\"uid\":\"2023247726\",\"pid\":\"*********\"},{\"account\":\"EMY0908332\",\"uid\":\"1000310971\",\"pid\":\"*********\"},{\"account\":\"EMY1313922\",\"uid\":\"2023104910\",\"pid\":\"*********\"},{\"account\":\"EMJN0169677\",\"uid\":\"1000844402\",\"pid\":\"*********\"},{\"account\":\"EM237891\",\"uid\":\"1000206632\",\"pid\":\"*********\"},{\"account\":\"EMY1317532\",\"uid\":\"2016731141\",\"pid\":\"*********\"},{\"account\":\"EMY1206699\",\"uid\":\"1000759183\",\"pid\":\"*********\"},{\"account\":\"EMY1218507\",\"uid\":\"2022744179\",\"pid\":\"*********\"},{\"account\":\"EMY0019768\",\"uid\":\"2015928315\",\"pid\":\"*********\"},{\"account\":\"EMY1197091\",\"uid\":\"2016852773\",\"pid\":\"*********\"},{\"account\":\"EMY1291968\",\"uid\":\"2017237116\",\"pid\":\"*********\"},{\"account\":\"EMY0501391\",\"uid\":\"2020032635\",\"pid\":\"*********\"},{\"account\":\"EMY1173627\",\"uid\":\"2020198152\",\"pid\":\"*********\"},{\"account\":\"EMY1537679\",\"uid\":\"2023476434\",\"pid\":\"*********\"},{\"account\":\"EMY1350723\",\"uid\":\"2023188770\",\"pid\":\"*********\"},{\"account\":\"EMY1289531\",\"uid\":\"2022950915\",\"pid\":\"*********\"},{\"account\":\"EMY0171059\",\"uid\":\"1000215256\",\"pid\":\"*********\"},{\"account\":\"EMY1507066\",\"uid\":\"2020741851\",\"pid\":\"*********\"},{\"account\":\"EMY1279031\",\"uid\":\"2022758528\",\"pid\":\"*********\"},{\"account\":\"EMY1533109\",\"uid\":\"2023467380\",\"pid\":\"*********\"},{\"account\":\"EMY1036706\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1578636\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EM035239\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0933553\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1210922\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1166907\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1169915\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1236197\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1229786\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY0989578\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1371076\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1369336\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1263132\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1626117\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1158839\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1286955\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1571792\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1335812\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1330805\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1376968\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1780033\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1255561\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1263979\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1060170\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1281072\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1512726\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1259369\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0863653\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1615963\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1082693\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1227551\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0896013\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0050225\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0501879\",\"uid\":\"*********\",\"pid\":\"*********\"},{\"account\":\"EMY1218982\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0335022\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1232283\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1352872\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1667906\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1027717\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0679370\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0568993\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1561218\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1772211\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1210927\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1562975\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1501826\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1021200\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0329229\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1029075\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EM038897\",\"uid\":\"1000317844\",\"pid\":\"*********\"},{\"account\":\"EMY1356855\",\"uid\":\"2023199206\",\"pid\":\"*********\"},{\"account\":\"EMY1721219\",\"uid\":\"2023957642\",\"pid\":\"*********\"},{\"account\":\"EMY1122726\",\"uid\":\"2022545985\",\"pid\":\"*********\"},{\"account\":\"EMY1271217\",\"uid\":\"2022966984\",\"pid\":\"*********\"},{\"account\":\"EMY1262295\",\"uid\":\"2022923573\",\"pid\":\"*********\"},{\"account\":\"EMY1155750\",\"uid\":\"2022522491\",\"pid\":\"*********\"},{\"account\":\"EMY1285817\",\"uid\":\"2022999833\",\"pid\":\"*********\"},{\"account\":\"EMY1508237\",\"uid\":\"2023360061\",\"pid\":\"*********\"},{\"account\":\"EMY1100555\",\"uid\":\"2022490137\",\"pid\":\"*********\"},{\"account\":\"EMJN0160165\",\"uid\":\"1000835167\",\"pid\":\"*********\"},{\"account\":\"EMY1300632\",\"uid\":\"2023062540\",\"pid\":\"*********\"},{\"account\":\"EMY1513909\",\"uid\":\"2021875227\",\"pid\":\"*********\"},{\"account\":\"EMY1709539\",\"uid\":\"2023926398\",\"pid\":\"*********\"},{\"account\":\"EMY1079967\",\"uid\":\"2022398383\",\"pid\":\"*********\"},{\"account\":\"EMY0177653\",\"uid\":\"2019332281\",\"pid\":\"*********\"},{\"account\":\"EMY1016030\",\"uid\":\"2022222682\",\"pid\":\"*********\"},{\"account\":\"EM250772\",\"uid\":\"1000204287\",\"pid\":\"*********\"},{\"account\":\"EMY1507766\",\"uid\":\"2019200073\",\"pid\":\"*********\"},{\"account\":\"EMY1562079\",\"uid\":\"2023517288\",\"pid\":\"*********\"},{\"account\":\"EMY1581550\",\"uid\":\"2011325693\",\"pid\":\"*********\"},{\"account\":\"EMY1291935\",\"uid\":\"1000552164\",\"pid\":\"*********\"},{\"account\":\"EMPB1001722\",\"uid\":\"501886627\",\"pid\":\"*********\"},{\"account\":\"EMY1558332\",\"uid\":\"2023524682\",\"pid\":\"*********\"},{\"account\":\"EMY0333091\",\"uid\":\"2019893512\",\"pid\":\"*********\"},{\"account\":\"EMY0911356\",\"uid\":\"2021525639\",\"pid\":\"*********\"},{\"account\":\"EMY1608270\",\"uid\":\"2023617801\",\"pid\":\"*********\"},{\"account\":\"EMY1217262\",\"uid\":\"2022798750\",\"pid\":\"*********\"},{\"account\":\"EMY1368205\",\"uid\":\"2023242111\",\"pid\":\"*********\"},{\"account\":\"EMY0321668\",\"uid\":\"2019774607\",\"pid\":\"*********\"},{\"account\":\"EMY1119513\",\"uid\":\"2021189253\",\"pid\":\"*********\"},{\"account\":\"EMY1162859\",\"uid\":\"2022632307\",\"pid\":\"*********\"},{\"account\":\"EMY1035207\",\"uid\":\"2022291284\",\"pid\":\"*********\"},{\"account\":\"EMY1539733\",\"uid\":\"2023487249\",\"pid\":\"*********\"},{\"account\":\"EMY0796685\",\"uid\":\"2021421312\",\"pid\":\"*********\"},{\"account\":\"EMY0128588\",\"uid\":\"2010969392\",\"pid\":\"*********\"},{\"account\":\"EMY0963122\",\"uid\":\"2022017063\",\"pid\":\"*********\"},{\"account\":\"EMY1277932\",\"uid\":\"2022936031\",\"pid\":\"*********\"},{\"account\":\"EMY1512887\",\"uid\":\"2023367857\",\"pid\":\"*********\"},{\"account\":\"EMY1558787\",\"uid\":\"2023515760\",\"pid\":\"*********\"},{\"account\":\"EMY0927535\",\"uid\":\"2020454732\",\"pid\":\"*********\"},{\"account\":\"EMY0319635\",\"uid\":\"2019838965\",\"pid\":\"*********\"},{\"account\":\"EMY1155565\",\"uid\":\"2022369195\",\"pid\":\"*********\"},{\"account\":\"EMY1251157\",\"uid\":\"2022893963\",\"pid\":\"*********\"},{\"account\":\"EMY1307780\",\"uid\":\"2023070998\",\"pid\":\"*********\"},{\"account\":\"EMY1772725\",\"uid\":\"2022990103\",\"pid\":\"*********\"},{\"account\":\"EMY0981279\",\"uid\":\"2021844953\",\"pid\":\"*********\"},{\"account\":\"EMY1510053\",\"uid\":\"2000655326\",\"pid\":\"*********\"},{\"account\":\"EMY0887837\",\"uid\":\"2005289951\",\"pid\":\"*********\"},{\"account\":\"EMY0905296\",\"uid\":\"2011178293\",\"pid\":\"*********\"},{\"account\":\"EMY1561965\",\"uid\":\"2018121202\",\"pid\":\"*********\"},{\"account\":\"EMY1813992\",\"uid\":\"2024535815\",\"pid\":\"*********\"},{\"account\":\"EMY1775682\",\"uid\":\"2021130351\",\"pid\":\"*********\"},{\"account\":\"EMY1799185\",\"uid\":\"2024484812\",\"pid\":\"*********\"},{\"account\":\"EMY1808177\",\"uid\":\"2024505888\",\"pid\":\"*********\"},{\"account\":\"EMY1810552\",\"uid\":\"2018016285\",\"pid\":\"*********\"},{\"account\":\"EMY1788611\",\"uid\":\"601027437\",\"pid\":\"*********\"},{\"account\":\"EMY1810126\",\"uid\":\"2014485097\",\"pid\":\"*********\"},{\"account\":\"EMY1800135\",\"uid\":\"500079897\",\"pid\":\"*********\"},{\"account\":\"EMY1772931\",\"uid\":\"2023589857\",\"pid\":\"*********\"},{\"account\":\"EMY1036082\",\"uid\":\"501727205\",\"pid\":\"*********\"},{\"account\":\"EMY1785116\",\"uid\":\"2023669003\",\"pid\":\"*********\"},{\"account\":\"EMY1785615\",\"uid\":\"2024414978\",\"pid\":\"*********\"},{\"account\":\"EMY1797167\",\"uid\":\"1000681093\",\"pid\":\"*********\"},{\"account\":\"EMY1803168\",\"uid\":\"2023992352\",\"pid\":\"*********\"},{\"account\":\"EMY1789261\",\"uid\":\"2024445396\",\"pid\":\"*********\"},{\"account\":\"EMY1813627\",\"uid\":\"2024527994\",\"pid\":\"*********\"},{\"account\":\"EMY1781868\",\"uid\":\"2018687885\",\"pid\":\"*********\"},{\"account\":\"EMY1777758\",\"uid\":\"2023857180\",\"pid\":\"*********\"},{\"account\":\"EMY1813027\",\"uid\":\"2023307373\",\"pid\":\"*********\"},{\"account\":\"EMY1772033\",\"uid\":\"2024218190\",\"pid\":\"*********\"},{\"account\":\"EMY1820335\",\"uid\":\"2008038203\",\"pid\":\"*********\"},{\"account\":\"EMY1813976\",\"uid\":\"2024529121\",\"pid\":\"*********\"},{\"account\":\"EMY0868228\",\"uid\":\"2021653763\",\"pid\":\"*********\"},{\"account\":\"EMY1757053\",\"uid\":\"2024049579\",\"pid\":\"*********\"},{\"account\":\"EMY1797252\",\"uid\":\"2023722068\",\"pid\":\"*********\"},{\"account\":\"EMY0958803\",\"uid\":\"2017069901\",\"pid\":\"*********\"},{\"account\":\"EMY1770582\",\"uid\":\"2024043656\",\"pid\":\"*********\"},{\"account\":\"EMY1780303\",\"uid\":\"2003700564\",\"pid\":\"*********\"},{\"account\":\"EMY1789071\",\"uid\":\"1000240615\",\"pid\":\"*********\"},{\"account\":\"EMY1800252\",\"uid\":\"2024490237\",\"pid\":\"*********\"},{\"account\":\"EMY1779952\",\"uid\":\"1000549566\",\"pid\":\"*********\"},{\"account\":\"EMY1712863\",\"uid\":\"2023916534\",\"pid\":\"*********\"},{\"account\":\"EMY1781083\",\"uid\":\"2024421242\",\"pid\":\"*********\"},{\"account\":\"EMY1329280\",\"uid\":\"2023151538\",\"pid\":\"*********\"},{\"account\":\"EMY1160930\",\"uid\":\"2022625120\",\"pid\":\"*********\"},{\"account\":\"EMY1807807\",\"uid\":\"2024512151\",\"pid\":\"*********\"},{\"account\":\"EMY1781912\",\"uid\":\"2024045498\",\"pid\":\"*********\"},{\"account\":\"EMY1820866\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY0806365\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1269589\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1811063\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1820160\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1726797\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1808768\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1732935\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1771585\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1820151\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1806719\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1817973\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1768763\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1820063\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1807108\",\"uid\":\"**********\",\"pid\":\"*********\"},{\"account\":\"EMY1781115\",\"uid\":\"**********\",\"pid\":\"*********\"}]";
//        String actCode_old = "firstclass20230628";
//        String actCode_new = "firstclass20231013_1";
//        String reason = "大师第一课特定功能观看满15天赠送15天使用期";
//        List<Map<String, String>> retList = new ArrayList<>();
//
//        List<LoginUserInfoVO> userInfoVOList = JSON.parseObject(jsonStr, new TypeReference<List<LoginUserInfoVO>>() {
//        });
//        for (LoginUserInfoVO loginUser :
//                userInfoVOList) {
//            Map<String, String> map = new HashMap<>();
//            String activityID = loginUser.pid.equals("*********") ? "PAC1230921132220252" : "PAC1230921132232638";
//
//            //重复领取判断-老code
//            String isSubmit = userService.IsSubmitByActCodes(actCode_old, loginUser.getUid());
//            isSubmit = (isSubmit != null && isSubmit.length() > 0) ? isSubmit.substring(0, isSubmit.length() - 1) : "";
//            if (!StringUtils.isEmpty(isSubmit)) {
//                map.put(loginUser.account, "已领取过，请勿重复领取" + actCode_old);
//                retList.add(map);
//            } else {
//                //重复领取判断-老code
//                String isSubmit_new = userService.IsSubmitByActCodes(actCode_new, loginUser.getUid());
//                isSubmit_new = (isSubmit_new != null && isSubmit_new.length() > 0) ? isSubmit_new.substring(0, isSubmit_new.length() - 1) : "";
//                if (!StringUtils.isEmpty(isSubmit_new)) {
//                    map.put(loginUser.account, "已领取过，请勿重复领取" + actCode_new);
//                    retList.add(map);
//                } else {
//                    SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
//                    sendPrivilegeDTO.setAppId("A009");
//                    sendPrivilegeDTO.setActivityID(activityID);
//                    sendPrivilegeDTO.setReason(reason);
//                    sendPrivilegeDTO.setApplyUserID("scb_public");
//                    List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
//                    CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();
//
//                    //2:手机号 1：em号  领取特权指定2  延期指定1
//                    createActivityGrantApplyAccountDTO.setAccountType(1);
//                    createActivityGrantApplyAccountDTO.setAccount(loginUser.getAccount());
//                    createActivityGrantApplyAccountDTO.setMID(loginUser.getMobileX());
//                    createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
//                    sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);
//
//                    Result<String> ret = logisticsService.sendPrivilegeResult(sendPrivilegeDTO);
//                    if (ret.isSuccess()) {
//                        //领取成功记录领取状态
//                        userService.AddCountByActCode(actCode_new, loginUser.getUid(), String.valueOf(System.currentTimeMillis()));
//
//                        map.put(loginUser.account, "领取成功");
//                        retList.add(map);
//                    } else {
//                        map.put(loginUser.account, "领取失败");
//                        retList.add(map);
//                    }
//                }
//            }
//        }
//
//        return retList;
//    }
}
