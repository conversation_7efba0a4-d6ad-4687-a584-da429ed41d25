package cn.emoney.web.controller;

import brave.Span;
import brave.Tracer;
import brave.propagation.TraceContext;
import cn.emoney.act.exception.PointException;
import cn.emoney.act.quest.logic.QuestReward;
import cn.emoney.act.quest.logic.reward.PointReward;
import cn.emoney.common.enums.CallFailedActionTypeEnum;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.mapper.activity.ActCallFailedMapper;
import cn.emoney.pojo.*;
import cn.emoney.pojo.vo.UserLoginIdInfoVO;
import cn.emoney.service.ActTaskConfService;
import cn.emoney.service.PointService;
import cn.emoney.service.SurveyService;
import cn.emoney.service.UserService;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.BiConsumer;

import static cn.emoney.web.controller.BaseController.getPostData;

/**
 * 问卷集成业务
 *
 * <AUTHOR>
 * @date 2022/03/18 09:40
 **/
@Slf4j
@Controller
@RequestMapping("/survey")
public class SurveyController {
    @Autowired
    private UserService userService;

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private PointService pointService;

    @Autowired
    private ActCallFailedMapper callFailedMapper;

    @Autowired
    private ActTaskConfService actTaskConfService;
    @Autowired
    Tracer tracer;

    @Value("${receiveSurveyJWUrl}")
    private String receiveSurveyJWUrl;
    /**
     * temp adapter for act:firstclass20231204
     */
    private final BiConsumer<String, WjxSurveyResultDO> adapter;

    public SurveyController(KafkaTemplate<String, Object> kafkaTemplate) {
        this.adapter = (code, result) -> {
            kafkaTemplate.send("first-class-user-survey", result);
        };
    }

    private static final String activityId_239811538 = "239811538";

    private static final String activityId_258993788 = "258993788";

    @RequestMapping("tagsExtract")
    public String tagsExtract(HttpServletRequest request, Model model) {
        String uid = request.getParameter("uid");
        String userName = request.getParameter("customerName");

        if (!StringUtils.isEmpty(uid)) {
            List<SurveyTagDTO> list = new ArrayList<>();
            SurveyTagDTO dto_239811538 = surveyService.tagsExtract(uid, activityId_239811538);
            SurveyTagDTO dto_258993788 = surveyService.tagsExtract(uid, activityId_258993788);
            if (dto_258993788 != null) {
                dto_258993788.setSurveyName("大师问卷");
                dto_258993788.setSurveyId("258993788");
                list.add(dto_258993788);
            }
            if (dto_239811538 != null) {
                dto_239811538.setSurveyName("小智盈问卷");
                dto_239811538.setSurveyId("239811538");
                list.add(dto_239811538);
            }
            list.sort((o1, o2) -> {
                // 降序排列
                return o2.getSubmitTime().compareTo(o1.getSubmitTime());
            });
            model.addAttribute("uid", uid);
            model.addAttribute("userName", userName);
            model.addAttribute("list", list.isEmpty() ? null : list);
        }
        return "survey/tags_extract";
    }

    @RequestMapping("/joindetail")
    public String joinDetail(HttpServletRequest request, HttpServletResponse response, Model model) throws Exception {

        Long activity = Long.parseLong(request.getParameter("activity"));
        Long joinId = Long.parseLong(request.getParameter("joinId"));

        if (activity > 0 && joinId > 0) {

            String template = surveyService.getWJXActivitytemplate(activity);
            WjxSurveyResultDO result = surveyService.getSurvey(joinId.toString());

            if (template == null || ("").equals(template)) {
                return "illegal activity";
            } else {
                model.addAttribute("template", template);
                model.addAttribute("result", result.getAnswer());
            }
        } else {
            return "miss activity or joinId";
        }

        return "survey/joindetail";

    }

    @PostMapping("onComplete")
    @ResponseBody
    public void onComplete(@RequestBody WjxSurveyResultDO result) {
        String scene = result.getEmscene();
        if (scene.startsWith("act_")) {
            String actCode = scene.substring("act_".length());
            BiConsumer<String, WjxSurveyResultDO> adapter = findResultAdapter(actCode);
            Assert.notNull(adapter, "can't found adapter for act:" + actCode);
            adapter.accept(actCode, result);
        }
    }

    private BiConsumer<String, WjxSurveyResultDO> findResultAdapter(String actCode) {
        if (actCode.startsWith("firstclass")) {
            return adapter;
        }
        return null;
    }

    /**
     * 大师新用户问卷调研-赠送积分-回调方法
     *
     * @param request
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/11/21 14:14
     */
    @RequestMapping("/callback_addpoint")
    @ResponseBody
    public String callback_addpoint(HttpServletRequest request) {
        Long uid = 0L;
        String pid = "";
        String scene = "";
        Long taskId = 0L;
        String actCode = "";
        String emNo = "";//EM号或者0X手机号

        String traceId = Optional.ofNullable(tracer.currentSpan())
                .map(Span::context)
                .map(TraceContext::traceIdString)
                .orElseGet(() -> UUID.randomUUID().toString());

        String content = getPostData(request);
        if (StringUtils.isEmpty(content)) {
            return JSON.toJSONString(Result.buildErrorResult("-1", "参数缺失", null));
        }
        WjxSurveyResultDO obj = JSON.parseObject(content, WjxSurveyResultDO.class);
        if (obj != null) {
            if (obj.getEmuid() != null) {
                uid = obj.getEmuid().longValue();
            }
            pid = obj.getPid();
            scene = obj.getEmscene();
            emNo = obj.getEmno();
        } else {
            return JSON.toJSONString(Result.buildErrorResult("-1", "参数反序列化失败", null));
        }

        if (StringUtils.isEmpty(scene)) {
            return JSON.toJSONString(Result.buildErrorResult("-1", "scene未查询到", null));
        }

        //无uid 根据em账号获取uid和pid
        if (uid.equals(0L) && StringUtils.isEmpty(pid) && !StringUtils.isEmpty(emNo)) {
            //根据emNo获取uid和pid
            pid = userService.getHighPIDByAccount(emNo);

            UserLoginIdInfoVO userLoginIdInfo = userService.GetLoginIDInfoByAccount(emNo);
            if (userLoginIdInfo != null) {
                uid = userLoginIdInfo.PID;
            }
        }

        //获取活动对应的task
        //积分类活动scene格式：activity_SurveyPoint
        if (scene.indexOf("_") > -1) {
            actCode = StringUtils.split(scene, "_")[1];

            //********新增逻辑-利用扩展参数ext3实现同类型多期活动的回调任务
            //例：http://act.emoney.cn/api/survey/gotopc?scene=activity_SurveyPoint&wjx=http://survey.emoney.cn/vm/exVLfAm.aspx&ext3=********
            //数据库根据scene_ext3查询活动对应的task
            if (StringUtils.isNotEmpty(obj.getExt3())) {
                actCode = actCode + "_" + obj.getExt3();
            }

            QuestReward reward = actTaskConfService.getTaskConf(actCode, QuestReward.class);
            if (reward instanceof PointReward) {
                taskId = ((PointReward) reward).getTaskId();
            }
        }

        //积分赠送
        if (taskId != 0) {
            //20231204-新增版本限制
            if (taskId.equals(1726805406031495168L)) {
                if (!"888020000,888080000".contains(pid)) {
                    return JSON.toJSONString(Result.buildSuccessResult("200", "无赠送积分权限", null));
                }
            }

            if (taskId.equals(1728948621172809728L)) {
                if (!"888010000".contains(pid)) {
                    return JSON.toJSONString(Result.buildSuccessResult("200", "无赠送积分权限", null));
                }
            }

            if (taskId.equals(1802910777624498176L)) {
                if (!"888020000,888080000,888204010,888224010".contains(pid)) {
                    return JSON.toJSONString(Result.buildSuccessResult("200", "无赠送积分权限", null));
                }
            }


            try {
                pointService.addPointByTaskId(uid, taskId);
            } catch (PointException e) {
                //积分赠送失败-入库记录
                ActCallFailedActionCntDO actionCntDO = new ActCallFailedActionCntDO();
                actionCntDO.uid = uid.toString();
                actionCntDO.TaskId = taskId.toString();

                ActCallFailedDO actCallFailedDO = new ActCallFailedDO();
                actCallFailedDO.traceId = traceId;
                actCallFailedDO.uid = uid.toString();
                actCallFailedDO.actCode = StringUtils.split(scene, "_")[1];
                actCallFailedDO.actionType = CallFailedActionTypeEnum.POINTS.getValue();
                actCallFailedDO.actionContext = JSON.toJSONString(actionCntDO);
                actCallFailedDO.actionTime = DateTime.now();
                actCallFailedDO.lastModifyTime = DateTime.now();
                callFailedMapper.insert(actCallFailedDO);
            }
        }

        return JSON.toJSONString(Result.buildSuccessResult("200", "成功", null));
    }


    /**
     * 大直播随堂测-同步教务-回调方法
     * @param request
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/9/18 15:15
     */
    @RequestMapping("/callback_livevideo")
    @ResponseBody
    public String callback_livevideo(HttpServletRequest request) {
        Long uid = 0L;
        String pid = "";
        String scene = "";
        Long taskId = 0L;
        String actCode = "";
        String emNo = "";//EM号或者0X手机号

        String traceId = Optional.ofNullable(tracer.currentSpan())
                .map(Span::context)
                .map(TraceContext::traceIdString)
                .orElseGet(() -> UUID.randomUUID().toString());

        String content = getPostData(request);
        if (StringUtils.isEmpty(content)) {
            return JSON.toJSONString(Result.buildErrorResult("-1", "参数缺失", null));
        }
        WjxSurveyResultDO obj = JSON.parseObject(content, WjxSurveyResultDO.class);
        if (obj != null) {
            if (obj.getEmuid() != null) {
                uid = obj.getEmuid().longValue();
            }
            pid = obj.getPid();
            scene = obj.getEmscene();
            emNo = obj.getEmno();
        } else {
            return JSON.toJSONString(Result.buildErrorResult("-1", "参数反序列化失败", null));
        }

        if (StringUtils.isEmpty(scene)) {
            return JSON.toJSONString(Result.buildErrorResult("-1", "scene未查询到", null));
        }

        //无uid 根据em账号获取uid和pid
        if (uid.equals(0L) && StringUtils.isEmpty(pid) && !StringUtils.isEmpty(emNo)) {
            //根据emNo获取uid和pid
            pid = userService.getHighPIDByAccount(emNo);

            UserLoginIdInfoVO userLoginIdInfo = userService.GetLoginIDInfoByAccount(emNo);
            if (userLoginIdInfo != null) {
                uid = userLoginIdInfo.PID;
            }
        }

        try {
            //同步教务接口
            String ret = OkHttpUtil.postJsonParams(receiveSurveyJWUrl, JSON.toJSONString(obj));
            if(!StringUtils.isEmpty(ret)){
                AppApiResult<Integer> result = JSON.parseObject(ret, new TypeReference<AppApiResult<Integer>>() {
                });

                if (result.getCode() != 0) {
                    return JSON.toJSONString(Result.buildErrorResult("大直播随堂测同步教务失败"));
                }
            }

        }catch(Exception exp){
            log.error("callback_livevideo.receive Error:" + exp);
        }

        return JSON.toJSONString(Result.buildSuccessResult("200", "成功", null));
    }
}
