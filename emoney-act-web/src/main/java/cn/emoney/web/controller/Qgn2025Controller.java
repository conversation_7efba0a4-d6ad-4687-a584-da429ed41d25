package cn.emoney.web.controller;

import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LoginService;
import cn.emoney.service.MobileService;
import cn.emoney.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2025-02-25
 */
@Controller
@RequestMapping("/qgn2025")
public class Qgn2025Controller extends BaseController {
    @Autowired
    private LoginService loginService;
    @Autowired
    private UserService userService;
    @Autowired
    private MobileService mobileService;

    @RequestMapping("/index0303")
    public String index0303(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250303";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250303";
    }

    @RequestMapping("/index0401")
    public String index0401(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250401";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250401";
    }

    @RequestMapping("/index0401mini")
    public String index0401mini(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250401";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250401mini";
    }

    @RequestMapping("/index0403")
    public String index0403(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250403";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250403";
    }

    @RequestMapping("/index0506")
    public String index0506(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250506";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250506";
    }

    @RequestMapping("/index0506mini")
    public String index0506mini(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250506";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250506mini";
    }

    @RequestMapping("/index0513")
    public String index0513(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250513";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250513";
    }

    @RequestMapping("/index0526")
    public String index0526(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250526";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250526";
    }

    @RequestMapping("/index0609")
    public String index0609(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250609";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250609";
    }

    @RequestMapping("/index0626")
    public String index0626(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250626";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250626";
    }

    @RequestMapping("/index0626mini")
    public String index0626mini(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250626";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250626mini";
    }

    @RequestMapping("/index0701")
    public String index0701(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250701";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250701";
    }

    @RequestMapping("/index0807")
    public String index0807(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250807";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250807";
    }

    @RequestMapping("/index0807mini")
    public String index0807mini(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250807";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250807mini";
    }

    @RequestMapping("/index0814")
    public String index0814(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "privilege20250814";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "qgn2025/20250814";
    }
}
