package cn.emoney.web.controller;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.pojo.CustServiceDO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LoginService;
import cn.emoney.service.redis.RedisService;
import cn.emoney.web.config.RedisConfig;
import cn.hutool.core.lang.Console;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hashids.Hashids;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.view.RedirectView;
import sun.misc.BASE64Encoder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.MessageFormat;
import java.util.Random;
import java.util.logging.LogManager;

/**
 * 服务专员
 * <AUTHOR>
 * @date 2023/12/6 17:57
 */
@Slf4j
@Controller
@RequestMapping("/custservice")
public class CustServiceController extends BaseController {
    @Autowired
    private LoginService loginService;

    @Autowired
    private RedisService redisService;

    private final RedisService cmpRedisService;

    private static final String redisKey_token_QRInfo = RedisConstants.RedisKey_Pre_Activity +"custservice:qrInfo:";
    private static final String redisKey_CustServiceInfo = "active-cycle-id-333:";
    /**
     * 可以自定义盐值，增加安全性
     */
    private static final String SALT = "custService";

    /**
     * 设置生成的ID长度
     */
    private static final int LENGTH = 16;

    private static final Long ExpiredTime = 30L;

    public CustServiceController(@Qualifier("cmpVisitRedisManager") RedisService cmpRedisService) {
        this.cmpRedisService = cmpRedisService;
    }

    /**
     * 专员二维码页
     * <AUTHOR>
     * @date 2023/12/6 20:45
     * @param request
     * @param response
     * @param model
     * @return java.lang.String
     */
    @RequestMapping("/qr")
    public String index(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "service20231211";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("custServiceInfo", getCustServiceInfo(loginUser.getMobileX()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "custservice/qr";
    }

    /**
     * 中转页
     * <AUTHOR>
     * @date 2023/12/6 20:39
     * @param request
     * @param response
     * @param model
     * @return java.lang.String
     */
    @RequestMapping("/qrtransfer")
    public String qr_transfer(HttpServletRequest request, HttpServletResponse response, Model model) {
        String token = request.getParameter("token");
        String tipMsg = "";
        if (StringUtils.isEmpty(token)) {
            tipMsg = "非法访问";
        }
        String key = redisKey_token_QRInfo + token;
        Object obj = redisService.get(key);
        if (obj == null) {
            tipMsg = "二维码已失效，请重新扫码进入";
        }

        //获取目标url
        String targetUrl = (String)obj;

        model.addAttribute("tipMsg", tipMsg);
        model.addAttribute("targetUrl", targetUrl);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "custservice/qrtransfer";
    }

    /**
     * 专员信息页
     * <AUTHOR>
     * @date 2023/12/6 20:39
     * @param request
     * @param response
     * @param model
     * @return java.lang.String
     */
    @RequestMapping("/info")
    public String info(HttpServletRequest request, HttpServletResponse response, Model model) {
        String mobile0x = request.getParameter("mobile");
        String weChatImg = "";
        if (!StringUtils.isEmpty(mobile0x)) {
            CustServiceDO custServiceDO = getCustServiceInfo(mobile0x);
            if(custServiceDO!=null){
                weChatImg = custServiceDO.userWechatLink;
            }
        }

        model.addAttribute("wechatImg", weChatImg);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "custservice/info";
    }

    /**
     * 获取二维码图片地址
     * <AUTHOR>
     * @date 2023/12/6 19:03
     * @return java.lang.String
     */
    @RequestMapping("/getqrimgurl")
    @ResponseBody
    public Result<String> getQrImgUrl(HttpServletRequest request) {
        String transferUrl = request.getParameter("transferurl");
        String uid = request.getParameter("uid");
        String mobileX = request.getParameter("mobilex");

        if (StringUtils.isEmpty(mobileX)) {
            return Result.buildErrorResult("用户未登录");
        }

        //1.获取唯一token
        String token = getUniqueID(uid);
        //2.获取二维码目标url
        String targetUrl = getQrTargetUrl(mobileX);

        if (StringUtils.isEmpty(targetUrl)) {
            return Result.buildErrorResult("未查询到专员信息");
        }
        //3.token和目标要素存入redis 30s有效期
        String key = redisKey_token_QRInfo + token;
        redisService.set(key, targetUrl, ExpiredTime);

        String Url = MessageFormat.format(transferUrl + "?token={0}", token);
        byte[] bytes = QrCodeUtil.generatePng(Url, 300, 300);
        String byteStr = new BASE64Encoder().encodeBuffer(bytes).trim();
        String dataURL = "data:image/png;base64," + byteStr;

        return Result.buildSuccessResult(dataURL);
    }

    /**
     * 拼接二维码目标页面
     * <AUTHOR>
     * @date 2023/12/11 14:23
     * @param mobileX
     * @return GetQrTargetUrl
     * https://work.weixin.qq.com/kfid/kfca4dda2c066c3a116?enc_scene=ENC2yn5AEJetbEKPbL5F2YLvF3ypUQeE81gfGainfdx7D7U&scene_param=拼上密文手机号码
     */
    public String  getQrTargetUrl(String mobileX) {
        String targetUrl = "";
        CustServiceDO custServiceDO = getCustServiceInfo(mobileX);
        if (custServiceDO != null) {
            //拼接二维码目标url
            targetUrl = MessageFormat.format(custServiceDO.kfUrl + "&scene_param={0}",mobileX);
        }

        return targetUrl;
    }

    /**
     * 获取用户服务专员信息
     * <AUTHOR>
     * @date 2023/12/11 14:34
     * @param mobileX
     * @return cn.emoney.pojo.CustServiceDO
     */
    public CustServiceDO getCustServiceInfo(String mobileX) {
        if (!StringUtils.isEmpty(mobileX)) {

            String key = redisKey_CustServiceInfo + mobileX.substring(2);
            String ret = cmpRedisService.get(key,String.class);
            CustServiceDO custServiceDO = JsonUtil.toBean(ret,CustServiceDO.class);

            return custServiceDO;
        }

        return null;
    }


    /**
     * 生成唯一ID
     * <AUTHOR>
     * @date 2023/12/6 19:45
     * @param uid
     * @return java.lang.String
     */
    public String getUniqueID(String uid) {
        Hashids hashids = new Hashids(SALT + new Random(), LENGTH);
        String uniqueID = hashids.encode(Long.parseLong(uid));

        return uniqueID;
    }
}
