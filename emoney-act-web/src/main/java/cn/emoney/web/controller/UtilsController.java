package cn.emoney.web.controller;

import cn.emoney.common.result.Result;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.pojo.bo.Benefit588DTO;
import cn.emoney.pojo.vo.result.ProductConfig;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.ProductConfigService;
import cn.emoney.service.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023-05-04
 */
@Controller
@RequestMapping("/utils")
@Validated
@Slf4j
public class UtilsController {
    @Autowired
    private ProductConfigService productConfigService;

    @Autowired
    private RedisService redisService;
    /**
     * 获取当前系统时间日期部分
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/5/4 11:37
     */
    @RequestMapping("/getsystemdate")
    @ResponseBody
    public String getSystemDate() {
        Date nowDate = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String nowDateStr = sdf.format(nowDate);
        return "var nowdate = '" + nowDateStr + "';";
    }

    @RequestMapping("/timestamp")
    @ResponseBody
    public String getTimestamp(String callback) {
        long unixTimeStamp = System.currentTimeMillis();
        String data = "";
        if (StringUtils.hasLength(callback)) {
            data = String.format("%s(%d)", callback, unixTimeStamp);
        } else {
            data = String.format("%d", unixTimeStamp);
        }
        return data;
    }

    /**
     * 获取客户端ip
     *
     * @param request
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/5/4 14:13
     */
    @RequestMapping("/getclientip")
    @ResponseBody
    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Cdn-Src-Ip");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (ip.indexOf(",") > -1) {
            ip = ip.substring(0, ip.indexOf(","));
        }
        return ip;
    }

    @RequestMapping("/getproductconfig")
    @ResponseBody
    @CrossOrigin
    public Result<String> getProductConfig(String configkey) {
        if (StringUtils.isEmpty(configkey)) {
            return Result.buildErrorResult("-1","缺少参数configKey");
        }

        String redisKey = "EMoney.Act.productConfig:" + configkey;
        String configContent = redisService.get(redisKey,String.class);

        if (configContent == null) {
            ProductConfig config = productConfigService.getConfig(configkey);
            if (config != null) {
                configContent = config.getConfigContent();
                redisService.set(redisKey, configContent, 5L, TimeUnit.MINUTES);
            }
        }

        return Result.buildSuccessResult(configContent);
    }
}
