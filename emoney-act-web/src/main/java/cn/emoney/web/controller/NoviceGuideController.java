package cn.emoney.web.controller;

import java.util.Date;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.pojo.NoviceGuideUserInfoDO;
import cn.emoney.pojo.TbSyncOaUserInfoDO;
import cn.emoney.service.NoviceGuideService;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2021-12-30
 */
@Controller
@RequestMapping("/noviceguide")
@Slf4j
public class NoviceGuideController {

    @Autowired
    private NoviceGuideService noviceGuideService;

    @Autowired
    private RedisService redisService;

    @RequestMapping
    public String index(HttpServletRequest request, Model model) {
        log.info("新手指引弹出请求; sso:" + request.getQueryString());
        SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
        String uid = "**********";
        String qr = "";
        if (ssoResult != null) {
            uid = ssoResult.getUid();
        }
        String isShow = "1";
        //检查是否已经弹出
        NoviceGuideUserInfoDO noviceGuideUserInfoDO = noviceGuideService.queryNoviceGuideUserInfoByUid(uid);
        if (noviceGuideUserInfoDO == null) {
            //获取业务员信息
            TbSyncOaUserInfoDO tbSyncOaUserInfoDO = noviceGuideService.queryByUid(uid);
            if (tbSyncOaUserInfoDO != null) {
                qr = tbSyncOaUserInfoDO.getQrCode();

                if (!StrUtil.isEmpty(qr)) {
                    //保存弹出记录
                    NoviceGuideUserInfoDO noviceGuideUserInfo = new NoviceGuideUserInfoDO();
                    noviceGuideUserInfo.setUid(uid);
                    noviceGuideUserInfo.setUsercode(tbSyncOaUserInfoDO.getUsercode());
                    noviceGuideUserInfo.setQrcode(tbSyncOaUserInfoDO.getQrCode());
                    noviceGuideUserInfo.setCreatetime(new Date());
                    noviceGuideService.insertNoviceGuideUserInfo(noviceGuideUserInfo);

                    log.info("新手指引弹出; uid:" + uid + "---------业务员信息：" + JSON.toJSONString(tbSyncOaUserInfoDO));
                }
            } else {
                isShow = "";
            }
        } else {
            isShow = "";
            qr = noviceGuideUserInfoDO.getQrcode();
        }
        model.addAttribute("qr", qr);
        model.addAttribute("uid", uid);
        model.addAttribute("isShow", isShow);
        return "noviceguide/noviceguide";
    }
}
