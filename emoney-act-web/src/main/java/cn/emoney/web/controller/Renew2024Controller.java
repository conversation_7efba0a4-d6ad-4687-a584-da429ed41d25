package cn.emoney.web.controller;

import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.pojo.bo.*;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-26
 */
@Controller
@RequestMapping("/renew2024")
public class Renew2024Controller extends BaseController {
    @Autowired
    private LoginService loginService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private PointService pointService;

    @Autowired
    private Benefit588Service benefit588Service;

    @Autowired
    private UserService userService;

    private static String coupon30_20240101 = "cp-1221212114155458";
    private final static String pointTaskID_20240506 = "1781238594530922496";
    private final static String activityID_coupon_20240506 = "cp-1240419163116133";
    private static String coupon30_20240531 = "cp-1240528135605759";
    private static String coupon_20240719 = "cp-1240716095440184";
    private static String coupon_20240801 = "cp-1240724141111384";

    @RequestMapping("/index010101")
    public String index_2024010101(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20240101_renew2023";
        String pid = request.getParameter("pid");
        boolean hasCoupon30 = false;
        Double point = 0.0;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }else{
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                hasCoupon30 = hasCoupon(coupon30_20240101,loginUser.getMobileX());
            }
            if(!hasCoupon30){
                //查询积分总数
                point = QueryPoint(loginUser.getUid());
            }
        }


        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon30", hasCoupon30);
        model.addAttribute("allPoint", point);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/2024010101";
    }

    @RequestMapping("/index010102")
    public String index_2024010102(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20240101_renew2023";
        String pid = request.getParameter("pid");
        boolean hasCoupon30 = false;
        Double point = 0.0;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }else{
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                hasCoupon30 = hasCoupon(coupon30_20240101,loginUser.getMobileX());
            }
            if(!hasCoupon30){
                //查询积分总数
                point = QueryPoint(loginUser.getUid());
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon30", hasCoupon30);
        model.addAttribute("allPoint", point);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/2024010102";
    }

    @RequestMapping("/index0329")
    public String index_20240329(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20240401_renew";
        String pid = request.getParameter("pid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/20240329";
    }
    @RequestMapping("/index0412")
    public String index_20240412(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20240412_renew";
        String pid = request.getParameter("pid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/20240412";
    }
    @RequestMapping("/index0506")
    public String index_20240506(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20240506_renew";
        String pid = request.getParameter("pid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/20240506";
    }

    @RequestMapping("/index0531")
    public String index_20240531(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20240531_renew";
        String pid = request.getParameter("pid");
        Boolean hasCoupon30 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有30元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && coupon30_20240531.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon30 = true;
                    }
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("hasCoupon30", hasCoupon30);
        return "renew2024/20240531";
    }

    @RequestMapping("/index0618")
    public String index0618(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20240618_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/20240618";
    }


    @RequestMapping("/index0719")
    public String index0719(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20240719_renew";
        String pid = request.getParameter("pid");
        boolean hasCoupon200 = false;
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        if (loginUser.getMobileX() != null) {
            hasCoupon200 = hasCoupon(coupon_20240719,loginUser.getMobileX());
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("hasCoupon200", hasCoupon200);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/20240719";
    }

    @RequestMapping("/index0801")
    public String index0801(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20240801_renew";
        String pid = request.getParameter("pid");
        boolean hasCoupon90 = false;
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        if (loginUser.getMobileX() != null) {
            hasCoupon90 = hasCoupon(coupon_20240801,loginUser.getMobileX());
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("hasCoupon90", hasCoupon90);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/20240801";
    }

    @RequestMapping("/index0906")
    public String index0906(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20240906_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/20240906";
    }

    @RequestMapping("/index1014")
    public String index1014(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20241014_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/20241014";
    }

    @RequestMapping("/index1123")
    public String index1123(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20241123_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/20241123";
    }

    @RequestMapping("/index1212")
    public String index1212(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20241212_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2024/20241212";
    }

    /**
     * 福袋领取-20240506小智盈续费
     * <AUTHOR>
     * @date 2024/4/24 上午10:22
     * @param request
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/sendLuckyBag")
    @ResponseBody
    public Result<String> sendLuckyBag(HttpServletRequest request) {
        //获取客户端传入actCode参数，并判断非空
        String actCode = request.getParameter("actCode");
        if (actCode == null || actCode.isEmpty()) {
            return Result.buildErrorResult("actCode不能为空");
        }
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        return benefit588Service.sendPointAndCoupon(actCode,loginUser.getUid(),loginUser.getPid(),pointTaskID_20240506,activityID_coupon_20240506,90);
    }

    /**
     * 查询是否有指定优惠券
     * <AUTHOR>
     * @date 2023/12/27 15:45
     * @param coupon
     * @param mobilex
     * @return boolean
     */
    public boolean hasCoupon(String coupon,String mobilex){
        //查询是否有30元优惠券
        List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, mobilex);
        Date now = new Date();
        for (QueryCouponListDTO item : list) {
            if (item.COUPON_ISENABLE == 0 && coupon.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取可用积分
     *
     * @param uid
     * @return java.lang.Double
     * <AUTHOR>
     * @date 2022/2/17 14:17
     */
    public Double QueryPoint(String uid) {
        Double point = Double.parseDouble("0");
        if (uid != null) {
            List<PointQueryDataDTO> list = pointService.pointQuerySummary(uid);
            for (PointQueryDataDTO item : list) {
                int pointStatus = item.pointStatus;
                if (pointStatus == 2 || pointStatus == 3) {
                    point += item.pointTotal;
                }
            }
        }
        return point;
    }
}
