package cn.emoney.web.controller;

import cn.emoney.common.result.Result;
import cn.emoney.common.result.ResultInfo;
import cn.emoney.pojo.bo.CourseDetailDTO;
import cn.emoney.pojo.bo.VideoStateSession;
import cn.emoney.pojo.bo.VideoUserSession;
import cn.emoney.pojo.vo.UserLoginIdInfoVO;
import cn.emoney.pojo.vo.ValidateUserVO;
import cn.emoney.pojo.vo.VideoWatchVO;
import cn.emoney.service.CourseService;
import cn.emoney.service.SMSVerificationService;
import cn.emoney.service.SsoAuthService;
import cn.emoney.service.UserService;
import cn.emoney.service.impl.ProspectService;
import cn.emoney.service.limiter.FrequencyLimit;
import cn.emoney.service.security.SecurityService;
import cn.emoney.web.client.ClientIP;
import cn.emoney.web.security.SecurityArg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.CacheControl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.time.Duration;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Validated
@Controller
@RequestMapping("/course")
public class VideoController {
    private final UserService userService;
    private final SsoAuthService ssoAuthService;
    private final CourseService courseService;
    private final SecurityService securityService;
    private final ProspectService prospectService;
    private final SMSVerificationService smsVerificationService;
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String PERMISSION_DENIED = "denied";
    private static final java.util.regex.Pattern MOBILE_PATTERN = java.util.regex.Pattern.compile("^1[3-9]\\d{9}$");

    public VideoController(UserService userService, SsoAuthService ssoAuthService, CourseService courseService,
                           SecurityService securityService, ProspectService prospectService,
                           SMSVerificationService smsVerificationService) {
        this.userService = userService;
        this.ssoAuthService = ssoAuthService;
        this.courseService = courseService;
        this.securityService = securityService;
        this.prospectService = prospectService;
        this.smsVerificationService = smsVerificationService;
    }


    @GetMapping("detail/{id}")
    @ResponseBody
    public CourseDetailDTO detail(@PathVariable Integer id) {
        return courseService.detail(id);
    }

    /**
     * Level:
     * 0: [NO-AUTH] 无需验证: 只用户打开即可观看，无需任何验证方式
     * 1: [AUTH] 用户认证, 分 0FA 与 1FA
     * 2: [AUTH(1FA) + PID] 验证版本
     * 9: [CODE] 课程码验证: 指设置课程口令，输入正确的课程口令，即可登录观看
     *
     * @param id
     * @param model
     * @return
     */
    @GetMapping({"{type:public|auth|auth-pid|phone|phone-pid|pw|pw-pid|sms|sms-pid|code}/{id}"})
    public ModelAndView play(@PathVariable Integer id,
                             @PathVariable String type,
                             @SessionAttribute(value = "user", required = false) VideoUserSession user,
                             @RequestParam(required = false) String token,
                             HttpSession session,
                             Model model) {
        if (user == null && token != null) {
            VideoUserSession ssoUser = ssoAuthService.auth(token)
                    .map(sso -> createSession(sso.getUserName(), true, Long.valueOf(sso.getUid())))
                    .orElse(null);
            if (ssoUser != null) {
                session.setAttribute("user", ssoUser);
                user = ssoUser;
            }
        }

        boolean accountCheck = type.startsWith("phone");
        boolean accountAuth = type.startsWith("pw") || type.startsWith("sms") || type.startsWith("auth");
        boolean codeCheck = "code".equals(type);
        boolean pidCheck = type.endsWith("pid");

        model.addAttribute("authType", type);

        CourseDetailDTO course = courseService.detail(id);
        if (course == null) {
            return new ModelAndView("video/404", HttpStatus.NOT_FOUND);
        }

        boolean passed = true;
        if (accountAuth) {
            if (user == null || !user.isConfirm()) {
                passed = false;
                model.addAttribute(PERMISSION_DENIED, user == null ? "needUserAuth" : "needAuthUpgrade");
            }
        }

        if (passed && accountCheck) {
            if (user == null) {
                passed = false;
                model.addAttribute(PERMISSION_DENIED, "needUserCheck");
            }
        }

        if (passed && pidCheck) {
            String pid = user.getPid();
            if (course.getVersionList().stream().noneMatch(s -> Objects.equals(s.getPId(), pid))) {
                passed = false;
                model.addAttribute(PERMISSION_DENIED, "pidNotMatch");
            }
        }

        if (passed && codeCheck) {
            VideoStateSession state = (VideoStateSession) session.getAttribute("video:state:" + id);
            if (state == null || !Objects.equals(state.getCourseCode(), course.getAccessPassword())) {
                model.addAttribute(PERMISSION_DENIED, "needCodeCheck");
            }
        }

        String playUrl = course.getCoursePlayBackUrl();
        boolean isVod = StringUtils.hasText(playUrl);
        if (!isVod) {
            playUrl = course.getCourseLiveUrl();
        }

        VideoWatchVO watchVO = new VideoWatchVO();
        watchVO.setCourseTime(Optional.ofNullable(course.getCourseBeginTime()).map(formatter::format).orElse(null));
        watchVO.setCourseName(course.getCourseName());
        watchVO.setOwnerId(model.getAttribute(PERMISSION_DENIED) == null ?
                playUrl.substring(playUrl.lastIndexOf("-") + 1) :
                null);
        watchVO.setMediaType(isVod ? "vod" : "live");

        model.addAttribute("courseId", id);
        model.addAttribute("video", watchVO);
        model.addAttribute("APPID", 1);
        return new ModelAndView("video/index");
    }

    @PostMapping("user-login")
    @ResponseBody
    public Result<Void> userLogin(@SecurityArg String username,
                                  @SecurityArg @NotBlank String userpwd,
                                  @SecurityArg @Pattern(regexp = "^[01]$", message = "参数错误") String loginmod,
                                  @SessionAttribute(value = "user", required = false) VideoUserSession curUser,
                                  HttpSession session) {
        VideoUserSession newUser = createSession(username, true, curUser);
        if (newUser != null && !newUser.isInvalidPid()) {
            // 根据用户名和 PID 检测密码
            ResultInfo<ValidateUserVO> resultInfo = userService.validateUserPasswordByPID(
                    newUser.getUsername(), userpwd, newUser.getPid());

            if (resultInfo.isSuccess()) {
                if ("1".equals(loginmod)) {
                    session.setMaxInactiveInterval(60 * 60 * 24 * 30);
                }
                session.setAttribute("user", newUser);
                return Result.buildSuccessResult();
            }
        }
        if (MOBILE_PATTERN.matcher(username).matches()) {
            prospectService.addMobile(username, "COURSE:USERNAME", false);
        }
        return Result.buildErrorResult("用户名或密码错误，请重试");
    }

    @PostMapping("sms-login")
    @ResponseBody
    public Result<Void> smsLogin(@SecurityArg @NotBlank @Pattern(regexp = "^\\d{4}$", message = "验证码错误") String code,
                                 @SecurityArg @NotBlank @Pattern(regexp = "^[01]$", message = "参数错误") String loginmod,
                                 @SessionAttribute(value = "sms-login-request", required = false) @NotBlank(message = "请先获取验证码") String loginRequest,
                                 @SessionAttribute(value = "user", required = false) VideoUserSession user,
                                 HttpSession session) {
        SMSVerificationService.Result verify = smsVerificationService.verifyCode(loginRequest, code);
        if (verify.getStatus() == 0) {
            return Result.buildErrorResult("验证码错误");
        }
        session.removeAttribute("sms-login-request");
        if (verify.getStatus() == -1) {
            return Result.buildErrorResult("V9000", "验证码已过期，请重新获取");
        }
        String mobile = verify.getMobile();

        VideoUserSession userSession = createSession(mobile, true, user);
        if (userSession == null || userSession.isInvalidPid()) {
            // 已注册但未绑定任何收费产品，或者新用户
            prospectService.addMobile(mobile, "COURSE:SMS", true);
            return Result.buildErrorResult("V9999", "还不是注册账户哦，请先注册");
        }

        if ("1".equals(loginmod)) {
            session.setMaxInactiveInterval(60 * 60 * 24 * 30);
        }
        session.setAttribute("user", userSession);
        return Result.buildSuccessResult();
    }

    @PostMapping("sms-code")
    @ResponseBody
    public Result<Void> getSMSCode(@SecurityArg @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号错误") String mobile,
                                   @SessionAttribute(value = "user", required = false) VideoUserSession user,
                                   @ClientIP String ipAddr,
                                   HttpSession session) {
        if (mobile == null) {
            if (user == null || user.getUsername() == null || !MOBILE_PATTERN.matcher(user.getUsername()).matches()) {
                return Result.buildErrorResult("手机号不能为空");
            } else {
                mobile = user.getUsername();
            }
        }

        prospectService.addMobile(mobile, "COURSE:SMS", false);
        String loginRequest = smsVerificationService.createCode(mobile, ipAddr, "187");
        session.setAttribute("sms-login-request", loginRequest);
        return Result.buildSuccessResult();
    }

    @PostMapping("mobile-check")
    @ResponseBody
    @FrequencyLimit(value = "'act:check-mobile:'+#client", frequency = 5, duration = "1m")
    @FrequencyLimit(value = "'act:check-mobile:'+#mobile", frequency = 1, duration = "1m")
    @FrequencyLimit(value = "'act:check-mobile:'+#client", frequency = 99, cron = "0 0 3 * * ?")
    @FrequencyLimit(value = "'act:check-mobile:'+#mobile", frequency = 10, cron = "0 0 3 * * ?")
    // @el(mobile: java.lang.String, client: java.lang.String)
    public Result<Void> checkByMobile(@SecurityArg @NotBlank @Pattern(regexp = "1[3-9]\\d{9}", message = "手机号错误") String mobile,
                                      @ClientIP String client,
                                      HttpSession session) {
        VideoUserSession userSession = createSession(mobile, false);
        //首先获取Pid
        if (userSession == null || userSession.isInvalidPid()) {
            prospectService.addMobile(mobile, "COURSE:CHECK", false);
            // 已注册但未绑定任何收费产品，或者新用户
            return Result.buildErrorResult("V9999", "还不是注册账户哦，请先注册");
        }
        session.setAttribute("user", userSession);
        return Result.buildSuccessResult();
    }

    @PostMapping("code-check")
    @ResponseBody
    public Result<Void> checkByCourseCode(Integer id, String code, HttpSession session) {
        CourseDetailDTO course = courseService.detail(id);
        if (Objects.equals(Objects.requireNonNull(course).getAccessPassword(), code)) {
            VideoStateSession stateSession = new VideoStateSession();
            stateSession.setCourseCode(code);
            session.setAttribute("video:state:" + id, stateSession);
            return Result.buildSuccessResult();
        }
        return Result.buildErrorResult("课程码错误");
    }

    @GetMapping("loginOut")
    @ResponseBody
    public Result<Void> toolLoginOut(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }
        return Result.buildSuccessResult();
    }

    @GetMapping("check")
    @ResponseBody
    public ResponseEntity<String> getPublicKey() {
        String publicKey = securityService.getPublicKey();
        return ResponseEntity.ok()
                .cacheControl(CacheControl.maxAge(Duration.ofMinutes(5)).cachePrivate().mustRevalidate())
                .body(publicKey);
    }

    private VideoUserSession createSession(String username, boolean confirm, @Nullable VideoUserSession session) {
        if (session != null && (username == null || Objects.equals(username, session.getUsername()))) {
            return createSession(username, confirm, session.getUid());
        }
        return createSession(username, confirm);
    }

    private VideoUserSession createSession(String username, boolean confirm) {
        return createSession(username, confirm, (Long) null);
    }

    private VideoUserSession createSession(String username, boolean confirm, @Nullable Long uid) {
        return createSession(username, confirm, uid, null);
    }

    private VideoUserSession createSession(String username, boolean confirm, @Nullable Long uid, @Nullable String pid) {
        if (StringUtils.isEmpty(username)) {
            return null;
        }

        if (uid == null) {
            Optional<Long> uidOptional = Optional.ofNullable(userService.GetLoginIDInfoByAccount(username))
                    .map(UserLoginIdInfoVO::getPID);
            if (!uidOptional.isPresent()) {
                return null;
            }
            uid = uidOptional.get();
        }

        if (pid == null) {
            pid = userService.GetAccountPID(username);
        }
        VideoUserSession userSession = new VideoUserSession();
        userSession.setUid(uid);
        userSession.setPid(pid);
        userSession.setUsername(username);
        userSession.setConfirm(confirm);
        return userSession;
    }
}
