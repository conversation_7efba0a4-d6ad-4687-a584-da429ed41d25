package cn.emoney.web.controller;

import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.pojo.*;
import cn.emoney.pojo.bo.PointQueryDataDTO;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.*;
import cn.emoney.service.redis.RedisService;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import groovyjarjarpicocli.CommandLine;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-04-27
 */

@Controller
@RequestMapping("/renewds")
public class RenewDSController extends BaseController {

    @Autowired
    private LoginService loginService;

    @Autowired
    private PointService pointService;

    @Autowired
    private PayService payService;

    @Autowired
    private UserService userService;

    @Autowired
    private BenefitDsService benefitDsService;

    @RequestMapping("/index0504")
    public String index_********(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewDS********";
        Double allPoint = 0.0;
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    String pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            allPoint = QueryPoint(loginUser.getUid());
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("allPoint", allPoint.intValue());
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS/********";
    }

    @RequestMapping("/index0625")
    public String index_********(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewDS********";
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    String pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS/********";
    }

    @RequestMapping("/index0715")
    public String index_********(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewDS********";
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    String pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS/********";
    }

    @RequestMapping("/index0912")
    public String index_20230912(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewDS20230912";
        String day = request.getParameter("day");
        if (StringUtils.isEmpty(day)) {
            day = "0";
        }
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    String pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }

        List<ActDsSignBenefitDO> signDayList = new ArrayList<>();
        List<ActDsSignRecordDO> signRecordDOList = new ArrayList<>();
        ActDsSignBenefitDO appendPrize = new ActDsSignBenefitDO();
        int dayCount = 0;
        int currentDay = -1;

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            //获取用户领取奖励记录
            signRecordDOList = benefitDsService.getSignRecords(loginUser.getUid());
        }

        //获取用户签到信息
        AppApiResult<ActDsSignDetail> result = benefitDsService.getUserSignInfo(loginUser.getUid(), day);
        if (result.getCode() == 0 && result.getDetail() != null) {
            ActDsSignDetail detail = result.getDetail();
            signDayList = detail.dayList;
            appendPrize = detail.appendPrize;
            dayCount = detail.dayCount;

            for (ActDsSignBenefitDO item :
                    signDayList) {
                if (item.isCurrent) {
                    currentDay = item.day;
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("signDayList", signDayList);
        model.addAttribute("appendPrize", appendPrize);
        model.addAttribute("signRecordList", signRecordDOList);
        model.addAttribute("dayCount", dayCount);
        model.addAttribute("day", day);
        model.addAttribute("currentDay", currentDay);
        return "renewDS/20230912";
    }

    @RequestMapping("/index1009")
    public String index_20231009(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewDS20231009";
        String day = request.getParameter("day");
        String pid = request.getParameter("pid");
        if (StringUtils.isEmpty(day)) {
            day = "0";
        }
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        List<ActDsDailyInfoDO> dayList = new ArrayList<>();
        List<ActDsSignRecordDO> signRecordDOList = new ArrayList<>();
        Long dayCount = Long.valueOf(0);

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            //获取用户领取奖励记录
            signRecordDOList = benefitDsService.getDailyRecords(loginUser.getUid());
        }

        //获取用户活动信息
        AppApiResult<ActDsDailyChanceDO> result = benefitDsService.getDailyInfo(loginUser.getUid(), loginUser.getPid(), day);
        if (result.getCode() == 0 && result.getDetail() != null) {
            ActDsDailyChanceDO detail = result.getDetail();
            dayList = detail.dayList;
            dayCount = detail.dayCount;
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("dayList", dayList);
        model.addAttribute("userRecordList", signRecordDOList);
        model.addAttribute("dayCount", dayCount);
        model.addAttribute("day", day);
        return "renewDS/20231009";
    }

    @RequestMapping("/index1016")
    public String index_20231016(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewDS20231016";
        String pid = request.getParameter("pid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS/20231016";
    }
    @RequestMapping("/index1025")
    public String index_20231025(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "renewDS20231025";
        String pid = request.getParameter("pid");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renewDS/20231025";
    }

    @RequestMapping("/ispayment")
    @ResponseBody
    public Result<Boolean> IsPayment(HttpServletRequest request) {
        String actCode = request.getParameter("actCode");
        String token = request.getParameter("token");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        String pids = "888020000,888080000";
        PayRequestDO requestDO = new PayRequestDO();
        requestDO.pids = pids;
        requestDO.groupCode = "3";
        requestDO.clientSource = 1002;
        requestDO.businessType = "biztyp-dsxf";
        requestDO.phoneEncrypt = loginUser.mobileX;
        requestDO.platformId = StringUtils.isEmpty(token) ? "11" : "8";

        AppApiResult<List<UserLogisticsPackageDO>> result = payService.getPayPackageList(requestDO);
        if (result.getCode() == 0) {
            List<UserLogisticsPackageDO> list = result.getDetail();
            UserLogisticsPackageDO packageDO = list.stream().filter(x -> x.getPID().equals(pids)).findFirst().get();
            if (packageDO != null) {
                for (LogisticsPackageViewModel item :
                        packageDO.List) {
                    if (item.ActivityType == 7) {
                        return Result.buildSuccessResult(true);
                    }
                }
            }
        }

        return Result.buildSuccessResult(false);
    }

    /**
     * 获取可用积分
     *
     * @param uid
     * @return java.lang.Double
     * <AUTHOR>
     * @date 2022/2/17 14:17
     */
    public Double QueryPoint(String uid) {
        Double point = Double.parseDouble("0");
        if (uid != null) {
            List<PointQueryDataDTO> list = pointService.pointQuerySummary(uid);
            for (PointQueryDataDTO item : list) {
                int pointStatus = item.pointStatus;
                if (pointStatus == 2 || pointStatus == 3) {
                    point += item.pointTotal;
                }
            }
        }
        return point;
    }

    /**
     * 获取用户定金支付状态
     *
     * @param request
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/6/15 16:43
     */
    @RequestMapping("/getjunepaystatus")
    @ResponseBody
    public String GetJunePayStatus(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String jsonpCallback1 = request.getParameter("jsonpCallback");
        String mobileX = request.getParameter("mobilex");
        String uid = request.getParameter("uid");
        String status = "0";

        if (StringUtils.isEmpty(jsonpCallback)) {
            jsonpCallback = jsonpCallback1;
        }

        if (StringUtils.isEmpty(mobileX) && !StringUtils.isEmpty(uid)) {
            //通过uid获取
            List<BindAccountVO> list = userService.GetBindAccountList(uid);
            if (list != null) {
                for (BindAccountVO item : list) {
                    if (item.AccountType.equals(1)) {
                        mobileX = item.EncryptMobile;
                    }
                }
            }
        }

        List<JunePayInfo> list = payService.getJunePayStatus(mobileX);
        for (JunePayInfo item :
                list) {
            if (item.OrderType == 1) {
                if (item.OrderStatus == 1) {
                    status = "1";
                }
            }
            if (item.OrderType == 2 && item.OrderStatus == 1) {
                status = "2";
            }
            if (item.OrderType == 3 && item.OrderStatus == 1) {
                status = "3";
            }
        }
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "成功", status)) + ")";
    }

    /**
     * 获取用户定金支付订单
     *
     * @param request
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/6/15 16:43
     */
    @RequestMapping("/getjunepaylist")
    @ResponseBody
    public String GetJunePayList(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String jsonpCallback1 = request.getParameter("jsonpCallback");
        String mobileX = request.getParameter("mobilex");
        String uid = request.getParameter("uid");

        if (StringUtils.isEmpty(jsonpCallback)) {
            jsonpCallback = jsonpCallback1;
        }

        if (StringUtils.isEmpty(mobileX) && !StringUtils.isEmpty(uid)) {
            //通过uid获取
            List<BindAccountVO> list = userService.GetBindAccountList(uid);
            if (list != null) {
                for (BindAccountVO item : list) {
                    if (item.AccountType.equals(1)) {
                        mobileX = item.EncryptMobile;
                    }
                }
            }
        }

        if (StringUtils.isEmpty(mobileX)) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少mobileX")) + ")";
        }

        List<JunePayInfo> list = payService.getJunePayStatus(mobileX);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "成功", list)) + ")";
    }


    /**
     * 签到领取奖励
     *
     * @param request
     * @return cn.emoney.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/9/6 11:03
     */
    @RequestMapping("/getsignbenefit")
    @ResponseBody
    public Result<String> GetSignBenefit(HttpServletRequest request) {
        String actCode = request.getParameter("actcode");
        String prizeId = request.getParameter("prizeid");
        String day = request.getParameter("day");
        if (StringUtils.isEmpty(day)) {
            day = "0";
        }
        String pids = "888020000,888080000,*********";
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }
        if (StringUtils.isEmpty(prizeId)) {
            return Result.buildErrorResult("缺少参数prizeId");
        }
        if (pids.indexOf(loginUser.getPid()) < 0) {
            return Result.buildErrorResult("本活动仅限大师用户参与");
        }

        boolean ret = benefitDsService.getSignBenefit(loginUser.uid, prizeId, day);
        if (ret) {
            return Result.buildSuccessResult();
        } else {
            return Result.buildErrorResult("领取奖励失败，请联系业务员");
        }
    }

    /**
     * 获取领取奖励记录
     *
     * @return null
     * <AUTHOR>
     * @date 2023/9/6 11:05
     */
    @RequestMapping("/getsignrecord")
    @ResponseBody
    public Result<List<ActDsSignRecordDO>> GetSignRecord(HttpServletRequest request) {
        String actCode = request.getParameter("actcode");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        List<ActDsSignRecordDO> list = benefitDsService.getSignRecords(loginUser.getUid());
        return Result.buildSuccessResult(list);
    }

    /**
     * 获取是否满签5天
     *
     * @param request
     * @return null
     * <AUTHOR>
     * @date 2023/9/6 15:01
     */
    @RequestMapping("/getappendprizestatus")
    @ResponseBody
    public Result<Boolean> GetAppendPrize(HttpServletRequest request) {
        String actCode = request.getParameter("actcode");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }
        ActDsSignBenefitDO signBenefitDO = new ActDsSignBenefitDO();
        AppApiResult<ActDsSignDetail> result = benefitDsService.getUserSignInfo(loginUser.getUid(), "0");
        if (result.getCode() == 0 && result.getDetail() != null) {
            signBenefitDO = result.getDetail().getAppendPrize();
        } else {
            return Result.buildErrorResult(result.getMsg());
        }

        return Result.buildSuccessResult(signBenefitDO.isCurrent);
    }

    /**
     * 获取签到总人数
     *
     * @param request
     * @return null
     * <AUTHOR>
     * @date 2023/9/6 15:01
     */
    @RequestMapping("/getdaycount")
    @ResponseBody
    public Result<String> GetDayCount(HttpServletRequest request) {
        String daycount = "";
        AppApiResult<ActDsSignDetail> result = benefitDsService.getUserSignInfo("", "0");
        if (result.getCode() == 0 && result.getDetail() != null) {
            daycount = String.valueOf(result.getDetail().dayCount);
        }

        return Result.buildSuccessResult(daycount);
    }

    /**
     * 获取当日众筹用户数
     *
     * @param request
     * @return cn.emoney.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2023/9/28 11:01
     */
    @RequestMapping("/getdaycount1009")
    @ResponseBody
    public Result<String> GetDayCount_1009(HttpServletRequest request) {
        String actCode = request.getParameter("actCode");
        String dayCount = "";
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        AppApiResult<ActDsDailyChanceDO> result = benefitDsService.getDailyInfo(loginUser.getUid(), loginUser.getPid(), "0");
        if (result.getCode() == 0 && result.getDetail() != null) {
            dayCount = String.valueOf(result.getDetail().dayCount);
        }

        return Result.buildSuccessResult(dayCount);
    }

    /**
     * 达标用户记录
     * <AUTHOR>
     * @date 2023/9/28 11:07
     * @param request
     * @return cn.emoney.common.result.Result<java.lang.Boolean>
     */
    @RequestMapping("/adduser1009")
    @ResponseBody
    public Result<Boolean> AddUser_1009(HttpServletRequest request)
    {
        String actCode = request.getParameter("actCode");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }
        AppApiResult<Boolean> result = benefitDsService.addDailyUser(loginUser.getUid());

        if (result == null) {
            return Result.buildErrorResult("记录失败。");
        }
        return Result.buildSuccessResult(result.getDetail());
    }

    /**
     * 领取鸿运值签到奖励
     * <AUTHOR>
     * @date 2023/9/28 11:08
     * @param request
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/getdailyreward")
    @ResponseBody
    public Result<Boolean> GetDailyReward(HttpServletRequest request) {
        String actCode = request.getParameter("actCode");
        String prizeId = request.getParameter("prizeId");
        String day = request.getParameter("day");
        String pids = "888020000,888080000,*********";
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }
        if (StringUtils.isEmpty(prizeId)) {
            return Result.buildErrorResult("缺少参数prizeId");
        }
        if (pids.indexOf(loginUser.getPid()) < 0) {
            return Result.buildErrorResult("本活动仅限大师用户参与");
        }

        String pid = loginUser.getPid();
        if (loginUser.getPid().equals("*********")) {
            pid = userService.getHighPIDByAccount(loginUser.getAccount());
        }

        AppApiResult<Boolean> result = benefitDsService.getDailyReward(loginUser.getUid(), pid, loginUser.getAccount(), loginUser.getMobileX(), prizeId, day);

        if (result == null) {
            return Result.buildErrorResult("领取失败，请联系专员。");
        }
        return Result.buildSuccessResult(result.getDetail());
    }
}
