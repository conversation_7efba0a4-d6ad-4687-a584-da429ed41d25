package cn.emoney.web.controller;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.enums.BaseResultCodeEnum;
import cn.emoney.common.result.LogisticsResult;
import cn.emoney.common.result.PointResult;
import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.common.utils.DateFormatUtil;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.pojo.bo.*;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.pojo.vo.SignRecordVO;
import cn.emoney.service.*;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022-01-24
 */
@Controller
@RequestMapping("/renew588")
public class Renew588Controller extends BaseController {
    @Autowired
    private LoginService loginService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private PointService pointService;

    @Autowired
    private SignRecordService signRecordService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserService userService;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor executor;

    private String redisKey_pointRecord = RedisConstants.Redis_Pre_Activity + "pointrecord20220207";

    private String redisKey_exchangeRecord = RedisConstants.Redis_Pre_Activity + "exchangerecord";

    private String redisKey_pop******** = RedisConstants.Redis_Pre_Activity + "pop:********";
    private String actCode = "20220207_renew588";

    private String FilterCOUPON_ACTIVITY_ID = "cp-1201221151151107,cp-1210122114043889,cp-1201228142434227,cp-1210427145116459,cp-1210329113607995";
    private String FilterCOUPON_ACTIVITY_ID_0221 = "cp-1201221151151107,cp-1210122114043889,cp-1201228142434227,cp-1210427145116459,cp-1210329113607995,cp-1220127155425489";
    private String FilterCOUPON_ACTIVITY_ID_0304 = "cp-1201221151151107,cp-1210122114043889,cp-1201228142434227,cp-1210427145116459,cp-1210329113607995,cp-1220228113337954";
    private String FilterCOUPON_ACTIVITY_ID_0318 = "cp-1201221151151107,cp-1210122114043889,cp-1201228142434227,cp-1210427145116459,cp-1210329113607995,cp-1220228113337954";
    private String FilterCOUPON_ACTIVITY_ID_0406 = "cp-1201221151151107,cp-1210122114043889,cp-1201228142434227,cp-1210427145116459,cp-1220228113337954";
    private String FilterCOUPON_ACTIVITY_ID_0426 = "cp-1220419095822521";
    private String FilterCOUPON_ACTIVITY_ID_0915 = "cp-1220906132105410";
    private String FilterCOUPON_ACTIVITY_ID_0516 = "cp-1201221151151107,cp-1210122114043889,cp-1210427145116459,cp-1220228113337954,cp-1210329113607995,cp-1210729115546420";
    private String FilterCOUPON_ACTIVITY_ID_0620 = "cp-1201221151151107,cp-1210122114043889,cp-1210427145116459,cp-1220228113337954,cp-1210329113607995,cp-1210729115546420,cp-****************";
    private String FilterCOUPON_ACTIVITY_ID_0714_100 = "cp-1201221151151107,cp-1210729115546420,cp-1220228113337954";
    private String FilterCOUPON_ACTIVITY_ID_0714_30 = "cp-1220526154000368";
    private String FilterCOUPON_ACTIVITY_ID_0901_100 = "cp-1201221151151107,cp-1210729115546420,cp-1220228113337954";
    private String FilterCOUPON_ACTIVITY_ID_0901_30 = "cp-1220824163703852";
    private String FilterCOUPON_ACTIVITY_ID_1010 = "cp-1220927150541939";
    private String FilterCOUPON_ACTIVITY_ID_1031_50 = "cp-1221019134640835";
    private String FilterCOUPON_ACTIVITY_ID_1209_60 = "cp-1221129170808381";
    private String FilterCOUPON_ACTIVITY_ID_1226_50 = "cp-1221219102547731";

    private String FilterCOUPON_ACTIVITY_ID_******** = "cp-1221212114312895";

    private String taskid30 = "1484038428462026752";

    private String taskid60 = "1498164998189486080";
    private String taskid30_0318 = "1502102382526468096";
    private String taskid30_0406 = "1508296111809499136";
    private String taskid30_0516 = "1522123385927241728";

    private String taskid30_******** = "1627609864450740224";

    private String hasPointRecordVal = "1";

    @RequestMapping("/202202")
    public String index(HttpServletRequest request, HttpServletResponse response, Model model) {
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasPointRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                }
            }

            if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
                hasPointRecord = hasPointRecord(loginUser.getUid(), taskid30);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("hasPointRecord", hasPointRecord);
        return "renew588/202202";
    }

    @RequestMapping("/index0221")
    public String index_20220221(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20220221_renew588";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasPointRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0221.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                }
            }

            if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
                hasPointRecord = hasPointRecord(loginUser.getUid(), taskid30);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("hasPointRecord", hasPointRecord);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew588/20220221";
    }

    @RequestMapping("/index0304")
    public String index_20220304(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20220304_renew588";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasPointRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0304.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                }
            }

            if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
                hasPointRecord = hasPointRecord(loginUser.getUid(), taskid60);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("hasPointRecord", hasPointRecord);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew588/20220304";
    }

    @RequestMapping("/index0318")
    public String index_20220318(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20220318_renew588";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasExChangeRecord = false;
        boolean hasPointRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0318.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                }
            }

            if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
                hasExChangeRecord = hasExChangeRecord(loginUser.getUid(), "288");
                hasPointRecord = hasPointRecord(loginUser.getUid(), taskid30_0318);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("hasExChangeRecord", hasExChangeRecord);
        model.addAttribute("hasPointRecord", hasPointRecord);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew588/20220318";
    }

    @RequestMapping("/index0406")
    public String index_20220406(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20220406_renew588";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasPointRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0318.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                }
            }

            if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
                hasPointRecord = hasPointRecord(loginUser.getUid(), taskid30_0406);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("hasPointRecord", hasPointRecord);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew588/20220406";
    }

    @RequestMapping("/index0426")
    public String index_20220426(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20220426_renew588_prod";
        String pid = "";
        boolean hasCoupon200 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有200元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0426.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon200 = true;
                    }
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon200", hasCoupon200);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew588/20220426";
    }

    @RequestMapping("/index0516")
    public String index_20220516(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20220516_renew588";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasPointRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0516.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                }
            }
        }
        if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
            hasPointRecord = hasPointRecord(loginUser.getUid(), taskid30_0516);
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("hasPointRecord", hasPointRecord);
        return "renew588/20220516";
    }

    @GetMapping("/sign20220621")
    public String sign_20220621(HttpServletRequest request, HttpServletResponse response, Model model) {
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew588/sign20220621";
    }

    @RequestMapping("/index0620")
    public String index_20220620(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20220620_renew588";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasExChangeRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0620.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                }
            }
        }
        if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
            hasExChangeRecord = hasExChangeRecord(loginUser.getUid(), "365");
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("hasExChangeRecord", hasExChangeRecord);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        return "renew588/20220620";
    }

    @RequestMapping("/index0714")
    public String index_********(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "********_renew588";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasCoupon30 = false;
        boolean hasExChangeRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0714_100.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0714_30.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon30 = true;
                    }
                }
            }
        }
        if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
            hasExChangeRecord = hasExChangeRecord(loginUser.getUid(), "365");
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("hasCoupon30", hasCoupon30);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("hasExChangeRecord", hasExChangeRecord);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        return "renew588/********";
    }

    @RequestMapping("/index0802")
    public String index_********(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "********_renew588";
        String pid = "";

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //默认赠送100元优惠券
                sendCoupon(loginUser.getMobileX(), "cp-****************", actCode, loginUser.getUid(), 100);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew588/********";
    }

    @RequestMapping("/index0901")
    public String index_20220901(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20220901_renew588";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasCoupon30 = false;
        boolean hasExChangeRecord = false;
        boolean hasPointRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0901_100.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0901_30.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon30 = true;
                    }
                }
            }
        }
        if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
            hasExChangeRecord = hasExChangeRecord(loginUser.getUid(), "451");
            hasPointRecord = hasPointRecord(loginUser.getUid(), "1563042067242946560");
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("hasCoupon30", hasCoupon30);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("hasExChangeRecord", hasExChangeRecord);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("hasPointRecord", hasPointRecord);
        return "renew588/20220901";
    }

    @RequestMapping("/index0915")
    public String index_20220915(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20220915_renew588_prod";
        String pid = "";
        boolean hasCoupon200 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有200元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_0915.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon200 = true;
                    }
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon200", hasCoupon200);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew588/20220915";
    }


    @RequestMapping("/index1010")
    public String index_20221010(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20221010_renew588";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasCoupon30 = false;
        boolean hasPointRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_1010.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                }
            }
        }
        if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
            hasPointRecord = hasPointRecord(loginUser.getUid(), "1574959148699881472");
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("hasPointRecord", hasPointRecord);
        return "renew588/20221010";
    }

    @RequestMapping("/index1031")
    public String index_20221031(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20221031_renew588";
        String point30Taskid = "1584360741337698304";
        String pid = "";
        boolean hasCoupon50 = false;
        boolean hasPointRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_1031_50.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon50 = true;
                    }
                }
            }

            if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
                hasPointRecord = hasPointRecord(loginUser.getUid(), point30Taskid);
            }

            if (loginUser.getPid().equals("*********") || loginUser.getPid().equals("*********")) {
                if (!hasCoupon50) {
                    //发放50元优惠券
                    sendCoupon(loginUser.getMobileX(), FilterCOUPON_ACTIVITY_ID_1031_50, actCode, loginUser.getUid(), 50);
                }
                if (!hasPointRecord) {
                    //赠送30积分
                    PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                    requestDTO.platform = "1";
                    requestDTO.pid = pid;
                    requestDTO.uid = loginUser.getUid();
                    requestDTO.subId = "";
                    requestDTO.taskId = point30Taskid;
                    boolean ret = pointService.pointRecordAdd(requestDTO);
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon50", hasCoupon50);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("hasPoint30Record", hasPointRecord);
        model.addAttribute("hasExChangeRecord", hasExChangeRecord(loginUser.getUid(), "535"));
        return "renew588/20221031";
    }

    @RequestMapping("/index1209")
    public String index_20221209(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "renew20221209";
        String pid = "";
        boolean hasCoupon60 = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_1209_60.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon60 = true;
                    }
                }
            }


            if (loginUser.getPid().equals("*********") || loginUser.getPid().equals("*********")) {
                if (!hasCoupon60) {
                    //发放60元优惠券
                    sendCoupon(loginUser.getMobileX(), FilterCOUPON_ACTIVITY_ID_1209_60, actCode, loginUser.getUid(), 60);
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon60", hasCoupon60);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew588/20221209";
    }


    @RequestMapping("/index1226")
    public String index_20221226(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "20221221_renew588";
        String point30Taskid = "1605020008918945792";
        String pid = "";
        boolean hasCoupon50 = false;
        boolean hasPointRecord = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有50元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_1226_50.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon50 = true;
                    }
                }
            }

            if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
                hasPointRecord = hasPointRecord(loginUser.getUid(), point30Taskid);
            }

            if (loginUser.getPid().equals("*********") || loginUser.getPid().equals("*********")) {
                if (!hasCoupon50) {
                    //发放50元优惠券
                    sendCoupon(loginUser.getMobileX(), FilterCOUPON_ACTIVITY_ID_1226_50, actCode, loginUser.getUid(), 50);
                }
                if (!hasPointRecord) {
                    //赠送30积分
                    PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                    requestDTO.platform = "1";
                    requestDTO.pid = pid;
                    requestDTO.uid = loginUser.getUid();
                    requestDTO.subId = "";
                    requestDTO.taskId = point30Taskid;
                    boolean ret = pointService.pointRecordAdd(requestDTO);
                }
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon50", hasCoupon50);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("hasPoint30Record", hasPointRecord);
        model.addAttribute("hasExChangeRecord", hasExChangeRecord(loginUser.getUid(), "606"));
        return "renew588/20221226";
    }

    @RequestMapping("/index********")
    public String index_********(HttpServletRequest request, HttpServletResponse response, Model model) {
        actCode = "********_renew588";
        String pid = "";
        boolean hasCoupon100 = false;
        boolean hasPointRecord = false;
        boolean hasPrivilege = false;
        boolean hasPop = false;

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
                if (ssoResult != null) {
                    loginUser = new LoginUserInfoVO();

                    pid = request.getParameter("pid");
                    loginUser.setUid(ssoResult.getUid());
                    loginUser.setPid(pid);
                    loginUser.setAccount(ssoResult.getUserName());

                    loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
                }
            }
        }
        if (loginUser == null) {
            loginUser = new LoginUserInfoVO();
        } else {
            if (loginUser.getMobileX() != null && !loginUser.getMobileX().isEmpty()) {
                //查询是否有100元优惠券
                List<QueryCouponListDTO> list = logisticsService.queryCouponList(2, loginUser.getMobileX());
                Date now = new Date();
                for (QueryCouponListDTO item : list) {
                    if (item.COUPON_ISENABLE == 0 && FilterCOUPON_ACTIVITY_ID_********.indexOf(item.COUPON_ACTIVITY_ID) > -1 && now.before(item.COUPON_END_TIME)) {
                        hasCoupon100 = true;
                    }
                }
            }


            if (loginUser.getUid() != null && !loginUser.getUid().isEmpty()) {
                hasPointRecord = hasPointRecord(loginUser.getUid(), taskid30_********);
            }

            if (loginUser.getPid().equals("*********") || loginUser.getPid().equals("*********")) {
                if (!hasPointRecord) {
                    //赠送30积分
                    PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                    requestDTO.platform = "1";
                    requestDTO.pid = pid;
                    requestDTO.uid = loginUser.getUid();
                    requestDTO.subId = "";
                    requestDTO.taskId = taskid30_********;
                    boolean ret = pointService.pointRecordAdd(requestDTO);
                }
            }

            hasPrivilege = hasPrivilege********(loginUser, actCode);
            hasPop = getPop********(loginUser.getUid());
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("actcode", actCode);
        model.addAttribute("hasCoupon100", hasCoupon100);
        model.addAttribute("hasPrivilege", hasPrivilege);
        model.addAttribute("allPoint", QueryPoint(loginUser.getUid()));
        model.addAttribute("hasPointRecord", hasPointRecord);
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        model.addAttribute("hasPop", hasPop);
        return "renew588/********";
    }


    /*
     * 赠送优惠券
     * <AUTHOR>
     * @date 2022/7/26 14:10
     * @param null
     * @return null
     */
    public boolean sendCoupon(String mobilex, String activityID, String actCode, String uid, Integer price) {
        String redisKey = RedisConstants.Redis_Pre_Activity + "sendCoupon:" + activityID + ":" + uid;

        Object redisval = redisService.get(redisKey);
        if (redisval != null && redisval.toString().equals("1")) {
            return true;
        }

        SendCouponRequestDTO req = new SendCouponRequestDTO();
        req.PRESENT_ACCOUNT_TYPE = 2;
        req.PRESENT_ACCOUNT = mobilex;
        req.COUPON_ACTIVITY_ID = activityID;
        req.COUPON_RULE_PRICE = price;
        req.PRESENT_PERSON = actCode;
        LogisticsResult<String> result = logisticsService.sendCoupon(req);
        if (result != null && result.getCode() == 0) {
            redisService.set(redisKey, 1, (long) 60 * 60 * 24 * 30);
        } else {
            return false;
        }
        return true;
    }

    /*
     * 获取福利-********周年庆
     * <AUTHOR>
     * @date 2022/7/26 14:11
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/getBenefits")
    @ResponseBody
    public Result<String> getBenefits(HttpServletRequest request, HttpServletResponse response) {
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String actCode = request.getParameter("actcode");
        String day = request.getParameter("day");
        String seltype = request.getParameter("seltype");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        if (!pid.contains("88801")) {
            return Result.buildErrorResult("非小智盈用户不能参与此活动");
        }

        String redisKey = RedisConstants.Redis_Pre_Activity + "UserBenefits:" + day;
        Object obj = redisService.hashGet(redisKey, uid);
        if (obj != null && "1".equals(obj)) {
            return Result.buildErrorResult("已申请过请勿重复申请");
        }

        switch (day) {
            case "1":
                CompletableFuture.runAsync(() -> {
                    //赠送5天使用期
                    boolean resultSendPrivilege1 = sendPrivilege("PAC1220726151609964", loginUser.account, "小智盈续费20周年庆", loginUser.getMobileX(), 1);
                    if (resultSendPrivilege1) {
                        redisService.hashSet(redisKey, uid, "1");
                    }
                }, executor);
                break;
            case "2":
                CompletableFuture.runAsync(() -> {
                    //赠送100积分
                    PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
                    requestDTO.platform = "1";
                    requestDTO.pid = pid;
                    requestDTO.uid = uid;
                    requestDTO.subId = "";
                    requestDTO.taskId = "1551824302268092416";
                    boolean ret = pointService.pointRecordAdd(requestDTO);

                    //赠送5天使用期
                    boolean resultSendPrivilege2 = sendPrivilege("PAC1220726151609964", loginUser.account, "小智盈续费20周年庆", loginUser.getMobileX(), 1);
                    if (resultSendPrivilege2) {
                        redisService.hashSet(redisKey, uid, "1");
                    }
                }, executor);
                break;
            case "3":
                CompletableFuture.runAsync(() -> {
                    if ("1".equals(seltype)) {
                        //赠送20天使用期
                        boolean resultSendPrivilege3 = sendPrivilege("PAC1220726152043682", loginUser.account, "小智盈续费20周年庆", loginUser.getMobileX(), 1);
                        if (resultSendPrivilege3) {
                            redisService.hashSet(redisKey, uid, "1");
                        }
                    }
                    if ("2".equals(seltype)) {
                        //20天北上资金使用期
                        boolean resultSendPrivilege4 = sendPrivilege("PAC1220726152349961", loginUser.account, "小智盈续费20周年庆", loginUser.getMobileX(), 2);
                        if (resultSendPrivilege4) {
                            redisService.hashSet(redisKey, uid, "1");
                        }
                    }
                }, executor);
                break;
            default:
                break;
        }

        return Result.buildSuccessResult();
    }

    @RequestMapping("/send_privilege_********")
    @ResponseBody
    public Result<String> sendPrivilege********(HttpServletRequest request, HttpServletResponse response) {
        String pid = request.getParameter("pid");
        String actCode = request.getParameter("actcode");

        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        if (!pid.contains("88801")) {
            return Result.buildErrorResult("非小智盈用户不能参与此活动");
        }

        if (hasPrivilege********(loginUser, actCode)) {
            return Result.buildErrorResult("您已经领取过该特权");
        }
        boolean resultSendPrivilege4 = sendPrivilege("PAC1230220174026693", loginUser.account, "小智盈续费3月福利活动", loginUser.getMobileX(), 2);
        if (resultSendPrivilege4) {
            userService.AddCountByActCode(actCode, loginUser.getUid(), "1");
//            String redisKey = RedisConstants.Redis_Pre_Activity + ":privilege:********";
//            redisService.hashSet(redisKey, loginUser.getUid(), "1");
            return Result.buildSuccessResult();
        }
        return Result.buildErrorResult("发送特权异常");
    }

    public boolean hasPrivilege********(LoginUserInfoVO loginUser, String actCode) {
        //重复领取判断
        String isSubmit = userService.IsSubmitByActCodes(actCode, loginUser.getUid());
        isSubmit = (isSubmit != null && isSubmit.length() > 0) ? isSubmit.substring(0, isSubmit.length() - 1) : "";
        if (!StringUtils.isEmpty(isSubmit)) {
            return true;
        }
        return false;

//        String redisKey = RedisConstants.Redis_Pre_Activity + ":privilege:********";
//        return redisService.hashGet(redisKey, loginUser.getUid()) == "1";
    }

    /**
     * 物流延期(特权|使用期)
     *
     * @param activityID
     * @param account
     * @param reason
     * @param mobilex
     * @param accountType 1:EM 2:MOBILE
     * @return boolean
     * <AUTHOR>
     * @date 2022/8/22 14:10
     */
    public boolean sendPrivilege(String activityID, String account, String reason, String mobilex, int accountType) {
        SendPrivilegeDTO sendPrivilegeDTO = new SendPrivilegeDTO();
        sendPrivilegeDTO.setAppId("A009");
        sendPrivilegeDTO.setActivityID(activityID);
        sendPrivilegeDTO.setReason(reason);
        sendPrivilegeDTO.setApplyUserID("scb_public");
        List<CreateActivityGrantApplyAccountDTO> createActivityGrantApplyAccountDTOS = new ArrayList<>();
        CreateActivityGrantApplyAccountDTO createActivityGrantApplyAccountDTO = new CreateActivityGrantApplyAccountDTO();

        createActivityGrantApplyAccountDTO.setAccountType(accountType);
        createActivityGrantApplyAccountDTO.setAccount(account);
        createActivityGrantApplyAccountDTO.setMID(mobilex);
        createActivityGrantApplyAccountDTOS.add(createActivityGrantApplyAccountDTO);
        sendPrivilegeDTO.setAccounts(createActivityGrantApplyAccountDTOS);

        Boolean resultSenddPrivilege = logisticsService.sendPrivilege(sendPrivilegeDTO);
        return resultSenddPrivilege;
    }

    /**
     * 领取100元优惠券
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/sendCoupon100")
    @ResponseBody
    public Result<String> sendCoupon100(HttpServletRequest request, HttpServletResponse response) {
        String actcode = request.getParameter("actcode");
        String activityID = "cp-****************";
        if (Strings.isEmpty(actcode)) {
            actcode = "20220516_renew588";
        }
        if ("20221010_renew588".equals(actcode)) {
            activityID = FilterCOUPON_ACTIVITY_ID_1010;
        }
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }
        SendCouponRequestDTO req = new SendCouponRequestDTO();
        req.PRESENT_ACCOUNT_TYPE = 2;
        req.PRESENT_ACCOUNT = loginUser.getMobileX();
        req.COUPON_ACTIVITY_ID = activityID;
        req.COUPON_RULE_PRICE = 100;
        req.PRESENT_PERSON = actcode;
        LogisticsResult<String> result = logisticsService.sendCoupon(req);
        if (result.getCode() == 0) {
            return Result.buildSuccessResult();
        } else {
            return Result.buildErrorResult("领取失败," + result.getMsg());
        }
    }

    /**
     * 领取200元优惠券
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/sendCoupon200")
    @ResponseBody
    public Result<String> sendCoupon200(HttpServletRequest request, HttpServletResponse response) {
        actCode = "20220426_renew588_prod";
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }
        SendCouponRequestDTO req = new SendCouponRequestDTO();
        req.PRESENT_ACCOUNT_TYPE = 2;
        req.PRESENT_ACCOUNT = loginUser.getMobileX();
        req.COUPON_ACTIVITY_ID = FilterCOUPON_ACTIVITY_ID_0426;
        req.COUPON_RULE_PRICE = 200;
        req.PRESENT_PERSON = "20220426_renew588";
        LogisticsResult<String> result = logisticsService.sendCoupon(req);
        if (result.getCode() == 0) {
            return Result.buildSuccessResult();
        } else {
            return Result.buildErrorResult("领取失败," + result.getMsg());
        }
    }

    /**
     * 领取200元优惠券
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("/sendCoupon200_0915")
    @ResponseBody
    public Result<String> sendCoupon200_0915(HttpServletRequest request, HttpServletResponse response) {
        actCode = "20220915_renew588_prod";
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }
        SendCouponRequestDTO req = new SendCouponRequestDTO();
        req.PRESENT_ACCOUNT_TYPE = 2;
        req.PRESENT_ACCOUNT = loginUser.getMobileX();
        req.COUPON_ACTIVITY_ID = FilterCOUPON_ACTIVITY_ID_0915;
        req.COUPON_RULE_PRICE = 200;
        req.PRESENT_PERSON = "20220915_renew588";
        LogisticsResult<String> result = logisticsService.sendCoupon(req);
        if (result.getCode() == 0) {
            return Result.buildSuccessResult();
        } else {
            return Result.buildErrorResult("领取失败," + result.getMsg());
        }
    }

    @RequestMapping("/addpointrecored")
    @ResponseBody
    public Result<String> AddPointRecored(HttpServletRequest request, HttpServletResponse response) {
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String actcode = request.getParameter("actcode");
        String taskid = request.getParameter("taskid");
        if (StringUtils.isEmpty(actcode)) {
            actcode = "";
        }
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actcode);
        if (loginUser == null) {
            return Result.buildErrorResult("用户未登录");
        }

        if ("20220221_renew588".equals(actcode)) {
            taskid30 = "1493518106864259072";
        }
        if ("20220304_renew588".equals(actcode)) {
            taskid30 = "1498164998189486080";
        }
        if ("20220318_renew588".equals(actcode)) {
            taskid30 = "1502102382526468096";
        }
        if ("20220406_renew588".equals(actcode)) {
            taskid30 = "1508296111809499136";
        }
        if ("20220516_renew588".equals(actcode)) {
            taskid30 = "1522123385927241728";
        }
        if ("20220901_renew588".equals(actcode)) {
            taskid30 = "1563042067242946560";
        }
        if ("20221010_renew588".equals(actcode)) {
            taskid30 = "1574959148699881472";
        }

        if (!StringUtils.isEmpty(taskid)) {
            taskid30 = taskid;
        }

        String isSubmit = userService.IsSubmitByActCodes(actcode, loginUser.getUid());
        isSubmit = (isSubmit != null && isSubmit.length() > 0) ? isSubmit.substring(0, isSubmit.length() - 1) : "";
        if (!StringUtils.isEmpty(isSubmit)) {
            return Result.buildErrorResult("-1", "已领取过，请勿重复领取");
        }

        PointRecordAddRequestDTO requestDTO = new PointRecordAddRequestDTO();
        requestDTO.platform = "1";
        requestDTO.pid = loginUser.getPid();
        requestDTO.uid = loginUser.getUid();
        requestDTO.subId = "";
        requestDTO.taskId = taskid30;

        boolean ret = pointService.pointRecordAdd(requestDTO);
        if (ret) {
            userService.AddCountByActCode(actcode, loginUser.getUid(), String.valueOf(System.currentTimeMillis()));
            return Result.buildSuccessResult("领取成功");
        } else {
            return Result.buildErrorResult("领取失败");
        }
    }

    /**
     * 积分兑换商品 （所有活动通用接口）
     * <AUTHOR>
     * @date 2023/4/27 17:19
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/pointOrderExchange")
    @ResponseBody
    public Result<String> PointOrderExchange(HttpServletRequest request, HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        if (StringUtils.isEmpty(actCode)) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            return Result.buildErrorResult("-1", "登录超时，请重新进入");
        }

        String uid = loginUser.getUid();
        String productId = request.getParameter("productId");
        int exchangePoint = 200;
        if ("20220318_renew588".equals(actCode)) {
            exchangePoint = 60;
        }
        if ("20220620_renew588".equals(actCode)) {
            exchangePoint = 60;
        }
        if ("********_renew588".equals(actCode)) {
            exchangePoint = 60;
        }
        if ("20220901_renew588".equals(actCode)) {
            exchangePoint = 60;
        }
        if ("20221031_renew588".equals(actCode)) {
            exchangePoint = 10;
        }
        if ("20221221_renew588".equals(actCode)) {
            exchangePoint = 10;
        }

        if ("********_renew588".equals(actCode)) {
            exchangePoint = 100;
        }

        if(productId.equals("599")){
            exchangePoint = 100;
        }
        if(productId.equals("598")){
            exchangePoint = 60;
        }
        if(productId.equals("743")){
            exchangePoint = 288;
        }
        if(productId.equals("745")){
            exchangePoint = 888;
        }

        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (StringUtils.isEmpty(productId)) {
            return Result.buildErrorResult("-1", "缺少productId");
        }

        //查询可用积分
        Double allPoint = QueryPoint(uid);
        if (allPoint < exchangePoint) {
            return Result.buildErrorResult("-1", "积分不足" + exchangePoint);
        }

        PointOrderAddRequestDTO orderAddRequestDTO = new PointOrderAddRequestDTO();
        orderAddRequestDTO.emNo = loginUser.getAccount();
        orderAddRequestDTO.mobile = loginUser.getMobileX();
        orderAddRequestDTO.mobileMask = loginUser.getMaskMobile();
        orderAddRequestDTO.platform = 1;
        orderAddRequestDTO.productId = Integer.parseInt(productId);
        orderAddRequestDTO.productQty = 1;
        orderAddRequestDTO.uid = Integer.parseInt(loginUser.getUid());

        PointResult<PointOrderAddDataDTO> orderAddResult = pointService.pointOrderAdd(orderAddRequestDTO);
        if (orderAddResult.isSuccess() && orderAddResult.getData() != null) {
            PointOrderAddDataDTO orderAddDataDTO = JsonUtil.toBean(JSON.toJSONString(orderAddResult.getData()), PointOrderAddDataDTO.class);
            if (orderAddDataDTO != null) {
                String orderNo = orderAddDataDTO.getOrderNo();
                //String tradeNo = orderAddResult.getData().getTradeNo();

                PointOrderExchangeRequestDTO orderExchangeRequestDTO = new PointOrderExchangeRequestDTO();
                orderExchangeRequestDTO.orderNo = orderNo;
                //orderExchangeRequestDTO.tradeNo = tradeNo;
                orderExchangeRequestDTO.uid = Integer.parseInt(loginUser.getUid());
                orderExchangeRequestDTO.payType = "0";

                PointResult<PointOrderAddDataDTO> result = pointService.pointOrderExchange(orderExchangeRequestDTO);
                if (result.isSuccess()) {
                    SetExChangeRecordToRedis(uid, productId);
                    return Result.buildSuccessResult("兑换成功");
                } else {
                    return Result.buildErrorResult("-1", result.getMsg());
                }
            }

        }

        return Result.buildErrorResult("-1", orderAddResult.getMsg());
    }

    @GetMapping("/autoSign")
    @ResponseBody
    public String autoSign(HttpServletRequest request, HttpServletResponse response) {
        Result<SignRecordVO> result = null;
        String jsonpCallback = "";
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String dateString = request.getParameter("date");
        jsonpCallback = request.getParameter("callback");

        if (StringUtils.isBlank(uid)) {
            result = Result.buildErrorResult("自动打卡异常：uid信息为空");
            return responseResult(JSON.toJSONString(result), jsonpCallback);
        }

        if (StringUtils.isBlank(pid)) {
            result = Result.buildErrorResult("自动打卡异常：pid信息为空");
            return responseResult(JSON.toJSONString(result), jsonpCallback);
        }

        if (!"*********".equals(pid)) {
            result = Result.buildErrorResult(BaseResultCodeEnum.NO_OPERATE_PERMISSION.getCode(), "自动打卡异常：您暂无打卡权限");
            return responseResult(JSON.toJSONString(result), jsonpCallback);
        }
        Date date;
        if (StringUtils.isBlank(dateString)) {
            date = new Date();
        } else {
            date = DateFormatUtil.string2Date(dateString, DateFormatUtil.TIME_FORMAT_D);
        }

        result = signRecordService.autoSign(uid, pid, date);
        return responseResult(JSON.toJSONString(result), jsonpCallback);
    }

    @GetMapping("/refreshFee")
    @ResponseBody
    public String compensate(HttpServletRequest request, HttpServletResponse response) {
        signRecordService.batchRefreshSignFee();
        return "success";
    }

    /**
     * 获取可用积分
     *
     * @param uid
     * @return java.lang.Double
     * <AUTHOR>
     * @date 2022/2/17 14:17
     */
    public Double QueryPoint(String uid) {
        Double point = Double.parseDouble("0");
        if (uid != null) {
            List<PointQueryDataDTO> list = pointService.pointQuerySummary(uid);
            for (PointQueryDataDTO item : list) {
                int pointStatus = item.pointStatus;
                if (pointStatus == 2 || pointStatus == 3) {
                    point += item.pointTotal;
                }
            }
        }
        return point;
    }


    /**
     * 是否领取过积分
     *
     * @param uid
     * @return boolean
     * <AUTHOR>
     * @date 2022/1/25 11:28
     */
    public boolean hasPointRecord(String uid, String taskid) {
        if (uid != null) {
            Object obj = redisService.hashGet(redisKey_pointRecord + taskid, uid);
            if (obj != null && obj.toString().equals(hasPointRecordVal)) {
                return true;
            } else {
                Long[] taskids = new Long[1];
                taskids[0] = Long.parseLong(taskid);
                PointQueryByTaskIDRequestDTO requestDTO = new PointQueryByTaskIDRequestDTO();
                requestDTO.uid = Long.parseLong(uid);
                requestDTO.taskIds = taskids;

                List<PointQueryByTaskIDDataDTO> list = pointService.pointQueryByTaskID(requestDTO);
                for (PointQueryByTaskIDDataDTO item : list) {
                    if (item.taskId.equals(taskids[0])) {
                        SetPointRecordToRedis(uid);
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * @param uid
     * @param productid
     * @return boolean
     * <AUTHOR>
     * @date 2022/3/15 15:56
     */
    public boolean hasExChangeRecord(String uid, String productid) {
        if (uid != null) {
            Object obj = redisService.hashGet(redisKey_exchangeRecord + productid, uid);
            if (obj != null && obj.toString().equals(hasPointRecordVal)) {
                return true;
            } else {
                PointResult<List<PointOrderAddDataDTO>> result = pointService.pointOrderQueryByUidAndProId(uid, productid);
                if (result.isSuccess() && result.getData() != null) {
                    List<PointOrderAddDataDTO> list = JsonUtil.toBeanList(JSON.toJSONString(result.getData()), PointOrderAddDataDTO.class);

                    for (PointOrderAddDataDTO item : list) {
                        if ((item.productId + "").equals(productid)) {
                            if (item.orderStatus == 1) {
                                SetExChangeRecordToRedis(uid, productid);
                                return true;
                            }
                        }
                    }
                }
            }
        }

        return false;
    }

    public void SetPointRecordToRedis(String uid) {
        redisService.hashSet(redisKey_pointRecord, uid, hasPointRecordVal);
    }

    public void SetExChangeRecordToRedis(String uid, String productid) {
        redisService.hashSet(redisKey_exchangeRecord + productid, uid, hasPointRecordVal);
    }

    @RequestMapping("/pop********")
    @ResponseBody
    public Result<String> pop********(HttpServletRequest request, HttpServletResponse response, String uid) {
        redisService.hashSet(redisKey_pop********, uid, "1");
        return Result.buildSuccessResult();
    }

    public boolean getPop********(String uid) {
        Object v = redisService.hashGet(redisKey_pop********, uid);
        if (v == null) {
            return false;
        }
        String value = v.toString();
        return "1".equals(value);
    }
}
