package cn.emoney.web.controller;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.bo.PointQueryDataDTO;
import cn.emoney.pojo.bo.QueryCouponListDTO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-26
 */
@Controller
@RequestMapping("/renew2025")
public class Renew2025Controller extends BaseController {
    @Autowired
    private LoginService loginService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private PointService pointService;

    @Autowired
    private Benefit588Service benefit588Service;

    @Autowired
    private UserService userService;

    @RequestMapping("/index0117")
    public String index0117(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20250117_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2025/20250117";
    }


    @RequestMapping("/index0217")
    public String index0217(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20250217_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2025/20250217";
    }

    @RequestMapping("/index0318")
    public String index0318(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20250318_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2025/20250318";
    }

    @RequestMapping("/index0501")
    public String index0501(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20250501_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2025/20250501";
    }

    @RequestMapping("/index0601")
    public String index0601(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20250601_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2025/20250601";
    }

    @RequestMapping("/index0701")
    public String index0701(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20250701_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2025/20250701";
    }

    @RequestMapping("/index0801")
    public String index0801(HttpServletRequest request, HttpServletResponse response, Model model) {
        String actCode = "20250801_renew";
        String pid = request.getParameter("pid");
        LoginUserInfoVO loginUser = loginService.GetLoginUserInfo(request, actCode);
        if (loginUser == null) {
            //sso登录
            String token = request.getParameter("token");
            if (token != null && !token.isEmpty()) {
                loginUser = SSOLogin(request, response, actCode, pid);
            }
        }

        if (loginUser == null || StringUtils.isEmpty(loginUser.getUid())) {
            loginUser = new LoginUserInfoVO();

            //密文手机+uid自动登录
            String phoneEncrypt = request.getParameter("phoneEncrypt");
            String UPcid = request.getParameter("UPcid");
            if (!StringUtils.isEmpty(phoneEncrypt) && !StringUtils.isEmpty(UPcid)) {
                loginUser.mobileX = phoneEncrypt;
                loginUser = loginService.SetLoginUserInfo(request, response, actCode, loginUser);
            }
        }

        model.addAttribute("loginUserInfo", loginUser);
        model.addAttribute("actCode", actCode);
        model.addAttribute("isLogin", loginUser.getUid() == null ? "0" : "1");
        model.addAttribute("mobileX", loginUser.getMobileX() == null ? "" : loginUser.getMobileX());
        model.addAttribute("staticPath", super.staticPath);
        model.addAttribute("resourceVersion", super.resourceVersion);
        return "renew2025/20250801";
    }
}
