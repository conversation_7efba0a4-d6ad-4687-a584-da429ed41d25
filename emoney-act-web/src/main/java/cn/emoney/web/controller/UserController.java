package cn.emoney.web.controller;

import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.pojo.bo.CreateActivityGrantApplyAccountDTO;
import cn.emoney.pojo.bo.SendPrivilegeDTO;
import cn.emoney.pojo.vo.BindAccountVO;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.LogisticsService;
import cn.emoney.service.UserService;
import cn.emoney.service.kafka.producer.ProducerService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-01-11
 */
@Controller
@RequestMapping("/user")
@Validated
@Slf4j
public class UserController {
    @Autowired
    private UserService userService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private ProducerService producerService;

    @RequestMapping("/addcountbyactcode")
    @ResponseBody
    public String AddCountByActCode(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String uid = request.getParameter("uid");
        String actCode = request.getParameter("actcode");
        String value = request.getParameter("value");

        String ret = userService.AddCountByActCode(actCode, uid, value);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "成功", ret)) + ")";
    }

    @RequestMapping("/issubmitbyactcodes")
    @ResponseBody
    public String IsSubmitByActCodes(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String uid = request.getParameter("uid");
        String actCodes = request.getParameter("actcodes");

        String ret = userService.IsSubmitByActCodes(actCodes, uid);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "成功", ret)) + ")";
    }


    @RequestMapping("/getcountbyactcode")
    @ResponseBody
    public String GetCountByActCode(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String actCodes = request.getParameter("actcode");

        String ret = userService.GetCountByActCodes(actCodes);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "成功", ret)) + ")";
    }

    @RequestMapping("/addusedcount")
    @ResponseBody
    public String AddUsedCount(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String actCodes = request.getParameter("actcode");

        String ret = userService.AddUsedCount(actCodes);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "成功", ret)) + ")";
    }


    @RequestMapping("/lessusedcount")
    @ResponseBody
    public String LessUsedCount(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String actCodes = request.getParameter("actcode");

        String ret = userService.DecrUsedCount(actCodes);
        return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "成功", ret)) + ")";
    }

    /**
     * 领取特权/赠送使用期
     *
     * @param request
     * @return null
     * <AUTHOR>
     * @date 2023/1/12 15:43
     */
    @RequestMapping("/sendprivilege")
    @ResponseBody
    public String sendPrivilege(HttpServletRequest request) {
        String jsonpCallback = request.getParameter("callback");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String activityID = request.getParameter("activityID");
        String actCode = request.getParameter("actCode");
        //领取特权指定2  延期指定1
        String type = request.getParameter("actType");
        String reason = request.getParameter("reason");
        Integer accountType = 1;
        if (!StringUtils.isEmpty(type)) {
            accountType = Integer.parseInt(type);
        }
        if (StringUtils.isEmpty(reason)) {
            reason = "大师第一课特定功能观看满15天赠送15天使用期";
        }

        if (StringUtils.isEmpty(uid)) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少uid")) + ")";
        }

        if (StringUtils.isEmpty(pid)) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少pid")) + ")";
        }

        if (StringUtils.isEmpty(actCode)) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少actCode")) + ")";
        }

        //activityID需物流提前申请-PACCode
        if (StringUtils.isEmpty(activityID)) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "缺少activityID")) + ")";
        }

        //解密sso
        LoginUserInfoVO loginUser = new LoginUserInfoVO();
        SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
        if (ssoResult != null) {
            loginUser.setUid(ssoResult.getUid());
            loginUser.setPid(pid);
            loginUser.setAccount(ssoResult.getUserName());
        } else {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "认证失败，未登录")) + ")";
        }

        //根据uid获取绑定的em账号和手机号
        if (loginUser.getUid().isEmpty()) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildErrorResult("-1", "认证失败，未登录")) + ")";
        }

        Result<String> resultSendPrivilege = userService.sendPrivilege_New(loginUser, activityID, reason, accountType, actCode);
        if (!resultSendPrivilege.isSuccess()) {
            return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("-1", resultSendPrivilege.getMsg(), null)) + ")";
        } else {
            if (actCode.equals("privilege20230601") || actCode.equals("privilege20230809") || actCode.equals("privilege20231011")) {
                //********-波段抢攻能活动领取成功 获取专员信息并发送短信
                producerService.sendMessage("privilegeActUserComing", uid + "_" + loginUser.getMobileX() + "_" + actCode);
            }

            return jsonpCallback + "(" + JSON.toJSONString(Result.buildSuccessResult("200", "领取成功", null)) + ")";
        }
    }

}
