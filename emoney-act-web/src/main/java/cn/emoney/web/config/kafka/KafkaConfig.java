package cn.emoney.web.config.kafka;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.apache.kafka.clients.admin.NewTopic;


/**
 * Kafka配置类
 *
 * <AUTHOR>
 * @date 2022/01/30 10:22
 **/
@EnableKafka
@Configuration
public class KafkaConfig {
    @Bean("ackContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> ackContainerFactory(ConsumerFactory<String, String> consumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        factory.setConcurrency(8);
        factory.setAutoStartup(false);
        return factory;
    }

    @Bean("ackNoticeContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> ackNoticeContainerFactory(ConsumerFactory<String, String> consumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        factory.setConcurrency(8);
        factory.setAutoStartup(false);
        return factory;
    }

    @Bean("firstClassUserContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> firstClassUserContainerFactory(ConsumerFactory<String, String> consumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        factory.setConcurrency(4);
        return factory;
    }

    @Bean("privilegeActUserContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> privilegeActUserContainerFactory(ConsumerFactory<String, String> consumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        factory.setConcurrency(4);
        return factory;
    }

    @Bean("sendPrivilegeUserContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> sendPrivilegeUserContainerFactory(ConsumerFactory<String, String> consumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        factory.setConcurrency(4);
        return factory;
    }

    @Bean("sendCouponUserContainerFactory")
    public ConcurrentKafkaListenerContainerFactory<String, String> sendCouponUserContainerFactory(ConsumerFactory<String, String> consumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        factory.setConcurrency(4);
        return factory;
    }

    private static final int partitions = 3;
    private static final short replicas = 2;
    @Bean
    public NewTopic SendCouponTopic() {
        return new NewTopic("sendCouponUserComing", partitions, replicas);
    }

    @Bean
    public NewTopic SendPrivilegeTopic() {
        return new NewTopic("sendPrivilegeUserComing", partitions, replicas);
    }
}
