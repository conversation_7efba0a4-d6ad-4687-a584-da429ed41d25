package cn.emoney.web.config;

import cn.emoney.act.core.request.LoggingInterceptor;
import cn.emoney.web.interceptor.DynamicTimeoutInterceptor;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
public class OkHttpConfig {

    @Bean
    public OkHttpClient okHttpClient(LoggingInterceptor loggingInterceptor) {
        return new OkHttpClient.Builder()
                .readTimeout(10, TimeUnit.SECONDS)
                .connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .connectionPool(new ConnectionPool(200, 100, TimeUnit.MINUTES))
                .addInterceptor(new DynamicTimeoutInterceptor())
                .addInterceptor(loggingInterceptor)
                .build();
    }
}