package cn.emoney.web.config;

import cn.emoney.web.client.ClientIPArgumentResolver;
import cn.emoney.web.security.SecurityArgumentResolver;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class WebConfiguration implements WebMvcConfigurer {
    private final SecurityArgumentResolver securityArgumentResolver;
    private final ClientIPArgumentResolver clientIPArgumentResolver;

    public WebConfiguration(SecurityArgumentResolver securityArgumentResolver, ClientIPArgumentResolver clientIPArgumentResolver) {
        this.securityArgumentResolver = securityArgumentResolver;
        this.clientIPArgumentResolver = clientIPArgumentResolver;
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(securityArgumentResolver);
        resolvers.add(clientIPArgumentResolver);
    }
}
