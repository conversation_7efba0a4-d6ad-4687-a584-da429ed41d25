package cn.emoney.web.config.datasource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

/**
 * <AUTHOR>
 * @date 2022-03-24
 */
@Configuration
@MapperScan(basePackages = "cn.emoney.mapper.pay", sqlSessionTemplateRef = "paySqlSessionTemplate")
public class PayDataSourceConfig {

    @Bean(name = "payDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.pay")
    public DataSource payDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "paySqlSessionFactory")
    public SqlSessionFactory paySqlSessionFactory(@Qualifier("payDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mybatis/mapper/pay/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "payTransactionManager")
    public DataSourceTransactionManager payTransactionManager(@Qualifier("payDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "paySqlSessionTemplate")
    public SqlSessionTemplate paySqlSessionTemplate(@Qualifier("paySqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
