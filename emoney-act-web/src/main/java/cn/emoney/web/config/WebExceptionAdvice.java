package cn.emoney.web.config;

import cn.emoney.common.enums.BaseResultCodeEnum;
import cn.emoney.common.result.Result;
import cn.emoney.service.limiter.FrequencyLimitException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class WebExceptionAdvice {
    @ExceptionHandler(FrequencyLimitException.class)
    public Result<?> handleAllException(FrequencyLimitException e) {
        return Result.buildErrorResult(BaseResultCodeEnum.FREQUENT_INTERFACE_ACCESS.code(), e.getMessage() != null ? e.getMessage() : "操作太频繁啦，请稍后再试");
    }
}