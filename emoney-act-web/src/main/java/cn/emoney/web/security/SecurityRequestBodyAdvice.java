package cn.emoney.web.security;

import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdvice;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;

@ControllerAdvice
public class SecurityRequestBodyAdvice implements RequestBodyAdvice {
    private final SecurityArgumentSelector selector;

    public SecurityRequestBodyAdvice(SecurityArgumentSelector selector) {
        this.selector = selector;
    }

    @Override
    public boolean supports(MethodParameter methodParameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return methodParameter.hasParameterAnnotation(SecurityArg.class);
    }

    @Override
    public HttpInputMessage beforeBodyRead(HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) throws IOException {
        SecurityArg methodSecurity = parameter.getMethodAnnotation(SecurityArg.class);
        SecurityArg paramSecurity = parameter.getParameterAnnotation(SecurityArg.class);
        assert paramSecurity != null;

        String keystore = StringUtils.isEmpty(paramSecurity.keystore()) && methodSecurity != null ?
                methodSecurity.keystore() : paramSecurity.keystore();
        return new HttpInputMessage() {
            @Override
            public HttpHeaders getHeaders() {
                return inputMessage.getHeaders();
            }

            @Override
            public InputStream getBody() throws IOException {
                return selector.decrypt(keystore, inputMessage.getBody(), getHeaders());
            }
        };
    }

    @Override
    public Object afterBodyRead(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return body;
    }

    @Override
    public Object handleEmptyBody(Object body, HttpInputMessage inputMessage, MethodParameter parameter, Type targetType, Class<? extends HttpMessageConverter<?>> converterType) {
        return null;
    }
}
