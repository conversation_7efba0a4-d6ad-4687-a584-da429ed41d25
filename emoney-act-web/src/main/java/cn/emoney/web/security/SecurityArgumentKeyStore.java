package cn.emoney.web.security;

import org.springframework.http.HttpHeaders;

import java.io.InputStream;
import java.util.Base64;

public interface SecurityArgumentKeyStore {
    default String decryptToString(String content, HttpHeaders headers) {
        return new String(decrypt(Base64.getMimeDecoder().decode(content), headers));
    }

    byte[] decrypt(byte[] content, HttpHeaders headers);

    InputStream decrypt(InputStream content, HttpHeaders headers);
}
