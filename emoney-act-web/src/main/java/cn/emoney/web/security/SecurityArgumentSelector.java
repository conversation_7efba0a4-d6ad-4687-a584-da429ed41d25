package cn.emoney.web.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.InputStream;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Component
public class SecurityArgumentSelector {
    private final SecurityArgumentKeyStore defaultKeyStore;
    private final Map<String, SecurityArgumentKeyStore> keyStoreMap;

    public SecurityArgumentSelector(@Autowired(required = false) SecurityArgumentKeyStore defaultKeyStore) {
        this.defaultKeyStore = defaultKeyStore;
        this.keyStoreMap = Collections.emptyMap();
    }

    public String decrypt(String keystore, String content, HttpHeaders headers) throws DecryptArgumentException {
        SecurityArgumentKeyStore store = StringUtils.hasText(keystore) ?
                Objects.requireNonNull(keyStoreMap.get(keystore), "No such keystore: " + keystore) :
                defaultKeyStore;
        return store.decryptToString(content, headers);
    }

    public InputStream decrypt(String keystore, InputStream inputStream, HttpHeaders headers) throws DecryptArgumentException {
        SecurityArgumentKeyStore store = StringUtils.hasText(keystore) ?
                Objects.requireNonNull(keyStoreMap.get(keystore), "No such keystore: " + keystore) :
                defaultKeyStore;
        return store.decrypt(inputStream, headers);
    }
}
