package cn.emoney.web.security;

import cn.emoney.service.security.SecurityService;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.Objects;

@Component
public class DefaultKeyStore implements SecurityArgumentKeyStore {
    private final SecurityService securityService;

    public DefaultKeyStore(SecurityService securityService) {
        this.securityService = securityService;
    }

    @Override
    public byte[] decrypt(byte[] content, HttpHeaders headers) {
        return Objects.requireNonNull(securityService.decrypt(content), "Decrypt failed");
    }

    @Override
    public InputStream decrypt(InputStream content, HttpHeaders headers) {
        return securityService.decrypt(content);
    }
}
