package cn.emoney.web.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.annotation.RequestParamMethodArgumentResolver;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Slf4j
@Component
public class SecurityArgumentResolver extends RequestParamMethodArgumentResolver {
    private final SecurityArgumentSelector selector;

    public SecurityArgumentResolver(ConfigurableBeanFactory beanFactory, SecurityArgumentSelector selector) {
        super(beanFactory, true);
        this.selector = selector;
    }

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(SecurityArg.class) && super.supportsParameter(parameter);
    }

    @Override
    protected Object resolveName(String name, MethodParameter parameter, NativeWebRequest request) throws Exception {
        Object value = super.resolveName(name, parameter, request);
        if (!(value instanceof String || value instanceof String[])) {
            return value;
        }

        SecurityArg methodSecurity = parameter.getMethodAnnotation(SecurityArg.class);
        SecurityArg paramSecurity = parameter.getParameterAnnotation(SecurityArg.class);
        assert paramSecurity != null;


        String keystore = StringUtils.isEmpty(paramSecurity.keystore()) && methodSecurity != null ?
                methodSecurity.keystore() : paramSecurity.keystore();

        HttpHeaders headers = getHeaders(request);

        if (value instanceof String[]) {
            return Arrays.stream((String[]) value)
                    .map(v0 -> selector.decrypt(keystore, v0, headers))
                    .toArray(String[]::new);
        } else {
            return selector.decrypt(keystore, (String) value, headers);
        }
    }

    private HttpHeaders getHeaders(NativeWebRequest request) {
        HttpServletRequest servletRequest = request.getNativeRequest(HttpServletRequest.class);
        if (servletRequest != null) {
            return Collections.list(servletRequest.getHeaderNames())
                    .stream()
                    .collect(Collectors.toMap(
                            Function.identity(),
                            h -> Collections.list(servletRequest.getHeaders(h)),
                            (oldValue, newValue) -> newValue,
                            HttpHeaders::new
                    ));
        } else {
            log.warn("Unknown request type: {}", request.getClass().getName());
            return StreamSupport.stream(
                            Spliterators.spliteratorUnknownSize(request.getHeaderNames(), Spliterator.ORDERED),
                            false)
                    .collect(Collectors.toMap(
                            Function.identity(),
                            h -> Optional.ofNullable(request.getHeaderValues(h)).map(Arrays::asList).orElse(Collections.emptyList()),
                            (oldValue, newValue) -> newValue,
                            HttpHeaders::new
                    ));
        }
    }
}
