package cn.emoney.web.client;

import cn.emoney.common.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;

@Slf4j
@Component
public class ClientIPArgumentResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(ClientIP.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        String address = HttpUtils.getIpAddr(webRequest::getHeader).orElseGet(() -> {
            HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
            return request != null ? request.getRemoteAddr() : null;
        });
        if (parameter.getParameterType().isAssignableFrom(InetAddress.class)) {
            return InetAddress.getByName(address);
        } else {
            return address;
        }
    }
}
