package cn.emoney.web.interceptor;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021-12-20
 */
public class DynamicTimeoutInterceptor implements Interceptor {

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request reequest = chain.request();
        //通过不同的接口地址来实现动态更改超时时间
        if(reequest.url().toString().contains("logistics")){
            //设置超时时间
            return chain.withConnectTimeout(20, TimeUnit.SECONDS)
                    .withReadTimeout(20, TimeUnit.SECONDS)
                    .proceed(reequest);
        }
        return chain.proceed(reequest);
    }
}
