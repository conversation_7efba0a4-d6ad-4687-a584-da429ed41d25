package cn.emoney.web.interceptor;

import cn.emoney.common.result.ApiGateWayResult;
import cn.emoney.common.utils.OkHttpUtil;
import cn.emoney.pojo.bo.EnsureUserRegisteredDTO;
import cn.emoney.service.MobileService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Aspect
@Component
@Slf4j
public class TakeCountAspect {
    // 限制时间 1个小时后才能登录
    private long limitTime = 60L * 60;
    // 最大次数 1000
    private Integer maxCount = 1000;
    private String limitName = "mobileLogin:count";
    private String limitQtyName = "mobileLoginMaxCount";


    @Autowired
    @Qualifier("redisTemplate")
    private RedisTemplate redisTemplate;

    @Value("${WarnAccount}")
    private String warnAccount;

    @Autowired
    private MobileService mobileService;
    //扫描所有添加了@TakeCount注解的方法
    @Before("@annotation(takeCount)")
    public void doBefore(TakeCount takeCount) {
        //接收到请求，记录请求内容
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        //记录请求的内容
        String mobile = request.getParameter("mobile");
        if (!StringUtils.isWhitespace(mobile)&&StringUtils.isNumeric(mobile) &&mobile.length() == 11) {
            if (redisTemplate.opsForValue().get(limitQtyName) == null) {
                //如果缓存当中没有当前接口的key就进行存储，如果有的话就对应接口的访问数据自增加一
                Boolean ifAbsent = redisTemplate.opsForValue().setIfAbsent("mobileLogin", "Qty", takeCount.time(), TimeUnit.SECONDS);
                if (ifAbsent) {
                    redisTemplate.opsForValue().set(limitName, 1);
                } else {
                    if (Integer.parseInt(redisTemplate.opsForValue().get(limitName).toString()) >= maxCount) {
                        //对调用次数进行限制
                        log.warn("访问失败，已超过访问次数。" + redisTemplate.opsForValue().get("mobileLogin:count"));
                        redisTemplate.opsForValue().setIfAbsent(limitQtyName, "Max", limitTime, TimeUnit.SECONDS);
                        mobileService.sendWarnTextMessage(warnAccount, MessageFormat.format("您好，您的EnsureUserRegistered用户注册接口已经超限{0}次-{1}", maxCount, DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss")));
                        return;
                    }
                    redisTemplate.opsForValue().increment(limitName);
                }
                //调用手机号激活接口
              mobileService.ensureUserRegistered(mobile);
            }
            log.info(MessageFormat.format("访问次数为{0},访问量是否超限：{1}", redisTemplate.opsForValue().get("mobileLogin:count"), redisTemplate.opsForValue().get("mobileLoginMaxCount") == null ? "没有超限" : "超限"));
        }
    }
}