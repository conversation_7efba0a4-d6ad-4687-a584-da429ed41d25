package cn.emoney;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;

@EnableRedisHttpSession(redisNamespace = "act:session", maxInactiveIntervalInSeconds = 7200)
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class EmoneyActWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(EmoneyActWebApplication.class, args);
    }

}
