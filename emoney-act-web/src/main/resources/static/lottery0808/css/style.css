@charset "utf-8";
/* CSS Document */

* {
    margin: 0;
    padding: 0;
}

body {
    background-color: #FF1C16;
    font-family: "微软雅黑";
}

ul, li {
    list-style: none;
}

.red2 {
    color: #e42c34;
    font-weight: bold;
}

.f1 {
    text-decoration: underline;
}

.img_1 {
    background: url(../images/index_01.jpg) center;
    height: 204px;
}

.img_2 {
    background: url(../images/index_02.jpg) center;
    height: 203px;
}

.img_3 {
    background: url(../images/index_03.jpg) center;
    height: 204px;
}

.img_4 {
    background: url(../images/index_04.png) center;
    height: 779px;
}

.img_5 {
    background: url(../images/index_05.png) center;
    height: 1157px;
}


.footer {
    text-align: center;
    font-family: "微软雅黑";
    font-size: 20px;
    line-height: 40px;
    color: #fff;
    margin: 20px 0 176px;
}

A:link, a:visited {
    TEXT-DECORATION: none;
}

A:hover {
    TEXT-DECORATION: none
}

img {
    border: none;
}

.black {
    color: #000;
    font-weight: bold;
}

.bott {
    background: url("../images/pf.png") center;
    height: 174px;
    position: fixed;
    bottom: 0;
    width: 100%;
}

.main {
    width: 100px;
    margin: 0 auto;
    position: relative;
}


.btn1 {
    background: url("../images/btn1.png");
    width: 506px;
    height: 132px;
    position: absolute;
    top: 64px;
    left: -199px;
}

.btn2 {
    background: url("../images/btn2.png");
    width: 395px;
    height: 105px;
    display: block;
    margin: 40px auto;
}

.btn3 {
    background: url("../images/btn3.png");
    width: 450px;
    height: 140px;
    display: block;
    margin: 40px auto;
}

.btn4 {
    position: absolute;
    top: 665px;
    left: -150px;
    background: url("../images/btn4.png");
    width: 407px;
    height: 108px;
}

.btn5 {
    position: absolute;
    top: 59px;
    left: 155px;
    background: url("../images/btn5.png");
    width: 399px;
    height: 106px;
}

.btn6 {
    position: absolute;
    top: -144px;
    left: 76px;
    background: url("../images/btn6.png");
    width: 23px;
    height: 24px;
}

.zp {
    position: absolute;
    top: -329px;
    left: 199px;
    background: url("../images/zp.png");
    width: 334px;
    height: 334px;
}

.zz {
    position: absolute;
    top: -274px;
    left: 304px;
    background: url("../images/zz.png");
    width: 135px;
    height: 181px;
}

.ico1 {
    position: absolute;
    top: -240px;
    left: 363px;
    background: url("../images/ico1.png");
    width: 184px;
    height: 47px;
    text-align: center;
    line-height: 37px;
    color: #fff0b3;
    font-size: 16px;
    pointer-events: none;
}

.ico6 {
    position: absolute;
    top: 107px;
    left: -415px;
    width: 525px;
    height: 13px;
    border-radius: 6px;
}


.process {
    position: relative;
    border-radius: 6px;
    height: 13px;
    background: url("../images/ico6.png") no-repeat;
    background-size: 100% 100%;
}

.process::after {
    content: '';
    display: block;
    position: absolute;
    right: -36px;
    top: -30px;
    width: 55px;
    height: 60px;
    background: url("../images/ico2.png");
}

.ico3 {
    position: absolute;
    left: 60px;
    top: -30px;
    width: 52px;
    height: 63px;
    background: url("../images/ico3.png");
}

.ico4 {
    position: absolute;
    left: 257px;
    top: -30px;
    width: 56px;
    height: 64px;
    background: url("../images/ico4.png");
}

.ico5 {
    position: absolute;
    left: 422px;
    top: -25px;
    width: 62px;
    height: 53px;
    background: url("../images/ico5.png");
}

.txt1 {
    position: absolute;
    left: -85px;
    top: 1px;
    width: 100px;
    color: #fee995;
    font-size: 28px;
    font-weight: bold;
    text-align: center;
}

.txt2 {
    position: absolute;
    left: 239px;
    top: 74px;
    width: 250px;
    color: #fee995;
    font-size: 22px;
    text-align: center;
}

.bg3 {
    text-align: center;
    background-image: url("../images/bg3.png");
    width: 997px;
    border: 2px solid #fff69e;
    padding: 35px 0 60px 0;
    margin: 0 auto;
}

.bg3 ul {
    margin: 0 105px;
}

.bg3 li {
    position: relative;
    overflow: hidden;
    line-height: 75px;
    font-size: 30px;
    color: #fff69e;
    text-decoration: underline solid #fff69e;
    text-align: left;
    text-indent: 40px;
}

.bg3 li::after {
    content: '';
    position: absolute;
    left: -0px;
    top: 24px;
    width: 34px;
    height: 29px;
    background: url("../images/icon.png") no-repeat;
    background-size: contain;
}

.bg4 {
    background-image: url("../images/bg4.png");
    width: 432px;
    height: 112px;
    margin: 0 auto;
}

.mask,
.bg {
    background-image: url(../images/bg.png);
    position: fixed;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
}

.tc {
    position: fixed;
    top: 50%;
    left: 50%;
    width: 900px;
    height: 500px;
    margin: -250px 0 0 -450px;
    background-color: #fff;
    border-radius: 20px;
    color: #000;
}

.tc .bt {
    line-height: 80px;
    border-bottom: 1px solid #dcdcdc;
    font-weight: bold;
    font-size: 40px;
    text-align: center;
}

.tc .txt {
    padding: 25px 0 0 195px;
    font-size: 20px;
}

.tc .red {
    font-size: 26px;
    padding: 15px 0;
    color: #d43839;
}

.tc .zj {
    letter-spacing: 16px;
}

.tc .yzm {
    border: solid 1px #808080;
    border-radius: 40px;
    font-size: 28px;
    text-align: center;
    color: #999;
    width: 190px;
    height: 45px;
    position: absolute;
    left: 640px;
    top: 331px;
    line-height: 45px;
}

.tc .yzm:hover {
    background-color: #f4f4f4;
}

.tc .tc-btn1 {
    width: 170px;
    height: 63px;
    background-color: #2774bc;
    text-align: center;
    line-height: 63px;
    display: block;
    margin: 40px 0 0 80px;
    color: #fff;
    border-radius: 10px;
    float: left;
}

.tc .tc-btn1:hover {
    background-color: #3689ce;
}

.tc .tc-btn2 {
    width: 170px;
    height: 63px;
    background-color: #e6e6e6;
    text-align: center;
    line-height: 63px;
    display: block;
    margin: 40px 0 0 60px;
    color: #000;
    border-radius: 10px;
    float: left;
}

.tc .tc-btn2:hover {
    background-color: #cecece;
}

.tc2 {
    position: fixed;
    top: 50%;
    left: 50%;
    width: 760px;
    height: 190px;
    margin: -95px 0 0 -380px;
    background-color: #fff;
    border-radius: 20px;
    color: #000;
    text-align: center;
    line-height: 190px;
    display: none;
    font-size: 40px;
}

input {
    background: #fff;
    border: solid 1px #808080;
    width: 330px;
    padding: 0 10px;
    height: 45px;
    line-height: 45px;
    color: #999;
    list-style: none;
    font-family: "微软雅黑";
    font-size: 28px;
    outline: none;
}

.dh {
    -webkit-animation: dh 0.3s linear infinite alternate;
    animation-name: dh 0.3s linear infinite alternate;
}

@-webkit-keyframes dh {
    to {
        -webkit-transform: scale(1.1);
        transform: scale(1.1);
    }
}

@keyframes dh {
    to {
        -webkit-transform: scale(1.1);
        transform: scale(1.1);
    }
}

.hdgz {
    background-color: #fff;
    width: 820px;
    border-radius: 10px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -332px 0 0 -410px;
    color: #000;
    padding: 25px;
    font-size: 16px;
    line-height: 25px;
}

.hdgz ul {
    padding-left: 20px;
}

.hdgz li {
    list-style-type: decimal;
}

.tc3 {
    position: fixed;
    top: 50%;
    left: 50%;
    margin: -269px 0 0 -226px;
}

.tc-btn {
    background: url("../images/tc-btn.png");
    width: 351px;
    height: 96px;
    text-align: center;
    line-height: 90px;
    color: #fff6d2;
    font-size: 32px;
}

.txt3 {
    position: absolute;
    left: 99px;
    top: 134px;
    width: 250px;
    color: #fee995;
    font-size: 45px;
    text-align: center;
}

.txt4 {
    position: absolute;
    left: 101px;
    top: 88px;
    width: 250px;
    color: #fff;
    font-size: 30px;
    font-weight: bold;
    text-align: center;
}

.an1 {
    position: absolute;
    top: 375px;
    left: 48px;
}

.an2 {
    position: absolute;
    top: 393px;
    left: 48px;
}

.an3 {
    position: absolute;
    top: 418px;
    left: 48px;
}

.server {
    background: url("../images/server.png");
    width: 120px;
    height: 120px;
    position: fixed;
    top: 330px;
    right: 0px;
}


.dh2 {
    -webkit-animation: dh2 2s linear infinite alternate;
    animation-name: dh2 2s linear infinite alternate;
}

@-webkit-keyframes dh2 {
    0%, 20%, 50%, 80%, 100% {
        -webkit-transform: translateY(0)
    }
    40% {
        -webkit-transform: translateY(-30px)
    }
    60% {
        -webkit-transform: translateY(-15px)
    }
}

@-moz-keyframes dh2 {
    0%, 20%, 50%, 80%, 100% {
        -moz-transform: translateY(0)
    }
    40% {
        -moz-transform: translateY(-30px)
    }
    60% {
        -moz-transform: translateY(-15px)
    }
}

.close {
    background: url("../images/close.png") center top no-repeat;
    position: absolute;
    right: -25px;
    top: -25px;
    width: 30px;
    height: 30px;
}

.hidden {
    display: none;
}

.popup-center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}


.tc3, .bg {
    display: none;
}


.recordWrap {
    min-height: 502px;
}

.relative {
    position: relative;
}

@keyframes marquee {
    0% {
        transform: translateX(0%);
    }
    100% {
        transform: translateX(-100%);
    }
}

.marquee {
    position: absolute;
    left: -372px;
    top: 18px;
    width: 910px;
    font-size: 16px;
    color: #fee995;
    white-space: nowrap;
    overflow: hidden;
}

.marquee .list {
    animation: marquee 20s linear infinite;
}
