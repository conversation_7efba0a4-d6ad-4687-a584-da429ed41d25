var jsonMessage = location.search
var userInfo = {
  uid: '',
  pid: '',
  uname: '',
  maskname: '',
  realname: '',
  points: 0,
  token: '',
  totalCount: 0,
  channelcode:''
}
var Urls = www;

var recordList = []
//弹窗类型
var PopupTypeEnum = {
  starPopup: [6, 7], northPopup: 4, kingPopup: 5, couponPopup: 8
}

// 当前的奖品
var currentReward = null
// 奖品
var rewards = [
  { id: 4, name: '30天北上资金功能' },
  { id: 6, name: '7天五星研报' },
  { id: 2, name: '30积分' },
  { id: 5, name: '7天量王叠现' },
  { id: 7, name: '30天五星研报' },
  { id: 1, name: '10积分' },
  { id: 3, name: '21天使用期' },
  { id: 8, name: '30元优惠券' }]

//活动码
var configData = {
  receiveCode: 'renew20230808',
  totalCode: 'renew20230808',
  hotCode: 'renew20230808_count',
  cmpCode: 'ACRenew20230801',
  isSendppCode:'renew20230808_issendpp',
  baseCount: 5138
}
var hotCount = 0

//交互抽奖相关配置
let timer = null

var lotteryConfig = {
  rotateAngle: 0, // 旋转角度
  config: {
    duration: 3000, // 总旋转时间 ms级
    circle: 7, // 旋转圈数
    mode: 'ease-in-out'
  }, drawIndex: undefined, // 实际中奖索引 转盘图片排序
  run: false, // 是否在转动
  normalRotateAngle: 0, offsetAngle: 360 / rewards.length / 2, // 偏移角度,转盘计算最后角度后再转动的角度
  normalTransition: true
}

var rotateStyle = function () {
  return `transition: transform ${lotteryConfig.config.duration}ms ${lotteryConfig.config.mode};
       transform: rotate(${lotteryConfig.rotateAngle}deg);`
}

var normalRotateStyle = function () {
  return `transform: rotate(${lotteryConfig.normalRotateAngle}deg);`
}

$(document).ready(function () {
  init()

  $('.btn2').click(function () {
    $('#rulePopup').show()
  })
  $('#ruleClose').click(function () {
    $('#rulePopup').hide()
  })

  $('.toPay').click(function (e) {
    checkPermission(userInfo.pid)

    pushdatatocmp(userInfo.uname,"ACRenew20230801");
    //e.stopPropagation()
    //directToPay()
  })

  //直接跳转收银台
  $('.goIM').click(function (e) {
    e.stopPropagation()
    goIM('小智盈续费领福利活动')
  })

  //点击蒙层关闭
  $('.mask').click(function () {
    $(this).hide()
  })

  //点击转盘抽奖
  $('#doLottery').click(function () {
    console.log(lotteryConfig.run, 'run')
    if (lotteryConfig.run) return

    //call抽奖Api
    doLotteryApi()

  })

  $('.btn6').click(function () {
    $('#tc1').show()
  })

})
// 检查用户是否有权限参与
function checkPermission(pid) {
  if (pid != "888010000" && pid != "888010400") {
    layer.msg("本活动仅限小智盈用户参与");
    return false;
  }
  return true;
}

function getQueryString (name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  var r = window.location.search.substr(1).match(reg)
  if (r != null) return unescape(r[2])
  return null
}

/**
 * 跳转IM
 * @param fromname
 */
function goIM (fromname) {
  var b = new Base64()
  var ret = PC_JH('EM_FUNC_START_IM', '0,AC20230808,' + b.encode(fromname))
}

/**
 * 查询热度
 */
function getHotCount () {
  request(
    `${Urls}user/getcountbyactcode?actcode=${configData.hotCode}`).
    then(res => {
      if (res.code === '200') {

        var num = 0
        if (res.data) {
          num = res.data.split(',')[0]
        }
        var usedCount = parseInt(!!num ? num : 0)

        hotCount = configData.baseCount + usedCount
        updateHotCount(hotCount)
      }
    })
}

function updateHotCount (hotCount) {

  var hotCountDom = $('#hotCount')
  $(".txt1").html(hotCount);
  if (hotCount >= 20000) {
    hotCountDom.css('width', '100%')
    return
  }
  hotCountDom.css('width', `${(hotCount / 20000 * 100).toFixed(2)}%`)
}



/**
 * 抽奖
 */
function doLotteryApi () {
  request(
      `${Urls}lottery0808/dolottery?actcode=${configData.totalCode}&uid=${userInfo.uid}&pid=888010000&platform=pc`).then(res => {
    console.log(res, '抽奖')
    if (res.code === '200') {
      //弹出成功弹窗并且刷新页面用户信息
      lotteryConfig.drawIndex = rewards.findIndex(
          item => item.id === res.data.id) + 1

      if (lotteryConfig.drawIndex) {
        //动画效果
        clearInterval(timer)

        lotteryConfig.normalTransition = false
        lotteryConfig.run = true

        lotteryConfig.rotateAngle = lotteryConfig.config.circle * 360 -
            (lotteryConfig.drawIndex * 360) / rewards.length +
            lotteryConfig.offsetAngle

        document.getElementById(
            'Lottery').style.cssText = normalRotateStyle() + rotateStyle()

        currentReward = res.data;
      }
    }
  })
}

function showSuccessfulPopup (data) {
  // 成功后弹窗
  if (PopupTypeEnum.starPopup.includes(data.id)) {
    if (data.tipName) {
      $('.txt4').text(data.tipName)
    }
    $('#starPopup').show()
  } else if (data.id === PopupTypeEnum.northPopup) {
    $('#northPopup').show()
  } else if (data.id === PopupTypeEnum.kingPopup) {
    $('#kingPopup').show()
  } else if (data.id === PopupTypeEnum.couponPopup) {
    $('#couponPopup').show()
  } else {
    if (data.tipName) {
      $('.rewardName').text(data.tipName)
    }
    $('#publicPopup').show()
  }
}

/**
 * 查询可用积分
 */
function getUserPoints () {
  request(
      `${Urls}renew2023/querypoint?actcode=${configData.totalCode}&uid=${userInfo.uid}`).
  then(res => {
    console.log(res, '查记录')
    if (res.code === '200') {
      if (res.data) {
        userInfo.points = res.data
        $('#point').text(userInfo.points);
      }
    }
  })

}

/**
 * 获取抽奖的记录
 */
function getRecordList () {
  request(
    `${Urls}lottery0808/getmylotteryinfo?actcode=${configData.totalCode}&uid=${userInfo.uid}&pid=888010000`).
    then(res => {
      console.log(res, '查记录')
      if (res.code === '200') {
        if (res.data.length) {
          recordList = res.data.slice(0, 5)
          updateRecord()
        }
      }
    })
}

function updateRecord () {
  var htmlDom = ''
  for (var i = 0; i < recordList.length; i++) {
    item = recordList[i]
    htmlDom += '<li>' +
      item.writeTime.replace(/^(\d{4})-(\d{2})-(\d{2})$/, '$1年$2月$3日') +
      '参加抽奖，成功领取' + item.benefitName + '！</li>'
  }
  $('#recordWrap').html(htmlDom)
}

/**
 * 直接跳转收银台
 */
function directToPay () {
  console.log('直接跳转收银台')

  // var url = ''
  // location.href = url
}

/**
 * 转盘结束
 */
function finished () {
  if (lotteryConfig.run) {
    if (currentReward) {
      showSuccessfulPopup(currentReward)
    }
    setTimeout(() => {
      lotteryConfig.run = false
      document.getElementById('Lottery').style.cssText = ''


      /**
       * 更新用户信息跟页面信息
       */
      getTotalCount();
      getRecordList()
      getUserPoints()
      updateHotCount()
    }, 2000)
  }
}

/**
 * 获取次数
 */
function getTotalCount () {
  request(
    `${Urls}lottery0808/getlotterycountbyday?actcode=${configData.totalCode}&uid=${userInfo.uid}`).
    then(res => {
      console.log(res, '总次数')
      if (res.code === '200') {
        userInfo.totalCount = res.data
        if (userInfo.totalCount === '3') {
          $('#lotteryTips').text('1次免费抽奖待使用')
        } else {
          $('#lotteryTips').text('20积分兑换1次抽奖')
        }
      }
    })
}

/**
 * 领取88积分和100优惠券
 */

function getFreeBenefits () {
  request(
    `${Urls}lottery0808/sendpp?actcode=${configData.totalCode}&uid=${userInfo.uid}&pid=888010000`).
    then(res => {
      if (res.code === '200') {
        layer.msg('您已成功领取88积分');
        console.log(res, '发88积分和100优惠券', '成功', '刷新用户页面积分信息')

      }
    })
}

function checkIsSendpp(){
  request(
      `${Urls}user/issubmitbyactcodes?actcodes=${configData.isSendppCode}&uid=${userInfo.uid}`).
  then(res => {
    if (res.code === '200') {
      var num = "";
      if (res.data) {
        num = res.data.split(',')[0]
      }
      if(!!num){
        checkPopupShow();
      }else{
        //未领取过88积分+优惠券
        getFreeBenefits();
      }
    }
  })
}

/**
 * 查询当日是否弹出弹窗
 */
function checkPopupShow () {
  request(
    `${Urls}lottery0808/gettipstatusbyday?actcode=${configData.totalCode}&uid=${userInfo.uid}`).
    then(res => {
      console.log(res)
      if (res.code === '200' && res.data) {
        if (res.data === 'false') {
          $('#tc4').show()
        }
      }
    })
}

function request (url, dataType) {
  return new Promise((resolve, reject) => {
    $.ajax({
      type: 'get',
      url: url,
      dataType: dataType ? dataType : 'jsonp',
      success: function (data) {
        console.log(data)
        if (data.code === '200') {
          resolve(data)
        } else {
          if (url.indexOf("sendpp") > -1) {
          } else {
            layer.msg(data.msg)
          }
        }
      },
      error: function (err) {
        reject(err)
      }
    })
  })
}



function generateMarquee () {
  var result = []

  for (let i = 0; i < 10; i++) {
    var XXX = Math.floor(Math.random() * 1000) // 随机生成三位数的数字
    var YYYArr = [
      '10积分',
      '30积分',
      '7天五星研报',
      '30天北上资金',
      '7天量王叠现',
      '30元优惠券']
    var YYY = YYYArr[Math.floor(Math.random() * YYYArr.length)] // 随机从 YYYArr 数组中选取一项
    result.push(`恭喜EMY${XXX.toString().padStart(3, '0')}****！领取${YYY}！`)
  }
  $('#marquee .list').html(result.join(' '))
}

function test () {

}


function pushdatatocmp(uname, adcode) {
  var data = {
    "appid": '10088',
    "logtype": 'click',
    "mid": '',
    "pid": userInfo.pid,
    "sid": getQueryString("sid"),
    "tid": getQueryString("tid"),
    "uid": userInfo.uid,
    "uname": uname,
    "adcode": adcode,
    "targeturl": "",
    "pageurl": window.top.location.href
  }
  var saasUrl = "https://ds.emoney.cn/saas/queuepush";
  var saasSrc = saasUrl + "?v=" + Math.random()
      + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
      + "&message=" + encodeURIComponent(JSON.stringify(data));

  var elm = document.createElement("img");
  elm.src = saasSrc;
  elm.style.display = "none";
  document.body.appendChild(elm);
}

