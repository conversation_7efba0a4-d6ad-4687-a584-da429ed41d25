@charset "utf-8";
body {font-size:12px;margin:0;padding:0;line-height:22px;background-color:#9A2426; color:#fff;font-family:"微软雅黑";}
.main {width:100px; margin:0 auto; position:relative;}
ul,li {margin:0;padding:0;list-style:none;}
A:link {COLOR: #ffffff; TEXT-DECORATION: none}
A:visited {COLOR: #ffffff; TEXT-DECORATION: none}
A:hover {COLOR: #ffff00; TEXT-DECORATION: none}
A.b:link {COLOR: #fff; TEXT-DECORATION: none}
A.b:visited {COLOR: #fff; TEXT-DECORATION: none}
A.b:hover {COLOR: #ffff00; TEXT-DECORATION: none}
.white {color:#ffffff;}
.bg_red { background-color:#008fff;}
.red {color:#e83828;}
.green {color:green;}
.black{ color:#000;}
.blue{color:#2ea5de;}
.org{color:#e83828;}
.gray{color:#444;}
.f14{font-size:14px;}
.f16{font-size:16px;font-family:"微软雅黑";}
.f18{font-size:18px;}
.f25{font-size:25px; font-weight: bold;}
.f35{font-size:35px;font-weight: bold; line-height: 55px;}
.f21{font-size:21px;}
.p_t10{padding-top:10px;}

.footer{text-align:center; font-family:"宋体"; font-size:12px; padding:0 0 40px 0;}


.bod .p{height:131px;}
.bod #dbg1{background:url("../images/index_01.jpg") center top no-repeat; height:293px;}
.bod #dbg2{background:url("../images/index_02.jpg") center top no-repeat; height: 403px;}
.bod #dbg3{background:url("../images/index_03.jpg") center top no-repeat; height:414px;}
.bod #dbg4{background:url("../images/index_04.png") center top no-repeat; height:396px;}
.bod #dbg5{background:url("../images/index_05.png") center top no-repeat; height: 356px;}

.btn1{
	background: url(../images/btn1.png) no-repeat right 0px;
	width: 566px;
	height: 119px;
	position: absolute;
	left: 74px;
	top: -164px;
}
.btn1h{
	background-color: #FDE285;
	width: 628px;
	height: 72px;
	line-height: 72px;
	text-align: center;
	color: #AF2E2E;
	position: absolute;
	left: 54px;
	top: -154px;
	font-size: 34px;
	font-weight: bold; border-radius: 36px;
}
.btn2{ background: url(../images/btn2.png);
	width: 280px;
	height: 53px; display: block; text-align: center; line-height: 53px; color: #fff; font-size: 34px; border-radius: 30px; margin: 0 auto;
}
.dh:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.btn_3{
	position: absolute;
	left: 366px;
	top: 58px;
	width: 233px;
	height: 33px;
}


.txt_2{
	position: absolute;
	top: 217px;
	left: -387px;
	font-size: 20px;
	line-height: 30px;
	width: 1009px;
	color: #fff;
}

.zz{
	position: absolute;
	top: -101px;
	left: -45px;background: url(../images/zz.png); width: 199px; height: 199px;
}
.zz2{
	position: absolute;
	top: -101px;
	left: -45px;background: url(../images/zz2.png); width: 199px; height: 199px;
}
.go{
	position: absolute;
	top: -101px;
	left: -45px;width: 199px; height: 199px;box-sizing: border-box; text-align: center; color: #FDE285; font-size: 26px; padding-top: 68px; line-height: 30px;
}
.ico{
	position: absolute;
	top: 30px;
	left: 103px;
	background: url(../images/ico.png);
	width: 34px;
	height: 32px;pointer-events: none; 
-webkit-animation: ico 0.3s linear infinite alternate;
  animation-name: ico 0.3s linear infinite alternate;
}
@-webkit-keyframes ico{
  to { margin: 10px 0 0 10px;
  }
}

@keyframes ico{
  to {margin: 10px 0 0 10px;
  }
}

.md{
	position: absolute;
	top: 238px;
	left: -312px;
	width: 750px;
	color: #591924;
	font-size: 20px;
	line-height: 30px;
}


.dl{
	position: absolute;
	left: -366px;
	top: 313px;
	background: url("../images/bg1.png");
	width: 332px;
	height: 315px;
}
.dl ul{width: 273px; margin: 28px auto 0 auto;}
.dl li{border-bottom: dotted 1px #3e3a39; line-height: 35px; padding: 7px 0 0 12px; color: #3e3a39; font-size: 20px;}
.dl .wz{font-size: 24px; color: #fff; text-align: center; line-height: 50px;}
.srk{background: url("../images/srk1.png");
	width: 225px;
	height: 53px; border: 0;
	color:#969696;font-size:18px; padding:0 10px 0 50px; line-height:53px; margin: 27px 0 0 25px;outline:none;
}
.srk2{background: url("../images/srk2.png");
	width: 225px;
	height: 53px;border: 0;
	color:#969696;font-size:18px; padding:0 10px 0 50px; line-height:53px; margin: 23px 0 0 25px;outline:none;
}

.pf{background:url("../images/pf.png"); width:136px; height:131px; position:fixed; top: 20%; right: 0px;}


.jpzx{padding-top:10px; overflow:auto; height:120px;}

.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/h.png); display: none;}
.tc_2{background:url(../images/tc2.png); width:646px; height:400px; position:absolute; left: 50%; top: 50%; margin: -200px 0 0 -323px; display: none;}
.tc_3{background:url(../images/tc3.png); width:682px; height:755px; position:absolute; left: 50%; top: 50%; margin: -377px 0 0 -341px; display: none;}
.nav{
	position: absolute;
	left: 128px;
	top: 188px;
}
.nav a{ font-size: 24px; color: #736357; margin-right:80px;border-bottom: 4px solid #FFF8F0; padding-bottom:3px;}
.nav a:hover,.nav .on{border-bottom: 4px solid #CC2514;}
.btn3{
	background: url(../images/btn3.png) no-repeat right 0px;
	width: 391px;
	height: 99px;
	position: absolute;
	left: 143px;
	top: 425px;
}
.pf1{
	position: absolute;
	left: 88px;
	top: 264px;
	color: #736357;
	font-size: 24px;
	line-height: 65px;
	text-align: right;
	width: 483px;
}
input { padding: 0 15px;
	border: 0px;background:none;outline: 0;overflow: hidden;box-sizing: border-box;
	color: #666666;
	font-size:28px; height: 65px; line-height: 65px; margin-left: 10px; width: 377px; margin-bottom: 10px;
}
.yzm{
	width: 130px;
	height: 62px;
	position: absolute;
	left: 337px;
top: 1px;
	text-align: center;
	line-height: 62px;
	font-size: 20px;
	background-color: #CC2514;
	color: #fff!important;
	TEXT-DECORATION: none !important;
}

.tc_txt3{
	font-size: 14px;padding-right: 47px;
	color: #c30d23;
	text-align: center;
}
.close{
	position: absolute;
	top: 3px;
	right: 5px;background:url("../images/close.png");
	width: 32px;
	height: 32px;
}


.tc{background:url(../images/tc.png); width:560px; height:286px; position:absolute; left: 50%; top: 50%; margin: -243px 0 0 -280px; display: none;}

.tc_txt1{
	padding: 50px 0 0 0; height: 140px;
	font-size: 24px;
	color: #4d4d4d;
	text-align: center; line-height:35px;
}
.tc_txt2{padding: 70px 0 0 0;
	font-size: 24px;
	color: #ec464d;
	text-align: center; line-height:40px;
}

.tc_txt4{
	padding: 150px 0 0 0;
	font-size: 74px;
	color: #e83828;padding-left: 10px;
	text-align: center;
	line-height: 80px; font-weight: bold;
}
.tc_txt5{
	padding: 10px 0 0 0;
	font-size: 32px;
	color: #591924; padding-right: 47px;
	text-align: center;
	line-height: 48px;
}
.tc_txt6{padding: 80px 0 0 0;
	font-size: 24px;
	color: #4d4d4d;
	text-align: center; line-height:50px; 
}
.tc_txt7{padding: 70px 0 0 0;
	font-size: 36px;
	color: #ec464d;
	text-align: center; line-height:45px; font-weight: bold;
}
.tab{margin: 20px auto 0 auto;}
.tab td {
  border : 1px solid #a37c52; text-align: center; color:#1B1B1B; font-size: 18px; padding: 10px;
  border-collapse: collapse;
}
