@charset "utf-8";
/*通用样式处理*/
input:focus,
select:focus,
textarea:focus,
button:focus {
    outline: none;
}
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}
body {
    line-height: 1;
}
ol,
ul {
    list-style: none;
}
blockquote,
q {
    quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
    content: "";
    content: none;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
html,
body {
    width: 100%;
    height: 100%;
}
* + html html,
body {
    height: auto;
}
body {
    font: 14px/20px "Microsoft YaHei", SimHei, "HelveticaNeue", "Helvetica Neue",
        Helvetica, Arial, sans-serif;
    color: #333;
    -webkit-text-size-adjust: 100%;
    background: #000;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: normal;
    font-size: 1em;
}
h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a {
    font-weight: inherit;
}

em {
    font-style: normal;
}
strong {
    font-weight: normal;
}
small {
    font-size: 80%;
}

a {
    text-decoration: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    cursor: pointer;
}
a,
a:visited {
    color: #333;
    text-decoration: none;
    outline: 0;
}
a:hover,
a:focus {
    color: #333;
}
p a,
p a:visited {
    line-height: inherit;
}

ul {
    list-style: none outside;
}
ol {
    list-style: decimal;
}
ol,
ul.square,
ul.circle,
ul.disc {
    margin-left: 30px;
}
ul.square {
    list-style: square outside;
}
ul.circle {
    list-style: circle outside;
}
ul.disc {
    list-style: disc outside;
}
ul ul,
ul ol,
ol ol,
ol ul {
    margin: 4px 0 5px 30px;
    font-size: 90%;
}
i {
    font-style: normal;
}

img.scale-with-grid {
    max-width: 100%;
    height: auto;
}

.button,
button,
input[type="submit"],
input[type="reset"],
input[type="button"],
input[type="number"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    font: 14px "Microsoft YaHei", SimHei, "HelveticaNeue", "Helvetica Neue",
        Helvetica, Arial, sans-serif;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0;
}
input:disabled {
    border: 1px solid #ddd;
    background-color: #f5f5f5;
    color: #aca899;
}

input[type="text"],
input[type="date"],
input[type="tel"],
input[type="password"],
input[type="email"],
input[type="number"],
textarea {
    border: 1px solid #dcdcdc;
    outline: none;
    font: 14px "Microsoft YaHei", SimHei, "HelveticaNeue", "Helvetica Neue",
        Helvetica, Arial, sans-serif;
    color: #000;
    max-width: 100%;
    display: block;
    background: #fff;
    -webkit-appearance: none;
    -moz-appearance: none;
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
}
select {
    border: 1px solid #dcdcdc;
    font: 14px "Microsoft YaHei", SimHei, "HelveticaNeue", "Helvetica Neue",
        Helvetica, Arial, sans-serif;
    color: #000;
    max-width: 100%;
    display: block;
    background: #fff;
    margin: 0;
}
input[type="text"]:focus,
input[type="date"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
textarea:focus,
select:focus {
    color: #333;
    border-color: #448aca;
}
textarea {
    min-height: 40px;
}
label,
legend {
    display: block;
    font-weight: normal;
}
input[type="checkbox"] {
    display: inline;
}
input[type="checkbox"],
input[type="radio"] {
    vertical-align: middle;
    margin: 0 3px 3px 0;
}
input.readonly,
.readonly {
    background-color: #eee;
}

.pr {
    position: relative;
}
.pa {
    position: absolute;
}

.fl {
    float: left;
}
.fr {
    float: right;
}
.clear {
    width: 100%;
    clear: both;
    font-size: 0px;
    height: 0px;
    line-height: 0px;
    margin: 0;
}
.imgb img {
    display: block;
}

.box-sizing {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.borderradius {
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
}
/*通用样式处理 end*/

.main_web {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    padding-left: 0px;
    padding-right: 0px;
}

.video_area {
    position: absolute;
    left: 0;
    right: 0;
    top: 0px;
    bottom: 0px;
}
.vtool-bar {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 53px;
    background-color: transparent;
    z-index:2147483647;
}
.vtool-bar .tool_area {
    height: 53px;
    visibility: hidden;

    /* border-top: solid 1px rgba(54, 54, 54, 0.24); */
    background-image: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0),
        rgba(0, 0, 0, 0.6)
    );

    /* background-color: #272727; */
}
body:hover .vtool-bar .tool_area {
    visibility: visible;
}
.progress_area {
    height: 2px;
    position: absolute;
    left: 0;
    right: 0;
    background-color: rgba(102, 102, 102, 0.329);
    font-size: 0;
}
.progress_area:hover {
    height: 10px;
    position: absolute;
    left: 0;
    right: 0;
    top: -8px;
    background-color: rgba(81, 81, 81, 0.43);
    font-size: 0;
}
.progress_area:hover .ui-slider-handle {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #20a0ff;
    border-radius: 50px;
    top: 50%;
    margin-top: -6px;
    margin-left: -6px;
    left: 50px;
    outline: none;
}

a.play_area {
    display: block;
    width: 48px;
    height: 53px;
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAYFBMVEUAAAAgoP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8A0xzOAAAAH3RSTlMAYw756dyIPuLESPOMg0MZ3grMyqBZKHUgsadqJ0sqwwhHGgAAAPRJREFUKM+Fk+luxCAQg4dwhARyH81ut/X7v2VbiBhIKq1/JMCHLMsaKKk2jZJSNaamq4RGkhYFOhbA+vaYqu8vb4HlyBwV7MbbzUIl71biWeVG1ROyjcuXg6eLPNwrLBY86KYHlpATtjrdPjNni7/MOnzDATK6Qf8mxUgJ4oPpiJoMDMOMBtCgZ5jTHg0pDAVEd+4GKJIghgWF/AcKhjdbwba3QCIPZNAxzBh1MJcSRFFCWZ8o6ovFTyfM2DTGmzNWumnFHP6743q5WLenMVmnHE1rHJNIHaxgJixcZNF5BnTXD0RD32lg3t8PNav28Tl4fg4/J7QVNcq6EX0AAAAASUVORK5CYII=")
        no-repeat center;
    cursor: pointer;
    float: left;
}
a.play_area.pause {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAVFBMVEUAAACPj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj48iNJC1AAAAG3RSTlMAA/Ni6dyIPgziy0iMQycZ3sTCoIQgsadcVSouKz3wAAAA3ElEQVQoz4WT227DIBBEd5ebwfiS2E7Szv//Z5FdbV2H0nkApCMdoWUgTfA3K2JvPtAlbBw0zvCZbQmIud+67rPPEUjbyWgRX6yWV4RVdy94dmdR94T0x3EUZLokQ8bdkvDgK+QHEpfdIB5OQNdijjBlc2V9h/QBV26KmWuQZwTy8FSDO7hjqMMBd7KY6nCCJQHXIUOasKltX8hjrcMVvjmE1vj4z8HPMMeTLfSWBYm/H9tfmYeMWpPld02WvSZKo+GfmkZluzkBbh0m5mlYHZDG/0p9xiEf3yEHRV/J0xPa89te+QAAAABJRU5ErkJggg==");
}
.play_time {
    float: left;
    padding: 0 15px 0 5px;
    line-height: 53px;
    color: #fff;
}

.livetool_area {
    position: absolute;
    visibility: hidden;
    left: 0;
    right: 0;
    bottom: 0;
    height: 53px;
    border-top: solid 1px rgba(160, 160, 160, 0.23);
    background-image: linear-gradient(  180deg, rgba(0, 0, 0, 0),  rgba(0, 0, 0, 0.6) );
}

.livevideo_area {
    position: absolute;
    left: 0;
    right: 0;
    top: 0px;
    bottom: 0;
    z-index:200000;
}

.volume_area {
    float: right;
    margin-right:20px;
}
a.volume_a {
    display: block;
    width: 40px;
    height: 53px;
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAMAAAC6V+0/AAAAXVBMVEUAAABzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3Nzc3MuQoNjAAAAHnRSTlMA8e7JURfqiBELBPjh2dKSg3larY9MPTYpnmthRR+kZihkAAAAlklEQVQY023QVw7DIBBF0TeAaW7gmsr+lxlcghzI/UJHGmkYpGyPIjOGwio+Um63RiFi3aoq2SIf2NDMnAYAVms9MIEdYz5EbRhj7YqE8FSdzxOpNwBXvxhCD4guRwZo+od1jqocl5OF5Q48riSXy0qKjk+95PxF64LA0SqfOxrBSVwO4jesOxdnU+92ilgeNKDM3NPzA4BaBY+WBkW8AAAAAElFTkSuQmCC")
        no-repeat center;
    float: left;
    cursor: pointer;
}
a.col_close_stop {
    background: url(../images/icon_mute.png) no-repeat center;
}
.volume_slide {
    width: 100px;
    height: 2px;
    font-size: 0;
    background-color: #ccc;
    float: left;
    margin-top: 25px;
    position: relative;
}
.ui-slider-horizontal .ui-slider-range-min {
    background: #01beff;
}
.ui-slider .ui-slider-range {
    position: absolute;
    display: block;
    border: 0;
}
.ui-slider-horizontal .ui-slider-range {
    top: 0;
    height: 100%;
}
.ui-slider-horizontal .ui-slider-range-min {
    left: 0;
}
.ui-slider .ui-slider-handle {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #20a0ff;
    border-radius: 50px;
    top: 50%;
    margin-top: -6px;
    margin-left: -6px;
    left: 50px;
    outline: none;
}

a.wifi_a {
    display: none;
    float: right;
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAACCElEQVQ4T62UPWgUQRTHf28WXQU1ipUWacRGJEFBCRe87NucCApCUKwsTKFgo4WYSsjFTkRQEGIhRi0EQcTCIsbbmzNFPFL5UdiIXRQEBT84i7AzssfuEU0KP266mfnP7/He/70RurRkJc7c3NzaxcXFEefcYWAvkAKPjTHPwzCcHhgY+Pr7u2WgWq3WFwTBNWAPsBpYlT/6AgTe+wXgbBzHT5bCfgElSVI1xoyLyF3gk/f+FLAzTdPtQRDMANeBfmCfc25ieHi4WsA6oHq9fkBEpguBtdYCC6p6PBNbax8Am1VVi4BpmvZXKpVX2X0b1Gw2N7RarXci8h2YAtYD54D7wJu2UGTQe78fuAJ8A0aAnjAMd5RKpR9tUJIkoyJyA3ghIq0scpYS8GxJHXqAXcDrLG1gjYjsFpHRoaGhe22QtXZSRPqiKBrM9yeAi6raW4CSJNlmjHkLjKrq7Vw3D8yo6oUClAmequrpTNBoNM5774+pauZce1lr12UpichYFEWX87Ob3vuNcRwf7TpoEjjknLuVRTLGZBarc+5qx16RTSJyBnjknHuZG3BSRKY6qXWt2Ln98yLyUVXLs7OzW9I0fe+9PxLH8cO8bmPe+0tBEGwtl8sfGo3GHe99bxiGBzv2Z8K/aUhr7ThQXdaQSyz+/xEpYCsM7ed8eM0fD20B68o38q//3E9a5yIiG9sQXgAAAABJRU5ErkJggg==")
        no-repeat center;
    cursor: pointer;
    width: 52px;
    height: 36px;
    position: absolute;
    right: 163px;
    bottom: 0px;

}
.net_list {
    height: 104px;
    width: 100%;
    line-height: 26px;
    overflow-x: hidden;
    overflow-y: auto;
    display:none;
}
.layui-layer-content .net_list{
    display:block;
}
.net_list .iner {
    padding: 5px 0 5px 10px;
}
.net_list a {
    display: block;
    padding-left: 20px;
    background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAMAAAAMCGV4AAAAVFBMVEUAAACysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrK9cpK1AAAAHHRSTlMAGgX678C5oGtfS0Ep6eLa1dTDsq2WfHplUjATffrs0QAAAHBJREFUCNdNzEkSgCAMRNEmzALOs/e/p4qi+btXqTSuyGmppHaE3N5MgUBhavabUa14WlUEqHMouY7gLf6shwnMwUAdzCJBgkUSSfB7jaFirgZsI/O4Af3ycenzz/xyrkUe0cbHI3qjRZmxbWptHj4Br/kDtWYxzyIAAAAASUVORK5CYII=")
        no-repeat left center;
    height: 26px;
    overflow: hidden;
}
.net_list a.on {
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAMAAAAMCGV4AAAAclBMVEUAAACysrKysrKysrKysrKysrKysrKysrKysrKysrIUit6ysrKysrKysrKysrIUit6ysrKysrKysrKysrIUit4Uit4Uit6ysrKysrKysrKysrKysrKysrKysrIUit4Uit6ysrKysrKysrKysrIUit4Uit6p77ZGAAAAJXRSTlMAGwf679W9e2dfS0tBKenm4sIV2tnQxLayraKflmxlX1IwBcMJTJsp1gAAAIlJREFUCNc1y0cWgyAAANERKwIae6+J979iEJ9/N4vBEknhB36RCJyjmrVA6Lk6sEyQ8EgCA2e4YP2wlvBETsCaX/kKTJJIw3bdNtARQQm16xq8DB+4HiB8shIalw14H3oFqesUVM8+AGmbtzYZdvjGvOKC+4nf/HhYXtRJUxrZRTYdNYZZOCqsP9msCgQtWRMmAAAAAElFTkSuQmCC");
    background-color: #e6f4ff;
}
a.net_btn {
    display: block;
    width: 100px;
    height: 38px;
    margin: 20px auto 0;
    background-color: #20a0ff;
    line-height: 38px;
    text-align: center;
    color: #fff;
    cursor: pointer;
}

.t_center {
    width: 50%;
    margin: 0 auto;
    height: 53px;
    line-height: 53px;
    text-align: center;
    overflow: hidden;
    color: #ffffff6e;
    font-weight: 100;
}
body:hover .livetool_area {
    visibility: visible;
}
body:hover a.wifi_a {
    display:block!important;
}

/* @media screen and (max-width: 500px) {} */