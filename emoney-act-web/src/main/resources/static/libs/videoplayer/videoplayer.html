<!doctype html>
<html xmlns:gs="http://www.gensee.com/ec">

<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="renderer" content="webkit">
	<title>直播demo</title>
	<link href="css/live-emnew.css" rel="stylesheet" type="text/css">
</head>

<body>
	<div class="main_web">
		<div class="main_center" id="mainshow">

		</div>
	</div>

	<!--
		说明
		1、替换site="*************"里的值为自己站点的域名
		2、替换ownerid="5bdf01c4a34b4a63b1f980f2642728b4"为当前直播的id
		3、替换uid="55831"的值为当前用户id
		4、替换uname="user5623"为当前用户昵称
		5、如果设置密码请设置authcode=""的值为直播密码
	-->
	<script type="text/javascript"
		src="http://static.gensee.com/webcast/static/sdk/js/gssdk-1.3.js?201806v477"></script>
	<script type="text/javascript" src="js/jquery-1.9.1.min.js"></script>
	<script type="text/javascript" src="js/jquery-ui-1.10.3.custom.min.js"></script>
	<script type="text/javascript" src="js/layer/layer.js"></script>
	<script type="text/javascript" src="js/utils.js?t=2"></script>
	<script type="text/javascript" src="../scripts/imlive/videoClickLikes1.js?rev=76949a5a7b5f" type="text/javascript"
		charset="utf-8"></script>
	<script type="text/javascript" src="../scripts/imlive/vote.js?rev=76949a5a7b5f" type="text/javascript"
		charset="utf-8"></script>
	<script type="text/javascript">
		; (function ($) {
			// $.cookie
			!(function ($) {
				var pluses = /\+/g;

				function encode(s) {
					return config.raw ? s : encodeURIComponent(s);
				}

				function decode(s) {
					return config.raw ? s : decodeURIComponent(s);
				}

				function stringifyCookieValue(value) {
					return encode(config.json ? JSON.stringify(value) : String(value));
				}

				function parseCookieValue(s) {
					if (s.indexOf('"') === 0) {
						// This is a quoted cookie as according to RFC2068, unescape...
						s = s
							.slice(1, -1)
							.replace(/\\"/g, '"')
							.replace(/\\\\/g, "\\");
					}

					try {
						// Replace server-side written pluses with spaces.
						// If we can't decode the cookie, ignore it, it's unusable.
						// If we can't parse the cookie, ignore it, it's unusable.
						s = decodeURIComponent(s.replace(pluses, " "));
						return config.json ? JSON.parse(s) : s;
					} catch (e) { }
				}

				function read(s, converter) {
					var value = config.raw ? s : parseCookieValue(s);
					return $.isFunction(converter) ? converter(value) : value;
				}

				var config = ($.cookie = function (key, value, options) {
					// Write

					if (value !== undefined && !$.isFunction(value)) {
						options = $.extend({}, config.defaults, options);

						if (typeof options.expires === "number") {
							var days = options.expires,
								t = (options.expires = new Date());
							t.setTime(+t + days * 864e5);
						}

						return (document.cookie = [
							encode(key),
							"=",
							stringifyCookieValue(value),
							options.expires
								? "; expires=" + options.expires.toUTCString()
								: "", // use expires attribute, max-age is not supported by IE
							options.path ? "; path=" + options.path : "",
							options.domain ? "; domain=" + options.domain : "",
							options.secure ? "; secure" : ""
						].join(""));
					}

					// Read

					var result = key ? undefined : {};

					// To prevent the for loop in the first place assign an empty array
					// in case there are no cookies at all. Also prevents odd result when
					// calling $.cookie().
					var cookies = document.cookie ? document.cookie.split("; ") : [];

					for (var i = 0, l = cookies.length; i < l; i++) {
						var parts = cookies[i].split("=");
						var name = decode(parts.shift());
						var cookie = parts.join("=");

						if (key && key === name) {
							// If second argument (value) is a function it's a converter...
							result = read(cookie, value);
							break;
						}

						// Prevent storing a cookie that we couldn't decode.
						if (!key && (cookie = read(cookie)) !== undefined) {
							result[name] = cookie;
						}
					}

					return result;
				});

				config.defaults = {};

				$.removeCookie = function (key, options) {
					if ($.cookie(key) === undefined) {
						return false;
					}

					// Must not alter options, thus extending a fresh object...
					$.cookie(key, "", $.extend({}, options, { expires: -1 }));
					return !$.cookie(key);
				};
			})($);
			var Util = {
				trim: function (str) {
					if (typeof str !== "string") {
						return str;
					}
					if (typeof str.trim === "function") {
						return str.trim();
					} else {
						return str.replace(/^(\u3000|\s|\t|\u00A0)*|(\u3000|\s|\t|\u00A0)*$/g, "");
					}
				},
				isEmpty: function (obj) {
					if (obj === undefined) {
						return true;
					} else if (obj == null) {
						return true;
					} else if (typeof obj === "string") {
						if (this.trim(obj) == "") {
							return true;
						}
					}
					return false;
				},
				isNotEmpty: function (obj) {
					return !this.isEmpty(obj);
				},
				breachHTML: function (str) {
					if (typeof str !== "string" || this.isEmpty(str)) return str;
					return str.replace(/\</g, "&lt;");
				},
				escapeHTML: function (str) {
					if (typeof str !== "string" || this.isEmpty(str)) return str;
					return str.replace(/\&/g, "&amp;").replace(/\</g, "&lt;");
				},
				checkTime: function (num) {
					var n = Number(num);
					if (n < 10) n = "0" + n;
					return n;
				},
				timeDuration: function (second) {
					if (!second || isNaN(second))
						return;
					second = parseInt(second);
					var time = '';
					var hour = second / 3600 | 0;
					if (hour != 0) {
						time += this.checkTime(hour) + ':';
					}
					var min = (second % 3600) / 60 | 0;
					time += this.checkTime(min) + ':';
					var sec = (second - hour * 3600 - min * 60) | 0;
					time += this.checkTime(sec);
					return time;
				},
				calcPercent: function (value, total) {
					if (isNaN(value) || Number(value) == 0)
						return "0";
					if (isNaN(total) || Number(total) == 0)
						return "0";
					return Math.round(Number(value) * 100 / Number(total));
				},
				formatTime: function (time) {
					var date = new Date();
					date.setTime(time);
					var h = date.getHours();
					var m = date.getMinutes();
					var s = date.getSeconds();
					return this.checkTime(h) + ":" + this.checkTime(m) + ":" + this.checkTime(s);
				},

				//占位符替换
				replaceholder: function (str, values) {
					return str.replace(/\{(\d+)\}/g, function (m, i) {
						return values[i];
					});
				},
				GetQueryString: function (name) {
					var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
					var r = window.location.search.substr(1).match(reg);
					if (r != null) return unescape(r[2]);
					return null;
				},
				//获取uid
				getUID: function () {
					var _this = this;
					var uid =
						$.cookie("expertnews.uid") || _this.GetQueryString("uid") || 0;
					return uid;
				},
				EMSSO: function () {
					var _this = this;
					var ssoSTR = location.search;
					var puressostr = ssoSTR.split('rand=')[1]
					return 'rand=' + puressostr;
				}
			};
			var vodchannel = GS.createChannel();
			var midtype = Util.GetQueryString('midtype') || "vod";
			var ownerid = Util.GetQueryString('ownerid') || 'a2b093ee6fb946d3bfd3fd9de81d038e'; //'a2b093ee6fb946d3bfd3fd9de81d038e';//'d0acc7dfd9554e8692e03823798d8d74'; 
			var uid = Util.GetQueryString('uid') || '1';
			var pid = Util.GetQueryString('pid') || '1';
			var uname = '策略大直播：' + uid + ':' + pid; //Util.GetQueryString('uname') || 'usrname';
			var authcode = Util.GetQueryString('authcode') || '123456';
			var appid = Util.GetQueryString('appid') || '1015010';
			var audiohtmlCodes = [];
			var voteid = Util.GetQueryString('voteid') || '30';

			function playLive() {

				layer.msg('直播链接中……', {
					time: 0
				});

				//直播状态监听
				vodchannel.bind("onStart", function (event) {
					vodchannel.send("submitMute", { "mute": true })
					console.log(event);
					$('#play_status').html("播放中");
				});
				// 暂停
				vodchannel.bind("onPause", function (event) {
					console.log(event);
					$('#play_status').html("已暂停");

				});
				// 播放中
				vodchannel.bind("onPlay", function (event) {
					console.log(event);
					$('#play_status').html("播放中");

				});
				// 结束
				vodchannel.bind("onStop", function (event) {
					console.log(event);
					$('#play_status').html("已结束");
				});

				// //收到系统消息
				// vodchannel.bind("onMessage", function (event) {
				// 	console.log(event);
				// 	$('#chat_body').append('<dl class="system"><dt><p>系统消息</p><span>'+Util.formatTime(event.data.time*1000)+'</span></dt><dd>'+event.data.content+'</dd></dl>');
				// 	$('#chat_body').scrollTo();
				// });


				//获取优选网络信息
				vodchannel.bind("onNetSettings", function (event) {
					console.log(event);
					var txt = '';
					var curNet = '当前网络';
					for (var i = 0; i < event.data.list.length; i++) {
						event.data.list[i].selected == "true" && (curNet = event.data.list[i].label);
						txt += '<a href="javascript:void(0);"' + (event.data.list[i].selected == "true" ? ' class="on"' : '') + '>' + event.data.list[i].label + '</a>';
					}
					$('.net_list').html('<div class="iner">' + txt + '</div>');

					layer.open({
						type: 1,
						title: '切换网络[当前:' + curNet + ']',
						closeBtn: 0,
						area: '300px',
						skin: '',
						shadeClose: true,
						content: $('.net_list').eq(0)
						, btn: ['切换网络', '取消']
						, yes: function (index) {

							if ($('.net_list a.on').length > 0) {
								var label = $('.net_list a.on').text();
								// 提交优选网络选择信息
								vodchannel.send("submitNetChoice", {
									"label": label
								});
								layer.close(index);
							} else {
								layer.msg('请选择网络');
							}

						}
					});
					$('#cover').fadeIn();
					$('.net_area').fadeIn();
				});

				//被踢出
				vodchannel.bind("onKickOut", function (event) {
					console.log(event);
					layer.alert("您已经被踢出");
				});

				vodchannel.bind('onUserOnline', function (evt) {
					$('#onlineCounts').text(evt.data.count);
					console.log(evt);
				})

				//双击视频/文档区域
				vodchannel.bind("onDoubleClick", function (event) {
					// if(event.data.widgetType == "vp"){
					// 	layer.msg(' 视频双击');
					// }else if(event.data.widgetType == "pd") {
					// 	layer.msg('文档双击')
					// }

				});

				//SDK状态通知 
				vodchannel.bind("onStatus", function (event) {
					console.log(event);
					var status = {
						1: 'License不足；',
						2: '点播未开始，等待点击开始按钮',
						3: '缓冲状态',
						4: '不能在ipad中播放',
						5: '正在执行seek命令',
						6: '表示有人登陆',
						7: '人数已经满了',
						8: '数据还没有准备好',
						9: '视频第一次缓冲播放开始'
					};

					layer.msg(event.data.type + ":" + status[event.data.type]);
				});

				//SDK加载完毕，所有API生效
				vodchannel.bind("onDataReady", function (event) {
					console.log(event);
					setTimeout(function () { sdkgo(); }, 0);
				});

				//异步执行sdk交互
				function sdkgo() {

					//提交音量信息
					$(".volume_slide").bind("slidechange", function (event, ui) {
						console.log(ui.value);
						vodchannel.send("submitVolume", {
							"value": ui.value / 100
						});
					});

					//获取优选网络
					$('#wifi_a').on('click', function () {
						vodchannel.send("requireNetSettings", {});
					});



				}
				//API错误通知
				vodchannel.bind("onAPIError", function (event) {
					console.log(event);
				});

				//相关操作处理
				$(function () {
					$('#uname').html($('#videoComponent').attr('uname'));


					//设置音量默认75
					$(".volume_slide").slider({
						value: 75,
						range: "min",
						change: function () {
							$(".volume_slide span").width($(".volume_slide a").css("left"));
						}
					});
					var preValue = 75;
					function voiceOper() {
						if ($(".volume_a").hasClass("col_close_stop")) {
							vodchannel.send("submitMute", { "mute": false });
							$(".volume_a").removeClass("col_close_stop");
							$(".ui-slider-handle").css({ "left": preValue });
							$(".ui-slider-horizontal .ui-slider-range-min").show();
						} else {
							vodchannel.send("submitMute", { "mute": true });
							$(".volume_a").addClass("col_close_stop");
							preValue = $(".ui-slider-handle").css("left");
							$(".ui-slider-handle").css({ "left": "0px" });
							$(".ui-slider-horizontal .ui-slider-range-min").hide();
						}
					}
					$(".volume_a").on("click", voiceOper);
					vodchannel.bind("onMute", function (evt) {
						if (evt.data) {
							if (evt.data.mute == "true" || evt.data.mute == true) {
								vodchannel.send("submitMute", { "mute": true });
								$(".volume_a").addClass("col_close_stop");
								preValue = $(".ui-slider-handle").css("left");
								$(".ui-slider-handle").css({ "left": "0px" });
								$(".ui-slider-horizontal .ui-slider-range-min").hide();
							} else {
								$(".volume_a").removeClass("col_close_stop");
							}
						}
					});

					//设置网路选中
					$('.net_list').on('click', 'a', function () {
						$(this).addClass('on').siblings().removeClass();
					});



				});
			}

			function playVOD() {

				//初始化点播，点播开始
				channel.bind("loadStart", function (event) {
					console.log(event);
					$('.play_area').addClass('pause');
				});
				//监听暂停
				channel.bind("onPause", function (event) {
					console.log(event);
					$('.play_area').removeClass('pause');
				});

				//跳转结束
				channel.bind("play", function (event) {
					console.log(event);
					$('.play_area').addClass('pause');
				});
				channel.bind("onPlay", function (event) {
					console.log(event);
					$('.play_area').addClass('pause');
				});
				channel.bind("onStop", function (event) {
					console.log("onStop", event);
					$('.play_area').removeClass('pause');
				});

				//SDK状态通知 
				channel.bind("onStatus", function (event) {
					console.log(event);
					if (event.data.type != "2") {
						alert("SDK状态通知 " + event.data.type + " " + event.data.explain);
					}
				});

				//获取播放总时长
				var duration = 0;
				var testInterval;

				channel.bind("onFileDuration", function (event) {
					console.log(event);
					duration = event.data.duration;
					$('#duration').html(Util.timeDuration(duration / 1000));

					//播放进度条
					$(".progress_area").slider({
						value: 0,
						range: "min",
						change: function () {
							// var disL = $(".progress_area a").css("left");
							// $(".progress_area span").width(disL);

						},
						slide: function (event, ui) {

							var played = duration * Number(ui.value) / 100;
							$('#play_now').html(Util.timeDuration(played / 1000));
						},
						start: function () {
							if (testInterval) {
								clearInterval(testInterval);
							}
						},
						stop: function (event, ui) {
							var v = ui.value;
							var s = duration * Number(v) / 100;
							var currentTime = parseInt(Number(s));

							console.log("seek-position", currentTime);
							//seek操作
							channel.send("seek", {
								"timestamp": currentTime
							});
						}
					});
				});

				//SDK加载完毕，所有API生效
				channel.bind("onDataReady", function (event) {
					console.log(event);
					setTimeout(function () { sdkgo(); }, 0);
				});
				//异步执行sdk交互
				function sdkgo() {
					//跳转结束
					channel.bind("onSeekCompleted", function (event) {
						console.log(event);
						getplaynow();
						$('.play_area').addClass('pause');
					});

					//定时获取当前播放时间点
					function getplaynow() {
						testInterval = setInterval(function () {
							channel.send("playheadTime", {});
						}, 190);
					}
					getplaynow();
					//监听播放时间
					var ttimer = null;
					channel.bind("onPlayheadTime", function (event) {
						ttimer += 190;
						if (ttimer > 190 * 6) {
							ttimer = 0;
							console.log("onPlayheadTime:", event.data.playheadTime);
							$('#play_now').html(Util.timeDuration(event.data.playheadTime / 1000));

							$(".progress_area").slider("value", Util.calcPercent(event.data.playheadTime, duration));
						}


						//changeChapter(event.data.playheadTime);
					});



					//播放按钮 播放和暂停
					$('.play_area').on('click', function () {
						if ($(this).hasClass('pause')) {
							channel.send("pause", {
							});
						} else {
							channel.send("play", {
							});
						}
					});

					//获取问答
					channel.send("submitQAList", {});

					//获取聊天
					channel.send("submitChatHistory", {});

					//提交音量信息
					$(".volume_slide").bind("slidechange", function (event, ui) {
						console.log(ui.value);

						channel.send("submitVolume", {
							"value": ui.value / 100
						});
					});

				}
				//API错误通知
				channel.bind("onAPIError", function (event) {
					console.log(event);
				});
				//相关操作处理
				$(function () {
					//聊天问答按钮点击切换
					$('#chat_qa_a a').on('click', function () {
						if (!$(this).hasClass('on')) {
							var id = $(this).attr('data-id');
							var old_id = $('#chat_qa_a a.on').attr('data-id');
							$('#chat_qa_a a.on').removeClass('on');
							$('.' + old_id + '_list').hide();
							$('.' + id + '_list').show();
							$(this).addClass('on');
						}
					});

					//设置音量默认75
					$(".volume_slide").slider({
						value: 75,
						range: "min",
						change: function () {
							$(".volume_slide span").width($(".volume_slide a").css("left"));
						}
					});
					var preValue = 75;
					function voiceOper() {
						if ($(".volume_a").hasClass("col_close_stop")) {
							channel.send("submitMute", { "mute": false });
							$(".volume_a").removeClass("col_close_stop");
							$(".volume_slide .ui-slider-handle").css({ "left": 75 });
							$(".volume_slide .ui-slider-range-min").show();
						} else {
							channel.send("submitMute", { "mute": true });
							$(".volume_a").addClass("col_close_stop");
							preValue = $(".volume_slide .ui-slider-handle").css("left");
							$(".volume_slide .ui-slider-handle").css({ "left": "0px" });
							$(".volume_slide .ui-slider-range-min").hide();
						}
					}
					$(".volume_a").on("click", voiceOper);
					channel.bind("onMute", function (evt) {
						if (evt.data) {
							if (evt.data.mute == "true" || evt.data.mute == true) {
								$(".volume_a").addClass("col_close_stop");
								preValue = $(".volume_slide .ui-slider-handle").css("left");
								$(".volume_slide .ui-slider-handle").css({ "left": "0px" });
								$(".volume_slide .ui-slider-range-min").hide();
							} else {
								$(".volume_a").removeClass("col_close_stop");
							}
						}
					});

					//单选多选设置答题
					$('body').on('click', '.survey_select div a', function () {
						var that = this;
						var is_radio = $(that).parents('.survey_select').hasClass('single');
						if ($(that).hasClass('on') && !is_radio) {
							$(that).removeClass('on');
						} else {
							if (is_radio) {
								$(that).parent('div').find('a').removeClass('on');
							}
							$(that).addClass('on');
						}
					});
				});
			}

			var livedom = [
				'<div class="livevideo_area">',
				'<gs:video-live id="videoComponent" site="emoney.gensee.com" ctx="webcast" fullscreen="true" ownerid="' + ownerid + '" uid="' + uid + '" uname="' + uname + '" authcode="' + authcode + '" bar="false" />',
				'</div>',
				'<div class="livetool_area">',
				'	<div class="volume_area">',
				'		<a href="javascript:void(0);" class="volume_a"></a>',
				'		<div class="volume_slide"></div>',
				'	</div>',
				'	<div class="t_iner"><a href="javascript:void(0);" id="wifi_a" class="wifi_a"></a> </div>',
				'	<div class="t_center">',
				'		<em id="play_status">未开始</em> <span>在线<b id="onlineCounts">10</b>人</span>',
				'	</div>',
				'</div>',
				'<div class="net_list"></div>'].join('');

			var voddom = ['<div class="video_area">',
				'<gs:video-vod id="videoComponent" site="emoney.gensee.com" ctx="webcast" fullscreen="true"  ownerid="' + ownerid + '" uid="' + uid + '" uname="' + uname + '" authcode="' + authcode + '" bar="false" gsVer="" py="1" />',
				'</div>',
				'<div class="vtool-bar">',
				'	<div class="tool_area">',
				'		<div class="progress_area"> </div>',
				'		',
				'		<a class="play_area"></a>',
				'		<div class="play_time">',
				'			<span id="play_now">00:00</span>',
				'			<span>/</span>',
				'			<em id="duration">00:00</em>',
				'		</div>',
				'		<div class="volume_area">',
				'			<a href="#" class="volume_a"></a>',
				'			<div class="volume_slide"></div>',
				'		</div>',
				'	</div>',
				'</div>'].join('');

			if (midtype == "vod") {
				$('#mainshow').html(voddom);
				playLive();
			} else if (midtype == "live") {
				$('#mainshow').html(livedom);
				playVOD();
				// 测试入口
				/** 点赞
				 * uid,          用户id 
				 * vchanelid,    视频id
				 * appid         appid
				 */
				// window.videoClickLikes().switchVchanel(uid, ownerid, appid);

				/** 投票：
				 *  PId:'',          产品id
				 *  appID:'',        appid
				 *  uid:'',          用户id
				 *  voteId:'',       投票id
				 *  $container：'',  展示容器 
				 *  size: '',        尺寸 默认 small
				 *  from: ''         来源 '实战会场'
				 */
				window.loadVote(pid, appid, uid, voteid, $('.live-inner'), "small", "实战会场");

			} else {
				layer.msg('播放类型有误')
			}



		})($);
	</script>
</body>

</html>