//example
// function bingoevent(payurl,qrurl){
//     $("#btn_xufei").attr("href",payurl);//按钮跳转
//     $("#img").attr("src",qrurl);//二维码图片
// }
// payTransfer.getPayUrl(uid,pid,paysource,bingoevent);

var payTransfer = {
    getPayUrl: function (uid, pid, paysource, bingoevent) {
        var default_payurl = "http://pay.emoney.cn/newpay/smallpay/index?source=" + paysource;
        var default_qrcodeurl = "http://www.emoney.cn/dianjin/ym/rj/zytyb/images/ewm.png";
        var payurlData = [['888020400', '1098', 'http://pay.emoney.cn/newpayv2/pay/Order?actid=20&channelcode=' + paysource, 'http://www.emoney.cn/dianjin/bb/20221014.png']];//[['pid','groupid','payurl','qrcodeurl']]

        for (var i = 0; i < payurlData.length; i++) {
            var strpid = payurlData[i][0];
            var groupid = payurlData[i][1];
            var payurl = payurlData[i][2];
            var qrcodeurl = payurlData[i][3];

            if (strpid == pid && !!groupid) {
                $.ajax({
                    url: "https://apiuserradar.emoney.cn/api/CheckUserjsonp",
                    timeout: 5000,
                    type: 'get',
                    dataType: 'jsonp',
                    cache: false,
                    data: {uid: uid, groupid: groupid},
                    success: function (data) {
                        if (!!data.UserGroupList) {
                            var groupList = data.UserGroupList;
                            $.each(groupList, function (idx, obj) {
                                if (obj.CheckResult) {
                                    if (typeof (bingoevent) == 'function') {
                                        bingoevent(payurl, qrcodeurl, 1);
                                        return;
                                    }
                                }else{
                                    bingoevent(default_payurl, default_qrcodeurl, 0);
                                }
                            });
                        }
                    }
                });
            }else{
                if (typeof (bingoevent) == 'function') {
                    bingoevent(default_payurl, default_qrcodeurl, 0);
                }
            }
        }
    }
}