(function ($) {
    window.utils = {
        /**
         * 字符串截取
         */
        subStr: function (str, length) {
            if (str.length > length) {
                return str.substr(0, parseInt(length)) + "...";
            }
            return str;
        },
        /*
         * 去掉前后空格
         */
        strTrim: function (s) {
            return s.replace(/(^\s+)|(\s+$)/g, "");
        },
        //获取当天日期xx-xx-xx
        getNowDate: function () {
            var date = new Date();
            var seperator1 = "-";
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var strDate = date.getDate();
            if (month >= 1 && month <= 9) {
                month = "0" + month;
            }
            if (strDate >= 0 && strDate <= 9) {
                strDate = "0" + strDate;
            }
            var currentdate =
                year + seperator1 + month + seperator1 + strDate;
            return currentdate;
        },
        //时间戳toDate
        timestampToDate: function (timestamp) {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = date.getMonth() + 1; // getMonth() 返回的月份从 0 开始，所以需要加 1
            const day = date.getDate();

            return `${year}年${month}月${day}日`;
        },
        //跳转 IM
        goIM: function (fromname) {
            var b = new Base64();
            var ret = this.PC_JH('EM_FUNC_START_IM', '0,,' + b.encode(fromname));
            if (ret == "1") {
                return false;
            }
        },
        GetExternal: function () {
            return window.external.EmObj;
        },

        //调用客户端接口
        PC_JH: function (type, c) {
            try {
                var obj = this.GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {
            }
        },
        getQueryString: function (name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        },
        //推送cmp
        pushdatatocmp: function (uname, adcode) {
            var data = {
                "appid": '10088',
                "logtype": 'click',
                "mid": '',
                "pid": this.getQueryString("pid"),
                "sid": this.getQueryString("sid"),
                "tid": this.getQueryString("tid"),
                "uid": this.getQueryString("uid"),
                "uname": uname,
                "adcode": adcode,
                "targeturl": "",
                "pageurl": window.top.location.href
            }
            var saasUrl = "https://ds.emoney.cn/saas/queuepush";
            var saasSrc = saasUrl + "?v=" + Math.random()
                + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
                + "&message=" + encodeURIComponent(JSON.stringify(data));

            var elm = document.createElement("img");
            elm.src = saasSrc;
            elm.style.display = "none";
            document.body.appendChild(elm);
        },
        //跳转看看
        goKanKan: function (url) {
            function GetExternal() {
                return window.external.EmObj;
            }

            //调用客户端接口
            function PC_JH(type, c) {
                try {
                    var obj = GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {
                }
            }

            var ret = PC_JH('EM_FUNC_OPEN_LIVE_VIDEO', '14,' + url);
            if (ret == "1") {
                return false;
            }
        },
        request:function (url, dataType) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: 'get',
                    url: url,
                    dataType: dataType ? dataType : 'jsonp',
                    timeout: 5000,
                    success: function (data) {
                        console.log(data)
                        resolve(data)
                    },
                    error: function (err) {
                        reject(err)
                    }
                })
            })
        }
    }
})(jQuery);