$(function () {
    //rsa加密
    var encrypt = new JSEncrypt();
    $.ajax({
        type: "get",
        url: www + "login/getPublicKey",//访问路径
        contentType: 'application/json;charset=utf-8',//返回json结果
        success: function (data) {
            encrypt.setPublicKey(data);
        }
    });
    $("#btnclean").click(function (){
        $("#loginname").val("");
        $("#loginmobile").val("");
        $("#loginsmscode").val("");
    });
    //登录
    $("#btnlogin").click(function () {
        var realname = $("#loginname").val();
        var mobile = $("#loginmobile").val();
        var smscode = $("#loginsmscode").val();
        var ischeckcode = $("#ischeckcode").val();
        var actcode=$("#hid_actcode").val();

        if(ischeckcode=="0"){
            if (mobile == "" || mobile == undefined) {
                layer.msg("请输入EM号或手机号");
                return false;
            }
            if(mobile.indexOf("em")>-1 || mobile.indexOf("sy")>-1){

            }else{
                if(!validateTel(mobile)){
                    return false;
                }
            }
        }else{
            if (realname == "" || realname == undefined) {
                layer.msg("请填写您的姓名!");
                $("#loginname").focus();
                return false;
            }

            if (mobile == "" || mobile == undefined || smscode == "" || smscode == undefined) {
                layer.msg("请输入手机号或者验证码!");
                return false;
            }
            if(!validateTel(mobile)){
                return false;
            }
        }

        name = encrypt.encrypt(mobile);
        pass = encrypt.encrypt(smscode);
        $.post(www + "login/userSMSLogin", { uname: name, pass: pass, realname: escape(realname),actcode: actcode,ischeckcode:ischeckcode}, function (data) {
            if (data.code == "200") {
                location.reload();
            }
            else {
                layer.msg("请输入正确的账号或验证码");
            }
        });
    });
    //获取验证码
    $("#sendcode").click(function () {
        var mobile = $("#loginmobile").val();

        if (mobile == "" || mobile == undefined) {
            layer.msg("请输入手机号!");
            return false;
        }
        if(!validateTel(mobile)){
            return false;
        }

        name = encrypt.encrypt(mobile);
        $.post(www + "login/sendMobileCode", { mobile: name }, function (data) {
            if (data.code == "200") {
                CalcTime();
            }
            else {
                layer.msg("请输入正确的账号");
            }
        });
    });
});


function CalcTime() {
    var num = 90;
    $("#sendcode").data("num", num).attr("disabled", true).css("pointer-events", "none").html("验证码(" + num + ")");
    var v = setInterval(function () {
        var thisv = Number($("#sendcode").data("num"));

        thisv = thisv - 1;
        if (thisv < 0) {
            $("#sendcode").attr("disabled", false).css("pointer-events", "").html("获取验证码");
            $("#sendcode").data("num", 0);

            clearInterval(v);
            return false;
        }
        $("#sendcode").html("验证码(" + thisv + ")");
        $("#sendcode").data("num", thisv);

    }, 1000);
}
// 手机验证
function validateTel(invitname) {
    var telReg = /^1[3-9]\d{9}$/;
    if (!$.trim(invitname) || !telReg.test(invitname)) {
        layer.msg("请输入正确的手机号！");
        return false;
    }
    return true;
}

function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;
}
function pushdatatocmp(uname,adcode) {
    var data = {
        "appid": '10088',
        "logtype": 'click',
        "mid": '',
        "pid": getQueryString("pid"),
        "sid": getQueryString("sid"),
        "tid": getQueryString("tid"),
        "uid": getQueryString("uid"),
        "uname": uname,
        "adcode": adcode,
        "targeturl": "",
        "pageurl": window.top.location.href
    }
    var saasUrl = "http://ds.emoney.cn/saas/queuepush";
    var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

    var elm = document.createElement("img");
    elm.src = saasSrc;
    elm.style.display = "none";
    document.body.appendChild(elm);
}

function setCookie(name, value) {
    var expdate = new Date();
    expdate.setTime(expdate.getTime() + 30 * 24 * 60 * 60 * 1000);
    document.cookie = name + "=" + value + ";expires=" + expdate.toGMTString() + ";path=/";
}


function getCookie(c_name) {
    if (document.cookie.length > 0) {
        c_start = document.cookie.indexOf(c_name + "=")
        if (c_start != -1) {
            c_start = c_start + c_name.length + 1
            c_end = document.cookie.indexOf(";", c_start)
            if (c_end == -1) c_end = document.cookie.length
            return unescape(document.cookie.substring(c_start, c_end))
        }
    }
    return ""
}