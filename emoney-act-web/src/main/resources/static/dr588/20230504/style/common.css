body, html {
	margin: 0;
	padding: 0;
	overflow: hidden;
	height: 100%; background-color: #000; font-family: "微软雅黑";font-size: 12px;
}
img {
	border: 0
}
ul, li {
	margin: 0;
	padding: 0;
	list-style: none;
}
A:link {TEXT-DECORATION: none}
A:visited {TEXT-DECORATION: none}
A:hover {COLOR:#fff; TEXT-DECORATION: none}
.lh23{line-height: 19px!important;}
.lh13{line-height: 14px!important;}
.main{width: 970px; height: 600px;background:url("../images/bg.png"); margin: 0 auto; position: relative;}
.left{
	position: absolute;
	left: 54px;
	top: 191px;
}
.left li{width: 282px; height: 89px; background:url("../images/bg3.png") no-repeat; color: #8C6239; text-align: center; margin-bottom: 7px;}
.left .on {
		width: 282px;
		height: 154px; background:url("../images/bg2.png");
		margin-bottom: 7px;
		box-shadow: none; color: #fff;
	}
.bt{padding-top: 8px;}
.f14{font-size: 14px;}
.f16{font-size: 14px; font-weight: bold; padding:0px 0 3px;}
.f20{font-size: 20px; font-weight: bold;}
.btn3{width:110px; height: 22px; background-color: #EE493A; display: block; line-height: 20px; text-align: center;margin: 0 auto; color: #fff;font-size: 14px; border-radius: 12px;}
.left .on .bt{text-align: center; width: 230px; margin-left: 17px; border-bottom: 1px solid #fff;}
.nr{width: 230px; margin-left: 17px;padding: 5px; line-height: 16px; font-size: 12px; text-align: left; display: none;}
.btn4{width: 150px; height: 22px; margin: 0 auto; text-align: center; line-height: 20px; background-color: #B3B3B3; border-radius: 11px; color: #fff;}
.left .on .btn4,.left .on .btn3{display: none;}
.left .on .nr{display: block;}

.right{
	position: absolute;
	left: 346px;
	top: 204px;
	width: 630px;
	text-align: center;
}
.right li{background:url("../images/bg1.png"); width:164px; height: 154px; float: left; margin:0 25px 11px 0;}
.txt1{font-size: 14px; color: #8C6239; line-height: 28px;width: 85px;
text-align: center;
padding-left: 80px; font-weight: bold;}
.txt2{font-size: 20px; font-weight: bold;color: #8C6239; line-height: 22px; padding-top: 12px;width:100%;}
.txt3{font-size: 14px; color: #8C6239; line-height: 22px; padding-top: 5px; width: 100%; height: 49px;}
.txt4{color: #EE6786; padding-top: 10px;}
.btn2{width:107px; height: 30px;background:url("../images/btn2.png"); display: block; line-height:29px; text-align: center;margin: 0 auto; color: #FFF8F2;font-size: 16px; padding-right: 15px;}
.btn2b{width:107px; height:30px;background:url("../images/btn2.png"); background-position: 0 -30px; display: block; line-height: 29px; text-align: center;margin: 0 auto; color: #fff;font-size: 16px;padding-right: 15px;}

.tc1{width:248px; height: 300px;background:url("../images/tc1.png"); display: block;}
.tc2{width:234px; height: 266px;background:url("../images/tc2.png"); display: block;}

.btn1{
	width:332px;
	height:45px;
	background: url("../images/btn1.png");
	position: absolute;
	left: 331px;
top: 547px;
}

.dh:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
