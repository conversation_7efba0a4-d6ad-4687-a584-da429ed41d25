body, html {
	margin: 0;
	padding: 0;
	overflow: hidden;
	height: 100%; background-color: #000; font-family: "微软雅黑";font-size: 12px;
}
img {
	border: 0
}
ul, li {
	margin: 0;
	padding: 0;
	list-style: none;
}
A:link {TEXT-DECORATION: none}
A:visited {TEXT-DECORATION: none}
A:hover {COLOR:#fff; TEXT-DECORATION: none}
.lh23{line-height: 19px!important;}
.main{width: 970px; height: 600px;background:url("../images/bg.png"); margin: 0 auto; position: relative;}
.left{
	position: absolute;
	left: 25px;
	top: 191px;
}
.left li{width: 267px; height: 89px; background-color: #7FA6DD; border-radius:10px; color: #fff; text-align: center; margin-bottom: 7px; border: 1px solid #fff;}
.left .on {
		width: 267px;
		height: 166px; background-color: #fff;
		margin-bottom: 7px;
		box-shadow: none;
		border-radius: 10px;border: 1px solid #fff;
	}
.bt{padding-top: 8px;}
.f14{font-size: 14px;}
.f16{font-size: 14px; font-weight: bold; padding:0px 0 3px;}
.f20{font-size: 20px; font-weight: bold;}
.btn3{width:110px; height: 22px; background-color: #EE6786; display: block; line-height: 20px; text-align: center;margin: 0 auto; color: #fff;font-size: 14px; border-radius: 12px;}
.bg2{width:269px; height: 202px;background:url("../images/bg2.png"); background-size: 100% 100%; margin-bottom:7px; border-radius: 11px;}
.left .on .bt{border-bottom: 1px solid #4E586E; color: #B23462; text-align: center; width: 230px; margin-left: 17px;}
.nr{color: #4E586E; width: 230px; margin-left: 17px;padding: 5px; line-height: 16px; font-size: 12px; text-align: left; display: none;}
.btn4{width: 150px; height: 22px; margin: 0 auto; text-align: center; line-height: 20px; background-color: #5882CD; border-radius: 11px;}
.left .on .btn4,.left .on .btn3{display: none;}
.left .on .nr{display: block;}

.right{
	position: absolute;
	left: 329px;
	top: 187px;
	width: 630px;
	text-align: center;
}
.right li{background:url("../images/bg1.png"); width:178px; height: 168px; float: left; margin:0 25px 11px 0;}
.txt1{font-size: 14px; color: #fff; text-align: center; line-height: 28px;width: 121px;
margin: 5px 0 0 27px;}
.txt2{font-size: 22px; font-weight: bold;color: #B23462; line-height: 22px; padding-top: 12px;width: 171px;}
.txt3{font-size: 14px; color: #4E586E; line-height: 22px; padding-top: 5px; width: 171px; height: 53px;}
.txt4{color: #EE6786; padding-top: 10px;}
.btn2{width:112px; height: 32px;background:url("../images/btn2.png"); display: block; line-height:29px; text-align: center;margin: 0 auto; color: #FFF8F2;font-size: 16px; padding-right: 15px;}
.btn2b{width:112px; height: 32px;background:url("../images/btn2.png"); background-position: 0 -32px; display: block; line-height: 29px; text-align: center;margin: 0 auto; color: #fff;font-size: 16px;padding-right: 15px;}

.tc1{width:248px; height: 300px;background:url("../images/tc1.png"); display: block;}
.tc2{width:234px; height: 266px;background:url("../images/tc2.png"); display: block;}

.btn1{
	width: 414px;
	height: 57px;
	background: url("../images/btn1.png");
	position: absolute;
	left: 281px;
	top: 537px;
}
.btn5{
	width: 118px;
	height: 127px;
	background: url("../images/btn5.png");
	position: absolute;
	left: 824px;
	top: 50px;
}
.dh:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
