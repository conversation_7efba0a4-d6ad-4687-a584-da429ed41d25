body, html {
	overflow-x:hidden;
	margin: 0;
	padding: 0;
	height: 100%; background-color: #000; font-family: "微软雅黑";font-size: 12px;
}
img {
	border: 0
}
ul, li {
	margin: 0;
	padding: 0;
	list-style: none;
}
A:link {TEXT-DECORATION: none}
A:visited {TEXT-DECORATION: none}
A:hover {COLOR:#fff; TEXT-DECORATION: none}
.lh23{line-height: 19px!important;}
.lh13{line-height: 14px!important;}
.main{width: 1000px; height: 1000px;background:url("../images/bg.png"); margin: 0 auto; position: relative;}
.left{
	position: absolute;
	left: 34px;
	top: 221px;
}
.left li{width:352px; height: 77px; background:url("../images/bg3.png") no-repeat; color: #8C6239; text-align: center; margin-bottom: 7px;}
.left .on {
		width: 369px;
		height: 167px; background:url("../images/bg2.png");
		margin-bottom: 7px;
		box-shadow: none; color: #fff;
	}
.bt{padding-top: 5px; height: 26px; font-size: 18px; font-weight: bold; color: #3e3a39;}
.bt .l1{float: left; width: 149px; text-align: right;}
.bt .l2{float: left; width: 186px;padding-left: 13px; text-align: left;}
.f12{font-size: 12px; font-weight: normal;}
.f14{font-size: 14px;}
.f24{font-size: 24px;}
.f20{font-size: 20px; font-weight: bold;}
.btn3{width: 269px; height: 30px; margin: 0 auto; text-align: center; line-height:30px; background:url("../images/btn4h.png") no-repeat; color: #231815; font-size: 18px; margin-top: 5px; display: block;}
.left .on .bt{padding-top: 42px; font-size: 18px; font-weight: bold; color: #fff; height:38px;}
.nr{width: 322px;
margin-left: 21px;
line-height: 20px;font-size: 14px; text-align: left; display: none;}
.btn4{width: 269px; height: 30px; margin: 0 auto; text-align: center; line-height:30px; background:url("../images/btn4.png") no-repeat; color: #595757; font-size: 18px; margin-top: 5px;}
.left .on .btn4,.left .on .btn3{display: none;}
.left .on .nr{display: block;}

.right{
	position: absolute;
	left: 418px;
	top: 283px;
	width: 420px;
	text-align: center;
}
.right li{width:180px; height:200px; float: left; margin:0 30px 20px 0; position: relative;}
.txt1{font-size: 14px; color: #595757;text-align: center; padding-top: 10px;}
.txt2{font-size: 20px; color: #a50000; padding-top: 12px; font-weight: bold;}
.txt3{font-size: 16px; color: #595757; line-height: 20px;
padding-top: 2px;}
.txt4{font-size: 16px; color: #595757; line-height: 22px; padding-top: 29px;}
.btn2{width:129px; height: 41px;background:url("../images/btn2.png"); line-height:35px; text-align: center;color: #fff;font-size: 16px; padding-right: 20px; position: absolute; left: 18px;top: 127px;}
.btn2b{width:129px; height: 41px;background:url("../images/btn2.png"); background-position: 0 -41px;line-height:35px; text-align: center;color: #231815;font-size: 16px; padding-right: 20px; position: absolute; left: 18px;top: 127px;}
.btn5{
	width: 104px;
	height: 43px;
	background: url("../images/btn3.png");
	line-height: 38px;
	text-align: center;
	color: #fff;
	font-size: 16px;
	padding-right: 20px;
	position: absolute;
	left: 407px;
	top: 287px;
}
.btn5b{
	width: 104px;
	height: 43px;
	background: url("../images/btn3.png");background-position: 0 -43px;
	line-height: 38px;
	text-align: center;
	color: #231815;
	font-size: 16px;
	padding-right: 20px;
	position: absolute;
	left: 407px;
	top: 287px;
}
.tc1{width:364px; height: 379px;background:url("../images/tc1.png"); display: block;}
.tc2{width:364px; height: 338px;background:url("../images/tc2.png"); display: block;}
.hdgz{
	position: absolute;
	left: 430px;
	top: 800px;
	color: #231815;
	font-size: 12px;
	width: 517px;
}
.btn1{
	width: 435px;
	height: 48px;
	background: url("../images/btn1.png");
	position: absolute;
	left: 450px;
	top: 895px;
}

.dh:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
