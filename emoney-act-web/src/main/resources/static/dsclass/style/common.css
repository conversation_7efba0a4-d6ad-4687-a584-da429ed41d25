@charset "utf-8";

img{ border:0px;}
body {
	font-size:12px; overflow-x:hidden;
	margin:0;
	padding:0;font-family:"微软雅黑";
}

.main{width:100px; margin:0 auto; position:relative;}
ul,li {margin:0;padding:0;list-style:none;}
A:link {COLOR: #333; TEXT-DECORATION: none;}
A:visited {COLOR: #333; TEXT-DECORATION: none}
A:hover {COLOR:#000; TEXT-DECORATION: none}
A.b:link {COLOR: #FFD334; TEXT-DECORATION: underline}
A.b:visited {COLOR: #FFD334; TEXT-DECORATION: underline}
A.b:hover {COLOR: #ffff00; TEXT-DECORATION: none}
input{background:transparent;border:0px;margin:0px; color:#939598;list-style:none; font-family:"微软雅黑"; font-size:14px;}
.white {color:#ffffff;}
.red2{color:#B6282C; font-size: 18px;}
.green {color:green;}
.black{ color:#000;}
.blue{color:#549FD6;}
.yellow{color:#eec199;}
.blue3{color:#255E98;}
.gray{color:#6A3906;}
.org{color:#ff5a00;}
.bg_h{background-color:#f0f0f0;}
.f_13{font-size:13px;}
.f_14{font-size:14px;}
.clr2{clear:both; height:1px; overflow:hidden;}
.m_t36{margin-top:36px;}
.m_t20{margin-top:20px;}
.m_t10{margin-top:10px;}
.t_c{text-align:center;}
.f_12{font-size:12px; font-family:"宋体";}
.f_16{font-size:16px;font-weight:bold;}
.f_17{font-size:17px;}
.f_26{font-size:26px; font-family:"微软雅黑"; line-height:50px;}
.f_30{font-size:28px; color:#603813; line-height:50px; font-weight:bold;}
.f_22{font-size:25px;}
.h340{height:340px;}
table,th,td {
  border : 1px solid #c6a278;
  border-collapse: collapse;
}

.f-r{float:right;}
.f-l{float:left;}
.f1{background-color:#f3f3fd; color:#333; padding:3px 5px;}

.tab{
	width: 800px;
	height: 234px;
	font-size: 14px;
	color: #3D3A39;
	line-height: 30px;
	position: absolute;
	left: -355px;
	top: 132px;
	text-align: center;
}
.tab .bt{background-color: #DED3B7;}

#dbg1{background:url("../images/index_01.jpg") center top no-repeat;height:222px;}
#dbg2{background:url(../images/index_02.jpg) center top no-repeat;height:180px;}
#dbg3{background:url("../images/index_03.jpg") center top no-repeat;height:306px;}
#dbg4{background:url("../images/index_04.jpg") center top no-repeat;height:424px;}
#dbg5{background:url("../images/index_05.jpg") center top no-repeat;height:513px;}
#dbg6{background:url("../images/index_06.jpg") center top no-repeat;padding: 35px 0;}
#dbg11{background:url("../images/pf.png") center top no-repeat;height:138px; position: fixed; width: 100%; left: 0; bottom: 0;}

.btn{
	background: url(../images/btn.png);
	width: 327px;
	height: 112px;
	cursor:pointer; z-index: 99
}
.btn div{
	position: absolute;
	left: 110px;
	top: 56px;
	width: 168px;
	font-size: 16px;
	color: #231815; text-align: center;
}
.an1{
	position: absolute;
	cursor: pointer;
	left: -163px;
	top: 150px;
	background: url("../images/btn1.png");
	width: 416px;
	height: 65px;
}
.an1 div{
	position: absolute;
	left: 135px;
	top: 74px;
	width: 327px;
	font-size: 18px;
	color: #605553;
}
.an2{
	position: absolute;
	left: 214px;
	top: 17px;
}

.pic1{
	position: absolute;
	left: -315px;
	top: 222px;
}
.pic2{
	position: absolute;
	left: -285px;
	top: 240px;
}
.pic3{
	position: absolute;
	left: -285px;
	top: 845px;
}
.pic4{
	position: absolute;
	left: -320px;
	top: 260px;
}
.pic5{
	position: absolute;
	left: -320px;
	top: 768px;
}
.pic6{
	position: absolute;
	left: -320px;
	top: 253px;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.dh2{
  -webkit-animation: dh2 1.5s linear infinite alternate;
  animation-name: dh2 1.5s linear infinite alternate;
}
@-webkit-keyframes dh2 {
  0%, 10%,20%, 100% {
    opacity: .6;
  }

  5%, 15% {
    opacity: 0;
  }
}

@keyframes dh2 {
  0%, 10%,20%, 100% {
    opacity: .6;
  }

  5%, 15% {
    opacity: 0;
  }
}
.bg2{background:url("../images/bg2.png"); width: 992px; height: 794px; margin: 0 auto 50px;
}
.kc{background:url("../images/bg1.png"); width: 992px; height: 889px; margin: 0 auto;
}
.kc ul{margin: 0 auto; width: 910px;padding-top: 196px;}
.kc li{height:55px; border-bottom: 1px dotted #EABF9B; line-height: 55px;font-size: 20px;
	color: #EABF9B;}
.kc .txt1{
	width: 577px; float: left;
}
.kc .txt2{width: 210px; float: left;}
.btn2,.btn2h{
	background: url("../images/btn2.png"); margin-top: 5px;
	width: 122px;
	height: 37px; float: right;
}
.btn2h{background:url("../images/btn2h.png"); }
.h{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
#sp{position:fixed; top: 50%;left:50%; margin:-302px 0 0 -435px; width:870px; height: 600px; border: 4px solid #fff;}
.if{width:870px; height: 600px;}
.close{
	background: url("../images/close.png") center top no-repeat;
	width: 25px;
	height: 25px;
	position: absolute;
	top: -30px;
	right: -30px;
}

.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.al1{margin: 0px auto 50px; position: relative;
	width: 947px;
	height: 618px;
}
.slider_box{ margin: 0px auto; width: 947px; height: 618px; position: relative;overflow:hidden;}
.silder_con{ height: 618px; position: absolute;}
.silder_panel{width: 947px; height: 618px; float: left; position: relative; text-align:center;}
.slider_box2{ margin: 0px auto; width: 947px; height: 618px; position: relative;overflow:hidden;}
.silder_con2{ height: 618px; position: absolute;}
.silder_panel2{width: 947px; height: 618px; float: left; position: relative; text-align:center;}


a.prev{ background: url("../images/left.png") no-repeat; width: 54px; height: 54px; text-indent: -9999px; display: block; position: absolute;left: -55px; top:49%;outline:none}
a.next{ background: url("../images/right.png") no-repeat; width: 54px; height: 54px; text-indent: -9999px; display: block; position: absolute; right: -70px; top: 49%;outline:none}

.footer{text-align:center; font-family:"宋体"; font-size:12px; padding:0px 0 158px;}
