@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#DA2431;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #e42c34; font-weight: bold;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center; height:223px;}
.img_2{background:url(../images/index_02.png) center; height:223px;}
.img_3{background:url(../images/index_03.png) center; height:551px;}
.img_4{background:url(../images/index_04.png) center; height:1152px;}
.img_5{background:url(../images/index_05.png) center; height:533px;}
.img_6{background:url(../images/index_06.png) center; height:1137px;}
.img_7{background:url(../images/index_07.png) center top; height:497px;}

.hdgz{
	position: absolute;
	top: 132px;
	left: -360px;
	color: #685242;
	font-size: 22px;
	width: 828px;
	line-height: 31px;
}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:0px 0 166px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.bott{background:url("../images/index_08.png") center;height:148px;position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}

.ico{
	background: url("../images/ico.png");
	width: 78px;
	height: 39px;
	position: absolute;
	top: -8px;
	left: 182px;
}
.btn{background: url("../images/btn.png"); background-size: 100% 100%; text-align: center; color: #fff;text-shadow:2px 2px 2px #FF4324;}
.an1{
	width: 262px;
	height: 103px;
	line-height: 85px;
	position: absolute;
	top: 297px;
	left: 45px;
	font-size: 36px;
}
.an2{
	width: 262px;
	height: 103px;
	line-height: 67px;
	position: absolute;
	top: 297px;
	left: 45px;
	font-size: 36px;
}
.an3{
	width:370px;
	height:146px;
	line-height: 117px;
position: absolute;
top: 484px;
left: 147px;
	font-size: 46px;
}

.t1{
	position: absolute;
	top: 0px;
	left: -497px;
	background: url("../images/t1.png");
	width: 360px;
	height: 343px;
}
.t2{
	position: absolute;
	top: 0px;
	left: -127px;
	background: url("../images/t2.png");
	width: 360px;
	height: 343px;
}
.t3{
	position: absolute;
	top: 0px;
	left: 243px;
	background: url("../images/t3.png");
	width: 360px;
	height: 343px;
}
.btn2{
	position: absolute;
	top: 176px;
	left: 150px;
	background: url("../images/btn7.png");
	width: 74px;
	height: 78px;
}
.btn8,.btn8h{
	position: absolute;
	top: 925px;
	left: -169px;
	background: url("../images/btn8.png");
	width: 452px;
	height: 132px;
}
.btn8h{background: url("../images/btn8h.png");}
.btn9{
	position: absolute;
	top: 17px;
	left: 250px;
	background: url("../images/btn9.png");
	width: 302px;
	height: 119px;
}

.sp{
	width: 828px;
	height: 473px;
	position: absolute;
	top: 376px;
	left: -360px;
}
.org{color: #e42c34; font-weight: bold;}


.djs{
	background: url(../images/djs.png) center top no-repeat;
	position: absolute;
	left: -141px;
	top: -1px;
	width: 356px;
	height: 153px;
	line-height: 50px;
	color: #fff;
	font-size: 32px;
	text-align: center;
	font-weight: bold; display: none;
}
.djs .t{
	position: absolute;
	left: 226px;
	top: 28px;
	width: 57px;
}
.djs .s{
	position: absolute;
	left: 22px;
	top: 81px;
	width: 96px;letter-spacing:18px;
}
.djs .f{
	position: absolute;
	left: 139px;
	top: 81px;
	width: 61px;letter-spacing:18px;
}
.djs .m{
	position: absolute;
	left: 246px;
top: 81px;
	width: 61px;letter-spacing:18px;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc1{
	position: fixed;
	top: 50%;
	left: 50%; margin: -325px 0 0 -325px;background: url("../images/tc1.png"); width:651px; height:650px;
}
.tc2{
	position: fixed;
	top: 50%;
	left: 50%; margin: -325px 0 0 -325px;background: url("../images/tc2.png"); width:651px; height:650px;
}
.tc3{
	position: fixed;
	top: 50%;
	left: 50%; margin: -325px 0 0 -325px;background: url("../images/tc3.png"); width:651px; height:650px;
}

.server{
    background: url("../images/server.png");
    width: 150px;
    height: 174px;
    position:fixed;
    top: 20%;
    right: 0px;
}

.txt1{
	color: #FFF587;
	font-size: 20px; margin-top: -34px;
}
.txt2{
    position: absolute;
    left: 0px;
    top: 230px; font-size:30px; color: #fff; text-align: center; width: 100%;
}
.txt3{
    position: absolute;
    left: 0px;
    top: 210px; font-size: 24px; color: #fff; text-align: center; width: 100%;
}


.dh2{
  -webkit-animation: dh2 2s linear infinite alternate;
  animation-name: dh2 2s linear infinite alternate;
}
@-webkit-keyframes dh2{
0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
40%{-webkit-transform:translateY(-30px)}
60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes dh2{
0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
40%{-moz-transform:translateY(-30px)}
60%{-moz-transform:translateY(-15px)}
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -70px;
top: -40px;
	width: 62px;
	height: 63px;
}
