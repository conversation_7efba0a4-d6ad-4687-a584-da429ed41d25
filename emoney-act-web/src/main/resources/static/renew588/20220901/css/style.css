@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#871A1A;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #e42c34; font-weight: bold;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center; height:335px;}
.img_2{background:url(../images/index_02.png) center; height:334px;}
.img_3{background:url(../images/index_03.png) center; height:1080px;}
.img_4{background:url(../images/index_04.png) center; height:829px;}
.img_5{background:url(../images/index_05.png) center; height:890px;}

.hdgz{
	position: absolute;
	top: 507px;
	left: -361px;
	color: #fff;
	font-size: 22px;
	width: 828px;
	line-height: 39px;
}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:20px 0 246px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.bott{background:url("../images/pf.png") center;height:156px;position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}
.ico{
	background: url("../images/ico.png");
	width: 490px;
	height: 128px;
	text-align: center;
	line-height: 100px;
	font-size: 35px;
	color: #7f4c19;
	position: absolute;
	top: -90px;
	left: -372px;
	text-shadow: 1px 1px 2px #FFFFFF;
	padding-right: 30px;cursor:pointer
}
.btn1{
	background: url("../images/btn1.png");
	width: 491px;
	height: 149px;
	position: absolute;
	top: 164px;
	left: -190px;
}
.btn2{
	background: url("../images/btn2.png");
	width: 378px;
	height: 119px;
	position: absolute;
	top: 904px;
	left: -148px;
}
.btn3{
	background: url("../images/btn3.png");
	width: 378px;
	height: 119px;
	position: absolute;
	top: 630px;
	left: -148px;
}
.btn4{
	position: absolute;
	top: 28px;
	left: 129px;
	background: url("../images/btn4.png");
	width: 379px;
	height: 110px;
}
.btn6,.btn6h{position: absolute;
	top: 55px;
left: 7px;
	background: url("../images/btn6h.png");
	width: 108px;
	height: 32px; text-align: center; line-height:32px; color: #fde9bd; font-size: 20px;
}
.btn6h{
	background: url("../images/btn6.png"); color: #fff;
}
.btn9,.btn9h{
	background: url("../images/btn9.png"); font-weight: bold;
	width:289px;
	height: 77px; text-align: center; line-height:70px; color: #fff; font-size: 44px;position: absolute;
	top: 301px;
left: 90px;
}
.btn9h{	background: url("../images/btn9h.png"); color: #6f6f6f;}


.sp{
	width: 607px;
	height: 392px;
	position: absolute;
	top: 428px;
	left: -125px;
	border: 2px solid #fff;
}
.org{color: #e42c34; font-weight: bold;}


.djs{
	background: url(../images/djs.png) center top no-repeat;
	position: absolute;
	left: -108px;
	top: -1px;
	width: 258px;
	height: 105px;
	line-height: 50px;
	color: #fff;
	font-size: 24px;
	text-align: center;
	font-weight: bold;
	letter-spacing:8px; display: none;
}
.djs .t{
	position: absolute;
	left: 146px;
	top: 9px;
	width: 57px;
	color: #6a3906;
}
.djs .s{
	position: absolute;
	left: 27px;
	top: 41px;
	width: 57px;
}
.djs .f{
	position: absolute;
	left: 93px;
	top: 41px;
	width: 61px;
}
.djs .m{
	position: absolute;
	left: 162px;
	top: 41px;
	width: 61px;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%; margin: -195px 0 0 -234px;
}

.tc5{
	position: fixed;
	top: 50%;
	left: 50%;margin: -299px 0 0 -243px;
}
.server{
    background: url("../images/server.png");
    width: 118px;
    height: 97px;
    position:absolute;
    top: -100px;
    left: 0px;
}
.pf2{
    background: url("../images/pf2.png");
    width: 118px;
    height: 97px;
    position:fixed;
    top: 20%;
    right: 0px;
}
.txt1{
	position: absolute;
	left: -156px;
	top: 359px;
	width: 450px;
	color: #7f4f21;
	font-size: 18px;
	height: 32px;
	overflow: hidden;
	line-height: 32px;
}
.txt2{
    position: absolute;
    left: -359px;
    top: 224px;
    width: 870px;
}
.txt2 a{ float: left; width: 409px; height: 156px;}
.txt2 a:hover{background-position: 0 -156px;}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}
.dh2{
	 -webkit-animation: dh2 2s linear infinite alternate;
	 animation-name: dh2 2s linear infinite alternate;
 }
@-webkit-keyframes dh2{
	0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
	40%{-webkit-transform:translateY(-30px)}
	60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes dh2{
	0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
	40%{-moz-transform:translateY(-30px)}
	60%{-moz-transform:translateY(-15px)}
}
