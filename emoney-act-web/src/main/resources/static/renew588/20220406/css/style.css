@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#871A1A;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #AD1E14;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center no-repeat; height:267px;}
.img_2{background:url(../images/index_02.png) center no-repeat; height:1289px;}
.img_3{background:url(../images/index_03.png) center no-repeat; height:951px;}
.img_4{background:url(../images/index_04.png) center no-repeat; height:945px;}
.img_5{background:url(../images/index_05.png) center no-repeat; height:933px;}
.img_6{background:url(../images/index_06.png) center no-repeat; height:948px;}
.img_7{background:url(../images/index_07.png) center no-repeat; height:958px;}
.img_8{background:url(../images/index_08.png) center no-repeat; height:943px;}
.img_9{background:url(../images/index_09.png) center no-repeat; height:607px;}
.img_10{background:url(../images/index_10.png) center no-repeat; height:625px; overflow: hidden; margin-bottom: 50px;}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin: 450px 0 0;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.bott{height:116px;position:fixed; bottom:0; width:100%; background-color: #FFF0DE;}
.main{width:100px; margin:0 auto; position:relative;}
.pf1,.pf2,.pf3{
	background: url("../images/pf1.png");
	width: 378px;
	height: 96px;
	position: absolute;
	top: 13px;
	left:-392px;
}
.pf2{
	background: url("../images/pf2.png");
}
.pf3{
	background: url("../images/pf3.png");
}

.bg1{
	position: absolute;
	top: 238px;
	left: -168px;
}
.ico{
	position: absolute;
	top: -2px;
	right: 0px;
	background: url("../images/ico.png");
	width: 94px;
	height: 36px;
	text-align: center;
	color: #c30d23;
	font-size: 15px;
	line-height: 30px;
}
.ico2{
	position: absolute;
top: -22px;
left: -48px;
background: url("../images/ico2.png");
width: 185px;
height: 53px;
text-align: center;
color: #fff;
font-size: 20px;
line-height: 35px;
}

.btn2{
	background: url("../images/btn2.png");
	width: 676px;
	height: 150px;
	position: absolute;
	top: 711px;
	left: -293px;
	text-align: center;
	line-height: 130px;
	color: #9f1623;
	font-size: 48px;
}

.txt3{
	position: absolute;
	left: -442px;
	top: 78px;
	font-size: 40px;
	width: 623px;
	color: #93542A;
}
.txt4{
	position: absolute;
	left: -210px;
	top: 116px;
	font-size: 50px;
	width: 453px;
	color: #DD130A;
	text-align: center;
}
.f62{font-size: 62px; font-weight: bold; line-height: 62px;}
.f22{font-size: 22px; text-decoration:line-through; color: #603813;}


.al1{
	width: 785px;
	height: 509px;
	position: absolute;
	left: -339px;
	top: 113px;
	border-radius: 10px;
}
.al2{
	width: 861px;
height: 320px;
position: absolute;
left: -381px;
top: 164px; border-radius: 36px;
}
.hdgz{
	width: 868px;
	position: absolute;
	left: -372px;
	top: 99px;
	font-size: 36px;
	color: #FFEED7;
	line-height: 54px;
}

.djs{
	background: url(../images/djs.png) center top no-repeat;
	position: absolute;
	left: -150px;
	top: 99px;
	width: 403px;
	height: 76px;
	line-height: 50px;
	color: #fff;
	font-size: 36px;
	text-align: center;
	font-weight: bold;
	letter-spacing: 12px; display: none;
}
.djs .t{
	position: absolute;
	left: 6px;
top: 30px;
	width: 57px;
}
.djs .s{
	position: absolute;
	left: 109px;
	top: 30px;
	width: 57px;
}
.djs .f{
	position: absolute;
	left: 212px;
	top: 30px;
	width: 61px;
}
.djs .m{
	position: absolute;
	left: 317px;
	top: 30px;
	width: 61px;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%; margin: -299px 0 0 -243px;
}
.btn3{
	background: url("../images/btn3.png");
	width: 357px;
	height: 130px;
	position: absolute;
	top: -14px;
left: 180px;
}
.tc5{
	position: fixed;
	top: 50%;
	left: 50%;margin: -299px 0 0 -243px;
}

.pf4,.pf4h{
	background: url("../images/pf4.png");
	width: 195px;
	height: 181px;
	position:fixed;
top: 20%; right: 0px;
}
.pf4h{
	background: url("../images/pf4h.png");
}
.txt1{
	position: absolute;
	left: -156px;
	top: 359px;
	width: 450px;
	color: #7f4f21;
	font-size: 18px;
	height: 32px;
	overflow: hidden;
	line-height: 32px;
}
.txt2{
	position: absolute;
	left: -343px;
	top: 693px;
	width: 870px;
}
.txt2 a{ float: left; width: 409px; height: 156px;}
.txt2 a:hover{background-position: 0 -156px;}
.b1{background: url("../images/b1.png") center top no-repeat}
.b2{background: url("../images/b2.png") center top no-repeat}
.b3{background: url("../images/b3.png") center top no-repeat}
.b4{background: url("../images/b4.png") center top no-repeat}
.b5{background: url("../images/b5.png") center top no-repeat}
.b6{background: url("../images/b6.png") center top no-repeat}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}
