@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#ae1d2d;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #AD1E14;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center; height:354px;}
.img_2{background:url(../images/index_02.png) center; height:319px;}
.img_3{background:url(../images/index_03.png) center; height:464px;}
.img_4{background:url(../images/index_04.png) center; height:965px;}
.img_5{background:url(../images/index_05.png) center; height:916px;}
.img_6{background:url(../images/index_06.png) center; height:892px;}
.img_7{background:url(../images/index_07.png) center; height:879px;}
.img_8{background:url(../images/index_08.png) center; height:891px;}
.img_9{background:url(../images/index_09.png) center; height:900px;}
.img_10{background:url("../images/index_10.png") center; height:900px;}
.img_11{background:url("../images/index_11.png") center; height:1060px;}
.img_12{background:url("../images/index_12.png") center; height:1208px;}
.img_13{background:url(../images/index_13.png) center; height:608px;}

.img2_1{background:url(../images/index2_01.png) center; height:324px;}
.img2_2{background:url(../images/index2_02.png) center; height:319px;}
.img2_3{background:url(../images/index2_03.png) center; height:748px;}
.img2_4{background:url(../images/index2_04.png) center; height:390px;}
.img2_5{background:url(../images/index2_05.png) center; height:115px;}
.img2_6{background:url(../images/index2_06.png) center; padding: 20px 0; background-size: 1920px 100%;}
.img2_7{background:url(../images/index2_07.png) center; height:638px;}

.img2_6 ul{width: 735px; margin: 0 auto; max-height: 600px; overflow-y: auto;}
.img2_6 li{border-bottom: 2px solid #666; line-height: 58px; color: #666; font-size: 22px; padding: 0 10px;}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin: 20px 0 165px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.bott{background:url("../images/index_14.png") center;height:145px;position:fixed; bottom:0; width:100%;}
.bott2{background:url("../images/index_14b.png") center;height:145px;position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}


.bg1{
    position: absolute;
    top: 60px;
    left: -113px;
    width: 450px;
}
.bg1 li{background: url("../images/bg1.png"); width: 65px; height: 49px; padding-right: 10px; line-height: 45px; text-align: center; color: #fff; font-size: 26px; float: left; margin-right: 75px;}
.bg1 .on{background: url("../images/bg1h.png");}
.a8{
    background: url("../images/a8.png");
    width: 101px;
    height: 101px;
    position: absolute;
    top: 33px;
    left: 301px;
}
.a8h{
    background: url("../images/a8h.png");
    width: 101px;
    height: 101px;
    position: absolute;
    top: 33px;
    left: 301px;
}
.btn2{
    background: url("../images/btn1.png");
    width: 485px;
    height: 111px;
    position: absolute;
    top: 741px;
    left: -203px;
    text-align: center;
    line-height: 100px;
    color: #9f1623;
    font-size: 48px;
}
.btn2 span{background-color: #c42135; color: #fff; padding: 0 20px; margin: 0 10px;}
.an1{
    position: absolute;
    top: 166px;
}
.an2{
    position: absolute;
    top: 326px;
}
.an3{
    position: absolute;
    top: 786px;
}
.an4{
    position: absolute;
    top: 886px;
}
.txt3{
	position: absolute;
	left: -442px;
	top: 78px;
	font-size: 40px;
	width: 623px;
	color: #93542A;
}
.txt4{
	position: absolute;
	left: -210px;
	top: 116px;
	font-size: 50px;
	width: 453px;
	color: #DD130A;
	text-align: center;
}
.f62{font-size: 62px; font-weight: bold; line-height: 62px;}
.f22{font-size: 22px; text-decoration:line-through; color: #603813;}


.al1{
    width: 785px;
    height: 509px;
    position: absolute;
    left: -339px;
    top: 106px;
    border-radius: 10px;
}
.al2{
	width: 861px;
height: 320px;
position: absolute;
left: -381px;
top: 164px; border-radius: 36px;
}
.hdgz{
    width: 800px;
    position: absolute;
    left: -347px;
    top: 602px;
    font-size: 22px;
    color: #fff;
    line-height: 40px;
}
.hdgz2{
    width: 800px;
    position: absolute;
    left: -347px;
    top: 136px;
    font-size: 22px;
    color: #fff;
    line-height: 38px;
}

.djs{
    background: url(../images/djs.png) center top no-repeat;
    position: absolute;
    left: -75px;
    top: -1px;
    width: 255px;
    height: 108px;
    line-height: 50px;
    color: #fff;
    font-size: 28px;
    text-align: center;
    font-weight: bold;
    letter-spacing:8px;
}
.djs .t{
    position: absolute;
    left: 156px;
    top: 18px;
    width: 57px;
    color: #713600;
    font-size: 26px;
    letter-spacing: 0px;
}
.djs .s{
    position: absolute;
    left: 19px;
    top: 51px;
    width: 57px;
}
.djs .f{
    position: absolute;
    left: 94px;
    top: 51px;
    width: 61px;
}
.djs .m{
    position: absolute;
    left: 170px;
    top: 51px;
    width: 61px;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%; margin: -233px 0 0 -250px;
}
.btn3{
    background: url("../images/btn3.png");
    width: 397px;
    height: 161px;
    position: absolute;
    top: -17px;
    left: 180px;
}
.btn3b{
    background: url("../images/btn3b.png");
    width: 397px;
    height: 161px;
    position: absolute;
    top: -17px;
    left: 180px;
}
.tc5{
	position: fixed;
	top: 50%;
	left: 50%;margin: -299px 0 0 -243px;
}
.server{
    background: url("../images/server.png");
    width: 140px;
    height: 143px;
    position:fixed;
    top: 20%;
    right: 0px;
}
.txt1{
	position: absolute;
	left: -156px;
	top: 359px;
	width: 450px;
	color: #7f4f21;
	font-size: 18px;
	height: 32px;
	overflow: hidden;
	line-height: 32px;
}
.txt2{
    position: absolute;
    left: -359px;
    top: 224px;
    width: 870px;
}
.txt2 a{ float: left; width: 409px; height: 156px;}
.txt2 a:hover{}
.b1{background: url("../images/b1.png") center top no-repeat}
.b2{background: url("../images/b2.png") center top no-repeat}
.b3{background: url("../images/b3.png") center top no-repeat}
.b4{background: url("../images/b4.png") center top no-repeat}
.b5{background: url("../images/b5.png") center top no-repeat}
.b6{background: url("../images/b6.png") center top no-repeat}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}
