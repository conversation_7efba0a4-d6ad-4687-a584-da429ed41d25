@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#F1232D;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #AD1E14;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center no-repeat; height:426px;}
.img_2{background:url(../images/index_02.png) center no-repeat; height:1067px;}
.img_3{background:url(../images/index_03.png) center no-repeat; height:1101px;}
.img_4{background:url(../images/index_04.png) center no-repeat; height:1115px;}
.img_5{background:url(../images/index_05.png) center no-repeat; height:837px;}

.djs{
	background: url(../images/djs.png) center top no-repeat;
	position: absolute;
	left: -75px;
	top: 18px;
	width: 250px;
	height: 86px;
    text-align: center;
    font-family: "微软雅黑"; font-size: 28px; padding-top: 48px; color: #ffffff; display: none;
}
.djs .t{
    color: #fff;
    position: absolute;
    left: 162px;
    top: 2px;
    font-size: 26px;
    width: 45px;
}
.djs .s{
    color: #fff;
    position: absolute;
    left: 30px;
    top: 37px;
    font-size: 30px;
    width: 45px;
}
.djs .ss{
    color: #fff;
    position: absolute;
    left: 62px;
    top: 50px;
    font-size: 17px;
    width: 45px;
}
.djs .f{
    color: #fff;
    position: absolute;
    left: 98px;
    top: 37px;
    font-size: 30px;
    width: 45px;
}
.djs .ff{
    color: #fff;
    position: absolute;
    left: 131px;
    top: 50px;
    font-size: 17px;
    width: 45px;
}
.djs .m{
    color: #fff;
    position: absolute;
    left: 163px;
    top: 37px;
    font-size: 30px;
    width: 45px;
}
.djs .mm{
    color: #fff;
    position: absolute;
    left: 199px;
    top: 50px;
    font-size: 17px;
    width: 45px;
}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin: 20px 0 180px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.main{width:100px; margin:0 auto; position:relative;}
.pf1,.pf2{
	background: url("../images/pf1.png") center no-repeat;
	height:136px;position:fixed; bottom:0; width:100%
}
.pf2{
	background: url("../images/pf1h.png") center no-repeat;
}

.bg1{
	position: absolute;
	top: 238px;
	left: -168px;
}
.btn1{
    background: url("../images/btn2.png");
    width: 592px;
    height: 121px;
    position: absolute;
    top: 890px;
    left: -250px;
}
.btn1_2{
    background: url("../images/btn2_2.png");
    width: 592px;
    height: 121px;
    position: absolute;
    top: 890px;
    left: -250px;
}
.btn2{
    background: url("../images/btn1.png");
    width: 592px;
    height: 121px;
    position: absolute;
    top: 880px;
    left: -250px;
}

.btn2_2{
    background: url("../images/btn1_2.png");
    width: 592px;
    height: 121px;
    position: absolute;
    top: 880px;
    left: -250px;
}

.al1{
    width: 867px;
    height: 458px;
    position: absolute;
    left: -388px;
    top: 316px;
    border-radius: 10px;
}
.al2{
	width: 861px;
height: 320px;
position: absolute;
left: -381px;
top: 164px; border-radius: 36px;
}
.hdgz{
	width: 912px;
	font-size: 20px;
	color: #fff; margin: 0 auto; line-height: 30px;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
.tc3{position: fixed;top:50%; left:50%; width: 541px; height: 600px; margin: -300px 0 0 -270px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh2:hover{
-webkit-animation:pulse 0.1s  ease both;
-moz-animation:pulse 0.1s  ease both;}
@-webkit-keyframes pulse{
0%{-webkit-transform:scale(1)}
100%{-webkit-transform:scale(1.05)}
}
@-moz-keyframes pulse{
0%{-moz-transform:scale(1)}
100%{-moz-transform:scale(1.05)}
}
.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.07);
    transform: scale(1.07);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.07);
    transform: scale(1.07);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%; margin: -290px 0 0 -198px;
}
.btn5{
    background: url("../images/btn1.png"); background-size: 100% 100%;
    width:393px;
    height:70px; display: block; margin: 10px auto 0;
}
.btn3{
    background: url("../images/btn3.png");
    width: 293px;
    height: 112px;
    position: absolute;
    top: 20px;
    left: 260px;
}
.btn6{
    background: url("../images/btn4.png");
    width: 293px;
    height: 112px;
    position: absolute;
    top: 20px;
    left: 160px;
}
.tc5{
	position: fixed;
	top: 50%;
	left: 50%;margin: -299px 0 0 -243px;
}

.pf4{
	background: url("../images/pf4.png");
	width: 252px;
	height: 199px;
	position:fixed;
top: 20%; right: 0px; text-align: center; color: #fff; font-size: 26px;}
.pf4-btn,.pf4-btn2{
	background: url("../images/pf4-btn.png") center top no-repeat;
	position: absolute;
	left: 35px;
    top: 127px;
	width: 182px;
	height: 48px;
}
.pf4-btn2{
	background: url("../images/pf4-btn2.png") center top no-repeat;
}
.server{
	position:fixed;
top: 50%; right: 0px;}
.txt1{
    position: absolute;
    left: 197px;
    top: 304px;
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	left: 40px;
	top:480px;
	width: 188px;
	height: 78px;
}
.close1{
	background: url("../images/close1.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top:-10px;
	width: 30px;
	height: 30px;
}
.btn7{
	background: url("../images/btn5.png") center top no-repeat;
	position: absolute;
	left: 245px;
	top:480px;
	width: 261px;
	height: 84px;
}
.btn8{
	background: url("../images/btn6.png") center top no-repeat;
	position: absolute;
	left: 100px;
	top:335px;
	width: 343px;
	height: 89px;
	font-size: 39px; color: #B51504; text-align: center; line-height: 86px; font-weight:500;
}

.a1{
    background: url("../images/a1.png") center top no-repeat;
    position: absolute;
    left: -332px;
    top: 233px;
    width: 238px;
    height: 187px;
}
.a2{
    background: url("../images/a2.png") center top no-repeat;
    position: absolute;
    left: -77px;
    top: 233px;
    width: 238px;
    height: 187px;
}
.a3{
    background: url("../images/a3.png") center top no-repeat;
    position: absolute;
    left: 178px;
    top: 233px;
    width: 238px;
    height: 187px;
}
.a4{
    background: url("../images/a4.png") center top no-repeat;
    position: absolute;
    left: -332px;
    top: 438px;
    width: 238px;
    height: 187px;
}
.a5{
    background: url("../images/a5.png") center top no-repeat;
    position: absolute;
    left: 178px;
    top: 438px;
    width: 238px;
    height: 187px;
}
.a6{
    background: url("../images/a6.png") center top no-repeat;
    position: absolute;
    left: -332px;
    top: 645px;
    width: 238px;
    height: 187px;
}
.a7{
    background: url("../images/a7.png") center top no-repeat;
    position: absolute;
    left: -77px;
    top: 645px;
    width: 238px;
    height: 187px;
}
.a8{
    background: url("../images/a1.png") center top no-repeat;
    position: absolute;
    left: 178px;
    top: 645px;
    width: 238px;
    height: 187px;
}
.a9{
    background: url("../images/a8.png") center top no-repeat;
    position: absolute;
    left: -78px;
    top: 433px;
    width: 240px;
    height: 200px;
}
.a9_2{
    background: url("../images/a8_2.png") center top no-repeat;
    position: absolute;
    left: -78px;
    top: 433px;
    width: 240px;
    height: 200px;
}
.btn4{background-color: #ED5A24; width: 180px; height: 56px; border-radius: 28px; line-height: 56px; text-align: center; color: #fff; font-size: 26px; display: block; margin-bottom: 24px;}
.b1{
    background: url("../images/b1.png") center top no-repeat;
    position: absolute;
    left: -355px;
    top: 138px;
    width: 248px;
    height: 82px;
}
.b2{
    background: url("../images/b2.png") center top no-repeat;
    position: absolute;
    left: -69px;
    top: 138px;
    width: 248px;
    height: 82px;
}
.b3{
    background: url("../images/b3.png") center top no-repeat;
    position: absolute;
    left: 215px;
    top: 138px;
    width: 248px;
    height: 82px;
}
