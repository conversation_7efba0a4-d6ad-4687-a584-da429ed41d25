@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#8a0d18;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #AD1E14;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center no-repeat; height:386px;}
.img_2{background:url(../images/index_02.png) center no-repeat; height:337px;}
.img_3{background:url(../images/index_03.png) center no-repeat; height:753px;}
.img_4{background:url(../images/index_04.png) center no-repeat; height:1118px;}
.img_5{background:url(../images/index_05.png) center no-repeat; height:1184px;}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin: 20px 0 180px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.main{width:100px; margin:0 auto; position:relative;}
.pf1,.pf2{
	background: url("../images/pf1.png") center no-repeat;
	height:160px;position:fixed; bottom:0; width:100%
}
.pf2{
	background: url("../images/pf1h.png") center no-repeat;
}

.bg1{
	position: absolute;
	top: 238px;
	left: -168px;
}
.btn1{
    background: url("../images/btn1.png");
    width: 502px;
    height: 90px;
    position: absolute;
    top: 1001px;
    left: -199px;
}
.btn2{
    background: url("../images/btn2.png");
    width: 502px;
    height: 90px;
    position: absolute;
    top: 959px;
    left: -199px;
}



.al1{
    width: 622px;
    height: 750px;
    position: absolute;
    left: -257px;
top: 143px;
    border-radius: 10px;
}
.al2{
    width: 611px;
    height: 435px;
    position: absolute;
    left: -97px;
    top: 138px;
    border-radius: 10px;
}
.hdgz{
	width: 912px;
	font-size: 20px;
	color: #fff; margin: 0 auto; line-height: 30px;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh,.dh2:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%; margin: -290px 0 0 -198px;
}
.btn5{
    background: url("../images/btn1.png"); background-size: 100% 100%;
    width:393px;
    height:70px; display: block; margin: 10px auto 0;
}
.btn3{
    background: url("../images/btn3.png");
    width: 447px;
    height: 173px;
    position: absolute;
    top: -34px;
    left: 220px;
}
.tc5{
	position: fixed;
	top: 50%;
	left: 50%;margin: -299px 0 0 -243px;
}

.pf4{
	background: url("../images/pf4.png");
	width: 219px;
	height: 225px;
	position:fixed;
top: 20%; right: 0px; text-align: center; color: #fff; font-size: 26px; padding-top: 15px;
}
.pf4-btn,.pf4-btn2{
	background: url("../images/pf4-btn.png") center top no-repeat;
	position: absolute;
	left: 33px;
top: 172px;
	width: 154px;
	height: 48px;
}
.pf4-btn2{
	background: url("../images/pf4-btn2.png") center top no-repeat;
}
.txt1{
    position: absolute;
    left: 197px;
    top: 304px;
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}

.a1{
    background: url("../images/a1.png") center top no-repeat;
    position: absolute;
    left: -427px;
    top: 46px;
    width: 189px;
    height: 201px;
}
.a2{
    background: url("../images/a2.png") center top no-repeat;
    position: absolute;
    left: -222px;
    top: 46px;
    width: 189px;
    height: 201px;
}
.a3{
    background: url("../images/a3.png") center top no-repeat;
    position: absolute;
    left: -17px;
    top: 46px;
    width: 189px;
    height: 201px;
}
.btn4{background-color: #ED5A24; width: 180px; height: 56px; border-radius: 28px; line-height: 56px; text-align: center; color: #fff; font-size: 26px; display: block; margin-bottom: 24px;}
.b1{
    background: url("../images/b1.png") center top no-repeat;
    position: absolute;
    left: -355px;
    top: 138px;
    width: 248px;
    height: 82px;
}
.b2{
    background: url("../images/b2.png") center top no-repeat;
    position: absolute;
    left: -69px;
    top: 138px;
    width: 248px;
    height: 82px;
}
.b3{
    background: url("../images/b3.png") center top no-repeat;
    position: absolute;
    left: 215px;
    top: 138px;
    width: 248px;
    height: 82px;
}
.tc3{position: fixed;top:50%; left:50%; width: 300px; height: 311px; margin: -155px 0 0 -150px;}