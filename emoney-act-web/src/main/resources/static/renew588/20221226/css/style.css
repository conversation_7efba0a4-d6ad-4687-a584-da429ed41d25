@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#FEA063;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #e42c34; font-weight: bold;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center; height:387px;}
.img_2{background:url(../images/index_02.png) center; height:387px;}
.img_3{background:url(../images/index_03.png) center; height:741px;}
.img_4{background:url(../images/index_04.png) center; height:797px;}
.img_5{background:url(../images/index_05.png) center; height:603px;}
.img_6{background:url(../images/index_06.png) center; height:492px;}
.img_7{background:url(../images/index_07.png) center; height:489px;}

.hdgz{
	position: absolute;
	top: 102px;
	left: -364px;
	color: #973A0A;
	font-size: 19px;
	width: 828px;
	line-height: 46px;
}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:20px 0 196px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.bott{background:url("../images/pf1.png") center;height:160px;position:fixed; bottom:0; width:100%;}
.bott2{background:url("../images/pf2.png") center;height:160px;position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}
.hp{
	width: 830px;
	height: 280px;
	overflow: auto;
	position: absolute;
	top: 136px;
	left: -366px;
	text-align: center;
}
.p1{
	background: url("../images/p1.png");
	width: 420px;
	height: 303px;
	position: absolute;
	top: -96px;
	left: -393px;
}
.p2{
	background: url("../images/p2.png");
	width: 396px;
	height: 268px;
	position: absolute;
	top: -79px;
left: -388px;
}
.ico1{
	background: url("../images/ico1.png");
	width: 211px;
	height: 100px;
	position: absolute;
	top: 180px;
	left: 121px;pointer-events: none; 
}
.ico2{
	background: url("../images/ico2.png");
	width: 360px;
	height: 100px;
	position: absolute;
	top: -39px;
	left: 24px;
}
.ico2h{
	background: url("../images/ico2h.png");
	width: 360px;
	height: 100px;
	position: absolute;
	top: -39px;
	left: 24px;
}
.ico3 {
	background: url(../images/ico3.png);
	width: 312px;
	height: 85px;
	position: absolute;
	top: 373px;
	left: 153px;
}
.ico2b{
	background: url("../images/ico2.png");
	width: 164px;
	height: 39px;
	position: absolute;
	top: 678px;
	left: 116px;
}
.btn1,.btn1h{
	background: url("../images/btn1.png");
	width: 119px;
	height: 126px;
	position: absolute;
	top: -112px;
	left: 200px;
}
.btn1h{background: url("../images/btn1h.png");}
.btn2{
	background: url("../images/btn2.png");
	width: 401px;
	height: 120px;
	position: absolute;
	top: 217px;
	left: -156px;
}
.btn2h{
	background: url("../images/btn2h.png");
	width: 401px;
	height: 120px;
	position: absolute;
	top: 237px;
	left: -156px;
}
.btn3{
	background: url("../images/btn3.png");
	width: 285px;
	height: 70px;
	line-height: 60px;
	text-align: center;
	color: #FDE9BD; font-size: 34px;
}
.a1{
	position: absolute;
	top: 602px;
	left: -87px;
}
.a2{
	position: absolute;
	top: 652px;
	left: -87px;
}
.a3{
	position: absolute;
	top: 461px;
	left: -87px;
}
.btn5{
	position: absolute;
	top: 42px;
	left: 152px;
	background: url("../images/btn5.png");
	width: 366px;
	height: 93px;
}
.btn5h{
	position: absolute;
	top: 42px;
	left: 152px;
	background: url("../images/btn5h.png");
	width: 366px;
	height: 93px;
}
.btn6{position: absolute;
top: 423px;
left: 64px;
	background: url("../images/btn6.png");
	width: 323px;
	height:73px; 
}
.btn7{
	background: url("../images/btn7.png"); font-weight: bold;
	width:323px;
	height: 73px;position: absolute;
top: 424px;
left: 65px;
}

.sp{
	width: 596px;
	height: 370px;
	position: absolute;
	top: 169px;
	left: -108px;
}
.org{color: #e42c34; font-weight: bold;}


.djs{
	background: url(../images/djs.png) center top no-repeat;
	position: absolute;
	left: -161px;
	top: 0px;
	width: 395px;
	height: 73px;
	line-height: 50px;
	color: #fff;
	font-size: 24px;
	text-align: center;
	font-weight: bold;
	letter-spacing:11px; display: none;
}
.djs .t{
	position: absolute;
	left: 45px;
	top: 18px;
	width: 57px;
}
.djs .s{
	position: absolute;
	left: 124px;
	top: 18px;
	width: 57px;
}
.djs .f{
	position: absolute;
	left: 202px;
	top: 18px;
	width: 61px;
}
.djs .m{
	position: absolute;
	left: 281px;
	top: 18px;
	width: 61px;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%; margin: -268px 0 0 -275px;background: url("../images/tc1.png"); width:451px; height: 537px;
}

.tc5{
	position: fixed;
	top: 50%;
	left: 50%; margin: -268px 0 0 -275px;background: url("../images/tc2.png"); width:452px; height: 538px;
}
.server{
    background: url("../images/server.png");
    width: 118px;
    height: 97px;
    position:absolute;
    top: -100px;
    left: 39px;
}

.txt1{
	position: absolute;
	left: 84px;
top: 13px;
	width: 60px;
	color: #E82300;
	font-size: 22px;
}
.txt2{
    position: absolute;
    left: -359px;
    top: 224px;
    width: 870px;
}
.txt2 a{ float: left; width: 409px; height: 156px;}
.txt2 a:hover{background-position: 0 -156px;}


.dh2{
  -webkit-animation: dh2 2s linear infinite alternate;
  animation-name: dh2 2s linear infinite alternate;
}
@-webkit-keyframes dh2{
0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
40%{-webkit-transform:translateY(-30px)}
60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes dh2{
0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
40%{-moz-transform:translateY(-30px)}
60%{-moz-transform:translateY(-15px)}
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}
