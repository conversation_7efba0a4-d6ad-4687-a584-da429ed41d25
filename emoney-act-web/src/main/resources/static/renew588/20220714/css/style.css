@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#FB6450;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #AD1E14;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.jpg) center; height:257px;}
.img_2{background:url(../images/index_02.jpg) center; height:185px;}
.img_3{background:url(../images/index_03.jpg) center; height:185px;}
.img_4{background:url(../images/index_04.jpg) center; height:200px;}
.img_5{background:url(../images/index_05.jpg) center; height:235px;}
.img_6{background:url(../images/index_06.jpg) center; height:120px;}
.img_7{background:url(../images/index_07.jpg) center; height:119px;}
.img_8{background:url(../images/index_08.png) center; height:474px;}
.img_9{background:url(../images/index_09.png) center; height:976px;}
.img_10{background:url("../images/index_10.png") center; height:1262px;}
.img_11{background:url("../images/index_11.png") center; height:738px;}
.img_12{width: 840px; background-color: #FCCFA8; margin: 90px auto 0 auto; padding: 0 60px 40px 60px; color: #666; font-size: 24px; border-radius: 10px; line-height: 45px;}

.ico2{background:url("../images/ico2.png") center; width: 354px; height:88px; margin: 0 auto 30px auto;}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin: 20px 0 275px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.bott{background:url("../images/pf.png") center;height:160px;position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}

.btn1{
	background: url("../images/btn1.png");
	width: 535px;
	height: 148px;
	position: absolute;
	top: 21px;
	left: -218px;
}
.btn2{
	background: url("../images/btn2.png");
	width: 380px;
	height: 110px;
	position: absolute;
	top: 782px;
	left: -143px;
}
.btn2b{
	background: url("../images/btn2h.png");
	width: 380px;
	height: 110px;
	position: absolute;
	top: 782px;
	left: -143px;
}
.btn3{
	background: url("../images/btn3.png");
	width: 416px;
	height: 123px;
	position: absolute;
	top: 1067px;
	left: -155px;
}
.btn4{
	background: url("../images/btn4.png");
	width: 380px;
	height: 110px;
	
}
.i1{
	position: absolute;
	top: 584px;
	left: -141px;
}
.i2{
	position: absolute;
	top: 26px;
	left: 141px;
}

.sp{
	width: 607px;
	height: 392px;
	position: absolute;
	top: 580px;
	left: -128px;
	border: 2px solid #fff;
}
.ico{
	position: absolute;
	top: -110px;
	left: -384px;
	background: url("../images/ico.png");
	width: 499px;
	height: 130px;
	text-align: center;
	padding-right: 23px;
	color: #7f4c19;
	font-size: 35px;
	line-height: 102px;
}
.org{color: #e42c34; font-weight: bold;}


.djs{
    background: url(../images/djs.png) center top no-repeat;
    position: absolute;
    left: -75px;
    top: -1px;
    width: 259px;
    height: 86px;
    line-height: 50px;
    color: #fff;
    font-size: 22px;
    text-align: center;
    font-weight: bold;
    letter-spacing:8px; display: none;
}
.djs .t{
	position: absolute;
	left: 129px;
	top: -8px;
	width: 57px;
	color: #fee9b7;
	font-size: 18px;
	letter-spacing: 0px;
}
.djs .s{
	position: absolute;
	left: 28px;
	top: 18px;
	width: 57px;
}
.djs .f{
	position: absolute;
	left: 91px;
	top: 18px;
	width: 61px;
}
.djs .m{
	position: absolute;
	left: 156px;
	top: 18px;
	width: 61px;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%; margin: -233px 0 0 -250px;
}

.tc5{
	position: fixed;
	top: 50%;
	left: 50%;margin: -299px 0 0 -243px;
}
.server{
    background: url("../images/server.png");
    width: 140px;
    height: 143px;
    position:fixed;
    top: 20%;
    right: 0px;
}
.txt1{
	position: absolute;
	left: -156px;
	top: 359px;
	width: 450px;
	color: #7f4f21;
	font-size: 18px;
	height: 32px;
	overflow: hidden;
	line-height: 32px;
}
.txt2{
    position: absolute;
    left: -359px;
    top: 224px;
    width: 870px;
}
.txt2 a{ float: left; width: 409px; height: 156px;}
.txt2 a:hover{background-position: 0 -156px;}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}
