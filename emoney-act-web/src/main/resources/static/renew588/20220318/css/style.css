@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#871A1A;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #AD1E14;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center no-repeat; height:309px;}
.img_2{background:url(../images/index_02.png) center no-repeat; height:309px;}
.img_3{background:url(../images/index_03.png) center no-repeat; height:1039px;}
.img_4{background:url(../images/index_04.png) center no-repeat; height:1026px;}
.img_5{background:url(../images/index_05.png) center no-repeat; height:741px;}
.img_6{background:url(../images/index_06.png) center no-repeat; height:994px;}
.img_7{background:url(../images/index_07.png) center no-repeat; height:1014px;}
.img_8{background:url(../images/index_08.png) center no-repeat; height:989px;}
.img_9{background:url(../images/index_09.png) center no-repeat; height:1355px;}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin: 20px 0 145px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.bott{height:130px; background:url(../images/pf1.png) center no-repeat; position:fixed; bottom:0; width:100%;}
.bott2{height:130px; background:url(../images/pf2.png) center no-repeat; position:fixed; bottom:0; width:100%;}
.bott3{height:130px; background:url(../images/pf3.png) center no-repeat; position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}

.btn1,.btn1h{
	background: url("../images/btn1.png");
	width: 165px;
	height: 41px;
	text-align: center;
	line-height: 41px;
	color: #FFF0CF; font-size: 26px;
}
.btn1h{
	background: url("../images/btn1h.png"); color: #4D4D4D;
}
.an1{
	position: absolute;
	top: 367px;
	left: -192px;
}
.an2{
	position: absolute;
	top: 367px;
	left: 100px;
}
.bg1{
	position: absolute;
	top: 134px;
	left: -321px;
}
.ico{
	position: absolute;
	top: -88px;
	left: -376px;
}

.btn2{
	background: url("../images/btn2.png");
	width: 676px;
	height: 150px;
	position: absolute;
	top: 821px;
	left: -293px;
	text-align: center;
	line-height: 130px;
	color: #9f1623;
	font-size: 68px;
}
.btn9{
	width: 218px;
	height: 129px;
	position: absolute;
	top: 0px;
	left: 187px;
}
.btn10{
	width: 447px;
	height: 152px;
	position: absolute;
	top: 294px;
	left: -422px;
}
.btn11{
	width: 447px;
	height: 152px;
	position: absolute;
	top: 292px;
	left: 76px;
}
.btn12{
	width: 447px;
	height: 152px;
	position: absolute;
	top: 486px;
	left: -423px;
}
.btn13{
	width: 447px;
	height: 152px;
	position: absolute;
	top: 485px;
	left: 76px;
}

.txt3{
	position: absolute;
	left: -442px;
	top: 78px;
	font-size: 40px;
	width: 623px;
	color: #93542A;
}
.txt4{
	position: absolute;
	left: -210px;
	top: 116px;
	font-size: 50px;
	width: 453px;
	color: #DD130A;
	text-align: center;
}
.f62{font-size: 62px; font-weight: bold; line-height: 62px;}
.f22{font-size: 22px; text-decoration:line-through; color: #603813;}


.al1{
	width: 972px;
	height: 560px;
	position: absolute;
	left: -438px;
	top: 167px;
	border-radius: 10px;
}
.al2{
	width: 861px;
height: 320px;
position: absolute;
left: -381px;
top: 164px; border-radius: 36px;
}
.hdgz{
	width: 868px;
	position: absolute;
	left: -372px;
	top: 99px;
	font-size: 36px;
	color: #FFEED7;
	line-height: 54px;
}

.djs{
	background: url(../images/djs.png) center top no-repeat;
	position: absolute;
	left: -451px;
	top: 0px;
	width: 486px;
	height: 42px;
	line-height: 50px;color: #fff;font-size: 26px;text-align: center;
	font-weight: bold;letter-spacing:3px;
}
.djs .t{
	position: absolute;
	left: 155px;
	top: -3px;
	width: 57px;
}
.djs .s{
	position: absolute;
	left: 228px;
	top: -3px;
	width: 57px;
}
.djs .f{
	position: absolute;
	left: 299px;
	top: -3px;
	width: 61px;
}
.djs .m{
	position: absolute;
	left: 371px;
	top: -3px;
	width: 61px;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%;
	width:473px;
	height:488px; margin: -244px 0 0 -237px;
	background: url("../images/tc1.png");
}
.btn3{
	background: url("../images/btn3.png");
	width: 161px;
	height: 163px;
	position: absolute;
top: 297px;
left: 151px;
}
.tc5{
	position: fixed;
	top: 50%;
	left: 50%;
	width:442px;
	height:456px; margin: -228px 0 0 -221px;
	background: url("../images/tc2.png");
}
.btn4{
	background: url("../images/btn4.png");
	width: 193px;
	height: 194px;
	position: absolute;
top: 270px;
left: 122px;
}
.tc6{
	position: fixed;
	top: 50%;
	left: 50%;
	width:473px;
	height:488px; margin: -244px 0 0 -237px;
	background: url("../images/tc3.png");
}
.btn5{
	background: url("../images/btn5.png");
	width: 193px;
	height: 68px; text-align: center; color: #FDDDD1; line-height: 68px; font-size: 30px;
}
.an3{position: absolute;
top: 362px;
left: 33px;}
.an4{position: absolute;
top: 362px;
left: 33px;}
.btn6{
	background: url("../images/btn6.png");
	width: 210px;
	height: 85px;
	position: absolute;
top: 356px;
left: 237px;
}
.btn7{
	background: url("../images/btn7.png");
	width: 210px;
	height: 85px;
	position: absolute;
top: 356px;
left: 237px;
}

.tc7{
	position: fixed;
	top: 50%;
	left: 50%;
	width:474px;
	height:488px; margin:-244px 0 0 -237px;
	background: url("../images/tc4.png");
}
.tc8{
	position: fixed;
	top: 50%;
	left: 50%;
	width:473px;
	height:488px; margin:-244px 0 0 -237px;
	background: url("../images/tc5.png");
}
.btn8{
	background: url("../images/btn8.png");
	width: 270px;
	height: 79px;
	position: absolute;
top: 371px;
left: 97px;
}
.pf4{
	background: url("../images/pf4.png");
	width: 225px;
	height: 228px;
	position:fixed;
top: 20%; right: 0px;
}
.pf4h{
	 background: url("../images/pf4h.png");
	 width: 225px;
	 height: 228px;
	 position:fixed;
	 top: 20%; right: 0px;
 }
.txt1{
	position: absolute;
left: 33px;
top: 141px;width: 400px; text-align: center; color: #9f1623; font-size: 24px;
}
.txt1 .t1{color: #894c1a; font-size: 20px;text-decoration:line-through}
.txt2{position: absolute;left: 79px;top: 229px; font-size: 24px; color: #894c1a;}
.txt2 ul{height: 84px; overflow: hidden;}
.txt2 li{padding-bottom: 10px; line-height: 32px;}
.txt2 a{background-color: #9f1623; color: #fff; padding: 0 10px; margin-left: 10px; border-radius: 5px;}


.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: 10px;
	top: 10px;
	width: 30px;
	height: 30px;
}
