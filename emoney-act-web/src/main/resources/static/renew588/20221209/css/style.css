@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#C1201A;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #e42c34; font-weight: bold;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
a{color: #999999; text-decoration: none;}
a:hover{color: #000; text-decoration: none;}
.img_1{background:url(../images/index_01.jpg) center; height:215px;}
.img_2{background:url(../images/index_02.jpg) center; height:216px;}
.img_3{background:url(../images/index_03.jpg) center; height:215px;}
.img_4{background:url(../images/index_04.png) center; height:567px;}
.img_5{background:url(../images/index_05.png) center; height:719px;}
.img_6{background:url(../images/index_06.png) center; height:743px;}
.img_7{background:url(../images/index_07.png) center; height:1497px;content-visibility: auto;
    contain-intrinsic-size: 120px;}

.hdgz{
	position: absolute;
	top: 510px;
	left: -414px;
	color: #fff;
	font-size: 24px;
	width: 935px;
	line-height: 44px;
}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:20px 0 196px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.bott{background:url("../images/index_08.png") center;height:149px;position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}
.ico2{
	background: url("../images/ico2.png");
	width: 385px;
	height: 84px;
	position: absolute;
	top: -29px;
	left: 154px;
}

.btn1,.btn1h{
	background: url("../images/btn1.png");
	width: 159px;
	height: 38px;
	position: absolute;
	top: 148px;
left: 32px;
}
.btn1h{background: url("../images/btn1h.png");}
.btn2{
	background: url("../images/btn2.png");
	width: 622px;
	height: 150px;
	position: absolute;
	top: 57px;
	left: -256px;
}
.btn2h{
	background: url("../images/btn2h.png");
	width: 458px;
	height: 139px;
	position: absolute;
	top: 557px;
	left: -154px;
}
.btn3{
	background: url("../images/btn3.png");
	width: 345px;
	height: 87px;
	position: absolute;
	top: 619px;
	left: 18px;
}
.btn5{
	position: absolute;
	top: 17px;
	left: 78px;
	background: url("../images/btn5.png");
	width: 427px;
	height: 134px;
}
.btn7{
	background: url("../images/btn7.png"); font-weight: bold;
	width:362px;
	height:92px;position: absolute;
top: 419px;
left: 149px;
}

.djs{
	background: url(../images/djs.png) center top no-repeat;
	position: absolute;
	left: 89px;
	top: -8px;
	width: 255px;
	height: 123px;
	line-height: 50px;
	color: #fff;
	font-size: 24px;
	text-align: center;
	font-weight: bold;
	letter-spacing: 11px; display: none;
}
.djs .t{
	position: absolute;
	left: 149px;
	top: 12px;
	width: 57px;
	color: #A1140A;
	letter-spacing: 0px;
}
.djs .s{
	position: absolute;
	left: 45px;
	top: 46px;
	width: 57px;
}
.djs .f{
	position: absolute;
	left: 102px;
	top: 46px;
	width: 61px;
}
.djs .m{
	position: absolute;
	left: 159px;
	top: 46px;
	width: 61px;
}
.pf{background-image:url(../images/pf.png); width: 218px; height: 218px; text-align: center; font-size: 22px; color: #A1140A; line-height:67px; position: fixed; right: -20px; top: 300px;}
.txt1{
	position: absolute;
	left: 54px;
	top: 140px;
	width: 300px; font-size: 17px;
}
.pf2{background-image:url(../images/pf2.png); width: 182px; height:190px; text-align: center; font-size: 22px; color: #A1140A; line-height:67px; position: fixed; right: 0px; top: 100px;}
.btn8{
	background: url("../images/btn8.png");
	width:172px;
	height:61px;position: absolute;
top: 141px;
left: 6px;
}


.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%; margin: -268px 0 0 -275px;background: url("../images/tc1.png"); width:657px; height: 559px;
}

.server{
    background: url("../images/server.png");
    width: 118px;
    height: 97px;
    position:fixed;
    top:300px;
    right: 0px;
}


.dh2{
  -webkit-animation: dh2 2s linear infinite alternate;
  animation-name: dh2 2s linear infinite alternate;
}
@-webkit-keyframes dh2{
0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
40%{-webkit-transform:translateY(-30px)}
60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes dh2{
0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
40%{-moz-transform:translateY(-30px)}
60%{-moz-transform:translateY(-15px)}
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}
