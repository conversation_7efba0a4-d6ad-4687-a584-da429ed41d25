@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#A80000;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #AD1E14;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center; height:265px;}
.img_2{background:url(../images/index_02.png) center; height:311px;}
.img_3{background:url(../images/index_03.png) center; height:709px;}
.img_4{background:url(../images/index_04.png) center; height:714px;}
.img_5{background:url(../images/index_05.png) center; height:604px;}

.hdgz{
	position: absolute;
	top: 156px;
	left: -409px;
	color: #6b5340;
	font-size: 20px;
	width: 900px;
	line-height: 27px;
}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:0px 0 125px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.bott{background:url("../images/pf.png") center;height:96px;position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}

.btn1{
	background: url("../images/btn1.png");
	width: 282px;
	height: 63px;
	position: absolute;
	top: 199px;
	left: 145px;
}
.btn2,.btn2h{
	background: url("../images/btn2.png");
	width: 354px;
	height: 92px;
	position: absolute;
	top: 487px;
	left: -123px;
}
.btn2h{
	background: url("../images/btn2h.png");
}
.btn3{
	background: url("../images/btn3.png");
	width: 317px;
	height: 78px;
	position: absolute;
	top: 590px;
	left: -112px;
}
.btn4{
	position: absolute;
	top: -58px;
	left: 159px;
	background: url("../images/btn4.png");
	width: 431px;
	height: 151px;
}
.btn5{
	position: absolute;
	top: 173px;
	left: -226px;
	background: url("../images/btn5.png");
	width: 75px;
	height: 61px;
}
.btn6,.btn6h{
	background: url("../images/btn6.png");
	width: 198px;
	height: 49px; text-align: center; line-height: 45px; color: #fde9bd; font-size: 24px;
}
.btn6h{
	background: url("../images/btn6h.png");
}
.i1{
	position: absolute;
	top: 375px;
	left: -367px;
}
.i2{
	position: absolute;
	top: 375px;
	left: -49px;
}
.i3{
	position: absolute;
	top: 375px;
	left: 271px;
}
.btn9,.btn9h{
	background: url("../images/btn9.png"); font-weight: bold;
	width:325px;
	height: 74px; text-align: center; line-height:60px; color: #ff351a; font-size: 40px;position: absolute;
	top: 425px;
	left: 63px;
}
.btn9h{	background: url("../images/btn9h.png"); color: #6f6f6f;}
.btn10{position: absolute;
	top: 131px;
	left: 42px;}
.btn10 li{
	background: url("../images/btn10.png"); font-weight: bold;
	width:38px;
	height:43px; cursor:pointer; margin-bottom: 30px;
}
.btn10 .on{background: url("../images/btn10h.png");}


.sp{
	width: 607px;
	height: 392px;
	position: absolute;
	top: 158px;
	left: -74px;
	border: 2px solid #fff;
}
.org{color: #e42c34; font-weight: bold;}


.djs{
	background: url(../images/djs.png) center top no-repeat;
	position: absolute;
	left: -108px;
	top: -1px;
	width: 256px;
	height: 101px;
	line-height: 50px;
	color: #fff;
	font-size: 26px;
	text-align: center;
	font-weight: bold;
	letter-spacing:10px; display: none;
}
.djs .t{
	position: absolute;
	left: 159px;
	top: 7px;
	width: 57px;
}
.djs .s{
	position: absolute;
	left: 20px;
	top: 45px;
	width: 57px;
}
.djs .f{
	position: absolute;
	left: 95px;
	top: 45px;
	width: 61px;
}
.djs .m{
	position: absolute;
	left: 172px;
	top: 45px;
	width: 61px;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}

.dh{
	-webkit-animation: dh 0.3s linear infinite alternate;
	animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}

@keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%; margin: -233px 0 0 -250px;
}

.tc5{
	position: fixed;
	top: 50%;
	left: 50%;margin: -299px 0 0 -243px;
}
.server{
	background: url("../images/server.png");
	width: 140px;
	height: 143px;
	position:fixed;
	top: 20%;
	right: 0px;
}
.txt1{
	position: absolute;
	left: -156px;
	top: 359px;
	width: 450px;
	color: #7f4f21;
	font-size: 18px;
	height: 32px;
	overflow: hidden;
	line-height: 32px;
}
.txt2{
	position: absolute;
	left: -359px;
	top: 224px;
	width: 870px;
}
.txt2 a{ float: left; width: 409px; height: 156px;}
.txt2 a:hover{background-position: 0 -156px;}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}
