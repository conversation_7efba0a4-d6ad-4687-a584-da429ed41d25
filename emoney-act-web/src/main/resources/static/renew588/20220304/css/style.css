@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #AD1E14;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/images_01.png) center no-repeat; height:489px;}
.img_2{background:url(../images/images_02.png) center no-repeat; height:682px;}
.img_2b{background:url(../images/images_02b.png) center no-repeat; height:682px;}
.img_3{background:url(../images/images_03.png) center no-repeat; height:446px;}
.img_4{background:url(../images/images_06.png) center no-repeat; height:569px;}
.img_5{background:url(../images/images_07.png) center no-repeat; height:390px;}
.img_6{background:url(../images/images_08.png) center no-repeat; height:400px;}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff;padding: 30px 0 245px;
	background:url("../images/images_10.png") center repeat-y;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.bott{height:170px; background:url(../images/images_12.png) center no-repeat; position:fixed; bottom:0; width:100%;}
.bott2{height:170px; background:url(../images/images_13.png) center no-repeat; position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}
.png_1{position: absolute; top:0px; left: -405px;}

.btn1,.btn1h{
	background: url("../images/btn1.png");
	width: 200px;
	height: 61px;
	text-align: center;
	line-height: 61px;
	color: #FFF0CF; font-size: 26px;
}
.btn1h{
	background: url("../images/btn1h.png"); color: #4D4D4D;
}
.an1{
    position: absolute;
    top: 377px;
    left: 137px;
}
.an2{
    position: absolute;
    top: 377px;
    left: 137px;
}
.bg1{
	position: absolute;
	top: -22px;
	left: -331px;
}
.ico{
	position: absolute;
	top: -88px;
	left: -376px;
}

.btn2{
	background: url("../images/btn2.png");
	width: 354px;
	height: 83px;
	position: absolute;
	top: 259px;
	left: -123px;
}
.btn8{
    width: 354px;
    height: 84px;
    position: absolute;
    top: 43px;
    left: 187px;
    background: url("../images/btn8.png") no-repeat;
}
.btn8_1{
    width: 354px;
    height: 84px;
    position: absolute;
    top: 43px;
    left: 187px;
}

.txt3{
	position: absolute;
	left: -442px;
	top: 78px;
	font-size: 40px;
	width: 623px;
	color: #93542A;
}
.txt4{
	position: absolute;
	left: -210px;
	top: 116px;
	font-size: 50px;
	width: 453px;
	color: #DD130A;
	text-align: center;
}
.f62{font-size: 62px; font-weight: bold; line-height: 62px;}
.f22{font-size: 22px; text-decoration:line-through; color: #603813;}


.al1{
	width: 980px;
	height: 560px;
	position: absolute;
	left: -438px;
	top: 86px;
}
.al2{
	width: 861px;
height: 320px;
position: absolute;
left: -381px;
top: 164px; border-radius: 36px;
}
.hdgz{
	width: 868px;
	position: absolute;
	left: -372px;
	top: 99px;
	font-size: 36px;
	color: #FFEED7;
	line-height: 54px;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 200px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%;
	width:551px;
	height:422px; margin: -284px 0 0 -280px;
	background: url("../images/tc1.png");
}
.btn3{
	background: url("../images/btn3.png");
	width: 287px;
	height: 62px;
	position: absolute;
top: 300px;
left: 137px;
}
.tc5{
	position: fixed;
	top: 50%;
	left: 50%;
	width:551px;
	height:409px; margin: -284px 0 0 -280px;
	background: url("../images/tc2.png");
}
.btn4{
	background: url("../images/btn4.png");
	width: 225px;
	height: 54px;
	position: absolute;
top: 290px;
left: 271px;
}
.btn9{
	background: url("../images/btn9.png");
	width: 191px;
	height: 54px;
	position: absolute;
top: 290px;
left: 55px;
}
.tc6{
	position: fixed;
	top: 50%;
	left: 50%;
	width:548px;
	height:585px; margin: -292px 0 0 -274px;
	background: url("../images/tc3.png");
}
.btn5{
	background: url("../images/btn5.png");
	width: 213px;
	height: 99px; text-align: center; color: #FDDDD1; line-height: 99px; font-size: 30px;
}
.an3{position: absolute;
top: 462px;
left: 21px;}
.an4{position: absolute;
top: 457px;
left: 88px;}
.btn6{
	background: url("../images/btn6.png");
	width: 349px;
	height: 181px;
	position: absolute;
top: 418px;
left: 218px;
}
.btn7{
	background: url("../images/btn7.png");
	width: 282px;
	height: 174px;
	position: absolute;
top: 418px;
left: 281px;
}

.txt1{
	position: absolute;
	left: 45px;
top: 163px;width: 520px;
}
.txt1 li{float: left; width: 256px; height: 142px; color: #000; font-size: 24px;}
.s1{background-color: #FFEBDA; padding: 3px 8px; margin-left: 15px; color: #D1612F; font-size: 24px; border-radius: 5px;}
.s2{color: #DD303F; font-size: 25px; line-height: 35px; margin-top: 5px;}

.tc7{
	position: fixed;
	top: 50%;
	left: 50%;
	width:553px;
	height:580px; margin:-328px 0 0 -309px;
	background: url("../images/tc4.png");
}


.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: 5px;
	top: 0px;
	width: 25px;
	height: 25px;
}
