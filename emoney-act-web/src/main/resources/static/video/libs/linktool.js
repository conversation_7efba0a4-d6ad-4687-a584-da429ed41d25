$(function () {
    FastClick.attach(document.body);
    var encrypt = new JSEncrypt();
    var page = {
        init: function () {
            var _this = this;
            _this.SendPwdFlag = true;
            _this.LoginFlag = true;
            _this.tnum = 0;
            _this.flagtime = 0;
            _this.SID = "1241881";
            _this.TID = "234";
            _this.acCode = "AC2020122501";
            _this.mobileAcCode = "AC2020122502";
            _this.UID = "";
            _this.PID = "888030000";
            _this.DivId = "div_pcAD";
            _this.Divh5Id = "div_h5AD";
            _this._UserName = "";
            var appiId = $("#hid_appid").val();
            // 视频直播窗口resize
            _this.resetLiveContboxSize();
            _this.bindEvents();
            var pid = $("#hid_pid").attr("value");
            var uid = $("#hid_uid").attr("value");
            var ownerId = $("#hid_ownerid").attr("value");
            var acCode = _this.acCode;
            var divId = _this.DivId;
            var u = navigator.userAgent;
            var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //安卓终端
            var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
            if (isAndroid || isIOS) {
                acCode = _this.mobileAcCode;
                divId = _this.Divh5Id;
            }

            if ($("#popViewBox").length === 0 && $("#classHasNoright").length === 0) {
                if (pid == "999999999") pid = _this.PID;
                AddjrptAd_Click(divId, _this.SID, _this.TID, pid, acCode, appiId, uid, _this._UserName);
                _this.videoMediaPlay();
            } else {
                AddjrptAd_Click(divId, _this.SID, _this.TID, _this.PID, acCode, appiId, _this.UID, _this._UserName);
            }
            var IEVersion = (function () {
                var userAgent = navigator.userAgent;
                var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1;
                var isEdge = userAgent.indexOf("Edge") > -1 && !isIE;
                var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
                if (isIE) {
                    var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
                    reIE.test(userAgent);
                    var fIEVersion = parseFloat(RegExp["$1"]);
                    if (fIEVersion == 7) {
                        return 7;
                    } else if (fIEVersion == 8) {
                        return 8;
                    } else if (fIEVersion == 9) {
                        return 9;
                    } else if (fIEVersion == 10) {
                        return 10;
                    } else {
                        return 6;
                    }
                } else if (isEdge) {
                    return 13;
                } else if (isIE11) {
                    return 11;
                } else {
                    return -1;
                }
            })();
            if (!(IEVersion == -1 || IEVersion > 8)) {
                $("#imgurl").html("");
                var url = window.location.href;
                $("#imgurl").qrcode({
                    render: "table", // 渲染方式有table方式和canvas方式
                    width: 150,   //默认宽度
                    height: 150, //默认高度
                    text: url, //二维码内容
                    typeNumber: -1,   //计算模式一般默认为-1
                    correctLevel: 2, //二维码纠错级别
                    background: "#ffffff",  //背景颜色
                    foreground: "#000000"  //二维码颜色
                });
                $("#imgurl").css({"margin-top": "30px"})
            } else {
                var elementById = document.getElementById("imgurl");
                if (elementById) {
                    elementById.innerHTML = "";
                    var _imgUrl = window.location.href;
                    var qrcode = new QRCode(document.getElementById("imgurl"), {
                        width: 271,
                        height: 271
                    });
                    qrcode.makeCode(_imgUrl);
                }
            }

            var defaultLoginPanel = $("#tab-login > a").first().data("tab-name");
            _this.selectLoginPanel(defaultLoginPanel)
        },
        // 浏览环境判断
        // 判断浏览器内核、手机系统等，使用 browser.version.ios
        browser: {
            ver: function () {
                var u = navigator.userAgent;
                var ua = navigator.userAgent.toLocaleLowerCase();
                var app = navigator.appVersion;
                return {
                    trident: u.indexOf('Trident') > -1, // IE内核
                    presto: u.indexOf('Presto') > -1, // opera内核
                    webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
                    gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, // 火狐内核
                    mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/AppleWebKit/), // 是否为移动终端
                    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // IOS终端
                    android: u.indexOf('Android') > -1 || u.indexOf('Mac') > -1, // 安卓终端
                    iPhone: u.indexOf('iPhone') > -1 || u.indexOf('Mac') > -1, // 是否为iphone或QQHD浏览器
                    iPad: u.indexOf('iPad') > -1, // 是否为iPad
                    webApp: u.indexOf('Safari') == -1, // 是否web应用程序，没有头部与底部
                    QQbrw: u.indexOf('MQQBrowser') > -1, // QQ浏览器
                    weiXin: u.indexOf('MicroMessenger') > -1, // 微信
                    QQ: ua.match(/QQ/i) == "qq", // QQ
                    weiBo: ua.match(/WeiBo/i) == "weibo", // 微博
                    ucLowEnd: u.indexOf('UCWEB7.') > -1, //
                    ucSpecial: u.indexOf('rv:*******') > -1,
                    webview: !(u.match(/Chrome\/([\d.]+)/) || u.match(/CriOS\/([\d.]+)/)) && u.match(/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/),
                    ucweb: function () {
                        try {
                            return parseFloat(u.match(/ucweb\d+\.\d+/gi).toString().match(/\d+\.\d+/).toString()) >= 8.2
                        } catch (e) {
                            if (u.indexOf('UC') > -1) {
                                return true;
                            }
                            return false;
                        }
                    }(),
                    Symbian: u.indexOf('Symbian') > -1,
                    ucSB: u.indexOf('Firofox/1.') > -1
                };
            }()
        },
        //登录
        userLogin: function (form) {
            var _this = this;
            if (_this.LoginFlag) {
                var inputs = form.find("input")
                var $account = inputs.filter("[name=account]");
                var $pwd = inputs.filter("[name=password]");
                var $loginMod = inputs.filter("[name=loginMod]");//是否自动登录
                var isLoginMod = "0";
                var account = $account.val();
                var defaultAccount = $account.attr("readonly");

                if (!defaultAccount && account == "") {
                    layer.msg("请输入手机号/EM卡号");
                    $account.focus();
                    return;
                }
                if ($pwd.val() == "") {
                    layer.msg("请输入登录密码");
                    $pwd.focus();
                    return;
                }
                if ($loginMod.is(':checked')) {
                    isLoginMod = "1";
                }
                _this.LoginFlag = false;
                $.when(_this.securityCheck()).done(function () {
                    $.ajax({
                        url: "../user-login",
                        type: "POST",
                        dataType: "json",
                        data: {
                            username: defaultAccount ? undefined : _this.do_encrypt(account),
                            userpwd: _this.do_encrypt($pwd.attr('value')),
                            loginmod: _this.do_encrypt(isLoginMod),
                        },
                        success: function (data) {
                            if (data.success) {
                                location.reload();
                            } else {
                                layer.msg(data.msg);
                                _this.LoginFlag = true;
                            }
                        }
                    });
                });
            }
        },
        smsLogin: function (form) {
            var _this = this;
            if (_this.LoginFlag) {
                var inputs = form.find("input")
                var $mobile = inputs.filter("[name=mobile]");
                var $pwd = inputs.filter("[name=code]");
                var $loginMod = inputs.filter("[name=loginMod]");//是否自动登录
                var isLoginMod = "0";
                var mobile = $mobile.val();
                var defaultAccount = $mobile.attr("readonly");

                if (!defaultAccount && !/^1[3-9]\d{9}$/.test(mobile)) {
                    layer.msg("请输入有效手机号");
                    $mobile.focus();
                    return;
                }
                if (!/^\d{4}$/.test($pwd.val())) {
                    layer.msg("请输入有效验证码");
                    $pwd.focus();
                    return;
                }
                if ($loginMod.is(':checked')) {
                    isLoginMod = "1";
                }
                _this.LoginFlag = false;
                $.when(_this.securityCheck()).done(function () {
                    $.ajax({
                        url: "../sms-login",
                        type: "POST",
                        dataType: "json",
                        data: {
                            mobile: defaultAccount ? undefined : _this.do_encrypt(mobile),
                            code: _this.do_encrypt($pwd.attr('value')),
                            loginmod: _this.do_encrypt(isLoginMod),
                        },
                        success: function (data) {
                            if (data.success) {
                                location.reload();
                            } else {
                                layer.msg(data.msg);
                                if (data.code.startsWith("V")) {
                                    var validate = $pwd.next(".validate");
                                    if (validate.length === 0) {
                                        validate = $pwd.after("<span class='validate' style='color: red'></span>").next(".validate");
                                    }
                                    validate.text(data.msg);
                                }
                                _this.LoginFlag = true;
                            }
                        }
                    });
                });
            }
        },
        //获取密码
        getOneTimePassword: function () {
            var _this = this;
            if (_this.SendPwdFlag) {
                var $mobile = $("form[data-tab-name='sms-login'] input[name=mobile]");
                var $pwd = $("form[data-tab-name='sms-login'] input[name=code]");
                var defaultAccount = $mobile.attr("readonly");
                var mobile = $mobile.val();
                if (!defaultAccount && !/^1[3-9]\d{9}$/.test(mobile)) {
                    layer.msg("请先输入有效的手机号，再获取验证码");
                    $mobile.focus();
                    return;
                }
                _this.SendPwdFlag = false;
                $.when(_this.securityCheck()).done(function () {
                    $.ajax({
                        url: "../sms-code",
                        type: "POST",
                        dataType: "json",
                        headers: {
                            "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                            "X-AD-CHECK": _this.do_encrypt(String.fromCharCode(Math.random() * 0x10000))
                        },
                        data: {mobile: defaultAccount ? undefined : _this.do_encrypt(mobile)},
                        success: function (data) {
                            if (data.success) {
                                layer.msg("短信发送成功，请注意查收！");
                                $pwd.next(".validate").remove();
                                _this.timejs();
                            } else {
                                layer.msg(data.msg);
                                _this.SendPwdFlag = true;
                            }
                        }
                    });
                });
            }
        },
        securityCheck: function () {
            return $.ajax({
                type: "get",
                url: "../check",//访问路径
                success: function (data) {
                    encrypt.setPublicKey(data);
                }
            });
        },
        timejs: function () {
            var _this = this;
            var num = 60 - _this.tnum;
            _this.tnum = _this.tnum + 1;
            $('.btn-getchkcode').attr("disabled", true);
            $('.btn-getchkcode').html('重发(' + num + 'S)');
            _this.flagtime = setTimeout(function () {
                _this.timejs();
            }, 1000);
            if (_this.tnum >= 60) {
                _this.stopjs();
                $('.btn-getchkcode').html("获取验证码");
                $('.btn-getchkcode').attr("disabled", false);
                _this.SendPwdFlag = true;
                _this.tnum = 0;
            }
        },
        stopjs: function () {
            var _this = this;
            clearTimeout(_this.flagtime);
        },
        //视频播放iframe地址拼装
        videoMediaPlay: function () {
            var _this = this;
            //var isnostart = false;
            var $iframe = $("#videoMedia");
            var midtype = $iframe.attr('data-midtype');
            //if (midtype == "vod") {
            //    if (replayUrl == "") {
            //        var nowtime = new Date();
            //        var courseBeginTime = new Date(Date.parse(_this.CourseBeginTime.replace(/[TZ]/ig, " ")));
            //        if (courseBeginTime > nowtime) {
            //            isnostart = true;
            //        }
            //        playUrl = noticeUrl;
            //    } else {
            //        playUrl = replayUrl;
            //    }
            //}
            //if (_this.initplay) {
            var ownerid = $("#hid_ownerid").attr("value");
            var uid = $("#hid_uid").attr("value");
            var pid = $("#hid_pid").attr("value");
            var appid = $("#hid_appid").attr("value");
            var uname = encodeURIComponent("videotool||" + uid || 'usrname');
            //var videoMediaSDK = window.location.protocol + "//" + window.location.host +"/Areas/ActShort20201208/Static/htmls/videoMediaSDK2.html";
            //var videoURL = videoMediaSDK + '?midtype=' + midtype + '&ownerid=' + ownerid + '&uid=' + uid + '&pid=' + pid + '&appid=' + appid + '&voteid=' + '' + '&uname=' + '' + '&authcode=888888' + '&ischeckpwd=0&nickName=' + uid + '&token=888888';
            var videoMediaSDK = "//static-dsclient.emoney.cn/livevideo/static/htmls/videoMediaSDK.html";
            var videoURL = videoMediaSDK + '?midtype=' + midtype + '&ownerid=' + ownerid + '&uid=' + uid + '&pid=' + pid + '&appid=' + appid + '&voteid=' + '' + '&uname=' + uname + '&authcode=888888' + '&ischeckpwd=0&r=' + Math.random();
            $iframe.attr('allowfullscreen', 'true');
            $iframe.attr('src', videoURL);

            $("#videoDetailBox").show();
            $("#classHasNoright").hide();

            //}

            //if (isnostart) {
            //    //未开始状态10秒钟自动检查课程是否已开始
            //    setTimeout(function () {
            //        var nowtime = new Date();
            //        var courseBeginTime = new Date(Date.parse(_this.CourseBeginTime.replace(/[TZ]/ig, " ")));
            //        if (nowtime > courseBeginTime) {
            //            location.reload();
            //        } else {
            //            _this.initplay = false;
            //            _this.videoMediaPlay();
            //        }
            //    }, 10000);
            //}
        },
        // 窗口缩放
        resetLiveContboxSize: function () {
            //点赞jsonp
            var _this = this;
            _this.reCountLiveCntSize();
            $(window).resize(function () {
                _this.reCountLiveCntSize();
            });
        },

        selectLoginPanel: function (tabName) {
            if (tabName == undefined) {
                return;
            }
            console.log("selectTab: " + tabName)
            var tabs = $("#tab-login > a");
            tabs.removeClass("on");
            tabs.filter("[data-tab-name='" + tabName + "']").addClass("on");
            var panels = $(".dl > .zc > [data-tab-name]");
            var selected = panels.filter("[data-tab-name='" + tabName + "']");
            if (selected.length) {
                panels.hide();
                selected.show();
            } else {
                console.error("tabName: " + tabName + "不存在")
            }
        },

        pageBindAction: function () {
            var _this = this;
            var $popViewBox = $("#popViewBox");

            //获取密码
            $(".btn-getchkcode").click(function () {
                _this.getOneTimePassword();
            });

            //登录窗口显示
            $("#dl,#divdl").click(function () {
                _this.securityCheck();
                $popViewBox.show();
            });

            //登录选择
            var loginTab = $("#tab-login");
            if (loginTab.children().length > 1) {
                loginTab.show();
            }
            loginTab.children().click(function () {
                _this.selectLoginPanel($(this).data("tab-name"));
            })

            $("[data-cancel]").click(function () {
                var $this = $(this);
                var filed = $this.data("cancel");
                var $filed = $this.closest("form")
                    .find("[name=" + filed + "]");
                if ($filed.length) {
                    $filed.val("");
                    $filed.removeAttr('readonly');
                    $this.remove()
                }
            })
            
            $("#user-login").submit(function () {
                _this.userLogin($(this));
                return false;
            })

            $("#sms-login").submit(function () {
                _this.smsLogin($(this));
                return false;
            })

            $("#code-check").submit(function () {
                if (_this.LoginFlag) {
                    var $code = $(this).find("input[name=code]");
                    if ($code.val() == "") {
                        layer.msg("请输入有效课程码");
                        $code.focus();
                        return false;
                    }
                    _this.LoginFlag = false;
                    $.post("../code-check", $(this).serialize(), function (data) {
                        if (data.success) {
                            location.reload();
                        } else {
                            if (data.code !== "705") {
                                var validate = $code.next(".validate");
                                if (validate.length === 0) {
                                    validate = $code.after("<span class='validate' style='color: red'></span>").next(".validate");
                                }
                                validate.text(data.msg);
                            }
                            layer.msg(data.msg);
                            _this.LoginFlag = true;
                        }
                    })
                }
                return false;
            })

            $("#mobile-check").submit(function () {
                if (_this.LoginFlag) {
                    var $mobile = $(this).find("input[name=mobile]");
                    if (!/^1[3-9]\d{9}$/.test($mobile.val())) {
                        layer.msg("请输入有效手机号");
                        $mobile.focus();
                        return false;
                    }
                    _this.LoginFlag = false;
                    var params = $(this).serializeArray();
                    $.when(_this.securityCheck()).done(function () {
                        params = $.param($.map(params, function (item) {
                            if (item.name === "mobile") {
                                item.value = _this.do_encrypt(item.value);
                            }
                            return item
                        }))
                        $.post("../mobile-check", params, function (data) {
                            if (data.success) {
                                location.reload();
                            } else {
                                layer.msg(data.msg);
                                if (data.code.startsWith("V")) {
                                    var validate = $mobile.next(".validate");
                                    if (validate.length === 0) {
                                        validate = $mobile.after("<span class='validate' style='color: red'></span>").next(".validate");
                                    }
                                    validate.text(data.msg);
                                }
                                _this.LoginFlag = true;
                            }
                        })
                    })
                }
                return false;
            })
            //退出
            $("#tc").click(function () {
                $.ajax({
                    url: "../loginOut",
                    type: "GET",
                    dataType: "json",
                    data: {},
                    success: function (data) {
                        if (data.success) {
                            location.reload();
                        } else {
                            layer.msg(data.msg);
                        }
                    }
                });
            });

            //登录窗口关闭
            $(".close").click(function () {
                $popViewBox.hide();
            });
            // 复制脚本
            $("#btn_copyUrl").click(function () {
                var userAgent = navigator.userAgent;
                var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1;
                if (!isIE) {
                    // $.ajax({
                    //     url: "./ToolCopyUrl",
                    //     type: "GET",
                    //     dataType: "json",
                    //     data: { copyurl: _this.do_encrypt(window.location.href) },
                    //     success: function (data) {
                    //         var _url = data.msg;
                    //         var clipboard = new ClipboardJS('#btn_copyUrl', {
                    //             text: function () {
                    //                 return _url;
                    //             }
                    //         });
                    //         clipboard.on('success', function (e) {
                    //             $(".ico5b").attr("class", "ico5c");
                    //             $("#fzlj").html("复制成功");
                    //         });
                    //
                    //         clipboard.on('error', function (e) {
                    //             $("#fzlj").html("复制失败");
                    //         });
                    //     }
                    // });
                    var _url = window.location.href;
                    var clipboard = new ClipboardJS('#btn_copyUrl', {
                        text: function () {
                            return _url;
                        }
                    });
                    clipboard.on('success', function (e) {
                        $(".ico5b").attr("class", "ico5c");
                        $("#fzlj").html("复制成功");
                    });

                    clipboard.on('error', function (e) {
                        $("#fzlj").html("复制失败");
                    });
                } else {
                    layer.open({
                        title: false
                        , content: '请到地址栏复制',
                        btn: false,
                        closeBtn: false,
                        time: 2000,
                        shade: false
                    });
                }
            });
            //一键分享QQ
            $("#btn_shareQQ").click(function () {
                var shareurl = window.location.href;
                var title = $(this).attr("data-title");
                var pics = $(this).attr("data-pics");
                var summary = $(this).attr("data-summary");
                var desc = "智盈实战直播—是益盟专为投资者打造的实战培训平台，专业型讲师教学，帮助投资者掌握有效战法，减少无效投资，提升获利能力。";
                var url = "https://connect.qq.com/widget/shareqq/index.html?url=" + shareurl
                    + "&sharesource=qzone&title=" + title
                    + "&pics=" + pics
                    + "&summary=" + summary
                    + "&desc=" + desc;
                window.open(url);
            });
            //    关闭mark层
            $(document).on('click', '.share-markbg', function () {
                $('.share-markbg').remove();
            })
        },

        reCountLiveCntSize: function () {
            var _this = this;
            var winH = $(window).height();
            var winW = $(window).width();
            //var videoBoxH = winH - 360;
            var u = navigator.userAgent;
            var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //g
            var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
            //$("#videoDetailBox").height(videoBoxH);
            //$(".iframe-box").height(videoBoxH);
            // $('.cover-img').height(videoDetailBoxH-52);
            //$("iframe").height(videoBoxH);

            //$("#mvMainBox .menu-box").height(videoBoxH - 38 + 40 + 50);
            //$("#menuScroll").height(videoBoxH - 70 + 40 + 50);
            //
            // //播放窗口自适应，但ie下拖动窗口不居中
            // // $(".art-cnt").width(winW-340-50);
            //
            // // 图文直播高度
            // $("#curLiveContent").height(videoBoxH + 40 - 38 - 36 + 50);
            // $("#curLiveContent .ive-nl-pack").height(
            //     videoBoxH + 40 - 38 - 36 - 45 + 50
            // );
            //if (winW > 500) {
            //    $('#videoDetailBox').height(winH-75);
            //    $('iframe').height(winH - 75);
            //    $('.video-detail-inner').height(winH - 75);
            //}

            if (!isAndroid && !isIOS) {
                if (winW > 500) {
                    var isliving = $("#mainPack").attr("data-isliving");
                    if (isliving == "1") {
                        $('.main-pack').addClass('reactive');
                    } else {
                        $('.main-pack').removeClass('reactive');
                    }
                } else {
                    $('.main-pack').removeClass('reactive');
                }
            }

        },
        // 直播窗口控制
        curliveboxBindevt: function () {
            var thispage = this;
            var $uptoextBtn = $("#uptoextBtn");
            var $yqqlivepack = $("#yqqlivebox .yqqlivepack");
            var $liveContent = $("#yqqlivebox .curlive-content");
            $uptoextBtn.on("click", "", function () {
                var $this = $(this);
                var winH = $(window).height();
                var liveBoxH = "";
                var liveCntScrollBox = "";
                var yqqlivepackH = $(window).height() - 314;
                var videoDetailBoxH = winH - 220 - 96;

                if ($this.hasClass("extend")) {
                    $this.removeClass("extend");
                    $(".icon-1", this).html("&#xe6b1;");
                    //   $(".celue-live-page .curlive-content").height(180 - 86);
                    // //  $("#liveCntScrollBox").height(180 - 86);
                    //   $("#yqqlivebox .yqqlivepack").css({
                    //       top: "0",
                    //       height: "100%"
                    //   });
                } else {
                    // $this.addClass("extend");
                    // $(".icon-1", this).html("&#xe6b2;");
                    // $(".celue-live-page .curlive-content").height(
                    //     180 - 86 + 280
                    // );
                    // $("#liveCntScrollBox").height(180 - 86 + 280);
                    //
                    // $("#yqqlivebox .yqqlivepack").css({
                    //     top: "-280px",
                    //     height: "460px"
                    // });
                }
            });
        },
        // 绑定交互
        bindEvents: function () {
            var _this = this;
            _this.pageBindAction();

            // 咨询助理
            $("#coursedetailBox").on("click", "#btn-IM", function () {
                window.utils.goIM("实战大直播-今日直播");
            });
        },

        formatTen: function (num) {
            return ('0' + num).slice(-2);
        },

        formatDate: function (date) {
            var _this = this;
            var year = date.getFullYear();
            var month = date.getMonth() + 1;
            var day = date.getDate();
            var hour = date.getHours();
            var minute = date.getMinutes();
            var second = date.getSeconds();
            return (
                year + "-" + _this.formatTen(month) + "-" + _this.formatTen(day)
            );
        },
        //rsa加密
        do_encrypt: function (str) {
            return encrypt.encrypt(str)
        }
    };
    page.init();
});
