@charset "UTF-8";

body {
    font-family: "Helvetica Neue",Helvetica,"Microsoft Yahei","PingFang SC","Hiragino Sans GB","WenQuanYi Micro Hei",sans-serif;
    font-size: 0.186667rem;
    font-size: 0.149333rem;
    font-size: 0.7rem;
    line-height: 1.42857143;
    color: #333;
    background-color: #ececeb;
    margin: 0;
}

ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

    ul li {
        list-style: none;
        margin: 0;
        padding: 0;
    }

a {
    color: inherit;
    text-decoration: none;
    cursor: pointer;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
}

[role=button],
area,
button,
input,
label,
select,
summary,
textarea {
    -ms-touch-action: manipulation;
    touch-action: manipulation;
}

html ::-webkit-scrollbar-track-piece {
    -webkit-border-radius: 0;
}

html ::-webkit-scrollbar {
    width: 0.08rem;
    height: 0.4rem;
}

html ::-webkit-scrollbar-thumb {
    height: 0.666667rem;
    background-color: #999;
    -webkit-border-radius: 0.053333rem;
    outline-offset: -0.026667rem;
    border: 0 solid #454545;
    min-height: 10em;
}

    html ::-webkit-scrollbar-thumb:hover {
        height: 0.666667rem;
        background-color: #888;
        -webkit-border-radius: 0.053333rem;
    }

.clearfloat {
    clear: both;
}

img {
    max-width: 100%;
    height: auto;
    vertical-align: middle;
}

table {
    border-collapse: collapse;
    max-width: 100%;
}

th {
    text-align: left;
}

input {
    outline: none;
}

.container {
    max-width: 16.266667rem;
    margin: 0 auto;
    position: relative;
}

.top-box {
    width: 100%;
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 0.2rem;
    padding: 0 0 0.133333rem 0;
}

    .top-box .emlogobox {
        height: 0.666667rem;
        background-image: url("../images/logo.png");
        background-repeat: no-repeat;
        background-position: 0.133333rem 70%;
        /*background-size:150px 40px;*/
    }

.login-btnline {
    line-height: 0.533333rem;
    position: absolute;
    top: 35%;
    right: 0.133333rem;
    font-size: 0.186667rem;
    text-align: right;
}

    .login-btnline .ico {
        width: 0.533333rem;
        height: 0.533333rem;
        overflow: hidden;
        background-color: #fff;
        -webkit-border-radius: 50%;
        border-radius: 50%;
        float: left;
        margin-right: 0.066667rem;
    }

    .login-btnline .quitbutton {
        background-color: red;
        color: #fff;
        -webkit-border-radius: 0.053333rem;
        border-radius: 0.053333rem;
        line-height: 1.8;
        padding: 0.1rem 0.15rem;
        margin-left: 0.1rem;
    }

    .login-btnline .nologin {
        background-color: red;
        color: #fff;
        -webkit-border-radius: 0.053333rem;
        border-radius: 0.053333rem;
        line-height: 1.8;
    }

        .login-btnline .nologin a {
            padding: 0 0.133333rem;
            color: #fff;
        }

        .login-btnline .nologin .icon-1 {
            font-size: 0.2rem;
            margin-right: 0.08rem;
        }

.fillet {
    -webkit-border-radius: 0.133333rem;
    border-radius: 0.133333rem;
    float: left;
    width: 100%;
}

.adver-banner {
    -webkit-border-radius: 0.133333rem;
    border-radius: 0.133333rem;
    float: left;
    width: 74%;
}

.rect-video {
    -webkit-border-radius: 0.133333rem;
    border-radius: 0.133333rem;
    float: left;
    width: 100%;
}

.p-r {
    position: relative;
}

.video {
    /*height: 92%;
    position: fixed;*/
    width: 100%;
    height: 7.466667rem;
    -webkit-border-radius: 0.133333rem 0.133333rem 0 0;
    border-radius: 0.133333rem 0.133333rem 0 0;
    overflow: hidden;
}

    .video .videoPack {
        width: 100%;
        height: 100%;
    }

        .video .videoPack > iframe {
            display: block;
            width: 100%;
            height: 100%;
        }

.video-alert {
    position: absolute;
    top: 25%;
    left: 0;
    width: 100%;
    text-align: center;
    color: #fff;
    font-size: 0.266667rem;
    background-color: #131313;
}

    .video-alert .alink-enter {
        padding: 0.133333rem 0.266667rem;
        line-height: 0.533333rem;
        margin: 0 0.173333rem;
        background-color: #ccc;
        text-align: center;
        line-height: 0.533333rem;
        color: #333;
        -webkit-border-radius: 0.666667rem;
        border-radius: 0.666667rem;
    }

        .video-alert .alink-enter:hover {
            background-color: #f33;
            color: #fff;
        }

    .video-alert .srk {
        width: 2.866667rem;
        height: 0.533333rem;
        border: 0.013333rem solid #ccc;
        -webkit-border-radius: 0.666667rem;
        border-radius: 0.666667rem;
        background: 0 0;
        text-align: center;
        line-height: 0.533333rem;
        color: #fff;
        font-size: 0.266667rem;
    }

        .video-alert .srk:hover {
            border: 0.013333rem solid #f33;
        }

    .video-alert .mt1 {
        margin-top: 0.333333rem;
    }

    .video-alert .mt2 {
        margin-top: 0.866667rem;
    }

    .video-alert .alink-new {
        border-bottom: 0.013333rem solid #fff;
        color: #f5f5f5;
    }

    .video-alert .alink-help {
        display: block;
        width: 2.666667rem;
        height: 0.533333rem;
        margin: 0.4rem auto 0 auto;
        background-color: #f33;
        text-align: center;
        line-height: 0.533333rem;
        color: #fff;
        -webkit-border-radius: 0.666667rem;
        border-radius: 0.666667rem;
        border: none;
    }

.vd-moreinfo {
    display: none;
}

.video-interactinfo {
    border-top: none;
    padding: 0.133333rem 0;
    background-color: #fff;
}

    .video-interactinfo .interactinfo {
        display: block;
    }

    .video-interactinfo .interact-btnbox {
        display: none;
    }

        .video-interactinfo .interact-btnbox .gotoAPPbtn {
            background-color: #f33;
            color: #fff;
            width: 94%;
            margin: 0 auto;
            font-size: 0.426667rem;
            text-align: center;
            line-height: 2.4;
            -webkit-border-radius: 0.106667rem;
            border-radius: 0.106667rem;
        }

    .video-interactinfo .btn-line {
        height: 0.48rem;
    }

        .video-interactinfo .btn-line .sbtn {
            border: 0.013333rem solid #e4e4e4;
            background-color: #f8f8f8;
            line-height: 2;
            padding: 0 0.16rem 0 0.413333rem;
            margin-right: 0.133333rem;
            float: left;
            color: #666;
            position: relative;
            -webkit-border-radius: 0.2rem;
            border-radius: 0.2rem;
            font-size: 0.186667rem;
        }

            .video-interactinfo .btn-line .sbtn > span > i {
                width: 0.24rem;
                height: 0.24rem;
                position: absolute;
                left: 0.133333rem;
                top: 0.08rem;
                font-size: 0.266667rem;
                line-height: 1;
                overflow: hidden;
            }

            .video-interactinfo .btn-line .sbtn.btn-share > span > i {
                background-position-y: -0.226667rem;
            }

            .video-interactinfo .btn-line .sbtn.btn-share > span:after {
                content: '分享';
            }

            .video-interactinfo .btn-line .sbtn.btn-favor > span > i {
                background-position-y: -0.453333rem;
            }

            .video-interactinfo .btn-line .sbtn.btn-favor > span:after {
                content: '收藏';
            }

            .video-interactinfo .btn-line .sbtn.btn-favor.favored > span > i {
                background-position-x: 0;
            }

            .video-interactinfo .btn-line .sbtn.btn-favor.favored > span:after {
                content: '已收藏';
            }

            .video-interactinfo .btn-line .sbtn.btn-like {
                cursor: pointer;
            }

                .video-interactinfo .btn-line .sbtn.btn-like > span > i {
                    background-position-y: -0.68rem;
                }

                .video-interactinfo .btn-line .sbtn.btn-like > span:after {
                    content: '点赞';
                }

                .video-interactinfo .btn-line .sbtn.btn-like.followed {
                    cursor: default;
                    background-color: #f33;
                    color: #fff;
                }

                    .video-interactinfo .btn-line .sbtn.btn-like.followed > span:after {
                        content: '赞';
                    }

            .video-interactinfo .btn-line .sbtn.favored:hover,
            .video-interactinfo .btn-line .sbtn:hover {
                background-color: #f33;
                color: #fff;
            }

                .video-interactinfo .btn-line .sbtn.favored:hover > span > i,
                .video-interactinfo .btn-line .sbtn:hover > span > i {
                    background-position-x: -0.213333rem;
                }

        .video-interactinfo .btn-line .on {
            background-color: #f33;
            color: #fff;
        }

        .video-interactinfo .btn-line .sbtn:hover .ico1,
        .video-interactinfo .btn-line .sbtn:hover .ico2,
        .video-interactinfo .btn-line .sbtn:hover .ico3,
        .video-interactinfo .btn-line .sbtn:hover .ico4 {
            background-position-x: -0.213333rem;
        }

        .video-interactinfo .btn-line .yc {
            padding-top: 0.133333rem;
            display: none;
        }

        .video-interactinfo .btn-line .p1 {
            position: absolute;
            left: -0.8rem;
            top: 0.4rem;
        }

        .video-interactinfo .btn-line .p2 {
            position: absolute;
            left: 0.04rem;
            top: 0.4rem;
        }

        .video-interactinfo .btn-line li .sjk {
            width: 2.666667rem;
            height: 2.8rem;
            position: relative;
            background-color: #fff;
            -webkit-box-shadow: 0 0 0.133333rem #333;
            box-shadow: 0 0 0.133333rem #333;
        }

            .video-interactinfo .btn-line li .sjk .ewm {
                width: 1.6rem;
                text-align: center;
                overflow: hidden;
                margin: 0.36rem 0 0 0.533333rem;
                color: #333;
                font-size: 0.173333rem;
                line-height: 0.226667rem;
            }

                .video-interactinfo .btn-line li .sjk .ewm img {
                    width: 1.733333rem 1.733333rem;
                    margin-bottom: 0.066667rem !important;
                }

            .video-interactinfo .btn-line li .sjk .red {
                color: #f33;
                font-size: 0.213333rem;
            }

        .video-interactinfo .btn-line li .fx {
            width: 3.6rem;
            height: 2.666667rem;
            position: relative;
            background-color: #fff;
            -webkit-box-shadow: 0 0 0.133333rem #333;
            box-shadow: 0 0 0.133333rem #333;
        }

        .video-interactinfo .btn-line li .line {
            height: 0.066667rem;
            overflow: hidden;
            background-color: #f33;
        }

        .video-interactinfo .btn-line li .jt {
            background-image: url(http://static.dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/ico4.png);
            width: 0.213333rem;
            height: 0.093333rem;
            position: absolute;
            left: 10%;
            top: -0.08rem;
            z-index: 9;
            overflow: hidden;
        }

        .video-interactinfo .btn-line li .fx .ewm {
            width: 1.6rem;
            text-align: center;
            overflow: hidden;
            margin: 0.36rem 0 0 0.4rem;
            color: #333;
            font-size: 0.173333rem;
        }

            .video-interactinfo .btn-line li .fx .ewm img {
                width: 1.733333rem 1.733333rem;
                margin-bottom: 0;
            }

        .video-interactinfo .btn-line li .fx .qq {
            position: absolute;
            left: 2.493333rem;
            top: 0.4rem;
            color: #333;
            font-size: 0.173333rem;
            width: 0.733333rem;
            text-align: center;
        }

        .video-interactinfo .btn-line li .fx .fz {
            position: absolute;
            left: 2.493333rem;
            top: 1.466667rem;
            color: #333;
            font-size: 0.173333rem;
            width: 0.733333rem;
            text-align: center;
        }

        .video-interactinfo .btn-line li .fx .ico5a {
            background-image: url(http://static.dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/ico5.png);
            width: 0.533333rem;
            height: 0.533333rem;
            margin: 0 auto;
        }

        .video-interactinfo .btn-line li .fx .ico5b {
            background: url(http://static.dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/ico5.png) 0 -0.533333rem no-repeat;
            width: 0.533333rem;
            height: 0.533333rem;
            margin: 0 auto;
        }

        .video-interactinfo .btn-line li .fx .ico5c {
            background: url(http://static.dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/ico5.png) 0 -1.066667rem no-repeat;
            width: 0.533333rem;
            height: 0.533333rem;
            margin: 0 auto;
        }

        .video-interactinfo .btn-line li:hover .yc {
            display: block;
        }

        .video-interactinfo .btn-line .ico1 {
            background-image: url(http://static.dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/ico.png);
            width: 0.213333rem;
            height: 0.226667rem;
            position: absolute;
            left: 0.133333rem;
            top: 0.08rem;
            overflow: hidden;
        }

        .video-interactinfo .btn-line .ico2 {
            background-image: url(http://static.dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/ico.png);
            background-position: 0 -0.226667rem;
            width: 0.213333rem;
            height: 0.226667rem;
            position: absolute;
            left: 0.133333rem;
            top: 0.08rem;
            overflow: hidden;
        }

        .video-interactinfo .btn-line .ico3 {
            background-image: url(http://static.dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/ico.png);
            background-position: 0 -0.453333rem;
            width: 0.213333rem;
            height: 0.226667rem;
            position: absolute;
            left: 0.133333rem;
            top: 0.08rem;
            overflow: hidden;
        }

        .video-interactinfo .btn-line .ysc {
            display: none;
            border: 0.013333rem solid #e4e4e4;
            background-color: #f33;
            color: #fff;
            line-height: 0.4rem;
            padding: 0 0.16rem 0 0.413333rem;
            margin-right: 0.133333rem;
            float: left;
            position: relative;
            -webkit-border-radius: 0.2rem;
            border-radius: 0.2rem;
        }

        .video-interactinfo .btn-line .ico3b {
            background-image: url(http://static.dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/ico.png);
            background-position: -0.213333rem -0.453333rem;
            width: 0.213333rem;
            height: 0.226667rem;
            position: absolute;
            left: 0.133333rem;
            top: 0.08rem;
            overflow: hidden;
        }

        .video-interactinfo .btn-line .ico4 {
            background-image: url(http://static.dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/ico.png);
            background-position: 0 -0.68rem;
            width: 0.213333rem;
            height: 0.226667rem;
            position: absolute;
            left: 0.133333rem;
            top: 0.08rem;
            overflow: hidden;
        }

.video-detail {
    background-color: #fff;
    height: auto !important;
    padding: 0 0.133333rem;
    -webkit-border-radius: 0 0 0.133333rem 0.133333rem;
    border-radius: 0 0 0.133333rem 0.133333rem;
    clear: both;
}

    .video-detail .abtn {
        display: block;
        cursor: pointer;
        line-height: 0.4rem;
        text-align: center;
        font-size: 0.186667rem;
        padding: 0 0.133333rem;
        float: right;
        background-color: #f33;
        color: #fff;
        -webkit-border-radius: 0.053333rem;
        border-radius: 0.053333rem;
    }

        .video-detail .abtn .icon-1 {
            font-size: 0.266667rem;
            vertical-align: middle;
        }

    .video-detail .vd-topic {
        overflow: hidden;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 400;
        margin-bottom: 0;
        height: 0.533333rem;
        line-height: 0.533333rem;
        font-size: 0.32rem;
    }

.video-detail-inner {
    height: 7.466667rem;
    position: relative;
    overflow: hidden;
    background-color: #131313;
}

.banner {
    height: 1.333333rem;
    overflow: hidden;
    margin-top: 0.2rem;
    -webkit-border-radius: 0.133333rem;
    border-radius: 0.133333rem;
}

    .banner img {
        width: 100%;
        margin: 0;
    }

.tab-pack {
    display: none;
}

.right-box {
    float: right;
    width: 4.026667rem;
}

.right-box-cont {
    border: 0.013333rem solid #e2e2e2;
    background-color: #f8f8f8;
    -webkit-border-radius: 0.133333rem;
    border-radius: 0.133333rem;
    overflow: hidden;
    display: none;
}

.ext-right-box {
    float: right;
    width: 25%;
}

.right-box-srk {
    padding: 0.266667rem;
}

    .right-box-srk .inp-box {
        position: relative;
    }

    .right-box-srk .right-box-input {
        width: 100%;
        height: 0.573333rem;
        font-size: 0.186667rem;
        line-height: 0.573333rem;
        border: 0.013333rem solid #e2e2e2;
        -webkit-border-radius: 0.066667rem 0 0 0.066667rem;
        border-radius: 0.066667rem 0 0 0.066667rem;
        padding: 0 0.133333rem;
    }

    .right-box-srk .right-box-ico {
        background: url(http://static.dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/ico3.png) center no-repeat;
        width: 0.533333rem;
        height: 0.533333rem;
        position: absolute;
        right: 0.8rem;
        top: 0.013333rem;
        background-color: #fff;
    }

        .right-box-srk .right-box-ico .icon-1 {
            font-size: 0.426667rem;
        }

    .right-box-srk .right-box-btn {
        width: 0.8rem;
        height: 0.573333rem;
        text-align: center;
        line-height: 0.6rem;
        background-color: #f33;
        color: #fff;
        font-size: 0.186667rem;
        position: absolute;
        top: 0;
        right: 0;
        list-style: none;
        border: 0;
        -webkit-border-top-right-radius: 0.066667rem;
        border-top-right-radius: 0.066667rem;
        -webkit-border-bottom-right-radius: 0.066667rem;
        border-bottom-right-radius: 0.066667rem;
    }

        .right-box-srk .right-box-btn:hover {
            color: #ff0;
        }

.right-box-phon {
    border: 0.013333rem solid #e2e2e2;
    height: 1.333333rem;
    background: #fff url(https://static-dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/phon.png) no-repeat center center;
    -webkit-background-size: 65% auto;
    background-size: 65% auto;
    -webkit-border-radius: 0.133333rem;
    border-radius: 0.133333rem;
    overflow: hidden;
    margin-top: 0.2rem;
    text-align: center;
    line-height: 1.266667rem;
}

.dh:hover {
    -webkit-animation: dh .3s linear infinite alternate;
    -webkit-animation-name: dh .3s linear infinite alternate;
    animation-name: dh .3s linear infinite alternate;
}

@-webkit-keyframes dh {
    to {
        -webkit-transform: scale(1.1);
        transform: scale(1.1);
    }
}

@keyframes dh {
    to {
        -webkit-transform: scale(1.1);
        transform: scale(1.1);
    }
}

.reactive .rect-video.p-r {
    width: 74%;
}

.reactive .right-box-cont .wdfy {
    border-bottom: 0.013333rem solid #e2e2e2;
    background-color: #fff;
    padding-top: 0.333333rem;
}

.reactive .right-box-cont .wdfy2 {
    border-left: 0.066667rem solid #f33;
    padding-left: 0.133333rem;
    height: 0.266667rem;
    line-height: 0.266667rem;
    margin: 0 0 0.24rem 0.4rem;
    color: #666;
    font-size: 0.186667rem;
    font-weight: 700;
}

.reactive .right-box-cont .qcnt-list {
    overflow-y: auto;
    font-size: 0.2rem;
    height: 508px;
}

    .reactive .right-box-cont .qcnt-list ul {
        padding: 0 0.2rem;
    }

    .reactive .right-box-cont .qcnt-list li {
        border-bottom: 0.013333rem solid #e2e2e2;
        padding: 0.2rem 0;
    }

        .reactive .right-box-cont .qcnt-list li:first-child {
            padding: 0 0 0.2rem 0;
        }

.reactive .right-box-cont .qa-quest {
    color: #ff5c5c;
    padding-bottom: 0.066667rem;
}

.reactive .right-box-cont .qa-reply {
    color: #bd8638;
    padding-bottom: 0.066667rem;
}

.reactive .right-box.tab-cont {
    width: 25%;
    margin-top: -8.213333rem;
}

    .reactive .right-box.tab-cont .right-box-cont {
        display: block;
    }

.reactive .video-detail.tab-cont {
    clear: none;
    float: left;
    width: 74%;
}

.pop-viewer {
    position: fixed;
    z-index: 9;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-image: url(https://static-dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/h.png);
    display: none;
}

.popwin {
    position: absolute;
    left: 50%;
    top: 20%;
    width: 7.066667rem;
    margin: 0 0 0 -3.533333rem;
    background-color: #fff;
    -webkit-border-radius: 0.133333rem;
    border-radius: 0.133333rem;
    overflow: hidden;
}

    .popwin .logo2 {
        background-color: #f6f6f6;
        background-image: url(https://static-dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/logo2.png);
        -webkit-background-size: 40% auto;
        background-size: 40% auto;
        background-repeat: no-repeat;
        background-position: center 30%;
        height: 0.933333rem;
        text-align: center;
        line-height: 0.96rem;
    }

    .popwin .dl {
        width: 70%;
        margin: 0 auto;
    }

        .popwin .dl .tab-login {
            font-size: 0.24rem;
            height: 0.533333rem;
            text-align: center;
        }

            .popwin .dl .tab-login a {
                width: 2.266667rem;
                display: inline-block;
                text-align: center;
                border-bottom: 0.013333rem solid #eaeaea;
                color: #9f9f9f;
                padding: 0.133333rem 0;
            }

                .popwin .dl .tab-login a.on {
                    color: #333;
                    border-bottom: 0.026667rem solid rgba(157, 157, 157, 0.12);
                }

        .popwin .dl .zc {
            margin-top: 0.4rem;
            font-size: 0.186667rem;
        }

            .popwin .dl .zc li {
                margin-bottom: 0.293333rem;
            }

            .popwin .dl .zc .inp-mobile {
                width: 65%;
                height: 0.573333rem;
                border: 0.013333rem solid #ccc;
                -webkit-border-radius: 0.066667rem;
                border-radius: 0.066667rem;
                background: transparent;
                line-height: 0.573333rem;
                color: #303030;
                font-size: 0.213333rem;
                padding: 0 0.2rem;
            }

            .popwin .dl .zc .inp-pwd,
            .popwin .dl .zc .inp-acount {
                width: 100%;
                height: 0.573333rem;
                border: 0.013333rem solid #ccc;
                -webkit-border-radius: 0.066667rem;
                border-radius: 0.066667rem;
                background: transparent;
                line-height: 0.573333rem;
                color: #303030;
                font-size: 0.213333rem;
                padding: 0 0.2rem;
            }

            .user-auth input {
                height: 0.573333rem;
                border: 0.013333rem solid #ccc;
                border-radius: 0.066667rem;
                line-height: 0.573333rem;
                font-size: 0.213333rem;
                padding: 0 0.2rem;
                box-sizing: border-box;
            }

            input.auth-input {
                margin-top: 30px;
            }

            .popwin .dl .zc .btn-getchkcode {
                display: block;
                background-color: #f33;
                text-align: center;
                width: 30%;
                line-height: 0.6rem;
                color: #fff;
                float: right;
                -webkit-border-radius: 0.066667rem;
                border-radius: 0.066667rem;
            }


                .popwin .dl .zc .btn-getchkcode[disabled] {
                    cursor: default;
                    display: block;
                    background-color: #888;
                    text-align: center;
                    width: 30%;
                    line-height: 0.6rem;
                    color: #fff;
                    float: right;
                    -webkit-border-radius: 0.066667rem;
                    border-radius: 0.066667rem;
                }

            .popwin .dl .zc .btn-login {
                display: block;
                background-color: #f33;
                text-align: center;
                width: 100%;
                line-height: 0.6rem;
                color: #fff;
                -webkit-border-radius: 0.066667rem;
                border-radius: 0.066667rem;
                border: none;
            }

        .popwin .dl .sjewm {
            width: 2.4rem;
            margin: 0.4rem auto 0;
            display: none;
            font-size: 0.186667rem;
            text-align: center;
        }

            .popwin .dl .sjewm img {
                width: 2.4rem;
                height: 2.4rem;
                margin-bottom: 0.133333rem;
            }

        .popwin .dl .inp-checkbox input {
            width: 0.266667rem;
            height: 0.266667rem;
            vertical-align: middle;
        }

    .popwin .close {
        width: 0.4rem;
        height: 0.4rem;
        background-image: url(https://static-dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/close.png);
        -webkit-background-size: 100% 100%;
        background-size: 100% 100%;
        position: absolute;
        top: 0;
        right: 0;
    }

    .popwin .login-tips {
        text-align: center;
        color: #666;
        padding-bottom: 0.266667rem;
    }

.fixtoplivecnt {
    padding: 0.066667rem;
    border-bottom: 0.013333rem dotted #ccc;
}

    .fixtoplivecnt .txtline {
        display: block;
    }

        .fixtoplivecnt .txtline .showfixtopcnt {
            display: inline;
            word-break: break-all;
            white-space: normal;
        }

            .fixtoplivecnt .txtline .showfixtopcnt p {
                margin: 0;
            }

            .fixtoplivecnt .txtline .showfixtopcnt img {
                max-height: 1.066667rem;
                *height: 1.066667rem;
            }

        .fixtoplivecnt .txtline .fixTopSign {
            border: 0.013333rem solid #900;
            background-color: #a32c31;
            color: #fff;
            padding: 0 0.066667rem;
            -webkit-border-radius: 0.053333rem;
            border-radius: 0.053333rem;
        }

        .fixtoplivecnt .txtline .livecnt-extbtn .ext {
            display: inline-block;
            *zoom: 1;
            *display: inline;
            color: #2c65a0;
            padding: 0 0.053333rem;
            cursor: pointer;
        }

        .fixtoplivecnt .txtline .livecnt-extbtn .closeup {
            display: none;
        }

        .fixtoplivecnt .txtline.extdown {
            height: auto;
            overflow: hidden;
            position: relative;
        }

            .fixtoplivecnt .txtline.extdown .livecnt-extbtn {
                bottom: 0;
                right: 0;
            }

                .fixtoplivecnt .txtline.extdown .livecnt-extbtn .ext {
                    display: none;
                }

                .fixtoplivecnt .txtline.extdown .livecnt-extbtn .closeup {
                    display: inline-block;
                    *zoom: 1;
                    *display: inline;
                    color: #2c65a0;
                    padding: 0 0.053333rem;
                    cursor: pointer;
                    background-color: #ebe9df;
                }

.footer {
    text-align: center;
    font-family: "宋体";
    font-size: 0.16rem;
    padding: 0.266667rem 0.4rem;
    margin-top: 0.266667rem;
    background-color: #fff;
}

@media screen and (max-width:500px) {

    .container {
        position: static;
    }

    .top-box {
        width: 100%;
        background-color: #fff;
        overflow: hidden;
        margin-bottom: 0.2rem;
        padding: 0 0 0.133333rem 0;
    }

        .top-box .emlogobox {
            height: 0.8rem;
            background-image: url("../images/logo.png");
            background-repeat: no-repeat;
            background-position: 0.133333rem center;
            background-size: 50% auto;
        }

    .login-btnline {
        line-height: 0.533333rem;
        position: absolute;
        top: 0.133333rem;
        right: 0.133333rem;
        font-size: 0.186667rem;
        text-align: right;
    }

        .login-btnline .ico {
            width: 0.533333rem;
            height: 0.533333rem;
            overflow: hidden;
            background-color: #fff;
            -webkit-border-radius: 50%;
            border-radius: 50%;
            float: left;
            margin-right: 0.066667rem;
        }

        .login-btnline .nologin .icon-1 {
            font-size: 0.373333rem;
            margin-right: 0.08rem;
        }

    .popwin .dl .zc .inp-acount:focus,
    .popwin .dl .zc .inp-pwd:focus {
        border-bottom: 1px solid #f00;
    }

    .rect-video.p-r {
        width: 100%;
        height:100%;
    }

        .rect-video.p-r .video {
            height: 56.84210526315789vw;
            width: 100%;
        }

            .rect-video.p-r .video .video-detail-inner {
                width: 100%;
                height: 100%;
            }

    .right-box-srk {
        background-color: #fff;
        position: fixed;
        width: 100%;
        bottom: 0;
        -webkit-box-shadow: -0.026667rem 0 0.133333rem rgba(0, 0, 0, .3);
        box-shadow: -0.026667rem 0 0.133333rem rgba(0, 0, 0, .3);
    }

        .right-box-srk .inp-box {
            position: relative;
        }

        .right-box-srk .right-box-input {
            width: 71%;
            height: 1.066667rem;
            font-size: 0.48rem;
            line-height: 1.066667rem;
            border: 0.026667rem solid #ddd;
            -webkit-border-radius: 0.133333rem;
            border-radius: 0.133333rem;
            padding: 0 0.266667rem;
        }

        .right-box-srk .right-box-ico {
            background: url(https://static-dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/ico3.png) center no-repeat;
            width: 1.066667rem;
            height: 1.013333rem;
            position: absolute;
            right: 1.6rem;
            top: 0.013333rem;
            background-color: #fff;
        }

            .right-box-srk .right-box-ico .icon-1 {
                font-size: 0.426667rem;
            }

        .right-box-srk .right-box-btn {
            width: 1.6rem;
            height: 1.066667rem;
            text-align: center;
            line-height: 1.066667rem;
            background-color: #f33;
            color: #fff;
            font-size: 0.373333rem;
            position: absolute;
            top: 0;
            right: 0;
            list-style: none;
            border: 0;
            -webkit-border-top-right-radius: 0.066667rem;
            border-top-right-radius: 0.066667rem;
            -webkit-border-bottom-right-radius: 0.066667rem;
            border-bottom-right-radius: 0.066667rem;
        }

            .right-box-srk .right-box-btn:hover {
                color: #ff0;
            }

    .video-detail {
        width: 100%;
        height:100%;
        background-color: #fff;
        height: auto;
        -webkit-border-radius: 0 0 0.133333rem 0.133333rem;
        border-radius: 0 0 0.133333rem 0.133333rem;
    }

        .video-detail .abtn {
            display: none;
        }

        .video-detail .vd-topic {
            background-color: #fff;
            padding: 0.133333rem;
            white-space: normal;
            word-break: break-all;
            height: auto;
            text-indent: 0.133333rem;
            line-height: 1.5;
            font-size: 0.425rem;
            font-weight: 700;
            padding: 0.325rem 0;
        }

            .video-detail .vd-topic > span {
                font-size: 324rem;
                font-weight: 400;
            }

    .vd-moreinfo {
        display: block;
    }
    .video-interactinfo {
        padding: 0.333333rem 0;
    }

        .video-interactinfo .interactinfo {
            display: none;
        }

        .video-interactinfo .interact-btnbox {
            display: block;
        }

    .ext-finfobox {
        display: none;
    }

    .tab-pack {
        display: block;
        height: 0.933333rem;
        width: 100%;
        clear: both;
        background-color: #fff;
        border-bottom: 0.013333rem solid #ddd;
    }

        .tab-pack ul {
            display: -webkit-box;
            display: -ms-flexbox;
            display: -webkit-flex;
            display: flex;
            width: 100%;
            -webkit-box-pack: center;
            -ms-flex-pack: center;
            -webkit-justify-content: center;
            justify-content: center;
            font-size: .4rem;
            line-height: 1rem;
        }

            .tab-pack ul li {
                -webkit-box-flex: 1;
                -ms-flex: 1;
                -webkit-flex: 1;
                flex: 1;
                text-align: center;
                cursor: default;
            }

                .tab-pack ul li span {
                    display: block;
                    width: 68%;
                    margin: 0 auto;
                    height: 0.92rem;
                    color: #666;
                    text-align: center;
                }

                .tab-pack ul li[data-target=baseinfo] span {
                    color: #f33;
                    border-bottom: 0.053333rem solid #f33;
                    font-weight: 700;
                }

                .tab-pack ul li[data-target=interactive] span {
                    color: #666;
                    border-bottom: 0.053333rem solid transparent;
                    font-weight: 400;
                }


    .reactive .tab-pack ul li[data-target=baseinfo] span {
        font-weight: 400;
    }

    .reactive .tab-pack ul li[data-target=interactive] span {
        font-weight: 700;
    }

    .video-alert {
        font-size: 0.426667rem;
    }

    .popwin {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        margin: 0;
        background-color: #fff;
        -webkit-border-radius: .133333rem;
        border-radius: .133333rem;
        overflow: hidden;
        bottom: 0;
    }

        .popwin .logo2 {
            background-color: #f6f6f6;
            background-image: url(https://static-dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/logo2.png);
            -webkit-background-size: 40% auto;
            background-size: 40% auto;
            background-repeat: no-repeat;
            background-position: center 40%;
            height: 1.333333rem;
            text-align: center;
            line-height: 0.96rem;
        }

        .popwin .dl {
            width: 90%;
            margin: 0.466667rem auto 0;
        }

            .popwin .dl .tab-login {
                font-size: 0.48rem;
                height: 0.933333rem;
            }

                .popwin .dl .tab-login a {
                    width: 80%;
                    margin: 0 auto;
                    display: block;
                    text-align: center;
                    border-bottom: 0.013333rem solid #f33;
                    color: #333;
                    padding-bottom: 0.133333rem;
                }

            .popwin .dl .zc {
                margin-top: 0.4rem;
                font-size: 0.32rem;
            }

                .popwin .dl .zc li {
                    margin-bottom: 0.293333rem;
                }

                .popwin .dl .zc .inp-acount {
                    width: 68%;
                    height: 1.23rem;
                    border: 0;
                    border-bottom: 0.013333rem solid #aaa;
                    -webkit-border-radius: 0.933333rem;
                    border-radius: 0;
                    background: 0 0;
                    line-height: 0.933333rem;
                    font-size: 0.5rem;
                    padding: 0 0.2rem;
                }

                .popwin .dl .zc .inp-pwd {
                    width: 100%;
                    height: 1.2rem;
                    border: 0;
                    border-bottom: 0.013333rem solid #aaa;
                    -webkit-border-radius: 0.933333rem;
                    border-radius: 0;
                    background: 0 0;
                    line-height: 0.933333rem;
                    font-size: 0.5rem;
                    padding: 0 0.2rem;
                }

                .popwin .dl .zc .btn-getchkcode {
                    display: block;
                    width: 30%;
                    font-size: 0.373333rem;
                    white-space: nowrap;
                    background-color: #f33;
                    text-align: center;
                    padding: 0 0.266667rem;
                    line-height: 0.933333rem;
                    color: #fff;
                    float: right;
                    -webkit-border-radius: 0.666667rem;
                    border-radius: 0.666667rem;
                }

                .popwin .dl .zc .login-tips {
                    text-align: center;
                    color: #666;
                }

                .popwin .dl .zc .inp-checkbox {
                    display: inline-block;
                    padding: 0.266667rem 0.133333rem;
                }

                    .popwin .dl .zc .inp-checkbox input {
                        width: 0.4rem;
                        height: 0.4rem;
                        vertical-align: middle;
                    }

            .popwin .dl .sjewm {
                width: 2.4rem;
                margin: 0.4rem auto 0;
                display: none;
                font-size: 0.186667rem;
                text-align: center;
            }

                .popwin .dl .sjewm img {
                    width: 2.4rem;
                    height: 2.4rem;
                    margin-bottom: 0.133333rem;
                }

        .popwin .close {
            width: 1.066667rem;
            height: 1.066667rem;
            background-image: url(https://static-dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/close.png);
            -webkit-background-size: 100% 100%;
            background-size: 100% 100%;
            position: absolute;
            top: 0;
            right: 0;
        }

    .right-box {
        width: 100%;
    }

    .reactive .right-box-cont {
        font-size: 0.48rem;
        border: 0.013333rem solid #e2e2e2;
        background-color: #f8f8f8;
        -webkit-border-radius: 0.133333rem;
        border-radius: 0.133333rem;
        overflow: hidden;
    }

        .reactive .right-box-cont .wdfy {
            border-bottom: 0.013333rem solid #e2e2e2;
            background-color: #fff;
            padding: 0.266667rem 0;
        }

        .reactive .right-box-cont .wdfy2 {
            border-left: 0.066667rem solid #f33;
            padding-left: 0.133333rem;
            margin: 0 0 0 0.2rem;
            color: #666;
            font-size: 0.4rem;
            font-weight: 700;
        }

        .reactive .right-box-cont .qcnt-list {
            font-size: 0.466667rem;
            overflow-y: auto;
            padding-right: 0.133333rem;
        }

            .reactive .right-box-cont .qcnt-list li {
                border-bottom: 0.013333rem solid #e2e2e2;
                padding: 0.2rem 0;
            }

                .reactive .right-box-cont .qcnt-list li:first-child {
                    padding: 0 0 0.2rem 0;
                }

        .reactive .right-box-cont .qa-quest {
            color: #ff5c5c;
            padding-bottom: 0.066667rem;
        }

        .reactive .right-box-cont .qa-reply {
            color: #bd8638;
            padding-bottom: 0.066667rem;
        }

    .reactive .rect-video.p-r {
        width: 100%;
    }

    .reactive .right-box.tab-cont {
        float: none;
        margin: 0;
        width: 100%;
        display: none;
    }

    .reactive .video-detail.tab-cont {
        width: 100%;
        display: block;
    }

    .reactive .right-box-cont {
        font-size: 0.373333rem;
    }

        .reactive .right-box-cont .qcnt-list {
            font-size: 0.373333rem;
        }

    .reactive.interactive .right-box.tab-cont {
        display: block;
    }

    .reactive.interactive .video-detail.tab-cont {
        display: none;
    }

    .reactive.interactive .tab-pack ul > li[data-target=baseinfo] span {
        color: #666;
        border-bottom: 0.053333rem solid transparent;
    }

    .reactive.interactive .tab-pack ul > li[data-target=interactive] span {
        color: #f33;
        border-bottom: 0.053333rem solid #f33;
    }

    .vd-moreinfo {
        display: block;
    }

    .footer {
        font-size: 0.325rem;
        line-height: 1.18;
    }
}

#qqFace,
.qqFace {
    display: none;
    bottom: 48px;
    right: 2px;
    border: 1px solid #ddd;
    max-width: 300px;
    max-width: 340px\9;
    left: unset !important;
    top: unset !important;
    background-color: #fff;
}

    #qqFace table td img,
    .qqFace table td img {
        border: 1px solid transparent;
    }

    #qqFace table td:hover img,
    .qqFace table td:hover img {
        border: 1px solid #28629e;
    }

div#facebox img {
    margin: 0;
}

.qqFace table td {
    padding: 6px 0;
}

.qqFace table {
    margin: 0;
}

/*直播内容*/


.qaShowbox .qaShowbox-p {
    color: #666;
    border-bottom: 1px dotted #bbb;
    padding-bottom: 6px;
}

    .qaShowbox .qaShowbox-p .qaQuestion {
        padding: 10px 0 0 0;
    }

        .qaShowbox .qaShowbox-p .qaQuestion .q-content {
            display: inline-block;
            *zoom: 1;
            *display: inline;
        }

        .qaShowbox .qaShowbox-p .qaQuestion .q-asker {
            color: #dd0000;
            display: inline-block;
            *zoom: 1;
            *display: inline;
        }

    .qaShowbox .qaShowbox-p .qaAnser {
        -webkit-border-radius: 5px;
        border-radius: 5px;
    }

        .qaShowbox .qaShowbox-p .qaAnser.section {
            margin-bottom: 5px;
        }

        .qaShowbox .qaShowbox-p .qaAnser .a-liveavatar {
            float: left;
            margin-left: -35px;
        }

            .qaShowbox .qaShowbox-p .qaAnser .a-liveavatar img {
                width: 30px;
            }

        .qaShowbox .qaShowbox-p .qaAnser .a-liveanser .color-blue {
            display: inline-block;
            *zoom: 1;
            *display: inline;
            color: #00a2ff;
            display: inline-block;
            *zoom: 1;
            *display: inline;
        }

        .qaShowbox .qaShowbox-p .qaAnser .a-liveanser .color-gray {
            display: inline-block;
            *zoom: 1;
            *display: inline;
            color: #c5c5c5;
            line-height: 1.2;
            font-size: 12px;
            display: inline-block;
            *zoom: 1;
            *display: inline;
        }

        .qaShowbox .qaShowbox-p .qaAnser .a-liveanser .a-content {
            display: inline-block;
            *zoom: 1;
            *display: inline;
        }

.liveowner_rq {
    line-height: 1.8;
    padding-bottom: 5px;
}

    .liveowner_rq .liveowner_rqt1 {
        color: #dd0000;
    }

    .liveowner_rq .liveowner_rqt2 {
        color: #dd0000;
    }

    .liveowner_rq .liveowner_rqt3 {
        color: #555;
    }

.liveowner_rat {
    line-height: 1.8;
    padding-top: 5px;
}

    .liveowner_rat .liveowner_rqt2 {
        color: #00a2ff;
    }

    .liveowner_rat .liveowner_rqt3 {
        color: #555;
    }

.share-markbg {
    position: fixed;
    z-index: 2147483647;
    background: rgba(0,0,0,0.5) url(https://static-dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/markbg.png) no-repeat;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: contain;
}
