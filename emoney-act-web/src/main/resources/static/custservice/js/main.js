$(function () {
    'use strict';
    var timer;
    var expireTime = 30000;
    var configData = {
        "actcode": $("#hid_actcode").val(),
        "uid":$("#hid_uid").val(),
        "mobilex":$("#hid_mobilex").val(),
        "transferurl": location.protocol  + "//" + location.host + "/activity/custservice/qrtransfer",
        "tipmsg":"微信扫码添加您的专属服务管家",
        "expiremsg":"二维码已失效,点击重新获取"
    }
    var thisPage = {
        Init:function (){
            if(!!configData.uid && !!configData.mobilex){
                thisPage.loadQrImg();
            }

            thisPage.bindEvent();
        },
        bindEvent:function (){
            $(".xz").click(function (){
                thisPage.loadQrImg();
            });
        },
        loadQrImg:function (){
            $.ajax({
                type: "get",
                url: "getqrimgurl",//访问路径
                data: {
                    actcode: configData.actcode,
                    transferurl: configData.transferurl,
                    uid: configData.uid,
                    mobilex:configData.mobilex
                },
                contentType: 'application/json;charset=utf-8',//返回json结果
                success: function (data) {
                    if (data.code === '200') {
                        $("#qrimg").attr("src", data.data);
                        $(".b").hide();
                        $(".txt").html(configData.tipmsg);

                        clearTimeout(timer);
                        timer = setTimeout(function () {
                            thisPage.expireQRCode();
                        }, expireTime);
                    } else {
                        // layer.msg(data.msg);
                        // thisPage.expireQRCode();
                    }
                }
            });
        },
        expireQRCode:function (){
            $(".b").show();
            $(".txt").html(configData.expiremsg);
        }
    }
    thisPage.Init();
});