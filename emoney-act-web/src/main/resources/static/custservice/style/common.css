body, html {
	font-size: 22px;
	font-family: "微软雅黑";
	margin: 0;
	padding: 0;
	line-height: 20px;
	color: #595757;
	height: 100%;
	background-color:transparent;
}
img {
	border: 0
}
ul, li {
	margin: 0;
	padding: 0;
	list-style: none;
}
/*漂浮*/
.pf {background-image: url("../images/pf.png");width: 276px; height: 318px; position: relative;}
.pf .tx{width: 54px; height: 54px; border-radius: 5px; position: absolute; left: 19px; top: 24px;}
.pf .name{color: #6b2b01; font-size: 18px; position: absolute; top: 30px; left:80px;}
.pf .ico{background-image: url("../images/ico.png"); background-size: 100% 100%; font-size: 12px; margin: 0 0 0 5px;padding: 1px 5px;}
.pf .zqbh{font-size: 13px;}
.pf .ewm{background-image: url("../images/ico2.png");width: 149px; height: 149px; position: absolute; top: 100px; left: 59px;}
.pf .ewm img{width: 136px; height: 136px; margin: 7px 0 0 7px;}
.pf .txt{font-size: 18px; position: absolute; left: 6px; top: 261px;text-align: center; width: 260px;color: #6b2b01;}
.pf .txt1{
	font-size: 20px;
	position: absolute;
	left: 16%;
	top: 33%;
	text-align: center;
	width: 180px;
	color: #6b2b01;
	line-height: 45px;
}
.pf .txt2{
	font-size: 18px;
	position: absolute;
	left: -3%;
	top: 30%;
	text-align: center;
	width: 286px;
	color: #6b2b01;
	line-height: 45px;
}
.pf .b{background-image: url("../images/b.png");width: 149px; height: 149px; position:absolute; top: 0px; left: 0px; border-radius: 10px;}
.pf .xz{background-image: url("../images/xz.png");width:65px; height:66px; position:absolute;top: 40px; left: 44px;cursor:pointer}
.pf .close{background-image: url("../images/close.png");width:27px; height:27px; position:absolute;top: -10px; right: -10px;}
#animation{
-webkit-animation:rotateIn 2s .2s ease infinite;
-moz-animation:rotateIn 2s .2s ease infinite;}
@-webkit-keyframes rotateIn{
0%{-webkit-transform-origin:center center;
-webkit-transform:rotate(-200deg);
opacity:0}
50%{-webkit-transform-origin:center center;
-webkit-transform:rotate(0);opacity:1}
100%{-webkit-transform-origin:center center;
-webkit-transform:rotate(200deg);opacity:0}
}
@-moz-keyframes rotateIn{
0%{-moz-transform-origin:center center;
-moz-transform:rotate(-200deg);
opacity:0}
50%{-moz-transform-origin:center center;
-moz-transform:rotate(0);
opacity:1}
100%{-moz-transform-origin:center center;
-moz-transform:rotate(200deg);
opacity:0}
}

/*页面*/
.bg {background-image: url("../images/bg.png");width:750px; height:1334px; position: relative; margin: 0 auto;}
.bg img{position: absolute; left: 210px;  top: 293px;width: 330px; height: 330px;}

