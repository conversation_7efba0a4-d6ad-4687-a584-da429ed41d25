@charset "utf-8";
/* CSS Document */
*{margin:0; padding:0;}
body{background-color:#C8212B;font-family: "微软雅黑";}
A:link {COLOR: #fff; TEXT-DECORATION: none;}
A:visited {COLOR: #fff; TEXT-DECORATION: underline}
A:hover{COLOR:#ffff00; TEXT-DECORATION: underline}
A.b:link {COLOR: #333; TEXT-DECORATION: underline}
A.b:visited {COLOR: #333; TEXT-DECORATION: underline}
A.b:hover {COLOR: #000; TEXT-DECORATION: none}
.img_1{background: url("../images/index_01.png") center no-repeat; height: 249px;}
.img_2{background: url("../images/index_02.png") center no-repeat; height: 249px;}
.img_3{background: url("../images/index_03.png") center no-repeat; height: 1240px;}
.img_4{background: url("../images/index_04.png") center no-repeat; height: 456px;}
.img_5{background: url("../images/index_05.png") center no-repeat; height: 478px;}
.img_6{background: url("../images/index_06.png") center no-repeat; height: 377px;}
.img_7{background: url("../images/index_07.png") center no-repeat; height: 595px;}
.img_8{background: url("../images/index_08.png") center no-repeat; height: 722px;}
.img_9{background: url("../images/index_09.png") center no-repeat; height: 545px;}

.main{width:100px; margin:0 auto; position:relative;}

.bg1{
	background: url("../images/bg1.png");
	width: 458px;
	height: 429px;
	position: absolute;
	right: -397px;
	top: 268px;
}
.bg1h{
	background: url("../images/bg1h.png");
	width: 458px;
	height: 429px;
	position: absolute;
	right: -397px;
	top: 268px;
}


.btn1{
	background: url("../images/btn1.png");
	width: 399px;
	height: 54px;display: block; margin: 50px auto;

}

.btn2{
	background: url("../images/btn2.png");
	width: 304px;
	height: 77px;
}
.btn3{
	background: url("../images/btn3.png");
	width: 484px;
	height: 111px;
	position: absolute;
	top: 737px;
	left: -206px
}
.btn3h{
	background: url("../images/btn3h.png");
	width: 484px;
	height: 111px;
	position: absolute;
	top: 737px;
	left: -206px
}
.an3{
	position: absolute;
	top: 4px;
	left: 282px;
}
.an4{
	position: absolute;
	top: 65px;
	left: 291px;
}
.an1{
	position: absolute;
	left: 297px;
	top: 72px;
	width: 282px;
	font-size: 14px;
	text-align: center;
	color: #fff;
}
.an2{
	position: absolute;
	left: 323px;
	top: 137px;
	width: 250px;
	font-size: 14px;
	text-align: center;
}
.an5{
	position: absolute;
	left: 206px;
	top: 387px;
	width: 250px;
	font-size: 14px;
	text-align: center;
}


.pf{background:url("../images/pf.png") center no-repeat; height:180px; width: 100%; position: fixed; left: 0; bottom: 0;}
.pfb{background:url("../images/pf2.png") center no-repeat; height:180px; width: 100%; position: fixed; left: 0; bottom: 0;}


.bod .dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}


.bg{background-image:url(../images/h.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.h{background: url("../images/h.png"); width: 100%; height: 100%; position: fixed; left: 0px; top: 0px; display: none;}
.xy{width: 600px; height: 600px; position: absolute; left: 50%; top: 50%; margin: -300px 0 0 -300px; background-color: #fff; border-radius: 10px;}
.xynr{height: 540px; overflow-y: auto; color: #000; font-size: 16px; padding: 0 20px 0 30px; margin: 30px 0; line-height: 28px;}
.xynr2{color: #000; font-size: 16px; padding: 0 20px 0 30px; margin: 30px 0; line-height: 28px;}
.f30{font-size: 30px; font-weight: bold; padding: 20px 0 20px; text-align: center;}
.close{background: url("../images/close.png"); width: 40px; height:41px; position: absolute; right: -40px; top: -40px;}
.hdgz{width: 700px; height: 470px; position: absolute; left: 50%; top: 50%; margin: -200px 0 0 -300px; background-color: #fff; border-radius: 10px;}

.tc3{
	position: fixed;
	top: 50%;
	left: 50%;margin: -325px 0 0 -323px;}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin: 20px 0 180px;
}
