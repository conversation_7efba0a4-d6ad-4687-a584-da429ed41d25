@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#A9211E;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #e42c34; font-weight: bold;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center; height:223px;}
.img_2{background:url(../images/index_02.png) center; height:224px;}
.img_3{background:url(../images/index_03.png) center; height:223px;}
.img_4{background:url(../images/index_04.png) center; height:1440px;}
.img_5{background:url(../images/index_05.png) center; height:725px;}
.img_6{background:url(../images/index_06.png) center; height:734px;}
.img_7{background:url(../images/index_07.png) center; height:737px;}
.img_8{background:url(../images/index_08.png) center; height:737px;}
.img_9{background:url(../images/index_09.png) center; height:739px;}
.img_10{background:url(../images/index_10.png) center; height:727px;}
.img_11{background:url(../images/index_11.png) center; height:685px;}


.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:20px 0 156px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}
.black{color: #000; font-weight: bold;}

.bott{background:url("../images/pf.png") center;height:124px; background-color: #FFF0DE; position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}


.btn1{
	background: url("../images/btn1.png");
	width: 275px;
	height: 89px;
}
.an1{position: absolute;
	top: 243px;
	left: -334px;}
.an2{
	position: absolute;
	top: 243px;
	left: 170px;
}
.an3{
	position: absolute;
	top: 64px;
left: 9px;
}
.an4{
	position: absolute;
	top: 1092px;
	left: 368px;
}
.btn2{
	background: url("../images/btn2.png");
	width: 484px;
	height: 112px; display: block; margin: 20px auto;
}
.btn3{
	background: url("../images/btn3.png");
	width: 106px;
	height: 49px;
}
.btn3h{
	background: url("../images/btn3h.png");
	width: 106px;
	height: 49px;
}
.btn4{
	position: absolute;
	top: -27px;
left: 113px;
background: url("../images/btn4.png");
width: 418px;
height: 147px;
}
.djs{
	position: absolute;
	left: 8px;
	font-size: 58px;
	width: 230px;
	line-height: 60px;
	color: #ed1d0e;
	top: 48px; font-weight: bold;letter-spacing:20px;
}


.sp{
	width: 823px;
	height: 561px;
	position: absolute;
	top: 112px;
	left: -355px;
}


.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%; margin: -343px 0 0 -457px;background: url("../images/tc.png");width:914px; height: 573px; color: #808080;padding: 215px 60px 0 60px;
font-size: 22px;
box-sizing: border-box;
line-height: 45px;
}

.tc5{
	position: fixed;
	top: 50%;
	left: 50%;margin: -300px 0 0 -348px;
}
.server{
    background: url("../images/server.png");
    width: 120px;
    height:120px;
    position:fixed;
    top: 330px;
    right: 0px;
}
.bg2{
    background: url("../images/bg2.png");
    width: 120px;
    height:120px;
    position:fixed;
    top: 200px;
    right: 0px;
}

.txt1{
	position: absolute;
	left: 84px;
top: 13px;
	width: 60px;
	color: #E82300;
	font-size: 22px;
}
.txt2{
	position: absolute;
	left: -359px;
	top: 164px;
	width: 870px;
}
.txt2 a{ float: left; width: 409px; height: 156px;}
.txt2 a:hover{background-position: 0 -156px;}
.b1{background: url("../images/b1.png") center top no-repeat}
.b2{background: url("../images/b2.png") center top no-repeat}
.b3{background: url("../images/b3.png") center top no-repeat}
.b4{background: url("../images/b4.png") center top no-repeat}
.b5{background: url("../images/b5.png") center top no-repeat}
.b6{background: url("../images/b6.png") center top no-repeat}


.dh2{
  -webkit-animation: dh2 2s linear infinite alternate;
  animation-name: dh2 2s linear infinite alternate;
}
@-webkit-keyframes dh2{
0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
40%{-webkit-transform:translateY(-30px)}
60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes dh2{
0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
40%{-moz-transform:translateY(-30px)}
60%{-moz-transform:translateY(-15px)}
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}
