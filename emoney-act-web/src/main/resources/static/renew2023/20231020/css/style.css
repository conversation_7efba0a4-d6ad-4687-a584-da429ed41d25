@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#4196E7;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #df4020;}
.yellow{color: #fee82f;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center; height:546px;}
.img_2{background:url(../images/index_02.png) center; height:420px;}
.img_3{background:url(../images/index_03.png) center; height:916px;}
.img_4{background:url(../images/index_04.png) center; height:916px;}
.img_5{background:url(../images/index_05.png) center; height:1029px;}
.img2_1{background:url(../images/index2_01.png) center; height:530px;}
.img2_2{background:url(../images/index2_02.png) center; height:559px;}
.img2_3{background:url(../images/index2_03.png) center; height:660px;}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:20px 0;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}
.black{color: #000; font-weight: bold;}

.main{width:100px; margin:0 auto; position:relative;}


.btn1{
	background: url("../images/btn1.png");
	width: 617px;
	height: 123px;
	border-radius: 61px;
	position: absolute;
	top: 122px;
	left: -261px; text-align: center; font-size: 56px; line-height: 123px; color: #fff;
}
.an1{
	position: absolute;
	top: 691px;
	left: -264px;
}
.an2{
	position: absolute;
	top: 243px;
	left: 170px;
}
.an3{
	position: absolute;
	top: 64px;
left: 9px;
}
.an4{
	position: absolute;
	top: 1092px;
	left: 368px;
}
.btn2{
	background: url("../images/btn1.png");
	width: 617px;
	height: 123px;text-align: center; font-size: 56px; line-height: 123px; color: #fff;border-radius: 48px;
}
.btn3{
	background: url("../images/btn1.png");
	background-size: 100% 100%;
	width: 150px;
	height: 45px;
	border-radius: 22px;
	position: absolute;
	top: 331px;
	left: 351px;
	text-align: center;
	font-size: 24px;
	line-height: 45px;
	color: #fff;
}
.btn3,.btn3h{
	background: url("../images/btn1.png");
	background-size: 100% 100%;
	width: 150px;
	height: 45px;
	border-radius: 22px;
	position: absolute;
	top: 331px;
	left: 351px;
	text-align: center;
	font-size: 24px;
	line-height: 45px;
	color: #fff;
}
.btn3h{
	background: url("../images/btn1h.png");}
.ico{
	background: url("../images/ico.png");
	width: 277px;
	height: 80px;
	position: absolute;
	top: -31px;
	left: 431px;
	text-align: center;
	font-size: 30px;
	line-height: 63px;
	color: #6b3b14;
}
.ico2{
	background: url("../images/ico2.png");
	width: 395px;
	height: 15px;
	position: absolute;
	top: 372px;
	left: -413px;
}
.ico3{
	background: url("../images/ico3.png");
	width: 1%;
	height: 15px;
	position: absolute;
	top: 0px;
	left: 0px; border-radius: 7px;
}
.nb{
	background-color: #D9EAFA;
	position: absolute;
	top: 157px;
	left: 256px;
	left: 256px;
	width: 185px;
	height: 62px;
	line-height: 62px;
	text-align: center;
	color: #4196e7;
	font-size: 46px;
	border-radius: 4px;
	overflow: hidden;
}
.hdgz{    width: 1020px;
	margin: 15px auto;
	font-size: 30px;
	color: #d5ebff;
	line-height: 50px;
	padding-left: 100px;}
.hdgz li{ list-style: decimal}
.txt2{
	width: 51px;
	position: absolute;
	left: -13px;
	top: 364px;
	color: #3380df;
	font-size: 22px;
}
.txt3{
	width: 705px; height: 320px; background-color: #DAEDFF; overflow-y: auto;
	position: absolute;
	left: -339px;
  top: 103px;
	color: #4497e7;
	font-size: 36px; border: 40px solid #DAEDFF;
}
.txt4{
	width: 426px;
	position: absolute;
	left: -415px;
	top: 398px;
	color: #8b8b8b;
	font-size: 18px;
}
.txt5{
	width: 458px;
	position: absolute;
	left: 94px;
	top: 385px;
	color: #72808d;
	font-size: 22px;
}
.txt3 li{margin-bottom: 20px;}

.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

.tc4{
	position: fixed;
	top: 50%;
	left: 50%; margin: -321px 0 0 -295px;background: url("../images/tc1.png");width:591px; height:642px;
}
.tc4 .btn{background: url("../images/tc_btn1.png");width:376px; height:115px; position: absolute;left: 106px; top: 492px;}
.tc4 .txt{width: 136px;
  text-align: center;
  position: absolute;
  left: 124px;
  top: 101px;
  color: #EE5030;
  font-size: 36px;}
.tc5{
	position: fixed;
	top: 50%;
	left: 50%; margin: -310px 0 0 -295px;background: url("../images/tc2.png");width:591px; height:621px;
}
.tc5 .btn{background: url("../images/tc_btn2.png");width:398px; height:151px; position: absolute;left: 107px;
  top: 450px;}


.dh2{
  -webkit-animation: dh2 2s linear infinite alternate;
  animation-name: dh2 2s linear infinite alternate;
}
@-webkit-keyframes dh2{
0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
40%{-webkit-transform:translateY(-30px)}
60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes dh2{
0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
40%{-moz-transform:translateY(-30px)}
60%{-moz-transform:translateY(-15px)}
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}
