$(function () {
    'use strict';
    var isLogin = $("#hid_isLogin").val();
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val(),
    }
    var testHost = 'testact.emoney.cn';
    var counterUrl = location.host === testHost ? "https://mobiletest.emoney.cn/emapp/" : "https://emapp.emoney.cn/";
    var apiUrl = location.host === testHost ? "https://preempc.emoney.cn/" : "https://empc.emoney.cn/";
    var isStudyPage = location.href.indexOf("index102002") > -1 ? true : false;
    var cmpCode = "ACRenew20231013";
    var actCode = "study20231008";
    var tipMsg = "";


    var thisPage = {
        init: function () {
            thisPage.bindEvents();

            if (isLogin === '1') {
                thisPage.getStudyInfo(loginUser.mobileX);
                thisPage.pushdatatocmp(loginUser.uid, cmpCode);
            } else {
                $(".bg").show();
                $(".tc").show();
            }
        },
        //绑定事件
        bindEvents: function () {
            //点击支付
            $('#studyCnt').on('click', ".btn2", function () {
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }

                if (!!tipMsg) {
                    layer.msg(tipMsg);
                    return false;
                }

                var key = $(this).attr("data-id");
                var url = $(this).attr("data-href");
                //计数
                thisPage.addStudyCount(key);

                //客户端
                if (!!thisPage.GetExternal()) {
                    thisPage.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "12," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }
            });

            $(".btn1").click(function () {
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
            });
            //去学习
            $("#btn_gotostudy,.tc4 .btn").click(function () {
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                if (!!tipMsg) {
                    layer.msg(tipMsg);
                    return false;
                }
                if ($(this).html() === '已学习') {
                    return false;
                }
                thisPage.addStudy();
                var contentId = $(this).attr("data-contentid");
                thisPage.goKanKan("https://kankan.emoney.cn/User/home/<USER>" + contentId + "&source=point&isview=1");
            });
            //关闭
            $(document).on('click', '.close', function () {
                $(".bg").hide();
                $(".tc4").hide();
                $(".tc5").hide();
            });
        },
        //获取挑战人数
        getStudyInfo: function (mobilex) {
            $.ajax({
                url: apiUrl + "Activity2023/StudyCheckIn/GetStudyInfo?Emapp-Format=EmappJsonp",
                timeout: 5000,
                type: 'get',
                dataType: 'jsonp',
                cache: false,
                data: {"phoneEncrypt": mobilex, "code": actCode},
                success: function (data) {
                    if (data.result.code === 0) {
                        var retObj = data.detail;
                        if (retObj) {
                            if (isStudyPage) {
                                //挑战学习页
                                if (retObj.studyConfigs) {
                                    thisPage.loadMyStudyConfig(retObj.studyConfigs, retObj.isComplete);
                                }

                                thisPage.popTips(retObj.orderInfo);

                                if (retObj.columnContent) {
                                    thisPage.loadColumnCnt(retObj.columnContent);
                                }
                                if (retObj.studyDate) {
                                    thisPage.loadStudyRecord(retObj.studyDate);
                                }
                            } else {
                                //支付页
                                if (retObj.studyConfigs) {
                                    thisPage.loadStudyConfigs(retObj.studyConfigs);
                                    thisPage.popTips_p1(retObj.orderInfo, retObj.isComplete);
                                }
                            }
                        }
                    }
                }
            });
        },
        //加载学习配置-支付页
        loadStudyConfigs: function (obj) {
            if (!obj) {
                return;
            }

            var html = "";
            var idList = "";
            $.each(obj, function (index, study) {
                var Id = study.id;
                var key = actCode + Id;
                var price = study.condition.price;
                var days = study.condition.addDays;
                var baseCount = study.condition.baseCount;
                var claId = index == 0 ? "3" : index == 1 ? "4" : "5";
                html += "<div class='img_" + claId + "' id='img" + claId + "'>" +
                    "    <div class='main'>" +
                    "        <div class='nb' data-id='" + key + "' data-basecount='" + baseCount + "'></div>" +
                    "        <a clickkey='btnpay_" + days + "' clickdata='" + loginUser.uid + "' data-href='" + thisPage.getPayUrl(study.displayIndex) + "' data-id='" + key + "' class='btn2 an1 dh'>" +
                    "            <span class='yellow'>" + price + "</span>元参加挑战" +
                    "            <div class='ico'>立得<span class='red2'>" + days + "</span>天智盈</div>" +
                    "        </a>" +
                    "    </div>" +
                    "</div>";
                idList += actCode + Id + ",";
            });

            $("#studyCnt").html(html);
            thisPage.loadStudyCount(idList);
        },
        //加载本期挑战人数-支付页
        loadStudyCount: function (keys) {
            $.ajax({
                url: counterUrl + "Statistics/Counter/GetMany?Emapp-Format=EmappJsonp",
                timeout: 5000,
                type: 'get',
                dataType: 'jsonp',
                cache: false,
                data: {"dataType": 7, "countType": 1, "keys": keys},
                success: function (data) {
                    if (data.detail) {
                        var result = data.detail;

                        var keyList = keys.split(",");
                        for (var i = 0; i < keyList.length; i++) {
                            var key = keyList[i];
                            var $spCount = $(".nb[data-id=" + key + "]");
                            var baseCount = parseInt($spCount.attr("data-basecount"));
                            $spCount.html(baseCount + result[key]);
                        }
                    }
                }
            });
        },
        //点击支付计数-支付页
        addStudyCount: function (key) {
            $.ajax({
                url: counterUrl + "/Statistics/Counter/Add?Emapp-Format=EmappJsonp",
                timeout: 5000,
                type: 'get',
                dataType: 'jsonp',
                cache: false,
                data: {"dataType": 7, "countType": 1, "key": key},
                success: function (data) {
                }
            });
        },
        //去学习-学习挑战页
        addStudy: function () {
            $.ajax({
                url: apiUrl + "Activity2023/StudyCheckIn/Study?Emapp-Format=EmappJsonp",
                timeout: 5000,
                type: 'get',
                dataType: 'jsonp',
                cache: false,
                data: {"comment": "今日学习挑战", "code": actCode, phoneEncrypt: loginUser.mobileX},
                success: function (data) {
                    if (data.result.code === 0) {
                        location.reload();
                        // $("#btn_gotostudy").removeClass("btn3").addClass("btn3h").html("已学习");
                        //
                        // $(".bg").hide();
                        // $(".tc4").hide();
                    }
                }
            });
        },
        //加载我的学习进度-学习挑战页
        loadMyStudyConfig: function (obj, isComplete) {
            if (!obj) {
                return;
            }

            $.each(obj, function (index, study) {
                if (study.isPay) {
                    var studyDays = study.condition.studyDays;
                    var addDays = study.addDays;

                    $("#mydays").html(studyDays);
                    $("#adddays").html(addDays + "天");

                    //挑战是否完成
                    if (isComplete) {
                        layer.msg("恭喜您挑战成功，" + addDays + "天智盈使用期已发放到您账户！");
                    }
                }
            });
        },
        //查询当日挑战成功人数-学习挑战页
        loadStudyCountByDay: function () {
            $.ajax({
                url: apiUrl + "Activity2023/StudyCheckIn/GetStudyCount?Emapp-Format=EmappJsonp",
                timeout: 5000,
                type: 'get',
                dataType: 'jsonp',
                cache: false,
                data: {"phoneEncrypt": loginUser.mobileX, "code": actCode},
                success: function (data) {
                    $(".tc4").find(".txt").html(data.detail + 3460);
                    $(".bg").show();
                    $(".tc4").show();
                }
            });
        },
        //加载购买订单信息判断是否有权限参与挑战-学习挑战页
        popTips: function (obj) {
            if (!obj) {
                //不可参与挑战
                $(".txt4").hide();
                $(".bg").show();
                $(".tc5").show();
            } else {
                var orderStatus = obj.orderStatusId;
                if (orderStatus == 30) {
                    //可挑战
                    $(".txt4").show();

                    //当日未学习 3秒后弹出窗口
                    setTimeout(function () {
                        if ($("#btn_gotostudy").html() === '去学习') {
                            thisPage.loadStudyCountByDay();
                        }
                    }, 3000);
                } else if (orderStatus == 20) {
                    if (obj.riskTestState == 0) {
                        tipMsg = "请先完成适当性流程，权限开通后即可参与挑战！";
                    } else {
                        tipMsg = "请在权限开通后参与挑战！";
                    }
                    layer.msg(tipMsg);
                    return;
                } else if (orderStatus == 40 || orderStatus == 201) {
                    tipMsg = "支付后退货，无法再次参与本活动！";
                    layer.msg(tipMsg);
                    return;
                }
            }
        },
        //页面提示-支付页
        popTips_p1: function (obj, isComplete) {
            if (isComplete) {
                tipMsg = "恭喜您挑战成功，奖励已发放到您账户！";
                layer.msg(tipMsg);
                return;
            }

            if (obj) {
                var orderStatus = obj.orderStatusId;
                if (orderStatus == 20) {
                    if (obj.riskTestState == 0) {
                        tipMsg = "您已支付完成，请先完成适当性，开通权限后请前往挑战页面参与挑战！";
                    } else {
                        tipMsg = "您已支付完成，请在权限开通后前往挑战页面参与挑战！";
                    }
                } else if (orderStatus == 30 && !isComplete) {
                    tipMsg = "您的挑战未完成，请前往挑战页面继续参与挑战！";
                } else if (orderStatus == 40 || orderStatus == 201) {
                    tipMsg = "支付后退货，无法再次参与本活动！";
                }
                layer.msg(tipMsg);
                return;
            }
        },
        //加载看看内容-学习挑战页
        loadColumnCnt: function (obj) {
            if (!obj) {
                return;
            }
            var contentId = obj.contentId;
            var kkTitle = obj.content.title;

            $(".btn3").attr("data-contentid", contentId);
            $(".txt5").html(kkTitle);
        },
        //加载挑战记录-学习挑战页
        loadStudyRecord: function (obj) {
            if (!obj) {
                return;
            }
            obj.sort(function (a, b) {
                return b - a;
            });
            var liHtml = "";
            $.each(obj, function (index, timesp) {
                var studyDate = thisPage.timestampToDate(timesp);
                var nowDate = thisPage.timestampToDate(new Date().getTime());
                if (nowDate === studyDate) {
                    //当日已学习
                    $("#btn_gotostudy").removeClass("btn3").addClass("btn3h").html("已学习");
                }
                liHtml += "<li>" + studyDate + "  已完成当日学习挑战！</li>";
            });
            $(".txt3 ul").html(liHtml);

            //我的学习进度
            var studyDays = obj.length;
            var mydays = $("#mydays").html();
            $(".txt2").html(studyDays + "天");
            $(".ico3").width((studyDays / mydays) * 100 + "%");
        },
        //获取支付链接
        getPayUrl: function (displayIndex) {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&businessType=biztyp-xzyxf";
            if(location.host === testHost){
                url = "https://mobiletest.emoney.cn/appstatic/ymstock/payment-middle/?goodsPid=888010000&businessType=biztyp-xzyxf";
            }
            switch (displayIndex) {
                case 1:
                    url += "&groupCode=6";
                    break
                case 2:
                    url += "&groupCode=5";
                    break
                case 3:
                    url += "&groupCode=7";
                    break
            }
            //url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
            return url;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return false;
            }
            return true;
        },
        //推送cmp
        pushdatatocmp: function (uname, adcode) {
            var data = {
                "appid": '10088',
                "logtype": 'click',
                "mid": '',
                "pid": thisPage.getQueryString("pid"),
                "sid": thisPage.getQueryString("sid"),
                "tid": thisPage.getQueryString("tid"),
                "uid": thisPage.getQueryString("uid"),
                "uname": uname,
                "adcode": adcode,
                "targeturl": "",
                "pageurl": window.top.location.href
            }
            var saasUrl = "https://ds.emoney.cn/saas/queuepush";
            var saasSrc = saasUrl + "?v=" + Math.random()
                + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
                + "&message=" + encodeURIComponent(JSON.stringify(data));

            var elm = document.createElement("img");
            elm.src = saasSrc;
            elm.style.display = "none";
            document.body.appendChild(elm);
        },//跳转看看
        goKanKan: function (url) {
            function GetExternal() {
                return window.external.EmObj;
            }

            //调用客户端接口
            function PC_JH(type, c) {
                try {
                    var obj = GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {
                }
            }

            var ret = PC_JH('EM_FUNC_OPEN_LIVE_VIDEO', '14,' + url);
            if (ret == "1") {
                return false;
            }
        },
        //时间戳toDate
        timestampToDate: function (timestamp) {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = date.getMonth() + 1; // getMonth() 返回的月份从 0 开始，所以需要加 1
            const day = date.getDate();

            return `${year}年${month}月${day}日`;
        },
        getQueryString: function (name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        },
        GetExternal: function () {
            return window.external.EmObj;
        },

        //调用客户端接口
        PC_JH: function (type, c) {
            try {
                var obj = thisPage.GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {
            }
        }
    }
    thisPage.init();
});