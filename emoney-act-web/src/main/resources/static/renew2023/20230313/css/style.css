@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#912328;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #e42c34; font-weight: bold;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.jpg) center; height:295px;}
.img_2{background:url(../images/index_02.jpg) center; height:296px;}
.img_3{background:url(../images/index_03.jpg) center; height:295px;}
.img_4{background:url(../images/index_04.png) center; height:692px;}
.img_5{background:url(../images/index_05.png) center; height:713px;}
.img_6{background:url(../images/index_06.png) center; height:719px;}
.img_7{background:url(../images/index_07.png) center; height:725px;}
.img_8{background:url(../images/index_08.png) center; height:723px;}
.img_9{background:url(../images/index_09.png) center; height:712px;}
.img_10{background:url(../images/index_10.png) center; height:842px;}


.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:0px 0 185px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}

.bott{height:165px;background:url(../images/index_11.png) center; position:fixed; bottom:0; width:100%;}
.bott2{height:165px;background:url(../images/index2_11.png) center; position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}


.btn1{
	background: url("../images/btn1.png");
	width: 134px;
	height: 134px;
}
.btn1h{
	background: url("../images/btn1h.png");
	width: 134px;
	height: 134px;
}
.an1{
	position: absolute;
	top: 42px;
	left: 372px;
}
.ico{
	background: url("../images/ico.png") no-repeat 14px;
	width: 351px;
	position: absolute;
	top: 209px;
	left: 59px;
	color: #7F4F21;
	padding-left: 33px;
}
.ico2{
	background: url("../images/ico2.png");
	width:384px;
	height: 84px;
	position: absolute;
	top: -37px;
left: -233px; line-height: 62px;
	color: #9E1F0A; text-align: center; font-size: 28px;
}
.an2{
	position: absolute;
	top: 243px;
	left: 170px;
}
.btn2{
	background: url("../images/btn2.png");
	width: 564px;
	height: 126px;
	position: absolute;
	top: 11px;
	left: -234px;
}
.btn3{
	background: url("../images/btn3.png");
	width: 161px;
	height: 55px;
	position: absolute;
	top: 488px;
left: 20px;
}
.btn4{
	position: absolute;
	top: 31px;
	left: 103px;
	background: url("../images/btn4.png");
	width: 413px;
	height: 118px;
}
.btn5{
	position: absolute;
	top: 416px;
left: 33px;
	background: url("../images/btn5.png");
	width: 429px;
	height: 112px;
}
.btn6{
	position: absolute;
	top: 416px;
left: 153px;
	background: url("../images/btn6.png");
	width: 429px;
	height: 112px;
}
.pf{
	position:fixed;
	top: 10%;
	right: 0px;
}


.sp{
	width: 823px;
	height: 463px;
	position: absolute;
	top: 125px;
	left: -355px;
}


.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.hdgz{
	position: absolute;
	top: 529px;
	left: -425px;
	width: 924px;
	color: #fff;
	font-size: 24px;
	line-height: 40px;
}

.tc5{
	position: fixed;
	top: 50%;
	left: 50%; width: 491px; height: 547px; background: url("../images/tc1.png");margin: -273px 0 0 -245px;
}
.tc6{
	position: fixed;
	top: 50%;
	left: 50%; width: 732px; height: 632px; background: url("../images/tc2.png");margin: -316px 0 0 -366px;
}
.server{
    background: url("../images/server.png");
    width: 118px;
    height: 97px;
    position:fixed;
    top: 20%;
    right: 0px;
}

.txt1{
	position: absolute;
	left: 132px;
top: 24px;
width: 200px;
color: #F8ECC8;
font-size: 26px;
text-align: right;
letter-spacing: 19px;
}
.txt2{
	position: absolute;
	left: -102px;
	top: 256px;
	width: 604px; color: #fff;
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}
