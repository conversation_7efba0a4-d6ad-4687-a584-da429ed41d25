$(function () {
    'use strict';
    var Urls = www;
    var isLogin = $("#hid_isLogin").val();
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val(),
    }
    var configData = {
        "actCode": $("#hid_actcode").val(),
        "isSendPP" :"renew20231127_issendpp",
        "cmpCode": "ACRenew20231201",
        "type":3
    }
    var isPage1 = location.href.indexOf("/index112701")>-1?true:false;
    configData.type = location.href.indexOf("/index112701")>-1?"3":"4"

    var thisPage = {
        init: function () {
            thisPage.bindEvents();
            thisPage.generateMarquee();

            if (isLogin === '1') {
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                thisPage.checkIsSendpp();
                thisPage.pushdatatocmp(loginUser.uid, configData.cmpCode);
            } else {
                $(".bg").show();
                $(".tc").show();
            }
        },
        //绑定事件
        bindEvents: function () {
            //点击支付
            $(".toPay").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                if(!$(".notips").prop("checked")){
                    layer.msg('请先阅读并同意该《信息服务协议》');
                    return false;
                }

                var url = thisPage.getPayUrl();

                //客户端
                if (!!thisPage.GetExternal()) {
                    thisPage.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }
            });
            $(".pf2").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                thisPage.sendpp();
            });
            $(".btn3").click(function () {
                $(".bg").show();
                $(".hdgz").show();
            });
            $(".ys").click(function (){
                $(".bg").show();
                $(".yhxy").show();
                $(".notips").prop('checked',true);
            });
            $(".txt,.notips").click(function (){
                $(".notips").prop('checked',true);
            });

            //关闭
            $(document).on('click', '.ruleClose', function () {
                if($(".tc3").css("display")==='block'){
                    $(".yhxy").hide();
                }else{
                    $(".bg").hide();
                    $(".yhxy").hide();
                }

            });
            //关闭
            $(document).on('click', '.close', function () {
                $(".bg").hide();
                $(".hdgz").hide();
                $(".tc3").hide();
            });
        },
        checkIsSendpp:function() {
            request(
                `${Urls}user/issubmitbyactcodes?actcodes=${configData.isSendPP}&uid=${loginUser.uid}`).then(res => {
                if (res.code === '200') {
                    var num = "";
                    if (res.data) {
                        num = res.data.split(',')[0]
                    }
                    if (!!num) {
                        thisPage.checkPopupShow();
                    } else {
                        //未领取过积分+优惠券
                        thisPage.sendpp();
                    }
                }
            })
        },
        /*
        赠送积分+优惠券
         */
        sendpp:function (){
            request(
                `${Urls}renew2023/sendpp20231127?actcode=${configData.actCode}&uid=${loginUser.uid}&pid=888010000`).
            then(res => {
                if (res.code === '200') {
                    //发送成功弹出窗口
                    $(".bg").show();
                    $(".tc3").show();
                    $(".ico8").show();
                }else if(res.code === '-3'){
                    //优惠券领取失败弹出浮层
                    $(".pf2").show();
                }
            })
        },
        /*
        查询当天是否弹出过
         */
        checkPopupShow:function () {
            request(
                `${Urls}lottery0808/gettipstatusbyday?actcode=${configData.actCode}&uid=${loginUser.uid}&type=${configData.type}`).
            then(res => {
                console.log(res)
                if (res.code === '200' && res.data) {
                    if (res.data === 'false') {
                        //弹出窗口
                        $(".bg").show();
                        $(".tc3").show();
                        $(".ico8").show();
                    }
                }
            })
        },
        generateMarquee:function () {
            var result = []
            var cnt = isPage1?"券后￥58续费1年智盈！":"券后￥128续费18个月智盈！";

            for (let i = 0; i < 10; i++) {
                var XXX = Math.floor(Math.random() * 1000) // 随机生成三位数的数字

                result.push(`恭喜！EMY${XXX.toString().padStart(3, '0')}****被福利砸中，${cnt}`)
            }
            $('marquee .list').html(result.join(' '))
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=0&businessType=biztyp-xzyxf";
            if(isPage1){
                url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=1&businessType=biztyp-xzyxf";
            }
            return url;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return false;
            }
            return true;
        },
        //推送cmp
        pushdatatocmp: function (uname, adcode) {
            var data = {
                "appid": '10088',
                "logtype": 'click',
                "mid": '',
                "pid": thisPage.getQueryString("pid"),
                "sid": thisPage.getQueryString("sid"),
                "tid": thisPage.getQueryString("tid"),
                "uid": thisPage.getQueryString("uid"),
                "uname": uname,
                "adcode": adcode,
                "targeturl": "",
                "pageurl": window.top.location.href
            }
            var saasUrl = "https://ds.emoney.cn/saas/queuepush";
            var saasSrc = saasUrl + "?v=" + Math.random()
                + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
                + "&message=" + encodeURIComponent(JSON.stringify(data));

            var elm = document.createElement("img");
            elm.src = saasSrc;
            elm.style.display = "none";
            document.body.appendChild(elm);
        },//跳转看看
        goKanKan: function (url) {
            function GetExternal() {
                return window.external.EmObj;
            }

            //调用客户端接口
            function PC_JH(type, c) {
                try {
                    var obj = GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {
                }
            }

            var ret = PC_JH('EM_FUNC_OPEN_LIVE_VIDEO', '14,' + url);
            if (ret == "1") {
                return false;
            }
        },
        //时间戳toDate
        timestampToDate: function (timestamp) {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = date.getMonth() + 1; // getMonth() 返回的月份从 0 开始，所以需要加 1
            const day = date.getDate();

            return `${year}年${month}月${day}日`;
        },
        getQueryString: function (name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        },
        GetExternal: function () {
            return window.external.EmObj;
        },

        //调用客户端接口
        PC_JH: function (type, c) {
            try {
                var obj = thisPage.GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {
            }
        }
    }
    thisPage.init();
});

function request (url, dataType) {
    return new Promise((resolve, reject) => {
        $.ajax({
            type: 'get',
            url: url,
            dataType: dataType ? dataType : 'jsonp',
            success: function (data) {
                console.log(data)
                if (data.code === '200') {
                    resolve(data)
                } else {
                    if (url.indexOf("sendpp") > -1) {
                    } else {
                        layer.msg(data.msg)
                    }
                }
            },
            error: function (err) {
                reject(err)
            }
        })
    })
}