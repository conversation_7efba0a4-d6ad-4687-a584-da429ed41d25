@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#7F17E6;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #e42c34; font-weight: bold;}
.white{color: #fff;}
.gray{color: #79440c;}
.org{color: #c54031;}
.f40{ font-size: 40px;}
.f60{ font-size: 60px;}
.f68{ font-size: 68px;}
.f1{text-decoration:underline;}
.img_1{background:url(../images/index_01.jpg) center; height:300px;}
.img_2{background:url(../images/index_02.jpg) center; height:439px;}
.img_3{background:url(../images/index_03.jpg) center top no-repeat; overflow: hidden; padding-top: 20px;}
.img_3 ul{width: 1010px; margin: 0 auto 30px; height: 60px;}
.img_3 a{float: left;background:url(../images/q1.png) center no-repeat; width: 163px;height: 55px; margin: 6px 5px 0 0; display: block;}
.img_3 a:hover{float: left;background:url("../images/q1h.png") center no-repeat; width: 163px;height: 55px; margin: 0px 5px 0 0;display: block; padding-top: 6px;}
.bg1{background:url(../images/bg1.png) center; width: 990px;height: 753px; margin: 0 auto 30px; overflow: hidden;}
.bg2{background:url(../images/bg2.png) center; width: 990px;height: 753px; margin: 0 auto 30px; overflow: hidden;}
.bg3{background:url(../images/bg3.png) center; width: 990px;height: 753px; margin: 0 auto 30px; overflow: hidden;}
.bg4{background:url(../images/bg4.png) center; width: 990px;height: 753px; margin: 0 auto 30px; overflow: hidden;}
.bg5{background:url(../images/bg5.png) center; width: 990px;height: 753px; margin: 0 auto 30px; overflow: hidden;}
.bg6{background:url(../images/bg6.png) center; width: 990px;height: 753px; margin: 0 auto 30px; overflow: hidden;}
.bg7{background:url("../images/bg7.png") center; width: 988px;height: 1158px; margin: 0 auto 90px; overflow: hidden;}
.bg8{background:url("../images/bg8.png") center; width: 978px;height: 40px; margin: 0 auto 30px; overflow: hidden;}

.ico1{background:url("../images/ico1.png") center; width: 30px;height: 30px; margin: 12px 0 0 7px; float: left;}
.ico2{background:url("../images/ico2.png") center; width: 30px;height: 30px; margin: 12px 0 0 7px; float: left;}
.ico3{background:url("../images/ico3.png") center; width: 30px;height: 30px; margin: 12px 0 0 7px; float: left;}
.ico4{background:url("../images/ico4.png") center; width: 30px;height: 30px; margin: 12px 0 0 7px; float: left;}
.ico5{background:url("../images/ico5.png") center; width: 30px;height: 30px; margin: 12px 0 0 7px; float: left;}
.ico6{background:url("../images/ico6.png") center; width: 30px;height: 30px; margin: 12px 0 0 7px; float: left;}
.t1{float: left; font-size: 16px; color: #fff; width: 110px; padding: 8px 0 0 8px;}
.t2{float: left; font-size: 12px; color: #fe8172; width: 110px; padding: 0px 0 0 8px;}
.img_3 a:hover .t1,.img_3 a:hover .t2{color: #e92f1a;}
.swf1{
width: 860px;
height: 510px;
  margin: 170px auto 45px; display: block;
}
.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:20px 0 214px;
}
A:link,a:visited {TEXT-DECORATION: none; color: #fff;}
A:hover {TEXT-DECORATION: none}
A.b:link,a.b:visited {TEXT-DECORATION: none; color: #333;}
A.b:hover {TEXT-DECORATION: none}
img{border:none;}
.black{color: #000; font-weight: bold;}

.bott{background:url("../images/pf.png") center;height:194px; position:fixed; bottom:0; width:100%;}
.bott2{background:url("../images/pf2.png") center;height:167px; position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}
.txt1{
	position: absolute;
	text-align: center;
	left: 78px;
	top: 8px;
	color: #fbde56;
	font-size: 40px;
	width: 440px;
	line-height: 60px;
}
.txt2{
	position: absolute;
	left: -446px;
	top: 35px;
	color: #f13472;
	font-size: 40px;
	line-height: 60px; font-weight: bold; text-align: center; width: 613px;
}
.txt3{
	position: absolute;
	left: 126px;
  top: 325px;
	color: #b68057;
	font-size: 24px; text-align: center; width: 420px;
}
.txt4{
	position: absolute;
	left: 126px;
  top: 376px; font-weight: bold;
	color: #eb0200;
	font-size: 28px; text-align: center; width: 420px;
}
.txt5{
	position: absolute;
	left: 146px;
	top: 323px;
	color: #480505;
	width: 143px;
	font-size: 16px;
	line-height: 26px;
	text-align: center;
	background-color: #E8AE5D;
}
.txt6{
	position: absolute;
	left: 316px;
	top: 323px;
	width: 135px;
	color: #480505;
	font-size: 16px;
	line-height: 26px;
	text-align: center;
	background-color: #E8AE5D;
}
.ico7{
	background: url("../images/ico7.png");
	width: 594px;
	height:55px;
	position: absolute;
	top: 116px;
	left: -446px;
	line-height: 55px; padding-left: 20px;
	color: #f9d548;
	font-size: 32px;
}
.an1{
	position: absolute;
	left: 175px;
	top: 288px;
	width: 283px;
	font-size: 16px;
}
.an2{
	position: absolute;
	left: 240px;
	top: 166px;
	width: 283px;
	font-size: 16px;
}
.an3{
	position: absolute;
	left: 210px;
  top: 643px;
	width: 283px;
	font-size: 16px;
}
.btn1{
	background: url("../images/btn1.png");
	width: 359px;
	height: 104px;
	position: absolute;
	top: 198px;
	left: 121px;
}
.ico8{
	background: url("../images/ico8.png");
	width: 178px;
	height: 43px;
	position: absolute;
	top: -22px;
	left: 241px;
}
.btn3{ font-size: 38px; text-align: center; color: #fff;
	width:395px; line-height: 105px;
	height:105px; display: block; margin: 40px auto; border: 2px solid #fff; border-radius:60px;}
.btn2{
	background: url("../images/btn2.png");
	width: 324px;
	height: 151px;
	position: absolute;
	top: 16px;
	left:210px;
}


.bg3{background:url("../images/bg3.png") no-repeat center top; width: 997px; border:3px solid #fff69e; padding:35px 0 60px 0; margin: 0 auto; border-radius: 15px;}
.yhxy{
	width: 900px;
	height:660px;
	position:absolute;
	left: 50%;
	top: 50%; margin: -350px 0 0 -450px; background-color: #fff; border-radius: 10px; padding: 20px 0 20px 20px; line-height: 24px; display: none;
}
.gmxz{
position: absolute;
left: 50%;
top: 50%;
margin: -294px 0 0 -423px;background: url("../images/gmxz.png"); width: 847px; height: 588px;
}
.nr{width: 880px;height:660px; overflow-y: auto; color:#626262; font-size: 16px; padding-right: 20px;}
.bg,.h{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%; display: none;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}
.mar{
	position: absolute;
	left: -252px;
	top: 386px;
	width: 771px;
	font-size: 18px;
	color: #fff;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

.hdgz{background-color: #fff;width: 505px;
border-radius: 10px;
position: absolute;
left: 50%;
top: 50%;
margin: -202px 0 0 -277px;
color: #000;
padding: 25px;
font-size: 16px;
line-height: 31px;}
.hdgz ul{padding-left: 20px;}
.hdgz li{list-style-type:decimal;}

.tc3{
	position: fixed;
	top: 50%;
	left: 50%;margin: -398px 0 0 -334px; display: none;
}
.tc-btn{background: url("../images/tc-btn.png");
    width:399px;
    height:113px; position: absolute;left: 136px;
  top: 528px;}

.pf2{
    background: url("../images/pf2.png");
    width: 170px;
    height:198px;
    position:fixed;
    top: 330px;
    right: 0px;
}
.server{
    background: url("../images/server.png");
    width: 120px;
    height:120px;
    position:fixed;
    top: 330px;
    right: 0px;
}



.dh2{
  -webkit-animation: dh2 2s linear infinite alternate;
  animation-name: dh2 2s linear infinite alternate;
}
@-webkit-keyframes dh2{
0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
40%{-webkit-transform:translateY(-30px)}
60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes dh2{
0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
40%{-moz-transform:translateY(-30px)}
60%{-moz-transform:translateY(-15px)}
}

.ruleClose,.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -25px;
	top: -25px;
	width: 30px;
	height: 30px;
}
