@charset "utf-8";
/* CSS Document */
*{margin:0; padding:0;}
body{background-color:#fee4d1;font-family: "微软雅黑";}
a{text-decoration: none;}

.fl{float: left;}
.fr{float: right;}
.clear{clear: both;}

.img_1{width: 100%; height:274px; background: url("images/img_01.png") center no-repeat;}
.img_2{width: 100%; height:452px; background: url("images/img_02.png") center no-repeat;}
.img_3{width: 100%; background: url("images/img_03.png") center top no-repeat}
.img_4{width: 100%; height:155px; background: url("images/img_05.png") center no-repeat; position: fixed; bottom: 0;}

.main{width:100px; margin:0 auto; position:relative;}
.box{width: 1050px; position:relative; margin: 0 auto;}
.box_1{width: 1050px; height: 72px; background: url("images/img2_01.png") center no-repeat;}
.box_2{width: 960px; height: 380px;  background: url("images/img2_02.png") center no-repeat; background-size: 100% 100%; padding-left: 90px; overflow: hidden;}
.box_3{width: 1050px; height: 216px; background: url("images/img2_03.png") center no-repeat;}
.box_3 a{color: #ED130F; text-align: center; text-decoration: none;}

.day_a {
	width: 	159px; height: 179px; text-align: center; margin: 0 0 20px 45px;background: url("images/bg_1a.png") no-repeat;
}
.day_b {
	width: 	159px; height: 179px; text-align: center; margin: 0 0 20px 45px;background: url("images/bg_1b.png") no-repeat;
}
.day_a .font1{height:33px; color: #FF5A44; padding-top: 8px; font-size: 21px;}
.day_b .font1{height:33px; color: #FFFFFF; padding-top: 8px; font-size: 21px;}
.font2 {
	font-size: 21px; color: #FFF5C0; line-height: 24px; height: 63px; padding-top: 15px;
}
.font2 span{line-height: 48px;}
.font2 .ss{font-size: 14px;}
.btn_1a{width: 159px; height: 49px; background: url("images/bgn_1a.png") no-repeat center; color: #603813; display: block; font-size: 18px; line-height: 42px; text-decoration: none;}
.btn_1b{width: 159px; height: 49px; background: url("images/bgn_1b.png") no-repeat center; color: #ffffff; display: block; font-size: 18px; line-height: 42px; text-decoration: none;}

.font3 {
	font-size: 21px; color: #FF5348;
}
.font3 span{font-size: 14px;}
.font4 {font-size: 18px; color: #FF5348;}

.icon {
	height: 95px; position: relative;
}

.lucy {
	position: absolute; background: url("images/bgn_2.png") no-repeat; top:75px; left: 35px; font-size:14px; color:  #FF5348; width: 90px; height: 24px; line-height: 22px;
}

.box_4 {
	width: 1050px; height: 304px; background: url("images/bg_3a.png") center no-repeat; margin-top: 40px; overflow: hidden;
}
.box_4 .ban{width:880px; height: 150px; background-color: #B0B0B0; margin: 120px auto 0;overflow: hidden;}

.box_5 {
	width: 835px;
	height: 358px;
	background: url("images/bg_3b.png") center no-repeat;
	margin-top: 40px;
	padding: 130px 0 0 235px;
}
.toptxt{
	position: absolute;
	left: -135px;
	top: 213px;
	font-size: 24px;
	color: #FF5348;
	font-weight: bold;
	width: 423px;
	line-height: 30px;
}

.box_5 ur {
	font-size: 24px;
	color: #FF5348;
	display: block;
	height: 316px;
	width: 739px;
	overflow: auto;
}
.box_5 ur li{height:54px; margin-top: 11px;  list-style: none; }
.btn_1c {
	width: 384px; height: 142px; margin: 40px auto 0 auto; display: block; background: url("images/bgn_2a.png") no-repeat;
}

.btn1 {
	font-size: 21px; text-align: center; width: 200px; height: 30px; margin-left:326px;
}
.btn1 a{text-decoration: none; color: #ED130F}
.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #666666; margin: 10px auto 200px auto;
}
.dh{
	-webkit-animation: dh 0.3s linear infinite alternate;
	animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}

@keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}

.bod{ width: 500px; height: 300px; overflow: hidden; background-color: #000;}
.btn3 {
	width: 300px; text-align: center; margin: 0 auto; line-height: 60px;}

.btn_2{
	width: 302px; height: 120px; background: url("images/bnt_2.png") no-repeat; position: absolute; top:10px; left: 200px; display: block;
}

.bg_4 {
	width: 197px; height: 63px; position: absolute; top:-40px; left: 350px;
}

.fc_1 {
	position: fixed; width: 144px; height: 144px; top:200px; right: 20px; background: url("images/kf.png") no-repeat; display: block;
}

.fc_2 {
	position: fixed; width: 144px; height: 144px; top:330px; right: 20px; background: url("images/kf2.png") no-repeat; display: block;
}



.bg{background: url("images/h.png"); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}

.tc_1 {         width: 444px; position: fixed;
	top: 50%;
	left: 50%;margin: -222px 0 0 -230px; }
.tc_2 {         width: 444px; position: fixed;
	top: 50%;
	left: 50%;margin: -222px 0 0 -230px; }
.tc_3 {         width: 444px;position: fixed;
	top: 50%;
	left: 50%;margin: -222px 0 0 -230px; }
.tc_4 {         width: 444px; position: fixed;
	top: 50%;
	left: 50%;margin: -222px 0 0 -230px; }
.tc_5 {         width: 444px; position: fixed;
	top: 50%;
	left: 50%;margin: -222px 0 0 -230px; }
.tc_6 {	 width: 444px; position: fixed;
	top: 50%;
	left: 50%;margin: -285px 0 0 -230px; }
.tc_7 {	 width: 447px; height: 540px; position: fixed;
	top: 50%;
	left: 50%;margin: -222px 0 0 -230px; }
.tc_7 .f1{
	position: absolute;
	text-align: center;
	top: 163px; left: 0px;
	font-size: 26px;
	color: #FFDFCB;
	width: 100%; z-index: 9; line-height: 50px;
}
.tc_7 .f2{font-size: 46px;}
.tc_8 {	 width: 444px; height: 578px; position: relative;position: fixed;
	top: 50%;
	left: 50%;margin: -222px 0 0 -296px; }
.tc_9 {	     width: 581px;
	position: fixed;
	top: 50%;
	left: 50%;
	margin: -385px 0 0 -230px;}
.h{background: url("images/h.png"); width: 100%; height: 100%; position: fixed; left: 0px; top: 0px; display: none;}
.close{background: url("images/close.png");width: 40px; height:41px; position: absolute; right: -40px; top: -40px;}
.hdgz{width:780px; height: 600px; position: absolute; left: 50%; top: 50%; margin: -300px 0 0 -390px; background-color: #fff; border-radius: 10px;}
.xynr2{color: #000; font-size: 16px; padding: 0 20px 0 30px; margin: 30px 0; line-height: 28px;}
.f30{font-size: 30px; font-weight: bold; padding: 20px 0 20px; text-align: center;}
.toptxt .f28{font-size: 30px; padding: 0 5px;}
.tc-btn{
	width: 250px;
	height: 53px;
	background: url("images/tc-btn.png") no-repeat; display: block; margin: 0 auto;
}