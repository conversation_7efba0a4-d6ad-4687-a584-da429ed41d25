@charset "utf-8";

img{ border:0px;}
body {
	font-size:12px; overflow-x:hidden;
	margin:0;
	padding:0;
	line-height:22px;
	color:#fff;
	background-color:#8C0A08;font-family:"微软雅黑";
}

.main{width:100px; margin:0 auto; position:relative;}
ul,li {margin:0;padding:0;list-style:none;}
A:link {COLOR: #fff; TEXT-DECORATION: underline;}
A:visited {COLOR: #fff; TEXT-DECORATION: none}
A:hover {COLOR:#ffff00; TEXT-DECORATION: none}
A.b:link {COLOR: #000; TEXT-DECORATION: underline;}
A.b:visited {COLOR: #000; TEXT-DECORATION: none}
A.b:hover {COLOR:#333; TEXT-DECORATION: none}
input{background:transparent;border:0px;margin:0px; color:#939598;list-style:none; font-family:"微软雅黑"; font-size:14px;}
.white {color:#ffffff;}
.red {color:#ed2c0e;}
.red2{color:#d30101;}
.green {color:green;}
.black{ color:#000;}
.blue{color:#549FD6;}
.yellow{color:#fceab8;}
.blue3{color:#255E98;}
.gray{color:#6A3906;}

.bg_h{background-color:#89001D; width: 996px; margin: 0 auto;}
.f_13{font-size:13px;}
.f_14{font-size:14px;}
.clr2{clear:both; height:1px; overflow:hidden;}
.m_t36{margin-top:36px;}
.m_t20{margin-top:20px;}
.m_t10{margin-top:10px;}
.t_c{text-align:center;}
.f_12{font-size:12px; font-family:"宋体";}
.f_16{font-size:16px;font-weight:bold;}
.f_17{font-size:17px;}
.f_26{font-size:26px; font-family:"微软雅黑"; line-height:50px;}
.f_30{font-size:28px; color:#603813; line-height:50px; font-weight:bold;}
.f20{font-size:20px; color: #fbcf9e; padding-top: 10px;}
.h340{height:340px;}
td{font-size:18px;text-align:center; background-color:#fff;}

.f-r{float:right;}
.f-l{float:left;}

.bod .dbg1{background:url("../images/index_01.jpg") center top;height:253px;}
.bod .dbg2{background:url("../images/index_02.jpg") center top;height:253px;}
.bod .dbg3{background:url("../images/index_03.jpg") center top;height:253px;}
.bod .dbg4{background:url(../images/index_04.jpg) center no-repeat;height:191px;}
.bod .dbg4b{background:url("../images/bg.png") center top; background-size: 1920px 100%; overflow: hidden;}
.bod .dbg5{background:url(../images/index_05.png) center top;width: 984px; height:591px; position: relative; margin: 0 auto;}
.bod .dbg6{background:url(../images/index_06.png) center top;width: 984px; height:591px; position: relative; margin: 0 auto;}
.bod .dbg7{background:url(../images/index_07.png) center top;width: 984px; height:591px; position: relative; margin: 0 auto;}
.bod .dbg8{background:url(../images/index_08.png) center top;width: 984px; height:591px; position: relative; margin: 0 auto;}
.bod .dbg9{background:url(../images/index_09.png) center top;width: 985px; height:569px; position: relative; margin: 0 auto;}
.bod .dbg10{background:url("../images/index_10.png") center top;width: 985px; height:530px; position: relative; margin: 0 auto;}
.bod .dbg11{background:url("../images/index_11.jpg") center top;height:468px;}

.bod #dbg8{height: 172px; background-color: #380403;}

.ico2{
	background: url("../images/ico2.png") no-repeat;
	width: 142px;
	height: 136px;
	position: absolute;
	left: -445px;
	top: 22px;
}
.ico3{
	background: url("../images/ico3.png") no-repeat;
	width: 39px;
	height: 49px;
	position: absolute;
	left: 482px;
	top: 141px; pointer-events: none;
}

.mar2{
	position: absolute;
	left: -211px;
	top: 453px;
	width: 714px;
	color: #7e4a2a;
	overflow: hidden;
	font-size: 16px;
}
.sp{
	position: absolute;
	left: -159px;
	top: 113px;
	width: 658px;
	height: 278px;
}
.p1{
	position: absolute;
	left: -431px;
	top: 163px;
}
.bg{
	width: 970px;
}
.bg .bt{
	color: #c30d23;
	font-size: 30px;
	text-align: center;
	padding-top: 25px;
	line-height: 30px;
	width: 210px;
	position: absolute;
	left: 57px;
	top: 0px;
	text-shadow: 1px 1px 1px #fff;
}
.bg .bt2{
	color: #fdf9cb;
	font-size: 26px;
	line-height: 30px;
	position: absolute;
	left: 312px;
	top: 13px;
}
.bg ul{float: left;width:281px; font-size: 20px;}
.bg a{ width:281px; height:109px;background:url("../images/bg1.png") top; display: block;
    text-decoration: none;
    overflow: hidden; margin-bottom: 10px;background-size: 100% 100%; color: #956134;
}
.f1{margin: 13px 0 0 0; padding: 0 0 10px 25px; font-size: 24px; line-height: 30px;background:url("../images/ico4.png") bottom center no-repeat;}
.bg a:hover .f1,.bg a.current .f1{margin: 13px 0 0 0; padding: 0 0 10px 25px; font-size: 24px; line-height: 30px;background:url("../images/ico4h.png") bottom center no-repeat;}
.bg .f3{margin: 5px 0 0 25px; font-size: 16px;}
.bg a:hover ,.bg a.current{
    background:url("../images/bg1h.png") top;
    color: #fff;
}
.swf1{
width: 680px;
height: 394px;
float: left;
margin: 0px 0 0 0px;
}
.swf2{
	width: 820px;
	height: 576px;
}



.pf{position: fixed; right: 0px; top: 20%; display: none}
.btn1{
	background: url(../images/btn1.png) no-repeat;
	width: 271px;
	height: 68px;
	position: absolute;
	left: 260px;
	top: 66px;
}
.btn2{
	background: url(../images/btn2.png);
	width: 639px;
	height: 160px; display: block; margin: 40px auto;
}
.btn3{
	background: url(../images/btn3.png);
	width: 218px;
	height: 27px;
	position: absolute;
	left: -429px;
	top: 429px;
}
.an1{
	position: absolute;
	width: 151px;
	height: 29px;
	left: 382px;
	top: 2px;
}
.ttt1{
	top: 560px;
}
.ttt2{
	top: 593px;
}
.ttt3{top: 545px;}

.nav{height: 77px; width: 867px;margin:0px auto 0 auto; padding-top: 110px;}
.nav a{background: url("../images/ico.png");
	width: 274px; text-align: center; font-size: 32px; color: #f2ddbd;TEXT-DECORATION: none;
	height: 57px; display: block; float: left; margin-right:20px; line-height: 55px;}
.nav a:last-child{margin-right: 0px;}
.nav a:hover ,.nav a.current{color: #956134;background: url("../images/icoh.png");}

.txt1{
	position: absolute;
	left: -397px;
	top: 323px;
	width: 926px;
	font-size: 16px;
	color: #606060;
}
.txt2{
	position: absolute;
	left: 81px;
	top: 26px;
	font-size: 16px;
	width: 200px;
	text-align: center;
}
.lb{font-size: 14px;
	color: #fff;
	font-family: "宋体"; width: 280px; line-height: 20px;}
.lb ul{height: 25px; padding-top: 5px;}
.lb li{float: left; padding: 0 7px; border: 1px solid #bc202c; background-color: #660208; margin-right: 2px; font-size: 12px; color: #e7c2ae;font-family: "宋体";}

.txt4{
	position: absolute;
	left: -384px;
	top: 130px;
	font-size: 16px;
	color: #C30D23!important;
	width: 305px;
}
.txt5{
	position: absolute;
	left: 49px;
	top: 11px;
	font-size: 16px;
	color: #3e3a39!important;
	width: 148px;
}
.txt6{
	position: absolute;
	left: 339px;
	top: 11px;
	font-size: 16px;
	color: #3e3a39!important;
	width: 149px;
}

.hdgz{background-color: #fff;width: 820px; height: 576px; padding: 10px;
border-radius: 5px;
position: absolute;
left: 50%;
top: 50%;
margin: -298px 0 0 -420px;}
.h,.h2{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/h.png); display: none;}
.tc2{background-image:url(../images/tc2.png); 
	width: 439px;
	height:321px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -160px 0 0 -219px; text-align: center; color: #fff;
}
.txt{width: 100%; position: absolute; top: 38%; font-size: 28px; line-height: 40px;}
.yellow{color: #faee00; font-size: 34px;}
.f16{font-size: 16px; margin-top: 5px; height: 40px;}
.f14{font-size: 14px;}
input{background:#fff;border:0px; margin-right: 5px; color:#000;list-style:none; font-size:30px; width: 40px; height: 40px; line-height: 40px; text-align: center;}
.tc-btn2{background-image:url("../images/tc-btn2.png"); width: 253px; height: 59px; display: block; margin: 12px auto 0px;}
.tc-btn3{background-image:url("../images/tc-btn3.png"); width: 253px; height: 59px; display: block; margin: 12px auto 5px;}
.close{
	background: url("../images/close.png") center top no-repeat;
	width: 25px;
	height: 25px;
	position: absolute;
	top: 5px;
	right: 5px;
}
.dh:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

.dh2{-webkit-animation: dh2 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh2{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
@keyframes dh2{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

#k{
	border: 2px solid #ffff00;
	width: 985px;
	height: 691px;
	position: absolute;
	left: -442px;
	top: -381px;
	opacity: 0;
	-webkit-animation: k 0.2s linear infinite alternate;
	animation: k 0.2s linear infinite alternate;display:none;
}
@-webkit-keyframes k {
 to {opacity: 1;
}
}
 @keyframes k {
 to {opacity: 1;
}
}
#div1{
	width: 820px;
	position: absolute;
	left: -264px;
	top: 14px;
	height: 144px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.footer{text-align:center; font-family:"宋体"; font-size:12px; padding:30px 0;}


.btn7{
	position: absolute;
	width: 340px;
	top: -113px;
	left: 174px;
	font-size: 20px;
}
.btn8{
	position: absolute;
	width: 168px;
	height: 118px;
	top: 13px;
	left: -188px;
}
.btn9{
	position: absolute;
	width: 168px;
	height: 118px;
	top: 14px;
	left: 17px;
}

@-webkit-keyframes wobble2 {
  0%,100% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  5% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  10% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  15% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  20% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  25% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  33% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }
}

@keyframes wobble2 {
  0%,100% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  5% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  10% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  15% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  20% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  25% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  33% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }
}

.wobble2 {
  -webkit-animation-name: wobble2;
          animation-name: wobble2;
		  -webkit-transform-origin: center bottom;
	-moz-transform-origin: center bottom;
	-ms-transform-origin: center bottom;
	-o-transform-origin: center bottom;
	transform-origin: center bottom;
	-webkit-animation-duration: 9s;
	-o-animation-duration: 9s;
	animation-duration: 9s;
	-webkit-animation-timing-function: ease-in;
	-o-animation-timing-function: ease-in;
	animation-timing-function: ease-in;
	-webkit-animation-delay: 0s;
	-o-animation-delay: 0s;
	animation-delay: 0s;
	-webkit-animation-iteration-count: infinite;
	-o-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	-webkit-animation-direction: normal;
	-o-animation-direction: normal;
	
	-webkit-animation-fill-mode: both;
	-o-animation-fill-mode: both;
	animation-fill-mode: both;
}

.djs{
	position: absolute;
	left: -190px;
	top: 1px;
	width: 488px;
	height: 70px;
	line-height: 36px;
	color: #b9201a;
	font-size: 24px;
	text-align: center;
	font-weight: bold;
	background-image: url("../images/djs.png"); display: none;
}
.djs .t{
	position: absolute;
	left: 171px;
	top: 14px;
	width: 57px;
}
.djs .s{
	position: absolute;
	left: 239px;
	top: 14px;
	width: 48px;
}
.djs .f{
	position: absolute;
	left: 302px;
	top: 14px;
	width: 48px;
}
.djs .m{
	position: absolute;
	left: 354px;
	top: 14px;
	width: 61px;
}
