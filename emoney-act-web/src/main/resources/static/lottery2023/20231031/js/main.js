var jsonMessage = location.search
var userInfo = {
  uid: '',
  pid: '',
  uname: '',
  maskname: '',
  realname: '',
  points: 0,
  token: '',
  totalCount: 0,
  channelcode:'',
  type:''
}
var Urls = www;

var recordList = []

// 当前的奖品
var currentReward = null

//活动码
var configData = {
  receiveCode: 'renew20231031',
  totalCode: 'renew20231031',
  hotCode: 'renew20231031_count',
  cmpCode: 'ACRenew20231101',
  isSendppCode: 'renew20231031_issendpp',
  baseCount: 5138
}
var hotCount = 0

$(document).ready(function () {
  init()

  setTimeout(function (){
    if($("#lotteryTips").text()==='1次免费抽奖待使用'){
      //无弹窗时 未抽奖则弹出抽奖窗口
      if ($(".mask:visible").length === 0){
        //五秒后未抽奖弹出抽奖窗口
        $("#zzPopup").show();
      }
    }
  },5000)

  $('.btn2').click(function () {
    $('#rulePopup').show()
  })
  $('#ruleClose').click(function () {
    $('#rulePopup').hide()
  })

  $('.toPay').click(function (e) {
    checkPermission(userInfo.pid)

    var payUrl = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=0&businessType=biztyp-xzyxf&ordersource=" + userInfo.channelcode;
    //客户端
    if (!!GetExternal()) {
      $('.toPay').removeAttr("target");
      PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + payUrl);
    } else {
      payUrl += "&phoneEncrypt=" + userInfo.uname + "&UPcid=" + userInfo.uid;
      //$('.toPay').attr("target", "_blank");
      $('.toPay').attr("href", payUrl);
    }
    //e.stopPropagation()
    //directToPay()
  })

  //直接跳转收银台
  $('.goIM').click(function (e) {
    e.stopPropagation()
    goIM('小智盈续费领福利活动')
  })

  //点击蒙层关闭
  $('.mask').click(function () {
    $(this).hide()
  })

  //点击转盘抽奖
  $('[name=doLottery]').click(function () {
    checkPermission(userInfo.pid)
    //call抽奖Api
    doLotteryApi()
  })

  $('.btn6').click(function () {
    $('#tc1').show()
  })

})
// 检查用户是否有权限参与
function checkPermission(pid) {
  if (pid != "888010000" && pid != "888010400") {
    layer.msg("本活动仅限小智盈用户参与");
    return false;
  }
  return true;
}

function getQueryString (name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  var r = window.location.search.substr(1).match(reg)
  if (r != null) return unescape(r[2])
  return null
}

/**
 * 跳转IM
 * @param fromname
 */
function goIM (fromname) {
  var b = new Base64()
  var ret = PC_JH('EM_FUNC_START_IM', '0,AC20231031,' + b.encode(fromname))
}

/**
 * 查询热度
 */
function getHotCount () {
  request(
    `${Urls}user/getcountbyactcode?actcode=${configData.hotCode}`).
    then(res => {
      if (res.code === '200') {

        var num = 0
        if (res.data) {
          num = res.data.split(',')[0]
        }
        var usedCount = parseInt(!!num ? num : 0)

        hotCount = configData.baseCount + usedCount
        updateHotCount(hotCount)
      }
    })
}

function updateHotCount (hotCount) {

  var hotCountDom = $('#hotCount')
  $(".txt1").html(hotCount);
  if (hotCount >= 20000) {
    hotCountDom.css('width', '100%')
    return
  }
  hotCountDom.css('width', `${(hotCount / 20000 * 100).toFixed(2)}%`)
}



/**
 * 抽奖
 */
function doLotteryApi () {
  request(
      `${Urls}lottery0808/dolottery?actcode=${configData.totalCode}&uid=${userInfo.uid}&pid=888010000&platform=pc`).then(res => {
    console.log(res, '抽奖')
    if (res.code === '200') {
      $("#firstPopup").hide();
      $("#zzPopup").hide();

      currentReward = res.data;
      showSuccessfulPopup(currentReward);

      getTotalCount();
      getRecordList()
      getUserPoints()
      updateHotCount()
    }else{
      layer.msg(res.message);
    }
  })
}
//弹窗类型
var PopupTypeEnum = {
  starPopup: [5, 8], northPopup: 7, kingPopup: 4, pointPopup: [1,6],bsPopup:2,gzdPopup:3
}
function showSuccessfulPopup (data) {
  // 成功后弹窗
  if (PopupTypeEnum.starPopup.includes(data.id)) {
    if (data.tipName) {
      $('.txt4').text(data.tipName)
    }
    $('#starPopup').show()
  } else if (PopupTypeEnum.pointPopup.includes(data.id)) {
    if (data.tipName) {
      $('.txt3').text(data.tipName)
    }
    $('#pointPopup').show()
  } else if (data.id === PopupTypeEnum.northPopup) {
    $('#northPopup').show()
  } else if (data.id === PopupTypeEnum.kingPopup) {
    $('#kingPopup').show()
  } else if(data.id === PopupTypeEnum.gzdPopup){
    $('#gzdPopup').show()
  } else if(data.id === PopupTypeEnum.bsPopup){
    $('#bsPopup').show()
  }else {
    //未找到
  }
}

/**
 * 查询可用积分
 */
function getUserPoints () {
  request(
      `${Urls}renew2023/querypoint?actcode=${configData.totalCode}&uid=${userInfo.uid}`).
  then(res => {
    console.log(res, '查记录')
    if (res.code === '200') {
      if (res.data) {
        userInfo.points = res.data
        $('#point').text(userInfo.points);
      }
    }
  })
}

/**
 * 获取抽奖的记录
 */
function getRecordList () {
  request(
    `${Urls}lottery0808/getmylotteryinfo?actcode=${configData.totalCode}&uid=${userInfo.uid}&pid=${userInfo.pid}`).
    then(res => {
      console.log(res, '查记录')
      if (res.code === '200') {
        if (res.data.length) {
          recordList = res.data.slice(0, 5)
          updateRecord()
        }
      }
    })
}

function updateRecord () {
  var htmlDom = ''
  for (var i = 0; i < recordList.length; i++) {
    item = recordList[i]
    htmlDom += '<li>' +
      item.writeTime.replace(/^(\d{4})-(\d{2})-(\d{2})$/, '$1年$2月$3日') +
      '参加抽奖，成功领取' + item.benefitName + '！</li>'
  }
  $('#recordWrap').html(htmlDom)
}

/**
 * 直接跳转收银台
 */
function directToPay () {
  console.log('直接跳转收银台')

  // var url = ''
  // location.href = url
}


/**
 * 获取次数
 */
function getTotalCount () {
  request(
    `${Urls}lottery0808/getlotterycountbyday?actcode=${configData.totalCode}&uid=${userInfo.uid}`).
    then(res => {
      console.log(res, '总次数')
      if (res.code === '200') {
        userInfo.totalCount = res.data
        if (userInfo.totalCount === '2') {
          $('#lotteryTips').text('1次免费抽奖待使用')
        } else {
          $('#lotteryTips').text('20积分兑换1次抽奖')
        }
      }
    })
}

/**
 * 领取88积分和100优惠券
 */

function getFreeBenefits () {
  request(
      `${Urls}lottery0808/sendpp?actcode=${configData.totalCode}&uid=${userInfo.uid}&pid=${userInfo.pid}&type=${userInfo.type}`).then(res => {
    if (res.code === '200') {
      $("#firstPopup").show();
    }
  })
}

function checkIsSendpp(){
  request(
      `${Urls}user/issubmitbyactcodes?actcodes=${configData.isSendppCode}${userInfo.type}&uid=${userInfo.uid}`).
  then(res => {
    if (res.code === '200') {
      var num = "";
      if (res.data) {
        num = res.data.split(',')[0]
      }
      if(!!num){
        checkPopupShow();
      }else{
        //未领取过88积分+优惠券
        getFreeBenefits();
      }
    }
  })
}

/**
 * 查询当日是否弹出弹窗
 */
function checkPopupShow () {
  request(
      `${Urls}lottery0808/gettipstatusbyday?actcode=${configData.totalCode}&uid=${userInfo.uid}&type=${userInfo.type}`).then(res => {
    console.log(res)
    if (res.code === '200' && res.data) {
      if (res.data === 'false') {
        $('#dayPopup').show()
      }
    }
  })
}

function request (url, dataType) {
  return new Promise((resolve, reject) => {
    $.ajax({
      type: 'get',
      url: url,
      dataType: dataType ? dataType : 'jsonp',
      success: function (data) {
        console.log(data)
        if (data.code === '200') {
          resolve(data)
        } else {
          if (url.indexOf("sendpp") > -1) {
          } else {
            layer.msg(data.msg)
          }
        }
      },
      error: function (err) {
        reject(err)
      }
    })
  })
}

function generateMarquee () {
  var result = []

  for (let i = 0; i < 10; i++) {
    var XXX = Math.floor(Math.random() * 1000) // 随机生成三位数的数字
    var YYYArr = [
      '10积分',
      '3天五星研报',
      '30天五星研报',
      '3天K线故事',
      '30天北上资金',
      '3天估值带',
      '3天买卖频谱',
      '21天智盈使用期']
    var YYY = YYYArr[Math.floor(Math.random() * YYYArr.length)] // 随机从 YYYArr 数组中选取一项
    result.push(`恭喜EMY${XXX.toString().padStart(3, '0')}****！领取${YYY}！`)
  }
  $('#marquee .list').html(result.join(' '))
}

function  getPayUrl(){}

function pushdatatocmp(uname, adcode) {
  var data = {
    "appid": '10088',
    "logtype": 'click',
    "mid": '',
    "pid": userInfo.pid,
    "sid": getQueryString("sid"),
    "tid": getQueryString("tid"),
    "uid": userInfo.uid,
    "uname": uname,
    "adcode": adcode,
    "targeturl": "",
    "pageurl": window.top.location.href
  }
  var saasUrl = "https://ds.emoney.cn/saas/queuepush";
  var saasSrc = saasUrl + "?v=" + Math.random()
      + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
      + "&message=" + encodeURIComponent(JSON.stringify(data));

  var elm = document.createElement("img");
  elm.src = saasSrc;
  elm.style.display = "none";
  document.body.appendChild(elm);
}
function GetExternal() {
  return window.external.EmObj;
}

function PC_JH(type, c) {
  try {
    var obj =
        GetExternal();
    return obj.EmFunc(type, c);
  } catch (e) {}
}
