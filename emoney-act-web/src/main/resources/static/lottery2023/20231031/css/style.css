@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#FF1C16;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #e42c34; font-weight: bold;}
.f1{text-decoration:underline;}
.img_1{background:url(../images/index_01.png) center; height:225px;}
.img_2{background:url(../images/index_02.png) center; height:197px;}
.img_3{background:url(../images/index_03.png) center; height:250px;}
.img_3b{background:url(../images/index_03b.png) center; height:250px;}
.img_4{background:url(../images/index_04.png) center; height:1001px;}
.img_4b{background:url(../images/index_04b.png) center; height:1001px;}
.img_5{background:url(../images/index_05.png) center; height:984px;}
.img_5b{background:url(../images/index_05b.png) center; height:984px;}


.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:20px 0 176px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}
.black{color: #000; font-weight: bold;}

.bott{background:url("../images/pf.png") center;height:156px; position:fixed; bottom:0; width:100%;}
.bott2{background:url("../images/pf2.png") center;height:167px; position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}


.btn1{
	background: url("../images/btn1.png");
	width: 720px;
	height: 145px;
	position: absolute;
	top: 114px;
	left: -269px;
}
.btn1b{
	background: url("../images/btn1b.png");
	width: 720px;
	height: 155px;
	position: absolute;
	top: 104px;
	left: -269px;
}
.btn2{ font-size: 46px; text-align: center; color: #fff;
	width:395px;
	height:105px; display: block; margin: 40px auto;}
.btn3{
	background: url("../images/btn3.png");
	width: 548px;
	height: 150px;display: block; margin: 40px auto;
}
.btn3b{
	background: url("../images/btn3b.png");
	width: 548px;
	height: 150px;display: block; margin: 40px auto;
}
.btn4{
	position: absolute;
	top: 825px;
	left: -210px;
	background: url("../images/btn4.png");
	width: 523px;
	height: 119px;
}
.btn5{
	position: absolute;
	top: 21px;
  left: 235px;
	background: url("../images/btn5.png");
	width: 293px;
	height: 118px;
}
.btn6{
	position: absolute;
	top: -125px;
	left: 71px;
	background: url("../images/btn6.png");
	width: 24px;
	height: 23px;
}
.zp{
	position: absolute;
	top: -329px;
	left: 199px;
	background: url("../images/zp.png");
	width: 334px;
	height: 334px;
}
.zz{
	position: absolute;
	top: -121px;
	left: 301px;
	background: url("../images/zz.png");
	width: 108px;
	height: 116px;
}
.ico1{
	position: absolute;
	top: -282px;
	left: 263px;
	width: 184px;
	text-align: center;
	color: #000;
	font-size: 18px;
}
.ico6{
	position: absolute;
	top: 101px;
  left: -400px;
	width: 497px;
	height: 13px;
	border-radius: 6px;
}

.process {
	position: relative;
	border-radius: 6px;
	height: 13px;
	background: url("../images/ico6.png") no-repeat;
	background-size: 100% 100%;
}

.process::after {
	content: '';
	display: block;
	position: absolute;
	right: -36px;
	top: -30px;
	width: 55px;
	height: 60px;
	background: url("../images/ico2.png");
}

.ico3{
	position: absolute;
	left: 30px;
	top: -30px;
	width: 52px;
	height: 63px;
	background: url("../images/ico3.png");
}
.ico4{
	position: absolute;
	left: 219px;
	top: -30px;
	width: 56px;
	height: 64px;
	background: url("../images/ico4.png");
}
.ico5{
	position: absolute;
	left: 408px;
	top: -18px;
	width: 54px;
	height: 44px;
	background: url("../images/ico5.png");
}
.txt1{
	position: absolute;
	left: -115px;
	top: 6px;
	width: 100px;
	color: #fee995;
	font-size: 20px;
	font-weight: bold;
	text-align: center;
}
.txt2{
	position: absolute;
	left: 172px;
	top: 176px;
	width: 368px;
	color: #633223;
	font-size: 19px;
	text-align: center;
}
.bg3{background:url("../images/bg3.png") no-repeat center top; width: 997px; border:3px solid #fff69e; padding:35px 0 60px 0; margin: 0 auto; border-radius: 15px;}
.bg3 ul{margin:61px 0 0 135px;}
.bg3 li{background:url("../images/bg5.png") no-repeat left 20px; width:700px; height: 65px; overflow: hidden; line-height: 75px; padding-left: 68px; font-size: 28px; color: #fff69e;}
.bg4{background-image:url("../images/bg4.png"); width: 432px; height: 112px; margin: 0 auto;}

.mask,
.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}

.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-clear{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-clear:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}
.mar{
	position: absolute;
	left: -372px;
	top: 18px;
	width: 910px;
	font-size: 18px;
	color: #ffdd74;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

.hdgz{background-color: #fff;width: 820px;
border-radius: 10px;
position: absolute;
left: 50%;
top: 50%;
margin: -332px 0 0 -410px;
color: #000;
padding: 25px;
font-size: 16px;
line-height: 25px;}
.hdgz ul{padding-left: 20px;}
.hdgz li{list-style-type:decimal;}

.tc3{
	display: none;
	position: fixed;
	top: 50%;
	left: 50%;margin: -269px 0 0 -226px;
}
.tc-btn{background: url("../images/tc-btn.png");
    width:351px;
    height:96px;text-align: center; line-height: 90px; color: #fff6d2; font-size: 32px;}
.tc-btn2{background: url("../images/tc-btn2.png");
    width:115px;
    height:120px;position:absolute;top: 198px;
  left: 156px;}
.txt3{
	position: absolute;
	left: 99px;
top: 134px;
	width: 250px;
	color: #fee995;
	font-size: 45px;
	text-align: center;
}
.txt4{
	position: absolute;
	left: 101px;
top: 88px;
	width: 250px;
	color: #fff;
	font-size: 30px; font-weight: bold;
	text-align: center;
}
.an1{position:absolute;top: 375px;left: 48px; }
.an2{position:absolute;top: 393px;left: 48px; }
.an3{position:absolute;top: 418px;left: 48px; }
.an4{position:absolute;top: 441px;left: 48px; }
.an5{position:absolute;top: 471px;left: 48px; }
.an6{position:absolute;top:497px;left: 48px; }
.an7{position:absolute;top: 350px;left: 48px; }
.server{
    background: url("../images/server.png");
    width: 120px;
    height:120px;
    position:fixed;
    top: 330px;
    right: 0px;
}



.dh2{
  -webkit-animation: dh2 2s linear infinite alternate;
  animation-name: dh2 2s linear infinite alternate;
}
@-webkit-keyframes dh2{
0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
40%{-webkit-transform:translateY(-30px)}
60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes dh2{
0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
40%{-moz-transform:translateY(-30px)}
60%{-moz-transform:translateY(-15px)}
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -25px;
	top: -25px;
	width: 30px;
	height: 30px;
}

.hidden {
	display: none;
}

.popup-center {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}


.tc3, .bg {
	display: none;
}


.recordWrap {
	min-height: 502px;
}

.relative {
	position: relative;
}

@keyframes marquee {
	0% {
		transform: translateX(0%);
	}
	100% {
		transform: translateX(-100%);
	}
}

.marquee {
	position: absolute;
	left: -372px;
	top: 18px;
	width: 910px;
	font-size: 16px;
	color: #fee995;
	white-space: nowrap;
	overflow: hidden;
}

.marquee .list {
	animation: marquee 20s linear infinite;
}