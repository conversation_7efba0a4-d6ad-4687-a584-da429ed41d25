@charset "utf-8";
body {
	margin: 0;
	padding: 0; overflow: hidden;
}


/*公共样式--开始*/

html,
body,
div,
ul,
li,
h1,
h2,
h3,
h4,
h5,
h6,
p,
dl,
dt,
dd,
ol,
form,
input,
textarea,
th,
td,
select {
	margin: 0;
	padding: 0;
}

* {
	box-sizing: border-box;
}

table tbody tr {
	height: 43px;
}

html,
body {
	min-height: 100%; background-color: #000;
}

body {
	font-family: "Microsoft YaHei";
	font-size: 14px;
	color: #333;overflow: hidden;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: normal;
}

ul,
ol {
	list-style: none;
}

img {
	border: none;
	vertical-align: middle;
}

a {
	text-decoration: none;
	color: #232323;
	outline: none;
}

a:hover {
}

a:visited {
}

a:link {
}

a:active {
	/*star: expression_r(this.onFocus=this.blur());*/
}

:focus {
	outline: 0;
}


/*内容开始*/

#content {
	position: fixed;
	width: 100%;
	height: 100%;
	background-color: rgba(51, 51, 51, 0.5);
	
}

.content {
	width: 910px;
	height:603px;
	border: 1px solid #b5b5b5;
	position: fixed;
	left: 50%;
	margin-left: -455px;
	top: 50%;
	margin-top: -301px;
	background: url("../images/tc/ynzjzyc1_bg.png") no-repeat center;
	overflow: hidden;
}
.txt1{
	position: absolute;
	left: 582px;
top: 378px;
	text-align: right;
	width: 136px;
	font-size: 12px;
	color: #ab999b;
	font-family: "宋体";
}
.bod .btn1{
	display: block;
	width:298px;
	height:124px;
	border: none;
	cursor: pointer;
	position:absolute;
left: 612px;
  top: 484px;background: url("../images/tc/btn.png") no-repeat center;
}
.bo .btn1{
	display: block;
	width:298px;
	height:124px;
	border: none;
	cursor: pointer;
	position:absolute;
left: 612px;
  top: 484px;background: url("../images/tc/btnh.png") no-repeat center;
}
.bod .btn2{
	display: block;
	width:265px;
	height:61px;
	border: none;
	cursor: pointer;
	position:absolute;
left: 473px;
  top: 311px;background: url("../images/tc/btn2.png") no-repeat center;
}
.bo .btn2{
	display: block;
	width:265px;
	height:61px;
	border: none;
	cursor: pointer;
	position:absolute;
left: 473px;
  top: 311px;background: url("../images/tc/btn2h.png") no-repeat center;
}

.button_none{
	display:none ;
}
.img{
	position: absolute;
	left: 665px;
	top: 310px;
}
.djs{
	position: absolute;
	left: 510px;
top: 88px;
width: 186px;
height: 61px;
line-height: 61px;
color: #B50D0D;
font-size: 46px;
	text-align: center;
	font-weight: bold;font-family: "Microsoft YaHei";
}

.bod .dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
}

.bod .dh{
	/*animation-name: dh 0.3s linear infinite alternate;*/
}

@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

.con_close{
	width: 35px;
	height: 35px;
	position: absolute;
	right: -7px;
	top: 2px;
}
.bzts{position: absolute; top: 2px; left: 830px; color: #fff; font-size: 12px; font-family: "宋体";}
.bzts input{vertical-align: -3px;}

.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/tc/h.png);}
.tc_qgn{background-image:url(../images/tc.png);
	width: 500px;
	height:333px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -166px 0 0 -250px;
}
.tc1{background-image:url("../images/tc/tc1.png"); 
	width: 500px;
	height:333px;
	position: absolute;
	left: 50%;
	top: 50%; margin:-166px 0 0 -250px;
}
.tc2{background-image:url(../images/tc/tc2.png); 
	width: 499px;
	height:243px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -121px 0 0 -250px;
}
.tc3{background-image:url(../images/tc/tc3.png); 
	width: 499px;
	height:243px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -126px 0 0 -250px;
}
.tc_btn1{background-image:url("../images/tc/tc-btn1.png"); 
	width: 195px;
	height:56px;
	position: absolute;
	left: 152px;
  top: 184px;
}
.tc_btn2{background-image:url("../images/tc/tc-btn2.png"); 
	width: 174px;
	height:50px;
	position: absolute;
	left: 301px;
  top: 252px;
}
.close{
	background: url("../images/tc/close.png") center top no-repeat;
	width: 25px;
	height: 25px;
	position: absolute;
	top: -30px;
	right:-30px;
}
