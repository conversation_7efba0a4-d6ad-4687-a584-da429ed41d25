$(function(){
	'use strict';
	var Urls = www;
	var isLogin = $("#hid_isLogin").val();
	var loginUser = {
		"uid": $("#hid_uid").val(),
		"pid": $("#hid_pid").val(),
		"mobileX": $("#hid_mobilex").val(),
		"maskMobile": $("#hid_maskmobile").val(),
		"point":$("#hid_allpoint").val()
	}
	var configData = {
		"actCode": $("#hid_actcode").val(),
		"Code_qgn":"privilege20240218",
		"cmp_qgn":"AC588Pop20240218",
		"cmp_qp":"AC588vot20240218",
		"tip_code":"privilege20240218tip",
		"actApi":Urls,
		"staticPath":$("#hid_staticPath").val()
	}
	var sso = location.search.slice(1);
	var thisPage = {
		Init: function () {
			thisPage.scrollFun();
			thisPage.bindEvents();

			$(".tc_btn1").attr("target", "_blank");
			// $(".tc_btn1").attr("href", "https://r.emoney.cn/zyclientjuejin");


			if(thisPage.isMiniPage()){
				thisPage.popWinInit();
			}else{
				if (isLogin === '0') {
					$('.bg').show();
					$('.tc').show();
				}else{
					thisPage.isSubmit(configData.Code_qgn,function (ret){
						if (ret.length > 1) {
							var val = ret[0];
							if (!!val) {
								if(thisPage.isNewVersion()){
									$(".bod").attr("class", "bo");
								}else{
									$(".bod").attr("class", "bo2");
								}
							}
						}

					});
				}
			}
		},
		scrollFun: function () {
			$(window).scroll(function (e) {
				if ($(window).scrollTop() >= 740) {
					$('.pf').fadeIn(300);
				} else {
					$('.pf').fadeOut(300);
				}
			});
		},
		//弹屏页
		popWinInit:function () {
			thisPage.PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
			thisPage.PC_JH("EM_FUNC_WND_SIZE", "w=910,h=628,mid");

			var ret = thisPage.getCookie(configData.tip_code);
			if (!!ret) {
				//设置不再弹出
				thisPage.PC_JH("EM_FUNC_CLOSE", "");
				return false;
			}
			//未设置
			thisPage.isSubmit(configData.tip_code + "," + configData.Code_qgn, function (retData) {
				if (retData.length > 1) {
					var tipval = retData[0];
					var qgnval = retData[1];
					if(tipval == '0') {
						//不再弹出
						thisPage.PC_JH("EM_FUNC_CLOSE", "");
					}else{
						setTimeout(function () {
							thisPage.PC_JH("EM_FUNC_SHOW", "1");
						}, 3000);
					}
					//是否领取
					if (!!qgnval) {
						if(thisPage.isNewVersion()){
							$(".bod").attr("class", "bo");
						}else{
							$(".bod").attr("class", "bo2");
						}
					}

				}
			});

		},
		//绑定事件
		bindEvents: function () {
			//抢功能
			$(".popupD").click(function () {
				//已领取
				if ($(".bo").length > 0) {
					return false;
				}
				if($(".bo2").length > 0){
					$(".h").show();
					$(".tc2").show();
					return false;
				}
				if (!thisPage.checkIsLogin()) {
					return false;
				}
				if (!thisPage.checkPermission()) {
					return false;
				}

				thisPage.addQgn(configData.Code_qgn, function () {
					if (thisPage.isNewVersion()) {
						$(".tc_btn1").hide();
						$(".tc_btn2").show();
					} else {
						$(".tc_btn1").show();
						$(".tc_btn2").hide();
					}
					$(".h").show();
					$(".tc_qgn").show();
					$(".bod").attr("class", "bo");
				});
				thisPage.pushdatatocmp('', configData.cmp_qgn);
				thisPage.pushdatatocmp('', configData.cmp_qp);
			});

			$("#notips").click(function (){
				if($(this).prop('checked')){
					thisPage.setCookie(configData.tip_code,loginUser.uid,60);
					thisPage.addCount(configData.tip_code,"0",function (){});
				}else{
					thisPage.setCookie(configData.tip_code, "",-1);//清除cookie
					thisPage.addCount(configData.tip_code,"1",function (){});
				}
			});
			$(".close,.tc_btn2").click(function () {
				$(".h,.tc_qgn,.tc1,.tc2,.tc3").hide();
			});
		},
		//抢攻能
		addQgn: function (accode, callback) {
			$.ajax({
				type: 'get',
				url: 'sendprivilege_qgn',
				dataType: 'json',
				data: {
					"actcode": "privilege20240218",
					"actType": 2,
					"activityID": "PAC1240130145111481",
					"reason": "19期智盈上拽养销"
				},
				success: function (data) {
					if (data.code == "200") {
						callback && callback();
					}
				}
			});
		},
		//计数
		addCount: function (accode,val, callback) {
			var nowtime = new Date().getTime();
			if (!thisPage.checkIsLogin()) {
				return false;
			}

			$.ajax({
				type: 'get',
				url: configData.actApi + '/user/addcountbyactcode?actcode=' + accode,
				dataType: 'jsonp',
				data: {
					uid: loginUser.uid,
					value: val
				},
				success: function (data) {
					if (data.code == "200") {
						callback && callback();
					}
				}
			});
		},
		//是否参与过
		isSubmit: function (accode,callback) {
			if (!thisPage.checkIsLogin()) {
				return false;
			}
			$.ajax({
				type: 'get',
				url: configData.actApi + "/user/issubmitbyactcodes?actcodes=" + accode,
				dataType: 'jsonp',
				data: {
					uid: loginUser.uid
				},
				success: function (data) {
					if (data.code == '200') {
						var retVals = "";
						if (data.data) {
							retVals = data.data.split(',')
						}
						callback && callback(retVals);
					}
				}
			});
		},
		//推送cmp
		pushdatatocmp: function (uname, adcode) {
			var data = {
				"appid": '10088',
				"logtype": 'click',
				"mid": '',
				"pid": thisPage.getQueryString("pid"),
				"sid": thisPage.getQueryString("sid"),
				"tid": thisPage.getQueryString("tid"),
				"uid": thisPage.getQueryString("uid"),
				"uname": uname,
				"adcode": adcode,
				"targeturl": "",
				"pageurl": window.top.location.href
			}
			var saasUrl = "https://ds.emoney.cn/saas/queuepush";
			var saasSrc = saasUrl + "?v=" + Math.random()
				+ "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
				+ "&message=" + encodeURIComponent(JSON.stringify(data));

			var elm = document.createElement("img");
			elm.src = saasSrc;
			elm.style.display = "none";
			document.body.appendChild(elm);
		},
		getQueryString: function (name) {
			var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
			var r = window.location.search.substr(1).match(reg);
			if (r != null) return unescape(r[2]);
			return null;
		},
		setCookie: function (name, value,expiredays) {
			var expdate = new Date();
			expdate.setDate(expdate.getDate() + expiredays);
			document.cookie = name + "=" + value + ";expires=" + expdate.toGMTString() + ";path=/";
		},
		getCookie: function (c_name) {
			if (document.cookie.length > 0) {
				var c_start = document.cookie.indexOf(c_name + "=")
				if (c_start != -1) {
					c_start = c_start + c_name.length + 1
					var c_end = document.cookie.indexOf(";", c_start)
					if (c_end == -1) c_end = document.cookie.length
					return unescape(document.cookie.substring(c_start, c_end))
				}
			}
			return ""
		},
		//客户端方法
		GetExternal: function () {
			return window.external.EmObj;
		},
		//调用客户端接口
		PC_JH: function (type, c) {
			try {
				var obj = thisPage.GetExternal();
				return obj.EmFunc(type, c);
			} catch (e) {
			}
		},
		//检查用户是否有权限参与
		checkPermission: function () {
			var pidlist = "888020000,888080000,888204010,888224010,888010000";
			var pidlist_ds = "888020000,888080000,888204010,888224010";
			//无权限
			if (pidlist.indexOf(loginUser.pid) < 0) {
				$(".h").show();
				$(".tc3").show();
				return false;
			}
			//大师用户提示
			if (pidlist_ds.indexOf(loginUser.pid) > -1) {
				if (thisPage.isNewVersion()) {
					$(".tc_btn1").hide();
					$(".tc_btn2").show();
				} else {
					$(".tc_btn1").show();
					$(".tc_btn2").hide();
				}

				$(".h").show();
				$(".tc1").show();
				return false;
			}
			return true;
		},
		//检查用户是否登录
		checkIsLogin: function () {
			if (isLogin === '0') {
				layer.msg("您还未登录，请在客户端登录后参与活动");
				return false;
			}
			return true;
		},
		//判断是否是最新版客户端
		isNewVersion: function () {
			var version = thisPage.getQueryString("Version");

			if (!!version && parseInt(version)>=2024020331) {
				return true;
			}
			return !version ? true : false;
		},
		//判断是否是弹窗页
		isMiniPage:function (){
			if(location.href.indexOf("mini")>-1){
				return true;
			}
			return false;
		}
	}
	
	thisPage.Init();
})