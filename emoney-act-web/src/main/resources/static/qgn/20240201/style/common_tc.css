@charset "utf-8";
* {
	margin: 0;
	padding: 0; overflow: hidden;
}


/*公共样式--开始*/

html,
body,
div,
ul,
li,
h1,
h2,
h3,
h4,
h5,
h6,
p,
dl,
dt,
dd,
ol,
form,
input,
textarea,
th,
td,
select {
	margin: 0;
	padding: 0;
}

* {
	box-sizing: border-box;
}

html,
body {
	min-height: 100%; background-color: #000;
}

body {
	font-family: "Microsoft YaHei";
	font-size: 14px;
	color: #333;overflow: hidden;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: normal;
}

ul,
ol {
	list-style: none;
}

img {
	border: none;
	vertical-align: middle;
}

a {
	text-decoration: none;
	color: #232323;
	outline: none;
}

:focus {
	outline: 0;
}


/*内容开始*/

#content {
	position: fixed;
	width: 100%;
	height: 100%;
	background-color: rgba(51, 51, 51, 0.5);

}

.content {
	width: 910px;
	height:603px;
	border: 1px solid #b5b5b5;
	position: fixed;
	left: 50%;
	margin-left: -455px;
	top: 50%;
	margin-top: -301px;
	background: url("../images/tc/ynzjzyc1_bg.png") no-repeat center;
	overflow: hidden;
}
.txt1{
	position: absolute;
	left: 582px;
	top: 378px;
	text-align: right;
	width: 136px;
	font-size: 12px;
	color: #ab999b;
	font-family: "宋体";
}
.bod .btn1{
	display: block;
	width:257px;
	height:102px;
	border: none;
	cursor: pointer;
	position:absolute;
	left: 621px;
	top: 511px;background: url("../images/tc/btn.png") no-repeat center;
}
.bo .btn1{
	display: block;
	width:257px;
	height:102px;
	border: none;
	cursor: pointer;
	position:absolute;
	left: 621px;
	top: 511px;background: url("../images/tc/btnh.png") no-repeat center;
}
.bo2 .btn1{
	display: block;
	width:257px;
	height:102px;
	border: none;
	cursor: pointer;
	position:absolute;
	left: 621px;
	top: 511px;background: url("../images/btn1h2.png") no-repeat center;
}
.btn2{
	display: block;
	width:756px;
	height:470px;
	border: none;
	cursor: pointer;
	position:absolute;
	left: 0px;
	top: 30px;
}

.button_none{
	display:none ;
}
.img{
	position: absolute;
	left: 665px;
	top: 310px;
}
.djs{
	position: absolute;
	left: 510px;
	top: 88px;
	width: 186px;
	height: 61px;
	line-height: 61px;
	color: #B50D0D;
	font-size: 46px;
	text-align: center;
	font-weight: bold;font-family: "Microsoft YaHei";
}

.bod .dh{
	-webkit-animation: dh 0.3s linear infinite alternate;
	animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}

@keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}

.con_close{
	width: 35px;
	height: 35px;
	position: absolute;
	right: -7px;
	top: 2px;
}
.bzts{position: absolute; top: 2px; left: 830px; color: #fff; font-size: 12px; font-family: "宋体";}
.bzts input{vertical-align: -3px;}

.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/tc/h.png);}
.tc_qgn{background-image:url(../images/tc/tc.png);
	width: 443px;
	height:320px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -160px 0 0 -221px;
}
.tc1{background-image:url("../images/tc/tc1.png");
	width: 443px;
	height:320px;
	position: absolute;
	left: 50%;
	top: 50%; margin:-160px 0 0 -221px;
}
.tc2{background-image:url(../images/tc/tc2.png);
	width: 443px;
	height:320px;
	position: absolute;
	left: 50%;
	top: 50%; margin:-160px 0 0 -221px;
}
.tc3{background-image:url(../images/tc/tc3.png);
	width: 443px;
	height:320px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -160px 0 0 -221px;
}
.tc_btn1{background-image:url("../images/tc/tc-btn1.png");
	width: 327px;
	height:98px;
	position: absolute;
	left: 86px;
	top: 207px;
}
.tc_btn2{background-image:url("../images/tc/tc-btn2.png");
	width: 269px;
	height:85px;
	position: absolute;
	left: 86px;
	top: 207px;
}

.close{
	background: url("../images/tc/close.png") center top no-repeat;
	width: 25px;
	height: 25px;
	position: absolute;
	top: 0;
	right: 0;
}

