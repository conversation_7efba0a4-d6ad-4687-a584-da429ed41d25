@charset "utf-8";

img{ border:0px;}
body {
	font-size:12px; overflow-x:hidden;
	margin:0;
	padding:0;
	line-height:22px;
	color:#BE5921;
	background-color:#EDAC4A;font-family:"微软雅黑";
}

.main{width:100px; margin:0 auto; position:relative;}
ul,li {margin:0;padding:0;list-style:none;}
A:link {TEXT-DECORATION: none;color:#fff;}
A:visited {TEXT-DECORATION: none;color:#fff;}
A:hover {TEXT-DECORATION: none;color:#ffff00;}
A.b:link {COLOR: #FFD334; TEXT-DECORATION: underline}
A.b:visited {COLOR: #FFD334; TEXT-DECORATION: underline}
A.b:hover {COLOR: #ffff00; TEXT-DECORATION: none}
input{background:transparent;border:0px;margin:0px; color:#939598;list-style:none; font-family:"微软雅黑"; font-size:14px;}
.white {color:#ffffff;}

.red2{color:#d30101;}
.green {color:green;}
.black{ color:#000;}
.blue{color:#549FD6;}
.yellow{color:#ffff00;}
.blue3{color:#255E98;}
.gray{color:#6A3906;}
.org{color:#ff5a00;}
.bg_h{background-color:#f0f0f0;}
.f_13{font-size:13px;}
.f_14{font-size:14px;}
.clr2{clear:both; height:1px; overflow:hidden;}
.m_t36{margin-top:36px;}
.m_t20{margin-top:20px;}
.m_t10{margin-top:10px;}
.t_c{text-align:center;}
.f_12{font-size:12px; font-family:"宋体";}
.f_16{font-size:16px;font-weight:bold;}
.f_17{font-size:17px;}
.f_26{font-size:26px; font-family:"微软雅黑"; line-height:50px;}
.f_30{font-size:28px; color:#603813; line-height:50px; font-weight:bold;}
.f_22{font-size:22px; font-family:"微软雅黑";}
.h340{height:340px;}
td{font-size:18px;text-align:center; background-color:#fff;}

.bt { font-weight:bold;color: #4d83f3;	font-size: 30px;}
.bt2{color: #4d83f3;	font-size: 21px;}
.f-r{float:right;}
.f-l{float:left;}
.f1{background-color:#f3f3fd; color:#333; padding:3px 5px;}

#dbg1{background:url("../images/index_01.jpg") center top no-repeat;height:362px;}
#dbg2{background:url(../images/index_02.jpg) center top no-repeat;height:345px;}
#dbg3{background:url(../images/index_03.jpg) center top no-repeat;height:549px;}
#dbg4{background:url(../images/index_04.png) center top no-repeat;height:539px;}
#dbg5{background:url(../images/index_05.png) center top no-repeat;height:891px;}
#dbg6{background:url(../images/index_06.png) center top no-repeat;height:599px;}
#dbg7{background:url(../images/index_07.png) center top no-repeat;height:429px;}
#dbg8{background:url(../images/index_08.png) center top no-repeat;height:577px;}
#dbg9{background:url(../images/index_09.png) center top no-repeat;height:1292px;}

.pf{background:url("../images/index_10.png") center top no-repeat; height: 182px; width: 100%; position: fixed; left: 0px; bottom: 0px; display: none;}
.pf2{background:url("../images/pf.png") center top no-repeat; width:262px; height:314px; position: fixed; right: 0px; top:80px;}
.pf3{background:url("../images/pf2.png") center top no-repeat;width:213px; height:231px;  position: fixed; right: 0px; top:390px;}
.qp .btn1{
	background: url("../images/btn1.png");
	position: absolute;
	left: -181px;
	top: 102px;
	width: 464px;
	height: 138px;
}
.qp .btn5{
	background: url("../images/btn5.png");
	position: absolute;
	left: 71px;
	top: 73px;
	width: 379px;
	height: 108px;
}
.qph .btn1{
	background: url("../images/btn1h.png");
	position: absolute;
	left: -181px;
	top: 102px;
	width: 464px;
	height: 138px;
}
.qph .btn5{
	background: url("../images/btn5h.png");
	position: absolute;
	left: 71px;
	top: 73px;
	width: 379px;
	height: 108px;
}

.bod .btn2{
	background: url("../images/btn2.png");
	position: absolute;
	left: -30px;
	top: 251px;
	width: 175px;
	height: 71px;
}
.bod .btn3{
	background: url("../images/btn3.png");
	position: absolute;
	left: -275px;
	top: 631px;
	width: 650px;
	height: 178px;
}

.bod .btn4{
	background: url("../images/btn4.png");
	position: absolute;
	left: -346px;
top: 73px;
	width: 379px;
	height: 108px;
}
.bo .btn2{
	background: url("../images/btn2h.png");
	position: absolute;
	left: -30px;
	top: 251px;
	width: 175px;
	height: 71px;
}
.bo .btn3{
	background: url("../images/btn3h.png");
	position: absolute;
	left: -275px;
	top: 631px;
	width: 650px;
	height: 178px;
}

.bo .btn4{
	background: url("../images/btn4h.png");
	position: absolute;
	left: -346px;
top: 73px;
	width: 379px;
	height: 108px;
}
.pfx{position: absolute; left: 540px; top: -7px; background: url("../images/close.png") center top no-repeat;
	width: 30px;
	height: 30px;}
.close{
	background: url("../images/close.png") center top no-repeat;
	width: 30px;
	height: 30px;
	position: absolute;
	top: 0;
	right: 0;
}
.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/h.png); display: none}
.tc{background-image:url(../images/tc3.png); 
	width: 679px;
	height:656px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -328px 0 0 -339px;
}
.tc_qgn{background-image:url(../images/tc.png);
	width: 679px;
	height:656px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -328px 0 0 -339px;
}
.server{position:fixed; right:0; top:20%; text-align: center; color: #fff; font-size: 16px; line-height: 25px;}
.server li{ margin-bottom:10px; width:157px; overflow:hidden;}
.bod .dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.qp .dh3{
  -webkit-animation: dh3 0.3s linear infinite alternate;
  animation-name: dh3 0.3s linear infinite alternate;
}
@-webkit-keyframes dh3{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh3{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.dh2{
  -webkit-animation: dh2 1.5s linear infinite alternate;
  animation-name: dh2 1.5s linear infinite alternate;
}
@-webkit-keyframes dh2 {
  0%, 10%,20%, 100% {
    opacity: .6;
  }

  5%, 15% {
    opacity: 0;
  }
}

@keyframes dh2 {
  0%, 10%,20%, 100% {
    opacity: .6;
  }

  5%, 15% {
    opacity: 0;
  }
}
@-webkit-keyframes wobble2 {
  0% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  15% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  30% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  45% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  60% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  75% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  100% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }
}

@keyframes wobble2 {
  0% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  15% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  30% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  45% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  60% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  75% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  100% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }
}

.wobble2 {
  -webkit-animation-name: wobble2;
          animation-name: wobble2;
		  -webkit-transform-origin: center bottom;
	-moz-transform-origin: center bottom;
	-ms-transform-origin: center bottom;
	-o-transform-origin: center bottom;
	transform-origin: center bottom;
	-webkit-animation-duration: 3s;
	-o-animation-duration: 3s;
	animation-duration: 3s;
	-webkit-animation-timing-function: ease-in;
	-o-animation-timing-function: ease-in;
	animation-timing-function: ease-in;
	-webkit-animation-delay: 0s;
	-o-animation-delay: 0s;
	animation-delay: 0s;
	-webkit-animation-iteration-count: infinite;
	-o-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	-webkit-animation-direction: normal;
	-o-animation-direction: normal;
	
	-webkit-animation-fill-mode: both;
	-o-animation-fill-mode: both;
	animation-fill-mode: both;
}
.pic1{
	position: absolute;
	left: -426px;
	top: 149px;
}


.footer{text-align:center; font-family:"宋体"; font-size:12px; padding:0px 0 200px; color: #fff;}

.bg{background-image:url(../images/h.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.bg .tclogin{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.bg .tclogin .bt1{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.bg .tclogin .txt{padding: 25px 0 0 195px;font-size: 20px;}
.bg .tclogin .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.bg .tclogin .zj{letter-spacing:16px;}
.bg .tclogin .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 304px;line-height: 45px;}
.bg .tclogin .yzm:hover{background-color: #f4f4f4;}
.bg .tclogin .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.bg .tclogin .tc-btn1:hover{ background-color: #3689ce;}
.bg .tclogin .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.bg .tclogin .tc-btn2:hover{ background-color: #cecece;}
input[type=text]{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}
.tc-txt{
	width: 454px;
	position: absolute;
	left: 0%;
	top: 220px; text-align: center; font-size: 16px; color: #000;
}
