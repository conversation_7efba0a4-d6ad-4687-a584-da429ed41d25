$(function(){
	'use strict';
	var Urls = www;
	var isLogin = $("#hid_isLogin").val();
	var loginUser = {
		"uid": $("#hid_uid").val(),
		"pid": $("#hid_pid").val(),
		"mobileX": $("#hid_mobilex").val(),
		"maskMobile": $("#hid_maskmobile").val(),
		"point":$("#hid_allpoint").val()
	}
	var configData = {
		"actCode": $("#hid_actcode").val(),
		"Code_qgn":$("#hid_actcode").val(),
		"Code_qp":"ACBandQP20240705",
		"cmp_qgn":"FUNC20240702105570",
		"cmp_qp":"TICKET20240702105566",
		"privilegeCode":"PAC124070216514188",
		"actApi":Urls,
		"staticPath":$("#hid_staticPath").val()
	}
	var sso = location.search.slice(1);
	var thisPage = {
		Init:function(){
			if (isLogin==='0') {
				$(".bg").show();
				$(".tclogin").show();
				return;
			}
			thisPage.scrollFun();
			thisPage.bindEvents();
			thisPage.isSubmit(configData.Code_qgn+","+configData.Code_qp);
			var over = getQueryString("d");
			if (over=="0") {
				$("#over").show();
			}
		},
		scrollFun:function(){
			$(window).scroll(function(e){
				if($(window).scrollTop() >= 740){
					$('.pf').fadeIn(300);
				}else{
					$('.pf').fadeOut(300);
				}
			});
		},
		//绑定事件
		bindEvents: function () {
			//点击抢功能
			$(".popupD").click(function(){
				//已领取
				if($(".bo").length>0){
					return false;
				}
				var date = thisPage.getQueryString("date");
				
				thisPage.addQgn(configData.Code_qgn,configData.privilegeCode,function(){
					$(".h").show();
					$(".tc_qgn").show();
					$(".bod").attr("class","bo");
				});
				thisPage.pushdatatocmp(loginUser.mobileX,configData.cmp_qgn);
			});
			//点击抢票
			$(".popupD2").click(function(){
				//已领取
				if($(".qph").length>0){
					return false;
				}
				
				thisPage.addCount(configData.Code_qp,function(){
					$(".h").show();
					$(".tc").show();
					$(".qp").attr("class","qph");
				});
				thisPage.pushdatatocmp(loginUser.mobileX,configData.cmp_qp);
			})
			//随堂测
			$(".pf3").click(function(){
				$(this).attr("target","_blank");
				$(this).attr("href",thisPage.getSurveyUrl());
			});
			
			$(".close").click(function(){
				$(".h,.tc,.tc_qgn").hide();
			});
		},
		//抢攻能
		addQgn: function (acCode, activityID,callback) {
			$.ajax({
				type: 'get',
				url: 'sendprivilege_qgn',
				dataType: 'json',
				data: {
					"actcode": acCode,
					"actType": 2,
					"activityID": activityID,
					"reason": "五星波段10期第一轮活动"
				},
				success: function (data) {
					if (data.code == "200") {
						callback && callback();
					}else{
						layer.msg(data.msg);
					}
				}
			});
		},
		//计数
		addCount:function(accode,callback){
			var nowtime = new Date().getTime();
			if (!thisPage.checkIsLogin()) {
				return false;
			}
			if (!thisPage.checkPermission()) {
				return false;
			}
			$.ajax({
				type: 'get',
				url: configData.actApi + '/user/addcountbyactcode?actcode=' + accode,
				dataType: 'jsonp',
				data: {
					uid: loginUser.uid ,
					value: nowtime
				},
				success: function (data) {
					if(data.code=="200"){
						callback && callback();
					}
				}
			});
		},
		//是否参与过
		isSubmit:function(accode){
			if (!thisPage.checkIsLogin()) {
				return false;
			}
			if (!thisPage.checkPermission()) {
				return false;
			}
			$.ajax({
				type: 'get',
				url: configData.actApi + "/user/issubmitbyactcodes?actcodes=" + accode,
				dataType: 'jsonp',
				data: {
					uid: loginUser.uid
				},
				success: function (data) {
					if (data.code == '200') {
						var num = "";
						if (data.data) {
							num = data.data.split(',')
						}
						if(num.length>1){
							var qgn = num[0];
							var qp = num[1];
							if(!!qgn){
								$(".bod").attr("class","bo");
							}
							if(!!qp){
								$(".qp").attr("class","qph");
							}
						}
					}
				}
			});
		},
		//获取随堂测
		getSurveyUrl:function () {
			var dates = [
				"2024/07/03 15:00-2024/07/09 15:00",
				"2024/07/09 15:00-2024/07/10 15:00",
				"2024/07/10 15:00-2024/07/11 15:00",
				"2024/07/11 15:00-2024/07/12 15:00",
				"2024/07/12 15:00-2024/07/13 15:00",
				"2024/07/13 15:00-2024/07/15 15:00",
				"2024/07/15 15:00-2024/07/16 15:00",
				"2024/07/16 15:00-2024/07/17 15:00",
				"2024/07/17 15:00-2024/07/18 15:00",
				"2024/07/18 15:00-2024/07/19 15:00",
				"2024/07/19 15:00-2024/07/20 15:00",
				"2024/07/20 15:00-2024/07/22 15:00"
			];

			var links = [
				"http://survey.emoney.cn/vm/YlZYb9A.aspx",
				"http://survey.emoney.cn/vm/wFq6YZh.aspx",
				"http://survey.emoney.cn/vm/Q02qK8V.aspx",
				"http://survey.emoney.cn/vm/QkqKgd2.aspx",
				"http://survey.emoney.cn/vm/YMb4ENY.aspx",
				"http://survey.emoney.cn/vm/Q8qKVp2.aspx",
				"http://survey.emoney.cn/vm/YDb4piY.aspx",
				"http://survey.emoney.cn/vm/hS7a8yt.aspx",
				"http://survey.emoney.cn/vm/eDFOrEY.aspx",
				"http://survey.emoney.cn/vm/hE8M47a.aspx",
				"http://survey.emoney.cn/vm/mynGKKx.aspx",
				"http://survey.emoney.cn/vm/wFYB2q6.aspx"
			];

			// 获取当前日期和时间
			var currentDate = new Date();
			if(!!thisPage.getQueryString("date")){
				currentDate = new Date(thisPage.getQueryString("date"));
			}
			var surveyUrl = "";
			// 根据当前时间，判断时间范围并获取对应的链接
			for (var i = 0; i < dates.length; i++) {
				var dateRange = dates[i].split('-'); // 将时间范围字符串分割成开始时间和结束时间
				var startTime = new Date(dateRange[0]).getTime(); // 计算开始时间
				var endTime = new Date(dateRange[1]).getTime(); // 计算结束时间
				if (currentDate.getTime() >= startTime && currentDate.getTime() < endTime) {
					surveyUrl = links[i];
					break;
				}
			}
			var retUrl  = surveyUrl + "?sojumpparm=" + encodeURIComponent("ActivityBand$" + loginUser.pid + "$" + loginUser.uid + "$$$$$$$");

			return retUrl;
		},
		//推送cmp
		pushdatatocmp:function (uname, adcode) {
            var data = {
                "appid": '10088',
                "logtype": 'click',
                "mid": '',
                "pid": thisPage.getQueryString("pid"),
                "sid": thisPage.getQueryString("sid"),
                "tid": thisPage.getQueryString("tid"),
                "uid": thisPage.getQueryString("uid"),
                "uname": uname,
                "adcode": adcode,
                "targeturl": "",
                "pageurl": window.top.location.href
            }
            var saasUrl = "https://ds.emoney.cn/saas/queuepush";
            var saasSrc = saasUrl + "?v=" + Math.random()
                + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
                + "&message=" + encodeURIComponent(JSON.stringify(data));

            var elm = document.createElement("img");
            elm.src = saasSrc;
            elm.style.display = "none";
            document.body.appendChild(elm);
		},
		getQueryString:function(name){
			var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
			var r = window.location.search.substr(1).match(reg);
			if (r != null) return unescape(r[2]); return null;
		},
		setCookie:function(name, value){
			var expdate = new Date();
			expdate.setTime(expdate.getTime() + 30 * 60 * 1000); 
			document.cookie = name + "=" + value + ";expires=" + expdate.toGMTString() + ";path=/";
		},
		getCookie: function (c_name) {
			if (document.cookie.length > 0) {
				var c_start = document.cookie.indexOf(c_name + "=")
				if (c_start != -1) {
					c_start = c_start + c_name.length + 1
					var c_end = document.cookie.indexOf(";", c_start)
					if (c_end == -1) c_end = document.cookie.length
					return unescape(document.cookie.substring(c_start, c_end))
				}
			}
			return ""
		},
		//客户端方法
		GetExternal: function () {
			return window.external.EmObj;
		},
		//调用客户端接口
		PC_JH: function (type, c) {
			try {
				var obj = thisPage.GetExternal();
				return obj.EmFunc(type, c);
			} catch (e) {
			}
		},
		//检查用户是否有权限参与
        checkPermission: function () {
           var pidlist = "888020000,888080000,888204010,888224010";
           if (pidlist.indexOf(loginUser.pid) < 0) {
               layer.msg("本活动仅限大师和天玑用户参与");
               return false;
           }
            return true;
        },
		//检查用户是否登录
        checkIsLogin: function () {
            if (isLogin==='0') {
                layer.msg("您还未登录，请在客户端登录后参与活动");
                return false;
            }
            return true;
        },
		isTest:function(){
			var test = thisPage.getQueryString("test");
			return !!test?true:false;
		}
	}
	
	thisPage.Init();
})