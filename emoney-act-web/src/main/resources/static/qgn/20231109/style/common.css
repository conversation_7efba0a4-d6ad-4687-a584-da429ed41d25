@charset "utf-8";

img{ border:0px;}
body {
	font-size:12px; overflow-x:hidden;
	margin:0;
	padding:0;
	line-height:22px;
	color:#fff; background-color: #5D8CE5; font-family:"微软雅黑";
}

.main{width:100px; margin:0 auto; position:relative;}
ul,li {margin:0;padding:0;list-style:none;}
A:link {COLOR: #fff; text-decoration: underline;outline:none}
A:visited {COLOR: #fff; TEXT-DECORATION: none}
A:hover {COLOR:#ffff00; TEXT-DECORATION: none}
input{background:transparent;border:0px;margin:0px; color:#939598;list-style:none; font-family:"黑体"; font-size:14px;}
.white {color:#ffffff;}
.red {color:#bb3230;}
.red2{color:#d30101;}
.green {color:green;}
.black{ color:#000;}
.blue{color:#549FD6;}
.yellow{color:#ffff00;}
.blue3{color:#255E98;}
.gray{color:#6A3906;}
.org{color:#ff5a00;}
.bg_h{background-color:#f0f0f0;}
.f_13{font-size:13px;}
.f_14{font-size:14px;}
.clr2{clear:both; height:1px; overflow:hidden;}
.m_t36{margin-top:36px;}
.m_t20{margin-top:20px;}
.m_t10{margin-top:10px;}
.t_c{text-align:center;}
.f_12{font-size:12px; font-family:"宋体";}
.f_16{font-size:16px;font-weight:bold;}
.f_18{font-size:16px;}
.f_24{font-size:24px;}
.f_30{font-size:28px; color:#603813; line-height:50px; font-weight:bold;}
.f_22{font-size:22px; font-family:"黑体";}
.h340{height:340px;}
.t-r{text-align: right;}
.t-c{text-align: center;}

.fr{float:right;}
.fl{float:left;}
.f1{background-color:#f3f3fd; color:#333; padding:3px 5px;}

.bod{content-visibility: auto;}
#dbg1{background:url("../images/index_01.png") center top;height:754px;}
#dbg2{background:url("../images/index_02.png") center top;height:654px;}
#dbg3{background:url("../images/index_03.png") center top;height:592px;}
#dbg4{background:url("../images/index_04.png") center top;height:911px;}
#dbg5{background:url("../images/index_05.png") center top;height:1286px;}
.pf{background:url("../images/index_06.png") center top;height:148px; width: 100%; position: fixed; left: 0px; bottom: 0px;}

.btn{
	position: absolute;
	left: 140px;
  top: 32px;
	background: url("../images/btn.png") center top;
	width: 478px;
	height: 93px;
}
.btnh{
	position: absolute;
	left: 140px;
	top: 32px;
	background: url("../images/btnh.png") center top;
	width: 478px;
	height: 93px;
}
.pic1{
	position: absolute;
	left: -240px;
  top: 180px;
}
.slider_box { margin: 0px auto; width: 759px; height:334px; overflow: hidden; }

.silder_con {
	height: 334px;
	overflow: hidden;
	position: absolute;
}
.silder_panel {width: 759px; height: 334px; overflow: hidden; float: left; position: relative;}
.silder_nav {
	position: absolute;
	left: -417px;
  top: 203px;
}
.silder_nav li { margin:0 0px 15px 0;background:url("../images/ico1.png"); background-size: 100%; width:190px;height:69px; line-height: 69px;text-align: center; font-size: 24px; color: #6b3e19; overflow: hidden; display: block;}
.silder_nav li.current {background:url("../images/ico1h.png") no-repeat; background-size: 100%;color: #fff;}
.slider_box a.prev {text-indent: -999px; overflow: hidden; display: block; position: absolute; cursor: pointer; font-size: 0px;}
.slider_box a.next {text-indent: -999px; overflow: hidden; display: block; position: absolute; cursor: pointer; font-size: 0px;}

.h{background: url("../images/h.png"); width: 100%; height: 100%; position: fixed; left: 0px; top: 0px; display: none;}
.close{background: url("../images/close.png");width: 65px; height:65px; position: absolute; left:260px; bottom: -100px;}
.tc1{background:url("../images/tc.png"); position: absolute; left: 50%; top: 50%; width: 587px; height: 406px; margin: -203px 0 0 -298px;}

.dh{
	-webkit-animation: dh 0.3s linear infinite alternate;
	animation: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh {
 to {
 -webkit-transform: scale(1.05);
 transform: scale(1.05);
}
}
 @keyframes dh {
 to {
 -webkit-transform: scale(1.05);
 transform: scale(1.05);
}
}

.footer{text-align:center;font-size:24px; padding:20px 0 180px; line-height: 40px;}

/*手机版样式*/
@media (max-width: 768px) {
  #dbg1{background:url("../images/app_01.png") center top no-repeat;height:576px;}
#dbg2{background:url("../images/app_02.png") center top no-repeat;height:463px;}
#dbg3{background:url("../images/app_03.png") center top no-repeat;height:412px;}
#dbg4{background:url("../images/app_04.png") center top no-repeat;height:663px;}
#dbg5{background:url("../images/app_05.png") center top no-repeat;height:1047px;}
.pf{background:url("../images/app_06.png") center top no-repeat;height:197px; width: 100%; position: fixed; left: 0px; bottom: 0px;}
	.btn{
	position: absolute;
	left: -79px;
    top: 106px;
	background: url("../images/btn.png") center top no-repeat; background-size: 100%;
	width: 428px;
	height: 93px;
}
	.btnh{
		position: absolute;
		left: -79px;
		top: 106px;
		background: url("../images/btnh.png") center top no-repeat; background-size: 100%;
		width: 428px;
		height: 93px;
	}
	.footer{text-align:center;font-size: 14px;
    padding: 20px 0 220px;
    line-height: 33px;}
	
	.pic1{
	position: absolute;
	left: -287px;
    top: 193px;
}
.slider_box { margin: 0px auto; width: 673px; height:297px; overflow: hidden; }
	.slider_box img{width: 100%;}
.silder_con {
	height: 297px;
	overflow: hidden;
	position: absolute;
}
.silder_panel {width: 673px; height: 297px; overflow: hidden; float: left; position: relative;}
.silder_nav {
	position: absolute;
	left: -227px;
    top: 129px;
    width: 610px;
}
.silder_nav li { margin:0 10px 0 0;background:url("../images/ico1.png") no-repeat; float: left; background-size: 100%; width:270px;height:69px; line-height: 69px;text-align: center; font-size: 24px; color: #6b3e19; overflow: hidden; display: block;}
}


.bg{background-image:url(../images/h.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt1{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 303px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}

.tc-txt{
	width: 454px;
	position: absolute;
	left: 0%;
	top: 220px; text-align: center; font-size: 16px; color: #000;
}

