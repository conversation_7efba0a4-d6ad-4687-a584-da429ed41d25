html,
body {
    height: 100%;
    margin: 0;
    padding: 0;
    background-color:rgb(143,1,6);
}

/**
滚动条
*/
::-webkit-scrollbar-track-piece {
    -webkit-border-radius: 0;
}

::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-thumb {
    height: 50px;
    background-color: #671e0b;
    border: 0px solid #454545;
    -webkit-border-radius: 4px;
    outline-offset: -2px;
}

::-webkit-scrollbar-thumb:hover {
    height: 50px;
    background-color: #671e0b;
    -webkit-border-radius: 4px;
}
#container {
    width:1300px;
    height:820px;
    overflow:hidden;
    background-image:url("../images/bg.png");
    background-repeat:no-repeat;
    background-position:top center;
    background-size:container;
    margin:auto;
}

/* 顶部 区域 */
.header{
    height:268px;
    position:relative;

}
.header .btn-video-play{
    width:310px;
    height:240px;
    position:absolute;
    right:130px;
    background-image:url("../images/bg-video.png");
    background-repeat:no-repeat;
}

.header .iner{
    height:100%;
    cursor:pointer;
}
.header .btn-play{
    height:63px;
    width:63px;
    position:absolute;
    top:50%;
    left:50%;
    opacity:0.8;

    margin: -50px auto auto -30px;
    background:url("../images/icon-play.png") no-repeat;
}
.header .btn-play:hover{
    opacity:1;
}

.main-mid{margin-top:80px;}

.main-mid .card-pack{
    display:flex;
    text-align:center;
    justify-content:space-between;
    width:930px;
    margin:0 auto;
}
.main-mid .card{
    width:203px;

}
.main-mid .card:hover{


}

.main-mid .card-lock,
.main-mid .card-unlock
{
    width:203px;
    height:266px;
    cursor:pointer;
    position: absolute;

    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;

}

.main-mid .card-unlock{
    transform: rotateY(180deg);
}

.main-mid .iner-box{
    position: relative;
    width:203px;
    height:258px;
    border-radius:10px;
    border:3px solid transparent;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}
.main-mid .iner-box.sel{
    border-color:rgb(135, 206, 255);

}

.main-mid .card.unlock .iner-box{
    transform: rotateY(180deg);
}
.main-mid .card .title-box{
    font-size:16px;
    color:#fff;
    height:50px;
}

.main-mid .card-unlock.card-san{
    background-image:url("../images/san.png")
}
.main-mid .card-unlock.card-bu{
    background-image:url("../images/bu.png")
}
.main-mid .card-unlock.card-qin{
    background-image:url("../images/qin.png")
}
.main-mid .card-unlock.card-long{
    background-image:url("../images/long.png")
}

.main-mid .card-lock.card-san{
    background-image:url("../images/san-h.png")
}
.main-mid .card-lock.card-bu{
    background-image:url("../images/bu-h.png")
}
.main-mid .card-lock.card-qin{
    background-image:url("../images/qin-h.png")
}
.main-mid .card-lock.card-long{
    background-image:url("../images/long-h.png")
}

/* 解锁 */
.btn-footer{
    text-align:center;
}
.btn-unlock{
    border:0 solid transparent;
    width:502px;
    height:90px;
    paddng-bottom:40px;
    line-height:20px;
    font-size:26px;
    cursor:pointer;
    opacity:0.95;
    margin:auto;
    background-color:transparent;
    background-image:url("../images/btn-unlock.png")
}
.btn-unlock:hover{
    opacity:1;
}
.btn-unlock:active{
    opacity:0.96;
}
.footer{padding-top:10px;}
.footer .link-rules{
    height:50px;
    color:#fff;

    text-align:center;
}

/* 活动规则 */
.link-rules{
    text-decoration:underline;
}

.active-rules{
    width:800px;
    height:450px;

}
.layui-layer.active-rules{
    border: 2px solid rgb(255 140 109);
    background-color: #fff;
    color: rgb(143,1,6);
    border-radius: 10px;
}
.active-content{padding:20px;}

/*.layui-layer.active-rules {*/
/*    !*height:100%;*!*/
/*    border-radius:8px;*/
/*    overflow:hidden;*/
/*    overflow-y:scroll;*/
/*}*/

.layui-layer.video-window .layui-layer-content {
    height:calc(100% - 50px);
}

.gold{
    color:#fcd9ab;
}