$(function(){
	var sWidth = $(".slider_name").width();
	var len = $(".slider_name .clearfix").length;
	var index = 0;
	var picTimer;
	
	var btn = "<a class='prev'>Prev</a><a class='next'>Next</a>";
	$(".slider_name").append(btn);

	$(".silder_nav li").css({"opacity":"1","filter":"alpha(opacity=100)"}).mouseenter(function() {																		
		index = $(".silder_nav li").index(this);
		showPics(index);
	}).eq(0).trigger("mouseenter");

	$(".slider_name .prev,.slider_name .next").css({"opacity":"1","filter":"alpha(opacity=100)"}).hover(function(){
		$(this).stop(true,false).animate({"opacity":"1","filter":"alpha(opacity=100)"},300);
	},function() {
		$(this).stop(true,false).animate({"opacity":"1","filter":"alpha(opacity=100)"},300);
	});


	// Prev
	$(".slider_name .prev").click(function() {
		index -= 1;
		if(index == -1) {index = len - 1;}
		showPics(index);
	});

	// Next
	$(".slider_name .next").click(function() {
		index += 1;
		if(index == len) {index = 0;}
		showPics(index);
	});

	// 
	$(".slider_name .silder_con").css("width",sWidth * (len));
	
	// mouse 
	$(".slider_name").hover(function() {
		clearInterval(picTimer);
	},function() {
		picTimer = setInterval(function() {
			showPics(index);
			index++;
			if(index == len) {index = 0;}
		},3000); 
	}).trigger("mouseleave");
	
	// showPics
	function showPics(index) {
		var nowLeft = -index*sWidth; 
		$(".slider_name .silder_con").stop(true,false).animate({"left":nowLeft},300);
		$(".silder_nav li").removeClass("current").eq(index).addClass("current"); 
		$(".silder_nav li").stop(true,false).animate({"opacity":"1"},300).eq(index).stop(true,false).animate({"opacity":"1"},300);
	}
});



$(function(){
	var sWidth = $(".slider_name2").width();
	var len = $(".silder_panel2").length;
	var index = 0;
	var picTimer;
	
	var btn = "<a class='prev'>Prev</a><a class='next'>Next</a>";
	$(".slider_name2").append(btn);

	$(".silder_nav2 li").css({"opacity":"1","filter":"alpha(opacity=100)"}).mouseenter(function() {																		
		index = $(".silder_nav2 li").index(this);
		showPics(index);
	}).eq(0).trigger("mouseenter");

	$(".slider_name2 .prev,.slider_name2 .next").css({"opacity":"1","filter":"alpha(opacity=100)"}).hover(function(){
		$(this).stop(true,false).animate({"opacity":"1","filter":"alpha(opacity=100)"},300);
	},function() {
		$(this).stop(true,false).animate({"opacity":"1","filter":"alpha(opacity=100)"},300);
	});


	// Prev
	$(".slider_name2 .prev").click(function() {
		index -= 1;
		if(index == -1) {index = len - 1;}
		showPics(index);
	});

	// Next
	$(".slider_name2 .next").click(function() {
		index += 1;
		if(index == len) {index = 0;}
		showPics(index);
	});

	// 
	$(".silder_con2").css("width",sWidth * (len));
	
	// mouse 
	$(".slider_name2").hover(function() {
		clearInterval(picTimer);
	},function() {
		picTimer = setInterval(function() {
			showPics(index);
			index++;
			if(index == len) {index = 0;}
		},3000); 
	}).trigger("mouseleave");
	
	// showPics
	function showPics(index) {
		var nowLeft = -index*sWidth; 
		$(".silder_con2").stop(true,false).animate({"left":nowLeft},300);
		$(".silder_nav2 li").removeClass("current").eq(index).addClass("current"); 
		$(".silder_nav2 li").stop(true,false).animate({"opacity":"1"},300).eq(index).stop(true,false).animate({"opacity":"1"},300);
	}
});