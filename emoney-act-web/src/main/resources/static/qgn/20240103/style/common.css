@charset "utf-8";

img{ border:0px;}
body {
	font-size:12px; overflow-x:hidden;
	margin:0;
	padding:0;
	line-height:22px;
	color:#BE5921;
	background-color:#BF1D1D;font-family:"微软雅黑";
}

.main{width:100px; margin:0 auto; position:relative;}
ul,li {margin:0;padding:0;list-style:none;}
A:link {TEXT-DECORATION: none;color:#fff;}
A:visited {TEXT-DECORATION: none;color:#fff;}
A:hover {TEXT-DECORATION: none;color:#ffff00;}
A.b:link {COLOR: #FFD334; TEXT-DECORATION: underline}
A.b:visited {COLOR: #FFD334; TEXT-DECORATION: underline}
A.b:hover {COLOR: #ffff00; TEXT-DECORATION: none}
input{background:transparent;border:0px;margin:0px; color:#939598;list-style:none; font-family:"微软雅黑"; font-size:14px;}
.white {color:#ffffff;}
.red {color:#0682f2; font-size:25px;line-height:50px;}
.red2{color:#d30101;}
.green {color:green;}
.black{ color:#000;}
.blue{color:#549FD6;}
.yellow{color:#ffff00;}
.blue3{color:#255E98;}
.gray{color:#6A3906;}
.org{color:#ff5a00;}
.bg_h{background-color:#f0f0f0;}
.f_13{font-size:13px;}
.f_14{font-size:14px;}
.clr2{clear:both; height:1px; overflow:hidden;}
.m_t36{margin-top:36px;}
.m_t20{margin-top:20px;}
.m_t10{margin-top:10px;}
.t_c{text-align:center;}
.f_12{font-size:12px; font-family:"宋体";}
.f_16{font-size:16px;font-weight:bold;}
.f_17{font-size:17px;}
.f_26{font-size:26px; font-family:"微软雅黑"; line-height:50px;}
.f_30{font-size:28px; color:#603813; line-height:50px; font-weight:bold;}
.f_22{font-size:22px; font-family:"微软雅黑";}
.h340{height:340px;}
td{font-size:18px;text-align:center; background-color:#fff;}

.bt { font-weight:bold;color: #4d83f3;	font-size: 30px;}
.bt2{color: #4d83f3;	font-size: 21px;}
.f-r{float:right;}
.f-l{float:left;}
.f1{background-color:#f3f3fd; color:#333; padding:3px 5px;}

#dbg1{background:url("../images/index_01.jpg") center top no-repeat;height:253px;}
#dbg2{background:url(../images/index_02.jpg) center top no-repeat;height:252px;}
#dbg3{background:url(../images/index_03.jpg) center top no-repeat;height:253px;}
#dbg4{background:url(../images/index_04.png) center top no-repeat;height:965px;}
#dbg5{background:url(../images/index_05.png) center top no-repeat;height:453px;}
#dbg6{background:url(../images/index_06.jpg) center top no-repeat;height:433px;}
#dbg7{background:url(../images/index_07.png) center top no-repeat;height:648px;}
#dbg8{background:url(../images/index_08.png) center top no-repeat;height:766px;}
#dbg9{background:url(../images/index_09.png) center top no-repeat;height:687px;}
#dbg10{background:url(../images/index_10.png) center top no-repeat;height:544px;}
#dbg11{background:url(../images/index_11.png) center top no-repeat;height:628px; overflow: hidden;}

.pf{background:url("../images/index_12.png") center top no-repeat; height: 117px; width: 100%; position: fixed; left: 0px; bottom: 0px; display: none;}
.pf2{background:url("../images/pf.png") center top no-repeat; width:179px; height:331px; position: fixed; right: 0px; top:10%;}
.pf3{background:url("../images/pf2.png") center top no-repeat;width:194px; height:56px;  position: fixed; left: 0px; top:10%;}
.qp .btn1{
	background: url("../images/btn1.png");
	position: absolute;
	left: 94px;
	top: 224px;
	width: 403px;
	height: 88px;
}
.qp .btn5{
	background: url("../images/btn5.png");
	position: absolute;
	left: 131px;
  top: 54px;
	width: 199px;
	height:67px;
}
.qph .btn1{
	background: url("../images/btn1h.png");
	position: absolute;
	left: 94px;
	top: 224px;
	width: 403px;
	height: 88px;
}
.qph .btn5{
	background: url("../images/btn5h.png");
	position: absolute;
	left: 131px;
  top: 54px;
	width: 199px;
	height:67px;
}

.bod .btn2{
	background: url("../images/btn2.png");
	position: absolute;
	left: -10px;
  top: 192px;
	width:109px;
	height:54px;
}
.bod .btn3{
	background: url("../images/btn3.png");
	position: absolute;
	left: -408px;
	top: 224px;
	width: 403px;
	height: 88px;
}

.bod .btn4{
	background: url("../images/btn4.png");
	position: absolute;
	left: -236px;
	top: 54px;
	width: 198px;
	height:67px;
}
.bo .btn2{
	background: url("../images/btn2h.png");
	position: absolute;
	left: -10px;
  top: 192px;
	width:109px;
	height:54px;
}
.bo .btn3{
	background: url("../images/btn3h.png");
	position: absolute;
	left: -408px;
	top: 224px;
	width: 403px;
	height: 88px;
}

.bo .btn4{
	background: url("../images/btn4h.png");
	position: absolute;
	left: -236px;
	top: 54px;
	width: 198px;
	height:67px;
}
.pfx{position: absolute; left: 540px; top: -7px; background: url("../images/close.png") center top no-repeat;
	width: 30px;
	height: 30px;}
.close{
	background: url("../images/close.png") center top no-repeat;
	width: 30px;
	height: 30px;
	position: absolute;
	top: 0;
	right: 0;
}
.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/h.png); display: none}
.tc{background-image:url(../images/tc3.png); 
	width: 679px;
	height:656px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -328px 0 0 -339px;
}
.tc_qgn{background-image:url(../images/tc.png);
	width: 679px;
	height:656px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -328px 0 0 -339px;
}
.server{position:fixed; right:0; top:20%; text-align: center; color: #fff; font-size: 16px; line-height: 25px;}
.server li{ margin-bottom:10px; width:157px; overflow:hidden;}
.bod .dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.qp .dh3{
  -webkit-animation: dh3 0.3s linear infinite alternate;
  animation-name: dh3 0.3s linear infinite alternate;
}
@-webkit-keyframes dh3{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh3{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.dh2{
  -webkit-animation: dh2 1.5s linear infinite alternate;
  animation-name: dh2 1.5s linear infinite alternate;
}
@-webkit-keyframes dh2 {
  0%, 10%,20%, 100% {
    opacity: .6;
  }

  5%, 15% {
    opacity: 0;
  }
}

@keyframes dh2 {
  0%, 10%,20%, 100% {
    opacity: .6;
  }

  5%, 15% {
    opacity: 0;
  }
}
@-webkit-keyframes wobble2 {
  0% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  15% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  30% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  45% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  60% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  75% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  100% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }
}

@keyframes wobble2 {
  0% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  15% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  30% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  45% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  60% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  75% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  100% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }
}

.wobble2 {
  -webkit-animation-name: wobble2;
          animation-name: wobble2;
		  -webkit-transform-origin: center bottom;
	-moz-transform-origin: center bottom;
	-ms-transform-origin: center bottom;
	-o-transform-origin: center bottom;
	transform-origin: center bottom;
	-webkit-animation-duration: 3s;
	-o-animation-duration: 3s;
	animation-duration: 3s;
	-webkit-animation-timing-function: ease-in;
	-o-animation-timing-function: ease-in;
	animation-timing-function: ease-in;
	-webkit-animation-delay: 0s;
	-o-animation-delay: 0s;
	animation-delay: 0s;
	-webkit-animation-iteration-count: infinite;
	-o-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	-webkit-animation-direction: normal;
	-o-animation-direction: normal;
	
	-webkit-animation-fill-mode: both;
	-o-animation-fill-mode: both;
	animation-fill-mode: both;
}
.pic1{
	position: absolute;
	left: -423px;
	top: 199px;
}

.hdgz{margin: 160px auto 0;  width: 860px; color: #fffab6; font-size: 19px; line-height: 30px;}
.hdgz span{ background-color: #fffab6; line-height: 28px; border-top-left-radius: 12px;border-bottom-right-radius: 12px; padding: 0 10px; color: #c30d23; margin-right: 10px;}
.hdgz .z{color: #ff8d72}

.slider_box { margin: 0px auto; width: 952px; height:678px; overflow: hidden; }

.silder_con {
	height: 678px;
	overflow: hidden;
	position: absolute;
}
.silder_panel {width: 952px; height:678px; overflow: hidden; float: left; position: relative;}
.silder_nav {
	position: absolute;
	left: -423px;
	top: 123px;
	width: 948px;
}
.silder_nav li { margin:0 0px 15px 0;background:url("../images/ico1.png"); background-size: 100%; width:316px;height:68px; line-height: 68px;text-align: center; font-size:28px; color: #fffab6; overflow: hidden; display: block; float: left;}
.silder_nav li.current {background:url("../images/ico1h.png") no-repeat; background-size: 100%;color: #fffab6;}
.slider_box a.prev {text-indent: -999px; overflow: hidden; display: block; position: absolute; cursor: pointer; font-size: 0px;}
.slider_box a.next {text-indent: -999px; overflow: hidden; display: block; position: absolute; cursor: pointer; font-size: 0px;}


.footer{text-align:center; font-family:"宋体"; font-size:12px; padding:0px 0 150px; color: #fff;}

.overbg{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url("../images/h.png"); color: #fff; text-align: center; line-height:500px; font-size: 40px; display: none}