$(function(){
	'use strict';
	var Urls = www;
	var isLogin = $("#hid_isLogin").val();
	var loginUser = {
		"uid": $("#hid_uid").val(),
		"pid": $("#hid_pid").val(),
		"mobileX": $("#hid_mobilex").val(),
		"maskMobile": $("#hid_maskmobile").val(),
		"point":$("#hid_allpoint").val()
	}
	var configData = {
		"actCode": $("#hid_actcode").val(),
		"Code_qgn":"AC588Pop20240103",
		"Code_qp":"ACBandPop20240103",
		"cmp_qgn":"AC588Pop20231121",
		"cmp_qp":"ACBandPop20231121",
		"actApi":Urls,
		"staticPath":$("#hid_staticPath").val()
	}
	var sso = location.search.slice(1);
	var thisPage = {
		Init:function(){
			thisPage.scrollFun();
			thisPage.bindEvents();
			thisPage.isSubmit(configData.Code_qgn+","+configData.Code_qp);
		},
		scrollFun:function(){
			$(window).scroll(function(e){
				if($(window).scrollTop() >= 740){ 
			        $('.pf').fadeIn(300); 
			    }else{    
			        $('.pf').fadeOut(300); 
				}
			});
		},
		//绑定事件
		bindEvents: function () {
			//点击抢功能
			$(".popupD").click(function(){
				//已领取
				if($(".bo").length>0){
					return false;
				}
				var date = thisPage.getQueryString("date");
				
				thisPage.addQgn(configData.Code_qgn,date,function(){
					$(".h").show();
					$(".tc_qgn").show();
					$(".bod").attr("class","bo");
				});
				thisPage.pushdatatocmp('',configData.cmp_qgn);
			});
			//点击抢票
			$(".popupD2").click(function(){
				//已领取
				if($(".qph").length>0){
					return false;
				}
				
				thisPage.addCount(configData.Code_qp,function(){
					$(".h").show();
					$(".tc").show();
					$(".qp").attr("class","qph");
				});
				thisPage.pushdatatocmp('',configData.cmp_qp);
			})
			//随堂测
			$(".pf3").click(function(){
				$(this).attr("target","_blank");
				$(this).attr("href",thisPage.getSurveyUrl() + "?" +sso);
			});
			
			$(".close").click(function(){
				$(".h,.tc,.tc_qgn").hide();
			});
		},
		//抢攻能
		addQgn:function (accode,date,callback){
			if (!thisPage.checkIsLogin()) {
				return false;
			}
			if (!thisPage.checkPermission()) {
				return false;
			}
			$.ajax({
				type: 'get',
				url: configData.actApi + '/qgn/getdyjj',
				dataType: 'json',
				data: {
					actcode:accode,
					uid: loginUser.uid ,
					mobilex:loginUser.mobileX,
					date:date
				},
				success: function (data) {
					if(data.code=="200"){
						callback && callback();
					}
				}
			});
		},
		//计数
		addCount:function(accode,callback){
			var nowtime = new Date().getTime();
			if (!thisPage.checkIsLogin()) {
				return false;
			}
			if (!thisPage.checkPermission()) {
				return false;
			}
			$.ajax({
				type: 'get',
				url: configData.actApi + '/user/addcountbyactcode?actcode=' + accode,
				dataType: 'jsonp',
				data: {
					uid: loginUser.uid ,
					value: nowtime
				},
				success: function (data) {
					if(data.code=="200"){
						callback && callback();
					}
				}
			});
		},
		//是否参与过
		isSubmit:function(accode){
			if (!thisPage.checkIsLogin()) {
				return false;
			}
			if (!thisPage.checkPermission()) {
				return false;
			}
			$.ajax({
				type: 'get',
				url: configData.actApi + "/user/issubmitbyactcodes?actcodes=" + accode,
				dataType: 'jsonp',
				data: {
					uid: loginUser.uid
				},
				success: function (data) {
					if (data.code == '200') {
						var num = "";
						if (data.data) {
							num = data.data.split(',')
						}
						if(num.length>1){
							var qgn = num[0];
							var qp = num[1];
							if(!!qgn){
								$(".bod").attr("class","bo");
							}
							if(!!qp){
								$(".qp").attr("class","qph");
							}
						}
					}
				}
			});
		},
		//获取随堂测
		getSurveyUrl:function () {
			var dates = [
				"2023/12/28 17:00-2024/01/04 17:00",
				"2024/01/04 17:00-2024/01/05 17:00",
				"2024/01/05 17:00-2024/01/08 17:00",
				"2024/01/08 17:00-2024/01/09 17:00",
				"2024/01/09 17:00-2024/01/10 17:00",
				"2024/01/10 17:00-2024/01/11 17:00",
				"2024/01/11 17:00-2024/01/12 17:00",
				"2024/01/12 17:00-2024/01/13 17:00",
				"2024/01/13 17:00-2024/01/14 17:00"
			];

			var links = [
				"http://survey.emoney.cn/vm/Q0YTXu8.aspx",
				"http://survey.emoney.cn/vm/ha5Bz4b.aspx",
				"http://survey.emoney.cn/vm/enHfDjV.aspx",
				"http://survey.emoney.cn/vm/thfxbwl.aspx",
				"http://survey.emoney.cn/vm/hcb5B4V.aspx",
				"http://survey.emoney.cn/vm/tVlc1ew.aspx",
				"http://survey.emoney.cn/vm/h4Qtb5i.aspx",
				"http://survey.emoney.cn/vm/Q5TXiYr.aspx",
				"http://survey.emoney.cn/vm/tVlvm4w.aspx"
			];

			// 获取当前日期和时间
			var currentDate = new Date();
			if(!!thisPage.getQueryString("date")){
				currentDate = new Date(thisPage.getQueryString("date"));
			}
			var surveyUrl = "";
			// 根据当前时间，判断时间范围并获取对应的链接
			for (var i = 0; i < dates.length; i++) {
				var dateRange = dates[i].split('-'); // 将时间范围字符串分割成开始时间和结束时间
				var startTime = new Date(dateRange[0]).getTime(); // 计算开始时间
				var endTime = new Date(dateRange[1]).getTime(); // 计算结束时间
				if (currentDate.getTime() >= startTime && currentDate.getTime() < endTime) {
					surveyUrl = links[i];
					break;
				}
			}
			return surveyUrl;
		},
		//推送cmp
		pushdatatocmp:function (uname, adcode) {
            var data = {
                "appid": '10088',
                "logtype": 'click',
                "mid": '',
                "pid": thisPage.getQueryString("pid"),
                "sid": thisPage.getQueryString("sid"),
                "tid": thisPage.getQueryString("tid"),
                "uid": thisPage.getQueryString("uid"),
                "uname": uname,
                "adcode": adcode,
                "targeturl": "",
                "pageurl": window.top.location.href
            }
            var saasUrl = "https://ds.emoney.cn/saas/queuepush";
            var saasSrc = saasUrl + "?v=" + Math.random()
                + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
                + "&message=" + encodeURIComponent(JSON.stringify(data));

            var elm = document.createElement("img");
            elm.src = saasSrc;
            elm.style.display = "none";
            document.body.appendChild(elm);
		},
		getQueryString:function(name){
			var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
			var r = window.location.search.substr(1).match(reg);
			if (r != null) return unescape(r[2]); return null;
		},
		setCookie:function(name, value){
			var expdate = new Date();
			expdate.setTime(expdate.getTime() + 30 * 60 * 1000); 
			document.cookie = name + "=" + value + ";expires=" + expdate.toGMTString() + ";path=/";
		},
		getCookie: function (c_name) {
			if (document.cookie.length > 0) {
				var c_start = document.cookie.indexOf(c_name + "=")
				if (c_start != -1) {
					c_start = c_start + c_name.length + 1
					var c_end = document.cookie.indexOf(";", c_start)
					if (c_end == -1) c_end = document.cookie.length
					return unescape(document.cookie.substring(c_start, c_end))
				}
			}
			return ""
		},
		//客户端方法
		GetExternal: function () {
			return window.external.EmObj;
		},
		//调用客户端接口
		PC_JH: function (type, c) {
			try {
				var obj = thisPage.GetExternal();
				return obj.EmFunc(type, c);
			} catch (e) {
			}
		},
		//检查用户是否有权限参与
        checkPermission: function () {
//            var pidlist = "888020000,888080000,888020400";
//            if (pidlist.indexOf(pid) < 0) {
//                layer.msg("本活动仅限大师用户参与");
//                return false;
//            }
            return true;
        },
		//检查用户是否登录
        checkIsLogin: function () {
            if (isLogin==='0') {
                layer.msg("您还未登录，请在客户端登录后参与活动");
                return false;
            }
            return true;
        },
		isTest:function(){
			var test = thisPage.getQueryString("test");
			return !!test?true:false;
		}
	}
	
	thisPage.Init();
})