@charset "utf-8";

img{ border:0px;}
body {
	font-size:12px; overflow-x:hidden;
	margin:0;
	padding:0;
	line-height:22px;
	color:#333;
	background-color:#FAE8C6;font-family:"微软雅黑";
}

.main{width:100px; margin:0 auto; position:relative;}
ul,li {margin:0;padding:0;list-style:none;}
A:link {TEXT-DECORATION: none;color:#333;}
A:visited {TEXT-DECORATION: none;color:#333;}
A:hover {TEXT-DECORATION: none;color:#000;}
A.b:link {COLOR: #FFD334; TEXT-DECORATION: underline}
A.b:visited {COLOR: #FFD334; TEXT-DECORATION: underline}
A.b:hover {COLOR: #ffff00; TEXT-DECORATION: none}
input{background:transparent;border:0px;margin:0px; color:#939598;list-style:none; font-family:"微软雅黑"; font-size:14px;}
.white {color:#ffffff;}
.red {color:#0682f2; font-size:25px;line-height:50px;}
.red2{color:#d30101;}
.green {color:green;}
.black{ color:#000;}
.blue{color:#549FD6;}
.yellow{color:#ffff00;}
.blue3{color:#255E98;}
.gray{color:#6A3906;}
.org{color:#ff5a00;}
.bg_h{background-color:#f0f0f0;}
.f_13{font-size:13px;}
.f_14{font-size:14px;}
.clr2{clear:both; height:1px; overflow:hidden;}
.m_t36{margin-top:36px;}
.m_t20{margin-top:20px;}
.m_t10{margin-top:10px;}
.t_c{text-align:center;}
.f_12{font-size:12px; font-family:"宋体";}
.f_16{font-size:16px;font-weight:bold;}
.f_17{font-size:17px;}
.f_26{font-size:26px; font-family:"微软雅黑"; line-height:50px;}
.f_30{font-size:28px; color:#603813; line-height:50px; font-weight:bold;}
.f_22{font-size:22px; font-family:"微软雅黑";}
.h340{height:340px;}
td{font-size:18px;text-align:center; background-color:#fff;}

.bt { font-weight:bold;color: #4d83f3;	font-size: 30px;}
.bt2{color: #4d83f3;	font-size: 21px;}
.f-r{float:right;}
.f-l{float:left;}
.f1{background-color:#f3f3fd; color:#333; padding:3px 5px;}

#dbg1{background:url("../images/index_01.jpg") center top no-repeat;height:343px;}
#dbg2{background:url(../images/index_02.png) center top no-repeat;height:394px;}
#dbg3{background:url(../images/index_03.png) center top no-repeat;height:469px;}
#dbg4{background:url(../images/index_04.png) center top no-repeat;height:899px;}
#dbg5{background:url(../images/index_05.png) center top no-repeat;height:542px;}
#dbg6{background:url(../images/index_06.png) center top no-repeat;height:537px;}
#dbg7{background:url(../images/index_07.png) center top no-repeat;height:1034px;}
#dbg8{background:url("../images/index_08.png") center top no-repeat;height:950px;}

.pf{background:url("../images/pf.png") center top no-repeat; height: 228px; width: 100%; position: fixed; left: 0px; bottom: 0px; display: none;}
.bod .btn1{
	background: url("../images/btn1.png");
	position: absolute;
	left: 39px;
  top: 351px;
	width:256px;
	height:82px;
}
.bod .btn2{
	background: url("../images/btn2.png");
	position: absolute;
	left: -204px;
	top: 140px;
	width: 525px;
	height: 191px;
}
.bod .btn4{
	background: url("../images/btn4.png");
	position: absolute;
	left: 231px;
  top: 98px;
	width: 396px;
	height: 114px;
}
.bo .btn1{
	background: url("../images/btn1h.png");
	position: absolute;
	left: 39px;
  top: 351px;
	width:256px;
	height:82px;
}
.bo .btn2{
	background: url("../images/btn2h.png");
	position: absolute;
	left: -204px;
	top: 140px;
	width: 525px;
	height: 191px;
}
.bo .btn4{
	background: url("../images/btn4h.png");
	position: absolute;
	left: 231px;
  top: 98px;
	width: 396px;
	height: 114px;
}
.pf2{background:url("../images/pf2.png") center top no-repeat; width:346px; height:400px; position: fixed; right: 0px;top: 20%;}

.an1{
	position: absolute;
	left: -75px;
	top: 95px;
}
.an2{
	position: absolute;
	left: 329px;
	top: 929px;
}
.an3{
	position: absolute;
	left: 329px;
	top: 809px;
}
.pfx{position: absolute; left: 316px;  top: -24px; background: url("../images/close.png") center top no-repeat;
	width: 25px;
	height: 25px;}
.close{
	background: url("../images/close.png") center top no-repeat;
	width: 25px;
	height: 25px;
	position: absolute;
	top: -30px;
	right:-30px;
}
.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/h.png);}
.tc_qgn{background-image:url(../images/tc.png);
	width: 500px;
	height:333px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -166px 0 0 -250px;
}
.tc1{background-image:url("../images/tc1.png"); 
	width: 500px;
	height:333px;
	position: absolute;
	left: 50%;
	top: 50%; margin:-166px 0 0 -250px;
}
.tc2{background-image:url(../images/tc2.png); 
	width: 499px;
	height:243px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -121px 0 0 -250px;
}
.tc3{background-image:url(../images/tc3.png); 
	width: 499px;
	height:253px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -126px 0 0 -250px;
}
.tc_btn1{background-image:url("../images/tc-btn1.png"); 
	width: 195px;
	height:56px;
	position: absolute;
	left: 152px;
  top: 184px;
}
.tc_btn2{background-image:url("../images/tc-btn2.png"); 
	width: 174px;
	height:50px;
	position: absolute;
	left: 301px;
  top: 252px;
}

.bod .dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}


.al1{
	position: absolute;
	left: -371px;
	top: 465px;
}
.slider_box {
	position: absolute;
	top: 0px;
	left: 0px;
	width: 847px;
	height: 612px;
	overflow: hidden;border-radius: 10px;
}
.slider_box2 {
	position: absolute;
	top: 0px;
	left: 0px;
	width: 847px;
	height: 612px;
	overflow: hidden;
}
.silder_con,.silder_con2 {
	height: 612px;
	overflow: hidden;
	position: absolute;
	top: 0px;
}
.silder_panel,.silder_panel2 {width: 847px; height:612px; overflow: hidden; float: left; position: relative; }
.silder_nav{
	position: absolute;
	top: -63px;
	left: 20px;
	width: 856px;
	overflow: hidden;
	float: left;
	margin-right: 15px;
}
.silder_nav li{width:256px;height: 64px; line-height: 64px; text-align: center; font-size: 32px; color: #7A221C; overflow: hidden; display: block;background: url("../images/bg2.png") center top no-repeat; margin-bottom: 40px;cursor:pointer; float: left; margin-right: 17px;}
.silder_nav li.current{background: url("../images/bg.png") center top no-repeat;color: #fff; font-weight: bold;}
.slider_box a.prev,.slider_box2 a.prev {text-indent: -999px; overflow: hidden; display: block; position: absolute; cursor: pointer; }
.slider_box a.next,.slider_box2 a.next {text-indent: -999px; overflow: hidden; display: block; position: absolute; cursor: pointer; }


.footer{ padding: 40px 0 220px 0;
	text-align: center;
	font-family: "宋体";
	font-size: 12px;
}

.bg{background-image:url(../images/h.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.bg .tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.bg .tc .bt1{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.bg .tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.bg .tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.bg .tc .zj{letter-spacing:16px;}
.bg .tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.bg .tc .yzm:hover{background-color: #f4f4f4;}
.bg .tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.bg .tc .tc-btn1:hover{ background-color: #3689ce;}
.bg .tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.bg .tc .tc-btn2:hover{ background-color: #cecece;}
input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 34px;
	line-height: 34px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}
.tc-txt{
	width: 454px;
	position: absolute;
	left: 0%;
	top: 220px; text-align: center; font-size: 16px; color: #000;
}
