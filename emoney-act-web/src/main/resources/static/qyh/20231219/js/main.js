$(function () {
    'use strict';
    var Urls = www;
    var isLogin = $("#hid_isLogin").val();
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val()
    }
    var configData = {
        "actCode": $("#hid_actcode").val(),
        "cmpCode": "ACBandPop20231206",
        "type":3,
        "staticPath":$("#hid_staticPath").val()
    }

    var thisPage = {
        init: function () {
            thisPage.bindEvents();

            if (isLogin === '1') {
                if(loginUser.pid==="888020000"){ $(".txt1").html("38800");$(".txt2").html("该价格适用于大师深度资金版用户");	}
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
            } else {
                $(".bg").show();
                $(".logintc").show();
            }
        },
        //绑定事件
        bindEvents: function () {
            //点击支付
            $(".btn1,.btn2,.btn3,.pf").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }

                var url = thisPage.getPayUrl();

                //客户端
                if (!!thisPage.GetExternal()) {
                    thisPage.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }

                thisPage.pushdatatocmp(loginUser.uid, configData.cmpCode);
            });
            $('.n1 a').click(function () {
                var index = $(this).index();
                $(".sx").hide();
                $('.a'+(index+1)).show();

                $('.al1').hide();
                $('.al1').attr('src','about:blank');
                $(".n1").css("background-image", "url('"+configData.staticPath+"static/qyh/20231219/images/n"+(index+1)+".png')");
            });

            $('.a1').click(function () {
                $('.a1').hide();
                $('.al1').show();
                $('.al1').attr('src','https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=32f207d517c14216bc02392235181847&rep=1&py=1')
            })
            $('.a2').click(function () {
                $('.a2').hide();
                $('.al1').show();
                $('.al1').attr('src','https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=152e85c5426c4ed1802c779b0ff67c3a&rep=1&py=1')
            })
            $('.a3').click(function () {
                $('.a3').hide();
                $('.al1').show();
                $('.al1').attr('src','https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=184d2ee74f39431b8335b304fa3e0e60&rep=1&py=1')
            })
            $('.a4').click(function () {
                $('.a4').hide();
                $('.al1').show();
                $('.al1').attr('src','https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=46730ab914fa45b1ab83b8629155d277&rep=1&py=1')
            })
            $('.a5').click(function () {
                $('.a5').hide();
                $('.al1').show();
                $('.al1').attr('src','https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=8182f1ac8a2f40ab89bf7f359f72b525&rep=1&py=1')
            })
            $('.a6').click(function () {
                $('.a6').hide();
                $('.al1').show();
                $('.al1').attr('src','https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=965d9452aa0f4af581bb9608bd322a71&rep=1&py=1')
            })

            //关闭
            $(document).on('click', '.close', function () {
                $(".bg").hide();
                $(".tc3").hide();
            });
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888204010,888224010&groupCode=0&resourcestypeid=234&resourcesid=1244834&businessType=biztyp-tjb";
            return url;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            // if (pid != "888010000" && pid != "888010400") {
            //     layer.msg("本活动仅限小智盈用户参与");
            //     return false;
            // }
            return true;
        },
        //推送cmp
        pushdatatocmp: function (uname, adcode) {
            var data = {
                "appid": '10088',
                "logtype": 'click',
                "mid": '',
                "pid": thisPage.getQueryString("pid"),
                "sid": thisPage.getQueryString("sid"),
                "tid": thisPage.getQueryString("tid"),
                "uid": thisPage.getQueryString("uid") || thisPage.getQueryString("UPcid"),
                "uname": uname,
                "adcode": adcode,
                "targeturl": "",
                "pageurl": window.top.location.href
            }
            var saasUrl = "https://ds.emoney.cn/saas/queuepush";
            var saasSrc = saasUrl + "?v=" + Math.random()
                + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
                + "&message=" + encodeURIComponent(JSON.stringify(data));

            var elm = document.createElement("img");
            elm.src = saasSrc;
            elm.style.display = "none";
            document.body.appendChild(elm);
        },
        //时间戳toDate
        timestampToDate: function (timestamp) {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = date.getMonth() + 1; // getMonth() 返回的月份从 0 开始，所以需要加 1
            const day = date.getDate();

            return `${year}年${month}月${day}日`;
        },
        getQueryString: function (name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        },
        GetExternal: function () {
            return window.external.EmObj;
        },

        //调用客户端接口
        PC_JH: function (type, c) {
            try {
                var obj = thisPage.GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {
            }
        }
    }
    thisPage.init();
});