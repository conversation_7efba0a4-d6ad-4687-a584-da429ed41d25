$(function () {
    'use strict';
    var isLogin = $("#hid_isLogin").val();
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val()
    }
    var configData = {
        "actCode": $("#hid_actcode").val(),
        "cmpCode": "DEPOSIT20241017171906",
        "apiUrl":"https://empc.emoney.cn/",
        "lotteryActCode":"202410183",
        "syCountCode":"qyh20241113",
        "baseCount":999
    }
    let bRotate = false;
    var isLottoryFlag = true;
    const angleConfig = {
        "15天使用期": 0,
        "30天使用期": 55,
        "90天使用期": 110,
        "1000积分": 170,
        "180天使用期": 235,
        "500积分": 300
    };

    var thisPage = {
        init: function () {
            this.bindEvents();
            if (isLogin === '1') {
                if (!this.checkPermission(loginUser.pid)) {
                    return false;
                }
                thisPage.getSyCount();
                thisPage.getRaffleTimes(function (data){
                    $("#rft_num").html(data.detail);
                });
                thisPage.getLotteryLog();
                thisPage.getMyPrize();
            } else {
                $(".bg").show();
                $(".tc-login").show();
                $(".tc").hide();
            }
        },
        //绑定事件
        bindEvents: function () {
            $(".dh2").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                var url = thisPage.getPayUrl();

                utils.pushdatatocmp(loginUser.uid,configData.cmpCode);
                thisPage.addCount(configData.syCountCode,loginUser.uid);

                //客户端
                if (!!window.utils.GetExternal()) {
                    window.utils.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }
            });
            //dolottery
            $("#btn-lottery").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }

                if(isLottoryFlag){
                    isLottoryFlag = false;
                    thisPage.doLottery(function (data) {
                        if (data.code === 0) {
                            var prizeCode = data.detail;
                            setTimeout(function (){
                                thisPage.getPrizeResult(prizeCode, function (angles,pname) {
                                    thisPage.rotateFn(null,angles,pname,5000);
                                    $("#rft_num").html(parseInt($("#rft_num").html())-1);
                                });
                            },500);
                        } else {
                            layer.msg(data.msg);
                            isLottoryFlag = true;
                        }
                    })
                }
            });
            $(".close").click(function (){
                $(".bg").hide();
                $(".tc").hide();
                thisPage.getMyPrize();
            })
        },
        //剩余席位
        getSyCount:function () {
            utils.request(
                `https://act.emoney.cn/activity/user/getcountbyactcode?actcode=${configData.syCountCode}`).then(res => {
                if (res.code === '200') {
                    var num = 0
                    if (res.data) {
                        num = res.data.split(',')[0]
                    }
                    var usedCount = parseInt(!!num ? num : 0)

                    var syCount = configData.baseCount - usedCount

                    if(syCount<=0){
                        syCount = 0;
                    }
                    $(".djs").html(syCount);
                }
            })
        },
        addCount:function(actcode,uid){
            //名额-1
            $.ajax({
                type: 'get',
                url: 'https://act.emoney.cn/activity/user/addcountbyactcode?actcode=' + actcode,
                dataType: 'jsonp',
                data: {
                    uid: uid ,
                    value: new Date().getTime()
                },
                success: function (data) {
                    if(data.code=="200"){
                        thisPage.getSyCount();
                    }
                }
            });
        },
        //get 抽奖次数
        getRaffleTimes:function (callback){
            var url = configData.apiUrl + "Activity2023/PrizeApi/GetRaffleTimes";
            utils.request(
                `${url}?Emapp-Format=Emoney&phoneEncrypt=${loginUser.mobileX}&code=${configData.lotteryActCode}`,'json').
            then(res => {
                callback && callback(res);
            });
        },
        //抽奖
        doLottery:function (callback){
            var url = configData.apiUrl + "Activity2023/PrizeApi/Prize";
            utils.request(
                `${url}?Emapp-Format=Emoney&phoneEncrypt=${loginUser.mobileX}&code=${configData.lotteryActCode}`,'json').
            then(res => {
                callback && callback(res);
            });
        },//获取中奖结果
        getPrizeResult:function (prizeCode,callback){
            var url = configData.apiUrl + "Activity2023/PrizeApi/PrizeResult";
            utils.request(
                `${url}?Emapp-Format=Emoney&phoneEncrypt=${loginUser.mobileX}&code=${prizeCode}`,'json').
            then(res => {
                if(!!res.detail && res.code === 0 ){
                    if (res.detail.status=== -1){
                        layer.msg("未中奖。");
                        isLottoryFlag = true;
                        return;
                    }else if (res.detail.status=== 0){
                        //抽奖中 继续请求
                        thisPage.getPrizeResult(prizeCode);
                        return;
                    }else if (res.detail.status=== 1) {
                        var prizeData = res.detail.prizeData;

                        if (prizeData.length > 0) {
                            var prizeName = prizeData[0].prizeName;
                            var angles = thisPage.getAnglesByPName(prizeName);
                            if (angles!=-1) {
                                callback&&callback(angles,prizeName);
                            } else {
                                layer.msg("未匹配到奖品。");
                                isLottoryFlag = true;
                                return;
                            }
                        } else {
                            layer.msg("抽奖失败，请重试。");
                            isLottoryFlag = true;
                            return;
                        }
                    }else{
                        thisPage.getPrizeResult(prizeCode);
                        return;
                    }
                }else {
                    thisPage.getPrizeResult(prizeCode);
                    return;
                }
            });
        },

        rotateFn:function (awards, angles, pname,duration) {
            bRotate = !bRotate;
            $('#rotate').stopRotate();

            $('#rotate').rotate({
                angle: 0,
                animateTo: angles + 1800,
                duration: duration,
                callback: function () {
                    if(!!pname){
                        bRotate = !bRotate;

                        $(".tc_1").html(`恭喜您获得<br />${pname}`);
                        $(".bg").show();
                        $(".tc").show();
                        isLottoryFlag = true;
                    }else{
                        thisPage.rotateFn(awards,angles,pname,duration);
                    }
                }
            })
        },
        getAnglesByPName:function(pName) {
            const patterns = Object.keys(angleConfig).map(key => new RegExp(key));
            for (let i = 0; i < patterns.length; i++) {
                if (patterns[i].test(pName)) {
                    return angleConfig[patterns[i].source];
                }
            }
            return "-1";
        },
        getLotteryLog:function () {
            var url = configData.apiUrl + "Activity2023/PrizeApi/GetPrizeWinLog";
            var top = 30;
            utils.request(
                `${url}?Emapp-Format=Emoney&phoneEncrypt=${loginUser.mobileX}&code=${configData.lotteryActCode}&top=${top}`, 'json').then(res => {
                var pData = res.detail;

                if(!!pData){
                    pData.forEach(item => {
                        $("#user_list").append(`<li>恭喜 ${item.account} 用户喜获 <span>${item.prizeName}</span></li>`)
                    })
                }
            });
        },
        getMyPrize:function () {
            var url = configData.apiUrl + "Activity2023/PrizeApi/GetPrizeLog";
            $("#my_list").html("");
            utils.request(
                `${url}?Emapp-Format=Emoney&phoneEncrypt=${loginUser.mobileX}&code=${configData.lotteryActCode}`, 'json').then(res => {
                var pData = res.detail;
                if (!!pData) {
                    pData.forEach(item => {
                        $("#my_list").append(`<li><span>${item.prizeName}</span></li>`)
                    });
                }
            });
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=*********,*********&groupCode=0&resourcestypeid=234&resourcesid=1244834&businessType=biztyp-tjb";

            return url;
        },
        //检查用户是否登录
        checkIsLogin: function () {
            if (isLogin==='0') {
                layer.msg("您还未登录，请在客户端登录后参与活动");
                return false;
            }
            return true;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            // if (pid != "888010000" && pid != "888010400") {
            //     layer.msg("本活动仅限小智盈用户参与");
            //     return false;
            // }
            return true;
        }
    }
    thisPage.init();
});
