//11111111111111111111111111111111111
$(function(){
	var sWidth = $(".slider_name").width();
	var len = $(".slider_name .silder_panel").length;
	var index = 0;
	var picTimer;
	
	$(".silder_nav li").css({"opacity":"1","filter":"alpha(opacity=60)"}).mouseenter(function() {																
		index = $(".silder_nav li").index(this);
		showPics(index);
	}).eq(0).trigger("mouseenter");

	$(".d1 .prev,.d1 .next").css({"opacity":"1","filter":"alpha(opacity=100)"}).hover(function(){
		$(this).stop(true,false).animate({"opacity":"0.6","filter":"alpha(opacity=60)"},300);
	},function() {
		$(this).stop(true,false).animate({"opacity":"0.2","filter":"alpha(opacity=20)"},300);
	});

	// Prev
	$(".d1 .prev").click(function() {
		index -= 1;
		if(index == -1) {index = len - 1;}
		showPics(index);
	});

	// Next
	$(".d1 .next").click(function() {
		index += 1;
		if(index == len) {index = 0;}
		showPics(index);
	});

	// 
	$(".slider_name .silder_con").css("width",sWidth * (len));
	
	// mouse 
	$(".slider_name").hover(function() {
		clearInterval(picTimer);
	},function() {
		picTimer = setInterval(function() {
			showPics(index);
			index++;
			if(index == len) {index = 0;}
		},10000); 
	}).trigger("mouseleave");
	
	// showPics
	function showPics(index) {
		var nowLeft = -index*sWidth; 
		$(".silder_con").stop(true,false).animate({"left":nowLeft},300);
		$(".silder_nav li").removeClass("current").eq(index).addClass("current"); 
		$(".silder_nav li").stop(true,false).animate({"opacity":"1"},300).eq(index).stop(true,false).animate({"opacity":"1"},300);
	}
});

//2222222222222222222222222222222222222222222222222222222222222222
$(function(){
	var sWidth = $(".slider_name2").width();
	var len = $(".slider_name2 .silder_panel2").length;
	var index = 0;
	var picTimer;
	
	$(".silder_nav2 li").css({"opacity":"1","filter":"alpha(opacity=60)"}).mouseenter(function() {																
		index = $(".silder_nav2 li").index(this);
		showPics(index);
	}).eq(0).trigger("mouseenter");

	$(".d2 .prev,.d2 .next").css({"opacity":"1","filter":"alpha(opacity=100)"}).hover(function(){
		$(this).stop(true,false).animate({"opacity":"0.6","filter":"alpha(opacity=60)"},300);
	},function() {
		$(this).stop(true,false).animate({"opacity":"0.2","filter":"alpha(opacity=20)"},300);
	});

	// Prev
	$(".d2 .prev").click(function() {
		index -= 1;
		if(index == -1) {index = len - 1;}
		showPics(index);
	});

	// Next
	$(".d2 .next").click(function() {
		index += 1;
		if(index == len) {index = 0;}
		showPics(index);
	});

	// 
	$(".slider_name2 .silder_con2").css("width",sWidth * (len));
	
	// mouse 
	$(".slider_name2").hover(function() {
		clearInterval(picTimer);
	},function() {
		picTimer = setInterval(function() {
			showPics(index);
			index++;
			if(index == len) {index = 0;}
		},10000); 
	}).trigger("mouseleave");
	
	// showPics
	function showPics(index) {
		var nowLeft = -index*sWidth; 
		$(".silder_con2").stop(true,false).animate({"left":nowLeft},300);
		$(".silder_nav2 li").removeClass("current").eq(index).addClass("current"); 
		$(".silder_nav2 li").stop(true,false).animate({"opacity":"1"},300).eq(index).stop(true,false).animate({"opacity":"1"},300);
	}
});


//33333333333333333333333333333333333333333333
$(function(){
	var sWidth = $(".slider_name3").width();
	var len = $(".slider_name3 .silder_panel3").length;
	var index = 0;
	var picTimer;
	
	$(".silder_nav3 li").css({"opacity":"1","filter":"alpha(opacity=60)"}).mouseenter(function() {																
		index = $(".silder_nav3 li").index(this);
		showPics(index);
	}).eq(0).trigger("mouseenter");

	$(".d3 .prev,.d3 .next").css({"opacity":"1","filter":"alpha(opacity=100)"}).hover(function(){
		$(this).stop(true,false).animate({"opacity":"0.6","filter":"alpha(opacity=60)"},300);
	},function() {
		$(this).stop(true,false).animate({"opacity":"0.2","filter":"alpha(opacity=20)"},300);
	});

	// Prev
	$(".d3 .prev").click(function() {
		index -= 1;
		if(index == -1) {index = len - 1;}
		showPics(index);
	});

	// Next
	$(".d3 .next").click(function() {
		index += 1;
		if(index == len) {index = 0;}
		showPics(index);
	});

	// 
	$(".slider_name3 .silder_con3").css("width",sWidth * (len));
	
	// mouse 
	$(".slider_name3").hover(function() {
		clearInterval(picTimer);
	},function() {
		picTimer = setInterval(function() {
			showPics(index);
			index++;
			if(index == len) {index = 0;}
		},3000); 
	}).trigger("mouseleave");
	
	// showPics
	function showPics(index) {
		var nowLeft = -index*sWidth; 
		$(".silder_con3").stop(true,false).animate({"left":nowLeft},300);
		$(".silder_nav3 li").removeClass("current").eq(index).addClass("current"); 
		$(".silder_nav3 li").stop(true,false).animate({"opacity":"1"},300).eq(index).stop(true,false).animate({"opacity":"1"},300);
	}
});



//4444444444444444444444444444444444444444444
$(function(){
	var sWidth = $(".slider_name4").width();
	var len = $(".slider_name4 .silder_panel4").length;
	var index = 0;
	var picTimer;
	
	var btn = "<a class='prev'>Prev</a><a class='next'>Next</a>";
	$(".slider_name4").append(btn);

	$(".slider_name4 .prev,.slider_name4 .next").css({"opacity":"1","filter":"alpha(opacity=100)"}).hover(function(){
		$(this).stop(true,false).animate({"opacity":"0.6","filter":"alpha(opacity=60)"},300);
	},function() {
		$(this).stop(true,false).animate({"opacity":"0.2","filter":"alpha(opacity=20)"},300);
	});

	// Prev
	$(".d4 .prev").click(function() {
		index -= 1;
		if(index == -1) {index = len - 1;}
		showPics(index);
	});

	// Next
	$(".d4 .next").click(function() {
		index += 1;
		if(index == len) {index = 0;}
		showPics(index);
	});

	// 
	$(".slider_name4 .silder_con4").css("width",sWidth * (len));
	
	// mouse 
	$(".slider_name4").hover(function() {
		clearInterval(picTimer);
	},function() {
		picTimer = setInterval(function() {
			showPics(index);
			index++;
			if(index == len) {index = 0;}
		},3000); 
	}).trigger("mouseleave");
	
	// showPics
	function showPics(index) {
		var nowLeft = -index*sWidth; 
		$(".slider_name4 .silder_con4").stop(true,false).animate({"left":nowLeft},300);
	}
});


//55555555555555555555555555555555555
$(function(){
	var sWidth = $(".slider_name5").width();
	var len = $(".slider_name5 .silder_panel5").length;
	var index = 0;
	var picTimer;
	
	var btn = "<a class='prev'>Prev</a><a class='next'>Next</a>";
	$(".slider_name5").append(btn);

	$(".slider_name5 .prev,.slider_name5 .next").css({"opacity":"1","filter":"alpha(opacity=100)"}).hover(function(){
		$(this).stop(true,false).animate({"opacity":"0.6","filter":"alpha(opacity=60)"},300);
	},function() {
		$(this).stop(true,false).animate({"opacity":"0.2","filter":"alpha(opacity=20)"},300);
	});

	// Prev
	$(".slider_name5 .prev").click(function() {
		index -= 1;
		if(index == -1) {index = len - 1;}
		showPics(index);
	});

	// Next
	$(".slider_name5 .next").click(function() {
		index += 1;
		if(index == len) {index = 0;}
		showPics(index);
	});

	// 
	$(".slider_name5 .silder_con5").css("width",sWidth * (len));
	
	// mouse 
	$(".slider_name5").hover(function() {
		clearInterval(picTimer);
	},function() {
		picTimer = setInterval(function() {
			showPics(index);
			index++;
			if(index == len) {index = 0;}
		},3000); 
	}).trigger("mouseleave");
	
	// showPics
	function showPics(index) {
		var nowLeft = -index*sWidth; 
		$(".slider_name5 .silder_con5").stop(true,false).animate({"left":nowLeft},300);
	}
});


//66666666666666666666666666666666
$(function(){
	var sWidth = $(".slider_name6").width();
	var len = $(".slider_name6 .silder_panel6").length;
	var index = 0;
	var picTimer;
	
	var btn = "<a class='prev'>Prev</a><a class='next'>Next</a>";
	$(".slider_name6").append(btn);

	$(".slider_name6 .prev,.slider_name6 .next").css({"opacity":"1","filter":"alpha(opacity=100)"}).hover(function(){
		$(this).stop(true,false).animate({"opacity":"0.6","filter":"alpha(opacity=60)"},300);
	},function() {
		$(this).stop(true,false).animate({"opacity":"0.2","filter":"alpha(opacity=20)"},300);
	});

	// Prev
	$(".slider_name6 .prev").click(function() {
		index -= 1;
		if(index == -1) {index = len - 1;}
		showPics(index);
	});

	// Next
	$(".slider_name6 .next").click(function() {
		index += 1;
		if(index == len) {index = 0;}
		showPics(index);
	});

	// 
	$(".slider_name6 .silder_con6").css("width",sWidth * (len));
	
	// mouse 
	$(".slider_name6").hover(function() {
		clearInterval(picTimer);
	},function() {
		picTimer = setInterval(function() {
			showPics(index);
			index++;
			if(index == len) {index = 0;}
		},3000); 
	}).trigger("mouseleave");
	
	// showPics
	function showPics(index) {
		var nowLeft = -index*sWidth; 
		$(".slider_name6 .silder_con6").stop(true,false).animate({"left":nowLeft},300);
	}
});

