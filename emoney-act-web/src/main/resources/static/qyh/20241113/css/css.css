@charset "gb2312";
body {font-size:12px;margin:0;padding:0;line-height:22px;background-color:#A20005; color:#595757;font-family:"微软雅黑";}
.main {width:996px; margin:0 auto; position:relative;}
ul,li {margin:0;padding:0;list-style:none;}
A:link {COLOR: #fff2d4; TEXT-DECORATION: none}
A:visited {COLOR: #fff2d4; TEXT-DECORATION: none}
A:hover {COLOR: #ffff00; TEXT-DECORATION: none}
A.b:link {COLOR: #ff6600; TEXT-DECORATION: none}
A.b:visited {COLOR: #ff6600; TEXT-DECORATION: none}
A.b:hover {COLOR: #333; TEXT-DECORATION: none}
.white {color:#ffffff;}
.red {color:#e73828;font-weight: bold;}
.yellow {color:#ffe8cf; font-weight: bold;}
.black{ color:#000;}
.blue{color:#2ea5de;}
.org{color:#f8b62d;}
.gray{color:#808285;}
td{font-size:12px; height:30px; line-height:30px;}

.dbg1{background:url(../images/index_01.png) center top no-repeat;height:493px;}
.dbg2{background:url(../images/index_02.png) center top no-repeat;height:412px;}
.dbg3{background:url(../images/index_03.png) center top no-repeat;height:617px;}
.dbg4{background:url(../images/index_04.png) center top no-repeat;height:433px;}
.dbg5{background:url(../images/index_05.png) center top no-repeat;height:493px;}
.dbg6{background:url(../images/index_06.png) center top no-repeat;height:1409px;}
.pf{background:url(../images/pf.png) center top no-repeat; width: 100%; height:150px; position: fixed; left: 0px; bottom: 0px;}

.footer{text-align:center; padding:20px 0 180px; font-family:"宋体"; font-size:12px; color: #fff;}

.btn1{
	BACKGROUND: url(../images/btn1.png) left no-repeat;
	width: 341px;
	height: 77px;
	position: absolute;
	left: 606px;
	top: 11px;
}
.btn2{
	BACKGROUND: url("../images/btn2.png");
	width: 314px;
	height: 105px;
	position: absolute;
	left: 660px;
	top: 4px;
}
.t{
	position: absolute;
	top: 108px;
	left: 782px;
	font-size: 78px;
	width: 120px;
	color: #a70006;
	line-height: 80px;
	text-align: center;
	font: DIN;
	font-weight: bold;
	letter-spacing: -12px;
}
.djs{
	position: absolute;
	top: 30px;
	left: 426px;
	font-size: 42px;
	width: 120px;
	color: #fed781;
	line-height: 42px;
	text-align: center;
	font: DIN;
	letter-spacing: -2px;
}
.hdgz2{
	position: absolute;
	top: 174px;
	left: 154px;
	font-size: 20px;
	width: 837px;
	color: #fddacb;
	line-height: 35px;
}
.zp{
	position: absolute;
	top: 70px;
	left: 91px;
	/* BACKGROUND: url("../images/zp.png"); */
	width: 348px;
	height: 348px;
}
.zz{
	position: absolute;
	top: 231px;
	left: 170px;
	BACKGROUND: url("../images/zz.png");
	width: 180px;
	height: 180px;
}
.go{
	position: absolute;
	top: 266px;
	left: 207px;
	width: 110px;
	height: 110px;
	cursor: pointer;
	display: flex;
	justify-content: center;
	align-items: center;
}
.rftime{
	position: absolute;
	top: 177px;
	left: 653px;
	width: 200px;
	color: #ffffff;
	font-size: 20px;
	font-weight: bold;
}
.md{
	position: absolute;
	top: 303px;
	left: 553px;
	width: 385px;
	color: #d56948;
	font-size: 14px;
}
.md li{padding: 0 20px; height: 30px; line-height:30px;background-color:#FBF0EC; margin-bottom: 5px; border-radius: 15px;}

.tc{
	width: 459px;
	height: 333px;
	BACKGROUND: url(../images/tc.png);
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
.tc_1{
	font-size: 32px;
	color: #441b17;
	width: 280px;
	position: absolute;
	top: 165px;
	text-align: center;
	line-height: 40px;
	left: 92px;
	transform: rotate(8deg);
}
.close{
	position: absolute;
	top: 8px;
	right: 2px;
	BACKGROUND: url("../images/close.png");
	width: 30px;
	height: 30px;
}

.dh,.dh2:hover{
	-webkit-animation: dh 0.3s linear infinite alternate;
	animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}

@keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}

.al1{
	position: absolute;
	left: 26px;
	top: 37px;
	width: 975px;
	height: 521px;
}
.al2{
	position: absolute;
	left: 26px;
	top: 651px;
	width: 975px;
	height: 541px;
}
.al3{ margin: 30px auto 0; width: 1051px; height: 650px;position: relative;
}

.slider_box{ margin: 0px auto; width: 975px; height: 521px; position: relative;overflow:hidden; border-radius: 28px;}
.silder_con{ height: 521px; position: absolute;}
.silder_panel{width: 975px; height: 521px; float: left; position: relative; text-align:center;}
.slider_box2{ margin: 0px auto; width: 975px; height: 541px; position: relative;overflow:hidden;border-radius: 28px;}
.silder_con2{ height: 541px; position: absolute;}
.silder_panel2{width: 975px; height: 541px; float: left; position: relative; text-align:center;}
.slider_box3{ margin: 0px auto; width: 1051px; height: 576px; position: relative;overflow:hidden;border-radius: 5px;}
.silder_con3{ height: 576px; position: absolute;}
.silder_panel3{width: 1051px; height: 576px; float: left; position: relative; text-align:center;}

.silder_nav {width: 500px;
	height: 14px;
	overflow: hidden;
	position: absolute;
	left: 331px;
	top: 550px;}
.silder_nav li { width: 84px; height: 14px; overflow: hidden; display: block;margin-right:15px; padding: 0px; background-color: #7f4f21; float: left;border-radius: 10px;}
.silder_nav li.current { background-color:#a30f14}

.silder_nav2 {width: 500px;
	height: 14px;
	overflow: hidden;
	position: absolute;
	left: 281px;
	top: 570px;}
.silder_nav2 li { width: 84px; height: 14px; overflow: hidden; display: block;margin-right:15px; padding: 0px; background-color: #7f4f21; float: left;border-radius: 10px;}
.silder_nav2 li.current { background-color:#a30f14}

.silder_nav3 {width: 710px;
	height: 14px;
	overflow: hidden;
	position: absolute;
	left: 184px;
	top: 600px;}
.silder_nav3 li { width: 84px; height: 14px; overflow: hidden; display: block;margin-right:15px; padding: 0px; background-color: #e83828; float: left;border-radius: 10px;}
.silder_nav3 li.current { background-color:#FADFAF}

a.prev{ background: url("../images/left.png") no-repeat; width: 54px; height: 54px; text-indent: -9999px; display: block; position: absolute;left: 0px; top:49%;outline:none}
a.next{ background: url("../images/right.png") no-repeat; width: 54px; height: 54px; text-indent: -9999px; display: block; position: absolute; right: 0px; top: 49%;outline:none}


.bg,.h{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%; display: none;}
.tc-login{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc-login .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc-login .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc-login .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc-login .zj{letter-spacing:16px;}
.tc-login .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 304px;line-height: 45px;}
.tc-login .yzm:hover{background-color: #f4f4f4;}
.tc-login .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc-login .tc-btn1:hover{ background-color: #3689ce;}
.tc-login .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc-login .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}

input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}