$(function () {
    'use strict';
    var Urls = www;
    var isLogin = $("#hid_isLogin").val();
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val()
    }
    var configData = {
        "actCode": $("#hid_actcode").val(),
        "cmpCode": "ACBandPop20231206",
        "type":3,
        "staticPath":$("#hid_staticPath").val()
    }

    var thisPage = {
        init: function () {
            thisPage.bindEvents();

            if (isLogin === '1') {
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }

            } else {
                $(".bg").show();
                $(".logintc").show();
            }
        },
        //绑定事件
        bindEvents: function () {
            //点击支付
            $(".btn1,.btn2,.btn3,.pf").click(function () {
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }

                var url = thisPage.getPayUrl();

                //客户端
                if (!!thisPage.GetExternal()) {
                    thisPage.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }

                thisPage.pushdatatocmp(loginUser.uid, configData.cmpCode);
            });
            //1年、2年、3年
            $(".btn4,.btn5,.btn6").click(function (){
                thisPage.openCode($(this));
            });
            //点击申请
            $('.btn1,.btn2,.btn3').click(function () {
                document.querySelector('#a1').scrollIntoView({behavior: 'smooth'})
                thisPage.bh();
            });
            //点击提交
            $("#btn_submit").click(function (){
                var code = ''
                var checkcode = $('#checkcode').html()
                var txt = $('.text1') // 获取所有文本框
                for (var i = 0; i < txt.length; i++) {
                    code += txt.eq(i).val()
                }
                if (code == '') {
                    layer.msg('请输入您专属的验证码')
                    return false
                }
                if (code != checkcode) {
                    layer.msg('您的验证码输入有误，请确认后输入')
                    return false
                }

                var accode = $(this).attr('data-accode')
                var type = $(this).attr('data-type')

                utils.PushDataToCMP(accode, uid, '', type);

                $('.tc1b').css('background-image', "url('"+configData.staticPath+"static/qyh/20231219/images/tc"+type+".png')");
                thisPage.addCount(accode,function (){
                    layer.closeAll()
                    layer.open({
                        type: 1,
                        title: false,
                        area: ['auto'],
                        skin: 'layui-layer-nobg', //没有背景色
                        content: $('#popwin2').html()
                    });
                });
            });
            //关闭
            $(document).on('click', '.close', function () {
                $(".bg").hide();
                $(".tc3").hide();
            });
        },
        openCode:function (obj){
            var accode = obj.attr('data-accode')
            var type = obj.attr('data-type')
            var addclass = obj.attr('data-addclass')

            if (uid) {
                $('#btn_submit').attr('data-accode', accode)
                $('#btn_submit').attr('data-type', type)
                $('#btn_submit').attr('data-addclass', addclass)
                var randnum = rand(1000, 9999)
                $('#checkcode').html(randnum)

                layer.open({
                    type: 1,
                    title: false,
                    //closeBtn: 0,
                    area: ['auto'],
                    //shadeClose: true,
                    skin: 'layui-layer-nobg', //没有背景色
                    content: $('#popwin1').html(),
                    success: function (layero, index) {
                        goNextInput('.layui-layer-content .text1')
                    },
                    end: function () {
                    }
                })
            }
        },
        openPopup:function (cb) {
            layer.close(layer.index)
            layer.open({
                type: 1,
                title: false,
                //closeBtn: 0,
                area: ['auto'],
                //shadeClose: true,
                skin: 'layui-layer-nobg', //没有背景色
                content: $('#popwin2').html(),
                success: function (layero, index) {
                    cb()
                },
                end: function () { }
            })
        },
        //计数
        addCount:function(accode,callback){
            var nowtime = new Date().getTime();
            if (!thisPage.checkIsLogin()) {
                return false;
            }
            if (!thisPage.checkPermission()) {
                return false;
            }
            $.ajax({
                type: 'get',
                url: Urls + '/user/addcountbyactcode?actcode=' + accode,
                dataType: 'jsonp',
                data: {
                    uid: loginUser.uid ,
                    value: nowtime
                },
                success: function (data) {
                    if(data.code=="200"){
                        callback && callback();
                    }
                }
            });
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888204010,888224010&groupCode=0&resourcestypeid=234&resourcesid=1244834&businessType=biztyp-tjb";
            return url;
        },
        //检查用户是否登录
        checkIsLogin: function () {
            if (isLogin==='0') {
                layer.msg("您还未登录，请在客户端登录后参与活动");
                return false;
            }
            return true;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            // if (pid != "888010000" && pid != "888010400") {
            //     layer.msg("本活动仅限小智盈用户参与");
            //     return false;
            // }
            return true;
        },
        bh: function () {
            var i = document.getElementById('k')
            i.style.display = 'block'
            setTimeout('clock()', 1300)
        },
        clock: function () {
            var i = document.getElementById('k')
            i.style.display = 'none'
        }
    }
    thisPage.init();
});