@charset "utf-8";

img{ border:0px;}
body {
	font-size:12px; overflow-x:hidden;
	margin:0;
	padding:0;
	line-height:22px;
	color:#fff;
	background-color:#B0231C;font-family:"微软雅黑";
}

.main{width:100px; margin:0 auto; position:relative;}
ul,li {margin:0;padding:0;list-style:none;}
A:link {COLOR: #fff; TEXT-DECORATION: none;}
A:visited {COLOR: #fff; TEXT-DECORATION: none}
A:hover {COLOR:#ffff00; TEXT-DECORATION: none}
A.b:link {TEXT-DECORATION: underline;}
A.b:visited {TEXT-DECORATION: underline;}
A.b:hover {TEXT-DECORATION: none}
A.c:link {COLOR: #c30d23; TEXT-DECORATION: underline;}
A.c:visited {COLOR: #c30d23; TEXT-DECORATION: underline}
A.c:hover {COLOR:#333; TEXT-DECORATION: none}
A.d:link {COLOR: #333; TEXT-DECORATION: underline;}
A.d:visited {COLOR: #333; TEXT-DECORATION: underline}
A.d:hover {COLOR:#333; TEXT-DECORATION: none}
input{background:transparent;border:0px;margin:0px; color:#939598;list-style:none; font-family:"微软雅黑"; font-size:14px;}
.white {color:#ffffff;}
.red {color:#8C3719;}
.red2{color:#d30101;}
.green {color:green;}
.black{ color:#000;}
.blue{color:#549FD6;}
.yellow{color:#ffea94; font-weight: bold;}
.blue3{color:#255E98;}
.gray{color:#EBC384;}
.z1{letter-spacing:1px;}
.t-c{ text-align: center;}

.bg_h{background-color:#840000; width: 996px; margin: 0 auto; overflow: hidden;}
.f_12{font-size:12px;}
.f_14{font-size:17px; color: #fff;line-height: 20px;}
.clr2{clear:both; height:1px; overflow:hidden;}
.m_t36{margin-top:36px;}
.m_t20{margin-top:20px;}
.m_t10{margin-top:10px;}
.t_c{text-align:center;}
.f_16{font-size:16px;font-weight:bold;}
.f_17{font-size:17px;}
.f_26{font-size:26px; font-family:"微软雅黑"; line-height:50px;}
.f_30{font-size:28px; color:#603813; line-height:50px; font-weight:bold;}
.f20{font-size:20px; color: #fbcf9e; padding-top: 15px;}
.f21{font-size:20px;}
.f-b{text-decoration: underline}

.h340{height:340px;}
td{font-size:18px;text-align:center; background-color:#fff;}

.f-r{float:right;}
.f-l{float:left;}

.bod .dbg1{background:url(../images/index_01.jpg) center top no-repeat;height:322px;}
.bod .dbg1_new{background:url(../images/index_01_new.jpg) center top no-repeat;height:322px;}
.bod .dbg2{background:url(../images/index_02.jpg) center top no-repeat;height:321px;}
.bod .dbg2_new{background:url(../images/index_02_new.jpg) center top no-repeat;height:321px;}
.bod .dbg3{background:url(../images/index_03.png) center top no-repeat;height:342px;}
.bod .dbg4{background:url(../images/index_04.png) center top no-repeat;height:886px;}
.bod #dbg5{background:url(../images/index_05.png) center top no-repeat;height:494px;}
.bod .dbg6{background:url(../images/index_06.png) center top no-repeat;height:493px;}
.bod .dbg7{background:url(../images/index_07.png) center top no-repeat; height:775px;}
.bod #dbg8{background:url(../images/index_08.png) center top no-repeat;height:719px;}
.bod #dbg9{background:url("../images/index_09.png") center top no-repeat;height:628px;}
.bod #dbg10{background:url(../images/index_10.png) center top no-repeat;height:708px; overflow: hidden;}
.bod #dbg11{background:url("../images/index_11.png") center top no-repeat;height:277px;}
.bod #dbg12{background:url("../images/index_12.jpg") center top no-repeat;height:560px;}

.bg2{ background-color: #FFE7C7; color: #c30d23;
	width: 996px;
	height: 80px; position: relative; text-align: center; line-height: 80px;font-size: 28px; margin-bottom: 55px;}
.bg3{background: url("../images/bg3.png") right;
	width: 210px; color: #7f4f21;
	height: 80px; float: left; text-align: center; font-size: 40px;text-shadow: 1px 1px 1px #fff;}
.bg5{background: url("../images/bg5.png");
	width: 996px;
	height: 79px;margin-bottom: 55px; position: relative;}
.bg5b{background: url("../images/bg5b.png");
	width: 996px;
	height: 80px;margin-bottom: 55px;}
.bg7{background: url("../images/bg7.png");
	width: 995px;
	height:80px;margin-bottom: 55px;}
.al1{position: absolute;left: -414px;top: 330px;}
.al2{
	position: absolute;
	left: -360px;
	top: 217px;
}
.al3{
	position: absolute;
	left: -360px;
	top: 130px;
}
.al4{
	position: absolute;
	left: -414px;
	top: 138px;
}
.bg{
	width: 940px;
}
.bg .bt{
	color: #c30d23;
	font-size: 30px;
	text-align: center;
	padding-top: 25px;
	line-height: 30px;
	width: 210px;
	position: absolute;
	left: 57px;
	top: 0px;
	text-shadow: 1px 1px 1px #fff;
}
.bg .bt2{
	color: #fdf9cb;
	font-size: 26px;
	line-height: 30px;
	position: absolute;
	left: 310px;
	top: 33px;
	width: 574px;
	text-align: center;
}
.bg ul{float: left;width:251px; font-size: 20px;}
.bg ul a{ width:251px; height:79px;background:url("../images/bg1.png") top; display: block;
    text-decoration: none;
    overflow: hidden; text-align: center; background-size: 100% 100%;
}
.bg .f1{margin: 10px 0 0 0; font-size: 26px; line-height: 30px;}
.bg .f1b{margin: 13px 0 0 0; font-size: 30px; line-height: 27px;}
.bg .f3{margin: 5px 0 0 0; font-size: 18px;}
.bg ul a:hover ,.bg ul a.current{
    background:url("../images/bg1h.png") top;
    color: #91413d; background-size: 100% 100%;
}
.swf1{
width: 660px;
  height: 380px;
  float: left; margin-left: 15px;
}
.h1{height: 73px!important;}
.bg10{
	background: url("../images/bg10.png"); background-size: 100% 100%;
	width: 967px;
	height: 473px; position: relative; margin: 0 auto 50px;
}

.bgb{
	width: 912px;
	height: 83px;
	position: absolute;
	left: 0px;
	top: 1px;
	color: #fdf9cb;
	text-align: center;
	font-size: 28px;
	line-height: 37px;
	padding-top: 20px;
}
.lh100{line-height: 70px;}
.bg4{ background-color: #A00017; border-radius: 40px;
	width: 450px;
	height: 40px;
	color: #fbcf9e;
	text-align: center; line-height: 40px;
	font-size: 18px; margin: 0 auto;
}
.bg6{
	background: url("../images/bg6.png"); background-size: 100% 100%;
	width: 967px;
	height: 440px; position: relative; margin: 0 auto 50px;
}
.sppf{background: url("../images/bg8.png") center top no-repeat;height:80px;}
.sppfbtn{
	background: url("../images/btn7.png");
	width: 210px;
	height: 87px;
	position: absolute;
	left: 283px;
  top: 8px;
}
.sppfbtn2{
	background: url("../images/btn8.png");
	width: 65px;
	height: 38px;
	position: absolute;
	left: 50%;
	top: 431px;
margin-left: 360px; display: none;
}
.sp{width: 670px; height: 380px; border-radius: 5px; border: 3px solid #fff; margin: 20px auto 0 auto;}
.bg9{
	background: url("../images/bg9.png");
	width:100%;
	height:80px; overflow: hidden; text-align: center;
}

.pf{position: fixed; left: 0px; bottom: 0%; background: url("../images/pf3.png") center; width: 100%; height: 137px;}
.pf_new{position: fixed; left: 0px; bottom: 0%; background: url("../images/pf3_new.png") center; width: 100%; height: 166px;}
.pf2{position: fixed; left: 0px; top: 20%;}
.wddd{position: absolute; right: 0px; top: 20%; background-color: #F44D39; width: 40px; padding: 10px 0; color: #fff; font-size: 22px; text-align: center; border-top-left-radius: 10px;border-bottom-left-radius: 10px; line-height: 25px;}
.wddd2{background-color: #fff;width:680px;
border-radius: 10px;
position: absolute;
left: 50%;
top: 50%;
margin: -100px 0 0 -340px;
color: #000;
padding: 25px;
font-size: 16px;}
.wddd2 .bt{ background-color: #FFD9AD; color: #9b4f1c; font-size: 22px;}
table,th,td {
  border : 1px solid #999;
  border-collapse: collapse; line-height: 50px;
}
.btn1{
	background: url(../images/btn1.png);
	width: 210px;
	height: 73px;
	position: absolute;
	left: 326px;
	top: 173px;
}
.btn2{
	background: url(../images/btn2.png);
	width: 513px;
	height: 137px;
}
.an1{
	position: absolute;
	left: -208px;
	top: -110px;
}
.an2{
	position: absolute;
	left: -117px;
	top: 1102px;
}
.an3{
	position: absolute;
	left: -117px;
	top: 518px;
}
.ico1{
	background: url("../images/ico2.png");
	width: 299px;
	height: 373px;
	position: absolute;
	left: -426px;
	top: 332px;transition: 0.3s;
}
.ico2{
	background: url("../images/ico.png");
	width: 299px;
	height: 373px;
	position: absolute;
	left: 249px;
	top: 332px;transition: 0.3s;
}
.ico3{
	background: url("../images/ico3.png");
	width: 466px;
	height: 669px;
	position: absolute;
	left: -173px;
	top: 229px;transition: 0.3s;
}
.ico4{
	background: url("../images/ico4.png");
	width: 132px;
	height: 30px;
	position: absolute;
	left: 173px;
  top: 5px;
}
.btn3{
	background: url(../images/btn3.png);
	width:329px;
	height:98px;position: absolute;
	left: 194px;
  top: 10px;
}
.btn3_new{
	background: url(../images/btn3.png);
	width: 329px;
	height: 98px;
	position: absolute;
	left: 192px;
	top: 8px;
}
.bod .btn4,.bod .btn4h{
	background: url(../images/btn4.png);
	width: 172px;
	height: 66px;
	position: absolute;
	left: 373px;
	top: 230px;
}
.btn5,.btn5h{
	background: url(../images/btn5.png);
	width: 172px;
	height: 66px;
	position: absolute;
	left: 373px;
	top: -12px;
}
.btn6,.btn6h{
	background: url(../images/btn6.png);
	width: 264px;
	height: 89px;
	position: absolute;
	left: -69px;
	top: 300px;
}
.btn4h,.btn5h{
    width: 187px;
    height: 72px;
    position: absolute;
    left: 290px;
    top: -38px;
	background:url(../images/btn5h.png) center no-repeat;
}
.btn6h{
	background:url("../images/btn6h.png") center no-repeat;
}

.djs{
	background: url(../images/djs.png) left no-repeat;
	position: absolute;
	left: 177px;
	top: 207px;
	width: 160px;
	height: 50px;
	font-size: 36px;
	color: #fff;
	line-height: 45px;
	letter-spacing: 16px;
	font-weight: bold;
	text-align: right;
}
.txt1{
	position: absolute;
	left: 356px;
	top: 243px;
	text-align: center;
	width: 151px;
	font-size: 18px;
	color: #fff;
}
.txt2{
	position: absolute;
	left: -360px;
	top: -181px;
	color: #ffdfca;
	font-size: 28px;
	width: 500px;
	text-align: center;
	font-weight: bold;
}
.txt2b{
	position: absolute;
	left: -360px;
	top: -175px;
	color: #ffdfca;
	font-size: 18px;
	width: 500px;
	text-align: center;
}
.txt2c{
	color: #ffe3cc;
	font-size: 16px;
	width: 180px;
	text-align: center; overflow: hidden;
}
.txt3{
	position: absolute;
	left: 255px;
	top: 523px;
	text-align: center;
	width: 379px;
	font-size: 14px;
	color: #fff;
}
.a1{font-size: 16px;}
.a2b{color: #fff4e3; padding:3px 0 0px;white-space:nowrap; font-size: 15px;}
.txt2c ul{ width: 200px; color: #fff; margin: 5px 0 0 12px;}
.txt2c li{width: 80px; float: left;margin:0 2px 5px 0px; color: #ae2723;}
.b1{
	position: absolute;
	left: -432px;
	top: -112px;
}
.b2{
	position: absolute;
	left: -218px;
	top: -112px;
}
.b3{
	position: absolute;
	left: -3px;
	top: -112px;
}
.txt2d{
	position: absolute;
	left: -31px;
	top: 175px;
	color: #9b4f1c;
	font-size: 12px;
	width: 215px;
}
.txt2e{
	position: absolute;
	left: 320px;
	top: -258px;
	color: #9b4f1c;
	font-size: 14px;
	width: 205px;
	text-align: center;
	line-height: 20px;
}
.txt2f{
	position: absolute;
	left: 271px;
	top: -174px;
	color: #ffe7c7;
	font-size: 20px;
	font-weight: bold;
	width: 75px;
	text-align: center;
}
.txt2f2{
	position: absolute;
	left: 271px;
	top: -69px;
	color: #ffe4c7;
	font-size: 20px;
	font-weight: bold;
	width: 75px;
	text-align: center;
}
.txt2g{
	position: absolute;
	left: 374px;
	top: -194px;
	color: #9b4f1c;
	font-size: 14px;
	width: 167px;
}
.txt2g2{
	position: absolute;
	left: 376px;
	top: -81px;
	color: #9b4f1c;
	font-size: 14px;
	width: 129px;
}
.txt2h{
	position: absolute;
	left: 262px;
	top: -130px;
	color: #8C6239;
	font-size: 18px;
	width: 173px;
}
.txt2h2{
	position: absolute;
	left: 268px;
	top: -8px;
	color: #8C6239;
	font-size: 18px;
	width: 136px;
}
.txt2i{
	position: absolute;
	left: 446px;
	top: -138px;
	color: #9b4f1c;
	font-size: 14px;
	width: 78px;
}
.txt2i2{
	position: absolute;
	left: 446px;
	top: -24px;
	color: #9b4f1c;
	font-size: 14px;
	width: 78px;
}
.txt2j{
	position: absolute;
	left: -397px;
	top: 28px;
	color: #3e3a39;
	font-size: 16px;
	width: 385px;
}
.txt2k{
	position: absolute;
	left: 268px;
	top: 34px;
	color: #ffe4c7;
	font-size: 20px; font-weight: bold;
	width: 115px;
}
.txt2k2{
	position: absolute;
	left: 428px;
	top: 30px;
	color: #9b4f1c;
	font-size: 14px;
	width: 90px;
}
.txt2k3{
	position: absolute;
	left: 212px;
	top: -29px;
	color: #BC231F;
	font-size: 18px;
	width: 115px;
	text-align: center;
}
.txt2k4{
	position: absolute;
	left: 265px;
	top: 180px;
	color: #fff;
	font-size: 12px;
	width: 281px;
}
.txt2k5{
	position: absolute;
	left: -275px;
	top: 154px;
	color: #9b4f1c;
	font-size: 12px;
	width: 224px;
	line-height: 20px;
}
.txt2k6{
	position: absolute;
	left: -31px;
	top: 113px;
	color: #9b4f1c;
	font-size: 12px;
	width: 217px;
	line-height: 18px;
}
.txt2l{
	position: absolute;
	left: -341px;
	top: -74px;
	color: #FFE0A7;
	font-size: 26px;
	width: 240px;
	text-align: center;
	line-height: 30px;
}
.txt2l2{
	position: absolute;
	left: -319px;
	top: -83px;
	color: #fff;
	font-size: 16px;
	width: 240px;
	text-align: center;
}
.txt2l3{
	position: absolute;
	left: 18px;
	top: 165px;
	color: #ffe0a7;
	font-size: 14px;
	width: 249px;
}
.txt2l4{
	position: absolute;
	left: 275px;
	top: -115px;
	color: #ffe0a7;
	font-size: 14px;
	width: 249px;
	line-height: 16px;
}
.txt2l5{
	position: absolute;
	left: 54px;
	top: 176px;
	color: #3e3a39;
	font-size: 14px;
	width: 312px;
	line-height: 22px;
}
.txt2l6{
	position: absolute;
	left: 136px;
	top: 170px;
	color: #ae1100;
	font-size: 19px;
	width: 335px;
	line-height: 25px;
}
.txt2l7{
	position: absolute;
	left: 45px;
	top: 167px;
	color: #fff;
	font-size: 14px;
	width: 430px;
	line-height: 25px;
}
.txt2l8{
	position: absolute;
	left: -169px;
	top: -93px;
	color: #F3D9AB;
	font-size: 14px;
	width: 115px;
	text-align: center;
}
.txt2l8b{
	position: absolute;
	left: 234px;
	top: 216px;
	color: #fff;
	font-size: 14px;
	width: 115px;
}


.lb{font-size: 14px;
	color: #fff;
	font-family: "宋体"; width: 280px; line-height: 20px;}
.lb ul{height: 25px; padding-top: 5px;}
.lb li{float: left; padding: 0 7px; border: 1px solid #bc202c; background-color: #660208; margin-right: 2px; font-size: 12px; color: #e7c2ae;font-family: "宋体";}

.txt4{
	position: absolute;
	left: -221px;
	top: 174px;
	font-size: 16px;
	color: #C30D23!important;
	width: 157px;
}
.txt5{
	position: absolute;
	left: -338px;
	top: -121px;
	font-size: 20px;
	width: 237px;
	text-align: center; color: #ffe0a7;
}
.txt6{
	position: absolute;
	left: 14px;
	top: 63px;
	font-size: 16px;
	width: 539px;
}
.txt7{
	position: absolute;
	left: 14px;
	top: 113px;
	font-size: 13px; line-height: 18px;
	width: 391px;
	color: #8e0a00;
	text-shadow: 1px 1px 0px #fb7a79;
}

.close{
	background: url("../images/close.png") center top no-repeat;
	width: 25px;
	height: 25px;
	position: absolute;
	top: -30px;
	right: 0;
}
.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/h.png); display: none; z-index: 99;}
.tc{background-image:url(../images/tc.png); 
	width: 409px;
	height:307px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -122px 0 0 -210px;
}
.hdgz{background-image:url(../images/hdgz.png); width:496px; height: 376px;
position: absolute;
left: 50%;
top: 50%;
margin: -213px 0 0 -273px;
color: #a0520b;
padding: 25px;
font-size: 16px;
line-height: 26px;}
.hdgz ul{padding: 50px 0 0 20px;}
.hdgz li{list-style-type:decimal;}
.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
.dh2:hover{
  -webkit-transform: scale(1.05);
    transform: scale(1.05); z-index: 9;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

#k{
	border: 4px solid #ffff00;
width: 1015px;
  height: 747px;
  position: absolute;
  left: -463px;
  top: -283px;
	opacity: 0;
	-webkit-animation: k 0.2s linear infinite alternate;
	animation: k 0.2s linear infinite alternate;pointer-events: none; display: none;
}
@-webkit-keyframes k {
 to {opacity: .3;
}
}
 @keyframes k {
 to {opacity: .3;
}
}
#div1{
	width: 951px;
	position: absolute;
	left: -424px;
	top: 359px;
	height: 144px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}

.footer{text-align:center; font-family:"宋体"; font-size:12px; padding: 20px 0 190px;}

*{margin:0; padding:0; font-family:"微软雅黑";}
.tc1{width:420px; height:244px; background:url(../images/tc.png) no-repeat; text-align:center;display:none;}
.tc1b{width:409px; height:307px; background:url("../images/tc1.png") no-repeat; text-align:center;display:none;}
.font1{font-size: 32px;color: #a0520b;padding-top: 81px; line-height:42px; font-weight:bold;}
.font2{font-size:37px; color:#a50000; padding-top:35px; line-height:78px;}
.font2 span{color:#000000; font-size:28px; line-height:42px;}
.t1{width:253px; margin:10px auto;}
.text1{width: 50px;height: 65px;border:1px solid #9e9e9e; float:left; margin:0 5px; text-align:center; line-height:28px; font-size:44px; color:#666666;}
.clear{clear:both;}
.btn_tc a{width:296px; height:45px; margin:15px auto; background:url(../images/tc_12.png) no-repeat; line-height:43px; color:#ffffff; font-size:24px; display:block;}
a{text-decoration:none;}
.btn_tc0 a{width:239px; height:86px; margin:15px auto; background:url("../images/tc0_12.png") no-repeat;display:block;}
.tc3-btn{width:299px; height:86px; background:url("../images/tc3-btn.png") no-repeat; position: absolute;left: 50px;top: 212px;}
.tc4-btn{width:239px; height:86px; background:url("../images/tc4-btn.png") no-repeat; position: absolute;left: 80px;top: 212px;}
a{text-decoration:none;}
.tc0{
	width: 409px;
	height: 307px;
	background: url(../images/tc0.png) center no-repeat;display:none;text-align:center; overflow: hidden;
}

@-webkit-keyframes wobble2 {
  0% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  15% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  30% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  45% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  60% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  75% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  100% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }
}

@keyframes wobble2 {
  0% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  15% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  30% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  45% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  60% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  75% {
    -webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
            transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }

  100% {
    -webkit-transform: none;
            transform: none;
			-moz-transform-origin:0% 80%;
			transform-origin:0% 80%;
  }
}

.wobble2 {
  -webkit-animation-name: wobble2;
          animation-name: wobble2;
		  -webkit-transform-origin: center bottom;
	-moz-transform-origin: center bottom;
	-ms-transform-origin: center bottom;
	-o-transform-origin: center bottom;
	transform-origin: center bottom;
	-webkit-animation-duration: 3s;
	-o-animation-duration: 3s;
	animation-duration: 3s;
	-webkit-animation-timing-function: ease-in;
	-o-animation-timing-function: ease-in;
	animation-timing-function: ease-in;
	-webkit-animation-delay: 0s;
	-o-animation-delay: 0s;
	animation-delay: 0s;
	-webkit-animation-iteration-count: infinite;
	-o-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	-webkit-animation-direction: normal;
	-o-animation-direction: normal;
	
	-webkit-animation-fill-mode: both;
	-o-animation-fill-mode: both;
	animation-fill-mode: both;
}


.djs2{
	background: url(../images/djs2.png);
	width: 265px;
	height: 79px;
	position: absolute;
	left: 252px;
	top: 119px;
	line-height: 34px;
	color: #fff;
	font-size: 22px;
	text-align: center;
	font-weight: bold;
	letter-spacing: 7.4px;
}
.t{
	position: absolute;
	left: 16px;
	width: 51px;
	top: 33px;
}
.s{
	position: absolute;
	left: 75px;width: 51px;
	top: 33px;
}
.f{
	position: absolute;
	left: 134px; width: 51px;
	top: 33px;
}
.m{
	position: absolute;
	left: 193px; width: 51px;
	top: 33px;
}
.btn7{
	position: absolute;
	width: 168px;
	height: 118px;
	top: -101px;
	left: -414px;
}
.btn8{
	position: absolute;
	width: 168px;
	height: 118px;
	top: -100px;
	left: -209px;
}
.btn9{
	position: absolute;
	width: 168px;
	height: 118px;
	top: -101px;
	left: -2px;
}
.btn10{
	background: url("../images/btn9.png")!important;
	width: 173px!important;
	height: 81px!important; display: block; margin: 5px auto 0 auto;
}
.btn11{
	padding-top: 30px;
	position: absolute;
	left: 82px;
	top: 613px;
	width: 700px;
	line-height: 40px;
	text-align: center; color: #c30d23; font-size: 28px;
}

.logintc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.logintc .bt1{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.logintc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.logintc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.logintc .zj{letter-spacing:16px;}
.logintc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 305px;line-height: 45px;}
.logintc .yzm:hover{background-color: #f4f4f4;}
.logintc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.logintc .tc-btn1:hover{ background-color: #3689ce;}
.logintc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.logintc .tc-btn2:hover{ background-color: #cecece;}
.logintc input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}

.tc-txt{
	width: 454px;
	position: absolute;
	left: 0%;
	top: 220px; text-align: center; font-size: 16px; color: #000;
}
