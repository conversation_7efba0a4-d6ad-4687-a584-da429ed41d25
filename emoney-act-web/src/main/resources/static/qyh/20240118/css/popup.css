/*遮罩*/
.popup-mask {
	position: fixed;
	top: 0px;
	left: 0px;
	width: 100%;
	height: 100%;
	background-color: #000;
	opacity: 0.5;
	filter: alpha(opacity=50);
	display: none;
}
/*弹窗*/
.popup-wrap {
	width: 662px;
	height: 446px;
	background: url(../images/tc.png) center no-repeat;
	overflow: hidden;
	display: none;
}
.popup-wrap .popup-title {
	height: 40px;
	line-height: 40px;
	text-align: center;
	font-size: 16px;
	color: #fff;
	overflow: hidden;
}
.popup-wrap .popup-body {
	padding: 10px;
	font-size: 14px;
	text-align: center;
}
.popup-wrap .popup-operate {
	margin-top: 16px;
	text-align: center;
}
.popup-wrap .popup-operate input[type=button] {
	height: 35px;
	line-height: 35px;
	padding: 0 20px;
	margin-right: 10px;
	font-size: 16px;
	cursor: pointer;
}
.popup-wrap .popup-operate input[type=button]:last-child {
	margin-right: 0;
}
.popup-wrap .popup-close {
	position: absolute;
	top: 0px;
	right: 0px;
	width: 50px;
	height: 50px;
	line-height: 50px;
	font-size: 30px;
	color: #fff;
	text-align: center;
	cursor: pointer;
}

/*新增*/
input{background:transparent;border:0px;margin:0px; color:#939598;list-style:none; font-family:"微软雅黑"; font-size:14px;}
.font3{font-size:30px; color:#a50000; padding:35px 0 10px; line-height:30px; font-weight:bold;}
.t2{width:330px; margin:15px auto;}
.srk{background:transparent;border:1px solid #ccc; padding: 0 15px; height: 40px; line-height: 40px; float: left; width: 188px; color:#666;list-style:none; font-family:"微软雅黑"; font-size:14px; text-align: center;}
a.yzm:link,a.yzm:visited{ background-color: #f5dad8; width: 105px; height: 42px; display: block; float: left; line-height: 42px; text-align: center; font-size: 14px; color: #c43832; margin-left: 5px;}
a.yzm:hover{color: #000;}
a.btn_tc2{width: 105px; height: 42px; background:url(../images/tc_12.png) no-repeat; line-height:42px; color:#ffffff; font-size:16px; display:block;float: left;margin-left: 5px;}