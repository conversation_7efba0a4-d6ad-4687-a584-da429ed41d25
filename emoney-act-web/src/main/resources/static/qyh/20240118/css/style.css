@charset "utf-8";
/* CSS Document */
*{margin:0; padding:0;}
body{background-color:#681A11; color: #EDD9A9; font-size: 17px;font-family:"微软雅黑"; line-height: 28px; overflow-x: hidden;}
A:link,a:visited {COLOR: #fff; TEXT-DECORATION: none;}
A:hover {COLOR:#ffff00; TEXT-DECORATION: none}

.red{color: #c10d23;}
.green{color: #A48B78}
.org{color: #ffff00}
.yellow{color: #fffac7; font-weight: bold;}
.img_1{height:379px; background: url("../images/index_01.png") center;}
.img_2{height:286px;background: url("../images/index_02.png") center;}
.img_3{height:611px;background: url("../images/index_03.png") center;}
.img_4{height:597px; background: url("../images/index_04.png") top center;}
.img_5{height:351px; background: url("../images/index_05.png") top center;}
.img_6{height:886px; background: url("../images/index_06.png") top center;}
.img_7{height:570px; background: url("../images/index_07.png") top center;}
.img_8{height:466px; background: url("../images/index_08.png") top center;}

.img_12{height:145px; background: url("../images/pf2.png") center; position: fixed; left: 0px; bottom: 0px; width: 100%; }
.bg1{padding-top: 50px}

.swf1{
	width: 385px;
  height: 232px;
	position: absolute;
	top: 225px;
	left: 145px;
}
.main{width: 100px; position: relative; margin: auto;}
.sx{cursor:pointer}
.yc{display: none}
.n1{
	position: absolute;
	left: 39px;
  top: 117px;
  background: url("../images/n1.png") center;
  width: 962px;
  height: 67px;
}
.n1 a{width: 160px;display: block; height: 67px;float: left;}

.dw1{position: absolute;top: 225px;left: 145px;}

.d1{background: url("../images/d1.png") center; width: 1001px; height: 438px; position: relative; margin: 0 auto; overflow: hidden;}
.d2{background: url("../images/d2.png") center; width: 1043px; height: 595px; position: relative; margin: 50px auto 0;}

.b1{background: url("../images/b1.png") center top; width: 1039px; height: 871px; position: relative; margin: 50px auto; text-align: center;}
.bk{
	border: 2px solid #C42221;
	border-radius: 27px;
	width: 932px;
	height: 525px;
	position: absolute;
	top: 245px;
	left: 51px;
}
.bk1{
	border-radius: 14px;
	width: 660px;
	height: 420px;
	border: 1px solid #C02819;
	position: absolute;
	left: -257px;
	top: 116px;
}

ul, li {
	margin: 0;
	padding: 0;
	list-style: none;
}

.txt1{
	width: 146px;
	color: #A3A3A3;
	font-size: 42px;
	font-weight: bold;
	position: absolute;
	left: -364px;
	top: 154px;
	line-height: 42px;
}
.txt2{
	width: 248px;
	color: #6a6a6a;
	font-size: 16px;
	font-weight: bold;
	position: absolute;
	left: -412px;
	top: 202px;
	text-align: center;
}
.txt3{
	width: 248px;
	font-size: 240px;
	line-height: 240px;
	color: #e50000;
	font-weight: bold;
	position: absolute;
	left: 85px;
	top: 123px;
	text-align: center;
}
.btn1{
	background: url(../images/btn1.png);
	width: 339px;
	height:96px;
	position: absolute;
	left: 166px;
	top: 162px;
}
.btn2{
	background: url(../images/btn2.png);
	width: 358px;
	height: 130px;
	position: absolute;
	left: 168px;
	top: 11px;
}
.btn3{
	width: 296px;
	height: 69px;
	position: absolute;
	left: 236px;
	top: 118px;
}

.pf{
	background: url("../images/pf.png");
	width: 242px;
	height: 263px;
	position: fixed;
	right: 0px;
	top: 20%;
}

.al{
	position: absolute;
	left: 36px;
	top: 111px;
	width: 962px;
}
.al2{
	position: absolute;
	left: -433px;
  top: 88px;
	width: 969px;
}
.slider_box{ margin: 0px auto; width:962px; height: 495px; position: relative;overflow:hidden; border-bottom-right-radius: 10px;border-bottom-left-radius: 10px;}
.silder_con{ height:463px;border-radius: 14px;overflow: hidden; position: absolute; color: #c30d23; font-size: 28px; line-height: 50px;}
.silder_panel{width: 962px; height: 463px;border-radius: 14px;overflow: hidden; float: left; position: relative; text-align:center;}
.silder_nav {height: 22px; position: absolute; bottom: 0px; left:438px;}
.silder_nav ul{width: 962px;}
.silder_nav li {float: left;cursor:pointer;width:15px; height:15px; overflow: hidden; display: block; background-color:#FEE8CA; border-radius: 50%; margin-right: 10px;}
.silder_nav li.current { background-color: #fff;}
.silder_nav li:last-child{margin: 0px;}

.slider_box2{ margin: 0px auto; width:969px; height:608px; position: relative;overflow:hidden; border-bottom-right-radius: 10px;border-bottom-left-radius: 10px;}
.silder_con2{ height:969px;border-radius: 14px;overflow: hidden; position: absolute; color: #c30d23; font-size: 28px; line-height: 50px;}
.silder_panel2{width: 969px; height: 969px;border-radius: 14px;overflow: hidden; float: left; position: relative; text-align:center;}


a.prev{ background: url("../images/left.png") no-repeat; width: 68px; height: 68px; text-indent: -999px; display: block; position: absolute;left: -70px; top:40%;}
a.next{ background: url("../images/right.png") no-repeat; width:68px; height:68px; text-indent: -999px; display: block; position: absolute; right: -70px; top:40%;}


.close{
	background: url("../images/close.png") center top no-repeat;
	width: 30px;
	height: 30px;
	position: absolute;
	top: 0;
	right: 0;
}
.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/h.png); display: none}
.tc{background-image:url(../images/tc.png); 
	width: 662px;
	height:446px;
	position: absolute;
	left: 50%;
	top: 50%; margin: -223px 0 0 -331px;
}


.bod .dh{-webkit-animation: dh 0.3s linear infinite alternate;animation: dh 0.3s linear infinite alternate;}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
}


.left{
	position: absolute;
	left: -430px;
	top: 114px;
}
.sp{
	position: absolute;
	left: -91px;
	top: 142px;
	width: 625px;
	height: 410px;
}

.footer{text-align:center; font-family:"宋体"; font-size:12px; padding:0px 0 170px; color: #fff;}

.tc1{width:420px; height:244px; background:url(../images/tc1.png) no-repeat; text-align:center;display:none;}
.font1{font-size:30px; color:#a50000; padding-top:28px; line-height:42px; font-weight:bold;}
.font2{font-size:37px; color:#a50000; padding-top:35px; line-height:78px;}
.font2 span{color:#000000; font-size:28px; line-height:42px;}
.t1{width:160px; margin:10px auto;}
.text1{width:28px; height:28px; border:1px solid #9e9e9e; float:left; margin:0 5px; text-align:center; line-height:28px; font-size:24px; color:#666666;}
.clear{clear:both;}
.btn_tc a{width:296px; height:45px; margin:15px auto; background:url(../images/tc_12.png) no-repeat; line-height:43px; color:#ffffff; font-size:24px; display:block;}
a{text-decoration:none;}

.bg{background-image:url(../images/h.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.logintc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.logintc .bt1{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.logintc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.logintc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.logintc .zj{letter-spacing:16px;}
.logintc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 316px;line-height: 45px;}
.logintc .yzm:hover{background-color: #f4f4f4;}
.logintc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.logintc .tc-btn1:hover{ background-color: #3689ce;}
.logintc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.logintc .tc-btn2:hover{ background-color: #cecece;}
input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}

.tc-txt{
	width: 454px;
	position: absolute;
	left: 0%;
	top: 220px; text-align: center; font-size: 16px; color: #000;
}