<!DOCTYPE html>
<html xmlns:gs="https://www.gensee.com/ec">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="x-dns-prefetch-control" content="on" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, minimal-ui"
    />
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta
            name="apple-mobile-web-app-status-bar-style"
            content="black-translucent"
    />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta name="full-screen" content="yes" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="keywords" content="" />
    <meta name="description" content="" />
    <title>益盟实战大直播</title>
    <script type="text/javascript" src="https://static.emoney.cn/static/libs/common/emwfpin.js"></script>
    <script type="text/javascript">
        document.domain = 'emoney.cn';
        // (function(){
        //     window.showFlash= '';
        //     var userAgent = navigator.userAgent;
        //     var isIE = (userAgent.indexOf("compatible") > -1) && userAgent.indexOf("MSIE") > -1;
        //
        //     isNeedInstallFlash = function() {
        //         var flashVersion = (function() {
        //             try {
        //                 if (typeof window.ActiveXObject != 'undefined') {
        //                     return parseInt((new ActiveXObject('ShockwaveFlash.ShockwaveFlash')).GetVariable("$version").split(" ")[
        //                             1].split(",")[0], 10);
        //                 } else {
        //                     return parseInt(navigator.plugins["Shockwave Flash"].description.split(' ')[2], 10);
        //                 }
        //             } catch (e) {
        //                 return 0;
        //             }
        //         })();
        //         if (flashVersion <= 0) {
        //             window.showFlash = ('<table style="width: 100%;    height: 100%;    position: fixed;    z-index: 2147483647;    background-color: #000;    text-align: center;"><tr><td><div style="width: 320px;    background-color: #f6f6f6;    color: #000;    margin: auto;    padding: 30px;"><div>（未安装flash插件，无法播放视频！）</div><a href="http://static.emoney.cn/ds/videolive/FlashPlayerPPAPI_23.0.0.207.zip" target="_blank" style="text-decoration:underline;color:#c00;">点击下载flash插件</a></div></td></tr></table>');
        //             return;
        //         }
        //     }
        //
        //     var oHead = document.getElementsByTagName("HEAD").item(0);
        //     var oScript= document.createElement("script");
        //     oScript.type = "text/javascript";
        //
        //     if(isIE){
        //         isNeedInstallFlash();
        //         if(parseInt(navigator.userAgent.match(/MSIE\s([\d\.]+)/i)[1]) <= 10){
        //             oScript.src="http://static.gensee.com/webcast/static/sdk/js/gssdk.js?201806v477";
        //         }
        //     }else{
        //         oScript.src="http://static.gensee.com/webcast/static/sdk/js/gssdk-1.3.js?201806v477";
        //     }
        //     oHead.appendChild( oScript);
        // })()
    </script>
    <style type="text/css">
        input:focus,
        select:focus,
        textarea:focus,
        button:focus {
            outline: none;
        }
        html,
        body,
        div,
        span,
        applet,
        object,
        iframe,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6,
        p,
        blockquote,
        pre,
        a,
        abbr,
        acronym,
        address,
        big,
        cite,
        code,
        del,
        dfn,
        em,
        img,
        ins,
        kbd,
        q,
        s,
        samp,
        small,
        strike,
        strong,
        sub,
        sup,
        tt,
        var,
        b,
        u,
        i,
        center,
        dl,
        dt,
        dd,
        ol,
        ul,
        li,
        fieldset,
        form,
        label,
        legend,
        table,
        caption,
        tbody,
        tfoot,
        thead,
        tr,
        th,
        td,
        article,
        aside,
        canvas,
        details,
        embed,
        figure,
        figcaption,
        footer,
        header,
        hgroup,
        menu,
        nav,
        output,
        ruby,
        section,
        summary,
        time,
        mark,
        audio,
        video {
            margin: 0;
            padding: 0;
            border: 0;
            font-size: 100%;
            font: inherit;
        }
        article,
        aside,
        details,
        figcaption,
        figure,
        footer,
        header,
        hgroup,
        menu,
        nav,
        section {
            display: block;
        }
        body {
            line-height: 1;
        }
        ol,
        ul {
            list-style: none;
        }
        blockquote,
        q {
            quotes: none;
        }
        blockquote:before,
        blockquote:after,
        q:before,
        q:after {
            content: '';
            content: none;
        }
        table {
            border-collapse: collapse;
            border-spacing: 0;
        }
        html,
        body {
            width: 100%;
            height: 100%;
        }
        * + html html,
        body {
            height: auto;
        }
        body {
            font: 14px/20px 'Microsoft YaHei', SimHei, 'HelveticaNeue',
            'Helvetica Neue', Helvetica, Arial, sans-serif;
            color: #333;
            -webkit-text-size-adjust: 100%;
            background: #000;
        }
        html,
        body {
            height: 100%;
            width: 100%;
            overflow: hidden;
            -webkit-transition: all 0.36s;
            transition: all 0.36s;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            outline: none;
        }
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-weight: normal;
            font-size: 1em;
        }
        h1 a,
        h2 a,
        h3 a,
        h4 a,
        h5 a,
        h6 a {
            font-weight: inherit;
        }
        em {
            font-style: normal;
        }
        strong {
            font-weight: normal;
        }
        small {
            font-size: 80%;
        }
        a {
            text-decoration: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
            cursor: pointer;
        }
        a,
        a:visited {
            color: #333;
            text-decoration: none;
            outline: 0;
        }
        a:hover,
        a:focus {
            color: #333;
        }
        p a,
        p a:visited {
            line-height: inherit;
        }
        ul {
            list-style: none outside;
        }
        ol {
            list-style: decimal;
        }
        ol,
        ul.square,
        ul.circle,
        ul.disc {
            margin-left: 30px;
        }
        ul.square {
            list-style: square outside;
        }
        ul.circle {
            list-style: circle outside;
        }
        ul.disc {
            list-style: disc outside;
        }
        ul ul,
        ul ol,
        ol ol,
        ol ul {
            margin: 4px 0 5px 30px;
            font-size: 90%;
        }
        i {
            font-style: normal;
        }
        img.scale-with-grid {
            max-width: 100%;
            height: auto;
        }
        .button,
        button,
        input[type='submit'],
        input[type='reset'],
        input[type='button'],
        input[type='number'] {
            -webkit-appearance: none;
            -moz-appearance: none;
            font: 14px 'Microsoft YaHei', SimHei, 'HelveticaNeue', 'Helvetica Neue',
            Helvetica, Arial, sans-serif;
        }

        button::-moz-focus-inner,
        input::-moz-focus-inner {
            border: 0;
            padding: 0;
        }
        input:disabled {
            border: 1px solid #ddd;
            background-color: #f5f5f5;
            color: #aca899;
        }
        input[type='text'],
        input[type='date'],
        input[type='tel'],
        input[type='password'],
        input[type='email'],
        input[type='number'],
        textarea {
            border: 1px solid #dcdcdc;
            outline: none;
            font: 14px 'Microsoft YaHei', SimHei, 'HelveticaNeue', 'Helvetica Neue',
            Helvetica, Arial, sans-serif;
            color: #000;
            max-width: 100%;
            display: block;
            background: #fff;
            -webkit-appearance: none;
            -moz-appearance: none;
            -moz-border-radius: 0px;
            -webkit-border-radius: 0px;
            border-radius: 0px;
        }
        select {
            border: 1px solid #dcdcdc;
            font: 14px 'Microsoft YaHei', SimHei, 'HelveticaNeue', 'Helvetica Neue',
            Helvetica, Arial, sans-serif;
            color: #000;
            max-width: 100%;
            display: block;
            background: #fff;
            margin: 0;
        }
        input[type='text']:focus,
        input[type='date']:focus,
        input[type='password']:focus,
        input[type='email']:focus,
        textarea:focus,
        select:focus {
            color: #333;
            border-color: #448aca;
        }
        textarea {
            min-height: 40px;
        }
        label,
        legend {
            display: block;
            font-weight: normal;
        }
        input[type='checkbox'] {
            display: inline;
        }
        input[type='checkbox'],
        input[type='radio'] {
            vertical-align: middle;
            margin: 0 3px 3px 0;
        }
        input.readonly,
        .readonly {
            background-color: #eee;
        }

        .pr {
            position: relative;
        }
        .pa {
            position: absolute;
        }
        .fl {
            float: left;
        }
        .fr {
            float: right;
        }
        .clear {
            width: 100%;
            clear: both;
            font-size: 0px;
            height: 0px;
            line-height: 0px;
            margin: 0;
        }
        .imgb img {
            display: block;
        }
        .box-sizing {
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
        }
        .borderradius {
            -moz-border-radius: 3px;
            -webkit-border-radius: 3px;
            border-radius: 3px;
        }
        .opacity70 {
            background-color: rgba(120, 120, 120, 0.24);
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RjEwQzc0OUFGRTI4MTFFNkJFODREMEMzNTFEQ0E1REEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RjEwQzc0OUJGRTI4MTFFNkJFODREMEMzNTFEQ0E1REEiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpGMTBDNzQ5OEZFMjgxMUU2QkU4NEQwQzM1MURDQTVEQSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpGMTBDNzQ5OUZFMjgxMUU2QkU4NEQwQzM1MURDQTVEQSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PitJTLoAAAAkSURBVHjaYmRgYNjMQEXAxEBlMGrgqIGjBo4aOGrgUDEQIMAA7xAA24lxTg8AAAAASUVORK5CYII=');
        }
        /*通用样式处理 end*/
        .main_web {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            padding-left: 0px;
            padding-right: 0px;
        }
        .video_area {
            position: absolute;
            left: 0;
            right: 0;
            top: 0px;
            bottom: 0px;
        }
        .vtool-bar {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 53px;
            background-color: transparent;
            z-index: 10000000;
        }
        .vtool-bar .tool_area {
            height: 53px;
            visibility: hidden;
            border-top: solid 1px rgba(54, 54, 54, 0.24);
            opacity: 0.8;
            background-image: linear-gradient(
                    180deg,
                    rgba(0, 0, 0, 0),
                    rgba(0, 0, 0, 0.6)
            ); /* background-color: #272727; */
        }
        body:hover .vtool-bar .tool_area {
            visibility: visible;
        }
        .progress_area {
            height: 2px;
            position: absolute;
            left: 0;
            right: 0;
            background-color: rgba(102, 102, 102, 0.329);
            font-size: 0;
        }
        .progress_area:hover {
            height: 10px;
            position: absolute;
            left: 0;
            right: 0;
            top: -8px;
            background-color: rgba(81, 81, 81, 0.43);
            font-size: 0;
        }
        .progress_area:hover .ui-slider-handle {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #20a0ff;
            border-radius: 50px;
            top: 50%;
            margin-top: -6px;
            margin-left: -6px;
            left: 50px;
            outline: none;
        }

        a.play_area {
            display: block;
            width: 48px;
            height: 53px;
            background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAYFBMVEUAAAAgoP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8goP8A0xzOAAAAH3RSTlMAYw756dyIPuLESPOMg0MZ3grMyqBZKHUgsadqJ0sqwwhHGgAAAPRJREFUKM+Fk+luxCAQg4dwhARyH81ut/X7v2VbiBhIKq1/JMCHLMsaKKk2jZJSNaamq4RGkhYFOhbA+vaYqu8vb4HlyBwV7MbbzUIl71biWeVG1ROyjcuXg6eLPNwrLBY86KYHlpATtjrdPjNni7/MOnzDATK6Qf8mxUgJ4oPpiJoMDMOMBtCgZ5jTHg0pDAVEd+4GKJIghgWF/AcKhjdbwba3QCIPZNAxzBh1MJcSRFFCWZ8o6ovFTyfM2DTGmzNWumnFHP6743q5WLenMVmnHE1rHJNIHaxgJixcZNF5BnTXD0RD32lg3t8PNav28Tl4fg4/J7QVNcq6EX0AAAAASUVORK5CYII=')
            no-repeat center;
            cursor: pointer;
            float: left;
        }
        a.play_area.pause {
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAVFBMVEUAAACPj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj4+Pj48iNJC1AAAAG3RSTlMAA/Ni6dyIPgziy0iMQycZ3sTCoIQgsadcVSouKz3wAAAA3ElEQVQoz4WT227DIBBEd5ebwfiS2E7Szv//Z5FdbV2H0nkApCMdoWUgTfA3K2JvPtAlbBw0zvCZbQmIud+67rPPEUjbyWgRX6yWV4RVdy94dmdR94T0x3EUZLokQ8bdkvDgK+QHEpfdIB5OQNdijjBlc2V9h/QBV26KmWuQZwTy8FSDO7hjqMMBd7KY6nCCJQHXIUOasKltX8hjrcMVvjmE1vj4z8HPMMeTLfSWBYm/H9tfmYeMWpPld02WvSZKo+GfmkZluzkBbh0m5mlYHZDG/0p9xiEf3yEHRV/J0xPa89te+QAAAABJRU5ErkJggg==');
        }
        .play_time {
            float: left;
            padding: 0 15px 0 5px;
            margin-left: 10px;
            line-height: 53px;
            color: #fff;
        }
        .play_time .play_statustips {
            display: inline-block;
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAMAAABhEH5lAAAAQlBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////8IX9KGAAAAFXRSTlMAwIDU8JtYybl6aTz036qikXFJEwypoN0vAAAAYklEQVQY023ORw7AIAxEURsINT2Z+181IBAK5W9sv5Vp3s4hL8EchTYonaZWMFSygCNywEI1H68FEOUUjF+cWKJJRkJXJbrWgeixPcXukXRP74mOvEJu/gS3wpGEbJ4XNOkDhi0O0eilHwQAAAAASUVORK5CYII=');
            background-position: left center;
            background-repeat: no-repeat;
            padding-left: 20px;
            color: #ffffff;
        }
        .livetool_area {
            position: absolute;
            visibility: hidden;
            left: 0;
            right: 0;
            bottom: 0;
            height: 53px;
            border-top: solid 1px rgba(160, 160, 160, 0.23);
            background-image: linear-gradient(
                    180deg,
                    rgba(0, 0, 0, 0),
                    rgba(0, 0, 0, 0.6)
            );
        }
        .livevideo_area {
            position: absolute;
            left: 0;
            right: 0;
            top: 0px;
            bottom: 0;
        }

        .volume_area {
            position: relative;
            float: right;
            margin-right: 10px;
        }
        a.volume_a {
            display: block;
            width: 40px;
            height: 53px;
            float: left;
            cursor: pointer;
            background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAASCAMAAABsDg4iAAAAbFBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8+T+BWAAAAI3RSTlMAqoBAd8CfkH0o+OfUvLR6YBvy7cqkcDgx2czHl4ZzW0kPDpcnv+UAAACaSURBVBjTZdDpEoMgDATgBaoWr4rV2vva93/HNrQog/mV+SYDmyBUTo20FLn3jYlNaUqjWUSGrUc0fMq8MWIetcVUnl/ISYp5rBrgwA5UnTHwKHCHZYE5yY7y4gD0WYLXChj6BG8lkFULbgTH0xcvy0eCrm5xpP5FKv7YlhNG2hBeUIaiPRUzQRg8WDvM2goCrO36dO4dJD3yBz4sC3ozqeYvAAAAAElFTkSuQmCC')
            no-repeat center;
        }
        a.col_close_stop {
            background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAASCAMAAACDzGUcAAAAe1BMVEUAAADqlBjrlRjnkxjqlRfqlRjqlRfslxrqlRjqlRjqlRjrlRjqlRfplBbrlRbplRfrmhrrlRjqlRfqlRfqlBfrlRnrlRfsmBfrlxjllhHqlRjqlRjplBjrlRjqlRjqlRjqlhjqlRjrmRTmmRnrlRfqlBjrlxfumxzqlRgUxHJzAAAAKHRSTlMAgKpAj8VIE+LMv3VkOTAhHNS5sJpxViomDvjy7eW2nnpfGQqkiEwRQbwFtQAAAMhJREFUGNNlkOlygkAQBnt3uUGQQwGNGjXH9/5PGJajYir9s2urd2ZYqawM/wilNzzl9gz40NUIwCjA42R3ZdexWDJFwBiHUrBntqZnd0pHaB3HRMmBKKVSAwfdQXUE+Unh8QlGBYMvLwPFgaY8j4uFc7ZZKK3P9y2k518LUejzFVn9Ys1AkUx5ig5k787NVvBtfD6GmzQFgXfBJ/1tmd4518rOtvS5qFGN59koIxc4vvKYYv1q2vrqLejygI19ul7Sjbww/Ln6D95CD7l6+OWyAAAAAElFTkSuQmCC')
            no-repeat center;
        }

        .volume_box {
            display: none;
            margin: auto;
            width: 100%;
            height: 140px;
            bottom: 35px;
            position: absolute;
            background-color: rgba(120, 120, 120, 0.24);
        }
        .volume_area:hover .volume_box {
            display: block;
        }
        .volume_slide {
            width: 2px;
            height: 100px;
            font-size: 0;
            background-color: rgb(240, 240, 240);
            margin-top: 25px;
            position: relative;
            margin-left: 45%;
        }
        .ui-slider-horizontal .ui-slider-range-min {
            background: #01beff;
        }
        .ui-slider .ui-slider-range {
            position: absolute;
            display: block;
            border: 0;
            bottom: 0;
            background-color: #20a0ff;
            width: 2px;
        }
        .ui-slider-horizontal .ui-slider-range {
            top: 0;
            height: 100%;
        }
        .ui-slider-horizontal .ui-slider-range-min {
            bottom: 0;
        }
        .ui-slider .ui-slider-handle {
            position: absolute;
            width: 12px;
            height: 12px;
            background: #20a0ff;
            border-radius: 50px;
            bottom: 50%;
            margin-bottom: -6px;
            margin-left: -5px;
            bottom: 50px;
            outline: none;
        }

        /* 展视网络切换 */
        a.wifi_a {
            width: 32px;
            height: 53px;
            margin-right: 15px;
            float: right;
            z-index: 10001000;
            right: 10px;
            bottom: 10px;
            color: #fff;
            text-align: center;
            line-height: 32px;
            font-size: 0;
            background-color: rgb(109, 107, 107);
            background-color: rgba(80, 80, 80, 0.65);
            background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAMAAABhEH5lAAAAe1BMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////NgkbwAAAAKHRSTlMAwGRt4YBA8SzZq0kJ7tK9t5NpVc6onJeJWlAfDvr23MiihHoVD7Qx0ySP0wAAAL5JREFUGNN1z8cOwzAMA1DKI06cPZrZPfX/X1i5QZFTeBLegRCxl2y6e3+fsk1iKk/en0qK/2JZqwqolGa7yodbkAZGQsvpr6fTC3gBIsaik6NQS6WpWaep5trQWwk9BjwbSpxLqHmiHoQO4hFBQnKpS6BeqAvUaaAP9ODmFZlAZn413If6ivLaRRI35GUV6o/JFNu8cK7Ibbw+gZQBbwF7ANZX5T7P6gpc1Xy22+zCjKMpttlApm7G3FSGnXwBVpIKqrGMxoUAAAAASUVORK5CYII=')
            no-repeat center;
            cursor: pointer;
        }
        a.wifi_a:hover {
            border-radius: 16px;
            background-color: rgba(110, 110, 110, 0.6);
        }
        .net_list {
            height: 0px;
            width: 100%;
            line-height: 26px;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .net_list.layui-layer-wrap {
            height: 104px;
        }
        .net_list .iner {
            padding: 5px 0 5px 10px;
        }
        .net_list a {
            display: block;
            padding-left: 20px;
            background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAMAAAAMCGV4AAAAVFBMVEUAAACysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrKysrK9cpK1AAAAHHRSTlMAGgX678C5oGtfS0Ep6eLa1dTDsq2WfHplUjATffrs0QAAAHBJREFUCNdNzEkSgCAMRNEmzALOs/e/p4qi+btXqTSuyGmppHaE3N5MgUBhavabUa14WlUEqHMouY7gLf6shwnMwUAdzCJBgkUSSfB7jaFirgZsI/O4Af3ycenzz/xyrkUe0cbHI3qjRZmxbWptHj4Br/kDtWYxzyIAAAAASUVORK5CYII=')
            no-repeat left center;
            height: 26px;
            overflow: hidden;
        }
        .net_list a.on {
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAMAAAAMCGV4AAAAclBMVEUAAACysrKysrKysrKysrKysrKysrKysrKysrKysrIUit6ysrKysrKysrKysrIUit6ysrKysrKysrKysrIUit4Uit4Uit6ysrKysrKysrKysrKysrKysrKysrIUit4Uit6ysrKysrKysrKysrIUit4Uit6p77ZGAAAAJXRSTlMAGwf679W9e2dfS0tBKenm4sIV2tnQxLayraKflmxlX1IwBcMJTJsp1gAAAIlJREFUCNc1y0cWgyAAANERKwIae6+J979iEJ9/N4vBEknhB36RCJyjmrVA6Lk6sEyQ8EgCA2e4YP2wlvBETsCaX/kKTJJIw3bdNtARQQm16xq8DB+4HiB8shIalw14H3oFqesUVM8+AGmbtzYZdvjGvOKC+4nf/HhYXtRJUxrZRTYdNYZZOCqsP9msCgQtWRMmAAAAAElFTkSuQmCC');
            background-color: #e6f4ff;
        }
        a.net_btn {
            display: block;
            width: 100px;
            height: 38px;
            margin: 20px auto 0;
            background-color: #20a0ff;
            line-height: 38px;
            text-align: center;
            color: #fff;
            cursor: pointer;
        }
        .t_center {
            width: 50%;
            margin: 0 auto;
            height: 53px;
            line-height: 53px;
            text-align: center;
            overflow: hidden;
            color: #ffffff6e;
            font-weight: 100;
        }
        body:hover a.wifi_a {
            display: block;
        }

        /* SDK播放速率 */
        .vtool-ctrls {
            transition: all;
            display: none;
            position: fixed;
            right: 10px;
            bottom: 100px;
            background-color: transparent;
            width: 80px;
            height: 32px;
        }
        body:hover .vtool-ctrls {
            transition: display 2s;
            -moz-transition: display 2s;
            -webkit-transition: display 2s;
            -o-transition: display 2s;
            display: block;
        }
        .vtool-ctrls .vtool-pack {
            position: relative;
        }
        .vtool-ctrls .vtool-pack .play_rate {
            background-color: rgb(109, 107, 107);
            background-color: rgba(80, 80, 80, 0.65);
            border-radius: 40px;
            opacity: 0.5;
        }
        .vtool-ctrls .vtool-pack:hover .play_rate {
            cursor: pointer;
            position: absolute;
            width: 100%;
        }
        .vtool-ctrls .vtool-pack .play_rate:hover{
            opacity: 1;
        }
        .play_rate .ccH5sp {
            padding: 5px;
            text-align: center;
            display: block;
            color: #fff;
        }
        .play_rate .ccH5spul {
            display: none;
            position: absolute;
            bottom: 30px;
            background-color: rgb(109, 107, 107);
            background-color: rgba(70, 70, 70, 83%);
            padding: 0px;
            width: 80px;
            border-radius: 4px;
            overflow: hidden;
        }
        .vtool-ctrls .vtool-pack:hover .ccH5spul {
            display: block;
        }
        .play_rate .ccH5spul li {
            padding: 6px;
            color: #ffffff;
            text-align: center;
            cursor: pointer;
        }
        .play_rate .ccH5spul li.selected {
            padding: 6px; /* background-color:#fff; */
            color: rgb(255 133 0);
            text-align: center;
        }
        .play_rate .ccH5spul li:hover {
            background-color: rgb(207, 207, 207);
            background-color: rgba(255, 255, 255, 0.22);
            color: #ffc033;
        }

        /* 全屏模拟按钮 */
        .btn_fullscreen {
            width: 32px;
            height: 53px;
            float: right;
            margin-right: 15px;
            background-repeat: no-repeat;
            background-position: center center;
            cursor: pointer;
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASAgMAAAAroGbEAAAADFBMVEUAAAD///////////84wDuoAAAAA3RSTlMAQIDntwj7AAAANElEQVQI12OAANEQxlAHBvkvjP8dGOSWMK4GkgwM3DBSlIGB1YEBB4DKItRDTICaBjUZDACXSgxZMN1tfQAAAABJRU5ErkJggg==');
        }
        .btn_fullscreen:hover {
            border-radius: 16px;
            background-color: rgba(110, 110, 110, 0.6);
        }
        #toFullscreen {
            /* position:fixed;
                right:10px;
                top:25px;
                background:#000;
                color:#ffc033;*/
        }
        #toOpenaudio {
            position: fixed;
            right: 10px;
            bottom: 80px;
            background: #000;
            color: #ffc033;
        }

        html,
        body {
            height: 100%;
            width: 100%;
            overflow: hidden;
        }

        .video-detail-inner {
            height: 100%;
            position: relative;
            overflow: hidden;
            background-color: #131313;
            z-index: 30000;
        }
        #__vconsole .vc-switch {
            display: block;
            position: fixed;
            right: 0.76923077em;
            bottom: 20% !important;
            color: #fff;
            background-color: #04be02;
            line-height: 1;
            font-size: 1.07692308em;
            padding: 0.61538462em 1.23076923em;
            z-index: 2147483647;
            border-radius: 0.30769231em;
            box-shadow: 0 0 0.61538462em rgba(0, 0, 0, 0.4);
        }
        .video-alert {
            position: absolute;
            top: 35%;
            left: 0px;
            width: 100%;
            text-align: center;
            color: #fff;
            font-size: 20px;
            background-color: #131313;
        }

        .video-alert .alink-enter {
            padding: 10px 20px;
            line-height: 40px;
            margin: 0 13px;
            background-color: #ccc;
            text-align: center;
            line-height: 40px;
            color: #333;
            border-radius: 50px;
        }

        .video-alert .alink-enter:hover {
            background-color: #ff3333;
            color: #fff;
        }

        .video-alert .srk {
            width: 190px;
            height: 40px;
            border: 1px solid #ccc;
            border-radius: 50px;
            background: transparent;
            text-align: center;
            line-height: 40px;
            color: #fff;
            font-size: 20px;
        }

        .video-alert .srk:hover {
            border: 1px solid #ff3333;
        }

        .video-alert .mt1 {
            margin-top: 15px;
        }

        .video-alert .mt2 {
            margin-top: 30px;
        }

        .video-alert .alink-new {
            border-bottom: 1px solid #fff;
            color: #f5f5f5;
        }

        .video-alert .alink-help {
            display: block;
            width: 200px;
            height: 40px;
            margin: 30px auto 0 auto;
            background-color: #ff3333;
            text-align: center;
            line-height: 40px;
            color: #fff;
            border-radius: 50px;
        }

        @media screen and (max-width: 500px) {
            .video-detail-inner {
            }

            .video-alert {
                top: 22%;
                font-size: 17px;
            }
        }

        marquee {
            display: none;
        }

        .prebackgo{
            position: absolute;
            left: -149px;
            /* background-color: #ddd; */
            top: -5px;
        }
        .prebackgo .backgobtn{
            display: block;
            width: 40px;
            height: 40px;
            float: left;
            background-color: rgba(120,120,120,0.5);
            background-repeat: no-repeat;
            background-position: center center;
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAclBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////9eWEHEAAAAJXRSTlMAEFZFzYh4ZjLu3aO/1a+qJBq7bFxAC+rkmoB0YlJLOiIFkirywKDq/gAAARtJREFUOMu9kOtygyAQhbmJIGqMUXPRaJr0vP8rdiExY9XaP52eGXZWzrdygP25mqLmP7syykBSd7lqtwqJ01Vd6RQqXvo7ZNXYG4dy6bvp4QLHuX+l+o3Ipp9nCKozYjeJjzNbqEP/7nOYdx+S7H0p83FvDzcf/6AVo3iNVJRQKlBvAdxGoMAhAPp4gq7L0gPkTnS6vw4gaRYTYBB2IjxD7/IxIrLGA7SQCCZhuAlg+gSuwODNECjCjSdJFO7iCAiyBxaAIEjGdZL4Nh2ByJeDz2B4ZDmXTCOEu7CJehBpoQwTlpKEkWH2NDMpxTbVId70+ecvP8ggtuwihd6w95fTUa46Q9E0D5FbuMeqf0GQdTS+rr5rW1Gz/9UXQ1IS/YW6zjIAAAAASUVORK5CYII=');
            border-radius: 40px;
            font-size: 12px;
            line-height: 40px;
            color: #fff;
            text-align: center;
            cursor: pointer;
            margin-left: 20px;
            opacity:0.3;
        }
        .prebackgo .backgobtn:hover{ opacity: 1; }
        .prebackgo .pregobtn{
            display: block;
            width: 40px;
            height: 40px;
            float: left;
            background-color: rgba(120,120,120,0.5);
            background-repeat: no-repeat;
            background-position: center center;
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAeFBMVEUAAAD////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////GqOSsAAAAJ3RSTlMAEFVCZoh4MiPu3c2/1a+qpFtJugvq5JqAdGxiHAOgkjwqFwfyyjjPqPP7AAABGUlEQVQ4y72R55KEIBCEB1Ak6K5pc94L/f5veMiBZbj119V+P6aG6S6nBfpvmFDta7W+WTiKtP5TTixyWYlKlgZfh7neoMxiXxVYT/UN+DDKdeoooh7hY8fa6xPHbhAfJ5qxg+j7VdPVs9/eDzOsYquQuPpBE0o8Q3eEIhIWrtUAVJ+iCh+8bV1pms7Qq5eLkNhu5G8E/0cJur1+kAI7knCc/TlPoyEBDCeBjGXUFogx8zIYHCyFYsakbvkd4MGQB4MHgpg0pguvQ6A9zjFDxlLNmCAJnyUY7ji6KuAmGjYjrmEqGmItjZhdWzJ/i8v4aD8ZLcJR0DISuVp2iM12/1x0PK7QK/5oW/X9ylKXGp79wiJ+OJxqejM/13oTQeT/dQYAAAAASUVORK5CYII=');
            border-radius: 40px;
            font-size: 12px;
            line-height: 40px;
            color: #fff;
            text-align: center;
            cursor: pointer;
            margin-left: 20px;
            opacity:0.3;
        }
        .prebackgo .pregobtn:hover{ opacity: 1; }


    </style>
</head>

<body>
<div class="video-detail-inner">
    <div class="video-alert" id="classCode" style="display: none">
        该课程为加密课程，需要输入课程码方能观看
        <div class="mt1">
            <input placeholder="请输入课程码" id="txt_accesspwd" class="srk" /><a
                href="javascript:void(0)"
                class="alink-enter"
        >确认</a
        >
        </div>
        <div class="mt2">没有课程码？　请向服务专员索要</div>
    </div>
</div>

<div class="main_web">
    <div class="main_center" id="mainshow"></div>
</div>

<script
        type="text/javascript"
        src="../libs/videoplayer/js/jquery-1.9.1.min.js"
></script>
<script
        type="text/javascript"
        src="../libs/videoplayer/js/jquery-ui-1.10.3.custom.min.js"
></script>
<script type="text/javascript" src="../libs/layer/layer.js"></script>
<script type="text/javascript">
    // base64
    ~(function (root, factory) {
        if (typeof define === 'function' && define.amd) {
            define([], factory);
        } else if (typeof module === 'object' && module.exports) {
            module.exports = factory();
        } else {
            root.Base = factory();
        }
    })(window, function () {
        'use strict';
        function Base64() {
            this._keyStr =
                    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
        }
        Base64.prototype.encode = function (input) {
            var output = '',
                    chr1,
                    chr2,
                    chr3,
                    enc1,
                    enc2,
                    enc3,
                    enc4,
                    i = 0;
            input = this._utf8_encode(input);
            while (i < input.length) {
                chr1 = input.charCodeAt(i++);
                chr2 = input.charCodeAt(i++);
                chr3 = input.charCodeAt(i++);
                enc1 = chr1 >> 2;
                enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
                enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
                enc4 = chr3 & 63;
                if (isNaN(chr2)) {
                    enc3 = enc4 = 64;
                } else if (isNaN(chr3)) {
                    enc4 = 64;
                }
                output =
                        output +
                        this._keyStr.charAt(enc1) +
                        this._keyStr.charAt(enc2) +
                        this._keyStr.charAt(enc3) +
                        this._keyStr.charAt(enc4);
            }
            return output;
        };
        Base64.prototype.decode = function (input) {
            var output = '',
                    chr1,
                    chr2,
                    chr3,
                    enc1,
                    enc2,
                    enc3,
                    enc4,
                    i = 0;
            input = input.replace(/[^A-Za-z0-9\+\/\=]/g, '');
            while (i < input.length) {
                enc1 = this._keyStr.indexOf(input.charAt(i++));
                enc2 = this._keyStr.indexOf(input.charAt(i++));
                enc3 = this._keyStr.indexOf(input.charAt(i++));
                enc4 = this._keyStr.indexOf(input.charAt(i++));
                chr1 = (enc1 << 2) | (enc2 >> 4);
                chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
                chr3 = ((enc3 & 3) << 6) | enc4;
                output = output + String.fromCharCode(chr1);
                if (enc3 != 64) {
                    output = output + String.fromCharCode(chr2);
                }
                if (enc4 != 64) {
                    output = output + String.fromCharCode(chr3);
                }
            }
            output = this._utf8_decode(output);
            return output;
        };
        Base64.prototype._utf8_encode = function (string) {
            string = string.replace(/\r\n/g, '\n');
            var utftext = '';
            for (var n = 0; n < string.length; n++) {
                var c = string.charCodeAt(n);
                if (c < 128) {
                    utftext += String.fromCharCode(c);
                } else if (c > 127 && c < 2048) {
                    utftext += String.fromCharCode((c >> 6) | 192);
                    utftext += String.fromCharCode((c & 63) | 128);
                } else {
                    utftext += String.fromCharCode((c >> 12) | 224);
                    utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                    utftext += String.fromCharCode((c & 63) | 128);
                }
            }
            return utftext;
        };
        Base64.prototype._utf8_decode = function (utftext) {
            var string = '',
                    i = 0,
                    c = 0,
                    c1 = 0,
                    c2 = 0,
                    c3 = 0;
            while (i < utftext.length) {
                c = utftext.charCodeAt(i);
                if (c < 128) {
                    string += String.fromCharCode(c);
                    i++;
                } else if (c > 191 && c < 224) {
                    c2 = utftext.charCodeAt(i + 1);
                    string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
                    i += 2;
                } else {
                    c2 = utftext.charCodeAt(i + 1);
                    c3 = utftext.charCodeAt(i + 2);
                    string += String.fromCharCode(
                            ((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
                    );
                    i += 3;
                }
            }
            return string;
        };
        var Base = new Base64();
        return Base;
    });

    // cookies
    !(function ($) {
        var pluses = /\+/g;

        function encode(s) {
            return config.raw ? s : encodeURIComponent(s);
        }

        function decode(s) {
            return config.raw ? s : decodeURIComponent(s);
        }

        function stringifyCookieValue(value) {
            return encode(config.json ? JSON.stringify(value) : String(value));
        }

        function parseCookieValue(s) {
            if (s.indexOf('"') === 0) {
                s = s.slice(1, -1).replace(/\\"/g, '"').replace(/\\\\/g, '\\');
            }

            try {
                s = decodeURIComponent(s.replace(pluses, ' '));
                return config.json ? JSON.parse(s) : s;
            } catch (e) {}
        }

        function read(s, converter) {
            var value = config.raw ? s : parseCookieValue(s);
            return $.isFunction(converter) ? converter(value) : value;
        }

        var config = ($.cookie = function (key, value, options) {
            if (value !== undefined && !$.isFunction(value)) {
                options = $.extend({}, config.defaults, options);

                if (typeof options.expires === 'number') {
                    var days = options.expires,
                            t = (options.expires = new Date());
                    t.setTime(+t + days * 864e5);
                }
                return (document.cookie = [
                    encode(key),
                    '=',
                    stringifyCookieValue(value),
                    options.expires
                            ? '; expires=' + options.expires.toUTCString()
                            : '',
                    options.path ? '; path=' + options.path : '',
                    options.domain ? '; domain=' + options.domain : '',
                    options.secure ? '; secure' : ''
                ].join(''));
            }

            var result = key ? undefined : {};
            var cookies = document.cookie ? document.cookie.split('; ') : [];
            for (var i = 0, l = cookies.length; i < l; i++) {
                var parts = cookies[i].split('=');
                var name = decode(parts.shift());
                var cookie = parts.join('=');
                if (key && key === name) {
                    result = read(cookie, value);
                    break;
                }
                if (!key && (cookie = read(cookie)) !== undefined) {
                    result[name] = cookie;
                }
            }

            return result;
        });

        config.defaults = {};

        $.removeCookie = function (key, options) {
            if ($.cookie(key) === undefined) {
                return false;
            }

            $.cookie(
                    key,
                    '',
                    $.extend({}, options, {
                        expires: -1
                    })
            );
            return !$.cookie(key);
        };
    })($);

    if (!window.console) {  window.console = {}; }
    if (!window.console.log) { window.console.log = function (msg) {}; }

    window.vodchannel = null;
    window.gConfig = {liveVideoApiHost:"https://act.emoney.cn/activity/firstclass/"};
    //window.gConfig = {liveVideoApiHost:"https://preact.emoney.cn/activity/firstlass/"};
    //window.gConfig = {liveVideoApiHost:"http://127.0.0.1:8088/page/"};
    //window.gConfig = { liveVideoApiHost: 'http://testact.emoney.cn/activity/firstclass/' };

    window.showFlash = '';

    var userAgent = navigator.userAgent;
    var isIE = !!window.ActiveXObject;
    var isIE6 = isIE && !window.XMLHttpRequest;
    var isIE8 = isIE && !!document.documentMode;
    var isIE7 = isIE && !isIE6 && !isIE8;
    var oldSDK =
            'https://static.gensee.com/webcast/static/sdk/js/gssdk.js?r=' +
            Math.random();
    var newSDK =
            'https://static.gensee.com/webcast/static/sdk/js/gssdk-1.3.js?r=' +
            Math.random();
    function includejs(file, cb) {
        var _doc = document.getElementsByTagName('head')[0];
        var js = document.createElement('script');
        js.setAttribute('type', 'text/javascript');
        js.setAttribute('src', file);
        _doc.appendChild(js);

        if (isIE6 || isIE7) {
            //if not IE
            //IE6、IE7 support js.onreadystatechange
            js.onreadystatechange = function () {
                if (js.readyState == 'loaded' || js.readyState == 'complete') {
                    cb();
                }
            };
        } else {
            //Firefox2、Firefox3、Safari3.1+、Opera9.6+ support js.onload
            js.onload = function () {
                cb();
            };
        }
        return false;
    }
    isNeedInstallFlash = function () {
        var flashVersion = (function () {
            try {
                if (typeof window.ActiveXObject != 'undefined') {
                    return parseInt(
                            new ActiveXObject('ShockwaveFlash.ShockwaveFlash')
                                    .GetVariable('$version')
                                    .split(' ')[1]
                                    .split(',')[0],
                            10
                    );
                } else {
                    return parseInt(
                            navigator.plugins['Shockwave Flash'].description.split(' ')[2],
                            10
                    );
                }
            } catch (e) {
                return 0;
            }
        })();
        if (flashVersion <= 0) {
            window.showFlash =
                    '<table style="width: 100%;    height: 100%;    position: fixed;    z-index: 2147483647;    background-color: #000;    text-align: center;"><tr><td><div style="width: 320px;    background-color: #f6f6f6;    color: #000;    margin: auto;    padding: 30px;"><div>（未安装flash插件，无法播放视频！）</div><a href="http://static.emoney.cn/ds/videolive/FlashPlayerPPAPI_23.0.0.207.zip" target="_blank" style="text-decoration:underline;color:#c00;">点击下载flash插件</a></div></td></tr></table>';
            document.body.innerHTML = window.showFlash;
            return;
        }
    };

    if (isIE) {
        isNeedInstallFlash();
        if (parseInt(navigator.userAgent.match(/MSIE\s([\d\.]+)/i)[1]) <= 10) {
            includejs(oldSDK, function () {
                setTimeout(function () {
                    pagerun($);
                }, 300);
            });
        }
    } else {
        includejs(newSDK, function () {
            setTimeout(function () {
                pagerun($);
            }, 300);
        });
    }

    function pagerun($) {
        var Utils = {
            isMobile: /(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent),
            IEVersion: function () {
                var userAgent = navigator.userAgent;
                var isIE =
                        userAgent.indexOf('compatible') > -1 &&
                        userAgent.indexOf('MSIE') > -1;
                var isEdge = userAgent.indexOf('Edge') > -1 && !isIE;
                var isIE11 =
                        userAgent.indexOf('Trident') > -1 &&
                        userAgent.indexOf('rv:11.0') > -1;
                if (isIE) {
                    var reIE = new RegExp('MSIE (\\d+\\.\\d+);');
                    reIE.test(userAgent);
                    var fIEVersion = parseFloat(RegExp['$1']);
                    if (fIEVersion == 7) {
                        return 7;
                    } else if (fIEVersion == 8) {
                        return 8;
                    } else if (fIEVersion == 9) {u
                        return 9;
                    } else if (fIEVersion == 10) {
                        return 10;
                    } else {
                        return 6;
                    }
                } else if (isEdge) {
                    return 13;
                } else if (isIE11) {
                    return 11;
                } else {
                    return -1;
                }
            },
            isIElow: function () {
                var IEV = Utils.IEVersion();
                return IEV > 0 && IEV < 10;
            },
            canvasSupport: function () {
                return !!document.createElement('canvas').getContext;
            },
            trim: function (str) {
                if (typeof str !== 'string') {
                    return str;
                }
                if (typeof str.trim === 'function') {
                    return str.trim();
                } else {
                    return str.replace(
                            /^(\u3000|\s|\t|\u00A0)*|(\u3000|\s|\t|\u00A0)*$/g,
                            ''
                    );
                }
            },
            isEmpty: function (obj) {
                if (obj === undefined) {
                    return true;
                } else if (obj == null) {
                    return true;
                } else if (typeof obj === 'string') {
                    if (this.trim(obj) == '') {
                        return true;
                    }
                }
                return false;
            },
            isNotEmpty: function (obj) {
                return !this.isEmpty(obj);
            },
            breachHTML: function (str) {
                if (typeof str !== 'string' || this.isEmpty(str)) return str;
                return str.replace(/\</g, '&lt;');
            },
            escapeHTML: function (str) {
                if (typeof str !== 'string' || this.isEmpty(str)) return str;
                return str.replace(/\&/g, '&amp;').replace(/\</g, '&lt;');
            },
            checkTime: function (num) {
                var n = Number(num);
                if (n < 10) n = '0' + n;
                return n;
            },
            timeDuration: function (second) {
                if (!second || isNaN(second)) return;
                second = parseInt(second);
                var time = '';
                var hour = (second / 3600) | 0;
                if (hour != 0) {
                    time += this.checkTime(hour) + ':';
                }
                var min = ((second % 3600) / 60) | 0;
                time += this.checkTime(min) + ':';
                var sec = (second - hour * 3600 - min * 60) | 0;
                time += this.checkTime(sec);
                return time;
            },
            calcPercent: function (value, total) {
                if (isNaN(value) || Number(value) == 0) return '0';
                if (isNaN(total) || Number(total) == 0) return '0';
                return Math.round((Number(value) * 100) / Number(total));
            },
            formatTime: function (time) {
                var date = new Date();
                date.setTime(time);
                var h = date.getHours();
                var m = date.getMinutes();
                var s = date.getSeconds();
                return (
                        this.checkTime(h) +
                        ':' +
                        this.checkTime(m) +
                        ':' +
                        this.checkTime(s)
                );
            },
            replaceholder: function (str, values) {
                return str.replace(/\{(\d+)\}/g, function (m, i) {
                    return values[i];
                });
            },
            GetQueryString: function (name) {
                var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]);
                return null;
            },
            getUID: function () {
                var _this = this;
                var uid =
                        $.cookie('firstclass.uid') || _this.GetQueryString('uid') || 0;
                return uid;
            },
            EMSSO: function () {
                var _this = this;
                var ssoSTR = location.search;
                var puressostr = ssoSTR.split('rand=')[1];
                return 'rand=' + puressostr;
            },
            setCookie: function (cname, cvalue, exhours) {
                var expires;

                if (exhours) {
                    var date = new Date();
                    date.setTime(date.getTime() + exhours * 60 * 60 * 1000);
                    expires = '; expires=' + date.toGMTString();
                } else {
                    expires = '';
                }
                document.cookie =
                        encodeURIComponent(cname) +
                        '=' +
                        encodeURIComponent(cvalue) +
                        expires +
                        '; path=/';
            },

            getCookie: function (cname) {
                var name = cname + '=';
                var ca = document.cookie.split(';');
                for (var i = 0; i < ca.length; i++) {
                    var c = $.trim(ca[i]);
                    if (c.indexOf(name) == 0)
                        return c.substring(name.length, c.length);
                }
                return '';
            }
        };

        window.vodchannel = GS.createChannel();

        var midtype = Utils.GetQueryString('midtype') || 'live';
        var ownerid =
                Utils.GetQueryString('ownerid') || 'e1fa7b37683c4bb3add75b8d44881b78';
        var uid = Utils.GetQueryString('uid') || '';
        var pid = Utils.GetQueryString('pid') || '1';
        var bztype = Utils.GetQueryString('bztype') || '';
        uid =
                ['1000404582', '1900436117', '1000404390'].indexOf(uid) > -1
                        ? uid + ('' + new Date().getTime()).substr(-5)
                        : uid;
        var uname = '大师第一课：' + uid + ':' + pid;
        var authcode = Utils.GetQueryString('authcode') || '888888';
        var appid = Utils.GetQueryString('appid') || '1015010';
        var audiohtmlCodes = [];
        var voteid = Utils.GetQueryString('voteid') || '30';

        //var vsite = !bztype ? "emoney.gensee.com" : "eresearch.gensee.com";
        var vsite = !!bztype ? bztype : 'emoney.gensee.com';

        var vlivestart = Utils.GetQueryString('vlivestart') || '';
        var vliveend = Utils.GetQueryString('vliveend') || '';
        var vlivecurrent = new Date();
        var vstarttimer = new Date(vlivestart.replace('-', '/')).getTime();
        var vendtimer = new Date(vliveend.replace('-', '/')).getTime();
        var vcurrenttimer = vlivecurrent.getTime();
        var isEMCLIENT = true;
        var currentPlayTimer = 0;

        window.Utils = Utils;
        window.vodchannel = GS.createChannel();

        (function () {
            try {
                isEMCLIENT = window.external.EmObj;
            } catch (err) {
                isEMCLIENT = !1;
            }
        })();

        // PC端
        showbar = 'display:none;'; //!Utils.isMobile ? 'display:block;' : 'display:none;';
        //showbar = isEMCLIENT ? midtype == 'vlive' ? 'display:block;': 'display:none;': 'display:block;';
        var showbar = isEMCLIENT
                ? midtype == 'vlive'
                        ? 'display:block;'
                        : 'display:none;'
                : Utils.IEVersion() != '-1'
                        ? 'display:block;'
                        : 'display:none;';
        var hidethis = 'display:none;';
        var gsver =
                Utils.IEVersion() == -1 || Utils.IEVersion() > 10 ? 'gsver="2"' : '';
        var showPlayrate =
                Utils.IEVersion() <= 10 && Utils.IEVersion() != -1
                        ? 'display:none;'
                        : '';
        var isChrome = navigator.userAgent.match(/Chrome\/([\d\.]+)/i);
        if (!!isChrome) {
            if (isChrome[1].split('.')[0] < 78) {
                showPlayrate = 'display:none;';
            }
        }

        if (Utils.isMobile) {
            function originScreen() {
                var _orienter = new window.Orienter();

                this.props.updateOrientation(_orienter.direction);
                _orienter.onOrient = function (obj) {
                    if (this.dir !== obj.dir) {
                        this.dir = obj.dir;
                        setTimeout(function () {
                            this.props.updateOrientation(obj.dir);
                        }, 200);
                    }
                };

                _orienter.on();
            }
            function bodyOrientationChange() {
                setTimeout(function () {
                    winWidth =
                            window.innerWidth > 0 ? window.innerWidth : screen.width;
                    winHeight =
                            window.innerHeight > 0 ? window.innerHeight : screen.height;
                    video_height = parseInt((9 * winWidth) / 16);

                    if (isPortrait()) {
                        $('#topHalf').height(video_height);
                        $('#doc-box').height(winHeight - video_height - tabsHeight);
                        $('.msg-content,.qa_txt').width(winWidth - 20);
                        $('.chat-bd,.qa_list_content').height(
                                winHeight - video_height - tabsHeight
                        );
                        $('.chapter-list-container').height(
                                winHeight -
                                video_height -
                                tabsHeight -
                                $('.chapter-hd').height()
                        );
                    } else {
                        $('#topHalf').height(winHeight);
                    }
                }, 100);
            }
            function createOrientationChangeProxy(fn, scope) {
                return function () {
                    clearTimeout(scope.orientationChangedTimeout);
                    var args = Array.prototype.slice.call(arguments, 0);
                    scope.orientationChangedTimeout = setTimeout(
                            $.proxy(function () {
                                var ori = window.orientation;
                                if (ori != scope.lastOrientation) {
                                    fn.apply(scope, args);
                                }
                                scope.lastOrientation = ori;
                            }, scope),
                            500
                    );
                };
            }
            function isPortrait() {
                if (
                        window.orientation == 0 &&
                        window.innerWidth > window.innerHeight
                ) {
                    return false;
                } else {
                    return (
                            window.orientation == 180 ||
                            window.orientation == 0 ||
                            window.orientation == undefined
                    );
                }
            }
            window.addEventListener(
                    'onorientationchange' in window ? 'orientationchange' : 'resize',
                    createOrientationChangeProxy(function () {
                        bodyOrientationChange();
                    }, window),
                    false
            );
            if (window.orientation == 90 || window.orientation == -90) {
            } else {
            }
            window.onorientationchange = function () {
                switch (window.orientation) {
                    case -90:
                    case 90:

                    case 0:
                    case 180:
                        break;
                }
            };
        }
        function toggleFullScreen(e) {
            var el = e.srcElement || e.target;
            var fel = $('html')[0];
            var isFullscreen =
                    document.fullScreen ||
                    document.mozFullScreen ||
                    document.webkitIsFullScreen;
            if (!isFullscreen) {
                (fel.requestFullscreen && fel.requestFullscreen()) ||
                (fel.mozRequestFullScreen && fel.mozRequestFullScreen()) ||
                (fel.webkitRequestFullscreen && fel.webkitRequestFullscreen()) ||
                (fel.msRequestFullscreen && fel.msRequestFullscreen());
                $('html').addClass('fullscreen');
            } else {
                document.exitFullscreen
                        ? document.exitFullscreen()
                        : document.mozCancelFullScreen
                        ? document.mozCancelFullScreen()
                        : document.webkitExitFullscreen
                                ? document.webkitExitFullscreen()
                                : '';
                $('html').removeClass('fullscreen');
            }
        }

        $('body').on('click', '', function (e) {
            var el = e.srcElement || e.target;
            if (el.id == 'toFullscreen') toggleFullScreen(e);
        });
        $('body').on('dblclick', '#play_div', function (e) {
            toggleFullScreen(e);
        });
        $('body').on('click', '#play_div', function (e) {
            if ($('.play_area').hasClass('pause')) {
                vodchannel.send('pause', {});
            } else {
                vodchannel.send('play', {});
            }
        });
        function playLive() {
            layer.msg('直播连接中……', {
                time: 0
            });
            vodchannel.bind('onStart', function (event) {
                layer.closeAll();
                $('#play_status').html('播放中');
            });
            vodchannel.bind('onPause', function (event) {
                layer.closeAll();
                // layer.msg('直播pause',{ time:0},function () {});
                // $('#defaultBgImg').attr('src', 'http://edf.emoney.cn/emoney/M00/00/ED/rBwBVmEWM9-ABPNlAAD01bF8Zhk324.jpg').show();
                $('#play_status').html('已暂停');
            });
            vodchannel.bind('onPlay', function (event) {
                layer.closeAll();
                //layer.msg('直播play',{ time:0},function () {});

                $('#play_status').html('播放中');
            });
            vodchannel.bind('onStop', function (event) {
                layer.closeAll();
                // layer.msg('直播结束了',{ time:0},function () {});
                // $('#defaultBgImg').attr('src', 'http://edf.emoney.cn/emoney/M00/00/ED/rBwBVmEWM9-ABPNlAAD01bF8Zhk324.jpg').show();
                $('#play_status').html('已结束');
            });

            vodchannel.bind('onKickOut', function (event) {
                layer.closeAll();
                layer.alert('您已在别处登录观看直播');
            });

            vodchannel.bind('onUserOnline', function (evt) {
                $('#onlineCounts').text(evt.data.count);
            });

            vodchannel.bind('onDoubleClick', function (event) {});

            vodchannel.bind('onStatus', function (event) {
                var status = {
                    1: '没有授权',
                    2: '点播未开始，等待点击开始按钮',
                    3: '正在缓冲',
                    4: '不能在iPAD中播放',
                    5: '正在跳转',
                    6: '表示有人登陆',
                    7: '人数已经满了',
                    8: '数据还没有准备好',
                    9: '视频第一次缓冲播放开始'
                };

                console.log(event.data);
                if (event.data.type == 3 || event.data.type == 5 ||event.data.type == 7 ) {
                    layer.msg(event.data.type + '__' + status[event.data.type],{time:3000},function(){});
                }
            });

            vodchannel.bind('onManualAutoplay', function (evt) {
                if (evt.isAutoPlay) {
                    $('#toOpenaudio').hide();
                } else {
                    $('#toOpenaudio').show();
                }
            });

            vodchannel.bind('onDataReady', function (event) {
                setTimeout(function () {
                    sdkgo();
                }, 0);
            });

            function sdkgo() {
                layer.closeAll();

                vodchannel.send('submitVolume', { value: 0.0 });

                $('.volume_slide').bind('slidechange', function (event, ui) {
                    vodchannel.send('submitVolume', { value: ui.value / 100 });
                });

                // 网络设置
                vodchannel.bind('onNetSettings', function (event) {
                    var txt = '';
                    var curNet = '当前网络';
                    for (var i = 0; i < event.data.list.length; i++) {
                        event.data.list[i].selected == 'true' &&
                        (curNet = event.data.list[i].label);
                        txt +=
                                '<a href="javascript:void(0);"' +
                                (event.data.list[i].selected == 'true' ? ' class="on"' : '') +
                                '>' +
                                event.data.list[i].label +
                                '</a>';
                    }
                    $('.net_list').html('<div class="iner">' + txt + '</div>');

                    layer.open({
                        type: 1,
                        title: '切换网络[当前:' + curNet + ']',
                        closeBtn: 0,
                        area: '300px',
                        skin: '',
                        shadeClose: true,
                        content: $('.net_list').eq(0),
                        btn: ['切换网络', '取消'],
                        end: function () {
                            $('.wifi_a.clicked').removeClass('clicked');
                        },
                        yes: function (index) {
                            if ($('.net_list a.on').length > 0) {
                                var label = $('.net_list a.on').text();

                                vodchannel.send('submitNetChoice', {
                                    label: label
                                });
                                layer.close(index);
                            } else {
                                layer.msg('请选择网络');
                            }
                        }
                    });
                    $('#cover').fadeIn();
                    $('.net_area').fadeIn();
                });
            }

            vodchannel.bind('onAPIError', function (event) {});

            $(function () {
                $('#uname').html($('#videoComponent').attr('uname'));

                $('.volume_slide').slider({
                    value: 80,
                    range: 'min',
                    orientation: 'vertical',
                    change: function () {
                        $('.volume_slide .ui-slider-range-min')
                                .show()
                                .height(
                                        parseInt($('.volume_slide .ui-slider-handle').css('bottom'))
                                );
                    }
                });
                var preValue = 80;

                function voiceOper() {
                    if ($('.volume_a').hasClass('col_close_stop')) {
                        vodchannel.send('submitMute', {
                            mute: false
                        });
                        $('.volume_a').removeClass('col_close_stop');
                        $('.volume_slide .ui-slider-handle').css({
                            bottom: parseInt(preValue) + '%'
                        });
                        $('.volume_slide .ui-slider-range-min').show();
                    } else {
                        vodchannel.send('submitMute', {
                            mute: true
                        });
                        $('.volume_a').addClass('col_close_stop');
                        preValue = $('.volume_slide .ui-slider-handle').css('bottom');
                        $('.volume_slide .ui-slider-handle').css({ bottom: '0' });
                        $('.ui-slider-range-min').hide();
                    }
                }
                $('.volume_a').on('click', voiceOper);
                vodchannel.bind('onMute', function (evt) {
                    if (evt.data) {
                        if (evt.data.mute == 'true' || evt.data.mute == true) {
                            vodchannel.send('submitMute', { mute: true });
                            $('.volume_a').addClass('col_close_stop');
                            preValue = $('.volume_slide .ui-slider-handle').css('bottom');
                            $('.volume_slide .ui-slider-handle').css({ bottom: '0px' });
                            $('.volume_slide .ui-slider-range-min').hide();
                        } else {
                            $('.volume_a').removeClass('col_close_stop');
                        }
                    }
                });

                // 网络选择
                $(document).on('click', '#wifi_a', function () {
                    var $this = $(this);
                    if ($this.hasClass('clicked')) {
                        $this.removeClass('clicked');
                    } else {
                        $this.addClass('clicked');
                        vodchannel.send('requireNetSettings', {});
                    }
                });
                $('.net_list').on('click', 'a', function () {
                    $(this).addClass('on').siblings().removeClass();
                });
            });

            var TTimer = setTimeout(function () {
                GS.loadTag(
                        'video-live',
                        document.getElementsByTagName('video-live')[0] ||
                        document.getElementsByTagName('gs:video-live')[0]
                );
                clearTimeout(TTimer);
            }, 3000);
        }

        var setLocalStorageVal = function (valkey, timerkey, val) {
            if (!!val) {
                if (!getLocalStorageVal(valkey, timerkey, 4)) {
                    window.localStorage.setItem(timerkey, new Date());
                }
                window.localStorage.setItem(valkey, val);
            }
            return null;
        };
        var getLocalStorageVal = function (valkey, timerkey, exphours) {
            var storageVal;
            var nowTime = new Date();
            if (!!window.localStorage.getItem(timerkey)) {
                var firstTime = new Date(window.localStorage.getItem(timerkey));
                var difftime = (nowTime - firstTime) / 1000;
                var days = parseInt(difftime / 86400); // 天  24*60*60*1000
                var hours = parseInt(difftime / 3600) - 24 * days; // 小时 60*60 总小时数-过去的小时数=现在的小时数

                if (hours <= exphours) {
                    storageVal = window.localStorage.getItem(valkey);
                }
            }
            return storageVal;
        };
        // 权限认证相关
        var ischeckpwd = Utils.GetQueryString('ischeckpwd');
        var checkflag = Utils.GetQueryString('checkflag');
        var id = Utils.GetQueryString('id'); //课程id
        var mobilex = Utils.GetQueryString('mobilex'); //分享页账号
        var source = Utils.GetQueryString('source'); //来源
        var isview = Utils.GetQueryString('isview'); //是否加积分
        var pointtaskcookiename = 'firstclass.posttask.' + id;
        var pointtasktimer = 'firstclass.posttasktimer.' + id;

        var pointtaskisdone = 'firstclass.posttaskisdone.' + id;
        var pointtaskisdonetimer = 'firstclass.posttaskisdonetimer.' + id;

        var sourcecookiename = 'firstclass-source1';
        var sourcecookietimer = 'firstclass-sourcetimer1';
        var timePointTask;

        // var sourcelocal = getLocalStorageVal(
        //         sourcecookiename,
        //         sourcecookietimer,
        //         4
        // );
        // if (!sourcelocal && !!source && source != 'null') {
        //     setLocalStorageVal(sourcecookiename, sourcecookietimer, source);
        //     //Utils.setCookie("firstclass.source",source,4);
        // }
        function playVOD() {
            vodchannel.bind('loadStart', function (event) {
                $('.play_area').addClass('pause');
            });

            vodchannel.bind('onPause', function (event) {
                $('.play_area').removeClass('pause');
            });

            vodchannel.bind('play', function (event) {
                $('.play_area').addClass('pause');
            });

            vodchannel.bind('onPlay', function (event) {
                $('.play_area').addClass('pause');
            });
            vodchannel.bind('onStop', function (event) {
                $('.play_area').removeClass('pause');
            });

            vodchannel.bind('onStatus', function (event) {
                if (event.data.type != '2') {
                    // 异常提示
                }
            });

            vodchannel.bind('onManualAutoplay', function (evt) {
                // console.log('onManualAutoplay***************', evt);
                if (evt.isAutoPlay) {
                    $('#toOpenaudio').hide();
                } else {
                    $('#toOpenaudio').show();
                }
            });

            var duration = 0;
            var testInterval;

            vodchannel.bind('onFileDuration', function (event) {
                duration = event.data.duration;
                $('#duration').html(Utils.timeDuration(duration / 1000));
                vodchannel.send('submitMute', { mute: false });
                vodchannel.send('submitVolume', { value: 0.8 });
                $('.progress_area').slider({
                    value: 0,
                    range: 'min',
                    change: function () {},
                    slide: function (event, ui) {
                        var played = (duration * Number(ui.value)) / 100;
                        $('#play_now').html(Utils.timeDuration(played / 1000));
                    },
                    start: function () {
                        if (testInterval) {
                            clearInterval(testInterval);
                        }
                    },
                    stop: function (event, ui) {
                        var v = ui.value;
                        var s = (duration * Number(v)) / 100;
                        var currentTime = Math.floor(Number(s));
                        vodchannel.send('seek', { timestamp: currentTime });
                    }
                });
                if (midtype == 'vlive') {
                    vodchannel.send('submitMute', { mute: true });
                    vodchannel.send('pause', {});
                    var vcurrenttimer = new Date().getTime();
                    var jumpTime = parseInt(vcurrenttimer - vstarttimer);
                    jumpTime = jumpTime <= 0 || duration <= jumpTime ? 0 : jumpTime;
                    var videotimer = parseInt(vstarttimer + jumpTime);
                    var difftimer = parseInt(duration - jumpTime);
                    if (
                            vcurrenttimer < vendtimer &&
                            vcurrenttimer > vstarttimer &&
                            difftimer > 3000
                    ) {
                        var kkkk = layer.msg(
                                '正在加载缓冲中……马上开始',
                                { time: 0, shade: 1 },
                                function () {}
                        );
                        var attimer = setTimeout(function () {
                            vodchannel.send('seek', { timestamp: jumpTime });
                            layer.closeAll();
                            clearTimeout(attimer);
                        }, 2000);
                    } else {
                        vodchannel.send('seek', {
                            timestamp: 0
                        });
                    }
                }
            });

            vodchannel.bind('onDataReady', function (event) {
                setTimeout(function () { sdkgo(); }, 0);
            });

            function sdkgo() {
                vodchannel.bind('onSeekCompleted', function (event) {
                    getplaynow();
                    vodchannel.send('submitMute', { mute: false });
                    vodchannel.send('submitVolume', { value: 0.8 });
                });
                function getplaynow() {
                    testInterval = setInterval(function () {
                        vodchannel.send('playheadTime', {});
                    }, 190);
                }
                getplaynow();

                var ttimer = null;
                var viewtime = 0;
                vodchannel.bind('onPlayheadTime', function (event) {
                    ttimer += 190;
                    var aPlayHeadTime = event.data.playheadTime;
                    currentPlayTimer = aPlayHeadTime;
                    if (ttimer > 190 * 6) {
                        ttimer = 0;
                        viewtime >= 0 && viewtime++;
                        $('#play_now').html(Utils.timeDuration(aPlayHeadTime / 1000));
                        $('.progress_area').slider(
                                'value',
                                Utils.calcPercent(aPlayHeadTime, duration)
                        );

                        //20210623录播课程观看时长>=30分钟 满足加积分条件
                        if ((viewtime * 190 * 6)  >= 1000*60*3) {
                            //alert((viewtime*190 * 6) +" "+(viewtime*190 * 6)/duration);

                            // if (source != 'point' || isview == "1") {
                            //     return;
                            // }

                            var isdone = getLocalStorageVal(
                                pointtaskisdone,
                                pointtaskisdonetimer,
                                4
                            );
                            if (!!isdone) {
                                return;
                            }

                            //增加积分
                            try {
                                $.ajax({
                                    url:
                                    window.gConfig.liveVideoApiHost +
                                    'adduserclassrecord',
                                    //url: "http://127.0.0.1:8088/page/addpointrecord",
                                    type: 'GET',
                                    dataType: 'jsonp',
                                    data: { uid: uid, classid: id, pid: pid,platform:"1" },
                                    success: function (data) {
                                        viewtime = -1;
                                        setLocalStorageVal(
                                                pointtaskisdone,
                                                pointtaskisdonetimer,
                                                1
                                        );
                                    }
                                });
                            } catch (exp) {
                                viewtime = -1;
                            }
                        }
                    }
                });
                $('.play_area').on('click', function () {
                    if ($(this).hasClass('pause')) {
                        vodchannel.send('pause', {});
                    } else {
                        vodchannel.send('play', {});
                    }
                });
                $('.volume_slide').bind('slidechange', function (event, ui) {
                    vodchannel.send('submitVolume', {
                        value: ui.value / 100
                    });
                });

                // 播放速度倍速
                $(document).on('click', '#vToolCtrls .play_rate li', function () {
                    var rate = { 0: 2, 1: 1.5, 2: 1.2, 3: 1, 4: 0.8 };
                    var $this = $(this);
                    var curRate = rate[$this.index()];

                    $this.addClass('selected').siblings().removeClass('selected');
                    $('#vToolCtrls .play_rate .ccH5sp').text($this.text());
                    vodchannel.send('submitPlaybackRate', { playbackRate: curRate });
                });

                // 跳转5秒
                $(document).on('click', '#prebackgo .pregobtn', function () {
                    var gotimer = Math.round(currentPlayTimer) + 15000;
                    var playpoint = Utils.timeDuration(gotimer/1000);
                    var allduration = Math.round(duration);
                    gotimer = gotimer<=0? 0 : gotimer >= allduration? allduration :  gotimer;
                    playpoint = gotimer<=0? '开头' : gotimer >= allduration? '结束' : Utils.timeDuration(gotimer/1000);
                    vodchannel.send('seek', { timestamp: gotimer });

                    layer.msg(
                            '前进15秒，跳转到'+playpoint,
                            { time:1600},
                            function () {}
                    );

                });
                $(document).on('click', '#prebackgo .backgobtn', function () {
                    var gotimer = Math.round(currentPlayTimer) - 15000;
                    var playpoint = Utils.timeDuration(gotimer/1000);
                    var allduration = Math.round(duration);
                    gotimer = gotimer<=0? 0 : gotimer >= allduration? allduration : gotimer;
                    playpoint = gotimer<=0? '开头' : gotimer >= allduration? '结束' : Utils.timeDuration(gotimer/1000);

                    vodchannel.send('seek', { timestamp: gotimer });
                    layer.msg(
                            '后退15秒，回到'+playpoint,
                            { time:1600},
                            function () {}
                    );
                });

                $(document).on('click', '#toOpenaudio', function () {
                    vodchannel.send('submitMute', { mute: false });
                    vodchannel.send('submitVolume', { value: 0.8 });
                    $('#toOpenaudio').hide();
                });
            }
            vodchannel.bind('onAPIError', function (event) {});

            $(function () {
                // 声音控制
                $('.volume_slide').slider({
                    value: 80,
                    range: 'min',
                    orientation: 'vertical',
                    change: function () {
                        $('.volume_slide .ui-slider-range-min')
                                .show()
                                .height(parseInt($('.volume_slide a').css('bottom')));
                    }
                });
                var preValue = 80;

                function voiceOper() {
                    if ($('.volume_a').hasClass('col_close_stop')) {
                        vodchannel.send('submitMute', { mute: false });
                        $('.volume_a').removeClass('col_close_stop');
                        $('.volume_slide .ui-slider-handle').css({
                            bottom: parseInt(preValue) + '%'
                        });
                        $('.volume_slide .ui-slider-range-min').show();
                    } else {
                        vodchannel.send('submitMute', { mute: true });
                        $('.volume_a').addClass('col_close_stop');
                        preValue = $('.volume_slide .ui-slider-handle').css('bottom');
                        $('.volume_slide .ui-slider-handle').css({ bottom: '0' });
                        $('.volume_slide .ui-slider-range-min').hide();
                    }
                }
                $('.volume_a').on('click', voiceOper);

                vodchannel.bind('onMute', function (evt) {
                    if (evt.data) {
                        if (evt.data.mute == 'true' || evt.data.mute == true) {
                            $('.volume_a').addClass('col_close_stop');
                            preValue = $('.volume_slide .ui-slider-range-min').css(
                                    'height'
                            );
                            $('.volume_slide .ui-slider-handle').css({ bottom: '0px' });
                            $('.volume_slide .ui-slider-range-min').hide();
                        } else {
                            $('.volume_a').removeClass('col_close_stop');
                        }
                    }
                });

                $('body').on('click', '.survey_select div a', function () {
                    var that = this;
                    var is_radio = $(that)
                            .parents('.survey_select')
                            .hasClass('single');
                    if ($(that).hasClass('on') && !is_radio) {
                        $(that).removeClass('on');
                    } else {
                        if (is_radio) {
                            $(that).parent('div').find('a').removeClass('on');
                        }
                        $(that).addClass('on');
                    }
                });
            });

            var TTimer = setTimeout(function () {
                GS.loadTag(
                        'video-vod',
                        document.getElementsByTagName('video-vod')[0] ||
                        document.getElementsByTagName('gs:video-vod')[0]
                );
                clearTimeout(TTimer);
            }, 300);
        }
        var livedom = [
            '<div class="livevideo_area" >',
            '<gs:video-live id="videoComponent" site="' +
            vsite +
            '" ctx="webcast" fullscreen="true" ownerid="' +
            ownerid +
            '" uid="' +
            uid +
            '" uname="' +
            uname +
            '" authcode="' +
            authcode +
            '" bar="false" ' +
            gsver +
            ' py="1" />',
            '</div>',
            '<div class="vtool-bar " style="display:block;" >',
            '	<div class="tool_area opacity70">',
            '	    <div class="play_time">',
            '	    	<span class="play_statustips">直播中...</span>',
            '	    </div>',
            '       <div class="btn_fullscreen" id="toFullscreen"></div>',
            '       <a href="javascript:void(0);" id="wifi_a" class="wifi_a">切换网络</a>',
            '		<div class="volume_area">',
            '			<a href="#" class="volume_a"></a>',
            '			<div class="volume_box"><div class="volume_slide"></div></div>',
            '		</div>',
            '	</div>',
            '</div>',

            '<div class="net_list"></div>'
        ].join('');
        var vlivedom = [
            '<div class="video_area" >',
            '<gs:video-vod id="videoComponent" site="' +
            vsite +
            '" ctx="webcast" fullscreen="true"  ownerid="' +
            ownerid +
            '" uid="' +
            uid +
            '" uname="' +
            uname +
            '" authcode="' +
            authcode +
            '" bar="false" ' +
            gsver +
            ' py="1" />',

            '</div>',
            '<div class="vtool-bar " style="display:block;" >',
            '	<div class="tool_area opacity70">',
            '	    <div class="play_time">',
            '	    	<span class="play_statustips">直播中...</span>',
            '	    </div>',
            '       <div class="btn_fullscreen" id="toFullscreen"></div>',
            // '       <a href="javascript:void(0);" id="wifi_a" class="wifi_a">切换网络</a>',
            '		<div class="volume_area">',
            '			<a href="#" class="volume_a"></a>',
            '			<div class="volume_box"><div class="volume_slide"></div></div>',
            '		</div>',
            '	</div>',
            '</div>',
            '<div class="net_list"></div>'
        ].join('');
        var voddom = [
            '<div class="video_area">',
            '<gs:video-vod id="videoComponent" site="' +
            vsite +
            '" ctx="webcast" fullscreen="true"  ownerid="' +
            ownerid +
            '" uid="' +
            uid +
            '" uname="' +
            uname +
            '" authcode="' +
            authcode +
            '" bar="true" ' +
            gsver +
            ' py="1" />',
            '</div>',
            '<div class="vtool-ctrls" id="vToolCtrls" style="' +
            showPlayrate +
            '">',
            '<div class="prebackgo" id="prebackgo">',
            '  <div class="backgobtn" title="后退15秒"></div>',
            '  <div class="pregobtn" title="前进15秒"></div>',
            '</div>',
            '<div class="vtool-pack">',
            // '<div class="vtool-ctrls" id="vToolCtrls" style="display:block;"> <div class="vtool-pack">',

            '     <div class="play_rate">',
            '         <span class="ccH5sp" style="">常速</span>',
            '         <ul class="ccH5spul" style=""><li class="">2倍速</li><li class="">1.5倍速</li><li class="">1.2倍速</li><li class="selected">常速</li><li class="">0.8倍速</li></ul>',
            '     </div>',
            '</div></div>',
            '<div class="vtool-bar" style="' + hidethis + '">',
            // '<div class="vtool-bar" style="display:block;">',
            '	<div class="tool_area opacity70">',
            '		<div class="progress_area"> </div>',
            '		<a class="play_area"></a>',
            '		<div class="play_time">',
            '			<span id="play_now">00:00</span>',
            '			<span>/</span>',
            '			<em id="duration">00:00</em>',
            '		</div>',
            '       <div class="btn_fullscreen" id="toFullscreen"></div>',
            '		<div class="volume_area">',
            '			<a href="#" class="volume_a"></a>',
            '			<div class="volume_box"><div class="volume_slide"></div></div>',
            '		</div>',
            '	</div>',
            '</div>'
        ].join('');

        function playVideo() {
            if (midtype == 'vod') {
                $('#mainshow').html(voddom);
                playVOD();
            } else if (midtype == 'live') {
                $('#mainshow').html(livedom);
                playLive();
            } else if (midtype == 'vlive') {
                if (vcurrenttimer >= vstarttimer && vcurrenttimer <= vendtimer) {
                    hidethis = 'display:block;';
                    $('#mainshow').html(vlivedom);
                    var TTimer = setTimeout(function () {
                        GS.loadTag(
                                'video-vod',
                                document.getElementsByTagName('video-vod')[0] ||
                                document.getElementsByTagName('gs:video-vod')[0]
                        );
                        // GS.loadTag('gs:video-vod', document.getElementsByTagName("gs:video-vod")[0]);

                        clearTimeout(TTimer);
                    }, 300);
                    playVOD();
                } else if (midtype == 'vlive') {
                    layer.msg(
                            '非直播时段，请关注其他视频内容',
                            { time: 0 },
                            function () {}
                    );
                    return;
                }
            } else {
                layer.msg('播放类型有误');
            }

            //视频观看满足条件赠送积分
            // var sourceval =  getLocalStorageVal(
            //         sourcecookiename,
            //         sourcecookietimer,
            //         4
            // );
            //if (source == 'point' && midtype == 'live') {
                var isdone = getLocalStorageVal(
                        pointtaskisdone,
                        pointtaskisdonetimer,
                        24
                );
                if (!isdone) {
                    HandlePointTask();
                }
            //}
        }

        function HandlePointTask() {
            try {
                var cookievalue = getLocalStorageVal(
                        pointtaskcookiename,
                        pointtasktimer,
                        4
                );
                if (!!cookievalue) {
                    var val = parseInt(cookievalue);
                    if (val > 2) {
                        var isdone = getLocalStorageVal(
                                pointtaskisdone,
                                pointtaskisdonetimer,
                                4
                        );
                        if (!!isdone) {
                            return;
                        }

                        //增加积分
                        try {
                            $.ajax({
                                url: window.gConfig.liveVideoApiHost + 'adduserclassrecord',
                                //url: "http://127.0.0.1:8088/page/addpointrecord",
                                type: 'GET',
                                dataType: 'jsonp',
                                data: { uid: uid, classid: id, pid: pid },
                                success: function (data) {
                                    setLocalStorageVal(
                                            pointtaskisdone,
                                            pointtaskisdonetimer,
                                            1
                                    );
                                }
                            });
                        } catch (exp) {}

                        clearTimeout(timePointTask); //清除定时器
                        return;
                    } else {
                        cookievalue = val + 1;
                        setLocalStorageVal(
                                pointtaskcookiename,
                                pointtasktimer,
                                cookievalue
                        );
                    }
                } else {
                    setLocalStorageVal(pointtaskcookiename, pointtasktimer, 1);
                }
            } catch (e) {}

            timePointTask = setTimeout(function () {
                HandlePointTask();
            }, 1000 * 60 * 1);
        }

        if (checkflag == '1') {
            $('.video-detail-inner').hide();
            playVideo();
        } else {
            if (ischeckpwd == '1') {
                $('.video-detail-inner').show();
                $('#classCode').show();
            } else {
                $('.video-detail-inner').hide();
                playVideo();
            }
        }
    }
</script>
</body>
</html>
