$(function () {
    'use strict';
    var Urls = www;
    var isLogin = $("#hid_isLogin").val();
    var lotterFlag = true;
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val(),
    }
    var configData = {
        "actCode": $("#hid_actcode").val(),
        "syCount" :"renewds20240306_count",
        "baseCount":3000,
        "cmpCode": ""
    }

    var thisPage = {
        init: function () {
            thisPage.bindEvents();

            if (isLogin === '1') {
                thisPage.getSyCount();
                thisPage.getLotteryCount();
                thisPage.getRecordList();
            } else {
                $(".bg").show();
                $(".tc").show();
            }
        },
        //绑定事件
        bindEvents: function () {
            //抽奖
            $("#doLottery").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }

                thisPage.doLottery();
            });

            //点击支付
            $(document).on('click', "[name=payurl]", function () {
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                var url = thisPage.getPayUrl();

                $(".bg").hide();
                $(".tc3").hide();
                //客户端
                if (!!thisPage.GetExternal()) {
                    thisPage.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }
            });

            $(".btn3").click(function(){
                $(".h").show();
            });
            $(".close,.tc1-btn").click(function(){
                $(".h").hide();
                $(".bg").hide();
                $(".tc1").hide();
                location.reload();
            });
        },
        //9宫格跳动效果
        doLotteryloop:function (){
            var i = 0; // 初始化索引为0
            var arr=[1,2,3,4,5,6,7,8,9];
            function loop() {
                if (i < arr.length) {
                    $(".zp ul li[data-num=" + i + "]").addClass("on").siblings().removeClass("on");

                    setTimeout(() => {
                        i++; // 更新索引值

                        loop(); // 递归调用自身进行下一次循环
                    }, 100); // 设置每次延迟时间为100毫秒
                } else {
                    console.log("循环结束"); // 所有元素都已经处理完成后打印提示信息
                }
            }
            loop(); // 开始第一次循环
        },
        //抽奖
        doLottery:function () {
            if(lotterFlag) {
                lotterFlag = false;
                if($(".f1").html()=='0'){
                    layer.msg("抽奖次数已用完")
                    return false;
                }
                thisPage.doLotteryloop();

                setTimeout(function () {
                    request(
                        `${Urls}renewds2024/dolottery?actcode=${configData.actCode}&uid=${loginUser.uid}&pid=${loginUser.pid}&platform=pc`).then(res => {

                        var currentPdata = res.data;
                        var prizeId = currentPdata.id;
                        var prizeName = currentPdata.name;

                        $(".zp ul li[data-num=" + prizeId + "]").addClass("on").siblings().removeClass("on");
                        $(".tc1 .txt").html(prizeName);
                        $(".bg").show();
                        $(".tc1").show();

                        lotterFlag = true;
                    });
                }, 1000);
            }
        },
        //获取抽奖次数
        getLotteryCount:function (){
            request(
                `${Urls}renewds2024/getlotterycount?actcode=${configData.actCode}`).then(res => {
                if (res.code === '200') {
                    var data = res.data;
                    if(data){
                        var pCount = data.pCount;
                        var pkgList = data.packageList;

                        $(".f1").html(pCount);
                        if (pkgList == null || pkgList.length <= 0) {
                            $(".f1").html(0);
                            $("#renew").show();
                            $("#doLottery").hide();
                        }
                    }else{
                        $(".f1").html(0);
                        $("#renew").show();
                        $("#doLottery").hide();
                    }

                }
            });
        },
        //剩余席位
        getSyCount:function () {
            request(
                `${Urls}user/getcountbyactcode?actcode=${configData.syCount}`).then(res => {
                if (res.code === '200') {
                    var num = 0
                    if (res.data) {
                        num = res.data.split(',')[0]
                    }
                    var usedCount = parseInt(!!num ? num : 0)

                    var syCount = configData.baseCount - usedCount

                    if(syCount<=0){
                        syCount = 0;
                    }
                    $(".a1").html(syCount);
                }
            })
        },
        //获取中奖记录
        getRecordList:function () {
            request(
                `${Urls}renewds2024/getmylotteryinfo?actcode=${configData.actCode}&uid=${loginUser.uid}`).
            then(res => {
                if (res.code === '200') {
                    if (res.data.length>0) {
                        var recordList = res.data;
                        var htmlDom = ''
                        for (var i = 0; i < recordList.length; i++) {
                            var item = recordList[i]
                            htmlDom += '恭喜你获得<span class="yellow">' + item.benefitName + '</span><br/>'
                        }
                        $('#recordWrap').html(htmlDom)
                    }
                }
            })
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888020000,888080000&groupCode=6&businessType=biztyp-dsxf";
            if (location.host.indexOf("preact.emoney.cn")>-1) {
                url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888020000,888080000&groupCode=3&resourcestypeid=234&resourcesid=1244397&businessType=biztyp-dsxf";
            }

            return url;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            var pidlist = "888020000,888080000,888020400";
            if (pidlist.indexOf(pid) < 0) {
                layer.msg("本活动仅限大师用户参与");
                return false;
            }
            return true;
        },
        //推送cmp
        pushdatatocmp: function (uname, adcode) {
            var data = {
                "appid": '10088',
                "logtype": 'click',
                "mid": '',
                "pid": thisPage.getQueryString("pid"),
                "sid": thisPage.getQueryString("sid"),
                "tid": thisPage.getQueryString("tid"),
                "uid": thisPage.getQueryString("uid"),
                "uname": uname,
                "adcode": adcode,
                "targeturl": "",
                "pageurl": window.top.location.href
            }
            var saasUrl = "https://ds.emoney.cn/saas/queuepush";
            var saasSrc = saasUrl + "?v=" + Math.random()
                + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
                + "&message=" + encodeURIComponent(JSON.stringify(data));

            var elm = document.createElement("img");
            elm.src = saasSrc;
            elm.style.display = "none";
            document.body.appendChild(elm);
        },
        //时间戳toDate
        timestampToDate: function (timestamp) {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = date.getMonth() + 1; // getMonth() 返回的月份从 0 开始，所以需要加 1
            const day = date.getDate();

            return `${year}年${month}月${day}日`;
        },
        getQueryString: function (name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        },
        GetExternal: function () {
            return window.external.EmObj;
        },

        //调用客户端接口
        PC_JH: function (type, c) {
            try {
                var obj = thisPage.GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {
            }
        },
        setCookie: function (name, value) {
            var expdate = new Date();
            expdate.setTime(expdate.getTime() + 1000 * 60 * 60 * 24 * 30);
            document.cookie = name + "=" + value + ";expires=" + expdate.toGMTString() + ";path=/";
        },


        getCookie: function (c_name) {
            if (document.cookie.length > 0) {
                var c_start = document.cookie.indexOf(c_name + "=")
                if (c_start != -1) {
                    c_start = c_start + c_name.length + 1
                    var c_end = document.cookie.indexOf(";", c_start)
                    if (c_end == -1) c_end = document.cookie.length
                    return unescape(document.cookie.substring(c_start, c_end))
                }
            }
            return ""
        }
    }
    thisPage.init();
});

function request (url, dataType) {
    return new Promise((resolve, reject) => {
        $.ajax({
            type: 'get',
            url: url,
            dataType: dataType ? dataType : 'jsonp',
            success: function (data) {
                console.log(data)
                if (data.code === '200') {
                    resolve(data)
                }
            },
            error: function (err) {
                reject(err)
            }
        })
    })
}