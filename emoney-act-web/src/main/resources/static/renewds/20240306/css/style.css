@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#CB172D;font-family: "微软雅黑"; overflow-x: hidden;}
ul,li{list-style: none;}
.red2{color: #e34536;}
.yellow{color: #ffea00;}
.f1{font-size: 32px;color: #fff;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.png) center; height:255px;}
.img_djs{background:url(../images/index_djs.png) center; height:293px;}
.img_2{background:url(../images/index_02.png) center; height:387px;}

.img_3{background:url(../images/index_03.png) center; height:874px;}
.img_4{background:url(../images/index_04.png) center; height:668px;}
.img_5{background:url(../images/index_05.png) center; height:740px;}


.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:20px 0 200px;
}
A:link,a:visited {color: #fff; TEXT-DECORATION: none;}
A:hover { color: #fff; text-decoration: none;}
img{border:none;}

.main{width:100px; margin:0 auto; position:relative;}

.btn1{
	background: url("../images/btn1.png"); background-size: 100% 100%;

}
.an1{
	position: absolute;
	top: 203px;
	left: 337px;
	width: 208px;
	height: 64px;
}
.s1{
	position: absolute;
	top: 14px;
  left: 171px;
}
.s2{
	position: absolute;
	top: 100px;
	left: 123px;
}
.s3{
	position: absolute;
	top: 30px;
  left: 263px;
}
.an2{
	position: absolute;
top: 72px;
  left: 141px;
	width: 314px;
	height: 92px;
}
.btn2{
	background: url("../images/btn2.png");
	width: 168px;
	height: 128px;
	position: absolute;
	top: 412px;
	left: -234px; color: #bb3f00!important; font-size: 34px; text-align: center; font-weight: bold; padding-top: 40px;
}
.zp{
	position: absolute;
	top: 230px;
	left: -418px;
	width: 560px;
}
.zp li{ float: left; width: 179px; height: 179px; margin:0 -2px -1px 0}
.zp .on{background: url("../images/zp.png");}
.txt1{
	position: absolute;
	top: 229px;
	left: 165px;
	color: #712d00;
	font-size: 25px;
	width: 280px;
}
.txt2{
	position: absolute;
	top: 242px;
	left: 455px;
	font-size: 14px;
	width: 75px;
}
.txt3{
	position: absolute;
	top: 361px;
  left: 148px;
	font-size: 19px;
	width:360px; height: 156px; border-radius: 10px; background-color: #CB172D; padding: 20px 0 20px 20px; color: #fff; line-height: 30px;white-space:nowrap
}
.txt4{
	position: absolute;
	top: 641px;
	left: 148px;
	font-size: 19px;
	width: 360px;
	height: 96px;
	border-radius: 10px;
	background-color: #CB172D;
	padding: 20px 0 20px 20px;
	color: #fff;
	line-height: 30px;
	white-space: nowrap;
	overflow-y: auto;
}
.txt5{
	position: absolute;
	top: 147px;
	left: -125px;
	width: 656px;
	height: 424px;
	border-radius: 10px;
}
.btn3{
	position: absolute;
	top: 325px;
	left: -26px;
	text-align: center;
	font-size: 22px;
	cursor: pointer; text-decoration: underline;
	width: 119px; color: #fff;
}
.djs{
	position: absolute;
	top: 213px;
	left: 145px;
	color: #fff;
	font-size: 22px;
	text-align: center;
	width: 178px;
}
.djs .a1{color: #ffe35e; font-size:30px;}
.djs2{
	width: 309px;
	height: 117px;
	position: absolute;
	left: 185px;
	top: 41px;
	line-height: 34px;
	color: #e13537;
	font-size: 28px;
	text-align: center;
	font-weight: bold;
}
.t{
	position: absolute;
	left: 11px;
	width: 51px;
	top: 36px;
}
.s{
	position: absolute;
	left: 81px;
	width: 51px;
	top: 36px;
}
.f{
	position: absolute;
	left: 154px;
	width: 51px;
	top: 36px;
}
.m{
	position: absolute;
	left: 226px;
	width: 51px;
	top: 36px;
}
.pf{
	background: url("../images/pf.png") center;
	width:100%;
	height: 194px;
	position: fixed;
	bottom: 0px;
	left: 0px;
}

.h{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%; display: none;}
.hdgz{width: 500px;  padding: 30px 50px; position: absolute; left: 50%; top: 50%; margin:-294px 0 0 -300px;font-size: 18px;color: #4b4b4b; background-color: #fff; border-radius: 10px; line-height: 30px;}
.hdgz .t1{ text-align: center; font-size: 52px; color: #000000; line-height: 60px; padding-bottom: 20px;}
.tc1{background-image:url("../images/tc.png"); width: 540px; height: 710px;position: absolute; left: 50%; top: 50%; margin:-400px 0 0 -270px;}
.tc1 .txt{text-align: center; width: 100%; top: 430px; font-size: 52px; color: #000; position: absolute;}
.tc1-btn{background-image:url("../images/tc-btn.png"); width: 338px; height: 81px;position: absolute; left: 101px; top: 563px;}

.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.dh2{
  -webkit-animation: dh2 0.5s linear infinite alternate;
  animation-name: dh2 0.5s linear infinite alternate;
}
@-webkit-keyframes dh2{
  to {margin: 15px 0 0 10px;
  }
}

@keyframes dh2{
  to {margin: 15px 0 0 10px;
  }
}

.close{
	background: url("../images/close.png") center top no-repeat;background-size: 100% 100%;
	position: absolute;
	right: -50px;
	top: -50px;
	width:45px;
	height:45px;
}
