@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#E14335;font-family: "微软雅黑"; overflow-x: hidden;}
ul,li{list-style: none;}
.red2{color: #e34536;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.jpg) center; height:312px; position: relative;}
.img_2{background:url(../images/index_02.jpg) center; height:282px;}
.img_3{background:url(../images/index_03.jpg) center; height:319px;}
.img_4{background:url(../images/index_04.png) center; height:318px;}
.img_5{background:url(../images/index_05.png) center; height:558px;}
.img_6{background:url(../images/index_06.png) center; height:558px; margin-bottom: 20px;}

.img_1 li{background:url("../images/dm.png"); width: 429px; height: 116px; position: absolute; left: 100%; font-size: 15px; color: #000; font-weight: bold;box-sizing: border-box; padding: 47px 0 0 56px;-webkit-animation: dm 65s linear infinite;
  animation-name: dm 65s linear infinite;
}
@-webkit-keyframes dm{
  to {left: -20%
  }
}

@keyframes dm{
  to {left: -400%
  }
}
.img_1 li:nth-child(1) {
  top: 20px;
   margin-left: 0px;
}

.img_1 li:nth-child(2) {
  top: 300px;
  margin-left: 300px;
}

.img_1 li:nth-child(3) {
  top: 150px;
  margin-left: 600px;
}

.img_1 li:nth-child(4) {
  top: 70px;
  margin-left: 900px;
}

.img_1 li:nth-child(5) {
  top: 220px;
  margin-left: 1200px;
}

.img_1 li:nth-child(6) {
  top: 20px;
  margin-left: 1500px;
}

.img_1 li:nth-child(7) {
  top: 300px;
  margin-left: 1800px;
}

.img_1 li:nth-child(8) {
  top: 150px;
  margin-left:2100px;
}

.img_1 li:nth-child(9) {
  top: 70px;
  margin-left: 2400px;
}

.img_1 li:nth-child(10) {
  top: 220px;
  margin-left: 2700px;
}
.img_1 li:nth-child(11) {
  top: 20px;
   margin-left: 3000px;
}

.img_1 li:nth-child(12) {
  top: 300px;
  margin-left: 3300px;
}

.img_1 li:nth-child(13) {
  top: 150px;
  margin-left: 3600px;
}

.img_1 li:nth-child(14) {
  top: 70px;
  margin-left: 3900px;
}

.img_1 li:nth-child(15) {
  top: 220px;
  margin-left: 4200px;
}

.img_1 li:nth-child(16) {
  top: 20px;
  margin-left: 4500px;
}

.img_1 li:nth-child(17) {
  top: 300px;
  margin-left: 4800px;
}

.img_1 li:nth-child(18) {
  top: 150px;
  margin-left:5100px;
}

.img_1 li:nth-child(19) {
  top: 70px;
  margin-left: 5400px;
}

.img_1 li:nth-child(20) {
  top: 220px;
  margin-left: 5700px;
}
.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff;
	position: absolute;
	left: -262px;
	top: 305px;
	width: 628px;
}
A:link,a:visited { text-decoration: none;}
A:hover { color: #fff; TEXT-DECORATION: none}
img{border:none;}

.main{width:100px; margin:0 auto; position:relative;}

.btn1,.btn1h{
	background: url("../images/btn1.png");
	width: 165px;
	height: 84px; text-align: center; line-height: 84px; font-size:30px; color: #ffff00; font-weight: bold; text-decoration: none;
}
.btn1h{
	background: url("../images/btn1h.png"); color: #fff;}
.an1{
	position: absolute;
	top: 106px;
	left: 355px;
}

.an2{
	position: absolute;
	top: 84px;
	left: 583px;
}

.btn3{
	width: 100px;
	position: absolute;
	top: 257px;
	left: 452px;
	color: #fff;
	font-size: 20px;text-decoration: underline!important;
}

.s1{
	position: absolute;
	top: 54px;
	right: -53px;
	width: 70px;
	height: 60px;
	pointer-events: none;
}
.s2{
	position: absolute;
	top: 22px;
right: -30px;
	width: 70px;
	height: 60px;
	pointer-events: none; 
}
.img_6 .t1{text-align: center;font-size: 55px; color: #3f3f3f; line-height: 60px; padding-top: 90px;}
.btn4{
	background: url("../images/btn4.png");
	width: 435px;
	height: 84px;
	text-align: center;
	line-height: 78px;
	font-size: 32px;
	color: #ffff00;
	position: absolute;
	top: 200px;
	left: -168px;TEXT-DECORATION: none
}
.btn5,.btn5h{
	background: url("../images/btn5.png");
	width: 365px;
	height: 101px;
	text-align: center;
	line-height: 85px;
	font-size: 35px;
	color: #feeac1;
	position: absolute;
	left: -82px;
	top: 132px;
}
.btn5h{
	background: url("../images/btn5h.png");color: #fff;
}

.h{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%; display: none;}
.hdgz{background-image:url(../images/hdgz.png); width: 571px; height:588px; position: absolute; left: 50%; top: 50%; margin:-294px 0 0 -285px;font-size: 18px;color: #808080;}
.hdgz .t1{position: absolute;left: 50px;
top: 138px;
width: 492px;
line-height: 20.5px;
font-size: 17px;}
.hdgz .t2{position: absolute;  left: 171px;
  top: 456px;
  line-height: 36px;
}
.hdgz .t3{position: absolute;left: 136px;
top: 464px;
width: 452px;}
.hdgz .t4{position: absolute; left: 136px;
top: 505px;
width: 440px;}
.btn7{width: 131px; height: 57px; position: absolute;left: 407px;
top: 469px;background-color: #f44e45; line-height:57px; text-align: center; font-size:22px; color: #fff; border-radius: 28px;TEXT-DECORATION: none}
.pf{background:url("../images/pf.png") center; position:fixed; left:0px; bottom:0px; width:100%; height:233px;}

.wddd{position: absolute; right: 0px; top: 20%; background-color: #F44D39; width: 40px; padding: 10px 0; color: #fff; font-size: 22px; text-align: center; border-top-left-radius: 10px;border-bottom-left-radius: 10px; line-height: 25px;}
.wddd2{background-color: #fff;width:680px;
border-radius: 10px;
position: absolute;
left: 50%;
top: 50%;
margin: -100px 0 0 -340px;
color: #000;
padding: 25px;
font-size: 16px;}
.wddd2 .bt{ background-color: #FFD9AD; color: #9b4f1c; font-size: 22px;}
table,th,td {
  border : 1px solid #999;
  border-collapse: collapse; line-height: 50px; text-align: center;
}



.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt1{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}

.dh:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.dh2{
  -webkit-animation: dh2 0.5s linear infinite alternate;
  animation-name: dh2 0.5s linear infinite alternate;
}
@-webkit-keyframes dh2{
  to {margin: 15px -15px 0 0px;
  }
}

@keyframes dh2{
  to {margin: 15px -15px 0 0px;
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}


.djs{
	position: absolute;
	left: 66px;
	top: 16px;
	width: 360px;
	height: 126px;
	line-height: 60px;
	color: #fee7b9;
	font-size: 50px;
	text-align: center;
	font-weight: bold;
	letter-spacing: 8px;
}
.djs .t{
	position: absolute;
	left: 12px;
	top: 52px;
	width: 67px;
}
.djs .s{
	position: absolute;
	left: 122px;
	top: 52px;
	width: 48px;
}
.djs .f{
	position: absolute;
	left: 252px;
	top: 52px;
	width: 48px;
}
.djs .m{
	position: absolute;
	left: 259px;
	top: 52px;
	width: 61px;
}


.txt1{
	position: absolute;
	left: 132px;
top: 24px;
width: 200px;
color: #F8ECC8;
font-size: 26px;
text-align: right;
letter-spacing: 19px;
}
.txt2{
	position: absolute;
	left: -102px;
	top: 256px;
	width: 604px; color: #fff;
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -20px;
	top: -20px;
	width: 30px;
	height: 30px;
}
