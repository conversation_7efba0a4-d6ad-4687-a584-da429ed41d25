@charset "utf-8";
/* CSS Document */
*{margin:0; padding:0;}
body{background-color:#AA1A1A;font-family: "微软雅黑";}
.img_1{background: url("../images/index_01.jpg") center; height: 262px;}
.img_2{background: url("../images/index_02.jpg") center; height: 261px;}
.img_3{background: url("../images/index_03.jpg") center; height: 209px;}
.img_4{background: url("../images/index_04.jpg") center; height: 209px;}
.img_5{background: url("../images/index_05.png") center; height: 471px;}
.img_6{background: url("../images/index_06.png") center; height: 1154px;}
.img_7{background: url("../images/index_07.png") center; height: 860px;}

.main{width:100px; margin:0 auto; position:relative;}
.txt{
	width: 220px;
	position: absolute;
	top: 35px;
	left: 197px;
	color: #BF0A00;
	font-size: 70px;
	line-height: 70px; font-weight: bold;
}
.btn1{
	background: url("../images/btn1.png");
	width: 220px;
	height: 172px;
	position: absolute;
	top: 4px;
	left: 247px;
}
.btn1h{
	background: url("../images/btn1h.png");
	width: 220px;
	height: 172px;
	position: absolute;
	top: 4px;
	left: 247px;
}
.hand{
	background: url("../images/hand.png");
	width: 77px;
	height: 80px;
	position: absolute;
	top: 142px;
	left: 439px;
	pointer-events: none;
-webkit-animation: hand 0.3s linear infinite alternate;
  animation-name: hand 0.3s linear infinite alternate;
}
@-webkit-keyframes hand{
  to { margin: 30px 0 0 30px;-webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes hand{
  to {
	  margin: 30px 0 0 30px;
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.btn2{
	background: url("../images/btn2.png");
	width: 348px;
	height: 115px;
	position: absolute;
	top: 21px;
	left: -122px;
}
.btn3{
	background: url("../images/btn3.png");
	width: 373px;
	height: 106px;
	position: absolute;
	top: 4px;
	left: 112px;
}
.close{
	background: url("../images/close.png") center top no-repeat;
	width: 40px;
	height: 41px;
	position: absolute;
	top: -40px;
	right: -40px;
	cursor:pointer;
}
.pfb{background: url("../images/pf.png"); height:150px; width: 100%; position: fixed; left: 0; bottom: 0;}


.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/h.png); display: none;}
.hdgz{
	position: absolute;
	left: 50%;
	top: 50%; margin: -338px 0 0 -457px;
	width:811px; height: 527px;
	color: #4F371A;
	font-size:18px;
	line-height:30px;background:url("../images/tc.png");
}
.hdgz ul{padding:70px 50px 0 73px}
.hdgz li{
	list-style-type:disc;}
.tc1{background:url("../images/tc1.png"); width: 534px; height: 455px; position: fixed;top:50%; left:50%;margin: -250px 0 0 -228px;}
.tc2{background:url("../images/tc2.png"); width: 534px; height: 455px; position: fixed;top:50%; left:50%;margin: -250px 0 0 -228px;}
.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin: 20px 0 180px;
}
.bg{background-image:url(../images/h.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .inputtxt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}