@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#E14335;font-family: "微软雅黑"; overflow-x: hidden;}
ul,li{list-style: none;}
.red2{color: #e34536;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.jpg) center; height:456px; position: relative;}
.img_2{background:url(../images/index_02.jpg) center; height:315px;}
.img_3{background:url(../images/index_03.jpg) center; height:286px;}
.img_4{background:url(../images/index_04.jpg) center; height:285px;}
.img_5{background:url(../images/index_05.jpg) center; height:257px;}
.img_6{background:url(../images/index_06.jpg) center; height:256px;}
.img_7{background:url(../images/index_07.jpg) center; height:829px;margin-bottom: 70px;}

.img_1 li{background:url("../images/dm.png"); width: 601px; height: 72px; position: absolute; left: 100%; font-size: 18px; color: #ffd928; font-weight: bold;box-sizing: border-box; padding: 40px 0 0 72px;-webkit-animation: dm 65s linear infinite;
  animation-name: dm 65s linear infinite;
}
@-webkit-keyframes dm{
  to {left: -20%
  }
}

@keyframes dm{
  to {left: -400%
  }
}
.img_1 li:nth-child(1) {
  top: 20px;
   margin-left: 0px;
}

.img_1 li:nth-child(2) {
  top: 100px;
  margin-left: 300px;
}

.img_1 li:nth-child(3) {
  top: 60px;
  margin-left: 900px;
}

.img_1 li:nth-child(4) {
  top: 20px;
  margin-left: 1500px;
}

.img_1 li:nth-child(5) {
  top: 100px;
  margin-left: 1800px;
}

.img_1 li:nth-child(6) {
  top: 60px;
  margin-left:2400px;
}

.img_1 li:nth-child(7) {
  top: 20px;
  margin-left: 2700px;
}

.img_1 li:nth-child(8) {
  top:100px;
  margin-left: 3000px;
}
.img_1 li:nth-child(9) {
  top: 20px;
   margin-left: 3600px;
}

.img_1 li:nth-child(10) {
  top:60px;
  margin-left: 3900px;
}

.img_1 li:nth-child(11) {
  top:100px;
  margin-left: 4200px;
}

.img_1 li:nth-child(12) {
  top: 20px;
  margin-left: 4800px;
}

.img_1 li:nth-child(13) {
  top:60px;
  margin-left: 5100px;
}

.img_1 li:nth-child(14) {
  top:100px;
  margin-left: 5400px;
}

.img_1 li:nth-child(15) {
  top:20px;
  margin-left: 6000px;
}

.img_1 li:nth-child(16) {
  top: 60px;
  margin-left:6300px;
}

.img_1 li:nth-child(17) {
  top: 100px;
  margin-left: 6600px;
}

.img_1 li:nth-child(18) {
  top:20px;
  margin-left:7200px;
}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff;
	margin: 0px 0 20px;
	position: absolute;
	left: -402px;
	top: 631px;
	width: 900px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover { color: #fff; TEXT-DECORATION: none}
img{border:none;}

.main{width:100px; margin:0 auto; position:relative;}


.btn1,.btn1h{
	background: url("../images/btn1.png");
	width: 366px;
	height: 122px;
	position: absolute;
	top: 95px;
	left: -112px;
}
.btn1h{
	background: url("../images/btn1h.png");
}
.btn2,.btn2h{
	background: url("../images/btn2.png");
	width: 189px;
	height: 128px;
	position: absolute;
	top: 20px;
  left: 327px;
}
.btn2h{
	background: url("../images/btn2h.png");}
.btn3{
	background: url("../images/btn3.png");
	width: 90px;
	height: 31px;
	position: absolute;
	top: 187px;
	left: -429px;
}

.s1{
	position: absolute;
	top: 32px;
right: -26px;
	pointer-events: none;
}
.s2{
	position: absolute;
	top: 22px;
right: -30px;
	width: 70px;
	height: 60px;
	pointer-events: none; 
}


.h{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%; display: none;}
.hdgz{background-image:url(../images/hdgz.png); width: 751px; height:600px; position: absolute; left: 50%; top: 50%; margin:-300px 0 0 -352px;font-size: 18px;color: #000;}
.hdgz .t1{position: absolute; left: 42px;top: 96px;
  width: 672px;
  line-height: 27.5px;height: 455px;}
.hdgz .t2{padding-right: 15px;}
.btn7{width: 92px; height: 40px; position: absolute;left: 480px;
top: 533px;background-color: #f44e45; line-height:40px; text-align: center; font-size:22px; color: #fff; border-radius: 28px;}


.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}

.dh:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.dh2{
  -webkit-animation: dh2 0.5s linear infinite alternate;
  animation-name: dh2 0.5s linear infinite alternate;
}
@-webkit-keyframes dh2{
  to {margin: 15px -15px 0 0px;
  }
}

@keyframes dh2{
  to {margin: 15px -15px 0 0px;
  }
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}




.txt1{
	position: absolute;
	left: 132px;
top: 24px;
width: 200px;
color: #F8ECC8;
font-size: 26px;
text-align: right;
letter-spacing: 19px;
}
.txt2{
	position: absolute;
	left: -102px;
	top: 256px;
	width: 604px; color: #fff;
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -50px;
	top: -50px;
	width: 59px;
	height: 59px;
}
.pf{
	background: url("../images/pf.png") center no-repeat;
	width: 100%;
	height:159px;
	position:fixed;
	right: 0px;
	bottom: 0%;
}

/*滚动条*/
.zUIpanelScrollBox,.zUIpanelScrollBar{
			width:10px;
			top:4px;
			right:2px;
			border-radius:5px;
			
		}
		.zUIpanelScrollBox{
			background:black;opacity:0.1;
			filter:alpha(opacity=10);
		}
		.zUIpanelScrollBar{
			background:#333;opacity:0.8;
			filter:alpha(opacity=80);
		}

/*切屏*/
.al2{
	position: absolute;
	left: -406px;
	top: 19px;
	width: 920px;
	height: 580px;
}
.slider_box2{ margin: 0px auto; width: 920px; height:580px; position: relative;overflow:hidden;}
.silder_con2{ height: 531px; position: absolute;}
.silder_panel2{width: 920px; height: 531px; float: left; position: relative; text-align:center;}

.silder_nav,.silder_nav2,.silder_nav3 { margin: 0px auto; width:480px; height: 17px; bottom: 0px; position: absolute;}
.silder_nav li,.silder_nav2 li,.silder_nav3 li { margin-right: 5px; padding: 0px; background-color: #fff; float: left;width:38px; height: 4px; overflow: hidden; display: block;}
.silder_nav li.current,.silder_nav2 li.current,.silder_nav3 li.current { background-color: #fff; height: 7px; margin-top: -2px;}
