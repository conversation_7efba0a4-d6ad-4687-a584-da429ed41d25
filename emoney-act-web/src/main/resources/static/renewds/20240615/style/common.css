@charset "utf-8";

img{ border:0px;}
body {
	font-size:12px; overflow-x:hidden;
	margin:0;
	padding:0;
	line-height:22px;
	color:#333;
	background-color:#E7E5E3;font-family:"微软雅黑";
}

.main{width:100px; margin:0 auto; position:relative;}
ul,li {margin:0;padding:0;list-style:none;}
A:link {COLOR: #333; TEXT-DECORATION: none;}
A:visited {COLOR: #333; TEXT-DECORATION: none}
A:hover {COLOR:#000; TEXT-DECORATION: none}
A.b:link {TEXT-DECORATION: underline;}
A.b:visited {TEXT-DECORATION: underline;}
A.b:hover {TEXT-DECORATION: none}
A.c:link {COLOR: #c30d23; TEXT-DECORATION: underline;}
A.c:visited {COLOR: #c30d23; TEXT-DECORATION: underline}
A.c:hover {COLOR:#333; TEXT-DECORATION: none}
A.d:link {COLOR: #333; TEXT-DECORATION: underline;}
A.d:visited {COLOR: #333; TEXT-DECORATION: underline}
A.d:hover {COLOR:#333; TEXT-DECORATION: none}
input{background:transparent;border:0px;margin:0px; color:#939598;list-style:none; font-family:"微软雅黑"; font-size:14px;}
.white {color:#ffffff;}
.red {color:#FF2A00; font-size: 20px;}
.red2{color:#FF2A00;}
.green {color:green;}
.black{ color:#000;}
.blue{color:#549FD6;}
.yellow{color:#ffea94; font-weight: bold;}
.blue3{color:#255E98;}
.gray{color:#EBC384;}
.z1{letter-spacing:1px;}
.t-c{ text-align: center;}

.bg_h{background-color:#840000; width: 996px; margin: 0 auto; overflow: hidden;}
.f_12{font-size:12px;}
.f_14{font-size:17px; color: #fff;line-height: 20px;}
.clr2{clear:both; height:1px; overflow:hidden;}
.m_t36{margin-top:36px;}
.m_t20{margin-top:20px;}
.m_t10{margin-top:10px;}
.t_c{text-align:center;}
.f_16{font-size:16px;font-weight:bold;}
.f_17{font-size:17px;}
.f_26{font-size:26px; font-family:"微软雅黑"; line-height:50px;}
.f_30{font-size:28px; color:#603813; line-height:50px; font-weight:bold;}
.f20{font-size:20px; color: #fbcf9e; padding-top: 15px;}
.f21{font-size:20px;}
.f-b{text-decoration: underline}

.h340{height:340px;}
td{font-size:18px;text-align:center; background-color:#fff;}

.f-r{float:right;}
.f-l{float:left;}

.bod .dbg1{background:url(../images/index_01.png) center top no-repeat;height:340px;}
.bod .dbg2{background:url(../images/index_02.png) center top no-repeat;height:339px;}
.bod .dbg3{background:url(../images/index_03.png) center top no-repeat;height:776px;}
.bod .dbg4{background:url(../images/index_04.png) center top no-repeat;height:848px;}
.bod #dbg5{background:url(../images/index_05.png) center top no-repeat;height:753px;}
.bod .dbg6{background:url(../images/index_06.png) center top no-repeat;height:529px;}
.bod .dbg7{background:url(../images/index_07.png) center top no-repeat; height:914px;}
.bod #dbg8{background:url(../images/index_08.png) center top no-repeat;height:1113px;}
.bod #dbg9{background:url("../images/index_09.png") center top no-repeat;height:1030px;}

.pic1{
	background: url(../images/pic.png);
	width: 603px;
	height: 257px;
	position: absolute;
	left: -457px;
	top: 87px;
}
.pic2{
	background: url(../images/pic2.png);
	width: 225px;
	height: 209px;
	position: absolute;
	left: -67px;
	top: 0px;
}

.pf{position: fixed; left: 0px; bottom: 0%; background: url("../images/pf3.png") center; width: 100%; height:150px;}
.pf2{position: fixed; left: 0px; top: 20%;}
.wddd{position: absolute; right: 0px; top: 20%; background-color: #F44D39; width: 40px; padding: 10px 0; color: #fff; font-size: 22px; text-align: center; border-top-left-radius: 10px;border-bottom-left-radius: 10px; line-height: 25px;}
.wddd2{background-color: #fff;width:680px;
	border-radius: 10px;
	position: absolute;
	left: 50%;
	top: 50%;
	margin: -100px 0 0 -340px;
	color: #000;
	padding: 25px;
	font-size: 16px;}
.wddd2 .bt{ background-color: #FFD9AD; color: #9b4f1c; font-size: 22px;}
table,th,td {
	border : 1px solid #999;
	border-collapse: collapse; line-height: 50px;
}
.btn1{
	background: url(../images/btn1.png);
	width: 360px;
	height: 121px;
	position: absolute;
	left: 213px;
	top: 112px;
}
.btn2{
	background: url(../images/btn2.png);
	width: 456px;
	height: 135px;
}
.an1{
	position: absolute;
	left: -168px;
	top: 655px;
}
.an2{
	position: absolute;
	left: -168px;
	top: 877px;
}
.an3{
	position: absolute;
	left: -168px;
	top: 907px;
}
.btn3{
	background: url(../images/btn3.png);
	width: 284px;
	height: 81px;
	position: absolute;
	left: 187px;
	top: 37px;
}
.btn4{
	background: url("../images/btn4.png");
	width: 145px;
	height: 147px;
	position: absolute;
	left: -245px;
	top: 300px;
}
.btn4b{
	background: url("../images/btn4b.png");
	width: 145px;
	height: 147px;
	position: absolute;
	left: -245px;
	top: 300px;
}
.hend{
	background: url("../images/hend.png");
	width: 85px;
	height: 85px;
	position: absolute;
	left: -151px;
	top: 390px;
	pointer-events: none;
}
.txt1{
	position: absolute;
	left: 171px;
	top: 88px;
	text-align: center;
	width: 364px;
	color: #9B552C;
	font-size: 32px;
	line-height: 35px;
	font-weight: bold;
}
.txt2{
	position: absolute;
	left: 179px;
	top: 245px;
	color: #666666;
	font-size: 18px;
	width: 346px;
	height: 200px;
	line-height: 30px;
	white-space: nowrap
}
.txt3{
	position: absolute;
	left: 179px;
	top: 571px;
	color: #DE552F;
	font-size: 24px;
	width: 346px;
	line-height: 35px;
	overflow: auto;
	height: 100px;
}
.hdgzk{
	position: absolute;
	left: 274px;
	top: 712px;
	text-align: center;
	width: 151px;
	font-size: 24px;
	color: #441203!important;
}
.zp{
	position: absolute;
	top: 148px;
	left: -400px;
	width: 560px;
}
.zp li{ float: left; width: 154px; height: 154px; margin:0 -3px -6px 0}
.zp .on{background: url("../images/zp.png");}

.dh{
	-webkit-animation: dh 0.3s linear infinite alternate;
	animation-name: dh 0.3s linear infinite alternate;
}
.dh2:hover{
	-webkit-transform: scale(1.05);
	transform: scale(1.05); z-index: 9;
}
@-webkit-keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}

@keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}

.footer{text-align:center; font-family:"宋体"; font-size:12px; padding: 20px 0 160px 0;}

*{margin:0; padding:0; font-family:"微软雅黑";}

@-webkit-keyframes wobble2 {
	0% {
		-webkit-transform: none;
		transform: none;
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	15% {
		-webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
		transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	30% {
		-webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
		transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	45% {
		-webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
		transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	60% {
		-webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
		transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	75% {
		-webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
		transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	100% {
		-webkit-transform: none;
		transform: none;
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}
}

@keyframes wobble2 {
	0% {
		-webkit-transform: none;
		transform: none;
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	15% {
		-webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
		transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -5deg);
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	30% {
		-webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
		transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 3deg);
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	45% {
		-webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
		transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -3deg);
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	60% {
		-webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
		transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, 2deg);
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	75% {
		-webkit-transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
		transform: translate3d(0, 0, 0) rotate3d(0, 0, 1, -1deg);
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}

	100% {
		-webkit-transform: none;
		transform: none;
		-moz-transform-origin:0% 80%;
		transform-origin:0% 80%;
	}
}

.wobble2 {
	-webkit-animation-name: wobble2;
	animation-name: wobble2;
	-webkit-transform-origin: center bottom;
	-moz-transform-origin: center bottom;
	-ms-transform-origin: center bottom;
	-o-transform-origin: center bottom;
	transform-origin: center bottom;
	-webkit-animation-duration: 3s;
	-o-animation-duration: 3s;
	animation-duration: 3s;
	-webkit-animation-timing-function: ease-in;
	-o-animation-timing-function: ease-in;
	animation-timing-function: ease-in;
	-webkit-animation-delay: 0s;
	-o-animation-delay: 0s;
	animation-delay: 0s;
	-webkit-animation-iteration-count: infinite;
	-o-animation-iteration-count: infinite;
	animation-iteration-count: infinite;
	-webkit-animation-direction: normal;
	-o-animation-direction: normal;

	-webkit-animation-fill-mode: both;
	-o-animation-fill-mode: both;
	animation-fill-mode: both;
}

.close{
	background: url("../images/close.png") center top no-repeat;
	width: 25px;
	height: 25px;
	position: absolute;
	top: -30px;
	right: 0;
}
.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/h.png); display: none}
.hdgz{background-color: #fff;width: 665px;
	border-radius: 10px;
	position: absolute;
	left: 50%;
	top: 50%;
	margin: -336px 0 0 -332px;
	color: #000;
	padding: 25px;
	font-size: 16px;
	line-height: 31px;}
.hdgz ul{padding-left: 20px;}
.hdgz li{list-style-type:decimal;}
.tc1{background-image:url("../images/tc.png");w;width: 623px;height: 812px;position: absolute;left: 50%;top: 50%;margin: -470px 0 0 -270px;}
.tc1 .txt{text-align: center;width: 539px;top: 500px;font-size: 26px;line-height: 41px;margin: 0 100px 0 40px;color: #000;position: absolute;}
.tc1-btn{background-image:url("../images/tc-btn.png");width: 338px;height: 81px;position: absolute;left: 146px;top: 658px;}
.bg{background-image:url(../images/h.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}

/*切屏*/
.al{
	position: absolute;
	left: -400px;
	top: 211px;
	width: 908px;
}
.slider_box{ margin: 0px auto; width:908px; height:648px; position: relative;overflow:hidden; border-bottom-right-radius: 10px;border-bottom-left-radius: 10px;}
.silder_con{ height:648px; overflow: hidden; position: absolute; color: #c30d23; font-size: 28px; line-height: 50px;}
.silder_panel{width: 908px; height: 648px;overflow: hidden; float: left; position: relative; text-align:center;}
a.prev{ background: url("../images/left.png") no-repeat; width:64px; height: 94px; text-indent: -999px; display: block; position: absolute;left: -49px;
	top: 45%;}
a.next{ background: url("../images/right.png") no-repeat; width:64px; height: 94px; text-indent: -999px; display: block; position: absolute; right: -49px;
	top: 45%;}

.bg{background-image:url(../images/h.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 304px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}