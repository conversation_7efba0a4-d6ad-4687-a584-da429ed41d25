$(function () {
    'use strict';
    var Urls = www;
    var isLogin = $("#hid_isLogin").val();
    var lotterFlag = true;
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val(),
    }
    var configData = {
        "actCode": $("#hid_actcode").val(),
        "syCount" :"renewds20240615_count",
        "baseCount":3500,
        "cmpCode": "ACMasterPop20240306"
    }

    var thisPage = {
        init: function () {
            thisPage.bindEvents();

            if (isLogin === '1') {
                thisPage.getLotteryCount();
                thisPage.getRecordList();
                thisPage.generateMarquee();
            } else {
                $(".bg").show();
                $(".tc").show();
            }
        },
        //绑定事件
        bindEvents: function () {
            //抽奖
            $("#doLottery").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }

                thisPage.doLottery();
                pageClick($(this));
            });

            //点击支付
            $(document).on('click', "[name=payurl]", function () {
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                var url = thisPage.getPayUrl();

                $(".bg").hide();
                $(".tc3").hide();
                //客户端
                if (!!thisPage.GetExternal()) {
                    thisPage.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }
                pageClick($(this));

                thisPage.pushdatatocmp("",configData.cmpCode);
            });

            $(".hdgzk").click(function(){
                $(".h").show();
            });
            $(".close,.tc1-btn").click(function(){
                $(".h").hide();
                $(".bg").hide();
                $(".tc1").hide();
                location.reload();
            });
        },
        //9宫格跳动效果
        doLotteryloop:function (){
            var i = 0; // 初始化索引为0
            var arr=[1,2,3,4,5,6,7,8,9];
            function loop() {
                if (i < arr.length) {
                    $(".zp ul li[data-num=" + i + "]").addClass("on").siblings().removeClass("on");

                    setTimeout(() => {
                        i++; // 更新索引值

                        loop(); // 递归调用自身进行下一次循环
                    }, 105); // 设置每次延迟时间为100毫秒
                } else {
                    console.log("循环结束"); // 所有元素都已经处理完成后打印提示信息
                }
            }
            loop(); // 开始第一次循环
        },
        //抽奖
        doLottery:function () {
            if(lotterFlag) {
                lotterFlag = false;
                if($(".f1").html()=='0'){
                    layer.msg("抽奖次数已用完")
                    return false;
                }

                request(
                    `${Urls}renewds2024/dolottery?actcode=${configData.actCode}&uid=${loginUser.uid}&pid=${loginUser.pid}&platform=pc`).then(res => {

                    var currentPdata = res.data;
                    var prizeId = currentPdata.id;

                    thisPage.doLotteryloop();
                    $(".tc1 .txt").html(thisPage.getShowPName(prizeId));

                    setTimeout(function (){
                        $(".zp ul li[data-num=" + prizeId + "]").addClass("on").siblings().removeClass("on");
                        $(".bg").show();
                        $(".tc1").show();
                        }, 900);

                    lotterFlag = true;
                });

            }
        },
        getShowPName:function(id) {
            var retName = "";
            switch (id) {
                case 1:
                    retName = "获得“500积分”，<br>可前往积分商城兑换商品。";
                    break;
                case 2:
                    retName = "获得“1000积分”，<br>可前往积分商城兑换商品。"
                    break;
                case 3:
                    retName = "获得“500元代金券”，<br>代金券有效期6个月，可用于任意大师以上的产品费用抵扣。"
                    break;
                case 4:
                    retName = "获得“1000元代金券”，<br>代金券有效期6个月，可用于任意大师以上的产品费用抵扣。"
                    break;
                case 5:
                    retName = "获得“大师使用期”30天，<br>使用期已自动赋权您的账号。"
                    break;
                case 6:
                    retName = "解锁“益研究每日内部报告（30天）“可于7月1日开始前往【用户中心】-【我的】进行领取。"
                    break;
                case 7:
                    retName = "获得“1V1对话益盟名师”机会，可与益盟名师通过10分钟线上语音通话进行简单答疑，请等待您的专属益盟助手联系您。"
                    break;
                case 8:
                    retName = "获得“账号升级”特权，可免费直通天玑版本，请等待您的专属益盟助手联系您办理相关手续。"
                    break;
                default:
                    break;
            }
            return retName;
        },
        //获取抽奖次数
        getLotteryCount:function (){
            request(
                `${Urls}renewds2024/getlotterycount?actcode=${configData.actCode}`).then(res => {
                if (res.code === '200') {
                    var data = res.data;
                    if(data){
                        var pCount = data.pCount;
                        var pkgList = data.packageList;

                        $(".f1").html(pCount);
                        if (pkgList == null || pkgList.length <= 0) {
                            $(".f1").html(0);
                            $("#renew").show();
                            $("#doLottery").hide();
                        }
                    }else{
                        $(".f1").html(0);
                        $("#renew").show();
                        $("#doLottery").hide();
                    }

                }
            });
        },
        generateMarquee:function () {
            var result = "";

            for (let i = 0; i < 16; i++) {
                var XXX = Math.floor(Math.random() * 1000) // 随机生成三位数的数字
                var YYYArr = [
                    '喜获1v1对话益盟名师',
                    '成功续费',
                    '喜获500元代金券',
                    '账号升级直通天玑版本',
                    '喜获1000元代金券',
                    '喜获30天使用期']
                var YYY = YYYArr[Math.floor(Math.random() * YYYArr.length)] // 随机从 YYYArr 数组中选取一项
                result += (`<li>恭喜EM****${XXX.toString().padStart(3, '0')}用户${YYY}！</li>`)
            }
            $('marquee ul').html(result);
        },
        //获取中奖记录
        getRecordList:function () {
            request(
                `${Urls}renewds2024/getmylotteryinfo?actcode=${configData.actCode}&uid=${loginUser.uid}`).
            then(res => {
                if (res.code === '200') {
                    if (res.data.length>0) {
                        var recordList = res.data;
                        var htmlDom = '<ul>'
                        for (var i = 0; i < recordList.length; i++) {
                            var item = recordList[i]
                            htmlDom += '<li>' + item.benefitName + '</li>'
                        }
                        htmlDom += '</ul>'
                        $('#recordWrap').html(htmlDom)
                    }
                }
            })
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888020000,888080000&groupCode=4&businessType=biztyp-dsxf";
            if (location.host.indexOf("preact.emoney.cn")>-1) {
                //url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888020000,888080000&groupCode=3&resourcestypeid=234&resourcesid=1244397&businessType=biztyp-dsxf";
            }

            return url;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            var pidlist = "888020000,888080000,888020400";
            if (pidlist.indexOf(pid) < 0) {
                layer.msg("本活动仅限大师用户参与");
                return false;
            }
            return true;
        },
        //推送cmp
        pushdatatocmp: function (uname, adcode) {
            var data = {
                "appid": '10088',
                "logtype": 'click',
                "mid": '',
                "pid": thisPage.getQueryString("pid"),
                "sid": thisPage.getQueryString("sid"),
                "tid": thisPage.getQueryString("tid"),
                "uid": thisPage.getQueryString("uid"),
                "uname": uname,
                "adcode": adcode,
                "targeturl": "",
                "pageurl": window.top.location.href
            }
            var saasUrl = "https://ds.emoney.cn/saas/queuepush";
            var saasSrc = saasUrl + "?v=" + Math.random()
                + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
                + "&message=" + encodeURIComponent(JSON.stringify(data));

            var elm = document.createElement("img");
            elm.src = saasSrc;
            elm.style.display = "none";
            document.body.appendChild(elm);
        },
        //时间戳toDate
        timestampToDate: function (timestamp) {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = date.getMonth() + 1; // getMonth() 返回的月份从 0 开始，所以需要加 1
            const day = date.getDate();

            return `${year}年${month}月${day}日`;
        },
        getQueryString: function (name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        },
        GetExternal: function () {
            return window.external.EmObj;
        },

        //调用客户端接口
        PC_JH: function (type, c) {
            try {
                var obj = thisPage.GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {
            }
        },
        setCookie: function (name, value) {
            var expdate = new Date();
            expdate.setTime(expdate.getTime() + 1000 * 60 * 60 * 24 * 30);
            document.cookie = name + "=" + value + ";expires=" + expdate.toGMTString() + ";path=/";
        },


        getCookie: function (c_name) {
            if (document.cookie.length > 0) {
                var c_start = document.cookie.indexOf(c_name + "=")
                if (c_start != -1) {
                    c_start = c_start + c_name.length + 1
                    var c_end = document.cookie.indexOf(";", c_start)
                    if (c_end == -1) c_end = document.cookie.length
                    return unescape(document.cookie.substring(c_start, c_end))
                }
            }
            return ""
        }
    }
    thisPage.init();
});

function request (url, dataType) {
    return new Promise((resolve, reject) => {
        $.ajax({
            type: 'get',
            url: url,
            dataType: dataType ? dataType : 'jsonp',
            success: function (data) {
                console.log(data)
                if (data.code === '200') {
                    resolve(data)
                }
            },
            error: function (err) {
                reject(err)
            }
        })
    })
}

function pageClick(obj){
    var App = "10013";   //APPID 没有请申请
    var Module = "renewds_20240615";//模块名称
    var Remark = "618大师续费";     //备注可为空
    var ClickFlag = true;//默认为true
    var Host = "https://api2-tongji.emoney.cn";
    var ClickUrl = Host + "/Page/PageClick";
    var PageViewUrl = Host + "/Page/PageView";
    var pageUrl = window.top.location.href;

    var _clickkey = obj.attr("clickkey");
    var _clickdata = obj.attr("clickdata");
    var _clickremark = obj.attr("clickremark");
    var _htmltype = obj.attr("type");
    if (App != "" && _clickdata != "") {
        var src = ClickUrl + "?v=" + Math.random()
            + "&app=" + App
            + "&module=" + Module
            + "&clickkey=" + _clickkey
            + "&clickdata=" + _clickdata
            + "&clickremark=" + _clickremark
            + "&htmltype=" + _htmltype
            + "&pageurl=" + encodeURIComponent(pageUrl)
            + "&remark=" + Remark;
        var elm = document.createElement("img");
        elm.src = src;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
}