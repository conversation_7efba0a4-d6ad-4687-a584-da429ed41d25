$(function(){
	var sWidth = $(".slider_name").width();
	var len = $(".slider_name .silder_panel").length;
	var index = 0;
	var picTimer;
	
	$(".slider_name .silder_nav li").css({"opacity":"0.6","filter":"alpha(opacity=60)"}).mouseenter(function() {																		
		index = $(".slider_name .silder_nav li").index(this);
		showPics(index);
	}).eq(0).trigger("mouseenter");
	
	$(".al .prev,.al .next").css({"opacity":"1","filter":"alpha(opacity=100)"}).hover(function(){
		$(this).stop(true,false).animate({"opacity":"1","filter":"alpha(opacity=100)"},300);
	},function() {
		$(this).stop(true,false).animate({"opacity":"0.6","filter":"alpha(opacity=60)"},300);
	});

	// Prev
	$(".al .prev").click(function() {
		index -= 1;
		if(index == -1) {index = len - 1;}
		showPics(index);
	});

	// Next
	$(".al .next").click(function() {
		index += 1;
		if(index == len) {index = 0;}
		showPics(index);
	});

	// 
	$(".slider_name .silder_con").css("width",sWidth * (len));
	
	// mouse 
	$(".slider_name").hover(function() {
		clearInterval(picTimer);
	},function() {
		picTimer = setInterval(function() {
			showPics(index);
			index++;
			if(index == len) {index = 0;}
		},3000); 
	}).trigger("mouseleave");
	
	// showPics
	function showPics(index) {
		var nowLeft = -index*sWidth; 
		$(".slider_name .silder_con").stop(true,false).animate({"left":nowLeft},300);
		$(".slider_name .silder_nav li").removeClass("current").eq(index).addClass("current"); 
		$(".slider_name .silder_nav li").stop(true,false).animate({"opacity":"0.5"},300).eq(index).stop(true,false).animate({"opacity":"1"},300);
	}
});



//2222222222222222222
$(function(){
	var sWidth = $(".slider_name2").width();
	var len = $(".slider_name2 .silder_panel2").length;
	var index = 0;
	var picTimer;
	
	$(".slider_name2 .silder_nav2 li").css({"opacity":"0.6","filter":"alpha(opacity=60)"}).mouseenter(function() {																		
		index = $(".slider_name2 .silder_nav2 li").index(this);
		showPics(index);
	}).eq(0).trigger("mouseenter");
	
	$(".al2 .prev,.al2 .next").css({"opacity":"1","filter":"alpha(opacity=100)"}).hover(function(){
		$(this).stop(true,false).animate({"opacity":"1","filter":"alpha(opacity=100)"},300);
	},function() {
		$(this).stop(true,false).animate({"opacity":"0.6","filter":"alpha(opacity=60)"},300);
	});

	// Prev
	$(".al2 .prev").click(function() {
		index -= 1;
		if(index == -1) {index = len - 1;}
		showPics(index);
	});

	// Next
	$(".al2 .next").click(function() {
		index += 1;
		if(index == len) {index = 0;}
		showPics(index);
	});

	// 
	$(".slider_name2 .silder_con2").css("width",sWidth * (len));
	
	// mouse 
	$(".slider_name2").hover(function() {
		clearInterval(picTimer);
	},function() {
		picTimer = setInterval(function() {
			showPics(index);
			index++;
			if(index == len) {index = 0;}
		},3000); 
	}).trigger("mouseleave");
	
	// showPics
	function showPics(index) {
		var nowLeft = -index*sWidth; 
		$(".slider_name2 .silder_con2").stop(true,false).animate({"left":nowLeft},300);
		$(".slider_name2 .silder_nav2 li").removeClass("current").eq(index).addClass("current"); 
		$(".slider_name2 .silder_nav2 li").stop(true,false).animate({"opacity":"0.5"},300).eq(index).stop(true,false).animate({"opacity":"1"},300);
	}
});