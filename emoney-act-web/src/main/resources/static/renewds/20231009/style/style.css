@charset "utf-8";
/* CSS Document */
*{margin:0; padding:0;}
body{background-color:#EFDDB7;font-family: "微软雅黑";}
ul,li {margin:0;padding:0;list-style:none;}
A:link {COLOR: #fff; TEXT-DECORATION: underline;}
A:visited {COLOR: #fff; TEXT-DECORATION: none}
A:hover{COLOR:#ffff00; TEXT-DECORATION: none}
.red{color: #e21a1a; font-size: 40px; font-weight: bold;}
.yellow{color: #edc99b;}
.black{color: #444041;}
.f12{font-size: 12px;}
.f14{font-size: 14px; line-height: 18px;}
.img_1{background: url("../images/index_01.jpg") center; height: 123px;}
.img_2{background: url("../images/index_02.jpg") center; height: 123px;}
.img_3{background: url("../images/index_03.jpg") center; height:245px;}
.img_4{background: url("../images/index_04.jpg") center; height: 248px;}
.img_5{background: url("../images/index_05.jpg") center; height:499px;}
.img_6{background: url("../images/index_06.jpg") center; height: 498px;}
.img_7{background: url("../images/index_07.jpg") center; height: 489px; overflow: hidden;}
.img_8{background: url("../images/index_08.jpg") center; height: 222px;}
.img_7 ul{
	width: 841px;
	line-height: 45px;
	font-size: 26px;
	color: #fff;
	position: absolute;
	left: -320px;
	top: 142px;
}


.main{width:100px; margin:0 auto; position:relative;}
.txt{
	width: 956px;
	position: absolute;
	top: 340px;
	left: -425px;
	color: #444041;
	font-size: 26px;
	text-align: center;
}
.txt2{
	position: absolute;
	top: 396px;
	left: 421px;
	font-size: 24px;
	width: 113px;
}
.txt3{
	position: absolute;
	top: 26px;
	left: 92px;
	font-size: 50px;
	width: 450px;
	color: #f0e3c2;
	text-align: center;
}
.hend{
	background: url("../images/hend.png");
	width: 54px;
	height: 54px;
	position: absolute;
	top: 15px;
	left: 160px;
}
.btn1{
	background: url("../images/btn1.png");
	width: 240px;
	height: 50px;
	position: absolute;
	top: -479px;
	left: 253px;
	color: #e21a1a!important;
	font-size: 24px;
	text-align: center;
	line-height: 50px;
	text-decoration: none!important;
}
.btn2{
	background: url("../images/btn2.png");
	width: 203px;
	height: 50px;
	color: #444041!important;
	font-size: 24px;
	text-align: center;
	line-height: 50px;
	text-decoration: none!important;
}
.btn2h{
	background: url("../images/btn2h.png");
	width: 203px;
	height: 50px;
	color: #444041!important;
	font-size: 24px;
	text-align: center;
	line-height: 50px;
	text-decoration: none!important;
}
.a1{
	position: absolute;
	top: -341px;
	left: 297px;
}
.a2{
	position: absolute;
	top: -202px;
	left: 297px;
}
.a3{
	position: absolute;
	top: -63px;
	left: 297px;
}
.a4{
	position: absolute;
	top: 74px;
	left: 297px;
}
.btn3{
	background: url("../images/btn3.png");
	width: 522px;
	height: 168px;
	position: absolute;
	top: 165px;
	left: -142px;
}
.btn4{
	background: url("../images/btn4.png");
	width: 414px;
	height: 141px;
	position: absolute;
	top: 36px;
left: 162px;
}
.ico{
	background: url("../images/ico.png");
	width: 82px;
	height: 69px;
}
.b1{
	position: absolute;
	top: 69px;
	left: -386px;
}
.b2{
	position: absolute;
	top: -69px;
	left: -386px;
}
.b3{
	position: absolute;
	top: -209px;
	left: -386px;
}
.b4{
	position: absolute;
	top: -349px;
	left: -386px;
}
.b5{
	position: absolute;
	top: -489px;
	left: -386px;
}
.b6{
	position: absolute;
	top: 115px;
	left: -386px;
}
.close{
	background: url("../images/close.png") center top no-repeat;
	width: 28px;
	height: 28px;
	position: absolute;
	top: 0px;
	right: 0px;
	cursor: pointer;
}
.pfb{background: url("../images/pf.png") center; height:178px; width: 100%; position: fixed; left: 0; bottom: 0;}
.pf1{position: fixed; right: 0; top: 10%; display: none;}
.pf1 .b1{background: url("../images/pf1.png") center; width: 200px; height:230px; display: block;}
.pf1 .b2{background: url("../images/pf2.png") center; width: 180px; height:163px; display: block; margin: 20px auto 0;}


.dh:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/h.png); display: none;}
.hdgz{
	position: absolute;
	left: 50%;
	top: 50%; margin: -400px 0 0 -300px;
	width:601px; height: 884px;
	color: #4F371A;
	font-size:16px;
	line-height:29px;background:url("../images/tc.png");
}
.hdgz ul{padding:98px 30px 0 67px; font-size: 18px; line-height: 34px;}
.tc1{background:url("../images/tc1.png"); width: 399px; height: 377px; position: fixed;
	top: 50%;
	left: 50%;
	margin: -250px 0 0 -200px;}

.tc1 .t1{
	position: absolute;
	top: 166px;
	left: 32px;
	font-size: 24px;
	width: 332px;
	color: #fff;
	text-align: center; line-height: 35px;
}
.tc1 .t2{
	position: absolute;
	top: 76px;
	left: 32px;
	font-size: 24px;
	width: 332px;
	color: #fff;
	text-align: center;
	line-height: 35px;
}
.tc1 .t3{
	position: absolute;
	top: 222px;
	left: 59px;
	border-top: 1px solid #fff;
	font-size: 16px;
	width: 278px;
	color: #fff;
	text-align: center;
	padding-top: 10px;
}
.tc2{background:url("../images/tc2.png"); width: 399px; height: 377px; position: fixed;
	top: 50%;
	left: 50%;
	margin: -250px 0 0 -200px;}
.tc2 .t1{
	position: absolute;
	top: 161px;
	left: 32px;
	font-size: 24px;
	width: 332px;
	color: #e21a1a;
	text-align: center;
	line-height: 35px;
}
.tc2 .t2{
	position: absolute;
	top: 234px;
	left: 32px;
	font-size: 15px;
	width: 332px;
	color: #e21a1a;
	text-align: center;
}
.tc-bg1{
	background: url("../images/tc-bg1.png") no-repeat bottom; color: #edc99b;
	font-size: 40px;
	font-weight: bold;
	width: 344px;
	padding-bottom: 25px;
	line-height: 45px;
	text-align: center;
	position: absolute;
	top: 81px;
	left: 26px;
}
.tc-bg1b{
	background: url("../images/tc-bg1.png") no-repeat bottom; color: #da2f1d;
	font-size: 35px;
	font-weight: bold;
	width: 344px;
	padding-bottom: 25px;
	line-height: 45px;
	text-align: center;
	position: absolute;
	top: 81px;
	left: 26px;
}
.tc1 .tc-btn1,
.tc2 .tc-btn1{
	background: url("../images/tc-btn1.png");
	width: 201px;
	height: 45px;
	line-height: 45px;
	text-align: center;
	position: absolute;
	top: 285px;
	left: 101px;
	font-size: 24px;
	text-decoration: none!important;
}
.tc1 .tc-btn2,
.tc2 .tc-btn2,
.hdgz .tc-btn2{
	background: url("../images/tc-btn2.png");
	width:281px;
	height: 63px;
	position: absolute;
	top: 693px;
  left: 159px;
}
.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #000; margin: 20px 0;
}

.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt1{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txtlogin{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}
.tc1,.tc2{
	display: none;
}