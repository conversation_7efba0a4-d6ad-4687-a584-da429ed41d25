@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#E14335;font-family: "微软雅黑"; overflow-x: hidden;}
ul,li{list-style: none;}
.red2{color: #e34536;}
.f1{font-size: 82px; font-weight: bold; line-height: 82px;}
.f2{font-size: 86px; font-weight: bold; line-height: 86px;}
.img_1{background:url(../images/index_01.jpg) center; height:335px; position: relative;}
.img_2{background:url(../images/index_02.jpg) center; height:336px;}
.img_3{background:url(../images/index_03.png) center; height:350px;}
.img_4{background:url(../images/index_04.png) center; height:349px;}
.img_5{background:url(../images/index_05.png) center; height:849px;}
.img_6{background:url(../images/index_06.png) center; height:473px;}

.img_1 li{background:url("../images/dm.png"); width: 429px; height: 116px; position: absolute; left: 100%; font-size: 15px; color: #000; font-weight: bold;box-sizing: border-box; padding: 47px 0 0 56px;}
.dm{
	-webkit-animation: dm 15s linear infinite;
	animation-name: dm 15s linear infinite;
}
@-webkit-keyframes dm{
	to {left: -20%
	}
}

@keyframes dm{
	to {left: -20%
	}
}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:0px 0 20px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover { color: #fff; TEXT-DECORATION: none}
img{border:none;}

.main{width:100px; margin:0 auto; position:relative;}
.bg1{
	background-image: url("../images/bg1.png");
	position: absolute;
	left: -470px;
	top: 43px;
	width: 1038px;
	height: 259px;
}
.bg2{
	background-image: url("../images/bg2.png");
	position: absolute;
	left: -470px;
	top: 43px;
	width: 1038px;
	height: 259px;
}
.bg3{
	background-image: url("../images/bg3.png");
	position: absolute;
	left: -470px;
	top: 43px;
	width: 1038px;
	height: 259px;
}

.btn1{
	background: url("../images/btn1.png");
	width: 307px;
	height: 76px; text-align: center; line-height: 70px; font-size: 42px; color: #ffff00; font-weight: bold; text-decoration: none;
}
.an1{
	position: absolute;
	top: 64px;
	left: 583px;
}

.an2{
	position: absolute;
	top: 84px;
	left: 583px;
}
.btn2{
	background: url("../images/btn2.png");
	width: 260px;
	height: 69px;
	position: absolute;
	top: 150px;
	left: 617px;
}
.btn3{
	background: url("../images/btn3.png");
	width: 47px;
	height: 122px;
	position: absolute;
	top: 94px;
	left: 505px;
}

.s1{
	position: absolute;
	top: 32px;
	right: -26px;
	width: 70px;
	height: 60px;
	pointer-events: none;
}
.s2{
	position: absolute;
	top: 22px;
	right: -30px;
	width: 70px;
	height: 60px;
	pointer-events: none;
}
.bg4{
	position: absolute;
	left: -290px;
	top: 199px;
	text-align: center;
	font-size: 28px;
	color: #3f3f3f;
	width: 794px;
}
.bg4 ul{width: 960px;}
.bg4 li{background-image:url("../images/bg4.png"); width: 289px; height:188px; float: left; margin-right: 80px; padding-top: 20px; line-height: 45px;}
.img_6 .t1{text-align: center;font-size: 55px; color: #3f3f3f; line-height: 60px; padding-top: 90px;}
.btn4{
	background: url("../images/btn4.png");
	width: 435px;
	height: 84px;
	text-align: center;
	line-height: 78px;
	font-size: 32px;
	color: #ffff00;
	position: absolute;
	top: 200px;
	left: -168px;
}
.btn5{
	background: url("../images/btn5.png");
	width: 175px;
	height: 48px; text-align: center; line-height: 45px; font-size:28px; color: #fff; display: block; margin: 10px auto 0; position: relative;
}
.btn6{
	background: url("../images/btn6.png");
	width: 238px;
	height: 48px; text-align: center; line-height: 45px; font-size:24px; color: #fff; display: block; margin: 10px auto 0;position: relative;
}

.h{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%; display: none;}
.hdgz{background-image:url(../images/hdgz.png); width: 607px; height:588px; position: absolute; left: 50%; top: 50%; margin:-294px 0 0 -303px;font-size: 14px;color: #808080;}
.hdgz .t1{position: absolute; left: 42px;top: 109px;width: 532px; line-height: 17.5px;}
.hdgz .t2{position: absolute;left: 136px;
	top: 408px;
	line-height: 26px;}
.hdgz .t3{position: absolute;left: 136px;
	top: 464px;
	width: 452px;}
.hdgz .t4{position: absolute; left: 136px;
	top: 505px;
	width: 440px;}
.btn7{width: 92px; height: 40px; position: absolute;left: 480px;
	top: 533px;background-color: #f44e45; line-height:40px; text-align: center; font-size:22px; color: #fff; border-radius: 28px;}


.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt1{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}

.dh:hover{
	-webkit-animation: dh 0.3s linear infinite alternate;
	animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}

@keyframes dh{
	to {
		-webkit-transform: scale(1.1);
		transform: scale(1.1);
	}
}
.dh2{
	-webkit-animation: dh2 0.5s linear infinite alternate;
	animation-name: dh2 0.5s linear infinite alternate;
}
@-webkit-keyframes dh2{
	to {margin: 15px -15px 0 0px;
	}
}

@keyframes dh2{
	to {margin: 15px -15px 0 0px;
	}
}
#div1{
	width: 666px;
	position: absolute;
	left: 15px;
	top: 10px;
	height: 262px;
	overflow: hidden;
}
#div1 ul{
	position: absolute;
	left: 0;
	top: -2px;
}
#div1 a{
	position: absolute;
	z-index: 2;
	text-decoration: none;
	top: 45%;
	display: none;
}
#div1 ul li{
	padding: 5px;
	list-style: none;
	float: left;
}




.txt1{
	position: absolute;
	left: 132px;
	top: 24px;
	width: 200px;
	color: #F8ECC8;
	font-size: 26px;
	text-align: right;
	letter-spacing: 19px;
}
.txt2{
	position: absolute;
	left: -102px;
	top: 256px;
	width: 604px; color: #fff;
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -10px;
	top: -10px;
	width: 30px;
	height: 30px;
}
.wddd{position: absolute; right: 0px; top: 20%; background-color: #F44D39; width: 40px; padding: 10px 0; color: #fff; font-size: 22px; text-align: center; border-top-left-radius: 10px;border-bottom-left-radius: 10px; line-height: 25px;}

.img_1 li{background:url("../images/dm.png"); width: 429px; height: 116px; position: absolute; left: 100%; font-size: 15px; color: #000; font-weight: bold;box-sizing: border-box; padding: 47px 0 0 56px;-webkit-animation: dm 65s linear infinite;
	animation-name: dm 65s linear infinite;
}
@-webkit-keyframes dm{
	to {left: -20%
	}
}

@keyframes dm{
	to {left: -400%
	}
}
.img_1 li:nth-child(1) {
	top: 20px;
	margin-left: 0px;
}

.img_1 li:nth-child(2) {
	top: 300px;
	margin-left: 300px;
}

.img_1 li:nth-child(3) {
	top: 150px;
	margin-left: 600px;
}

.img_1 li:nth-child(4) {
	top: 70px;
	margin-left: 900px;
}

.img_1 li:nth-child(5) {
	top: 220px;
	margin-left: 1200px;
}

.img_1 li:nth-child(6) {
	top: 20px;
	margin-left: 1500px;
}

.img_1 li:nth-child(7) {
	top: 300px;
	margin-left: 1800px;
}

.img_1 li:nth-child(8) {
	top: 150px;
	margin-left:2100px;
}

.img_1 li:nth-child(9) {
	top: 70px;
	margin-left: 2400px;
}

.img_1 li:nth-child(10) {
	top: 220px;
	margin-left: 2700px;
}
.img_1 li:nth-child(11) {
	top: 20px;
	margin-left: 3000px;
}

.img_1 li:nth-child(12) {
	top: 300px;
	margin-left: 3300px;
}

.img_1 li:nth-child(13) {
	top: 150px;
	margin-left: 3600px;
}

.img_1 li:nth-child(14) {
	top: 70px;
	margin-left: 3900px;
}

.img_1 li:nth-child(15) {
	top: 220px;
	margin-left: 4200px;
}

.img_1 li:nth-child(16) {
	top: 20px;
	margin-left: 4500px;
}

.img_1 li:nth-child(17) {
	top: 300px;
	margin-left: 4800px;
}

.img_1 li:nth-child(18) {
	top: 150px;
	margin-left:5100px;
}

.img_1 li:nth-child(19) {
	top: 70px;
	margin-left: 5400px;
}

.img_1 li:nth-child(20) {
	top: 220px;
	margin-left: 5700px;
}