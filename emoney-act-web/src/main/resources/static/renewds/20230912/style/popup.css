/*遮罩*/
.popup-mask {
	position: fixed;
	top: 0px;
	left: 0px;
	width: 100%;
	height: 100%;
	background-color: #000;
	opacity: 0.5;
	filter: alpha(opacity=50);
	display: none; z-index: 998;
}
/*弹窗*/
.popup-wrap {
	position: fixed;
	top: 50%;
	left: 50%;
	min-width: 462px;
	min-height: 464px;
	background: url(../images/tc.png) center no-repeat;
	overflow: hidden;
	display: none;z-index: 999;
}
.popup-wrap .popup-title {
	height: 40px;
	line-height: 40px;
	text-align: center;
	font-size: 16px;
	color: #fff;
	overflow: hidden;
}
.popup-wrap .popup-body {
	padding: 10px;
	font-size: 14px;
	text-align: center;
}
.popup-wrap .popup-operate {
	margin-top: 16px;
	text-align: center;
}
.popup-wrap .popup-operate input[type=button] {
	height: 35px;
	line-height: 35px;
	padding: 0 20px;
	margin-right: 10px;
	font-size: 16px;
	cursor: pointer;
}
.popup-wrap .popup-operate input[type=button]:last-child {
	margin-right: 0;
}
.popup-wrap .popup-close {
	position: absolute;
	top: -15px;
	right: -10px;
	width: 50px;
	height: 50px;
	line-height: 50px;
	font-size: 30px;
	color: #fff;
	text-align: center;
	cursor: pointer;
}

