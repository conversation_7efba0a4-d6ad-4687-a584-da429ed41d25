@charset "utf-8";
/* CSS Document */
*{margin:0; padding:0;}
body{background-color:#FFE4CD;font-family: "微软雅黑";}
ul,li {margin:0;padding:0;list-style:none;}
A:link {COLOR: #e83828; TEXT-DECORATION: underline;}
A:visited {COLOR: #e83828; TEXT-DECORATION: none}
A:hover{COLOR:#000; TEXT-DECORATION: none}
.red{color: #ff4e1f;}
.f12{font-size: 12px;}
.f14{font-size: 14px; line-height: 18px;}
.img_1{background: url("../images/index_01.jpg") center; height: 299px;}
.img_2{background: url("../images/index_02.jpg") center; height: 396px;}
.img_2 ul{width:710px; margin:0 auto; position:relative; padding: 30px 0 0 20px;}
.img_2 li{background: url("../images/bg1.png") no-repeat top; width: 156px; height: 173px; float: left; margin: 0 15px 10px 0;}
.img_2 .t1{width: 107px;line-height: 44px; text-align: center; color: #fff; font-size: 18px;}
.img_2 .t2{height: 76px; text-align: center; display: table-cell; vertical-align: middle; width: 150px; color: #ff5348; font-size: 18px;}
.img_2 .btn{background: url("../images/btn1.png"); width: 114px; height:38px; line-height: 33px; display: block; text-align: center; color: #603813; font-size: 16px; margin: 0 0 0 19px; text-decoration: none;}
.img_2 .a1{color: #fff; background-position: 0 -38px ;}
.img_2 .a2{color: #fff; background-position: 0 -76px ;}
.img_2 .a3{color: #fff; background-position: 0 -114px ;}
.img_2 .on{background-position: 0 -173px;}
.img_2 .on .t1{color: #ff5a44;}
.img_2 .on .t2{color: #fff5c0;}
.img_3{background: url("../images/index_03.jpg") center; height:245px;}
.img_4{background: url("../images/index_04.jpg") center; height: 311px;}
.img_5{background: url("../images/index_05.png") center; height: 541px;}
.img_6{background: url("../images/index_06.png") center; height: 564px;}
.img_7{background: url("../images/index_07.png") center; height: 500px;}
.img_8{background: url("../images/index_08.png") center; height: 190px;}
.img_9{background: url("../images/index_09.png") center; background-size: 2500px 100%;}
.img_9 ul{padding: 30px 0; line-height: 54px; font-size: 22px; color: #ff5348;}
.img_9 li{width: 670px; height: 54px; background-color: #FFEEED; margin: 0 auto 20px; border-radius: 27px; padding-left:30px;}
.img_10{background: url("../images/index_10.png") center; height: 98px;}

.main{width:100px; margin:0 auto; position:relative;}
.txttip{
	width: 600px;
	position: absolute;
	top: 181px;
	left: -251px;
	color: #934e1f;
	font-size: 22px;
	text-align: center;
}
.txt2{
	position: absolute;
	top: 37px;
	left: 393px;
	font-size: 24px;
	width: 113px;
}
.btn2{
	background: url("../images/btn2.png");
	width: 248px;
	height: 218px;
	position: absolute;
	top: 27px;
	left: 247px; color: #ffffd7!important; font-size: 42px; text-align: center; line-height: 141px; text-decoration: none!important;box-sizing: border-box; padding: 0 53px 0 0;
}
.btn3{
	background: url("../images/btn3.png");
	width: 486px;
	height: 145px;
	position: absolute;
	top: 25px;
	left: -178px;
}
.btn4{
	background: url("../images/btn4.png");
	width: 414px;
	height: 141px;
	position: absolute;
	top: 36px;
left: 162px;
}
.close{
	background: url("../images/close.png") center top no-repeat;
	width: 45px;
	height: 45px;
	position: absolute;
	top: -40px;
	right: -40px;
}
.pfb{background: url("../images/pf.png") center; height:178px; width: 100%; position: fixed; left: 0; bottom: 0;}
.pf1{position: fixed; right: 0; top: 10%; display: none;}
.pf1 .b1{background: url("../images/pf1.png") center; width: 200px; height:230px; display: block;}
.pf1 .b2{background: url("../images/pf2.png") center; width: 180px; height:163px; display: block; margin: 20px auto 0;}


.dh:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.h{position:fixed; left:0px; top:0px; width:100%; height:100%;background-image:url(../images/h.png); display: none;}
.hdgz{
	position: absolute;
	left: 50%;
	top: 50%; margin: -300px 0 0 -450px;
	width:900px; height: 600px;
	color: #4F371A;
	font-size:16px;
	line-height:29px;background:url("../images/tc.png");
}
.hdgz ul{padding:70px 30px 0 63px}
.hdgz li{
	list-style-type:disc;}
.tc1{display: none; position: absolute;
	left: 50%;
	top: 50%;background:url("../images/tc1.png"); width: 810px; height: 502px; margin: -250px 0 0 -450px;}
.tc2{display:none;position: absolute;
	left: 50%;
	top: 50%;background:url("../images/tc2.png"); width: 638px; height: 582px; margin: -250px 0 0 -310px;}
.tc2 .t1{width: 596px; text-align: center; color: #fff; font-size: 45px; padding-top: 140px; height: 120px;}
.tc2 .t2{width: 596px; text-align: center; color: #fff; font-size: 52px;}
.tc2 .t3{width: 596px; text-align: center; color: #fff; font-size: 24px;height: 84px;}
.tc2 .t4{width: 596px; text-align: center; color: #000; font-size: 30px;height: 90px;}
.tc2 .t5{width: 544px; line-height: 62px; background-color: #F3F3F3; border-radius: 12px; text-align: center; color: #666; font-size: 20px; margin-left: 24px;}
.tc1 .tc-btn1{
	background: url("../images/tc-btn1.png");
	width: 619px;
	height: 163px;
	position: absolute;
	top: 203px;
	left: 106px;
}
.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #000; margin: 20px 0 180px;
}
.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt1{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}