$(function () {
    'use strict';
    var isLogin = $("#hid_isLogin").val();
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val(),
    }
    var testHost = 'testact.emoney.cn';
    var apiUrl = location.host === testHost ? "https://mobiletest.emoney.cn/emapp/" : "https://emapp.emoney.cn/";
    var cmpCode = "ACMasterPop20231025";
    var actcode = $("#hid_actcode").val();
    var cookiename = "emoney.actds20231025." + loginUser.uid;
    var acCodeList = "ac-1231009101620462,ac-1231009101421839,ac-123100910115428,ac-123100910094197,ac-123100910062341,ac-1231009100347980,ac-1231009100105662,ac-123100909580938,ac-1231009095443695";

    var thisPage = {
        init: function () {
            thisPage.bindEvents();

            if (isLogin === '1') {
                thisPage.isPayment();
                thisPage.pushdatatocmp(loginUser.uid, cmpCode);
            } else {
                $(".bg").show();
                $(".tc").show();
            }
        },
        //绑定事件
        bindEvents: function () {
            //点击支付
            $(document).on('click', "[name=payurl]", function () {
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                var url = thisPage.getPayUrl();

                $(".bg").hide();
                $(".tc3").hide();
                //客户端
                if (!!thisPage.GetExternal()) {
                    thisPage.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }
            });
            $(".btn3").click(function(){
                $(".h").show();
            });
            $(".close").click(function(){
                $(".h").hide();
                $(".bg").hide();
                $(".tc3").hide();
            });
        },
        //是否付定金
        isPayment: function () {
            //查询是否支付尾款
            $.ajax({
                url: apiUrl + "buy/order/QueryOrderList?Emapp-Format=EmappJsonp",
                timeout: 5000,
                type: 'get',
                dataType: 'jsonp',
                cache: false,
                data: {"phoneEncrypt": loginUser.mobileX, "type": 0},
                success: function (data) {
                    if (data.detail) {
                        var orderList = data.detail;
                        var flag_isPayDJ = false;
                        $.each(orderList, function (index, item) {
                            var orderLogistice = item.orderLogistice;//订单活动包信息
                            $.each(orderLogistice, function (index1, item1) {
                                if (acCodeList.indexOf(item1.activityCode) > -1) {
                                    if (item.payStatusId == 1) {
                                        //已付定金 待付尾款
                                        flag_isPayDJ = true;
                                        thisPage.showWK(false);
                                    } else if (item.payStatusId == 200) {
                                        //已付尾款
                                        flag_isPayDJ = true;
                                        thisPage.showWK(true);
                                    }
                                }
                            });
                        });

                        if(!flag_isPayDJ){
                            thisPage.showDJ();
                            var val = thisPage.getCookie(cookiename);
                            if (!val) {
                                $(".bg").show();
                                $(".tc3").show();

                                thisPage.setCookie(cookiename, "1");
                            }
                        }

                    }
                }
            });
        },
        showDJ: function () {
            $(".img_1 ul").hide();
            $("#ul_dj").show();
            $(".red2").html("订金");

            $(".img_4").hide();
            $(".img_4b").show();

            $(".pf").hide();
            $(".pfb").show();
        },

        showWK: function (isComplete) {
            $(".img_1 ul").hide();
            $("#ul_wk").show();
            $(".red2").html("尾款");

            $(".img_4").show();
            $(".img_4b").hide();

            $(".pf").show();
            $(".pfb").hide();

            if (isComplete) {
                $(".btn1h").hide();
                $(".btn1h2").show();

                $(".btn2h").hide();
                $(".btn2h2").show();
            }
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888020000,888080000&groupCode=3&resourcestypeid=234&resourcesid=1244397&businessType=biztyp-dsxf";
            if (location.host === testHost) {
                url = "https://mobiletest.emoney.cn/appstatic/ymstock/payment-middle/?goodsPid=888020000,888080000&groupCode=3&resourcestypeid=234&resourcesid=1244397&businessType=biztyp-dsxf";
            }

            return url;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            var pidlist = "888020000,888080000,888020400";
            if (pidlist.indexOf(pid) < 0) {
                layer.msg("本活动仅限大师用户参与");
                return false;
            }
            return true;
        },
        //推送cmp
        pushdatatocmp: function (uname, adcode) {
            var data = {
                "appid": '10088',
                "logtype": 'click',
                "mid": '',
                "pid": thisPage.getQueryString("pid"),
                "sid": thisPage.getQueryString("sid"),
                "tid": thisPage.getQueryString("tid"),
                "uid": thisPage.getQueryString("uid"),
                "uname": uname,
                "adcode": adcode,
                "targeturl": "",
                "pageurl": window.top.location.href
            }
            var saasUrl = "https://ds.emoney.cn/saas/queuepush";
            var saasSrc = saasUrl + "?v=" + Math.random()
                + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
                + "&message=" + encodeURIComponent(JSON.stringify(data));

            var elm = document.createElement("img");
            elm.src = saasSrc;
            elm.style.display = "none";
            document.body.appendChild(elm);
        },
        //时间戳toDate
        timestampToDate: function (timestamp) {
            const date = new Date(timestamp);
            const year = date.getFullYear();
            const month = date.getMonth() + 1; // getMonth() 返回的月份从 0 开始，所以需要加 1
            const day = date.getDate();

            return `${year}年${month}月${day}日`;
        },
        getQueryString: function (name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        },
        GetExternal: function () {
            return window.external.EmObj;
        },

        //调用客户端接口
        PC_JH: function (type, c) {
            try {
                var obj = thisPage.GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {
            }
        },
        setCookie: function (name, value) {
            var expdate = new Date();
            expdate.setTime(expdate.getTime() + 1000 * 60 * 60 * 24 * 30);
            document.cookie = name + "=" + value + ";expires=" + expdate.toGMTString() + ";path=/";
        },


        getCookie: function (c_name) {
            if (document.cookie.length > 0) {
                var c_start = document.cookie.indexOf(c_name + "=")
                if (c_start != -1) {
                    c_start = c_start + c_name.length + 1
                    var c_end = document.cookie.indexOf(";", c_start)
                    if (c_end == -1) c_end = document.cookie.length
                    return unescape(document.cookie.substring(c_start, c_end))
                }
            }
            return ""
        }
    }
    thisPage.init();
});