@charset "utf-8";
/* CSS Document */

body{margin:0; padding:0;background-color:#80050A; line-height: 40px; font-size: 33px; color: #F9D2B4;font-family: "微软雅黑";}
a{color: #fff;TEXT-DECORATION: none;}
a:hover{color: #ffff00;}
div{vertical-align:bottom; font-family: "微软雅黑"; }
img{border:none;}
ul,li{list-style: none; margin: 0; padding: 0;}
.clr{clear: both;}
table,th,td {
  border : 1px solid black;
  border-collapse: collapse; text-align: center; color: #000; font-size: 18px;
}
.f27{font-size: 27px;}
.f19{font-size: 19px;}
.yellow{color: #FFFF00}
.red{color: #C30D23; font-size: 34px; padding-bottom: 35px;}


.img_1{background:url("images/index_01.jpg") center no-repeat; height:332px;}
.img_2{background:url("images/index_02.jpg") center no-repeat; height:301px;}
.img_2_app {
	background: url("images/index_02_app.jpg") center no-repeat;
	height: 276px;
}
.img_3{background:url("images/index_03.jpg") center no-repeat; height:258px;}
.img_4{background:url("images/index_04.jpg") center top no-repeat; padding-top: 45px; overflow: hidden;}
.img_5{background:url("images/index_05.png") center no-repeat; height:463px;}
.fc {
	position: fixed;
	bottom: 0;
	width: 100%;
	height: 90px;
	background: url("images/jpg_05.jpg") center no-repeat;
}

.main{position:relative; width:1px; margin:0 auto;}
.pf{background:url("images/pf.png") center no-repeat; width: 211px; height:359px; position: fixed; right: 0; top: 20%; display: none;}
.pf img{position: absolute; left:40px;top: 92px; width: 123px; height: 123px;}
.a1{width: 156px; height: 38px; position: absolute; left:20px;top: 233px;}
.a2{width: 156px; height: 38px; position: absolute; left:20px;top: 277px;}
.ewm{
	position: absolute;
	left: -189px;
	top: 59px;
	width: 140px;
	height: 140px;
}
.bg{background:url("images/bg.png"); width: 922px; height: 424px; position: relative; margin: 0 auto 50px auto;}
.bg .t1{
	width: 300px;
	text-align: center;
	font-size: 20px;
	color: #FFCFA4;
	position: absolute;
	right:  -27px;
top: 59px;
transform: rotate(32deg);
-ms-transform:rotate(32deg); 	/* IE 9 */
-moz-transform:rotate(32deg); 	/* Firefox */
-webkit-transform:rotate(32deg); /* Safari 和 Chrome */
-o-transform:rotate(32deg); 	
}
.bg .t2{
	color: #4F240E;
	font-size: 22px;
	width: 670px;
	position: absolute;
	right: 169px;
	top: 56px;
}
.bg .wx{
	position: absolute;
	left: -45px;
	top: -32px;
	background: url("images/wx.png");
	width: 23px;
	height: 21px;
}
.btn1{background:url("images/btn1.png"); width: 289px; height: 120px;}
.btn2{background:url("images/btn2.png"); width: 289px; height: 120px;}
.btn3{background:url("images/btn3.png"); width: 289px; height: 120px;}

.btn4 {
	background: url("images/btn4.png");
	width: 502px;
	height: 128px;
}

.btn5 {
	background: url("images/btn5.png") no-repeat;
	width: 414px;
	height: 120px;
	margin: 70px 0 0 145px;
	display: block;
}
.btn1:hover,.btn2:hover{-webkit-transform: scale(1.05);
    transform: scale(1.05);}
.an1{
	position: absolute;
	left: 260px;
	top: 264px;
}
.an2{
	position: absolute;
	left: 70px;
	top: 264px;
}
.an3{
	position: absolute;
	left: 370px;
	top: 264px;
}
.an4{
	position: absolute;
	left: 313px;
	top: 264px;
}
.an5 {
	position: absolute;
	left: 52px;
	top: -38px;
}
.ico1{
	background: url("images/ico1.png");
	width: 120px;
	height: 51px;
	position: absolute;
	right: 0px;
	top: 303px;
	color: #990D24;
	font-size: 24px;
	text-align:center;
	padding-left: 100px;
	line-height: 54px;
}

.txt4{
	position: absolute;
	left: -308px;
	top: 132px;
	width: 645px;
	font-size: 18px;
	line-height: 33px;
	color: #FFF6E1;
}
.footer{
	text-align: center;
	font-family: "宋体";
	font-size: 12px;
	padding: 20px 0;
	line-height: 25px;
}

.h{background-image:url(images/h.png); position:fixed; left:0px; top:0px; width:100%; height:100%; display: none;}
.tc {
	background-image: url("images/tc.png");
	position: fixed;
	top: 50%;
	left: 50%;
	margin: -222px 0 0 -332px;
	width: 665px;
	height: 445px;
	text-align: center;
	color: #FFCFA4;
}

	.tc .t1 {
		font-size: 68px;
		line-height: 90px;
		padding-top: 20px;
	}

	.tc .t2 {
		font-size: 28px;
	}

	.tc .t3 {
		font-size: 24px;
		color: #4F240E;
		padding-top: 20px;
		line-height: 30px;
	}
.close{
	background: url("images/close.png") center top no-repeat;
	width: 25px;
	height: 25px;
	position: absolute;
	top: -30px;
	right: -30px;
}
