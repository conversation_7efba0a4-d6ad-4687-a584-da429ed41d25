body, html {
	font-size: 14px;
	font-family: "微软雅黑";
	margin: 0;
	padding: 0;
	line-height: 24px;
	color: #333;
	height: 100%; background-color: #EDF1F7;
}
ul, li {
	margin: 0;
	padding: 0;
	list-style: none;
}
img {
	border: 0
}
A:link {COLOR: #fff; TEXT-DECORATION: none;}
A:visited {COLOR: #fff; TEXT-DECORATION: underline}
A:hover,.on{COLOR:#ffff00; TEXT-DECORATION: underline}
A.b:link {COLOR: #e44257; TEXT-DECORATION: underline;}
A.b:visited {COLOR: #333; TEXT-DECORATION: none}
A.b:hover{COLOR:#333; TEXT-DECORATION: none}
A.c:link {COLOR: #333; TEXT-DECORATION: none;}
A.c:visited {COLOR: #000; TEXT-DECORATION: underline}
A.c:hover{COLOR:#000; TEXT-DECORATION: underline}

.h2{background:url("../images/h.png");width: 100%;height: 100%;position: fixed;left: 0px;top: 0px;}
.t-c{text-align: center;}
.dl{width: 485px; height: 535px; position: absolute; left: 50%; top: 50%; margin: -267px 0 0 -242px; background-color: #fff; border-radius: 4px;}
.dl .t1{width: 416px; border-bottom: 1px solid #e6e6e6; text-align: center; margin: 0 auto; padding: 55px 0 0px; height: 40px; font-size: 16px;}
.dl .t1 li{ float: left; height: 20px; line-height: 20px;cursor:pointer}
.dl .t1 .m{margin-left: 105px; border-right: 1px solid #e6e6e6; padding-right: 20px; margin-right: 20px;}
.dl .t1 .on{color: #e44257;}
.dl .t2{width: 348px; margin: 10px auto 0;font-size: 16px;}
.dl .t2 li{padding-top: 20px; position: relative;}
.dl .t2 li img{width: 230px; height: 230px; display: block; margin: 0 auto;}
.dl .t2 input{background-color: #FCFCFC; border: 1px solid #cccccc; padding: 0 20px; line-height: 47px; color: #999999;outline:none; font-size: 16px;}
.dl .t2 .srk1{width: 308px;}
.dl .t2 .srk2{width: 175px;}
.dl .t2 .btn{background-color:#e44257; width: 348px; line-height: 47px; text-align: center; display: block; color: #fff; font-size: 18px;}
.dl .t2 .logo{width:241px; height: 48px;background:url("../images/logo.png"); margin: 30px auto 0;}
.dl .t2 .yzm{width: 133px; height: 47px;background:url("../images/bg2.png");border: 1px solid #cccccc; text-align: center; line-height: 47px; color: #fff; position: absolute; top: 20px; left:215px; }
.dl .dl1{width: 105px; height: 89px;background:url("../images/dl1.png"); position: absolute; top: 0px; right: 0px; border-top-right-radius: 4px;cursor:pointer}
.dl .dl2{width: 105px; height: 89px;background:url("../images/dl2.png"); position: absolute; top: 0px; right: 0px; border-top-right-radius: 4px;cursor:pointer}




