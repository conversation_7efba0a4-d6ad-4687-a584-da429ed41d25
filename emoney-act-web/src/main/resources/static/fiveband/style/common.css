body, html {
	margin: 0;
	padding: 0;
	overflow: hidden;
	height: 100%; background-color: #000; font-family: "微软雅黑";font-size: 12px;
}
img {
	border: 0
}
ul, li {
	margin: 0;
	padding: 0;
	list-style: none;
}
A:link {TEXT-DECORATION: none}
A:visited {TEXT-DECORATION: none}
A:hover {COLOR:#fff; TEXT-DECORATION: none}
.f18{font-size: 18px;}
.main{width: 970px; height: 600px;background:url("../images/bg.png"); margin: 0 auto; position: relative; overflow: hidden;}
.left{
	position: absolute;
	left: 47px;
top: 387px;
	width: 389px; color: #fff; font-size: 20px;
}
.left img{margin-top: 18px;}
.right{
	position: absolute;
	left: 470px;
	top: 286px;
	width: 463px;
}
.right li{background:url("../images/bg1.png"); width:463px; height:50px;margin: 0 0 10px 0; position: relative; line-height: 50px;}
.right .t1{
	position: absolute;
	left: 23px;
	top: 0px;
	color: #fff;
	font-size: 18px;
}
.right .t2{
	position: absolute;
	left: 90px;
	top: 0px;
	color: #4d4d4d;
	font-size: 14px;
}
.txt1{
	position: absolute;
	left: 47px;
	top: 170px;
	width: 131px;
	color: #fff;
	font-size: 20px;
}
.txt2{
	position: absolute;
	left: 467px;
	top: 170px;
	width: 164px;
	color: #fff;
	font-size: 20px;
}
.txt3{
	font-size: 12px;
	color: #4d4d4d;
	line-height: 32px;
	width: 352px;
	position: absolute;
	left: 87px;
top: 214px;
}
.txt4{
	font-size: 12px;
	color: #4d4d4d;
	line-height: 30px;
width: 352px;
position: absolute;
left: 73px;
top: 293px;
}
.btn2{
	width: 80px;
	height: 28px;
	background: url("../images/btn2.png");
	line-height: 28px;
	text-align: center;
	color: #fff;
	font-size: 14px;
	padding-right: 20px;
	position: absolute;
	left: 320px;
	top: 10px;
}
.btn2b{width: 80px;
	height: 28px;
	background: url("../images/btn2.png") 0 -28px;
	line-height: 28px;
	text-align: center;
	color: #fff;
	font-size: 14px;
	padding-right: 20px;
	position: absolute;
	left: 320px;
	top: 10px;}

.bg{background-image:url(../images/bg11.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc1{position: fixed;top:50%; left:50%; margin: -250px 0 0 -307px; width:479px; height:217px;background:url("../images/tc1.png"); display: block; padding: 270px 70px 0 70px; font-size: 16px; color: #999999; text-align: center; line-height: 35px;}
.tc1 .btn{width: 248px; height: 58px; border-radius: 29px;background:url("../images/btn.png"); display: block; text-align: center; line-height: 55px; color: #fff; margin: 25px auto 0; font-size: 26px;}

.bg2{
	position: absolute;
	left: 495px;
	top: 241px;
	width: 449px;
}
.bg2 li{background:url("../images/bg2.png") no-repeat; width:69px; height: 30px; float: left; text-align: center; color: #cccccc; padding: 15px 12px 0 0; margin-right: 3px; font-size: 14px; position: relative;}
.bg2 .on{background-position: -81px 0; color: #8c6239;}
.btn3,.on .btn3{
	width: 101px;
	height: 38px;
	background: url("../images/btn3.png");
	line-height: 22px;
	text-align: center;
	color: #fff;
	font-size: 14px;
	position: absolute;
	left: 23px;
	top: -36px; padding-right: 11px;
}
.on .btn3{
	background: url("../images/btn3.png"); background-position: 0 -38px;
}

.dh:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
