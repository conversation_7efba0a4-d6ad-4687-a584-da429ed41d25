$(function () {
    'use strict';
    var isLogin = $("#hid_isLogin").val();
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val()
    }
    var configData = {
        "actCode": $("#hid_actcode").val(),
        "cmpCode": "ACRenew20240401"
    }

    var thisPage = {
        init: function () {
            this.bindEvents();

            if (isLogin === '1') {
                if (!this.checkPermission(loginUser.pid)) {
                    return false;
                }
                window.utils.pushdatatocmp(loginUser.uid, configData.cmpCode);
            } else {
                $(".bg").show();
                $(".tc").show();
            }
        },
        //绑定事件
        bindEvents: function () {
            //点击支付
            $(".toPay").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }

                var url = thisPage.getPayUrl();

                //客户端
                if (!!window.utils.GetExternal()) {
                    window.utils.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }
            });
            $('.btn3').click(function () {
                $('.btn3').hide();
                $('.sp').show();
            })
            $("#goIM").click(function (){
                window.utils.goIM('小智盈续费领福利活动');
            });
            //关闭
            $(document).on('click', '.close', function () {
                $(".bg").hide();
                $(".tc3").hide();
            });
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=0&businessType=biztyp-xzyxf";

            return url;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return false;
            }
            return true;
        }
    }
    thisPage.init();
});
