$(function () {
    'use strict';
    var isLogin = $("#hid_isLogin").val();
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val()
    }
    var configData = {
        "actCode": $("#hid_actcode").val(),
        "cmpCode": "FIVEA20241008161350",
        "apiUrl":"https://empc.emoney.cn/",
        "lotteryActCode":"*********"
    }
    let bRotate = false;
    var isLottoryFlag = true;
    const angleConfig = {
        "15天使用期": 0,
        "1个月使用期": 55,
        "3个月使用期": 110,
        "三步擒龙": 170,
        "行业掘金": 235,
        "免单": 300
    };
    const Dates =[{ date: '2024-11-11', accounts: ['136****5043', '188****1076'] },
        { date: '2024-11-12', accounts: ['187****8042'] },
        { date: '2024-11-13', accounts: ['188****2123'] },
        { date: '2024-11-15', accounts: ['171****1286'] },
        { date: '2024-11-18', accounts: ['188****5963'] },
        { date: '2024-11-20', accounts: ['162****4084'] },
        { date: '2024-11-22', accounts: ['181****3613'] } ] ;

    var thisPage = {
        init: function () {
            this.bindEvents();
            if (isLogin === '1') {
                if (!this.checkPermission(loginUser.pid)) {
                    return false;
                }
                thisPage.getRaffleTimes(function (data){
                    $("#rft_num").html(data.detail);
                });
                thisPage.getLotteryLog();
                thisPage.getMyPrize();
            } else {
                $(".bg").show();
                $(".tc-login").show();
                $(".tc").hide();
            }
        },
        //绑定事件
        bindEvents: function () {
            $(".dh2").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                var url = thisPage.getPayUrl();

                utils.pushdatatocmp(loginUser.uid,configData.cmpCode);

                //客户端
                if (!!window.utils.GetExternal()) {
                    window.utils.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }
            });
            //dolottery
            $("#btn-lottery").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                const today = new Date();
                const beginDate = new Date("2024-11-11");
                if (today < beginDate){
                    layer.msg("暂未开始，抽奖时间11月11日");
                    return false;
                }

                if(isLottoryFlag){
                    isLottoryFlag = false;
                    //thisPage.rotateFn(null,0,"",8000);

                    thisPage.doLottery(function (data) {
                        if (data.code === 0) {
                            var prizeCode = data.detail;
                            setTimeout(function (){
                                thisPage.getPrizeResult(prizeCode, function (angles,pname) {
                                    thisPage.rotateFn(null,angles,pname,5000);
                                    $("#rft_num").html(parseInt($("#rft_num").html())-1);
                                });
                            },500);
                        } else {
                            layer.msg(data.msg);
                            isLottoryFlag = true;
                        }
                    })
                }
            });
            $(".close").click(function (){
                $(".bg").hide();
                $(".tc").hide();
                thisPage.getMyPrize();
            })
        },
        //get 抽奖次数
        getRaffleTimes:function (callback){
            var url = configData.apiUrl + "Activity2023/PrizeApi/GetRaffleTimes";
            utils.request(
                `${url}?Emapp-Format=Emoney&phoneEncrypt=${loginUser.mobileX}&code=${configData.lotteryActCode}`,'json').
            then(res => {
                callback && callback(res);
            });
        },
        //抽奖
        doLottery:function (callback){
            var url = configData.apiUrl + "Activity2023/PrizeApi/Prize";
            utils.request(
                `${url}?Emapp-Format=Emoney&phoneEncrypt=${loginUser.mobileX}&code=${configData.lotteryActCode}`,'json').
            then(res => {
                callback && callback(res);
            });
        },//获取中奖结果
        getPrizeResult:function (prizeCode,callback){
            var url = configData.apiUrl + "Activity2023/PrizeApi/PrizeResult";
            utils.request(
                `${url}?Emapp-Format=Emoney&phoneEncrypt=${loginUser.mobileX}&code=${prizeCode}`,'json').
            then(res => {
                if(!!res.detail && res.code === 0 ){
                    if (res.detail.status=== -1){
                        layer.msg("未中奖。");
                        isLottoryFlag = true;
                        return;
                    }else if (res.detail.status=== 0){
                        //抽奖中 继续请求
                        thisPage.getPrizeResult(prizeCode);
                        return;
                    }else if (res.detail.status=== 1) {
                        var prizeData = res.detail.prizeData;

                        if (prizeData.length > 0) {
                            var prizeName = prizeData[0].prizeName;
                            var angles = thisPage.getAnglesByPName(prizeName);
                            if (angles!=-1) {
                                callback&&callback(angles,prizeName);
                            } else {
                                layer.msg("未匹配到奖品。");
                                isLottoryFlag = true;
                                return;
                            }
                        } else {
                            layer.msg("抽奖失败，请重试。");
                            isLottoryFlag = true;
                            return;
                        }
                    }else{
                        thisPage.getPrizeResult(prizeCode);
                        return;
                    }
                }else {
                    thisPage.getPrizeResult(prizeCode);
                    return;
                }
            });
        },

        rotateFn:function (awards, angles, pname,duration) {
            bRotate = !bRotate;
            $('#rotate').stopRotate();

            $('#rotate').rotate({
                angle: 0,
                animateTo: angles + 1800,
                duration: duration,
                callback: function () {
                    if(!!pname){
                        bRotate = !bRotate;

                        $(".tc_1").html(`恭喜您获得<br />${pname}`);
                        $(".bg").show();
                        $(".tc").show();
                        isLottoryFlag = true;
                    }else{
                        thisPage.rotateFn(awards,angles,pname,duration);
                    }
                }
            })
        },
        getAnglesByPName:function(pName) {
            const patterns = Object.keys(angleConfig).map(key => new RegExp(key));
            for (let i = 0; i < patterns.length; i++) {
                if (patterns[i].test(pName)) {
                    return angleConfig[patterns[i].source];
                }
            }
            return "-1";
        },
        getLotteryLog:function () {
            var url = configData.apiUrl + "Activity2023/PrizeApi/GetPrizeWinLog";
            var top = 30;
            utils.request(
                `${url}?Emapp-Format=Emoney&phoneEncrypt=${loginUser.mobileX}&code=${configData.lotteryActCode}&top=${top}`, 'json').then(res => {
                var pData = res.detail;
                const today = new Date();
                const month = today.getMonth() + 1;
                const day = today.getDate();
                var nowDate = `2024-${month}-${day}`;
                var acResult = Dates.find(item => item.date === nowDate)
                if (!!acResult && pData.length > 0) {
                    var pInfo = {"account": acResult.accounts[0], "prizeName": "免单"}
                    var idx = Math.round(pData.length / 2);
                    pData.splice(idx, 0, pInfo);

                    if (acResult.accounts.length > 1) {
                        var pInfo = {"account": acResult.accounts[1], "prizeName": "免单"}
                        var idx = Math.round(pData.length / 3);
                        pData.splice(idx, 0, pInfo);
                    }
                }

                if(!!pData){
                    pData.forEach(item => {
                        $("#user_list").append(`<li>恭喜 ${item.account} 用户喜获 <span>${item.prizeName}</span></li>`)
                    })
                }
            });
        },
        getMyPrize:function () {
            var url = configData.apiUrl + "Activity2023/PrizeApi/GetPrizeLog";
            $("#my_list").html("");
            utils.request(
                `${url}?Emapp-Format=Emoney&phoneEncrypt=${loginUser.mobileX}&code=${configData.lotteryActCode}`, 'json').then(res => {
                var pData = res.detail;
                if (!!pData) {
                    pData.forEach(item => {
                        $("#my_list").append(`<li><span>${item.prizeName}</span></li>`)
                    });
                }
            });
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=*********&groupCode=0&businessType=biztyp-xzyxf";
            //var url="https://mobiletest.emoney.cn/appstatic/ymstock/payment-middle/?goodsPid=*********&groupCode=0&businessType=biztyp-xzyxf";
            return url;
        },
        //检查用户是否登录
        checkIsLogin: function () {
            if (isLogin==='0') {
                layer.msg("您还未登录，请在客户端登录后参与活动");
                return false;
            }
            return true;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            if (pid != "*********" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return false;
            }
            return true;
        }
    }
    thisPage.init();
});
