@charset "gb2312";
body {font-size:12px;margin:0;padding:0;line-height:22px;background-color:#A20005; color:#595757;font-family:"微软雅黑";}
.main {width:996px; margin:0 auto; position:relative;}
ul,li {margin:0;padding:0;list-style:none;}
A:link {COLOR: #fff2d4; TEXT-DECORATION: none}
A:visited {COLOR: #fff2d4; TEXT-DECORATION: none}
A:hover {COLOR: #ffff00; TEXT-DECORATION: none}
A.b:link {COLOR: #ff6600; TEXT-DECORATION: none}
A.b:visited {COLOR: #ff6600; TEXT-DECORATION: none}
A.b:hover {COLOR: #333; TEXT-DECORATION: none}
.white {color:#ffffff;}
.red {color:#e73828;font-weight: bold;}
.yellow {color:#ffe8cf; font-weight: bold;}
.black{ color:#000;}
.blue{color:#2ea5de;}
.org{color:#f8b62d;}
.gray{color:#808285;}
td{font-size:12px; height:30px; line-height:30px;}

.dbg1{background:url(../images/index_01.png) center top no-repeat;height:360px;}
.dbg2{background:url(../images/index_02.png) center top no-repeat;height:366px;}
.dbg3{background:url(../images/index_03.png) center top no-repeat;height:579px;}
.dbg4{background:url(../images/index_04.png) center top no-repeat;height:581px;}
.dbg5{background:url(../images/index_05.png) center top no-repeat;height:688px;}
.dbg6{background:url(../images/index_06.png) center top no-repeat;height:557px;}
.dbg7{background:url(../images/index_07.png) center top no-repeat;height:356px;}
.pf{background:url(../images/pf.png) center top no-repeat; width: 100%; height:143px; position: fixed; left: 0px; bottom: 0px;}

.footer{text-align:center; padding:20px 0; font-family:"宋体"; font-size:12px; color: #fff;}
.bg1{
	BACKGROUND: url("../images/bg1.png") left no-repeat;
	width: 177px;
	height: 168px;
	position: absolute;
	left: 216px;
	top: 189px;
}
.btn1{
	BACKGROUND: url(../images/btn1.png) left no-repeat; text-align: center; padding-right: 23px; color: #fff;font-size: 30px;
	font-weight: bold;
	line-height: 86px;
  width: 305px;
	height: 120px;
}
.btn2{
	BACKGROUND: url("../images/btn2.png");
	text-align: center;
	color: #fff;
	font-size: 24px;
	font-weight: bold;
	line-height: 70px;
	width: 255px;
	height: 98px;
	position: absolute;
	left: 700px;
	top: 55px;
}
.an1{
	position: absolute;
	left: 317px;
	top: 424px;
	
}
.an2{
	position: absolute;
	left: 594px;
	top: 464px;
}
.an3{
	position: absolute;
	left: 228px;
	top: 918px; font-size: 30px;
}
.an4{
	position: absolute;
	left: 291px;
	top: 831px;
}
.txt1{
	position: absolute;
	top: 348px;
	left: 35px;
	font-size: 24px;
	width: 336px;
	text-align: center;
	color: #fff;
}
.hdgz{
	position: absolute;
	top: 159px;
	left: 135px;
	font-size: 18px;
	width: 763px;
	color: #ffada4;
	line-height: 27px;
}
.hdgz li{list-style-type:decimal;}
.hdgz2{
	position: absolute;
	top: 112px;
	left: 73px;
	font-size: 18px;
	width: 837px;
	color: #ffada4;
	line-height: 26px;
}
.zp{
	position: absolute;
	top: 65px;
	left: 91px;
	BACKGROUND: url("../images/zp.png");
	width: 348px;
	height: 348px;
}
.zz{
	position: absolute;
	top: 148px;
	left: 170px;
	BACKGROUND: url("../images/zz.png");
	width: 180px;
	height: 180px;
}
.go{
	position: absolute;
	top: 181px;
	left: 205px;
	width: 110px;
	height: 110px;
	cursor: pointer;
	display: flex;
	justify-content: center;
	align-items: center;
}
.rftime{
	position: absolute;
	top: 85px;
	left: 653px;
	width: 200px;
	color: #ffffff;
	font-size: 20px;
	font-weight: bold;
}
.md{
	position: absolute;
	top: 220px;
  left: 543px;
	width: 385px;
	color: #d56948;
	font-size: 14px;
}
.md li{padding: 0 20px; height: 30px; line-height:30px;background-color:#FBF0EC; margin-bottom: 5px; border-radius: 15px;}

.tc{
	width: 459px;
	height: 333px;
	BACKGROUND: url(../images/tc.png);
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}
.tc_1{
	font-size: 32px;
	color: #441b17;
	width: 280px;
	position: absolute;
	top: 165px;
  text-align: center;
  line-height: 40px;
  left: 92px;
  transform: rotate(8deg);
}
.close{
	position: absolute;
	top: 8px;
	right: 2px;
	BACKGROUND: url("../images/close.png");
	width: 30px;
	height: 30px;
}

.dh,.dh2:hover{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
.txt1{
	position: absolute;
	top: 338px;
	left: 28px;
	font-size: 42px;
	width: 144px;
	color: #e83828;
	line-height: 42px;
	text-align: right;
	font: DIN;letter-spacing:-2px;
}

@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}
.bg,.h{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%; display: none;}
.tc-login{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc-login .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc-login .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc-login .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc-login .zj{letter-spacing:16px;}
.tc-login .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 304px;line-height: 45px;}
.tc-login .yzm:hover{background-color: #f4f4f4;}
.tc-login .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc-login .tc-btn1:hover{ background-color: #3689ce;}
.tc-login .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc-login .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}

input{
	background:#fff;
	border: solid 1px #808080;
	width: 330px;
	padding: 0 10px;
	height: 45px;
	line-height: 45px;
	color: #999;
	list-style: none;
	font-family: "微软雅黑";
	font-size: 28px;
	outline: none;
}