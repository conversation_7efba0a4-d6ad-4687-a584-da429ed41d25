$(function () {
    'use strict';
    var Urls = www;
    var isLogin = $("#hid_isLogin").val();
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val(),
        "point":$("#hid_allpoint").val()
    }
    var configData = {
        "actCode": $("#hid_actcode").val(),
        "cmpCode": "ACRenew20240101",
        "type":"20240188",
        "productId":"598"
    }
    var isPage1 = location.href.indexOf("/index010101")>-1?true:false;
    configData.type = location.href.indexOf("/index010101")>-1?"20240188":"202401128"

    var thisPage = {
        init: function () {
            this.bindEvents();

            if (isLogin === '1') {
                if (!this.checkPermission(loginUser.pid)) {
                    return false;
                }
                this.checkPopupShow();
                window.utils.pushdatatocmp(loginUser.uid, configData.cmpCode);
            } else {
                $(".bg").show();
                $(".tc").show();
            }
        },
        //绑定事件
        bindEvents: function () {
            var $this = this;
            $("[name=btn_exchange]").click(function (){
                request(
                    `${Urls}renew588/pointOrderExchange?actcode=${configData.actCode}&productId=${configData.productId}`,'json').
                then(res => {
                    if (res.code === '200') {
                        $(".bg").show();
                        $(".tc4").show();
                    }
                });
            });
            //点击支付
            $(".toPay").click(function (){
                if (!$this.checkPermission(loginUser.pid)) {
                    return false;
                }
                if(!$(".notips").prop("checked")){
                    layer.msg('请先阅读并同意该《信息服务协议》');
                    return false;
                }

                var url = $this.getPayUrl();

                //客户端
                if (!!window.utils.GetExternal()) {
                    window.utils.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }
            });
            $(".pf2").click(function (){
                if (!$this.checkPermission(loginUser.pid)) {
                    return false;
                }
            });
            $(".btn3").click(function () {
                $(".bg").show();
                $(".hdgz").show();
            });
            $(".ys").click(function (){
                $(".bg").show();
                $(".yhxy").show();
                $(".notips").prop('checked',true);
            });
            $(".txt,.notips").click(function (){
                $(".notips").prop('checked',true);
            });

            //关闭
            $(document).on('click', '.ruleClose', function () {
                if($(".tc3").css("display")==='block'){
                    $(".yhxy").hide();
                }else{
                    $(".bg").hide();
                    $(".yhxy").hide();
                }

            });
            //关闭
            $(document).on('click', '.close', function () {
                $(".bg").hide();
                $(".hdgz").hide();
                $(".tc3").hide();
                $(".tc4").hide();
            });
        },
        /*
        查询当天是否弹出过
         */
        checkPopupShow:function () {
            request(
                `${Urls}lottery0808/gettipstatusbyday?actcode=${configData.actCode}&uid=${loginUser.uid}&type=${configData.type}`).
            then(res => {
                console.log(res)
                if (res.code === '200' && res.data) {
                    if (res.data === 'false') {
                        //弹出窗口
                        $(".bg").show();
                        $(".tc3").show();
                    }
                }
            })
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=0&businessType=biztyp-xzyxf";
            if(isPage1){
                url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=1&businessType=biztyp-xzyxf";
            }
            return url;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return false;
            }
            return true;
        }
    }
    thisPage.init();
});

function request (url, dataType) {
    return new Promise((resolve, reject) => {
        $.ajax({
            type: 'get',
            url: url,
            dataType: dataType ? dataType : 'jsonp',
            success: function (data) {
                console.log(data)
                if (data.code === '200') {
                    resolve(data)
                } else {
                    if (url.indexOf("sendpp") > -1) {
                    } else {
                        layer.msg(data.msg)
                    }
                }
            },
            error: function (err) {
                reject(err)
            }
        })
    })
}