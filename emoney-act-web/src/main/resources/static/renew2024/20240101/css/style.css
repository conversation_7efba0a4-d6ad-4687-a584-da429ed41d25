@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#8B0303;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #e42c34; font-weight: bold;}
.white{color: #fff;}
.gray{color: #79440c;}
.org{color: #c54031;}
.yellow{color: #ffe44d;}
.f14{ font-size: 14px;}
.f25{ font-size: 25px;}
.f40{ font-size: 40px;}
.f60{ font-size: 60px; font-weight: bold;}
.f68{ font-size: 68px;}
.f1{text-decoration:underline;}
.img_1{background:url(../images/index_01.jpg) center; height:322px;}
.img_2{background:url(../images/index_02.jpg) center; height:325px;}
.img_3{background:url(../images/index_03.jpg) center; height:224px;}
.img_4{background:url(../images/index_04.jpg) center; height:636px;}
.img_5{background:url(../images/index_05.jpg) center; height:636px;}
.img_6{background:url(../images/index_06.jpg) center; background-size: 1920px 100%; padding: 30px 0;}

.ico1{
	background: url("../images/ico1.png");
	width: 171px;
	height: 35px;
	position: absolute;
	top: 228px;
	left: 421px;
	color: #5b0505!important;
	font-size: 16px;
	text-align: center;
	line-height: 25px;
}
.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff; margin:20px 0 180px;
}
A:link,a:visited {TEXT-DECORATION: none; color: #fff;}
A:hover {TEXT-DECORATION: none}
A.b:link,a.b:visited {TEXT-DECORATION: none; color: #333;}
A.b:hover {TEXT-DECORATION: none}
img{border:none;}
.black{color: #000; font-weight: bold;}

.bott{background:url("../images/pf.png") center;height:198px; position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}
.txt1{
	position: absolute;
	text-align: center;
	left: 147px;
	top: 126px;
	color: #d9362e;
	font-size: 28px;
	width: 440px;
	line-height: 60px;
}
.txt2{
	position: absolute;
	left: -150px;
	top: 61px;
	color: #d9362e;
	font-size: 28px;
	width: 341px;
	line-height: 60px;
	text-align: center;
}
.txt3{
	position: absolute;
	left: 234px;
	top: 190px;
	color: #ffc94e;
	font-size: 18px;
	text-align: center;
	width: 253px;
}
.txt4{
	position: absolute;
	left: 126px;
  top: 376px; font-weight: bold;
	color: #eb0200;
	font-size: 28px; text-align: center; width: 420px;
}
.txt5{
	position: absolute;
	left: -108px;
	top: 127px;
	color: #ffc94e;
	font-size: 18px;
	text-align: center;
	width: 253px;
}
.txt6{
	position: absolute;
	left: 32px;
  top: 297px;
  color: #482a2a;
  font-size: 30px;
  text-align: center;
  width: 465px;
}
.txt7{
	position: absolute;
	left: 27px;
  top: 181px;
  color: #a74e0c;
  font-size: 56px;
  line-height: 60px;
  text-align: center;
  width: 645px;
}
.an1{
	position: absolute;
	left: 228px;
	top: 311px;
	width: 283px;
	font-size: 16px;
}
.an2{
	position: absolute;
	left: 267px;
	top: 146px;
	width: 283px;
	font-size: 16px;
}
.an3{
	position: absolute;
	left: 210px;
  top: 643px;
	width: 283px;
	font-size: 16px;
}
.btn1{
	background: url("../images/btn1.png");
	width: 358px;
	height: 70px;
	position: absolute;
	top: 251px;
	left: 191px;
}
.btn2{
	background: url("../images/btn2.png");
	width: 289px;color: #5b0505!important; text-align: center; font-size: 14px;box-sizing: border-box; padding: 0 0 0 147px; line-height: 25px;
	height: 93px;
	position: absolute;
	top: 43px;
	left: 260px;
}


.yhxy{
	width: 900px;
	height:660px;
	position:absolute;
	left: 50%;
	top: 50%; margin: -350px 0 0 -450px; background-color: #fff; border-radius: 10px; padding: 20px 0 20px 20px; line-height: 24px; display: none;
}
.nr{width: 880px;height:660px; overflow-y: auto; color:#626262; font-size: 16px; padding-right: 20px;}
.bg,.h{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
.tc input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}
.mar{
	position: absolute;
	left: -252px;
	top: 386px;
	width: 771px;
	font-size: 18px;
	color: #fff;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

.hdgz{width: 900px;
margin:0 auto;
color: #fff;
font-size: 20px; line-height: 30px;}
.hdgz li{list-style-type:decimal;}
.tc4{
	position: fixed;
	top: 50%;
	left: 50%;margin: -270px 0 0 -264px;background: url("../images/tc2.png"); width: 528px; height:540px;display: none;
}
.tc3{
	position: fixed;
	top: 50%;
	left: 50%;margin: -350px 0 0 -353px;background: url("../images/tc1.png"); width: 701px; height: 706px;
}
.tc3 ul{position: absolute; left: 74px;  top: 276px;}
.tc3 li{width: 175px; height: 150px; border-radius: 20px; background-color: #F95A42; float: left; margin-right: 15px; text-align: center; color: #fff; font-size: 40px; line-height:50px; padding-top: 45px;}
.tc-btn{background: url("../images/tc-btn.png");
	width:406px;
	height:113px; position: absolute;left: 182px;color: #5b0505!important; text-align: center; font-size:20px;box-sizing: border-box;padding: 0 0 0 206px;
	line-height: 34px;
	top: 540px;}
.tc-btnb{background: url("../images/tc-btnb.png");
	width:406px;
	height:113px; position: absolute;left: 182px;color: #5b0505!important; text-align: center; font-size:20px;box-sizing: border-box;padding: 0 0 0 206px;
	line-height: 34px;
	top: 540px;}
.tc4 .tc-btn2{background: url("../images/tc-btn2.png");
    width:338px;
    height:92px; position: absolute;left: 89px;
  top: 398px;}
.an3{
	position: absolute;
	left: 210px;
	top: 665px;
	width: 283px;
	font-size: 16px;
}
.an4{
	position: absolute;
	left: 120px;
	top: 502px;
	width: 283px;
	font-size: 16px;
}
.server{
    background: url("../images/server.png");
    width: 120px;
    height:120px;
    position:fixed;
    top: 330px;
    right: 0px;
}



.dh2{
  -webkit-animation: dh2 2s linear infinite alternate;
  animation-name: dh2 2s linear infinite alternate;
}
@-webkit-keyframes dh2{
0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
40%{-webkit-transform:translateY(-30px)}
60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes dh2{
0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
40%{-moz-transform:translateY(-30px)}
60%{-moz-transform:translateY(-15px)}
}

.close,.ruleClose{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -25px;
	top: -25px;
	width: 30px;
	height: 30px;
}

input[type=checkbox]{
	width: 35px;
	height: 30px;
	float: left;
}

.ys{
	line-height: 30px;
	float: left;
}