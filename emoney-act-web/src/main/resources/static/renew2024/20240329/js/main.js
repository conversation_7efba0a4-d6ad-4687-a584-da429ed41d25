$(function () {
    'use strict';
    var Urls = www;
    var isLogin = $("#hid_isLogin").val();
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val()
    }
    var configData = {
        "actCode": $("#hid_actcode").val(),
        "cmpCode": "ACRenew20240401",
        "privilegeCode":"PAC1240320135221315",
    }

    var thisPage = {
        init: function () {
            this.bindEvents();

            if (isLogin === '1') {
                if (!this.checkPermission(loginUser.pid)) {
                    return false;
                }
                this.isSubmit();
                window.utils.pushdatatocmp(loginUser.uid, configData.cmpCode);
            } else {
                $(".bg").show();
                $(".tc").show();
            }
        },
        //绑定事件
        bindEvents: function () {
            $("#btn_getReward").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                thisPage.addQgn(function (){
                    $(this).hide();
                    $(".btn3h").show();
                    $(".bg").show();
                    $(".tc3").show();
                });
            });
            //点击支付
            $(".toPay").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }

                var url = thisPage.getPayUrl();

                //客户端
                if (!!window.utils.GetExternal()) {
                    window.utils.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }
            });
            $("#goIM").click(function (){
                window.utils.goIM('小智盈续费领福利活动');
            });
            //关闭
            $(document).on('click', '.close', function () {
                $(".bg").hide();
                $(".tc3").hide();
            });
        },
        isSubmit:function (){
            if (!thisPage.checkPermission(loginUser.pid)) {
                return false;
            }
            $.ajax({
                type: 'get',
                url: Urls + "/user/issubmitbyactcodes?actcodes=" + configData.actCode,
                dataType: 'jsonp',
                data: {
                    uid: loginUser.uid
                },
                success: function (data) {
                    if (data.code == '200') {
                        var num = "";
                        if (data.data) {
                            num = data.data.split(',')
                        }
                        if (num.length>0 && !!num[0]) {
                            $(".btn3").hide();
                            $(".btn3h").show();
                        }
                    }
                }
            });
        },
        //送功能
        addQgn: function (callback) {
            $.ajax({
                type: 'get',
                url: Urls + 'qgn/sendprivilege_qgn',
                dataType: 'json',
                data: {
                    "actcode": configData.actCode,
                    "actType": 2,
                    "activityID": configData.privilegeCode,
                    "reason": "小智盈续费4月活动"
                },
                success: function (data) {
                    if (data.code == "200") {
                        callback && callback();
                    }else{
                        layer.msg(data.message);
                    }
                }
            });
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=0&businessType=biztyp-xzyxf";

            return url;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return false;
            }
            return true;
        }
    }
    thisPage.init();
});
