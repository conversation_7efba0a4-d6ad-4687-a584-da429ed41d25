@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#F6C6D6;font-family: "微软雅黑";}
ul,li{list-style: none;}
.red2{color: #e42c34; font-weight: bold;}
.white{color: #fff;}
.gray{color: #79440c;}
.org{color: #c54031;}
.f40{ font-size: 40px;}
.f60{ font-size: 60px;}
.f68{ font-size: 68px;}
.f1{text-decoration:underline;}
.img_1{background:url(../images/index_01.jpg) center; height:429px;}
.img_2{background:url(../images/index_02.jpg) center; height:531px;}
.img_3{background:url(../images/index_03.jpg) center; height: 1037px;}
.img_4{background:url(../images/index_04.jpg) center; height: 632px;}
.img_5{background:url(../images/index_05.jpg) center; height: 459px; margin-bottom: 179px;}

.t1{float: left; font-size: 16px; color: #fff; width: 110px; padding: 8px 0 0 8px;}
.t2{float: left; font-size: 12px; color: #fe8172; width: 110px; padding: 0px 0 0 8px;}
.img_3 a:hover .t1,.img_3 a:hover .t2{color: #e92f1a;}
.swf1{
width: 860px;
height: 510px;
  margin: 170px auto 45px; display: block;
}
.txt1{
	position: absolute;
	left: -327px;
	top: 22px;
	width: 799px;
	font-size: 24px;
	color: #fff;
}
.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #63202e;
	position: absolute;
	left: -253px;
	top: 330px;
	width: 600px;
}
A:link,a:visited {TEXT-DECORATION: none;}
A:hover {TEXT-DECORATION: none}
img{border:none;}
.black{color: #000; font-weight: bold;}

.bott{background:url("../images/pf.png") center;height:189px; position:fixed; bottom:0; width:100%;}
.main{width:100px; margin:0 auto; position:relative;}

.an1{
	position: absolute;
	left: 118px;
	top: 293px;
	width: 423px;
	font-size: 18px;
}

.btn1{
	background: url("../images/btn1.png");
	width: 528px;
	height: 165px;
	position: absolute;
	top: 361px;
	left: -185px;
}
.btn2{
	background: url("../images/btn2.png");
	width: 434px;
	height: 149px;
	position: absolute;
	top: 45px;
  left: 186px;
}
.btn3,.btn3h{
	background: url("../images/btn3.png");
	width: 246px;
	height: 91px;
	position: absolute;
	top: 226px;
	left: -427px;
}
.btn3h{
	background: url("../images/btn3h.png");}
.btn4{
	background: url("../images/btn4.png");
	width: 528px;
	height: 165px;
	position: absolute;
	top: 848px;
	left: -209px;
}
.btn5{
	background: url("../images/btn5.png");
	width: 528px;
	height: 165px;
	position: absolute;
	top: 459px;
	left: -209px;
}

.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}
.mar{
	position: absolute;
	left: -252px;
	top: 348px;
	width: 771px;
	font-size: 18px;
	color: #fff;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

.hdgz{
	width: 877px;
	position: absolute;
	left: -380px;
	top: 70px;
	color: #63202e;
	font-size: 20px;
	line-height: 40px;
}
.hdgz li{list-style-type:decimal;}

.tc3{
	position: fixed;
	top: 50%;
	left: 50%;background: url("../images/tc1.png"); width: 602px; height: 623px; margin: -301px 0 0 -311px;
}
.tc-btn{background: url("../images/tc-btn.png");
    width:396px;
    height:124px; position: absolute;left: 99px;
  top: 404px;}

.pf2{
    background: url("../images/pf2.png");
    width: 170px;
    height:198px;
    position:fixed;
    top: 330px;
    right: 0px;
}
.server{
    background: url("../images/server.png");
    width: 120px;
    height:120px;
    position:fixed;
    top: 330px;
    right: 0px;
}



.dh2{
  -webkit-animation: dh2 2s linear infinite alternate;
  animation-name: dh2 2s linear infinite alternate;
}
@-webkit-keyframes dh2{
0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
40%{-webkit-transform:translateY(-30px)}
60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes dh2{
0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
40%{-moz-transform:translateY(-30px)}
60%{-moz-transform:translateY(-15px)}
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -25px;
	top: -25px;
	width: 30px;
	height: 30px;
}
