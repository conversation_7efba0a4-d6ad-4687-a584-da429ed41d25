@charset "utf-8";
/* CSS Document */

*{margin:0; padding:0;}
body{background-color:#CA1800;font-family: "微软雅黑";background:url(../images/index_08.jpg) center;}
ul,li{list-style: none;}
.red{color: #dd3226;}
.white{color: #fff;}
.gray{color: #79440c;}
.org{color: #c54031;}
.f28{ font-size:35px; margin-bottom: 10px; padding-bottom: 10px; text-align: center; border-bottom: 2px solid #f43d47;}
.f60{ font-size: 60px;}
.f1{text-decoration:underline;}
.img_1{background:url(../images/index_01.jpg) center; height:255px;}
.img_2{background:url(../images/index_02.jpg) center; height:255px;}
.img_3{background:url(../images/index_03.jpg) center; height: 255px;}
.img_4{background:url(../images/index_04.jpg) center; height: 195px;}
.img_5{background:url(../images/index_05.jpg) center; height: 581px;}
.img_6{background:url(../images/index_06.jpg) center; height: 620px;}
.img_7{background:url(../images/index_07.jpg) center; height: 979px;}
.img_8{height: 498px;}

.t1{float: left; font-size: 16px; color: #fff; width: 110px; padding: 8px 0 0 8px;}
.t2{float: left; font-size: 12px; color: #fe8172; width: 110px; padding: 0px 0 0 8px;}
.img_3 a:hover .t1,.img_3 a:hover .t2{color: #e92f1a;}
.swf1{
width: 860px;
height: 510px;
  margin: 170px auto 45px; display: block;
}
.txt1{
	position: absolute;
	left: -302px;
	top: 90px;
	width: 849px;
	font-size: 16px;
	color: #fff;
}
.txt2{
	position: absolute;
	left: 81px;
	top: -244px;
	font-weight: bold;
	color: #dd3226;
	font-size: 190px;
	line-height: 190px;
	font-family:Impact, Haettenschweiler, "Franklin Gothic Bold", "Arial Black", "sans-serif";
}
.txt3{
	position: absolute;
	left: 20px;
	top: 44px;
	font-weight: bold;
	color: #3b3b3b;
	font-size: 21px;
	width: 165px;
	text-align: center;
	line-height: 25px;
}
.txt4{
	position: absolute;
	left: 22px;
	top: -258px;
	color: #fff;
	font-size: 21px;
	width: 30px;
	text-align: center;
	line-height: 24px;
}
.sp{
	position: absolute;
	left: -420px;
	top: 162px;
	width: 951px;
	height: 489px; border-radius: 10px;
}
.ico{
	background: url("../images/ico.png");
	width: 382px;
	height: 59px;
	position: absolute;
	top: 25px;
	left: -142px;
}
.ico2{
	background: url("../images/ico2.png");
	width: 97px;
	height: 35px;
	position: absolute;
	top: -135px;
	left: -150px;
	text-align: center;
	color: #fff;
	font-size: 17px;
	line-height: 25px;
}
.djs{
	position: absolute;
	left: -337px;
	top: 17px;
	width: 834px;
	height: 66px;
	line-height: 36px;
	color: #c04733;
	font-size: 24px;
	font-weight: bold;
	background-image: url("../images/djs.png"); text-align: center; display: none;
}
.t{
	position: absolute;
	left: 476px;
	top: 12px;
	width: 63px;
}
.s{
	position: absolute;
	left: 545px;
	top: 12px;
	width: 63px;
}
.f{
	position: absolute;
	left: 620px;
	top: 12px;
	width: 63px;
}
.m{
	position: absolute;
	left: 694px;
	top: 12px;
	width: 63px;
}

.footer{
	text-align: center;
	font-family: "微软雅黑";
	font-size: 20px;
	line-height: 40px;
	color: #fff;margin-bottom: 140px;
}
A:link,a:visited {TEXT-DECORATION: none; text-decoration: underline;}
A:hover {TEXT-DECORATION: none;}
img{border:none;}
.black{color: #000; font-weight: bold;}

.bott{background:url("../images/pf.png") center;height:120px; position:fixed; bottom:0; width:100%; display: none;}
.main{ width: 100px;
	margin: 0 auto;
	position: relative;
}



.btn1{
	background: url("../images/btn1.png");
	width: 541px;
	height: 161px;
	position: absolute;
	top: -75px;
	left: -218px;
}
.btn2{
	background: url("../images/btn2.png");
	width: 334px;
	height: 163px;
	position: absolute;
	top: -28px;
  left: 236px;
}
.btn3{
	background: url("../images/btn3.png");
	width: 570px;
	height: 196px;
	position: absolute;
	top: -110px;
	left: -233px;
}
.btn4{
	background: url("../images/btn4.png");
	width: 564px;
	height: 196px;
}
.an1{
	position: absolute;
	top: 387px;
	left: -227px;
}
.an2{
	position: absolute;
	top: 410px;
	left: -227px;
}
.an3{
	position: absolute;
	top: -19px;
	left: -227px;
}
.al{
	position: absolute;
	left: -371px;
	top: 406px;
	width: 840px;
}
.slider_box{ margin: 0px auto; width:840px; height: 540px; position: relative;overflow:hidden; border-bottom-right-radius: 10px;border-bottom-left-radius: 10px;}
.silder_con{ height:482px; overflow: hidden; position: absolute; color: #c30d23; font-size: 28px; line-height: 50px;}
.silder_panel{width: 840px; height: 482px;overflow: hidden; float: left; position: relative; text-align:center;}
.silder_nav {height: 42px; position: absolute; bottom: 0px; left: 387px;}
.silder_nav ul{width: 1000px;}
.silder_nav li {float: left;cursor:pointer;width:15px; height:15px; overflow: hidden; display: block; background-color:#E63E3E; border-radius: 50%; margin-right: 10px;}
.silder_nav li.current { background-color: #E63E3E;}
.silder_nav li:last-child{margin: 0px;}

.bg{background-image:url(../images/bg.png); position:fixed; left:0px; top:0px; width:100%; height:100%;}
.tc{position: fixed;top:50%; left:50%; width: 900px; height: 500px; margin: -250px 0 0 -450px; background-color: #fff; border-radius: 20px; color: #000;}
.tc .bt{line-height: 80px; border-bottom: 1px solid #dcdcdc; font-weight: bold; font-size: 40px; text-align: center;}
.tc .txt{padding: 25px 0 0 195px;font-size: 20px;}
.tc .red{font-size: 26px;padding: 15px 0;color: #d43839;}
.tc .zj{letter-spacing:16px;}
.tc .yzm{border: solid 1px #808080;border-radius: 40px;font-size: 28px;text-align: center;color: #999;width: 190px;height: 45px;position: absolute;left: 640px;top: 331px;line-height: 45px;}
.tc .yzm:hover{background-color: #f4f4f4;}
.tc .tc-btn1{width: 170px; height: 63px; background-color: #2774bc; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 80px; color: #fff; border-radius: 10px; float: left;}
.tc .tc-btn1:hover{ background-color: #3689ce;}
.tc .tc-btn2{width: 170px; height: 63px; background-color: #e6e6e6; text-align: center; line-height: 63px; display: block; margin: 40px 0 0 60px; color: #000; border-radius: 10px;float: left;}
.tc .tc-btn2:hover{ background-color: #cecece;}
.tc2{position: fixed;top:50%; left:50%; width: 760px; height: 190px; margin: -95px 0 0 -380px; background-color: #fff; border-radius: 20px; color: #000; text-align: center; line-height: 190px; display: none; font-size: 40px;}
.tc3{
	position: fixed;
	top: 50%;
	left: 50%;background: url("../images/tc.png"); width: 428px; height: 561px; margin: -330px 0 0 -214px;
}
.tc-btn{background: url("../images/tc-btn.png");
    width:478px;
    height:172px; position: absolute;left: -21px;
  top: 544px;}
input{
        background:#fff;
        border: solid 1px #808080;
        width: 330px;
        padding: 0 10px;
        height: 45px;
        line-height: 45px;
        color: #999;
        list-style: none;
        font-family: "微软雅黑";
        font-size: 28px;
        outline: none;
}
.mar{
	position: absolute;
	left: -252px;
	top: 348px;
	width: 771px;
	font-size: 18px;
	color: #fff;
}

.dh{
  -webkit-animation: dh 0.3s linear infinite alternate;
  animation-name: dh 0.3s linear infinite alternate;
}
@-webkit-keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes dh{
  to {
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

.hdgz{
	width: 877px;
	position: absolute;
	left: -384px;
	top: 173px;
	color: #fff;
	font-size: 18px;
	line-height: 32px;
}
.hdgz li{list-style-type:decimal;}

.server{
    background: url("../images/server.png");
    width: 120px;
    height:120px;
    position:fixed;
    top: 330px;
    right: 0px;
}



.dh2{
  -webkit-animation: dh2 2s linear infinite alternate;
  animation-name: dh2 2s linear infinite alternate;
}
@-webkit-keyframes dh2{
0%,20%,50%,80%,100%{-webkit-transform:translateY(0)}
40%{-webkit-transform:translateY(-30px)}
60%{-webkit-transform:translateY(-15px)}
}
@-moz-keyframes dh2{
0%,20%,50%,80%,100%{-moz-transform:translateY(0)}
40%{-moz-transform:translateY(-30px)}
60%{-moz-transform:translateY(-15px)}
}

.close{
	background: url("../images/close.png") center top no-repeat;
	position: absolute;
	right: -25px;
	top: -25px;
	width: 30px;
	height: 30px;
}
