$(function () {
    'use strict';
    var Urls = www;
    var isLogin = $("#hid_isLogin").val();
    var loginUser = {
        "uid": $("#hid_uid").val(),
        "pid": $("#hid_pid").val(),
        "mobileX": $("#hid_mobilex").val(),
        "maskMobile": $("#hid_maskmobile").val()
    }
    var configData = {
        "actCode": $("#hid_actcode").val(),
        "cmpCode": "ACRenew20240506"
    }

    var thisPage = {
        init: function () {
            this.bindEvents();
            this.scorllWin();

            if (isLogin === '1') {
                if (!this.checkPermission(loginUser.pid)) {
                    return false;
                }
                window.utils.pushdatatocmp(loginUser.uid, configData.cmpCode);

                thisPage.isSubmit(function (){
                    thisPage.showDoneStatus();
                });
            } else {
                $(".bg").show();
                $(".tc").show();
            }
        },
        scorllWin:function (){
            $(window).scroll(function(e){
                if($(window).scrollTop() >= 840){
                    $('.bott').fadeIn(300);
                }else{
                    $('.bott').fadeOut(300);
                }
            })
        },
        //绑定事件
        bindEvents: function () {
            //点击支付
            $(".toPay").click(function (){
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }

                var url = thisPage.getPayUrl();

                //客户端
                if (!!window.utils.GetExternal()) {
                    window.utils.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + url);
                } else {
                    url += "&phoneEncrypt=" + loginUser.mobileX + "&UPcid=" + loginUser.uid;
                    $(this).attr("target", "_blank");
                    $(this).attr("href", url);
                }
            });
            //领取福袋
            $('.btn1').click(function () {
                if (!thisPage.checkPermission(loginUser.pid)) {
                    return false;
                }
                $.ajax({
                    type: 'get',
                    url: Urls + 'renew2024/sendLuckyBag',
                    dataType: 'json',
                    data: {
                        "actCode": configData.actCode
                    },
                    success: function (data) {
                        if (data.code == "200") {
                            thisPage.showDoneStatus();
                            $(".bg").show();
                            $(".tc3").show();
                        }else{
                            layer.msg(data.message);
                        }
                    }
                });
            })
            $("#goIM").click(function (){
                window.utils.goIM('小智盈续费领福利活动');
            });
            //关闭
            $(document).on('click', '.close', function () {
                $(".bg").hide();
                $(".tc3").hide();
            });
        },
        showDoneStatus:function (){
            $('.btn1').hide();
            $('.btn3').show();

            $("#cont1").hide();
            $("#cont2").show();
        },
        //获取支付链接
        getPayUrl: function () {
            var url = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=0&businessType=biztyp-xzyxf";

            return url;
        },
        //检查用户是否有权限参与
        checkPermission: function (pid) {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return false;
            }
            return true;
        },
        //是否领取过
        isSubmit:function (callback){
            if (!thisPage.checkPermission(loginUser.pid)) {
                return false;
            }
            $.ajax({
                type: 'get',
                url: Urls + "/user/issubmitbyactcodes?actcodes=" + configData.actCode,
                dataType: 'jsonp',
                data: {
                    uid: loginUser.uid
                },
                success: function (data) {
                    if (data.code == '200') {
                        var num = "";
                        if (data.data) {
                            num = data.data.split(',')
                        }
                        if (num.length>0 && !!num[0]) {
                            callback && callback();
                        }
                    }
                }
            });
        }
    }
    thisPage.init();
});
