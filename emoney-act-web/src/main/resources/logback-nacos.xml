<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false" scanPeriod="1 seconds">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <springProperty scope="context" name="springAppName" source="spring.application.name"/>
    <springProperty scope="context" name="logUrl" source="logstash.url"/>
    <springProperty scope="context" name="mailHost" source="spring.mail.host"/>
    <springProperty scope="context" name="mailPort" source="spring.mail.port"/>
    <springProperty scope="context" name="mailUserName" source="spring.mail.username"/>
    <springProperty scope="context" name="mailPassword" source="spring.mail.password"/>
    <springProperty scope="context" name="mailAddress" source="mail.toMail.addr"/>
    <contextName>logback</contextName>
    <property name="log.path" value="/data/applogs/actweb/"/>
    <!--0. 日志格式和颜色渲染 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />

    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>
            <!-- 设置字符集 -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 按照每天生成日志文件 -->
    <appender name="filelog"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>${log.path}/${springAppName}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- each file should be at most 300MB, keep 2 days worth of history-->
            <maxFileSize>300MB</maxFileSize>
            <!--历史文件保留个数-->
            <maxHistory>1</maxHistory>
            <!--日志文件最大量 5G-->
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset> <!-- 设置字符集 -->
        </encoder>
    </appender>

    <!-- 异步输出 -->
    <appender name ="asyncFile" class= "ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
        <discardingThreshold >0</discardingThreshold>
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>512</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref ="filelog"/>
    </appender>

    <appender name="logstash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <destination>${logUrl}</destination>
        <!-- encoder必须配置,有多种可选 -->
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
            <customFields>{"appname":"activityweblog"}</customFields>
            <includeContext>false</includeContext>
        </encoder>
        <connectionStrategy>
            <roundRobin>
                <connectionTTL>5 minutes</connectionTTL>
            </roundRobin>
        </connectionStrategy>
    </appender>

    <!-- 输出异常日志到EMail -->
    <appender name="mail" class="ch.qos.logback.classic.net.SMTPAppender">
        <smtpHost>${mailHost}</smtpHost>
        <smtpPort>${mailPort}</smtpPort>
        <SSL>true</SSL>
        <to>${mailAddress}</to>
        <from>${mailUserName}</from>
        <username>${mailUserName}</username>
        <password>${mailPassword}</password>
        <subject>emoney-activityweb报警邮件</subject>
        <evaluator class="ch.qos.logback.classic.boolex.OnErrorEvaluator"></evaluator>
        <layout class="ch.qos.logback.classic.html.HTMLLayout">
            <pattern>%date%level%thread%logger{0}%line%message</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <logger name="org.springframework.kafka.listener.KafkaMessageListenerContainer" level="error" />
    <logger name="org.apache.kafka.clients.NetworkClient" level="off" />

    <springProfile name="dev">
        <root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="filelog"/>
        </root>
    </springProfile>

    <springProfile name="test">
        <root level="INFO">
            <appender-ref ref="console"/>
            <appender-ref ref="filelog"/>
            <appender-ref ref="logstash"/>
        </root>
        <logger name="cn.emoney" level="INFO" additivity="false">
            <appender-ref ref="filelog"/>
            <appender-ref ref="logstash"/>
        </logger>
    </springProfile>

    <springProfile name="uat">
        <root level="INFO">
            <appender-ref ref="console" />
            <appender-ref ref="filelog"/>
            <appender-ref ref="logstash"/>
        </root>
        <logger name="cn.emoney" level="INFO" additivity="false">
            <appender-ref ref="filelog"/>
            <appender-ref ref="logstash"/>
        </logger>
    </springProfile>
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="console" />
            <appender-ref ref="filelog"/>
            <appender-ref ref="logstash"/>
        </root>
        <logger name="cn.emoney" level="INFO" additivity="false">
            <appender-ref ref="filelog"/>
            <appender-ref ref="logstash"/>
        </logger>
    </springProfile>
</configuration>