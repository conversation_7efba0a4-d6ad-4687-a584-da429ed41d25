<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <title>益盟操盘手智盈</title>
    <link href="static/noviceguide/css/common.css" rel="stylesheet" type="text/css">
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script src="static/noviceguide/js/z1920.js"></script>
</head>
<body>
<div class="main">
    <div class="p1">
        <a href="javascript:void(0)" class="an1" clickkey="zyxszy-no1-start" th:clickdata="${uid}"></a>
        <a href="javascript:void(0)" class="btn2 hide" clickkey="zyxszy-no1-no" th:clickdata="${uid}">不，谢谢></a>
    </div>
    <div class="p2">
        <a href="javascript:void(0)" class="btn an2" clickkey="zyxszy-no2-know" th:clickdata="${uid}">我知道了</a>
    </div>
    <div class="p3">
        <a href="javascript:void(0)" class="btn an3" clickkey="zyxszy-no3-know" th:clickdata="${uid}">我知道了</a>
    </div>
    <div class="p4">
        <a href="javascript:void(0)" class="btn an4" clickkey="zyxszy-no4-know" th:clickdata="${uid}">我知道了</a>
    </div>
    <div class="p5">
        <img th:src="${qr}" alt="">
        <a href="javascript:void(0)" class="btn an5" clickkey="zyxszy-no5-know" th:clickdata="${uid}">我知道了</a>
    </div>
    <div class="p6">
        <a href="javascript:void(0)" class="btn an6" clickkey="zyxszy-no6-know" th:clickdata="${uid}">我知道了</a>
    </div>
    <div class="p7">
        <div class="p7b">
            <a href="javascript:void(0)" class="btn an7" clickkey="zyxszy-no7-know" th:clickdata="${uid}">我知道了</a>
        </div>
    </div>
    <div class="p8">
        <a href="javascript:void(0)" class="btn an8" clickkey="zyxszy-no8-know" th:clickdata="${uid}">我知道了</a>
    </div>
    <div class="p9">
        <a href="javascript:void(0)" class="btn an9" clickkey="zyxszy-no9-know" th:clickdata="${uid}">我知道了</a>
    </div>
    <div class="p11">
        <img th:src="${qr}" alt="">
        <a href="javascript:void(0)" class="close hide"></a>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function () {
        $(".an1").click(function () {
            $(".p1").hide(300);
            $(".p2").show(300);
        });
        $(".an2").click(function () {
            $(".p2").hide(300);
            $(".p3").show(300);
        });
        $(".an3").click(function () {
            $(".p3").hide(300);
            $(".p4").show(300);
        });
        $(".an4").click(function () {
            $(".p4").hide(300);
            $(".p5").show(300);
        });
        $(".an5").click(function () {
            $(".p5").hide(300);
            $(".p6").show(300);
        });
        $(".an6").click(function () {
            $(".p6").hide(300);
            $(".p7").show(300);
        });
        $(".an7").click(function () {
            $(".p7").hide(300);
            $(".p8").show(300);
        });
        $(".an8").click(function () {
            $(".p8").hide(300);
            $(".p9").show(300);
        });
        $(".an9").click(function () {
            $(".p9").hide(300);
            $(".p11").show(300);
        });
    });
</script>
<script th:inline="javascript">

    setTimeout(function () { openWindow(); }, 100);

    //获取客户端接口
    function GetExternal() {
        return window.external.EmObj
    };

    //调用客户端接口
    function PC_JH(type, c) {
        try {
            var obj = GetExternal();
            return obj.EmFunc(type, c);

        } catch (e) { }
    };

    //页面加载完成
    function LoadComplete() {
        try {
            PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
        } catch (ex) {

        }
    };

    //页面隐藏
    function EM_FUNC_HIDE() { try { PC_JH("EM_FUNC_HIDE", ""); } catch (ex) { } };

    //功能：最大化窗口 //参数：无
    function EM_FUNC_MAXIMIZE() { try { PC_JH("EM_FUNC_WND_MAXIMIZE", "hidden"); } catch (ex) { } }

    //功能：显示窗口 //参数：无
    function EM_FUNC_SHOW() { try { PC_JH("EM_FUNC_SHOW", "topmost"); } catch (ex) { } }

    //功能：关闭窗口 //参数：无
    function EM_FUNC_CLOSE() { try { PC_JH("EM_FUNC_HIDE", ""); } catch (ex) { } }
    //打开窗口
    function openWindow() {
        LoadComplete();
        EM_FUNC_MAXIMIZE()
        var isShow =/*[[${isShow}]]*/ "";
        if (isShow !== "") {
            EM_FUNC_SHOW();
        }else {
            closeWindow();
        }
    }
    function closeWindow() {
        EM_FUNC_CLOSE();
    }

    $(".hide").click(function () {
        closeWindow();
    });
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=77A5BDA6CBF442B804A76A90AD48E62F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "noviceguide";//模块名称(焦点图2)
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>