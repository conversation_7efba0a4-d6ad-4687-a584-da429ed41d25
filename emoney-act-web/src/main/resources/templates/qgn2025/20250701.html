<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="来听课 限时抢福利 更多福利等待解锁......" />
    <link th:href="@{${staticPath}+'static/qgn/20250701/style/css.css?r=20250716'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var phoneWidth = parseInt(window.screen.width);
        var phoneScale = phoneWidth/750;
        var ua = navigator.userAgent;
        if (/Android (\d+\.\d+)/.test(ua)){
            var version = parseFloat(RegExp.$1);
            if(version>2.3){
                document.write('<meta name="viewport" content="width=750, minimum-scale ='+phoneScale+', maximum-scale ='+phoneScale+', target-densitydpi=device-dpi">');
            }else{
                document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
            }
        } else {
            document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
        }

        function isMobileDevice() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        var www="../";
        // 封装与外部对象交互的函数
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj = GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {
                console.error('PC_JH error:', e);
                return null;
            }
        }

        function LoadComplete() {
            try {
                PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
            } catch (ex) {
                console.error('LoadComplete error:', ex);
            }
        }

        function EM_FUNC_HIDE() {
            try {
                PC_JH("EM_FUNC_HIDE", "");
            } catch (ex) {
                console.error('EM_FUNC_HIDE error:', ex);
            }
        }

        function EM_FUNC_SHOW() {
            try {
                PC_JH("EM_FUNC_SHOW", "");
            } catch (ex) {
                console.error('EM_FUNC_SHOW error:', ex);
            }
        }

        function IsShow() {
            try {
                return PC_JH("EM_FUNC_WND_ISSHOW", "");
            } catch (ex) {
                console.error('IsShow error:', ex);
                return "0";
            }
        }

        function openWindow() {
            LoadComplete();
            PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
            if (IsShow() != "1") {
                EM_FUNC_SHOW();
            }
        }

        // 初始化页面时打开窗口
        $(document).ready(function () {
            openWindow();
        });

    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2">
    <div class="main"><div class="btnh an1"></div><div class="btnh an2"></div><div class="btnh an3"></div><div class="btnh an4"></div>
        <a href="https://www.emoney.cn/dianjin/bb/nianzhongzhutibaogao.pdf" target="_blank" class="btn an1 dh" clickkey="btn1"></a>
        <a href="https://www.emoney.cn/dianjin/bb/sandalongtouzhanfamiji.pdf" target="_blank" class="btn an2 dh" clickkey="btn2"></a>
        <a href="https://www.emoney.cn/dianjin/bb/fangleizhinan.pdf" target="_blank" class="btn an3 dh" clickkey="btn3"></a>
        <a href="https://www.emoney.cn/dianjin/bb/redianbankuaibaogao.pdf" target="_blank" class="btn an4 dh" clickkey="btn4"></a>
    </div>
</div>
<div class="img_3">
    <div class="main"><div class="btnh an5"></div><div class="btnh an6"></div><div class="btnh an7"></div><div class="btnh an8"></div>
        <a href="https://www.emoney.cn/dianjin/bb/zhongbaoyejiyubao.xlsx" target="_blank" class="btn an5 dh" clickkey="btn1"></a>
        <a href="https://www.emoney.cn/dianjin/bb/wajuediweiqianligu.pdf" target="_blank" class="btn an6 dh" clickkey="btn2"></a>
        <a href="https://www.emoney.cn/dianjin/bb/jigoumijidiaoyanmingdan.pdf" target="_blank" class="btn an7 dh" clickkey="btn3"></a>
        <a href="https://www.emoney.cn/dianjin/bb/longhubangmingxingxiwei.pdf" target="_blank" class="btn an8 dh" clickkey="btn4"></a>
    </div>
</div>
<div class="img_4">
    <div class="main"><div class="btnh an9"></div><div class="btnh an10"></div>
        <a href="https://www.emoney.cn/dianjin/bb/jietaozhinanshang.pdf" target="_blank" class="btn an9 dh" clickkey="btn1"></a>
        <a href="https://www.emoney.cn/dianjin/bb/jietaozhinanxia.pdf" target="_blank" class="btn an10 dh" clickkey="btn2"></a>
    </div>
</div>
<div class="img_5"></div>
<div class="foot">
    欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340
</div>
<div class="h" style="display: none;">
    <div class="logintc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入注册益盟产品时用的手机号</div>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="ischeckcode" value="0">
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<script type="text/javascript" th:src="@{${staticPath}+'static/js/jquery.slides.js'}"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script>
    // 封装日期比较逻辑
    function showElementBasedOnDate(selector, targetDate) {
        var currentDate = new Date();
        if (currentDate.getTime() > targetDate.getTime()) {
            $(selector).show();
        }
    }

    // 封装按钮点击事件处理函数
    function setupButtonClickEvent(selector, eventId) {
        $(selector).click(function () {
            try {
                window.goods.addEventRecord(
                    JSON.stringify({
                        eventId: eventId
                    })
                );
            } catch (e) {
                console.error('Button click event error:', e);
            }
        });
    }

    // 初始化日期和元素显示逻辑
    $(document).ready(function () {
        if(isMobileDevice()){
            $(".h").hide();
        }else {
            if ($("#hid_isLogin").val() == "0") {
                $(".h").show();
                $(".logintc").show();
            }
        }

        //APP打点-begin
        try {
            window.goods.setAnalysisPageId(
                JSON.stringify({
                    pageId: 'Html:Activity:tklfl20250508'
                })
            );
        } catch (e) {
            console.error('Set analysis page ID error:', e);
        }

        for (var i = 1; i <= 9; i++) {
            setupButtonClickEvent('.an' + i, 'Html:Activity:tklfl20250508:btn' + i);
        }
        //APP打点-end

        var a = new Date("2025/7/4 20:00:00");
        var b = new Date("2025/7/8 20:00:00");
        var c = new Date("2025/7/10 20:00:00");
        var d = new Date("2025/7/14 20:00:00");
        var e = new Date("2025/7/15 20:00:00");
        var f = new Date("2025/7/16 20:00:00");
        var g = new Date("2025/7/19 14:30:00");
        var h = new Date("2025/7/25 20:00:00");
        var i = new Date("2025/7/29 20:00:00");
        var j = new Date("2025/8/1 20:00:00");
        showElementBasedOnDate('.an1', a);
        showElementBasedOnDate('.an2', b);
        showElementBasedOnDate('.an3', c);
        showElementBasedOnDate('.an4', d);
        showElementBasedOnDate('.an5', e);
        showElementBasedOnDate('.an6', f);
        showElementBasedOnDate('.an7', g);
        showElementBasedOnDate('.an8', h);
        showElementBasedOnDate('.an9', i);
        showElementBasedOnDate('.an10', j);

    });
</script>
<script type="text/javascript">
    document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));
</script>
<script src="https://imgtongji.emoney.cn/scripts/https/emoneyanalytics.js" type="text/javascript"></script>
<script>
    // 初始化统计
    var App = "10088";
    var Module = "dslfl-20250701";
    var Remark = "";
    var ClickFlag = true;
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>

</html>