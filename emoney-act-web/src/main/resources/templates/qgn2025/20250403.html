<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>《景气洞察布局四月》系列课</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="会后方向怎么选？低位景气做反转!年报+季报窗口期，盯景气上升，找景气反转">
    <link th:href="@{${staticPath}+'static/qgn/20250403/style/common.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "5大高端功能免费领");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>
<body>
<div class="bod">
    <div id="dbg1"></div>
    <div id="dbg2">
        <div class="main"><a href="javascript:void(0)" class="btn1 dh popupD"></a></div>
    </div>
    <div id="dbg3"></div>
    <div id="dbg4"><div class="main"><a href="javascript:void(0)" class="btn2 dh popupD"></a></div></div>
    <div id="dbg5"></div>
    <div id="dbg6"><div class="main"> <div class="txt1">
        每节课听满45分钟可以获得200积分（含直播或回放，积分2个工作日内送达）<br />
        <br />
        <br />
        ①1000积分可兑换原工具使用期5天（活动期内仅可兑换两次）；<br />
        ②2000积分可兑换原工具使用期10天（活动期内仅可兑换一次）；<br />
        ③200积分可兑换《褚伟锋-本周行情展望》或《涂举华-本周行情展望》<br />
        <br />
        软件端“积分”APP端“积分中心”<br />
        <br />
        原工具使用期只有参与课程才可以兑换，《褚伟锋-本周行情展望》或《涂举华-本周行情展望》有积分即可兑换<br />
        活动时间：4月6日-4月19日，本次赠送积分自获得日算起，有效期3个月，3个月内未兑换，自动过期失效
    </div></div></div>

    <div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br/>
        本活动最终解释权归益盟股份有限公司　沪ICP备06000340
    </div>
    <div class="pf">
        <div class="main"><a href="javascript:void(0)" class="btn4 dh popupD"></a></div>
    </div>
</div>
<div class="h" style="display: none;">
    <div class="tc" style="display: none;"><a href="javascript:void(0)" class="close"></a></div>
    <div class="tc_qgn" style="display: none;"><a href="javascript:void(0)" class="close"></a></div>
    <div class="tclogin" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<script type="text/javascript" th:src="@{${staticPath}+'static/js/jquery.slides.js'}"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/qgn/20250403/js/main.js?r=20240218'}"></script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>

</body>
</html>