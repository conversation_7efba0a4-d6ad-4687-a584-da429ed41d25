<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>服务管家</title>
<!--    <link th:href="@{${staticPath}+'static/custservice/style/common.css?r=20231211'}" rel="stylesheet" type="text/css" />-->
<!--    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>-->

    <link href="style/common.css" rel="stylesheet" type="text/css" />
    <script src="/activity/static/js/jquery-1.11.0.min.js" type="text/javascript"></script>
    <script> document.domain = 'emoney.cn';</script>
</head>
<body>
<div class="pf">
    <div th:if="${custServiceInfo != null}">
        <img src="images/tx.png" class="tx" alt="">
        <div class="name">
            <span th:text="(${custServiceInfo}?${custServiceInfo.currentBelongUserName}:'')"></span>
            <span class="ico">专属服务</span>
            <div class="zqbh" th:text="'从业编号'+(${custServiceInfo}?${custServiceInfo.jobNumber}:'')"></div></div>
        <div class="ewm">
            <img id="qrimg" src="images/ewm.png" alt="">
            <div class="b" style="display: none;"><div class="xz" id="animation"></div></div>
        </div>
        <div class="txt">扫码添加您的专属服务管家</div>
    </div>

    <div th:if="${custServiceInfo == null}">
        <div th:if="${isLogin == '1' && loginUserInfo.MobileX!=null}" class="txt1">正在为您分配管家，请耐心等待...</div>
        <div th:if="${isLogin == '0' || loginUserInfo.MobileX==null}" class="txt2">检测到您的账号未绑定手机号<br/>请先绑定手机号哦~</div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_uid" th:value="(${isLogin == '1'}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin == '1'}?${loginUserInfo.MobileX}:'')">
<script src="/activity/static/libs/layer/layer.js"></script>
<script src="js/main.js"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>