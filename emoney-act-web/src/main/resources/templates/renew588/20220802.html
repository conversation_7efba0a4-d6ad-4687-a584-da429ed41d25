<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew588/20220802/css/style.css?v=0801'}" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        .bg {
            display: none
        }

        .tc4 {
            display: none
        }

        .tc {
            display: none
        }
    </style>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function () {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {
                }
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {
                }
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {
                }
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {
                }
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }

            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1">
    <div class="main">
        <div class="djs">
            <div class="t">0</div>
            <div class="s">00</div>
            <div class="f">00</div>
            <div class="m">00</div>
        </div>
    </div>
</div>
<div class="img_2">
    <div class="main">
        <a href="#content" class="btn1 dh"></a>
        <a href="javascript:void(0)" name="payurl" target="_blank" class="btn5 dh"></a>
    </div>
</div>
<div class="img_3" id="content">
    <div class="main">
        <a href="javascript:;" name="getBenefit" data-day="1" data-actcode="2022080201" class="btn6h i1">点击领取</a>
        <a href="javascript:;" name="getBenefit" data-day="2" data-actcode="2022080202" class="btn6 i2">尚未开始</a>
        <a href="javascript:;" name="getBenefit" data-day="3" data-actcode="2022080203" class="btn6 i3">尚未开始</a>
        <a href="javascript:void(0)" name="payurl" target="_blank" class="btn2 dh"></a>
        <!--状态2<a href="" class="btn2h"></a>--></div>
</div>
<div class="img_4">
    <div class="main">
        <iframe frameborder="0" webkitallowfullscreen="true" mozallowfullscreen="true" allowfullscreen="true" src=""
                class="sp" name="al1" scrolling="no"></iframe>
        <a href="javascript:void(0)" name="payurl" target="_blank" class="btn3 dh"></a></div>
</div>
<div class="img_5">
    <div class="main">
        <div class="hdgz" style="line-height:27px;">
            1、活动有效时间：2022年8月3日-2022年8月31日。<br/>
            2、活动参与对象：仅限智盈付费用户参加1次，智盈大师或其他产品用户不参与本活动。<br/>
            3、领福袋活动规则：<br/>
            ①福袋奖励仅限登录活动页领取。<br/>
            ②领取说明：<br/>
            -活动期间福袋奖励每日限领1份。奖励自动发放到领取时所用账户；<br/>
            -续费成功后，福袋所赠智盈VIP或北上资金使用期翻倍；<br/>
            -翻倍部分奖励(即30天智盈VIP，或10天智盈VIP+20天北上资金使用期)，于续费后立即发放；其余奖励需回福袋领取页逐日领取。<br/>
            -福袋所赠积分与优惠券不参与翻倍；<br/>
            ③若续费后退单，翻倍赠送的使用期随之失效，其余福袋奖励保留。<br/>
            4、特权生效说明：完成续费和适当性测评后开通【智盈软件使用期】权限。<br/>
            5、优惠券规则：持本活动有效立减券的用户，续费时可抵扣续费金额。立减券不可与其他优惠叠加使用。
        </div>
    </div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a>
    股市有风险，投资需谨慎<br/>
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340
</div>
<a href="javascript:;" id="btnIM" class="server"></a>
<div class="bott">
    <div class="main">
        <a href="javascript:void(0)" name="payurl" target="_blank" class="btn4 dh"></a>
    </div>
</div>
<div class="bg">
    <div class="tc">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text"/>
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text"/>
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode"/>
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;"
                                                                                class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <!--第一天-->
    <div class="tc4" id="div_day1"><a href="javascript:;" class="close"></a>
        <img th:src="@{${staticPath}+'static/renew588/20220802/images/tc1.png'}" alt=""><a href="javascript:;"
                                                                                           class="btn9">确定</a>
    </div>
    <!--第三天-20天使用期-->
    <div class="tc4" id="day3_1"><a href="javascript:;" class="close"></a>
        <img th:src="@{${staticPath}+'static/renew588/20220802/images/tc2.png'}" alt=""><a href="javascript:;"
                                                                                           class="btn9">确定</a>
    </div>
    <!--第二天福利-->
    <div class="tc4" id="div_day2"><a href="javascript:;" class="close"></a>
        <img th:src="@{${staticPath}+'static/renew588/20220802/images/tc3.png'}" alt=""><a href="javascript:;"
                                                                                           class="btn9">确定</a>
    </div>
    <!--第三天-20天北上资金功能使用期-->
    <div class="tc4" id="day3_2"><a href="javascript:;" class="close"></a>
        <img th:src="@{${staticPath}+'static/renew588/20220802/images/tc4.png'}" alt=""><a href="javascript:;"
                                                                                           class="btn9">确定</a>
    </div>
    <!--第三天选择福利-->
    <div class="tc4" id="div_day3"><a href="javascript:;" class="close"></a>
        <img th:src="@{${staticPath}+'static/renew588/20220802/images/tc5.png'}" alt="">
        <div class="btn10">
            <ul>
                <li data-val="1"></li>
                <li data-val="2"></li>
            </ul>
        </div>
        <a href="javascript:;" id="getBenefit3" data-day="3" data-actcode="2022080203" class="btn9h">确认选择</a>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}"/>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:inline="javascript">
    var www = "../";
    $(document).ready(function () {
        var uid = "";
        var pid = "";
        var uname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        var benefitActCodes = "2022080201,2022080202,2022080203";
        var activityLoginCode = /*[[${actcode}]]*/ "0"
        if (!channelcode) {
            channelcode = "A12050";
        }

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        } else {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";

            if (pid != "888010000" && pid != "888010400") {
                layer.msg("非小智盈用户，不能参与本次活动。");
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        }

        var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=77&channelcode=" + channelcode + "&" + location.search;
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search;
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("非小智盈用户，不能参与本次活动。");
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        });

        //初始化福利按钮状态
        initBenefitBtnStatus(benefitActCodes, uid);

        //领取福利
        $("[name=getBenefit]").click(function () {
            var day = $(this).attr("data-day");
            var actcode = $(this).attr("data-actcode");
            var text = $(this).html();
            if (text == "点击领取") {
                if (day != "3") {
                    getBenefit(uid, pid, activityLoginCode, day, "", actcode);
                } else {
                    $(".bg").show();
                    $("#div_day" + day).show();
                }
            }
        });

        //day3-确认选择
        $("#getBenefit3").click(function () {
            var day = $(this).attr("data-day");
            var actcode = $(this).attr("data-actcode");

            //选择福利
            var hasSel1 = $(".btn10 ul li:eq(0)").hasClass("on");
            var hasSel2 = $(".btn10 ul li:eq(1)").hasClass("on");

            var seltype = hasSel1 ? "1" : hasSel2 ? "2" : "";

            if (seltype == "") {
                layer.msg("请选择福袋奖励");
                return false;
            }
            getBenefit(uid, pid, activityLoginCode, day, seltype, actcode);
        });
        $("#btnIM").click(function () {
            goIM("8月小智盈续费周年庆活动");
        });
        $(".close,.btn9").click(function () {
            $(".bg").hide();
            $(".tc4").hide();
        });
    });

    function initBenefitBtnStatus(actcodes, uid) {
        var requestDate = getQueryString("date");
        var nowDate = new Date();
        if (!!requestDate) {
            nowDate = new Date(requestDate);
        }

        $.ajax({
            type: 'get',
            url: 'https://act2017.emoney.cn/ActShort20200408/FlashSale/IsSubmitByActCodes?actcodes=' + actcodes,
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if (data.retCode == "0") {
                    var ret = data.Message;
                    var val = ret.split(",");
                    //var codes = banefitActCodes.split(",");
                    for (var i = 0; i < val.length; i++) {
                        if (!!val[i]) {
                            $("[name=getBenefit]").eq(i).removeClass("btn6h").addClass("btn6").html("已领取");
                        } else {
                            if (i > 0) {
                                var preDate = val[i - 1];
                                if (!!preDate) {
                                    var days = new Date(nowDate.toDateString()) - new Date(new Date(parseInt(preDate)).toDateString());
                                    if (days / (24*1000*3600) >= 1) {
                                        $("[name=getBenefit]").eq(i).removeClass("btn6").addClass("btn6h").html("点击领取");
                                    }
                                }
                            } else {
                                $("[name=getBenefit]").eq(i).removeClass("btn6").addClass("btn6h").html("点击领取");
                            }
                        }
                    }
                }
            }
        });
    }

    function getBenefit(uid, pid, activityLoginCode, day, seltype, actcode) {
        var nowDate = new Date();
        //获取福利
        $.ajax({
            type: 'get',
            url: "getBenefits",
            dataType: 'json',
            data: {
                uid: uid,
                pid: pid,
                actcode: activityLoginCode,
                day: day,
                seltype: seltype
            },
            success: function (data) {
                if (data.code == "200") {
                    //记录领取福利时间
                    $.ajax({
                        type: 'get',
                        url: "https://act2017.emoney.cn/ActShort20200408/FlashSale/AddCountByActCode?actcode=" + actcode,
                        dataType: 'jsonp',
                        data: {
                            uid: uid,
                            value: nowDate.getTime()
                        },
                        success: function (data) {
                        }
                    });
                    $("[name=getBenefit]").eq(parseInt(day) - 1).removeClass("btn6h").addClass("btn6").html("已领取");
                    if (seltype != "") {
                        $("#div_day" + day).hide();
                        $("#day3_" + seltype).show();
                    } else {
                        $(".bg").show();
                        $("#div_day" + day).show();
                    }
                } else {
                    layer.msg(data.msg);
                    return false;
                }
            }
        });
    }

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "http://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:AdClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }

    function GetExternal() {
        return window.external.EmObj;
    }

    function PC_JH(type, c) {
        try {
            var obj =
                GetExternal();
            return obj.EmFunc(type, c);
        } catch (e) {
        }
    }

    //跳转 IM
    function goIM(fromname) {
        var b = new Base64();
        var ret = PC_JH('EM_FUNC_START_IM', '0,AC20220714,' + b.encode(fromname));
    }
</script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script>
    function SetTimeout(year, month, day, hour, minute, second) {
        var leftTime = (new Date(year, month - 1, day, hour, minute, second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }

    djs = setInterval("SetTimeout(2022,9,1,00,00,00)", 1000);

    function checkTime(i) { //将0-9的数字前面加上0，例1变为01
        if (i < 10) {
            i = "0" + i;
        }
        return i;
    }

    SetTimeout();

    var classdate = new Date();
    var a = new Date("2022/9/1 00:00:00");
    var b = new Date("2022/8/27 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("0");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs").show();
    }

    $('.btn10 li').click(function () {
        $(this).addClass('on').siblings('li').removeClass('on');
        $("#getBenefit3").removeClass("btn9h").addClass("btn9");
    });


    var al1 = "0";
    $(window).scroll(function (e) {
        if (($(window).scrollTop() > 756) && (al1 == "0")) {
            $("[name='al1']").attr("src", "https://www.emoney.cn/dianjin/mf/bszj6/bszj.html");
            al1 = "1";
        }
    });

</script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
