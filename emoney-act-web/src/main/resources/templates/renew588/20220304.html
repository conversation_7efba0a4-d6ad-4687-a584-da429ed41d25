<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew588/20220304/css/style.css'}" rel="stylesheet" type="text/css" />
    <style>
        .hide{display: none;}
    </style>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1">
    <div class="main">
        <a th:style="'display:' + @{(${hasPointRecord} ? 'none' : 'block')} + ''" href="javascript:;" class="btn1 an2 dh" id="btn_addPoint"></a>
        <!--状态2-->
        <div th:style="'display:' + @{(${!hasPointRecord} ? 'none' : 'block')} + ''" class="btn1h an1">已领取</div>
    </div>
</div>
<div th:style="'display:' + @{(${hasCoupon100} ? 'none' : 'block')} + ''"  class="img_2"></div>
<div th:style="'display:' + @{(${hasCoupon100} ? 'block' : 'none')} + ''"  class="img_2b"></div>
<div class="img_3"></div>
<div class="img_4"><div class="main"><img class="png_1" th:src="@{${staticPath}+'static/renew588/20220304/images/images_05.png'}"></div></div>
<div class="img_5">
    <div class="main">
        <a href="javascript:;" class="btn2 dh" name="payurl" target="_blank"></a>
    </div>
</div>
<div class="img_6"></div>
<div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div th:style="'display:' + @{(${hasCoupon100} ? 'none' : 'block')} + ''" class="bott">
    <div class="main"><a href="javascript:;" name="payurl" target="_blank" class="btn8 dh"></a>
    </div>
</div>
<!--状态2-->
<div th:style="'display:' + @{(${hasCoupon100} ? 'block' : 'none')} + ''" class="bott2">
    <div class="main"><a href="javascript:;" name="payurl" target="_blank" class="btn8 dh"></a>
    </div>
</div>
<div class="bg hide">
    <div class="tc hide">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc4 hide"><a href="javascript:;" class="close" data-type="open"></a>
        <a href="javascript:;" name="payurl" target="_blank" class="btn3 dh"></a>
    </div>
    <div class="tc5 hide"><a href="javascript:;" class="close"></a>
        <a href="javascript:;" name="payurl" target="_blank" class="btn4 dh"></a><a href="javascript:;" class="btn9"></a>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:inline="javascript">
    var www="../";
    $(document).ready(function () {
        var uid = "";
        var pid = "";
        var uname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var allpoint =  /*[[${allPoint}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        var hasCoupon = false;
        var actcode = /*[[${actcode}]]*/ "0"

        if (!channelcode) {
            channelcode = "A12050";
        }

        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
            hasCoupon =  /*[[${hasCoupon100}]]*/ false;
        }

        var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=65&channelcode=" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("非小智盈用户，不能参与本次活动。");
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        });
        //立即领取
        $("#btn_addPoint").click(function () {
            var _this = $(this);
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return;
            }
            $.getJSON("addpointrecored", { uid: uid, pid: pid,actcode:actcode }, function (data) {
                if (data.code == "200") {
                    $(".btn1").hide();
                    $(".btn1h").show();

                } else {
                    layer.msg(data.msg);
                }
                //推送cmp
                pushdatatocmp(uname, "ACRenew20210104");
            });
        });

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        } else {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("非小智盈用户，不能参与本次活动。");
                return false;
            }
            //有优惠券弹出优惠券窗口
            if(hasCoupon){
                setTimeout(function (){
                    $(".bg").show();
                    $(".tc4").show();
                },3000);
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        }

        $(".btn9").click(function () {
            $(".bg").hide();
            $(".tc5").hide();
        });
        $(".close").click(function (){
            var type = $(this).attr("data-type");
            if(type == "open"){
                $(".tc4").hide();
                $(".tc5").show();
            }else {
                $(".bg").hide();
            }
        });
    });

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "http://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:AdClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
</script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='http://api2.tongji.emoney.cn/scripts/emoneyanalyticspv.js%3Fcode=E9B1C7CAEA688BB4E9AEC91549ABE92A' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
