<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew588/20221031/css/style.css'}" rel="stylesheet" type="text/css"/>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1">
    <div class="main">
        <div class="djs"><div class="t">0</div>
        <div class="s">00</div>
        <div class="f">00</div><div class="m">00</div>
        </div>
    </div>
</div>
<div class="img_2">
    <div class="main">
        <div class="p1" name="buttonStatus" data-type="1"></div>
        <!--状态2-->
        <div class="p2" style="display: none" name="buttonStatus" data-type="2" ></div>

        <!--btn1 拆盲盒-->
        <a href="javascript:void(0)" class="btn1 dh" name="buttonStatus" data-type="1" clickkey="openBox" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a>
        <!--状态2-->
        <div class="btn1h" style="display: none" name="buttonStatus" data-type="2"></div>

        <!--btn2 拆盲盒-->
        <a href="javascript:void(0)" class="btn2 dh" name="buttonStatus" data-type="1" clickkey="openBox" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a>
        <!--状态2-->
        <div style="display: none" name="buttonStatus" data-type="2">
            <a href="javascript:void(0)" name="payurl" target="_blank" class="btn2h dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a><div class="ico1"></div>
        </div>
    </div>
</div>
<div class="img_3">
    <div class="main">
        <iframe frameborder="0" webkitallowfullscreen="true" mozallowfullscreen="true" allowfullscreen="true" src="javascript:void(0)" class="sp" name="al1" scrolling="no"></iframe>
        <a href="javascript:void(0)" class="btn3 a1 dh" name="buttonStatus" data-type="1" clickkey="openBox" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}">拆盒解锁</a><!--btn3 拆盲盒-->
        <!--状态2-->
        <a style="display: none" href="javascript:void(0)" name="payurl" target="_blank" class="btn3 a1 dh" data-type="2" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}">续费解锁</a>
    </div>
</div>
<div class="img_4">
    <div class="main">
        <a href="javascript:void(0)" class="btn3 a2 dh" name="buttonStatus" data-type="1" clickkey="openBox" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}">拆盒立享</a><!--btn3 拆盲盒-->
        <!--状态2-->
        <a style="display: none" href="javascript:void(0)" name="payurl" target="_blank" class="btn3 a2 dh" data-type="2" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}">续费立享</a >
    </div>
</div>
<div class="img_5">
    <div class="main">
        <div class="hp"><img th:src="@{${staticPath}+'static/renew588/20221031/images/hp.png'}" src="images/hp.png" alt=""></div>
        <a href="javascript:void(0)" class="btn3 a3 dh" name="buttonStatus" data-type="1" clickkey="openBox" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}">拆盒续费</a><!--btn3 拆盲盒-->
        <!--状态2-->
        <a style="display: none" href="javascript:void(0)" name="payurl" target="_blank" class="btn3 a3 dh"  data-type="2" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}">立即续费</a>
    </div>
</div>
<div class="img_6"></div>
<div class="img_7">
    <div class="main">
        <div class="hdgz">1、活动有效时间：2022年11月1日-2022年12月11日。<br />
        2、活动参与对象：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。<br />
        3、福利发放规则：拆盲盒获得的【智盈使用期】和【北上资金】权限在完成续费和适当性测评后开通。【北上资金】为拆盲盒赠送功能，赠送成功后不可更换手机号，下单时请确认好手机号。<br />
        4、优惠券规则：持本活动有效立减券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用。<br />
        5、积分规则：盲盒赠送30积分立即到账。完成续费和适当性测评后发放100积分（冻结状态），适当性测评完成30天后，积分解冻，可在积分商城使用。
        </div>
    </div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="bott" name="buttonStatus" data-type="1">
    <div class="main"><a href="javascript:void(0)" class="btn5 dh" name="buttonStatus" data-type="1" clickkey="openBox" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a><!--btn5 拆盲盒-->
    </div>
</div>
<!--状态2-->
<div class="bott2" style="display: none;" name="buttonStatus" data-type="2">
    <div class="main">
        <!--10积分兑换100优惠券-->
        <div style="cursor: pointer;" th:style="'display:'+@{(${hasExChangeRecord} ? 'none' : 'block')}+''" name="btn_exchange" id="bott_exchange" clickkey="btn_exchange" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}">
            <a href="javascript:;" class="btn5h"></a><div class="ico2 dh"></div>
        </div>
        <div id="bott_pay" th:style="'display:'+@{(${hasExChangeRecord} ? 'block' : 'none')}+''">
            <a href="javascript:;" class="btn5h" name="payurl" target="_blank" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a>
            <a href="javascript:;" name="payurl" target="_blank" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"><div class="ico2h dh"></div></a>
        </div>
    </div>
</div>


<div class="bg" style="display: none">
    <div class="tc" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc4" id="pop_100coupon" style="display: none;"><a href="javascript:void(0)" class="close"></a>
        <a href="javascript:void(0)" name="payurl" target="_blank" class="btn6 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a><!--兑换-->
    </div>
    <div class="tc5" id="pop_box" style="display: none;"><a href="javascript:void(0)" class="close"></a>
        <a href="javascript:void(0)" name="payurl" target="_blank"  class="btn7 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a><!--立即领取-->
        <a href="javascript:void(0)" class="ico3 dh" name="btn_exchange" clickkey="btn_exchange" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a>
    </div>
</div>

    <input type="hidden" id="hid_actcode" th:value="${actcode}" />
    <script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
    <script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
    <script th:inline="javascript">
        var www="../";
        var actcode = /*[[${actcode}]]*/ "0"
        $(document).ready(function () {
            var uid = "";
            var pid = "";
            var uname = "";
            var isLogin = /*[[${isLogin}]]*/ "0";
            var realname = "";
            var maskname = "";
            var channelcode = getQueryString("channelcode");
            var token = getQueryString("token");
            var hasExChangeRecord = false;
            var productid = "535";

            if (!channelcode) {
                channelcode = "A12050";
            }

            if(isLogin == "1") {
                uid = /*[[${loginUserInfo.uid}]]*/ "0";
                pid = /*[[${loginUserInfo.pid}]]*/ "0";
                uname = /*[[${loginUserInfo.MobileX}]]*/ "";
                maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
                realname = /*[[${loginUserInfo.RealName}]]*/ "";
                hasExChangeRecord = /*[[${hasExChangeRecord}]]*/ false;
            }

            var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=86&channelcode=" + channelcode + "&" + location.search;
            var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
            if (isMobile) {
                payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search;
            }
            if (!token) {
                payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
            }
            $("[name=payurl]").attr("href", payUrl);
            $("[name=payurl]").click(function () {
                if (!checkPermission(pid)) {
                    return false;
                }
                //推送cmp
                pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
            });
            //拆盲盒
            $("[data-type=1]").click(function (){
                addCount(uid);
            });
            //立即兑换
            $("[name=btn_exchange]").click(function () {
                var $this = $(this);

                $.ajax({
                    type: 'get',
                    url: "pointOrderExchange?&productId=" + productid+"&actcode="+actcode,
                    dataType: 'json',
                    success: function (data) {
                        if (data.code == "200") {
                            $(".bg").show();
                            $("#pop_box").hide();
                            $("#pop_100coupon").show();
                            //气泡变化
                            $("#bott_exchange").hide();
                            $("#bott_pay").show();
                        } else {
                            layer.msg(data.msg);
                            return;
                        }
                    }
                });
            });

            if (isLogin == "0") {
                //未登录
                $(".bg").show();
                $(".tc").show();
            } else {
                if (!checkPermission(pid)) {
                    return false;
                }
                InitStatus(uid);
                //推送cmp
                pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
            }

            $("#btnIM").click(function (){
                goIM("11月小智盈续费活动");
            });

            $(".close").click(function (){
                $(".bg").hide();
                $(".tc4").hide();
                $(".tc5").hide();
            });
        });

        function getQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }
        function pushdatatocmp(uname, adcode) {
            var data = {
                "appid": '10088',
                "logtype": 'click',
                "mid": '',
                "pid": getQueryString("pid"),
                "sid": getQueryString("sid"),
                "tid": getQueryString("tid"),
                "uid": getQueryString("uid"),
                "uname": uname,
                "adcode": adcode,
                "targeturl": "",
                "pageurl": window.top.location.href
            }
            var saasUrl = "http://ds.emoney.cn/saas/queuepush";
            var saasSrc = saasUrl + "?v=" + Math.random()
                + "&queuekey=EMoney:softsupport:AdClickToCMPQueueID"
                + "&message=" + encodeURIComponent(JSON.stringify(data));

            var elm = document.createElement("img");
            elm.src = saasSrc;
            elm.style.display = "none";
            document.body.appendChild(elm);
        }
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj =
                    GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {
            }
        }
        //跳转 IM
        function goIM(fromname) {
            var b = new Base64();
            var ret = PC_JH('EM_FUNC_START_IM', '0,AC2022091,' + b.encode(fromname));
        }
        // 检查用户是否有权限参与
        function checkPermission(pid) {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return false;
            }
            return true;
        }
        //名额-1
        function addCount(uid) {
            $.ajax({
                type: 'get',
                url: window.location.protocol + '//act2017.emoney.cn/ActShort20200408/FlashSale/AddCountByActCode?actcode=' + actcode,
                dataType: 'jsonp',
                data: {
                    uid: uid,
                    value: "1"
                },
                success: function (data) {
                    if (data.retCode == "0") {
                        //申请成功
                        $("[data-type=1]").hide();
                        $("[data-type=2]").show();

                        $(".bg").show();
                        $("#pop_box").show();
                    }
                }
            });
        }

        function InitStatus(uid) {
            $.ajax({
                type: 'get',
                url: window.location.protocol + '//act2017.emoney.cn/ActShort20200408/FlashSale/IsSubmit?actcode=' + actcode,
                dataType: 'jsonp',
                data: {
                    uid: uid
                },
                success: function (data) {
                    if (data.retCode == 0) {
                        $("[data-type=1]").hide();
                        $("[data-type=2]").show();
                    }
                }
            });
        }

    </script>
    <script th:src="@{${staticPath}+'static/js/login.js'}"></script>
    <script th:src="@{${staticPath}+'static/js/base64.js'}"></script>

    <script>
        function SetTimeout(year,month,day,hour,minute,second){
            var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
            var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
            var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
            var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
            var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
            days = checkTime(days);
            hours = checkTime(hours);
            minutes = checkTime(minutes);
            seconds = checkTime(seconds);

            $(".t").html(days);
            $(".s").html(hours);
            $(".f").html(minutes);
            $(".m").html(seconds);
        }
        djs=setInterval("SetTimeout(2022,12,11,24,00,00)",1000);
        function checkTime(i){ //将0-9的数字前面加上0，例1变为01
            if(i<10)
            {
                i = "0"+i;
            }
            return i;
        }
        SetTimeout();

        var classdate = new Date();
        var a=new Date("2022/12/11 24:00:00");
        var b=new Date("2022/12/7 00:00:00");
        if (classdate.getTime() > a.getTime()) {
            clearInterval(djs);
            $(".t").html("00");
            $(".s").html("00");
            $(".f").html("00");
            $(".m").html("00");
        }
        if (classdate.getTime() > b.getTime()) {
            $(".djs").show();
        }


        var al1 = "0";
        $(window).scroll(function(e){
            if(($(window).scrollTop() >556) && (al1 == "0")){
                $("[name='al1']").attr("src","http://www.emoney.cn/dianjin/mf/bszj6/bszj.html");
                al1 = "1";
            }
        });
    </script>

    <script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20221031";//模块名称(焦点图2)
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
