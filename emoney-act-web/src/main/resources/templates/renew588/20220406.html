<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew588/20220406/css/style.css'}" rel="stylesheet" type="text/css" />
    <style>
        .hide{display: none;}
    </style>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1">
    <div class="main"><div class="djs"><div class="t">00</div>
        <div class="s">00</div>
        <div class="f">00</div><div class="m">00</div></div></div></div>
<div class="img_2">
    <div class="main">
        <a href="javascript:;" id="btn_add288" class="bg1 dh"><img th:src="@{${staticPath}+'static/renew588/20220406/images/bg1.png'}" alt=""><span class="ico">仅剩<span name="sycount">5888</span></span></a>
        <!--日常参与576-->
        <a id="btn_576" style="display: none" href="javascript:;" name="payurl" target="_blank" class="bg1 dh"><img th:src="@{${staticPath}+'static/renew588/20220406/images/bg2.png'}" alt=""><span class="ico">仅剩<span name="sycount">5888</span></span></a>
        <!--领取288无100券-->
        <a id="btn_288" style="display: none" href="javascript:;" name="payurl" target="_blank" class="bg1 dh"><img th:src="@{${staticPath}+'static/renew588/20220406/images/bg2b.png'}" alt=""><span class="ico">仅剩<span name="sycount">5888</span></span></a>
        <!--领取288有100券-->
        <a id="btn_188" style="display: none" href="javascript:;" name="payurl" target="_blank" class="bg1 dh"><img th:src="@{${staticPath}+'static/renew588/20220406/images/bg2c.png'}" alt=""><span class="ico">仅剩<span name="sycount">5888</span></span></a>
        <marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="2" direction="up" class="txt1">
            恭喜！<span id="em1"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em2"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em3"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em4"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em5"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em6"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em7"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em8"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em9"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em10"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em11"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em12"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em13"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em14"></span>*** 领取成功，￥288元续费2年VIP<br />
            恭喜！<span id="em15"></span>*** 领取成功，￥288元续费2年VIP<br />
        </marquee>
        <div class="txt2"><a href="#a1" class="b1"></a>
            <a href="#a2" class="b2"></a>
            <a href="#a3" class="b3"></a>
            <a href="#a4" class="b4"></a>
            <a href="#a5" class="b5"></a>
            <a href="#a6" class="b6"></a>
        </div>
    </div></div>
<div class="img_3" id="a1"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al1"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">点击开通BS点</a>
</div></div>
<div class="img_4" id="a2"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al2"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">点击开始诊断</a>
</div></div>
<div class="img_5" id="a3"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al3"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">点击开通策略池</a>
</div></div>
<div class="img_6" id="a4"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al4"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">点击获取指标</a>
</div></div>
<div class="img_7" id="a5"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al5"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">点击开通策略池</a>
</div></div>
<div class="img_8" id="a6"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al6"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">立即开通</a>
</div></div>
<div class="img_9"></div>
<div class="img_10"><div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div></div>

<a href="javascript:;" id="btn_addPoint" class="pf4" th:style="'display:'+@{(${hasPointRecord} ? 'none' : 'block')}+''"></a>
<a class="pf4h" th:style="'display:'+@{(${hasPointRecord} ? 'block' : 'none')}+''"></a>

<!--未领取288-->
<div class="bott" id="div_576">
    <div class="main">
        <div class="pf1"></div>
        <div class="ico2">限时领减￥288券</div>
        <a href="javascript:;" name="payurl" target="_blank" class="btn3"></a>
    </div>
</div>
<!--已领取288，无优惠券-->
<div class="bott" style="display: none" id="div_288">
  <div class="main">
    <div class="pf2"></div>
	  <div class="ico2">已领取￥288券</div>
	  <a href="javascript:;" name="payurl" target="_blank" class="btn3"></a>
  </div>
</div>

<!--已领取288，有优惠券-->
<div class="bott" style="display: none" id="div_188">
  <div class="main">
    <div class="pf3"></div>
	  <div class="ico2">已领取￥388券</div>
	  <a href="javascript:;" name="payurl" target="_blank" class="btn3"></a>
  </div>
</div>

<div class="bg" style="display: none">
    <div class="tc" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <!--领取288券-->
    <div class="tc4" style="display: none"><a href="javascript:;" class="close"></a>
        <a href="javascript:;" name="payurl" target="_blank"><img th:src="@{${staticPath}+'static/renew588/20220406/images/tc1.png'}" alt=""></a>
    </div>
    <!--100元券-->
    <div class="tc5" style="display: none"><a href="javascript:;" class="close"></a>
        <a href="javascript:;" name="payurl" target="_blank"><img th:src="@{${staticPath}+'static/renew588/20220406/images/tc2.png'}" alt=""></a>
    </div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actcode}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:inline="javascript">
    var www="../";
    $(document).ready(function () {
        var uid = "";
        var pid = "";
        var uname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        var hasCoupon = false;
        var actcode = /*[[${actcode}]]*/ "0"

        if (!channelcode) {
            channelcode = "A12050";
        }
        RandEMCode();
        InitCount(5888,actcode);
        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
            hasCoupon =  /*[[${hasCoupon100}]]*/ false;
        }

        var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=67&channelcode=" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("非小智盈用户，不能参与本次活动。");
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        });
        //领取288
        $("#btn_add288").click(function (){
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("非小智盈用户，不能参与本次活动。");
                return false;
            }
            addCount(actcode,uid,hasCoupon);
            $(".bg").show();
            $(".tc4").show();
        });
        //立即领取
        $("#btn_addPoint").click(function () {
            var _this = $(this);
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return;
            }
            $.getJSON("addpointrecored", { uid: uid, pid: pid,actcode:actcode }, function (data) {
                if (data.code == "200") {
                    $(".pf4").hide();
                    $(".pf4h").show();
                } else {
                    layer.msg(data.msg);
                }
                //推送cmp
                pushdatatocmp(uname, "ACRenew20210104");
            });
        });

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        } else {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("非小智盈用户，不能参与本次活动。");
                return false;
            }
            //判断用户是否领取288券
            $.ajax({
                type: 'get',
                url: 'http://act2017.emoney.cn/ActShort20200408/FlashSale/IsSubmit?actcode=' + actcode,
                dataType: 'jsonp',
                data: {
                    uid: uid
                },
                success: function (data) {
                    if(data.retCode=="0"){
                        //已申请过
                        $(".bott").hide();
                        $(".bg1").hide();
                        if(hasCoupon){
                            $("#div_188").show();
                            $("#btn_188").show();
                        }else{
                            $("#div_288").show();
                            $("#btn_288").show();
                        }
                    }if(data.retCode=="-2"){
                        //未申请过,5秒后统一领取，并更改状态
                        setTimeout(function (){
                            addCount(actcode,uid,hasCoupon);
                        },5000);
                    }
                }
            });
            //有优惠券弹出优惠券窗口
            if(hasCoupon){
                setTimeout(function (){
                    $(".bg").show();
                    $(".tc5").show();
                },3000);
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        }

        $(".close").click(function (){
            $(".bg").hide();
            $(".tc4").hide();
            $(".tc5").hide();
        });
    });
    function RandEMCode() {
        for (var y = 0; y < 15; y++) {
            var code = Math.floor(Math.random() * (999 - 100 + 1) + 100);
            var emcode = "Emy" + code;

            $("#em" + y).html("Emy" + code);
        }
    }
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "http://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:AdClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }

    function addCount(actcode,uid,hasCoupon){
        //名额-1
        $.ajax({
            type: 'get',
            url: 'http://act2017.emoney.cn/ActShort20200408/FlashSale/AddCountByActCode?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                uid: uid ,
                value: "1"
            },
            success: function (data) {
                if(data.retCode=="0"){
                    //申请成功
                    $(".bott").hide();
                    $(".bg1").hide();
                    if(hasCoupon){
                        $("#div_188").show();
                        $("#btn_188").show();
                    }else{
                        $("#div_288").show();
                        $("#btn_288").show();
                    }
                }
            }
        });
    }
    function InitCount(initCount,actcode) {
        $.ajax({
            type: 'get',
            url: 'http://act2017.emoney.cn/ActShort20200408/FlashSale/GetCountByActCode?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                initCount: initCount
            },
            success: function (data) {
                if (data.retCode == 0) {
                    if (data.Message == "") {
                        data.Message = 0;
                    }
                    var usedCount = parseInt(data.Message);
                    $("[ name=sycount]").html(initCount - parseInt(usedCount));//剩余席位

                    setTimeout("InitCount()", 60000 * 5);
                }
            }
        });
    }
</script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs=setInterval("SetTimeout(2022,4,26,00,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0"+i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2022/4/26 00:00:00");
    var b=new Date("2022/4/11 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("00");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs").show();
    }
</script>

<script>

    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";

    $(window).scroll(function(e){
        if(($(window).scrollTop() > 800) && (al1 == "0")){
            $("[name='al1']").attr("src","http://www.emoney.cn/dianjin/mf/bs/bs.html");
            al1 = "1";
        }
        if(($(window).scrollTop() > 1800) && (al2 == "0")){
            $("[name='al2']").attr("src","http://www.emoney.cn/dianjin/mf/m-ggsfz/ggsfz.html");
            al2 = "1";
        }
        if(($(window).scrollTop() > 2750) && (al3 == "0")){
            $("[name='al3']").attr("src","http://www.emoney.cn/dianjin/mf/sslt3/sslt.html");
            al3 = "1";
        }
        if(($(window).scrollTop() > 3690) && (al4 == "0")){
            $("[name='al4']").attr("src","http://www.emoney.cn/dianjin/mf/m-qsdd/qsdd.html");
            al4 = "1";
        }
        if(($(window).scrollTop() > 4626) && (al5 == "0")){
            $("[name='al5']").attr("src","http://www.emoney.cn/dianjin/mf/dblb2/dblb.html");
            al5 = "1";
        }
        if(($(window).scrollTop() > 5580) && (al6 == "0")){
            $("[name='al6']").attr("src","http://www.emoney.cn/dianjin/mf/tydp/tydp.html");
            al6 = "1";
        }
    });

</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=E9B1C7CAEA688BB4E9AEC91549ABE92A' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
