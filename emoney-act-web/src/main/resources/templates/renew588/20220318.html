<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew588/20220318/css/style.css'}" rel="stylesheet" type="text/css" />
    <style>
        .hide{display: none;}
    </style>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2">
    <div class="main">
        <div class="djs">
            <div class="t">00</div>
            <div class="s">00</div>
            <div class="f">00</div>
            <div class="m">00</div>
        </div>
        <!--158-->
        <a href="javascript:;" name="payurl" target="_blank">
        <img th:style="'display:' + @{(${!hasCoupon100 && hasExChangeRecord} ? 'block' : 'none')} + ''" th:src="@{${staticPath}+'static/renew588/20220318/images/bg1g.png'}" class="bg1 img158" alt="">
        </a>
        <!--188-->
        <a href="javascript:;" name="payurl" target="_blank">
        <img th:style="'display:' + @{(${!hasCoupon100 && !hasExChangeRecord} ? 'block' : 'none')} + ''" th:src="@{${staticPath}+'static/renew588/20220318/images/bg1.png'}" class="bg1 img188" alt="">
        </a>
        <!--88-->
        <a href="javascript:;" name="payurl" target="_blank">
        <img th:style="'display:' + @{(${hasCoupon100} ? 'block' : 'none')} + ''" th:src="@{${staticPath}+'static/renew588/20220318/images/bg1h.png'}" class="bg1 img88" alt="">
        </a>
    </div>
</div>
<div class="img_3"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al1"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">立即开通</a>
</div></div>
<div class="img_4"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al2"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">立即开通</a>
</div></div>
<div class="img_5">
    <div class="main"><a href="#a1" class="btn10"></a><a href="#a2" class="btn11"></a><a href="#a3" class="btn12"></a><a href="#a4" class="btn13"></a></div>
</div>
<div class="img_6" id="a1"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al3"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">点击开通BS点</a>
</div></div>
<div class="img_7" id="a2"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al4"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">点击开始诊断</a>
</div></div>
<div class="img_8" id="a3"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al5"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">点击开通策略池</a>
</div></div>
<div class="img_9" id="a4"><div class="main">
    <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al6"></iframe>
    <a href="javascript:;" name="payurl" target="_blank" class="btn2 dh">立即开通</a>
</div></div>
<div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<a href="javascript:;" id="btn_addPoint" class="pf4" th:style="'display:'+@{(${hasPointRecord} ? 'none' : 'block')}+''"></a>
<a class="pf4h" th:style="'display:'+@{(${hasPointRecord} ? 'block' : 'none')}+''"></a>
<!--158-->
<div class="bott3" th:style="'display:' + @{(${!hasCoupon100 && hasExChangeRecord} ? 'block' : 'none')} + ''">
  <div class="main"><a href="javascript:;" name="payurl" target="_blank" class="btn9"></a>
  </div>
</div>
<!--188-->
<div class="bott" th:style="'display:' + @{(${!hasCoupon100 && !hasExChangeRecord} ? 'block' : 'none')} + ''">
    <div class="main"><a href="javascript:;" name="payurl" target="_blank" class="btn9"></a>
    </div>
</div>
<!--88-->
<div class="bott2" th:style="'display:' + @{(${hasCoupon100} ? 'block' : 'none')} + ''">
    <div class="main"><a href="javascript:;" name="payurl" target="_blank" class="btn9"></a>
    </div>
</div>
<div class="bg hide">
    <div class="tc hide">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <!--兑换成功-->
    <div class="tc4 hide" flag="pop"><a href="javascript:;" class="close"></a>
        <a href="javascript:;" name="payurl" target="_blank" class="btn3 dh"></a>
    </div>
    <!--有100券-->
    <div class="tc5 hide" flag="pop"><a href="javascript:;" class="close"></a>
        <a href="javascript:;" name="payurl" target="_blank" class="btn4 dh"></a>
    </div>
    <!--兑换-->
    <div class="tc6 hide" flag="pop"><a href="javascript:;" class="close"></a>
        <a href="javascript:;" class="btn5 an3">放弃</a>
        <a href="javascript:;" class="btn6" id="btn_exchange" data-productid="288"></a>
    </div>
<!--    <div class="tc7 hide"><a href="" class="close"></a>-->
<!--        <a href="" class="btn5 an4">忍痛离开</a>-->
<!--        <a href="" class="btn7"></a>-->
<!--    </div>-->
    <!--拼团-->
    <div class="tc8 hide" flag="pop"><a href="javascript:;" class="close"></a>
        <div class="txt1">¥188<!--¥158-->=13个月VIP<span class="t1">(¥288/12月)</span><br />送3个月大师版高端功能</div>
        <div class="txt2">
            <ul>
                <li><span id="em0"></span>****在等1人<a href="javascript:;" name="payurl" target="_blank">去拼单</a></li>
                <li><span id="em1"></span>****在等1人<a href="javascript:;" name="payurl" target="_blank">去拼单</a></li>
                <li><span id="em2"></span>****在等1人<a href="javascript:;" name="payurl" target="_blank">去拼单</a></li>
            </ul>
        </div>
        <a href="javascript:;" name="payurl" target="_blank" class="btn8"></a>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:inline="javascript">
    var www="../";
    $(document).ready(function () {
        var uid = "";
        var pid = "";
        var uname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var allpoint =  /*[[${allPoint}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        var hasCoupon = false;
        var hasExChangeRecord = false;
        var actcode = /*[[${actcode}]]*/ "0"

        if (!channelcode) {
            channelcode = "A12050";
        }

        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
            hasCoupon =  /*[[${hasCoupon100}]]*/ false;
            hasExChangeRecord = /*[[${hasExChangeRecord}]]*/ false;
        }

        var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=66&channelcode=" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("非小智盈用户，不能参与本次活动。");
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        });
        //立即领取
        $("#btn_addPoint").click(function () {
            var _this = $(this);
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return;
            }
            $.getJSON("addpointrecored", { uid: uid, pid: pid,actcode:actcode }, function (data) {
                if (data.code == "200") {
                    $(".pf4").hide();
                    $(".pf4h").show();

                    allpoint = parseInt(allpoint) + 30;
                    if (allpoint >= 60 && !hasCoupon) {
                        //弹出兑换窗口
                        $(".bg").show();
                        $(".tc6").show();
                    }
                } else {
                    layer.msg(data.msg);
                }
                //推送cmp
                pushdatatocmp(uname, "ACRenew20210104");
            });
        });
        //立即兑换
        $("#btn_exchange").click(function () {
            var $this = $(this);
            var productid = $(this).attr("data-productid");

            $.ajax({
                type: 'get',
                url: "pointOrderExchange?&productId=" + productid+"&actcode="+actcode,
                dataType: 'json',
                success: function (data) {
                    if (data.code == "200") {
                        $(".tc4").show();
                        $(".tc6").hide();

                        $(".bott").hide();
                        $(".bott3").show();

                        $(".img158").show();
                        $(".img188").hide();

                    } else {
                        layer.msg(data.msg);
                        return;
                    }
                }
            });
        });

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        } else {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("非小智盈用户，不能参与本次活动。");
                return false;
            }
            //有优惠券弹出优惠券窗口
            if(hasCoupon){
                setTimeout(function (){
                    $(".bg").show();
                    $(".tc5").show();
                },3000);
            }else{
                if(!hasExChangeRecord){
                    if(parseInt(allpoint)>=60){
                        setTimeout(function (){
                            $(".bg").show();
                            $(".tc6").show();
                        },3000);
                    }else{
                        RandEMCode();
                        setTimeout(function (){
                            $(".bg").show();
                            $(".tc8").show();
                        },3000);
                    }
                }
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        }

        $(".btn5").click(function () {
            $(".bg").hide();
            $(".tc6").hide();
        });
        $(".close").click(function (){
            var type = $(this).attr("data-type");
            if(type == "open"){
                $(".tc4").hide();
                $(".tc5").show();
            }else {
                $(".bg").hide();
                $("[flag=pop]").hide();
            }
        });
    });
    function RandEMCode() {
        for (var y = 0; y < 3; y++) {
            var code = Math.floor(Math.random() * (999 - 100 + 1) + 100);
            var emcode = "Emy" + code;

            $("#em" + y).html("Emy" + code);
        }
    }
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "http://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:AdClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
</script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs=setInterval("SetTimeout(2022,4,5,00,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0"+i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a = new Date("2022/4/5 00:00:00");
    var b = new Date("2022/3/26 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("0");
        $(".s").html("00");
        $(".f").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs").show();
    }else{
        $(".djs").hide();
    }
</script>

<script>

    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";

    $(window).scroll(function(e){
        if(($(window).scrollTop() > 206) && (al1 == "0")){
            $("[name='al1']").attr("src","http://www.emoney.cn/dianjin/mf/xzy-tqs11/pc/al.html");
            al1 = "1";
        }
        if(($(window).scrollTop() > 1050) && (al2 == "0")){
            $("[name='al2']").attr("src","http://www.emoney.cn/dianjin/mf/sslt3/sslt.html");
            al2 = "1";
        }
        if(($(window).scrollTop() > 2470) && (al3 == "0")){
            $("[name='al3']").attr("src","http://emoney.gensee.com/webcast/site/vod/play-1a8509f5f55948c4938dfaaf357e8b65");
            al3 = "1";
        }
        if(($(window).scrollTop() > 3890) && (al4 == "0")){
            $("[name='al4']").attr("src","http://www.emoney.cn/dianjin/mf/m-ggsfz/ggsfz.html");
            al4 = "1";
        }
        if(($(window).scrollTop() > 5090) && (al5 == "0")){
            $("[name='al5']").attr("src","http://www.emoney.cn/dianjin/mf/dblb2/dblb.html");
            al5 = "1";
        }
        if(($(window).scrollTop() > 6090) && (al6 == "0")){
            $("[name='al6']").attr("src","http://www.emoney.cn/dianjin/mf/m-tydp/tydp.html");
            al6 = "1";
        }
    });

</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='http://api2.tongji.emoney.cn/scripts/emoneyanalyticspv.js%3Fcode=E9B1C7CAEA688BB4E9AEC91549ABE92A' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>