<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew588/20220714/css/style.css'}" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        .tc4{display: none;}
    </style>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1">
    <div class="main"><div class="djs"><div class="t">0</div>
        <div class="s">00</div>
        <div class="f">00</div><div class="m">00</div></div></div></div>
<div class="img_2"></div>
<div class="img_3">
</div>
<div class="img_4"><div class="main"><a href="javascript:void(0)" name="payurl" target="_blank" class="btn1 dh"></a></div></div>
<div class="img_5"></div>
<div class="img_6"></div>
<div class="img_7"></div>
<div class="img_8"></div>
<div class="img_9">
    <div class="main"><a href="javascript:void(0)" name="payurl" target="_blank" class="btn2 dh"></a><!--状态2<a href="" class="btn2b dh"></a>--></div></div>
<div class="img_10">
    <div class="main"><iframe  frameborder="0" webkitallowfullscreen="true" mozallowfullscreen="true" allowfullscreen="true" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=d14c22c6a4aa42c8bb7d8796ab86d2a6&rep=1&py=0" class="sp" width="0" Height="0" scrolling="no"></iframe>
        <a href="javascript:void(0)" name="payurl" target="_blank" class="btn3 dh"></a></div></div>
<div class="img_11">
    <div class="main"><a href="javascript:void(0)" name="payurl" target="_blank" class="btn4 i1 dh"></a></div></div>
<div class="img_12">
    <div class="ico2"></div>
    1、活动有效时间：2022年7月14日-2022年8月1日。<br />
    2、活动参与对象：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。<br />
    3、福利发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限和【五星研报】权限，并发放50积分（冻结状态）。适当性测评完成30天后，50积分解冻，可在积分商城使用。<br />
    4、打卡活动参与权限：<br />
    ①限完成指定续费活动用户参与。<br />
    ②仅限使用续费时的手机号参与打卡。续费后更换手机号，则无法完成打卡。<br />
    ③用户EM账号过期，则无法打卡。<br />
    5、打卡及使用期赠送规则：<br />
    ①登录打卡页面视为有效打卡；一个自然周内打卡满3天，可领取7天使用期；<br />
    ②打卡活动最多送365天使用期，可与续费所得使用期叠加。<br />
    ③打卡活动自第一次打卡之日起持续52周；若参加多轮打卡活动，则打卡周期累计顺延。<br />
    ④每周一0点刷新打卡天数，未领取的使用期自动作废。<br />
    ⑤若参加打卡后退单，赠送的使用期也随之失效。
    <br />
</div>
<div class="img_13"></div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<a href="javascript:;" id="btnIM" class="server"></a>
<div class="bott">
    <div class="main">
        <div id="btn_openexchange" data-productid="365" class="ico" th:style="'cursor:pointer;display:'+@{(${allPoint>=60} ? 'block' : 'none')}+''">您有1张<span class="org">¥30优惠券</span>可兑换</div>
        <a href="javascript:void(0)" name="payurl" target="_blank" ><div class="ico" th:style="'display:'+@{(${hasCoupon30} ? 'block' : 'none')}+''">您有1张<span class="org">¥30优惠券</span>可抵扣</div></a>
        <a href="javascript:void(0)" name="payurl" target="_blank" ><div class="ico" th:style="'display:'+@{(${hasCoupon100} ? 'block' : 'none')}+''">您有1张<span class="org">¥100优惠券</span>可抵扣</div></a>
        <a href="javascript:void(0)" name="payurl" target="_blank" class="btn4 i2 dh"></a>
    </div>
</div>
<div class="bg" style="display: none">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <!--立即兑换-->
    <div class="tc4" id="tc_exchange"><a href="javascript:;" class="close"></a>
        <a href="javascript:;" id="btn_exchange" data-productid="365"><img th:src="@{${staticPath}+'static/renew588/20220714/images/tc1.png'}" alt=""></a>
    </div>
    <!--兑换成功-->
    <div class="tc4" id="tc_exchange1"><a href="javascript:;" class="close"></a>
        <a href="javascript:void(0)" name="payurl" target="_blank"><img th:src="@{${staticPath}+'static/renew588/20220714/images/tc2.png'}" alt=""></a>
    </div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:inline="javascript">
    var www="../";
    $(document).ready(function () {
        var uid = "";
        var pid = "";
        var uname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var allpoint =  /*[[${allPoint}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        var hasCoupon = false;
        var hasExChangeRecord = false;
        var actcode = /*[[${actcode}]]*/ "0"

        if (!channelcode) {
            channelcode = "A12050";
        }

        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
            hasCoupon =  /*[[${hasCoupon100}]]*/ false;
            hasExChangeRecord = /*[[${hasExChangeRecord}]]*/ false;
        }

        var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=76&channelcode=" + channelcode + "&" + location.search;
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search;
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("非小智盈用户，不能参与本次活动。");
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        });

        $("#btn_openexchange").click(function () {
            $(".bg").show();
            $("#tc_exchange").show();
        });

        //立即兑换
        $("#btn_exchange").click(function () {
            var $this = $(this);
            var productid = $(this).attr("data-productid");

            $.ajax({
                type: 'get',
                url: "pointOrderExchange?&productId=" + productid+"&actcode="+actcode,
                dataType: 'json',
                success: function (data) {
                    if (data.code == "200") {
                        $(".bg").show();
                        $("#tc_exchange").hide();
                        $("#tc_exchange1").show();
                    } else {
                        layer.msg(data.msg);
                        return;
                    }
                }
            });
        });

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        } else {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("非小智盈用户，不能参与本次活动。");
                return false;
            }

            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        }
        $("#btnIM").click(function (){
            goIM("7月小智盈续费活动");
        });

        $(".close").click(function (){
            $(".bg").hide();
            $(".tc4").hide();
        });
    });

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "http://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:AdClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
    function GetExternal() {
        return window.external.EmObj;
    }

    function PC_JH(type, c) {
        try {
            var obj =
                GetExternal();
            return obj.EmFunc(type, c);
        } catch (e) {
        }
    }
    //跳转 IM
    function goIM(fromname) {
        var b = new Base64();
        var ret = PC_JH('EM_FUNC_START_IM', '0,AC20220714,' + b.encode(fromname));
    }
</script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs=setInterval("SetTimeout(2022,8,2,00,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0"+i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2022/8/2 00:00:00");
    var b=new Date("2022/7/28 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("0");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs").show();
    }
</script>

<script>

    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";

    $(window).scroll(function(e){
        if(($(window).scrollTop() > 800) && (al1 == "0")){
            $("[name='al1']").attr("src","http://www.emoney.cn/dianjin/mf/bs/bs.html");
            al1 = "1";
        }
        if(($(window).scrollTop() > 1800) && (al2 == "0")){
            $("[name='al2']").attr("src","http://www.emoney.cn/dianjin/mf/m-ggsfz/ggsfz.html");
            al2 = "1";
        }
        if(($(window).scrollTop() > 2750) && (al3 == "0")){
            $("[name='al3']").attr("src","http://www.emoney.cn/dianjin/mf/sslt3/sslt.html");
            al3 = "1";
        }
        if(($(window).scrollTop() > 3690) && (al4 == "0")){
            $("[name='al4']").attr("src","http://www.emoney.cn/dianjin/mf/m-qsdd/qsdd.html");
            al4 = "1";
        }
        if(($(window).scrollTop() > 4626) && (al5 == "0")){
            $("[name='al5']").attr("src","http://www.emoney.cn/dianjin/mf/dblb2/dblb.html");
            al5 = "1";
        }
        if(($(window).scrollTop() > 5580) && (al6 == "0")){
            $("[name='al6']").attr("src","http://www.emoney.cn/dianjin/mf/tydp/tydp.html");
            al6 = "1";
        }
    });

    var videoList = $('.sp');
    var wHeigt = window.innerHeight;
    document.addEventListener('scroll',function(){
        var isPlay = false;
        //滚动条高度+视窗高度 = 可见区域底部高度
        var visibleBottom = window.scrollY + document.documentElement.clientHeight;
        //可见区域顶部高度
        var visibleTop = window.scrollY;
        for (var i = 0; i < videoList.length; i++) {
            var centerY = $(videoList[i]).offset().top+(videoList[i].offsetHeight/2);
            if(centerY>visibleTop&&centerY<visibleBottom){
                if(!isPlay) {
                    videoList[i].src.match(/py=0/) && (videoList[i].src = videoList[i].src.replace(/py=0/, 'py=1'))
                    isPlay = true
                } else {
                    videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
                }
            }else{
                videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
            }
        }
    });
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
