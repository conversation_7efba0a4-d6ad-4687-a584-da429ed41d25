<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="http://www.thymeleaf.org">
        <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>益盟操盘手智盈</title>
        <link th:href="@{${staticPath}+'static/renew588/20220621/css/style.css?v=20221208'}" rel="stylesheet" type="text/css" />
        <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
        <script type="text/javascript">
            (function() {
                openWindow();
            })();
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }

            //跳转 IM
            function goIM(fromname) {
                var b = new Base64();
                var ret = PC_JH('EM_FUNC_START_IM', '0,AC20220621,' + b.encode(fromname));
            }
        </script>
        </head>

        <body>
<div class="img2_1"></div>
<div class="img2_2">
          <div class="main">
    <div class="bg1">
              <ul>
        <li id="sign1">1天</li>
        <li id="sign2">2天</li>
        <li id="sign3">3天</li>
      </ul>
            </div>
              <a href="javascript:void(0);" class="a8h" id="receive1" recordnum=""></a>
   <!--状态2 <a href="" class="a8"></a>--> <a href="javascript:void(0);" class="btn2 an1 dh" id="signBtn">已打卡<span id="recordnum">0</span>天</a> </div>
        </div>
<div class="img2_3"><div class="main">
    <div class="txt2" style="margin-top: -30px;"><a href="javascript:void(0);" class="b1"></a> <a href="javascript:void(0);" class="b2"></a> <a href="javascript:void(0);" class="b3"></a> <a href="javascript:void(0);" class="b4"></a> <a href="javascript:void(0);" class="b5"></a> <a href="javascript:void(0);" class="b6"></a> </div>
</div></div>
<div class="img2_4">
          
        </div>
<div class="img2_5"></div>
<div class="img2_6"><ul id="receiveRecord">

</ul></div>
<div class="img2_7">
          <div class="main">
    <div class="hdgz2">1、打卡活动参与权限：<br />
        ①限完成指定续费活动的小智盈用户参与。<br />
        ②仅限使用续费时的手机号参与打卡。续费后更换手机号，则无法完成打卡。<br />
        ③用户EM账号过期，则无法打卡。<br />
        2、打卡及使用期赠送规则：<br />
        ①登录打卡页面视为有效打卡；一个自然周内打卡满3天，可领取7天使用期。<br />
      ②打卡期限自第一次打卡之日起计算，打卡活动最多送365天使用期，可与续费所得使用期叠加。<br />
      ③若参加多轮打卡活动，则打卡周期累计顺延。<br />
      ④每周一0点刷新打卡天数，未领取的使用期自动作废。<br />
      ⑤若参加打卡后退单，赠送的使用期也随之失效。<br />
        3、本活动最终解释权归益盟股份有限公司所有。
    </div>
  </div>
        </div>
<div class="footer" style="margin: 20px 0;">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
          本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<a href="javascript:void(0);" class="server" id="goIM"></a>

<div class="bg hide" id="pop4">
    <div class="tc4">
        <a href="javascript:void(0);" class="close" id="btnCloseImage"></a>
        <a href="javascript:void(0);" class="tc4_image" id="btnClickImage">
            <img th:src="@{${staticPath}+'static/renew588/20220621/images/tc5.png'}" alt="">
        </a>
    </div>
</div>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<style type="text/css">
    .hide {
        display: none;
    }
</style>

<script type="text/javascript">
    var uid = getQueryString("uid") == null ? "" : getQueryString("uid");
    var pid = getQueryString("pid") == null ? "" : getQueryString("pid");
    var date = getQueryString("date") == null ? "" : getQueryString("date");
    var hasActivity;
    $(document).ready(function () {
        $.ajax({
            type: 'get',
            url: "autoSign?uid=" + uid + "&pid=" + pid + "&date=" + date,
            dataType: 'json',
            success: function (data) {
                if (data.code == "200" || data.code == "201") {
                    if(data.data.recordNum == 1){
                        $("#sign1").addClass("on");
                    }else if(data.data.recordNum == 2){
                        $("#sign1").addClass("on");
                        $("#sign2").addClass("on");
                    }else if(data.data.recordNum == 3){
                        $("#sign1").addClass("on");
                        $("#sign2").addClass("on");
                        $("#sign3").addClass("on");
                        $("#receive1").removeClass("a8h").addClass("a8");
                    }
                    $("#receive1").attr("recordnum",data.data.recordNum);
                    $("#recordnum").html(data.data.recordNum);
                    var html = "";
                    $.each(data.data.receiveRecordList, function (a, c) {
                        html += "<li>" + c.receiveTimeZone +"打卡3天 ，领取7天智盈使用期。</li>";
                    });
                    $("#receiveRecord").html(html);
                    if(data.code == "200"){
                        if(data.data.recordNum >= 3){
                            layer.msg("成功领取7天使用期，下周继续参与！", {time: 5000});
                        }else if(data.data.recordNum > 0){
                            var num = 3 - parseInt(data.data.recordNum);
                            var msg = "再打卡"+ num +"天，送7天使用期，最高领取365天！";
                            layer.msg(msg, {time: 5000});
                        }else{
                            layer.msg("打卡活动已结束，感谢参与！", {time: 5000});
                        }
                    }else if(data.code == "201"){
                        layer.msg("本周已领取7天使用期，请下周继续参与！", {time: 5000});
                    }else{

                    }

                } else if (data.code == "512") {
                    if(data.msg == "1"){
                        hasActivity = 1;
                    }else{
                        hasActivity = 0;
                    }
                    $("#pop4").removeClass("hide");
                } else {
                    layer.msg(data.msg, {time: 5000});
                    return;
                }
            }
        });
    });

    $("#btnCloseImage").click(function () {
        $("#pop4").addClass("hide");
    });

    $("#btnClickImage").click(function () {
        if(hasActivity == 1){
            var params = window.location.search;
            window.location.href="http://r.emoney.cn/renew588sign" + params;
        }else{
            $("#pop4").addClass("hide");
            layer.msg("活动已下线，感谢参与！", {time: 5000});
        }

    });

    $("#receive1").click(function () {
        var recordnum = $("#receive1").attr("recordnum");
        if(recordnum >= 3){
            layer.msg("本周已领取7天使用期，下周继续参与！", {time: 5000});
        }else if(recordnum > 0){
            var num = 3 - parseInt(recordnum);
            var msg = "再打卡"+ num +"天，送7天使用期，最高领取365天！";
            layer.msg(msg, {time: 5000});
        }else{
            layer.msg("打卡活动已结束，感谢参与！", {time: 5000});
        }
    });

    $("#signBtn").click(function () {
        var recordnum = $("#receive1").attr("recordnum");
        if(recordnum >= 3){
            layer.msg("本周已领取7天使用期，下周继续参与！", {time: 5000});
        }else if(recordnum > 0){
            var num = 3 - parseInt(recordnum);
            var msg = "再打卡"+ num +"天，送7天使用期，最高领取365天！";
            layer.msg(msg, {time: 5000});
        }else{
            layer.msg("打卡活动已结束，感谢参与！", {time: 5000});
        }
    });

    $("#goIM").click(function () {
        goIM("小智盈续费打卡");
    });

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }
</script>
</body>
</html>
