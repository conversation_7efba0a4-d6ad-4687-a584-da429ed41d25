<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew588/20220516/css/style.css'}" rel="stylesheet" type="text/css"/>
    <style>
        .hide {
            display: none;
        }
    </style>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function () {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {
                }
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {
                }
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {
                }
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {
                }
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }

            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1">
    <div class="main">
        <div class="djs"><div class="t">0</div>
            <span class="s">23</span><span class="ss"></span><span class="f">59</span><span class="ff"></span><span class="m">59</span><span class="mm"></span>
        </div>
    </div>
</div>
<div class="img_2">
    <div class="main">
        <a class="a1 dh2"></a><a class="a2 dh2"></a>
        <a class="a3 dh2"></a><a class="a4 dh2"></a>
        <a class="a5 dh2"></a><a class="a6 dh2"></a>
        <a class="a7 dh2"></a><a class="a8 dh2"></a>
        <a href="javascript:;" class="a9 dh" name="btn_lottery"></a>
        <a href="#" class="a9_2 dh" style="display: none" name="payurl" target="_blank"></a>
    </div></div>
<div class="img_3" id="a1">
    <div class="main">
        <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al1"></iframe>
        <a href="javascript:;" class="btn2 dh" name="btn_lottery"></a>
        <!--btn2的第二种状态-->
        <a href="" class="btn2_2 dh" style="display: none" name="payurl" target="_blank"></a>
    </div></div>
<div class="img_4" id="a2">
    <div class="main">
    <a href="javascript:;" class="btn1 dh" name="btn_lottery"></a>
    <!--btn1的第二种状态-->
    <a href="" class="btn1_2 dh" style="display: none"  name="payurl" target="_blank"></a>
    </div>
</div>
<div class="img_5" id="a3">
    <div class="main" style="display: none;">
        <a href="" class="b1 dh2"></a>
        <a href="" class="b2 dh2"></a>
        <a href="" class="b3 dh2"></a>
    </div>
</div>

<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf4">
    <a href="javascript:;" id="btn_addPoint" class="pf4-btn dh" th:style="'display:'+@{(${hasPointRecord} ? 'none' : 'block')}+''"></a>
    <!--状态2-已领取-->
    <a class="pf4-btn2" th:style="'display:'+@{(${hasPointRecord} ? 'block' : 'none')}+''"></a>
</div>
<div class="pf1">
    <div class="main"><a href="javascript:;" class="btn3 dh" name="btn_lottery"></a>
    </div>
</div>
<div class="pf2" style="display: none">
  <div class="main"><a href="#" class="btn6 dh" name="payurl" target="_blank"></a>
  </div>
</div>
<div class="bg hide">
    <div class="tc hide">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <!--中奖窗口-有100元券-->
    <div class="tc3 hide" id="div_award100">
        <a href="javascript:void(0)" class="close1"></a>
        <a href="javascript:void(0)" class="close" data-tip="ljlj"></a>
        <a href="javascript:void(0)" class="btn7 dh" name="payurl" target="_blank"></a>
        <img th:src="@{${staticPath}+'static/renew588/20220516/images/tc.png'}" alt=""></div>
    <!--中奖窗口-无100元券-->
    <div class="tc3 hide" id="div_award">
        <a href="javascript:void(0)" class="close1"></a>
        <a href="javascript:void(0)" class="close" data-tip="ljyq"></a>
        <a href="javascript:void(0)" class="btn7 dh" name="payurl" target="_blank"></a>
        <img th:src="@{${staticPath}+'static/renew588/20220516/images/tc2.png'}" alt=""></div>
    <!--立即领奖-->
    <div class="tc3 hide" id="div_ljlj">
        <a href="javascript:void(0)" class="close1"></a>
        <a href="javascript:void(0)" class="btn8 dh" name="payurl" target="_blank">立即领奖</a>
        <img th:src="@{${staticPath}+'static/renew588/20220516/images/tc3.png'}" alt=""></div>
    <!--立即用券-->
    <div class="tc3 hide" id="div_ljyq">
        <a href="javascript:void(0)" class="close1"></a>
        <a href="javascript:void(0)" class="btn8 dh" name="payurl" target="_blank">立即用券</a>
        <img th:src="@{${staticPath}+'static/renew588/20220516/images/tc4.png'}" alt=""></div>
    <!--恭喜您获得一次抽奖机会-未抽奖5秒后弹出-->
    <div class="tc3 hide" id="div_awardtips">
        <a href="javascript:void(0)" class="close1"></a>
        <a href="javascript:void(0)" class="btn8 dh" name="btn_lottery">立即抽奖</a>
        <img th:src="@{${staticPath}+'static/renew588/20220516/images/tc5.png'}" alt="">
    </div>
</div>
<a href="javascript:;" id="btn_im" class="server"><img th:src="@{${staticPath}+'static/renew588/20220516/images/server.png'}" alt=""></a>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:inline="javascript">
    var www="../";
    $(document).ready(function () {
        var uid = "";
        var pid = "";
        var uname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        var hasCoupon = false;
        var actcode = /*[[${actcode}]]*/ "0"

        if (!channelcode) {
            channelcode = "A12050";
        }
        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
            hasCoupon =  /*[[${hasCoupon100}]]*/ false;
        }

        var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=69&channelcode=" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (!checkPermission(pid)) {
                return false;
            }

            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        });
        //立即领取
        $("#btn_addPoint").click(function () {
            var _this = $(this);
            if (!checkPermission(pid)) {
                return false;
            }
            $.getJSON("addpointrecored", { uid: uid, pid: pid,actcode:actcode }, function (data) {
                if (data.code == "200") {
                    $(".pf4-btn").hide();
                    $(".pf4-btn2").show();
                } else {
                    layer.msg(data.msg);
                }
                //推送cmp
                pushdatatocmp(uname, "ACRenew20210104");
            });
        });

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        } else {
            if (!checkPermission(pid)) {
                return false;
            }
            initAwardState(actcode,uid);
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
        }
        //goIM
        $("#btn_im").click(function (){
            goIM("小智盈5月抽奖");
        });
        //抽奖
        $("[name=btn_lottery]").click(function (){
            if (!checkPermission(pid)) {
                return false;
            }
            addCount(actcode,uid,hasCoupon);
        });

        //再看看
        $(".close").click(function () {
            var tipname = $(this).attr("data-tip");
            $(".tc3").hide();
            $("#div_" + tipname).show();
        });

        //关闭
        $(".close1").click(function (){
            $(".bg").hide();
            $(".tc3").hide();
        });
    });
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:AdClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
</script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script>
    // 检查用户是否有权限参与
    function checkPermission(pid) {
        if (pid != "888010000" && pid != "888010400") {
            layer.msg("本活动仅限小智盈用户参与");
            return false;
        }
        return true;
    }
    // 发送优惠券
    function sendCoupon100() {
        $.post("sendCoupon100", {}, function (data) {
            if (data == null) {
                layer.msg("领取异常，请稍后重试！");
                return false;
            }
            if (data.code == 200) {
                $("#div_award100").show();
            } else {
                layer.msg(data.msg);
            }
        });
    }

    // 初始化优惠券状态
    function initAwardState(actcode,uid) {
        //判断用户是否抽奖
        $.ajax({
            type: 'get',
            url: 'https://act2017.emoney.cn/ActShort20200408/FlashSale/IsSubmit?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if(data.retCode=="0"){
                    //抽过奖
                    $(".a9,.pf1,.btn1,.btn2").hide();
                    $(".a9_2,.pf2,.btn1_2,.btn2_2").show();
                }if(data.retCode=="-2"){
                    //未申请过,5秒后弹出提醒抽奖
                    setTimeout(function (){
                        $(".bg").show();
                        $(".tc3").hide();
                        $("#div_awardtips").show();
                    },5000);
                }
            }
        });
    }
    //抽奖记录
    function addCount(actcode,uid,hasCoupon){
        //名额-1
        $.ajax({
            type: 'get',
            url: 'https://act2017.emoney.cn/ActShort20200408/FlashSale/AddCountByActCode?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                uid: uid ,
                value: "1"
            },
            success: function (data) {
                if(data.retCode=="0"){
                    //申请成功
                    $(".bg").show();
                    $(".tc3").hide();

                    $(".a9,.pf1,.btn1,.btn2").hide();
                    $(".a9_2,.pf2,.btn1_2,.btn2_2").show();
                    if(hasCoupon){
                        $("#div_award").show();
                    }else{
                        //赠送一张100元优惠券
                        sendCoupon100();
                    }
                }
            }
        });
    }
    function GetExternal() {
        return window.external.EmObj;
    }

    function PC_JH(type, c) {
        try {
            var obj =
                GetExternal();
            return obj.EmFunc(type, c);
        } catch (e) {
        }
    }
    //跳转 IM
    function goIM(fromname) {
        var b = new Base64();
        var ret = PC_JH('EM_FUNC_START_IM', '0,AC20220511,' + b.encode(fromname));
    }

</script>
<script>
    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";

    $(window).scroll(function(e){
        if(($(window).scrollTop() > 1300) && (al1 == "0")){
            $("[name='al1']").attr("src","https://www.emoney.cn/dianjin/mf/xzy-tqs11/pc/al2.html");
            al1 = "1";
        }
    });

    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs=setInterval("SetTimeout(2022,6,21,00,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2022/6/21 00:00:00");
    var b=new Date("2022/6/16 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("0");
        $(".s").html("00");
        $(".f").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs").show();
    }
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>

</body>
</html>
