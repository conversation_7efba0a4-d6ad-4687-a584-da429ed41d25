<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew588/20220915/css/style.css'}" rel="stylesheet" type="text/css"/>
    <style>
        .hide {
            display: none;
        }
    </style>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function () {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {
                }
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {
                }
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {
                }
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {
                }
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }

            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1">
</div>
<div class="img_2">
    <div class="main"><a href="#a1" class="a1 dh2"></a><a href="#a2" class="a2 dh2"></a>
        <a href="#a3" class="a3 dh2"></a>
    </div>
</div>
<div class="img_3" id="a1">
    <div class="main">
        <iframe frameborder="0" src="about:blank" scrolling="no" class="al2" name="al2"></iframe>
        <a style="top: 640px" href="" class="btn1 dh btn-go-order" clickkey="renew588_buy" clickdata="20220915" clickremark="北上资金"></a></div>
</div>
<div class="img_4" id="a2">
    <div class="main">
        <iframe frameborder="0" src="about:blank" scrolling="no" class="al1" name="al1"></iframe>
        <a href="" class="btn1 dh btn-go-order" clickkey="renew588_buy" clickdata="20220915" clickremark="北上资金指标"></a>
    </div>
</div>
<div class="img_5" id="a3">
    <div class="main" id="div-zf">
        <a href="" class="b1 dh2" value="0"></a>
        <a href="" class="b2 dh2" value="1"></a>
        <a href="" class="b3 dh2" value="2"></a>
        <a href="" class="btn2 dh btn-go-order" clickkey="renew588_buy" clickdata="20220915" clickremark="领取战法"></a>
    </div>
</div>
<div class="hdgz">1、活动有效时间：2022年9月15日-2022年9月30日<br/>
    2、活动参与对象：仅限智盈老用户参与本活动1次。智盈大师或其他产品用户不参与本续费活动。<br/>
    3、活动福利发放：完成续费和适当性测评后开通【智盈软件使用期】权限，并发放积分（冻结状态）。<br/>
    适当性测评完成30天后，积分解冻，可在积分商城使用。<br/>
    4、优惠券规则：尝鲜券仅限本活动使用。
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a>
    股市有风险，投资需谨慎<br/>
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340
</div>
<div class="pf4">仅剩<span id="coupon_count">1888</span>张<a href="javascript:void(0)" class="pf4-btn dh" clickkey="renew588_get_coupon" clickdata="20220915" clickremark=""></a></div>
<div class="pf1">
    <div class="main"><a href="#" class="btn3 dh btn-go-order" clickkey="renew588_buy" clickdata="20220915" clickremark="立即开通"></a>
    </div>
</div>
<div class="bg bg-login hide">
    <div class="tc">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text"/>
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text"/>
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode"/>
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc2"><a href="javascript:void(0)" class="close"></a>请输入购买智盈产品时用的手机号</div>
</div>

<div class="bg bg-zf hide">
    <div class="tc4"><a href="" class="close"></a>
        <img class="hide" th:src="@{${staticPath}+'static/renew588/20220915/images/p1.png'}" alt="">
        <img class="hide" th:src="@{${staticPath}+'static/renew588/20220915/images/p2.png'}" alt="">
        <img class="hide" th:src="@{${staticPath}+'static/renew588/20220915/images/p3.png'}" alt="">
        <a href="" class="btn5 dh btn-go-order" clickkey="renew588_buy" clickdata="20220915" clickremark="2大专属战法"></a>
    </div>
</div>
<div class="bg bg-received hide">
    <div class="tc3 hide">
        <a class="close"></a>
        <a class="btn-go-order" style="cursor: pointer">
            <img th:src="@{${staticPath}+'static/renew588/20220915/images/tc.png'}" alt="">
        </a>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:inline="javascript">
    var actcode = /*[[${actcode}]]*/ "0"
    var couponCount = 1888;
    var www = "../";
    var limitMsg = "本活动仅限智盈老用户参与。";
    var hasCoupon = false;
    var hasCouponConfirm = false;
    var hasCouponConfirmText = "您还未领取200元尝鲜券，券后支付更划算！";
    var uid = "";
    var pid = "";
    var uname = "";
    var payUrl = "";
    var initCountHandler = null;

    $(document).ready(function () {
        var isLogin = /*[[${isLogin}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");

        if (!channelcode) {
            channelcode = "A12050";
        }
        RandEMCode();
        InitCount();
        if (isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
            hasCoupon =  /*[[${hasCoupon200}]]*/ false;
            initCouponState(hasCoupon);
        }

        payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=81&channelcode=" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }

        // 点击解锁
        $(".div-unlock .btn4").click(function () {
            return false;
        });

        // 点击具体专属战法 弹出对应的介绍弹窗
        initZFClick();

        // 初始化领取优惠券点击事件
        initReceiveCouponClick();

        // 初始化立即开通 去下单点击事件
        initGoOrderClick();

        if (isLogin == "0") {
            //未登录
            $(".bg-login").show();
            $(".tc").show();
        } else {
            if (!checkPermission(pid)) {
                return false;
            }

            //推送cmp
            //pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");

        }

        // 初始化关闭按钮事件
        initCloseClick();

    });

</script>
<script>

    // 发送优惠券
    function sendCoupon200() {
        $.post("sendCoupon200_0915", {}, function (data) {
            if (data == null) {
                layer.msg("领取异常，请稍后重试！");
                return false;
            }
            if (data.code == 200) {
                hasCoupon = true;
                initCouponState(hasCoupon);
                addCount(uid, hasCoupon);
                //layer.msg("领取成功");
                $(".tc3").show();
                $(".bg-received").show();
            } else {
                layer.msg(data.msg);
            }
        });
    }

    // 初始化领取优惠券点击事件
    function initReceiveCouponClick() {
        $(".pf4-btn").click(function () {
            var $this = $(this);
            receiveCoupon($this, pid);
            return false;
        });
    }

    // 初始化立即开通 去下单点击事件
    function initGoOrderClick() {
        $(".btn-go-order").click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            // 没有领优惠券并且没有提示过用户
            if (!hasCoupon && !hasCouponConfirm) {
                layer.msg(hasCouponConfirmText);
                hasCouponConfirm = true;
                return false;
            }
            window.open(payUrl);
            //推送cmp
            //pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
            return false;
        });
    }

    // 初始化关闭按钮事件
    function initCloseClick() {
        $(".close").click(function () {
            $(".bg").hide();
            $(".tc3").hide();
            $(".tc4").hide();
            $(".tc5").hide();
            return false;
        });
    }

    // 点击具体专属战法 弹出对应的介绍弹窗
    function initZFClick() {
        $("#div-zf .dh2").click(function () {
            var v = $(this).attr("value");
            $(".bg-zf img").hide().eq(v).show();
            $(".bg-zf").show();
            $(".tc4").show();
            return false;
        });
    }

    function RandEMCode() {
        for (var y = 0; y < 15; y++) {
            var code = Math.floor(Math.random() * (999 - 100 + 1) + 100);
            var emcode = "Emy" + code;

            $("#em" + y).html("Emy" + code);
        }
    }

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "http://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:AdClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }

    //名额-1
    function addCount(uid, hasCoupon) {
        $.ajax({
            type: 'get',
            url: window.location.protocol+'//act2017.emoney.cn/ActShort20200408/FlashSale/AddCountByActCode?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                uid: uid,
                value: "1"
            },
            success: function (data) {
                if (data.retCode == "0") {
                    //申请成功 更新数量
                    clearTimeout(initCountHandler)
                    InitCount()
                }
            }
        });
    }

    function InitCount() {
        var initCount = couponCount;
        $.ajax({
            type: 'get',
            url: window.location.protocol+'//act2017.emoney.cn/ActShort20200408/FlashSale/GetCountByActCode?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                initCount: initCount
            },
            success: function (data) {
                if (data.retCode == 0) {
                    if (data.Message == "") {
                        data.Message = 0;
                    }
                    var usedCount = parseInt(data.Message);
                    $("#coupon_count").html(initCount - parseInt(usedCount));//剩余席位

                    initCountHandler = setTimeout("InitCount()", 60000 * 5);
                }
            }
        });
    }

    function receiveCoupon($this, pid) {
        if (!checkPermission(pid)) {
            return false;
        }
        // 检查是否已经领取
        if (hasCoupon) {
            return false;
        }
        // 发优惠券
        sendCoupon200();
    }

    // 初始化优惠券状态
    function initCouponState(hasCoupon) {
        if (hasCoupon) {
            $(".pf4-btn").removeClass("pf4-btn").addClass("pf4-btn2").attr("clickkey","renew588_get_coupon_already");
            $(".pf1").removeClass("pf1").addClass("pf2");
        } else {
            $(".pf4-btn2").removeClass("pf4-btn2").addClass("pf4-btn");
            $(".pf2").removeClass("pf2").addClass("pf1");
        }
    }

    // 检查用户是否有权限参与
    function checkPermission(pid) {
        if (pid != "888010000" && pid != "888010400") {
            layer.msg(limitMsg);
            return false;
        }
        return true;
    }
</script>

<script>
    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";

    $(window).scroll(function (e) {
        if (($(window).scrollTop() > 1100) && (al1 == "0")) {
            $("[name='al1']").attr("src", "https://www.emoney.cn/dianjin/mf/m-bszjzb/bszj.html");
            al1 = "1";
        }

        if(($(window).scrollTop() > 300) && (al2 == "0")){
            $("[name='al2']").attr("src","https://www.emoney.cn/dianjin/mf/bszj6/bszj.html");
            al2 = "1";
        }
    });

</script>
<script src="https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js" type="text/javascript"></script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588";//模块名称
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=E9B1C7CAEA688BB4E9AEC91549ABE92A' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
