<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew588/20230301/css/style.css'}" rel="stylesheet" type="text/css"/>
    <style>
        .hide {
            display: none;
        }
    </style>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
    <script type="text/javascript">

        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj =
                    GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {
            }
        }

        // 打开积分窗口
        function OpenPointWindow() {
            PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "17,http://static.emoney.cn/ds/point/index.html");
        }

        (function () {
            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {
                }
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {
                }
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {
                }
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }

            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1">
    <div class="main">
        <div class="djs">
            <div class="t">0</div>
            <div class="s">00</div>
            <div class="f">00</div>
            <div class="m">00</div>
        </div>
    </div>
</div>
<div class="img_2"></div>
<div class="img_3">
    <div class="main">
        <div class="t1">
            <div class="txt2 point_state state_1 hide">恭喜您获得30积分</div>
            <a href="" class="btn an1 point_state state_1 hide btn-go-order" clickkey="go-order" clickdata="btn1" clickremark="助力礼1-去用券">去用券
                <div class="ico"></div>
            </a>
            <div class="txt2 point_state state_2">恭喜您获得30积分<!--<br/>100积分=¥100券--></div>
            <a href="" class="btn an1 point_state state_2 btn_exchange" clickkey="go-exchange" clickdata="btn1" clickremark="积分-去兑券">去兑券
                <div class="ico"></div>
            </a>
            <div class="txt3 point_state state_3 hide">恭喜您获得30积分<br/>还差<span id="point_left"></span>积分可兑¥100券
            </div>
            <a href="" id="btn_go_point" class="btn an1 point_state state_3 hide" clickkey="earn-points" clickdata="btn1" clickremark="去赚积分">去赚积分</a>
        </div>
        <div class="t2">
            <a href="" class="btn2 privilege_state privilege_state_1" clickkey="get-privilege" clickdata="btn2" clickremark="领取特权2"></a>
            <a href="" class="btn an1 privilege_state privilege_state_1" clickkey="get-privilege" clickdata="btn1" clickremark="领取特权">领取</a>
            <div class="txt2 privilege_state privilege_state_2 hide">恭喜您领取成功</div>
            <a href="#ad_first" class="btn an1 privilege_state privilege_state_2 hide" clickkey="look-privilege" clickdata="btn1" clickremark="查看北上资金">查看功能介绍</a>
        </div>
        <div class="t3"><a href="#" class="btn2 btn-go-im" clickkey="go-im" clickdata="btn1" clickremark="IM-北上讲义"></a><a href="#" class="btn an2 btn-go-im" clickkey="go-im" clickdata="btn2" clickremark="IM-北上讲义">找专员领
            <div class="txt1">回复：BZ</div>
        </a></div>
    </div>
</div>
<div class="img_4" id="ad_first">
    <div class="main">
        <a href="#" class="btn8 dh privilege_state privilege_state_1" clickkey="get-privilege" clickdata="btn3" clickremark="领取特权3"></a>
        <a href="javascript:void(0);" class="btn8h privilege_state privilege_state_2 hide"></a>
    </div>
</div>
<div class="img_5"></div>
<div class="img_6">
    <div class="main">
        <iframe frameborder="0" src="http://www.emoney.cn/dianjin/mf/bszj6/bszj.html" scrolling="no" class="sp"
                name="al3"></iframe>
        <a href="#" class="btn8 dh privilege_state privilege_state_1" clickkey="get-privilege" clickdata="btn4" clickremark="领取特权4"></a>
        <a href="javascript:void(0);" class="btn8h privilege_state privilege_state_2 hide"></a>
    </div>
</div>
<div class="img_7">
    <div class="main">
        <div class="hdgz">1、活动有效时间：2023年3月1日-2023年3月31日。<br/>
            <br/>
            2、活动参与对象：仅限智盈老用户参加本活动续费单个商品1次，智盈大师或其他产品用户不参与本续费活动。<br/>
            <br/>
            3、续费福利发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限和【北上资金】权限。<br/>
            <br/>
            4、优惠券规则：持本活动有效优惠券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用。
        </div>
    </div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a>
    股市有风险，投资需谨慎<br/>
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340
</div>
<a href="#" class="server btn-go-order" clickkey="go-order" clickdata="btn2" clickremark="右侧-去续费"></a>
<div class="bott">
    <div class="main"><a href="" class="btn9 dh btn-go-order" clickkey="go-order" clickdata="btn3" clickremark="底部-立即续费"></a>
    </div>
</div>
<div class="bg bg-login hide">
    <div class="tc">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text"/>
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text"/>
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode"/>
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;"
                                                                                class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc2"><a href="javascript:void(0)" class="close"></a>请输入购买智盈产品时用的手机号</div>
</div>
<div class="bg bg_tc div_tc hide">
    <div class="tc1 div_tc hide btn_exchange"><a href="" class="close"></a>
        <a href="" class="btn an3 dh" clickkey="go-exchange" clickdata="btn2" clickremark="积分-立即兑换">立即兑换</a>
    </div>
    <div class="tc2 div_tc hide"><a href="" class="close"></a>
        <a href="" class="btn an3 dh btn-go-order" clickkey="go-order" clickdata="btn4" clickremark="弹窗-立即续费">立即续费</a>
    </div>
    <div class="tc3 div_tc hide"><a href="" class="close"></a>
        <a href="" class="btn an3 dh btn-go-order" clickkey="go-order" clickdata="btn5" clickremark="弹窗-立即使用">立即使用</a>
    </div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actcode}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:inline="javascript">
    var actcode = /*[[${actcode}]]*/ "0"
    var allPoint = /*[[${allPoint}]]*/ "0"
    var www = "../";
    var limitMsg = "本活动仅限智盈老用户参与。";
    var hasCoupon = false;
    var hasPrivilege = false;
    var hasCouponConfirm = false;
    var hasCouponConfirmText = "您还未领取100元尝鲜券，券后支付更划算！";
    var uid = "";
    var pid = "";
    var uname = "";
    var payUrl = "";
    var initCountHandler = null;
    var shouldPop=false;
    var hasPop=false;

    $(document).ready(function () {
        var isLogin = /*[[${isLogin}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");

        if (!channelcode) {
            channelcode = "A12050";
        }
        RandEMCode();
        if (isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
            hasCoupon =  /*[[${hasCoupon100}]]*/ false;
            hasPrivilege =  /*[[${hasPrivilege}]]*/ false;
            hasPop =  /*[[${hasPop}]]*/ false;
            initState(hasCoupon, allPoint);
            initPrivilege(hasPrivilege);
        }

        payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=91&channelcode=" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }


        // 初始化立即开通 去下单点击事件
        initGoOrderClick();

        if (isLogin == "0") {
            //未登录
            $(".bg-login").show();
            $(".tc").show();
        } else {
            if (!checkPermission(pid)) {
                return false;
            }

            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");

        }

        // 初始化关闭按钮事件
        initCloseClick();

        // 初始化积分兑换事件
        initPointOrderExchange();

        // 初始化打开积分窗口事件
        initGoPoint();

        // 跳转IM
        initGoIM();

        initGetPrivilege();

        initAutoPop();
    });

</script>

<script>


    // 如果满足要求 自动弹窗
    function initAutoPop() {
        setTimeout(function () {
            if(shouldPop&&!hasPop){
                $(".div_tc").hide();
                $(".tc1").show();
                $(".bg_tc").show();
                $.post("pop20230301?uid=" + uid ,function (){})
            }
        }, 3000)
    }

    // 领取特权
    function initGetPrivilege() {
        $(".privilege_state_1").click(function () {
            $.ajax({
                type: 'post',
                url: "send_privilege_20230301?pid=" + pid + "&actcode=" + actcode,
                dataType: 'json',
                success: function (data) {
                    if (data.code == "200") {
                        // 兑换成功
                        $(".div_tc").hide();
                        $(".tc2").show();
                        $(".bg_tc").show();
                        $(".privilege_state").hide();
                        $(".privilege_state_2").show();
                    } else {
                        layer.msg(data.msg);
                        return;
                    }
                }
            });

            return false;
        });
    }

    //跳转 IM
    function goIM(fromname) {
        var b = new Base64();
        var ret = PC_JH('EM_FUNC_START_IM', '0,AC20230301,' + b.encode(fromname));
        return false;
    }

    function initGoIM() {
        $(".btn-go-im").click(function () {
            goIM("小智盈续费3月福利活动");
            return false;
        });
    }

    function initGoPoint() {
        $("#btn_go_point").click(function () {
            OpenPointWindow();
            return false;
        });
    }

    // 初始化优惠券状态
    function initState(hasCoupon, allPoint) {
        if (hasCoupon) {
            $(".point_state").hide();
            $(".state_1").show();
        } else if (!hasCoupon && allPoint >= 100) {
            $(".point_state").hide();
            $(".state_2").show();
            shouldPop=true;
        } else if (!hasCoupon && allPoint < 100) {
            $(".point_state").hide();
            $("#point_left").html(100 - allPoint);
            $(".state_3").show();
        }
    }

    function initPrivilege(hasPrivilege) {
        if (hasPrivilege) {
            $(".privilege_state").hide();
            $(".privilege_state_2").show();
        } else {
            $(".privilege_state").hide();
            $(".privilege_state_1").show();
        }
    }

    function initPointOrderExchange() {
        //立即兑换
        $(".btn_exchange").click(function () {
            var $this = $(this);
            $.ajax({
                type: 'get',
                url: "pointOrderExchange?productId=599&actcode=" + actcode,
                dataType: 'json',
                success: function (data) {
                    if (data.code == "200") {
                        // 兑换成功
                        $(".point_state").hide();
                        $(".state_1").show();
                        $(".div_tc").hide();
                        $(".tc3").show();
                        $(".bg_tc").show();
                        shouldPop=false;
                    } else {
                        layer.msg(data.msg);
                        return;
                    }
                }
            });
            return false;
        });
    }


    // 初始化立即开通 去下单点击事件
    function initGoOrderClick() {
        $(".btn-go-order").click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            // 没有领优惠券并且没有提示过用户
            // if (!hasCoupon && !hasCouponConfirm) {
            //     layer.msg(hasCouponConfirmText);
            //     hasCouponConfirm = true;
            //     return false;
            // }
            window.open(payUrl);
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
            return false;
        });
    }

    // 初始化关闭按钮事件
    function initCloseClick() {
        $(".close").click(function () {
            $(".bg").hide();
            $(".tc1").hide();
            $(".tc2").hide();
            $(".tc3").hide();
            return false;
        });
    }

    function RandEMCode() {
        for (var y = 0; y < 15; y++) {
            var code = Math.floor(Math.random() * (999 - 100 + 1) + 100);
            var emcode = "Emy" + code;

            $("#em" + y).html("Emy" + code);
        }
    }

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "http://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:AdClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }

    // 检查用户是否有权限参与
    function checkPermission(pid) {
        if (pid != "888010000" && pid != "888010400") {
            layer.msg(limitMsg);
            return false;
        }
        return true;
    }
</script>

<script>
    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";

    $(window).scroll(function (e) {
        if (($(window).scrollTop() > 1300) && (al1 == "0")) {
            $("[name='al1']").attr("src", "http://www.emoney.cn/dianjin/mf/m-bszjzb/bszj.html");
            al1 = "1";
        }
    });

</script>

<script>
    function SetTimeout(year, month, day, hour, minute, second) {
        var leftTime = (new Date(year, month - 1, day, hour, minute, second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }

    djs = setInterval("SetTimeout(2023,3,31,24,00,00)", 1000);

    function checkTime(i) { //将0-9的数字前面加上0，例1变为01
        if (i < 10) {
            i = "0" + i;
        }
        return i;
    }

    SetTimeout();

    var classdate = new Date();
    var a = new Date("2023/3/31 24:00:00");
    var b = new Date("2023/3/27 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("0");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs").show();
    }

</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20230301";//模块名称(焦点图2)
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
