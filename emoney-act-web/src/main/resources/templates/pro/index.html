<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="操盘手Pro 全新来袭，免费升级！新版更有料 又稳又高效"/>
    <link th:href="@{${staticPath}+'static/pro/style/css.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj =
                    GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {}
        }
        (function() {
            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"></div>
<div class="img_3"></div>
<div class="img_4"><div class="main"><a href="javascript:void(0)" class="txt1 b">点击查看功能教学视频</a></div></div>
<div class="img_5"></div>
<div class="img_6">
    <div class="main"><a href="javascript:void(0)" class="btn1 a1 dh" clickkey="btn" clickdata="btn"></a><a href="javascript:void(0)" class="btn1 a2 dh" clickkey="btn" clickdata="btn"></a><a href="javascript:void(0)" class="btn1 a3 dh" clickkey="btn" clickdata="btn"></a></div></div>
<div class="img_7">
    <div class="main"><div class="foot">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
        本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div></div></div>
<div class="pf"><div class="main"><a href="javascript:void(0)" class="btn2 dh" clickkey="btn" clickdata="btn"></a></div></div>
<div class="h"><div class="tc"><div class="close"></div>课程尚未开始，请先选择其他课程，如有疑问可联系服务专员</div></div>
<script>
    $(".a2").click(function(){
        $(".h").show();
        $(".tc").show();
        $(".sptc").hide();
    });
    $(".txt1").click(function(){
        $(".h").show();
        $(".tc").hide();
        $(".sptc").show();
        $('.sp').attr('src','https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=76cba2f1e11e4effbe898f03d3148108&rep=1&py=1')
    });
    $(".txt2").click(function(){
        $(".h").show();
        $(".tc").hide();
        $(".sptc").show();
        $('.sp').attr('src','https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a8803d6ce0074336825b21e50667fb51&rep=1&py=1')
    });
    $(".close").click(function(){
        $(".h").hide();
        $('.sp').attr('src','about:blank')
    });
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "cps-20250324";//操盘手pro
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
