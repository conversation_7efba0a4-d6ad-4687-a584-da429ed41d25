<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renewds/20230715/css/style.css'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <style type="text/css">
        .bt{ background-color: #FFD9AD; color: #9b4f1c; font-size: 22px;}
        .orderlist{width: 700px;
            margin: 20px;
            border-radius: 25px;display: none;}
        table,th,td {
            border : 1px solid #999;
            border-collapse: collapse; line-height: 50px;font-size: 16px; text-align: center;
            background: #fff;
        }
    </style>
    <script type="text/javascript">
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj =
                    GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {}
        }
        (function() {
            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"><ul>
    <li>恭喜EM016****成功续费，享受优惠福利</li>
    <li>恭喜EM001****成功续费，享受优惠福利</li>
    <li>恭喜EM093****成功续费，享受优惠福利</li>
    <li>恭喜EM983****成功续费，享受优惠福利</li>
    <li>恭喜EM146****成功续费，享受优惠福利</li>
    <li>恭喜EM500****成功续费，享受优惠福利</li>
    <li>恭喜EM340****成功续费，享受优惠福利</li>
    <li>恭喜EM122****成功续费，享受优惠福利</li>
    <li>恭喜EM058****成功续费，享受优惠福利</li>
    <li>恭喜EM023****成功续费，享受优惠福利</li>
    <li>恭喜EM088****成功续费，享受优惠福利</li>
    <li>恭喜EM001****成功续费，享受优惠福利</li>
    <li>恭喜EM018****成功续费，享受优惠福利</li>
    <li>恭喜EM132****成功续费，享受优惠福利</li>
    <li>恭喜EM012****成功续费，享受优惠福利</li>
    <li>恭喜EM552****成功续费，享受优惠福利</li>
    <li>恭喜EM119****成功续费，享受优惠福利</li>
    <li>恭喜EM130****成功续费，享受优惠福利</li>
    <li>恭喜EM980****成功续费，享受优惠福利</li>
</ul></div>
<div class="img_2">
    <div class="main">
        <div class="main">
            <a href="javascript:void(0)" class="btn1 dh an1" clickkey="btn" clickdata="btn" target="_blank" name="payurl">马上续费<img th:src="@{${staticPath}+'static/renewds/20230715/images/ico1.png'}" class="s1 dh2" alt=""></a>
            <!--状态2-->
            <a href="javascript:void(0)" class="btn1h an1" clickkey="btn" clickdata="btn" style="display: none;">已支付</a>
        </div>
    </div>
</div>
<div class="img_3"></div>
<div class="img_4"></div>
<div class="img_5">
    <div class="main"><a href="javascript:void(0)" class="btn4 dh" clickkey="btn2" clickdata="btn2">我要听课得使用期 &gt;&gt;&gt;<img th:src="@{${staticPath}+'static/renewds/20230715/images/ico1.png'}" class="s1 dh2" alt=""></a></div></div>
<div class="img_6">
    <div class="main"><a href="javascript:void(0)" class="btn3">活动规则</a>
        <div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
            本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div></div></div>
<div class="pf">
    <div class="main"><div class="djs"><div class="t">00</div>
        <span class="s">23</span><span class="f">59</span></div><a href="javascript:void(0)" class="btn5" target="_blank" name="payurl">我要续费</a></div></div>
<div class="bg" style="display: none">
    <div class="tc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc2"><a href="javascript:void(0)" class="close"></a>请输入购买智盈产品时用的手机号</div>
</div>
<a href="javascript:void(0)" class="wddd">我的订单</a>
<div class="h">
    <div class="hdgz"><div class="t1">1. 活动参与对象：仅限智盈大师用户参与<br />
    2. 活动时间：2023年7月17日—2023年7月31日<br />
    3. 赠送规则：完成续费和适当性测评后开通【智盈大师软件使用期】<br />
    4. 插件赠送：完成续费和适当性测评后开通【智盈大师插件功能使用期】，3年版插件功能对齐使用期最晚到账时间为2023年8月10日。<br />
    5. 插件功能说明：<br />
    ①智盈大师掘金版(三年版)含5个插件功能:黑马雷达、席位龙虎榜、机构调研、主力识别、北上强买;<br />
    ②智盈大师深度资金版(三年版)含3个插件功能:席位龙虎榜、机构调研、北上强买: 深度资金版标配功能中包含黑马雷达、主力识别功能。<br />
    6. 原有未到期插件：是指智盈大师用户续费当日有效的未到期插件功能，插件到期时间详见“我的产品”。</div>
    <div class="t2">2023/7/17-2023/7/31<br />智盈大师用户</div>
    <a href="javascript:void(0)" class="btn7">知道了</a>
    </div>
</div>
<div class="orderlist">
    <table width="100%" border="0" cellspacing="0" cellpadding="0">
        <tbody id="tbody">
        <tr>
            <td class="bt">订单号</td>
            <td class="bt">订单类型</td>
            <td class="bt">支付时间</td>
            <td class="bt">支付金额</td>
        </tr>

        </tbody>
    </table>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}" />
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var actcode = /*[[${actCode}]]*/ "0";
    var www="../";

    $(document).ready(function(){
        var uid = "";
        var pid = "";
        var uname = "";
        var realname = "";
        var maskname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var mobileX = /*[[${mobileX}]]*/ "";

        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        if (!channelcode) {
            channelcode = "A12050";
        }

        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
        }
        var payUrl = "https://pay.emoney.cn/newpayv2/JunePay/Recharge?actid=119&channelcode=" + channelcode + "&" + location.search.slice(1);
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (isLogin == "0") {
                layer.msg("您还未登录，请登录后操作。");
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACMasterPop20230717");
        });

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        }else{
            if(!!mobileX){
                getPayStatus(mobileX);
            }
        }

        $("#btn_goFirstClass,.btn4").click(function (){
            PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "10,https://static.emoney.cn/static/platformstatic/act_firstclass/202306/index.html");
        });

        $(".wddd").click(function (){
            getPayList(mobileX);
        });
        $(".btn3").click(function(){
            $(".h").show();
        });
        $(".btn7").click(function(){
            $(".h").hide();
        });
    });

    function getPayStatus(mobilex) {
        $.ajax({
            type: 'get',
            url: "getjunepaystatus?mobilex=" + mobilex,
            dataType: 'jsonp',
            success: function (data) {
                if (data.code == "200") {
                    var status = data.data;//0 未付定金  1 定金已付  2 尾款已付  -1 已退款
                    if (status == "3") {
                        $(".btn1").hide();
                        $(".btn1h").hide();

                        $(".btn5").hide();
                        $(".btn5h").hide();
                    }
                }
            }
        });
    }
    function getPayList(mobilex){
        $("#tbody tr").eq(0).siblings().remove();
        $.ajax({
            type: 'get',
            url: "getjunepaylist?mobilex=" + mobilex,
            dataType: 'jsonp',
            success: function (data) {
                if (data.code == "200") {
                    var list = data.data;
                    if(!!list && list.length>0){
                        for(var i=0;i<list.length;i++){
                            var item = list[i];

                            var trhtml = document.createElement("tr");
                            trhtml.id = "mDiv";
                            trhtml.innerHTML = "<td>"+item.OrderID+"</td><td>"+item.OrderTypeName+"</td><td>"+item.OrderTime+"</td><td>￥"+item.OrderPrice+"</td>";

                            $("#tbody").append(trhtml);
                        }
                    }else{
                        var trhtml = document.createElement("tr");
                        trhtml.id = "mDiv";
                        trhtml.innerHTML = "<td colspan='4'>您好，当前未查询到任何有效订单</td>";

                        $("#tbody").append(trhtml);
                    }


                    layer.open({
                        type: 1,
                        title: false,
                        //skin: 'layui-layer-nobg', //没有背景色
                        area: ['auto'],
                        shade: 0.6,
                        shadeClose: false,
                        content: $(".orderlist")
                    });
                }
            }
        });
    }

    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
</script>
<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数

        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs=setInterval("SetTimeout(2023,7,31,24,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0"+i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2023/7/31 24:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("00");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
</script>
<script src="https://imgtongji.emoney.cn/scripts/https/emoneyanalytics.js" type="text/javascript"></script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "dsxf-tc0715";//大师续费
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
