<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <style type="text/css">
        .bt{ background-color: #FFD9AD; color: #9b4f1c; font-size: 22px;}
        table,th,td {
            border : 1px solid #999;
            border-collapse: collapse; line-height: 50px;font-size: 16px; text-align: center;
            background: #fff;
        }
    </style>
    <script type="text/javascript">
    function GetExternal() {
        return window.external.EmObj;
    }

    function PC_JH(type, c) {
        try {
            var obj =
                GetExternal();
            return obj.EmFunc(type, c);
        } catch (e) {}
    }

    (function() {
        function LoadComplete() {
            try {
                PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
            } catch (ex) {}
        }

        function EM_FUNC_HIDE() {
            try {
                PC_JH("EM_FUNC_HIDE", "");
            } catch (ex) {}
        }

        function EM_FUNC_SHOW() {
            try {
                PC_JH("EM_FUNC_SHOW", "");
            } catch (ex) {}
        }

        function IsShow() {
            try {
                return PC_JH("EM_FUNC_WND_ISSHOW", "");
            } catch (ex) {
                return "0";
            }
        }

        function openWindow() {
            LoadComplete();
            PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
            PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
            PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

            if (IsShow() != "1") {
                EM_FUNC_SHOW();
            }
        }
        openWindow();
    })();
    </script>

    <link th:href="@{${staticPath}+'static/renewds/20230625/css/style.css'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
</head>

<body>
<div class="img_1"><ul>
    <li>恭喜EM****52已支付订金338元，成功参与活动</li>
    <li>恭喜EM****01已支付订金338元，成功参与活动</li>
    <li>恭喜EM****22已支付订金338元，成功参与活动</li>
    <li>恭喜EM****88已支付订金338元，成功参与活动</li>
    <li>恭喜EM****02已支付订金338元，成功参与活动</li>
    <li>恭喜EM****09已支付订金338元，成功参与活动</li>
    <li>恭喜EM****02已支付订金338元，成功参与活动</li>
    <li>恭喜EM****12已支付订金338元，成功参与活动</li>
    <li>恭喜EM****82已支付订金338元，成功参与活动</li>
    <li>恭喜EM****32已支付订金338元，成功参与活动</li>
    <li>恭喜EMY****41已支付订金338元，成功参与活动</li>
    <li>恭喜EMY****01已支付订金338元，成功参与活动</li>
    <li>恭喜EMY****98已支付订金338元，成功参与活动</li>
    <li>恭喜EMY****62已支付订金338元，成功参与活动</li>
    <li>恭喜EMY****31已支付订金338元，成功参与活动</li>
    <li>恭喜EMY****11已支付订金338元，成功参与活动</li>
    <li>恭喜EMY****10已支付订金338元，成功参与活动</li>
    <li>恭喜EMY****99已支付订金338元，成功参与活动</li>
    <li>恭喜EMY****49已支付订金338元，成功参与活动</li>
    <li>恭喜EMY****77已支付订金338元，成功参与活动</li>
</ul></div>
<div class="img_2">
    <div class="main">
        <div class="bg1" name="paystatus0"><a href="##" target="_blank" name="payurl1" class="btn1 dh an1">马上付订金<img th:src="@{${staticPath}+'static/renewds/20230625/images/ico1.png'}" class="s1 dh2" alt=""></a><a href="##" target="_blank" name="payurl" class="btn2 dh"></a></div>
        <!--状态2-->
        <div class="bg2" name="paystatus1"><a href="##" target="_blank" name="payurl1" class="btn1 an2">现在付尾款<img th:src="@{${staticPath}+'static/renewds/20230625/images/ico1.png'}" class="s1 dh2" alt=""></a></div>
        <!--状态3-->
        <div class="bg3" name="paystatus2"></div>
    </div>
</div>
<div class="img_3"><div class="main"><a href="javascript:void(0)" class="btn3 dh"></a></div></div>
<div class="img_4"></div>
<div class="img_5">
    <div class="main"><a href="###" class="btn4 dh">我要听课得使用期 &gt;&gt;&gt;<img th:src="@{${staticPath}+'static/renewds/20230625/images/ico1.png'}" class="s1 dh2" alt=""></a></div></div>
<div class="img_6">
    <div class="main">
        <div class="bg4">
            <ul>
                <li name="paystatus0"><span class="red2">支付338元订金</span><br />
                    参与活动享福利<a href="##" target="_blank" name="payurl1" class="btn5 dh">支付订金<img th:src="@{${staticPath}+'static/renewds/20230625/images/ico1.png'}" class="s2 dh2" alt=""></a></li>
                <!--状态2-->
                <li name="paystatus1"><span class="red2">马上支付尾款</span><br />
                    享受超值活动福利<a href="##" target="_blank" name="payurl1" class="btn5 dh">马上付尾款<img th:src="@{${staticPath}+'static/renewds/20230625/images/ico1.png'}" class="s2 dh2" alt=""></a></li>
                <!--状态3-->
                <li name="paystatus2"><span class="red2">恭喜您续费成功</span><br />
                    开通服务享受<br />超值活动福利</li>
                <li><span class="red2">持续听课打卡</span><br />送免费使用期<a href="##" id="btn_goFirstClass" class="btn5 dh">去听课<img th:src="@{${staticPath}+'static/renewds/20230625/images/ico1.png'}" class="s2 dh2" alt=""></a></li>
            </ul>
        </div>
    </div>
    <div class="t1" name="paystatus0">活动时间有限赶快参与 轻松薅羊毛</div>
    <!--状态2-->
    <div class="t1" name="paystatus1">活动时间有限马上付尾款 轻松薅羊毛</div>
    <!--状态3-->
    <div class="t1" name="paystatus2">恭喜您续费成功 享受超值活动福利</div>
</div>
<a href="###" class="wddd">我的订单</a>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="bg" style="display: none">
	<div class="tc" style="display: none">
            <div class="bt1">请登录</div>
            <div class="txt">
                <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
                <div class="red">*请填写您的真实姓名</div>
                手机号码 <input id="loginmobile" type="text" />
                <div class="red">*请输入购买智盈产品时用的手机号</div>
                <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
                <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
                <div>
                    <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
                </div>
            </div>
        </div>
		<div class="tc2"><a href="javascript:void(0)" class="close"></a>请输入购买智盈产品时用的手机号</div>
	</div>
<div class="h">
    <div class="hdgz"><div class="t1">1.支付订金代表用户参与活动的意愿，成功支付订金的用户，需进一步成功支付尾款后才可享受活动福利。<br />
        2.活动福利仅限成功支付尾款的用户享受，仅支付定金的用户无法享受活动福利。<br />
        3.支付订金时间有限（时间为2023/6/25-2023/7/31），过期未支付尾款则无法享受活动福利，视为用户主动放弃，请注意及时使用订金，如有延期则另行通知。<br />
        4.支付订金后获得的优惠福利，不能和其他同期的福利叠加享受，无法重复使用或延期使用。<br />
        5.活动期间每个用户仅能参与一次。<br />
        6.用户最终续费价格以实际支付页面上的价格为准。<br />
        7.插件赠送规则：完成续费和适当性测评后开通【智盈大师软件使用期】<br />
        8.插件赠送：完成续费和适当性测评后开通【智盈大师插件功能使用期】，3年版插件功能对齐使用期最晚到账时间为2023年8月10日。<br />
        9.插件功能说明：<br />
        ①智盈大师掘金版(三年版)含5个插件功能:黑马雷达、席位龙虎榜、机构调研、主力识别、北上强买;<br />
        ②智盈大师深度资金版(三年版)含3个插件功能:席位龙虎榜、机构调研、北上强买: 深度资金版标配功能中包含黑马雷达、主力识别功能。</div>
        <div class="t2">2023/6/25-2023/7/31<br />智盈大师用户</div>
        <div class="t3">成功支付订金338元后可参与活动，成功支付尾款后可享受续费活动优惠，优惠信息以页面内容为准。</div>
        <div class="t4">退款开启时间8月1日后，可向专员申请，成功退还订金则无法享受相关优惠。</div>
        <a href="javascript:void(0)" class="btn7">知道了</a>
    </div>
</div>
<div class="orderlist" style="width: 700px;
    margin: 20px;
    border-radius: 25px;display: none">
    <table width="100%" border="0" cellspacing="0" cellpadding="0">
        <tbody id="tbody">
        <tr>
            <td class="bt">订单号</td>
            <td class="bt">订单类型</td>
            <td class="bt">支付时间</td>
            <td class="bt">支付金额</td>
        </tr>

        </tbody>
    </table>
</div>

<input type="hidden" id="hid_actcode" th:value="${actCode}" />
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var actcode = /*[[${actCode}]]*/ "0";
    var www="../";

    RandEMCode();
    $(document).ready(function(){
        var uid = "";
        var pid = "";
        var uname = "";
        var realname = "";
        var maskname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var mobileX = /*[[${mobileX}]]*/ "";
        $("[name=paystatus1]").hide();
        $("[name=paystatus2]").hide();

        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        if (!channelcode) {
            channelcode = "A12050";
        }

        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
        }
        var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=22&channelcode=" + channelcode + "&" + location.search.slice(1);
        var payUrl1 = "https://pay.emoney.cn/newpayv2/JunePay/Recharge?actid=114&channelcode="+channelcode+"&" + location.search.slice(1);//定金支付页
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search.slice(1);
            payUrl1 = "https://pay.emoney.cn/newpayv2/JunePay/Rechargemob?actid=114&channelcode=" + channelcode + "&" + location.search.slice(1);
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
            payUrl1 += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl1]").attr("href", payUrl1);
        $("[name=payurl]").click(function () {
            if (isLogin == "0") {
                layer.msg("您还未登录，请登录后操作。");
            }
        });
        $("[name=payurl1]").click(function () {
            if (isLogin == "0") {
                layer.msg("您还未登录，请登录后操作。");
            }
            //点击定金支付推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACMasterPop20230625");
        });

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        }else{
            if(!!mobileX){
                getPayStatus(mobileX);
            }
        }

        $("#btn_goFirstClass,.btn4").click(function (){
            PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "10,https://static.emoney.cn/static/platformstatic/act_firstclass/202306/index.html");
        });

        $(".wddd").click(function (){
            getPayList(mobileX);
        });
        $(".btn3").click(function(){
            $(".h").show();
        });
        $(".btn7").click(function(){
            $(".h").hide();
        });
    });
    function RandEMCode() {
        for (var y = 1; y <=3; y++) {
            var code = Math.floor(Math.random() * (99999 - 10000 + 1) + 10000);

            $("#em" + y).html( code);
        }
    }
    function getPayStatus(mobilex) {
        $.ajax({
            type: 'get',
            url: "getjunepaystatus?mobilex=" + mobilex,
            dataType: 'jsonp',
            success: function (data) {
                if (data.code == "200") {
                    var status = data.data;//0 未付定金  1 定金已付  2 尾款已付  -1 已退款
                    if (status == "0" || status == "-1") {
                        $("[name=paystatus0]").show();
                    }
                    if (status == "1") {
                        $("[name=paystatus1]").show();
                        $("[name=paystatus0]").hide();
                        $("[name=paystatus2]").hide();
                    }
                    if (status == "2") {
                        $("[name=paystatus2]").show();
                        $("[name=paystatus1]").hide();
                        $("[name=paystatus0]").hide();
                    }
                }
            }
        });
    }
    function getPayList(mobilex){
        $("#tbody tr").eq(0).siblings().remove();
        $.ajax({
            type: 'get',
            url: "getjunepaylist?mobilex=" + mobilex,
            dataType: 'jsonp',
            success: function (data) {
                if (data.code == "200") {
                    var list = data.data;
                    if(!!list && list.length>0){
                        for(var i=0;i<list.length;i++){
                            var item = list[i];

                            var trhtml = document.createElement("tr");
                            trhtml.id = "mDiv";
                            trhtml.innerHTML = "<td>"+item.OrderID+"</td><td>"+item.OrderTypeName+"</td><td>"+item.OrderTime+"</td><td>￥"+item.OrderPrice+"</td>";

                            $("#tbody").append(trhtml);
                        }
                    }else{
                        var trhtml = document.createElement("tr");
                        trhtml.id = "mDiv";
                        trhtml.innerHTML = "<td colspan='4'>您好，当前未查询到任何有效订单</td>";

                        $("#tbody").append(trhtml);
                    }


                    layer.open({
                        type: 1,
                        title: false,
                        //skin: 'layui-layer-nobg', //没有背景色
                        area: ['auto'],
                        shade: 0.6,
                        shadeClose: false,
                        content: $(".orderlist")
                    });
                }
            }
        });
    }

    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
