<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="玩转大波段 纵横大A股 智盈大师4月感恩回馈，积分兑豪礼，续费优惠最高可达1200元">
    <script type="text/javascript">
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj =
                    GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {}
        }
        (function() {
            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>

    <link th:href="@{${staticPath}+'static/renewds/20230504/style/style.css'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
</head>
<body>
<div class="bod">
    <div class="img_1"></div>
    <div class="img_2"><div class="main"><div class="txt" th:text="${allPoint}"></div></div></div>
    <div class="img_3"><div class="main"><a href="javascript:void(0)" data-point="888" data-productid="745" class="btn1" clickkey="wydh" clickdata="wydh"></a><div class="hand"></div></div></div>
    <div class="img_4"><div class="main"><a href="javascript:void(0)" data-point="288" data-productid="743" class="btn1" clickkey="wydh2" clickdata="wydh2"></a><div class="hand"></div></div></div>
    <div class="img_5"></div>
    <div class="img_6"></div>
    <div class="img_7">
        <div class="main"><a href="javascript:void(0)" class="btn3 dh" clickkey="hdgz" clickdata="hdgz"></a></div></div>

    <div class="pfb">
        <div class="main"><a href="javascript:void(0)" class="btn2 dh" target="_blank" name="payurl" clickkey="btn3" clickdata="btn3"></a></div></div>
</div>

<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="h"><div class="hdgz"><a href="javascript:void(0)" class="close"></a>
    <ul>
        <li><strong>活动参与对象</strong>：仅限智盈大师用户参与，3年版福利每位用户仅可参与一次</li>
        <li><strong>活动时间</strong>：2023年5月1日—2023年5月31日</li>
        <li><strong>赠送规则</strong>：完成续费和适当性测评后开通【智盈大师软件使用期】</li>
        <li><strong>插件赠送</strong>：完成续费和适当性测评后开通【智盈大师插件功能使用期】，3年版插件功能对齐使用期最晚到账时间为2023年6月10日。</li>
        <li><strong>优惠券使用说明</strong>：100元优惠券仅限智盈大师1年版使用；1200元优惠券仅限智盈大师3年版使用，优惠券不可交叉使用。</li>
        <li><strong>听课积分领取规则</strong>：《智盈大师波段掘金课程》一节课需听满30分钟才可领取50积分，每节课最高可领取50积分，每节课仅能领取一次积分，不可重复领取。</li>
        <li><strong>插件功能说明</strong>：<br />
            ①智盈大师掘金版(三年版)含5个插件功能:黑马雷达、席位龙虎榜、机构调研、主力识别、北上强买;<br />
            ②智盈大师深度资金版(三年版)含3个插件功能:席位龙虎榜、机构调研、北上强买: 深度资金版标配功能中包含黑马雷达、主力识别功能。</li>
    </ul>
</div></div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="inputtxt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc1" style="display: none"><div class="close"></div></div>
    <div class="tc2" style="display: none"><div class="close"></div></div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actCode}" />
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var cookiename = "emoney.act20230421";
    var flag = true;
    var actcode = /*[[${actCode}]]*/ "0";
    var www="../";
    $(document).ready(function () {
        var uid = "";
        var pid = "";
        var uname = "";
        var realname = "";
        var maskname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var allpoint =  /*[[${allPoint}]]*/ "0";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        if (!channelcode) {
            channelcode = "A12050";
        }

        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
        }
        var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=20&channelcode=" + channelcode + "&" + location.search;
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search;
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (isLogin == "0") {
                layer.msg("您还未登录，请登录后操作。");
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACMasterPop20230426");
        });
        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        }else{
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACMasterPop20230426");
        }

        //积分兑换优惠券
        $('.btn1').click(function () {
            var $this = $(this);
            var productid = $this.attr("data-productid");
            var point = $this.attr("data-point");

            var b = new Base64();
            if(parseInt(allpoint) < parseInt(point)){
                $(".bg").show();
                $(".tc2").show();

                PC_JH('EM_FUNC_START_IM', '0,AC202300504,' + b.encode("大师续费活动"));
                return false;
            }

            $.ajax({
                type: 'get',
                url: "/activity/renew588/pointOrderExchange?&productId=" + productid + "&actcode=" + actcode,
                dataType: 'json',
                success: function (data) {
                    if (data.code == "200") {
                        $this.removeClass("btn1").removeAttr("clickkey").addClass("btn1h");
                        $(".bg").show();
                        $(".tc1").show();
                    } else {
                        if(data.msg == "积分不足"){
                            $(".bg").show();
                            $(".tc2").show();

                            PC_JH('EM_FUNC_START_IM', '0,AC202300504,' + b.encode("大师续费活动"));
                            return false;
                        }else{
                            layer.msg(data.msg);
                            return;
                        }
                    }
                }
            });
        });
    });

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }

    function setCookie(name, value) {
        var expdate = new Date();
        expdate.setTime(expdate.getTime() + 30 * 60 * 1000);
        document.cookie = name + "=" + value + ";expires=" + expdate.toGMTString() + ";path=/";
    }


    function getCookie(c_name) {
        if (document.cookie.length > 0) {
            c_start = document.cookie.indexOf(c_name + "=")
            if (c_start != -1) {
                c_start = c_start + c_name.length + 1
                c_end = document.cookie.indexOf(";", c_start)
                if (c_end == -1) c_end = document.cookie.length
                return unescape(document.cookie.substring(c_start, c_end))
            }
        }
        return ""
    }
    $(".btn3").click(function(){
        $(".h").show();
    });
    $(".close").click(function(){
        $(".h").hide();
        $(".bg").hide();
    });
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
</script>
<script src="https://imgtongji.emoney.cn/scripts/https/emoneyanalytics.js" type="text/javascript"></script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "xzyxf-ym20230426";//小智盈续费
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>