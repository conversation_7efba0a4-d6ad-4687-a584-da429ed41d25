<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renewds/20231025/css/style.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script type="text/javascript" th:src="@{${staticPath}+'static/renewds/20231025/js/zUI.js'}"></script>
    <script type="text/javascript" th:src="@{${staticPath}+'static/renewds/20231025/js/jquery.slides.js'}"></script>
    <script type="text/javascript">
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj =
                    GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {}
        }
        (function() {
            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1">
    <ul id="ul_dj">
        <li>恭喜EM001****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM093****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM983****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM146****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM500****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM340****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM122****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM058****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM023****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM088****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM001****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM018****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM132****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM012****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM552****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM119****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM130****支付订金1000元，成功参与双11续费活动</li>
        <li>恭喜EM980****支付订金1000元，成功参与双11续费活动</li>
    </ul>
    <ul id="ul_wk" style="display: none;">
        <li>恭喜EM001****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM093****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM983****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM146****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM500****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM340****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM122****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM058****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM023****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM088****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM001****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM018****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM132****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM012****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM552****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM119****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM130****已成功支付尾款，享受双11续费福利</li>
        <li>恭喜EM980****已成功支付尾款，享受双11续费福利</li>
    </ul>

</div>
<div class="img_2"></div>
<div class="img_3"><div class="main">
    <div class="txt1">抓紧支付<span class="red2">尾款</span>，错过这次等明年</div>
</div></div>
<div class="img_4" style="display: none;">
    <div class="main">
        <a href="javascript:;" class="btn1h dh" name="payurl">
            <img th:src="@{${staticPath}+'static/renewds/20231025/images/ico1.png'}" class="s1 dh2" alt="">
        </a>
        <!--状态2-->
        <a href="javascript:;" class="btn1h2" style="display: none;"></a>
    </div>
</div>
<div class="img_4b">
    <div class="main">
        <a href="javascript:;" class="btn1 dh" name="payurl">
            <img th:src="@{${staticPath}+'static/renewds/20231025/images/ico1.png'}" class="s1 dh2" alt="">
        </a>
    </div>
</div>
<div class="img_5"></div>
<div class="img_6"><div class="main"><a href="javascript:void(0)" class="btn3 dh"></a></div>
    <div class="djs"><div class="t">00</div>
        <span class="s">23</span>
    </div>
    <div class="al2 d2"><!-- 代码 开始 -->
        <div class="slider_name2 slider_box2">
            <ul class="silder_con2" style=" width: 5200px;">
                <li class="silder_panel2"><img th:src="@{${staticPath}+'static/renewds/20231025/images/1.png'}"></li>
                <li class="silder_panel2"><img th:src="@{${staticPath}+'static/renewds/20231025/images/2.png'}"></li>
                <li class="silder_panel2"><img th:src="@{${staticPath}+'static/renewds/20231025/images/3.png'}"></li>
                <li class="silder_panel2"><img th:src="@{${staticPath}+'static/renewds/20231025/images/4.png'}"></li>
                <li class="silder_panel2"><img th:src="@{${staticPath}+'static/renewds/20231025/images/5.png'}"></li>
            </ul>
            <ul class="silder_nav2" style="margin-left: 332px;">
                <li></li>
                <li></li>
                <li></li>
                <li></li>
                <li></li>
            </ul>
        </div>
        <!-- 代码 结束 -->
    </div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<!--尾款-->
<div class="pf" style="display: none;">
    <div class="main">
        <a href="javascript:;" class="btn2h" name="payurl"></a>
        <!--状态2-->
        <a href="javascript:;" class="btn2h2" style="display: none;"></a>
    </div>
</div>
<!--定金-->
<div class="pfb">
    <div class="main">
        <a href="javascript:;" class="btn2 dh" name="payurl"></a>
    </div>
</div>
<div class="bg" style="display: none;">
	<div class="tc" style="display: none;">
            <div class="bt">请登录</div>
            <div class="txt">
                <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
                <div class="red">*请填写您的真实姓名</div>
                手机号码 <input id="loginmobile" type="text" />
                <div class="red">*请输入购买智盈产品时用的手机号</div>
                <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
                <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
                <div>
                    <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
                </div>
            </div>
    </div>
    <div class="tc3" style="display: none;">
        <a href="javascript:void(0)" class="close" style="right: 30px; top: 10px;"></a>
        <a href="javascript:;" class="tc3-btn dh" name="payurl"></a></div>
</div>

<div class="h">
    <div class="hdgz"><div class="t1">
        <div class="t2">活动说明：<br />
            1.支付订金代表用户参与活动的意愿，成功支付订金的用户，需进一步成功支付尾款后才可享受相关活动福利。<br />
            2.支付订金时间有限（时间为2023/10/16-2023/11/17），过期未支付尾款则无法享受相关活动福利，视为用户主动放弃，请注意及时使用订金，如有延期则另行通知。<br />
            3.支付订金后获得的优惠福利，不能和其他同期的福利叠加享受，无法重复使用或延期使用。<br />
            4.活动期间每个用户仅能参与一次。<br />
            5.用户最终续费价格以实际支付页面上的价格为准。<br />
            6.插件赠送规则：完成续费和适当性测评后开通【智盈大师插件功能使用期】，3年版插件功能对齐使用期最晚到账时间为2023年11月30日。<br />
            7.插件功能说明：<br />
            ①智盈大师掘金版(三年版)含5个插件功能:黑马雷达、席位龙虎榜、机构调研、主力识别、北上强买;<br />
            ②智盈大师深度资金版(三年版)含3个插件功能:席位龙虎榜、机构调研、北上强买: 深度资金版标配功能中包含黑马雷达、主力识别功能。<br />
            <br />
            订金支付时间：2023/10/16-2023/11/17<br />
            尾款支付时间：2023/10/25-2023/11/17<br />
            活动对象：智盈大师用户<br />
            活动内容：成功支付订金1000元后可参与活动，成功支付尾款后可享受续费活动优惠，优惠信息以页面内容为准。<br />
            订金退款：退款开启时间为11月30日后，成功退还订金/尾款则无法享受相关优惠。<br />
            <br />
            <div>活动最终解释权归益盟股份！</div></div>
        </div>
        <a href="javascript:void(0)" class="close"></a>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script type="text/javascript" th:src="@{${staticPath}+'static/renewds/20231025/js/index.js'}"></script>
<script type="text/javascript">
    var www="../";
    $(function(){
        $(".t1").panel({iWheelStep:32});
    });
</script>
<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数

        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
    }
    djs=setInterval("SetTimeout(2023,11,17,24,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0" +i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2023/11/17 24:00:00");
    var b=new Date("2023/11/13 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("0");
        $(".s").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs").show();
    }
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
