<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renewds/20231016/css/style.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script type="text/javascript" th:src="@{${staticPath}+'static/renewds/20231016/js/zUI.js'}"></script>
    <script type="text/javascript">
        document.domain = 'emoney.cn';
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"><ul>
    <li>恭喜EM001****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM093****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM983****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM146****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM500****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM340****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM122****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM058****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM023****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM088****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM001****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM018****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM132****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM012****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM552****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM119****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM130****已支付订金1000元，成功参与双11续费活动</li>
    <li>恭喜EM980****已支付订金1000元，成功参与双11续费活动</li>
</ul></div>
<div class="img_2"><div class="main">
    <a href="javascript;" name="payurl" target="_blank" class="btn1 dh"><img th:src="@{${staticPath}+'static/renewds/20231016/images/ico1.png'}" class="s1 dh2" alt=""></a>
    <!--状态2-->
    <a href="javascript;" class="btn1h" style="display: none"></a>
</div></div>
<div class="img_3"></div>
<div class="img_4"><div class="main"><a href="javascript:void(0)" class="btn3 dh"></a></div></div>
<div class="img_5">
</div>
<div class="img_6"></div>
<div class="img_7"><div class="main"><div class="al2 d2"><!-- 代码 开始 -->
    <div class="slider_name2 slider_box2">
        <ul class="silder_con2" style=" width: 5200px;">
            <li class="silder_panel2"><img th:src="@{${staticPath}+'static/renewds/20231016/images/1.png'}"></li>
            <li class="silder_panel2"><img th:src="@{${staticPath}+'static/renewds/20231016/images/2.png'}"></li>
            <li class="silder_panel2"><img th:src="@{${staticPath}+'static/renewds/20231016/images/3.png'}"></li>
            <li class="silder_panel2"><img th:src="@{${staticPath}+'static/renewds/20231016/images/4.png'}"></li>
            <li class="silder_panel2"><img th:src="@{${staticPath}+'static/renewds/20231016/images/5.png'}"></li>
            <li class="silder_panel2"><img th:src="@{${staticPath}+'static/renewds/20231016/images/6.png'}"></li>
        </ul>
        <ul class="silder_nav2" style="margin-left: 332px;">
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
    </div>
    <!-- 代码 结束 -->
</div>
    <div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
        本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div></div></div>
<div class="pf">
    <div class="main">
        <a href="javascript;" name="payurl" target="_blank" class="btn2 dh"></a>
        <!--状态2-->
        <a href="javascript;" class="btn2h" style="display: none;"></a>
    </div>
</div>
<div class="bg" style="display: none;">
	<div class="tc" style="display: none;">
            <div class="bt">请登录</div>
            <div class="txt">
                <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
                <div class="red">*请填写您的真实姓名</div>
                手机号码 <input id="loginmobile" type="text" />
                <div class="red">*请输入购买智盈产品时用的手机号</div>
                <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
                <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
                <div>
                    <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
                </div>
            </div>
        </div>
		<div class="tc2"><a href="javascript:void(0)" class="close"></a>请输入购买智盈产品时用的手机号</div>
	</div>
<div class="h">
    <div class="hdgz"><div class="t1">
        <div class="t2">活动说明：<br />
            1.支付订金代表用户参与活动的意愿，成功支付订金的用户，需进一步成功支付尾款后才可享受相关活动福利。<br />
            2.活动部分福利（鸿运大礼）仅限成功支付尾款的用户享受，仅支付定金的用户无法享受该活动福利。<br />
            3.支付订金时间有限（时间为2023/10/16-2023/10/24），过期未支付尾款则无法享受相关活动福利，视为用户主动放弃，请注意及时使用订金，如有延期则另行通知。<br />
            4.支付订金后获得的优惠福利，不能和其他同期的福利叠加享受，无法重复使用或延期使用。<br />
            5.活动期间每个用户仅能参与一次。<br />
            6.用户最终续费价格以实际支付页面上的价格为准。<br />
            7.插件赠送规则：完成续费和适当性测评后开通【智盈大师插件功能使用期】，3年版插件功能对齐使用期最晚到账时间为2023年11月31日。<br />
            8.插件功能说明：<br />
            ①智盈大师掘金版(三年版)含5个插件功能:黑马雷达、席位龙虎榜、机构调研、主力识别、北上强买;<br />
            ②智盈大师深度资金版(三年版)含3个插件功能:席位龙虎榜、机构调研、北上强买: 深度资金版标配功能中包含黑马雷达、主力识别功能。<br />
            <br />
            支付订金时间：2023/10/16-2023/10/24<br />
            尾款支付开始时间：2023/10/25<br />
            活动对象：智盈大师用户<br />
            活动内容：成功支付订金1000元后可参与活动，成功支付尾款后可享受续费活动优惠，优惠信息以页面内容为准。<br />
            订金退款：退款开启时间为11月30日后，成功退还订金则无法享受相关优惠。<br />
            <br />
            <div>活动最终解释权归益盟股份！</div></div>
    </div>
        <a href="javascript:void(0)" class="close"></a>
    </div>
</div>
<script type="text/javascript" th:src="@{${staticPath}+'static/renewds/20231016/js/jquery.slides.js'}"></script>
<input type="hidden" id="hid_actcode" th:value="${actCode}" />
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script type="text/javascript">
    $(function(){
        $(".t1").panel({iWheelStep:32});
    });
</script>
<script th:inline="javascript">
    var actcode = /*[[${actCode}]]*/ "0";
    var www="../";
    var uid = "";
    var pid = "";
    var uname = "";
    var realname = "";
    var maskname = "";
    var isLogin = /*[[${isLogin}]]*/ "0";
    var mobileX = /*[[${mobileX}]]*/ "";
    $(document).ready(function() {
        var token = getQueryString("token");

        if (isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
        }
        var payUrl = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888020000,888080000&groupCode=3&resourcestypeid=234&resourcesid=1244397&businessType=biztyp-dsxf&" + location.search.slice(1);
        if (!token) {
            payUrl += "&phoneEncrypt=" + mobileX + "&UPcid=" + uid;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (isLogin == "0") {
                layer.msg("您还未登录，请登录后操作。");
            }
            if (!checkPermission(pid)) {
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACMasterPop20231016");
        });

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        }else{
            //查询是否付过定金
            isPayment(actcode,token);
        }

        $(".btn1h,.btn2h").click(function (){
            layer.msg("恭喜您已成功支付订金，尾款支付开启时间为10月25日");
            return false;
        });

        $(".btn3").click(function(){
            $(".h").show();
        });
        $(".close").click(function(){
            $(".h").hide();
        });

    });

    function isPayment(actcode,token){
        $.get("ispayment", {actCode: actcode,token:!!token?token:""}, function (data) {
            if(data.code === '200'){
                var ret = data.data;
                if(ret){
                    $(".btn2").hide();$(".btn1").hide();
                    $(".btn2h").show();$(".btn1h").show();
                }
            }
        });
    }

    // 检查用户是否有权限参与
    function checkPermission(pid) {
        var pidlist = "888020000,888080000,888020400";
        if (pidlist.indexOf(pid) < 0) {
            layer.msg("本活动仅限大师用户参与");
            return false;
        }
        return true;
    }
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }

    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
