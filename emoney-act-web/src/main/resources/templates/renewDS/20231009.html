<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="众筹鸿运值你旺我旺大家旺，幸运之机好比市场，稍一耽搁，价格就变了">
    <link th:href="@{${staticPath}+'static/renewds/20231009/style/style.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        document.domain = 'emoney.cn';
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="bod">
    <div class="img_1"></div>
    <div class="img_2"></div>
    <div class="img_3"></div>
    <div class="img_4"></div>
    <div class="img_5"></div>
    <div class="img_6">
        <div class="main">
            <div class="ico b6" style="display: none;"></div>
            <div name="ico" th:each="item,iterStat: ${dayList}" th:if="${item.isAvailable}" th:class="'ico b'+(${item.point==10000}?'1':(${item.point==25000}?'2':(${item.point==50000}?'3':(${item.point==75000}?'4':'5'))))"></div>
            <a name="btnGetReward" th:each="item,iterStat: ${dayList}" th:if="${item.point==100000}" class="btn1 dh" href="javascript:void(0)" th:attr="isreceived=${item.isReceived},isavailable=${item.isAvailable},prizeid=${item.id},point=${item.point}" th:text="(${item.isReceived}?'马上续费享用福利':'点我解锁鸿运大礼')"></a>
            <a name="btnGetReward" th:each="item,iterStat: ${dayList}" th:if="${item.point<100000}" th:class="(${item.isReceived}?'btn2h':'btn2')+ ' a'+${iterStat.index}+' dh'" href="javascript:void(0)" th:attr="isreceived=${item.isReceived},isavailable=${item.isAvailable},prizeid=${item.id},point=${item.point}" th:text="(${item.isReceived}?'福利已收下':'点我碰碰运气')"></a>

            <a href="javascript:void(0)" class="btn3 dh"></a>
            <div class="txt">今天已有<span class="red" th:text="${dayCount}"></span>位用户参与众筹</div></div></div>
    <div class="img_7">
        <div class="main"><a href="javascript:void(0)" class="txt2">活动规则</a><ul>
            <li th:each="item1,iterStat1:${userRecordList}" th:text="${item1.showTime}+'碰运气喜得'+${item1.description}"></li>
        </ul></div></div>
    <div class="img_8">
        <div class="main"><div class="txt3"><span id="hypoint">0</span>点鸿运值</div></div></div>
</div>

<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #000;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf1"><a href="javascript:void(0)" class="b1"></a><a href="javascript:void(0)" class="b2"></a></div>
<div class="h"><div class="hdgz"><a href="javascript:void(0)" class="tc-btn2"></a>
    <ul>
        活动参与对象：仅限智盈大师用户参与<br />
        活动时间：2023年10月9日—2023年10月16日<br />
        本次活动所有福利，每位用户仅限领取一次，不可重复领取。<br />
        每人每日最高可积攒88点鸿运值，活动期间每位参与活动的用户最高可积攒588点鸿运值。<br />
        活动期间，当鸿运值总数分别达到10000、25000、50000、75000、100000点时，所有参与活动的用户，可点击“点我碰碰运气”按钮，领取对应的鸿运福利。<br />
        众筹鸿运值突破100000时，触发鸿运大礼福利。参与双11大师续费活动可解锁该福利，完成续费和适当性测评后即可开通享用，可与双11活动优惠叠加享用。未续费或已退款订单无法享受此福利。<br />
        活动结束时间为2023年10月16日23：59：59，逾期福利将自动失效，未领取则视为主动放弃，不可再领取。<br />
        <br />
        活动最终解释权归益盟股份！
    </ul>
</div></div>

<div class="bg" style="display: none">
    <div class="tc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txtlogin">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc1" id="tc_10000">
        <div class="close"></div>
        <div class="tc-bg1">恭喜您!</div><div class="t1">今天时来运转<br />鸿运值噌噌噌！<br />
        喜得<span class="yellow">大师1天使用期</span></div>
        <a href="javascript:void(0)" class="tc-btn1 dh">开心收下</a>
    </div>
    <div class="tc2" id="tc_25000">
        <div class="close"></div>
        <div class="tc-bg1b">鸿运天天不停息!</div><div class="t1"><span class="black">喜得</span>5大赛道指数<br />15天限免使用权限</div>
        <div class="t2">进入路径：<br />
            大势风口 &gt; 风格看盘 &gt; 5大赛道指数</div>
        <a href="javascript:void(0)" class="tc-btn1 dh">开心收下</a>
    </div>
    <div class="tc1" id="tc_50000">
        <div class="close"></div>
        <div class="tc-bg1">恭喜您!</div><div class="t1">福气满身财运绕<br />
            鸿运值爆棚啦！<br />
            喜得<span class="yellow">大师3天使用期</span></div>
        <a href="javascript:void(0)" class="tc-btn1 dh">开心收下</a>
    </div>
    <div class="tc2" id="tc_75000">
        <div class="close"></div>
        <div class="tc-bg1b">福运接连不断!</div><div class="t1"><span class="black">喜得</span>投研内参功能<br />
        30天限免使用权限</div><div class="t2">进入路径：<br />
        登录软件 &gt; 基本面频道 &gt; 投研内参</div>
        <a href="javascript:void(0)" class="tc-btn1 dh">开心收下</a>
    </div>

    <div class="tc1" id="tc_100000">
        <div class="close"></div>
        <div class="t2">鸿运值大爆发！<br />
            喜得鸿运大礼！<br />
            <span class="yellow">获得双11第一波福利<br />“大师使用期15天”</span></div>
        <div class="t3">现在续费，可马上享用<br />
            本福利与双11活动优惠叠加享用</div>
        <a href="javascript:void(0)" class="tc-btn1 dh" name="payurl" target="_blank">马上续费<div class="hend"></div></a>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}" />
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var actcode = /*[[${actCode}]]*/ "0";
    var testday = /*[[${day}]]*/ "0";
    var www="../";
    var uid = "";
    var pid = "";
    var uname = "";
    var realname = "";
    var maskname = "";
    var isLogin = /*[[${isLogin}]]*/ "0";
    var mobileX = /*[[${mobileX}]]*/ "";
    $(document).ready(function() {
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        if (!channelcode) {
            channelcode = "A12050";
        }

        if (isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
        }
        var payUrl = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888020000,888080000&groupCode=1&businessType=biztyp-dsxf&" + location.search.slice(1);
        if (!token) {
            payUrl += "&phoneEncrypt=" + mobileX + "&UPcid=" + uid;
        }

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        }
        if($(".ico").length == 1){
            $(".b6").show();
        }
        getMyPoint();
        getDayCount();

        //点击碰运气
        $("[name=btnGetReward]").click(function () {
            var isavalible = $(this).attr("isavailable");
            var prizeid = $(this).attr("prizeid");
            var isreceived = $(this).attr("isreceived");
            var point = $(this).attr("point");
            var txt = $(this).html();

            if (txt === '福利已收下') {
                return false;
            }

            if (isavalible === 'false') {
                layer.msg("鸿运值尚未到达目标值，请继续努力参与众筹！");
                return false;
            }

            var yhpoint = $("#hypoint").html();
            if (parseInt(yhpoint) < 1) {
                layer.msg("请动动手指一起众筹，加油解锁免费好礼~");
                return false;
            }

            $(".bg").show();
            $("#tc_" + point).show();
            if (point != '100000') {
                $("#tc_" + point).find(".tc-btn1").attr("prizeid", prizeid);
            } else {
                $.get("getdailyreward", {actCode: actcode, prizeId: prizeid, day: testday}, function (data) {
                });
                $(this).html("马上续费享用福利");
            }
        });

        //开心收下
        $(".tc-btn1").click(function () {
            var prizeId = $(this).attr("prizeId");
            var txt = $(this).html();
            if (txt === '开心收下') {
                $.get("getdailyreward", {actCode: actcode, prizeId: prizeId, day: testday}, function (data) {
                    $(".bg").hide();
                    $(".tc1").hide();
                    $(".tc2").hide();

                    $(".main a[prizeid=" + prizeId + "]").removeClass("btn2").addClass("btn2h").html("福利已收下");
                });
            } else {
                $("[name=payurl]").attr("href", payUrl);
            }
        });

        //点击众筹
        $(".btn3").click(function () {
            var nowDate = new Date();
            var dateStr = (nowDate.getMonth() +1).toString() + nowDate.getDate().toString();
            var cookiename = "emoney.actds20231009." + uid;
            var cookiename_date = "emoney.actds20231009." + uid + "." + dateStr;
            var val = getCookie(cookiename);//总鸿运值
            var val_day = getCookie(cookiename_date);//当日鸿运值
            var count = 0;
            var count_day = 0;

            if (!!val_day) {
                //当日鸿运值
                count_day = parseInt(val_day);
            }

            if (!!val) {
                //总鸿运值
                count = parseInt(val);
            } else {
                //无记录读取默认鸿运值
                var point = $("#hypoint").html();
                if (!!point) {
                    count = parseInt(point);
                }
            }

            if (count_day >= 88) {
                layer.msg("今日鸿运值已到顶峰，请明日再来哦！");
                return false;
            }
            if (count >= 588) {
                layer.msg("恭喜您，个人累计鸿运值已满，请联系服务专员了解后续详情！");
                return false;
            }

            count_day++;
            count++;
            setCookie(cookiename, count);
            setCookie(cookiename_date, count_day);
            $("#hypoint").html(count);

            //推送cmp
            if (count === 1) {
                pushdatatocmp(uid, "ACMasterPop20231009");
                $.get("adduser1009", {actCode: actcode}, function (data) {
                });

                //观看记录
                $.ajax({
                    type: 'get',
                    url: "/activity/user/addcountbyactcode?actcode=" + actcode,
                    dataType: 'jsonp',
                    data: {
                        uid: uid,
                        value: "1"
                    },
                    success: function (data) {
                    }
                });
            }
        });

        //关闭窗口
        $(".close").click(function () {
            $(".bg").hide();
            $(".tc1").hide();
            $(".tc2").hide();
        });
    });

    function getMyPoint(){
        var cookiename = "emoney.actds20231009." + uid;
        var val = getCookie(cookiename);
        var count = 0;
        if (!!val) {
            count = parseInt(val);
            $("#hypoint").html(count);
        }else{
            $.ajax({
                type: 'get',
                url: "/activity/user/issubmitbyactcodes?actcodes=" + actcode,
                dataType: 'jsonp',
                data: {
                    actcodes:actcode,
                    uid:uid
                },
                success: function (data) {
                    if (data.code === '200') {
                        var num = "";
                        if (data.data) {
                            num = data.data.split(',')[0];
                            if(!!num){
                                $("#hypoint").html(1);
                            }else{
                                $("#hypoint").html(0);
                            }
                        }
                    }
                }
            });
        }
    }

    function getDayCount(){
        $.get("getdaycount",{},function (data){
            if (data.code == '200' && !!data.data) {
                $("#daycount").html(data.data);
            }
        });

        setTimeout(function (){
            getDayCount();
        },1000*60);
    }

    $(window).scroll(function(e){
        if($(window).scrollTop() >= 1200){
            $('.pf1').fadeIn(300);
        }else{
            $('.pf1').fadeOut(300);
        }}
    );
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }

    function setCookie(name, value) {
        var expdate = new Date();
        expdate.setTime(expdate.getTime() + 1000 * 60 * 60 * 24 * 30);
        document.cookie = name + "=" + value + ";expires=" + expdate.toGMTString() + ";path=/";
    }


    function getCookie(c_name) {
        if (document.cookie.length > 0) {
            c_start = document.cookie.indexOf(c_name + "=")
            if (c_start != -1) {
                c_start = c_start + c_name.length + 1
                c_end = document.cookie.indexOf(";", c_start)
                if (c_end == -1) c_end = document.cookie.length
                return unescape(document.cookie.substring(c_start, c_end))
            }
        }
        return ""
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }

    $(".txt2").click(function(){
        $(".h").show();
    });
    $(".close,.tc-btn2").click(function(){
        $(".h").hide();
    });
</script>
<script src="https://imgtongji.emoney.cn/scripts/https/emoneyanalytics.js" type="text/javascript"></script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "dsxf-ym20231009";//小智盈续费
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>

</html>
