<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="玩转大波段 纵横大A股 智盈大师4月感恩回馈，积分兑豪礼，续费优惠最高可达1200元">
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>

    <link th:href="@{${staticPath}+'static/renewds/20230912/style/style.css'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
</head>

<body>
<div class="bod">
    <div class="img_1"><div class="main"><div class="txttip">恭喜！今天已有<span id="daycount" class="red" th:text="${dayCount}"></span>位用户签到，成功领取签到好礼</div></div></div>
    <div class="img_2">
        <ul id="ulSignCnt">
            <li th:each="item,iterStat: ${signDayList}" th:class="(${item.isCurrent}?'on':'')" th:attr="isreceived=${item.isReceived}">
                <div class="t1" th:text="'第'+${item?.day}+'天'"></div>
                <div class="t2">
                    <span th:if="${item.day == 1}">首日签到礼<br>100元抵扣券<br></span>
                    <span th:if="${item.day == 2 || item.day == 4 || item.day == 8}">1天使用期</span>
                    <span th:if="${item.day == 3 || item.day == 6}">5大赛道指数<br>15天使用权限</span>
                    <span th:if="${item.day == 5}">投研内参功能<br>30天使用权限</span>
                    <span th:if="${item.day == 7}"><span class="f14">5大高端功能插件<br>教学精品课<br>（5大高端功能插<br>件为3年版专享）</span></span>
                    <span class="f12" th:if="${item?.day == 1}">仅限续费1年使用</span>
                </div>
                <a href="javascript:void(0)" class="btn a1" th:if="${!item.isReceived && currentDay!=-1 && currentDay<=item.day}" th:attr="prizeid=${item.id},day=${item?.day},iscurrent=${item.isCurrent}">领 取</a>
                <a href="javascript:void(0)" class="btn a3" th:if="${item.isReceived && item.day !=7}">已领取</a>
                <a href="javascript:void(0)" class="btn a1" th:if="${item.isReceived && item.day ==7}">查 看</a>
                <a href="javascript:void(0)" class="btn a3" th:if="${!item.isReceived && !item.isCurrent && (currentDay>item.day || currentDay==-1)}">领 取</a>
            </li>
    </ul></div>
    <div class="img_3"><div class="main">
        <a href="javascript:void(0)" class="btn2 dh" id="btn_appendPrize" th:attr="prizeId=${appendPrize.id},day=${appendPrize.day}" th:unless="${appendPrize.isReceived}">领 取</a>
        <a href="javascript:void(0)" class="btn2" th:if="${appendPrize.isReceived}" >已领取</a></div></div>
    <div class="img_4"><div class="main"><a href="javascript:void(0)" class="btn3 dh" name="payurl" target="_blank"></a></div></div>
    <div class="img_5"></div>
    <div class="img_6"></div>
    <div class="img_7"></div>
    <div class="img_8"></div>
    <div class="img_9">
        <ul>
            <li th:each="item1,iterStat1:${signRecordList}" th:text="'恭喜您于'+${item1.showTime}+'成功领取'+${item1.description}"></li>
        </ul>
    </div>
    <div class="img_10">
        <div class="main"><a href="javascript:void(0)" class="txt2">活动规则</a></div></div>

    <div class="pfb">
        <div class="main"><a href="javascript:void(0)" class="btn4 dh" name="payurl" target="_blank"></a></div></div>
</div>

<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #000;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf1"><a href="javascript:void(0)" class="b1" name="payurl" target="_blank"></a></div>
<div class="h"><div class="hdgz"><a href="javascript:void(0)" class="close"></a>
    <ul>
        <li><strong>每日完成签到任务，</strong>即可获得当日签到福利，8天内累计签到满5次，可额外获得弹射起步功能7天限免使用权限。签到福利可累计领取，但不可多次重复领取。若用户成功续费，则可一次性解锁全部签到好礼，总计9项；其中，弹射起步功能使用权限额外再赠送1年使用期，从续费成功当日起开始计算。</li>
        <li><strong>每日签到福利，</strong>每个用户仅能领取一次。若当日未领取则视为放弃该日签到福利的领取机会，逾期将不能再领取。若用户成功续费，则可一次性解锁全部签到好礼，总计9项。福利领取后将于48小时内发放至账户。</li>
        <li><strong>插件赠送：</strong>完成续费和适当性测评后开通【智盈大师插件功能使用期】。3年版赠送5大插件功能对齐使用期，1年版原有未到期插件对齐使用期。</li>
        <li><strong>插件功能说明：</strong>①智盈大师掘金版(三年版)含5个插件功能:黑马雷达、席位龙虎榜、机构调研、主力识别、北上强买;<br />
            ②智盈大师深度资金版(三年版)含3个插件功能:席位龙虎榜、机构调研、北上强买: 深度资金版标配功能中包含黑马雷达、主力识别功能。</li>
        <li><strong>首日签到礼说明：</strong>首日签到礼为100元抵扣券，有效期截止日为9月30日，逾期将自动作废。该抵用券仅限智盈大师1年版续费使用。</li>
        <li><strong>订单查询路径：</strong>进入益盟操盘手软件 &gt; 用户精选中心 &gt; 我的 &gt; 我的产品内，可查询认购产品名称、产品首次激活日期、产品到期日等详细信息。</li>
        <li><strong>活动时间：2023/9/11-2023/9/30</strong></li>
        <li><strong>活动对象：智盈大师用户</strong></li>
    </ul>
</div></div>
<div class="bg" style="display: none">
    <div class="tc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>

    <div class="tc1" id="pop1">
        <a href="javascript:void(0)" class="close"></a>
        <a href="javascript:void(0)" class="tc-btn1 dh" name="payurl" target="_blank"></a>
    </div>
    <div class="tc2" id="pop3and6">
        <a href="javascript:void(0)" class="close"></a>
        <div class="t1">恭喜您！签到成功</div><div class="t2">“五大赛道指数”</div><div class="t3">使用权限15天</div><div class="t4">领取成功后，需要重新登陆软件<br />
        <strong>5大赛道指数进入路径：</strong></div>
        <div class="t5">登录软件 &gt; 大势风口 &gt; 风格看盘 &gt; 5大赛道指数</div>
    </div>
    <div class="tc2" id="pop5">
        <a href="javascript:void(0)" class="close"></a>
        <div class="t1">恭喜您！签到成功</div><div class="t2">“投研内参功能”</div><div class="t3">使用权限30天</div><div class="t4">领取成功后，需要重新登陆软件<br />
        <strong>投研内参功能进入路径：</strong></div>
        <div class="t5">登录软件 &gt; 基本面频道 &gt; 投研内参</div>
    </div>
    <div class="tc2" id="pop0">
        <a href="javascript:void(0)" class="close"></a>
        <div class="t1">恭喜您！累计签到满5天</div><div class="t2">“弹射起步功能”</div><div class="t3">使用权限7天</div><div class="t4">领取成功后，需要重新登陆软件<br />
        <strong>弹射起步进入路径：</strong></div>
        <div class="t5">登录软件 &gt; 技术频道 &gt; 量价异动 &gt; 弹射起步</div>
    </div>
</div>
<form id="form7day" target="_blank"></form>

<input type="hidden" id="hid_actcode" th:value="${actCode}" />
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var actcode = /*[[${actCode}]]*/ "0";
    var testday = /*[[${day}]]*/ "0";
    var www="../";
    $(document).ready(function(){
        var uid = "";
        var pid = "";
        var uname = "";
        var realname = "";
        var maskname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var mobileX = /*[[${mobileX}]]*/ "";

        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        if (!channelcode) {
            channelcode = "A12050";
        }

        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
        }
        var payUrl = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888020000,888080000&activityName=jyxf&groupCode=&" + location.search.slice(1);
        if (!token) {
            payUrl += "&phoneEncrypt=" + mobileX + "&UPcid=" + uid;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (isLogin == "0") {
                layer.msg("您还未登录，请登录后操作。");
            }
            if (!checkPermission(pid)) {
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACMasterPop20230911");
        });

        getDayCount();

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        }else{
            //首次签到自动领取奖励
            var $firstLi = $("#ulSignCnt li").eq(0);
            var isFirstDay = $firstLi.hasClass("on");
            var isReceived = $firstLi.attr("isreceived");
            if(isFirstDay && isReceived === 'false'){
                var prizeId = $firstLi.find("a").attr("prizeid");
                var day = $firstLi.find("a").attr("day");
                sendPrize(prizeId,day);
            }
        }
        //签到领取奖励
        $('#ulSignCnt').on('click', ".a1", function () {
            if (!checkPermission(pid)) {
                return false;
            }

            var prizeId = $(this).attr("prizeid");
            var day = $(this).attr("day");
            var isCurrent = $(this).attr("iscurrent");
            if($(this).html()==='已领取'){
                return false;
            }
            if($(this).html()==='查 看'){
                getDay7Piz();
                return false;
            }
            var isCurrentReceived =  $("#ulSignCnt li.on").attr("isreceived");
            if(isCurrent === 'false') {
                if (isCurrentReceived === 'true') {
                    layer.msg("今日签到已完成，想提前解锁全部福利，可联系服务专员！")
                    return false;
                } else {
                    layer.msg("请尽快参与今日签到，想提前解锁全部福利，可联系服务专员！")
                    return false;
                }
            }

            sendPrize(prizeId,day);
        });
       $("#btn_appendPrize").click(function (){
           if (!checkPermission(pid)) {
               return false;
           }

           var prizeId = $(this).attr("prizeId");
           var day = $(this).attr("day");
           if($(this).html()==='已领取'){
               return false;
           }
           //是否满足累签5天
           $.get("getappendprizestatus",{actcode:actcode},function (data){
               if(data.code == '200'){
                   if(data.data === true){
                       sendPrize(prizeId,day);
                   }else{
                       layer.msg("累计签到不满5天，继续加油哦~");
                   }
               }else{
                   layer.msg(data.msg);
                   return;
               }
           });
       })

        $(".txt2").click(function(){
            $(".h").show();
        });
        $(".close").click(function(){
            $(".h").hide();
            $(".bg").hide();
            $(".tc,.tc1,.tc2").hide();
        });

        function sendPrize(prizeId,day) {
            $.get("getsignbenefit", {prizeid: prizeId,actcode:actcode,day:testday}, function (data) {
                if (data.code == '200') {
                    //弹出对应窗口
                    switch (day) {
                        case "1":
                            $(".bg").show();
                            $("#pop1").show();
                            break;
                        case "2":
                        case "4":
                        case "8":
                            layer.msg("恭喜您！签到成功。成功领取1天使用期");
                            break;
                        case "3":
                        case "6":
                            $(".bg").show();
                            $("#pop3and6").show();
                            break;
                        case "5":
                            $(".bg").show();
                            $("#pop5").show();
                            break;
                        case "7":
                            getDay7Piz();
                            break;
                        case "0":
                            $(".bg").show();
                            $("#pop0").show();
                            break;
                    }
                    if(day == "0"){
                        $("[prizeId="+prizeId+"]").removeClass("dh").removeAttr("id").removeAttr("href").html("已领取");
                    }else{
                        if(day == '7'){
                            $("[prizeId="+prizeId+"]").html("查 看");
                        }else{
                            $("[prizeId="+prizeId+"]").removeClass("a1").addClass("a3").html("已领取");
                        }
                        $("[prizeId="+prizeId+"]").parent().attr("isreceived",'true');
                    }
                } else {
                    layer.msg(data.msg);
                }
            });
        }
        function getDay7Piz(){
            $("#form7day").attr("action","https://www.emoney.cn/course/dashi/5dgdcjgn.pdf");
            $("#form7day").submit();
        }

        function getDayCount(){
            $.get("getdaycount",{},function (data){
                if (data.code == '200' && !!data.data) {
                    $("#daycount").html(data.data);
                }
            });

            setTimeout(function (){
                getDayCount();
            },1000*60);
        }
    });

    // 检查用户是否有权限参与
    function checkPermission(pid) {
        var pidlist = "888020000,888080000,888020400";
        if (pidlist.indexOf(pid) < 0) {
            layer.msg("本活动仅限大师用户参与");
            return false;
        }
        return true;
    }
</script>
<script type="text/javascript">
    $(window).scroll(function(e){
        if($(window).scrollTop() >= 1200){
            $('.pf1').fadeIn(300);
        }else{
            $('.pf1').fadeOut(300);
        }
    });

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }

    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
</script>
<script src="https://imgtongji.emoney.cn/scripts/https/emoneyanalytics.js" type="text/javascript"></script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "renewds20230912";//大师续费
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>

</html>
