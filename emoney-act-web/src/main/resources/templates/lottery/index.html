<!doctype html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
<meta charset="utf-8">
<title>益盟操盘手</title>
<meta name="Keywords" content="抽奖,益盟,操盘手">
<meta name="Description" content="双旦欢乐购 赚出好运来 承包你的2023炒股装备 最多9个月使用期！">
<link href="../static/lottery/css/css.css" rel="stylesheet" type="text/css">
<script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>

	<script src="../static/lottery/js/awardRotate.js"></script>
</head>
<body>
<div class="bod">
  <div id="dbg1"></div>
  <div id="dbg2"></div>
  <div id="dbg3">
    <div class="main">
      <div class="an1" th:if="${hasPrivilege == '1'}" id="div_an1"><div class="zz" id="rotate"></div><a href="javascript:void(0)" class="pointer go">点 击<br>抽 奖</a><div class="ico"></div></div>
        <div class="an2" th:if="${hasPrivilege == '0'}" id="div_an2"><div class="zz2"></div><div class="go">剩0次<br>抽奖机会</div></div>
    </div>
  </div>
	<div id="dbg4">
	  <div class="main">
          <div class="md"><a th:if="${isLogin == '0'}" href="javascript:void(0)" class="btn1"></a><div th:if="${isLogin == '1'}" th:text="${'你好，用户' + loginUserInfo.getMaskMobile()}" class="btn1h"></div>
        <marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="2" direction="up" height="120px"><table width="100%" border="0" cellspacing="0" cellpadding="0">
  <tbody id="latestLotteryInfo">

  </tbody>
</table>

        </marquee>
      </div></div></div>
	<div id="dbg5">
	  <div class="main"><div class="txt_2">12.17——12.31号下单订购智盈的用户，可获1次大转盘抽奖机会；<br>
	    抽中使用期奖励，会在1个工作日内自动添加至账号；若未添加，可咨询你的专员，或拨打客服热线：10108688；<br>
      活动最终解释归益盟股份所有；</div></div></div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn">www.emoney.cn</a> 股市有风险，投资需谨慎<br>
  本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
	<a href="javascript:void(0)" class="pf"></a>
	
<div class="h">
<div class="tc" id="wzg"><a href="javascript:void(0)" class="close"></a>
  <div class="tc_txt1"><span class="f35">尊敬的益盟用户</span><br>
    您好，您目前没有抽奖资格。</div>
	<a href="#" class="btn2 b">我知道了&gt;</a>
</div>
	
	<div class="tc" id="cf"><a href="javascript:void(0)" class="close"></a>
  <div class="tc_txt1"><span class="f35">尊敬的益盟用户</span><br>您好，您已参与抽奖<br>中奖记录请至“我的奖品”中查看</div>
	<a href="#" class="btn2 b">我的奖品&gt;</a>
</div>
	<div class="tc" id="jp"><a href="javascript:void(0)" class="close"></a>
  <div class="tc_txt7">★★★ 我的奖品 ★★★</div>
		<table width="450" class="tab" border="0" cellspacing="0" cellpadding="0">
  <tbody>
    <tr>
      <td class="f25">账号</td>
      <td class="f25">奖品</td>
      <td class="f25">抽奖日期</td>
    </tr>
    <tr id="myLotteryInfo">

    </tr>
  </tbody>
</table>

</div>
	<div class="tc_2"><a href="javascript:void(0)" class="close"></a>
  <div class="tc_txt4">恭喜您！</div>
  <div class="tc_txt5">抽中“3个月使用期”</div>
</div>
	<div class="tc_3"><a href="javascript:void(0)" class="close"></a>
		<div class="nav"><a href="javascript:void(0)" class="on" id="n1">手机验证码登录</a><a href="javascript:void(0)" id="n2" style="display:none">软件账号登录</a></div>
		<div class="pf1" id="n1a">
			手机号<input type="text" name="tel" maxlength="11" data-tel id="loginmobile">
		  验证码<input type="text"  maxlength="4" data-yzm id="loginsmscode">
        <a href="javascript:void(0)" class="yzm" data-send id="sendcode">发送验证码</a>
      </div>
		<div class="pf1" id="n2a" style="display: none">
			EM账号<input type="text" name="tel" maxlength="11" data-tel>
		  账号密码<input type="text"  maxlength="4" data-yzm>
      </div>
		<a href="javascript:void(0)" class="btn3" id="btnlogin"></a>
</div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<input type="hidden" id="loginname" th:value="益盟" />
<input type="hidden" id="hid_uid" th:value="${loginUserInfo == null ? '0' : loginUserInfo.getUid()}">
<input type="hidden" id="hid_pid" th:value="${loginUserInfo == null ? '0' : loginUserInfo.getPid()}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script>
    //document.domain="emoney.cn";
    var www="../";
    var uid = $("#hid_uid").val();
    var pid = $("#hid_pid").val();
    $(function (){

        var rotateTimeOut = function (){

            $('#rotate').rotate({
                angle:0,
                animateTo:2160,
                duration:8000,
                callback:function (){
                    alert('网络超时，请检查您的网络设置！');
                }

            });

        };

        var bRotate = false;
        var rotateFn = function (awards, angles, txt){

            bRotate = !bRotate;

            $('#rotate').stopRotate();

            $('#rotate').rotate({
                angle:0,
                animateTo:angles+1800,
                duration:8000,
                callback:function (){
                    $(".h").show();
                    $(".tc_2").show();
                    $(".tc_txt5").text(txt);
                    $("#div_an1").hide();
                    $("#div_an2").show();
                    bRotate = !bRotate;
                }

            })

        };

        $('.pointer').click(function (){
            if(bRotate)return;
            $.get(www + "lottery/doLottery1201", { uid: uid,pid:pid }, function (data) {
                var obj = $.parseJSON(data);
                if (obj.code == "200") {
                    var myLotteryInfo = obj.data.myLotteryInfo[0];
                    switch(myLotteryInfo.level){
                        case 1:
                            rotateFn(1, 90, '抽中“3个月使用期”');
                            break;
                        case 2:
                            rotateFn(2, 180, '抽中“6个月使用期”');
                            break;
                        case 3:
                            rotateFn(3, 270, '抽中“9个月使用期”');
                            break;
                    }
                    // 最近获奖记录
                    var latestLotteryInfo = obj.data.latestLotteryInfo;
                    var html = "";
                    if(latestLotteryInfo!=null && latestLotteryInfo.length > 0){
                        for(var i=0;i<latestLotteryInfo.length;i=i+2){
                            html += "<tr>";
                            html += "<td>" + latestLotteryInfo[i].mobilemask + "</td>";
                            html += "<td>" + latestLotteryInfo[i].prizename + "</td>";
                            if((i + 1) < latestLotteryInfo.length){
                                html += "<td>" + latestLotteryInfo[i+1].mobilemask + "</td>";
                                html += "<td>" + latestLotteryInfo[i+1].prizename + "</td>";
                            }else{
                                html += "<td width='23%'>" + "</td>";
                                html += "<td>" +  "</td>";
                            }
                            html += "</tr>";
                        }
                        $("#latestLotteryInfo").html(html);
                    }
                    // 我的获奖记录
                    if(myLotteryInfo!=null){
                        var htmlMyLottery = "";
                        htmlMyLottery += "<td>" + myLotteryInfo.mobilemask + "</td>";
                        htmlMyLottery += "<td>" + myLotteryInfo.prizename + "</td>";
                        htmlMyLottery += "<td>" + timestampToTime(myLotteryInfo.lotterytime) + "</td>";
                        $("#myLotteryInfo").html(htmlMyLottery);
                    }
                }
                else {
                    layer.msg(obj.msg);
                }
            });

        });

    });

    function rnd(n, m){

        return Math.floor(Math.random()*(m-n+1)+n)

    }

</script>

<script type="text/javascript">
    $(document).ready(function(){

        $.get(www + "lottery/getLotteryInfo1201", { uid: uid }, function (data) {
            var obj = $.parseJSON(data);
            if (obj.code == "200") {
                // 最近获奖记录
                var latestLotteryInfo = obj.data.latestLotteryInfo;
                var html = "";
                if(latestLotteryInfo!=null && latestLotteryInfo.length > 0){
                    for(var i=0;i<latestLotteryInfo.length;i=i+2){
                        html += "<tr>";
                        html += "<td width='24%'>" + latestLotteryInfo[i].mobilemask + "</td>";
                        html += "<td width='27%'>" + latestLotteryInfo[i].prizename + "</td>";

                        if((i + 1) < latestLotteryInfo.length){
                            html += "<td width='23%'>" + latestLotteryInfo[i+1].mobilemask + "</td>";
                            html += "<td>" + latestLotteryInfo[i+1].prizename + "</td>";
                        }else{
                            html += "<td width='23%'>" + "</td>";
                            html += "<td>" +  "</td>";
                        }
                        html += "</tr>";
                    }
                    $("#latestLotteryInfo").html(html);
                }
                // 我的获奖记录
                var myLotteryInfo = obj.data.myLotteryInfo;
                if(myLotteryInfo!=null && myLotteryInfo.length > 0){
                    var htmlMyLottery = "";
                    for(var i=0;i<myLotteryInfo.length;i++){
                        htmlMyLottery += "<td>" + myLotteryInfo[i].mobilemask + "</td>";
                        htmlMyLottery += "<td>" + myLotteryInfo[i].prizename + "</td>";
                        htmlMyLottery += "<td>" + timestampToTime(myLotteryInfo[i].lotterytime) + "</td>";
                    }
                    $("#myLotteryInfo").html(htmlMyLottery);
                }
            }
            else {
                layer.msg(obj.msg);
            }
        });

        $("#n1").click(function(){
            $("#n1a").show();
            $("#n2a").hide();
        });

        $("#n2").click(function(){
            $("#n1a").hide();
            $("#n2a").show();
        });

        $(".pf").click(function(){
            $(".h").show();
            $("#jp").show();
        });

        $(".btn1").click(function(){
            $(".h").show();
            $(".tc_3").show();
        });

        $(".close").click(function(){
            $(".h").hide();
            $("#wzg").hide();
            $("#cf").hide();
            $("#jp").hide();
            $(".tc_2").hide();
            $(".tc_3").hide();
        });

    });

    $('.nav a').click(function () {
		$(this).addClass('on').siblings('a').removeClass('on');
	});

    /*function parseTime(str) {
        var time = str.split('T');
        var day = time[0].split('-')[0] + '-' + time[0].split('-')[1] + '-' + time[0].split('-')[2];
        return day;
    }*/

    function timestampToTime(timestamp) {
        var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
        var Y = date.getFullYear() + '-';
        var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1):date.getMonth()+1) + '-';
        var D = (date.getDate()< 10 ? '0'+date.getDate():date.getDate())+ ' ';
        var h = (date.getHours() < 10 ? '0'+date.getHours():date.getHours())+ ':';
        var m = (date.getMinutes() < 10 ? '0'+date.getMinutes():date.getMinutes()) + ':';
        var s = date.getSeconds() < 10 ? '0'+date.getSeconds():date.getSeconds();
        return Y+M+D+h+m+s;
    }

</script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='http://api2.tongji.emoney.cn/scripts/emoneyanalyticspv.js%3Fcode=E9B1C7CAEA688BB4E9AEC91549ABE92A' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>