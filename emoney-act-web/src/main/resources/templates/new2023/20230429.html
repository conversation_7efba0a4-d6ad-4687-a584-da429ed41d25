<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="上半年政策力度“乘势而上”，国九条的长期效应也将逐步体现，个股投资机会进一步凸显，内外资先知先觉，新一批牛股即将起爆！" />
    <link th:href="@{${staticPath}+'static/new2025/20250701/ty/style/common.css?r=20'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
    <script type="text/javascript" th:src="@{${staticPath}+'static/new2025/20250701/ty/js/jquery.slides.js'}"></script>
    <script type="text/javascript">
        function getQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }
        var sso = location.search.slice(1);
        var tid = getQueryString("tid");
        var paysource = "A12048";
        if (tid == "242") {
            paysource = "A12079";
        }
        var payurl = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=0&businessType=biztyp-xzyxz";

        $(document).ready(function () {
            var actcode = $("#hid_actcode").val();
            $(".linkTO").click(function () {
                $(".h").show();
                var ishelp = $("#hid_isHelp").val();
                var islogin = $("#hid_isLogin").val();
                if (ishelp == "1") {
                    $(".help2").show();
                    $(".help1").hide();
                } else {
                    if (islogin == "1") {
                        $.post("/activity/new2023/sendcode", {actCode: actcode, groupPrc: "288"}, function (data) {

                        });
                    }

                    $(".help1").show();
                    document.getElementById('code1').focus()
                    $(".help2").hide();
                }
            });

            $(".txt2").click(function(){
                $(".h2").show();
            });
            $(".txt3").click(function(){
                $(".h4").show();
            });
            $(".close").click(function () {
                $(".h").hide();
                $(".h2").hide();
                $(".h3").hide();
                $(".h4").hide();
            });
            $('#popupD3,.tc-btn3').click(function (){
                var emObj = "";
                try{
                    emObj = window.utils.GetExternal();
                }catch (e){}
                //客户端
                if (!!emObj) {
                    window.utils.PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + payurl + "&ordersource=" + paysource);
                } else {
                    $(this).attr("target", "_blank");
                    $(this).attr("href", payurl + "&ordersource=" + paysource + "&" + sso);
                }
            });
            $(".zz").click(function (){
                layer.msg("抱歉！您还不是操盘手VIP。无法参与抽奖活动");
                return false;
            })
        });
    </script>
    <script type="text/javascript">
        (function () {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) { }
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) { }
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) { }
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) { }
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
    <script type="text/javascript">
        //----函数定义-----
        function setCookie(c_name, value, expiredays) {
            var exdate = new Date();
            exdate.setDate(exdate.getDate() + expiredays);
            document.cookie = c_name + "=" + escape(value) + ";expires=" + exdate.toGMTString() + ";path=/";
        }

        function getCookie(c_name) {

            if (document.cookie.length > 0) {
                c_start = document.cookie.indexOf(c_name + "=")

                if (c_start != -1) {
                    c_start = c_start + c_name.length + 1
                    c_end = document.cookie.indexOf(";", c_start)
                    if (c_end == -1) c_end = document.cookie.length
                    return unescape(document.cookie.substring(c_start, c_end))
                }
            }
            return ""
        }
    </script>
</head>
<body>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isHelp" th:value="${isHelp}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">
<input type="hidden" id="hid_redisCode" th:value="${redisCode}">
<div class="bod">
    <div class="dbg1"><div class="main"><div class="djs"><span class="t">00</span><span class="s">00</span><span class="f">00</span><span class="m">00</span></div></div></div>
    <div class="dbg2"><div class="main"><div class="pic wow zx" data-wow-duration="1000ms"></div></div></div>
    <div class="dbg3"><div class="main"><a href="javascript:void(0)" class="txt3 b">※ 刮 刮 乐 活 动 规 则 请 戳→</a><a href="javascript:void(0)" class="txt2 b">※ 补贴活动规则请戳→</a>
        <a href="javascript:void(0)" class="btn1 dh linkTO"></a><marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="3" direction="left" class="txt1">
            185****5037 王女士 <span class="red">订购成功</span>      131*****818 崔女士 <span class="red">订购成功</span>    158****8387 章先生 <span class="red">订购成功</span>    189****6867 龚女士 <span class="red">订购成功</span>    157****7068 王先生 <span class="red">订购成功</span>
            183****1988  孙女士  <span class="red">订购成功</span>     150****0355   周女士  <span class="red">订购成功</span>    130****0198     王女士 <span class="red">订购成功</span>       181****5985  陈先生 <span class="red">订购成功</span>   136****1933  于先生  <span class="red">订购成功</span>
            181****9258  朱女士 <span class="red">订购成功</span>       183****9169  孙女士 <span class="red">订购成功</span>       139****7306    李先生  <span class="red">订购成功</span>  139****5168   孟女士 <span class="red">订购成功</span>    157****7987  任先生  <span class="red">订购成功</span>
            152****3459  黎先生  <span class="red">订购成功</span>    134****7068  刘先生  <span class="red">订购成功</span>    138****8597     强女士 <span class="red">订购成功</span>    177****5178   沈女士  <span class="red">订购成功</span>   180****9008   何女士 <span class="red">订购成功</span>
            175****8192      刘先生  <span class="red">订购成功</span>     135****4778  柳先生 <span class="red">订购成功</span>   185****5037 王女士 <span class="red">订购成功</span>      131*****818 崔女士 <span class="red">订购成功</span>    158****8387 章先生 <span class="red">订购成功</span>    189****6867 龚女士 <span class="red">订购成功</span>    157****7068 王先生 <span class="red">订购成功</span>
            183****1988  孙女士  <span class="red">订购成功</span>     150****0355   周女士  <span class="red">订购成功</span>    130****0198     王女士 <span class="red">订购成功</span>       181****5985  陈先生 <span class="red">订购成功</span>   136****1933  于先生  <span class="red">订购成功</span>
            181****9258  朱女士 <span class="red">订购成功</span>       183****9169  孙女士 <span class="red">订购成功</span>       139****7306    李先生  <span class="red">订购成功</span>  139****5168   孟女士 <span class="red">订购成功</span>    157****7987  任先生  <span class="red">订购成功</span>
            152****3459  黎先生  <span class="red">订购成功</span>    134****7068  刘先生  <span class="red">订购成功</span>    138****8597     强女士 <span class="red">订购成功</span>    177****5178   沈女士  <span class="red">订购成功</span>   180****9008   何女士 <span class="red">订购成功</span> 185****5037 王女士 <span class="red">订购成功</span>      131*****818 崔女士 <span class="red">订购成功</span>    158****8387 章先生 <span class="red">订购成功</span>    189****6867 龚女士 <span class="red">订购成功</span>    157****7068 王先生 <span class="red">订购成功</span>
            183****1988  孙女士  <span class="red">订购成功</span>     150****0355   周女士  <span class="red">订购成功</span>    130****0198     王女士 <span class="red">订购成功</span>       181****5985  陈先生 <span class="red">订购成功</span>   136****1933  于先生  <span class="red">订购成功</span>
            181****9258  朱女士 <span class="red">订购成功</span>       183****9169  孙女士 <span class="red">订购成功</span>       139****7306    李先生  <span class="red">订购成功</span>  139****5168   孟女士 <span class="red">订购成功</span>    157****7987  任先生  <span class="red">订购成功</span>
            152****3459  黎先生  <span class="red">订购成功</span>    134****7068  刘先生  <span class="red">订购成功</span>    138****8597     强女士 <span class="red">订购成功</span>    177****5178   沈女士  <span class="red">订购成功</span>   180****9008   何女士 <span class="red">订购成功</span> </marquee></div></div>
    <div class="dbg4b" id="al3"><div class="main">
        <div class="al3"><!-- 代码 开始 -->
            <div class="slider_name3 slider_box3">
                <ul class="silder_con3">
                    <li class="silder_panel3"><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/b1.png'}" /></li>
                    <li class="silder_panel3"><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/b2.png'}" /></li>
                    <li class="silder_panel3"><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/b3.png'}" /></li>
                </ul>
            </div>
            <a href="javascript:void(0)" class="prev"></a><a href="javascript:void(0)" class="next"></a>
            <!-- 代码 结束 -->
        </div>
        <a href="javascript:void(0)" class="btn2 an1 dh linkTO"></a></div></div>
    <div class="dbg5">
        <div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="swf1 ttt1" name="al1"></iframe><a href="javascript:void(0)" class="btn2 an2 dh linkTO"></a></div></div>
    <div class="dbg6" id="al2"><div class="main">
        <div class="main"><div class="al2"><!-- 代码 开始 -->
            <div class="slider_name2 slider_box2">
                <ul class="silder_con2">
                    <li class="silder_panel2"><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/a1.png'}" /></li>
                    <li class="silder_panel2"><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/a2.png'}" /></li>
                    <li class="silder_panel2"><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/a3.png'}" /></li>
                </ul>
            </div>
            <a href="javascript:void(0)" class="prev"></a><a href="javascript:void(0)" class="next"></a>
            <!-- 代码 结束 -->
        </div></div>
        <a href="javascript:void(0)" class="btn2 an2 dh linkTO"></a></div></div>
    <div class="dbg7" id="al1">
        <div class="main"><div class="ico4"><a href="javascript:void(0)" class="qa1"></a><a href="javascript:void(0)" class="qa2"></a></div><div class="swf3"><a href="javascript:void(0)" class="s1">打卡听课最高得3个月使用期</a></div><div class="swf3b"></div><a href="javascript:void(0)" class="btn2 an1 dh linkTO"></a></div></div>
    <div class="dbg8" id="al4"></div>
    <div class="dbg9" id="al6"><div class="main"><div class="al"><!-- 代码 开始 -->
        <div class="slider_name slider_box">
            <ul class="silder_con">
                <li class="silder_panel"><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/p1.png'}"/></li>
                <li class="silder_panel"><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/p2.png'}"/></li>
            </ul>
        </div>
        <a href="javascript:void(0)" class="prev"></a><a href="javascript:void(0)" class="next"></a>
        <!-- 代码 结束 -->
    </div></div></div>
    <div id="dbg8"><div class="main">
        <div id="div1">
            <ul>
                <li><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/zs1.png'}"/></li>
                <li><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/zs2.png'}"/></li>
                <li><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/zs3.png'}"/></li>
                <li><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/zs4.png'}"/></li>
                <li><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/zs5.png'}"/></li>
                <li><img th:src="@{${staticPath}+'static/new2025/20250619/ty/images/zs6.png'}"/></li>
            </ul>
            <a href="javascript:;" style="left:10px;"></a>
            <a href="javascript:;" style="right:10px;"></a>
        </div>
        <script type="text/javascript">
            window.onload = function(){
                var oDiv = document.getElementById('div1');
                var oUl = oDiv.getElementsByTagName('ul')[0];
                var aLi = oUl.getElementsByTagName('li');
                var aA = oDiv.getElementsByTagName('a');
                var iSpeed = 1;//正左负右
                var timer = null;
                //计算ul的宽为所有li的宽的和;
                oUl.innerHTML += oUl.innerHTML+oUl.innerHTML;
                oUl.style.width = aLi[0].offsetWidth*aLi.length+'px';
                function Slider(){
                    if (oUl.offsetLeft<-oUl.offsetWidth/2) {
                        oUl.style.left = 0;
                    }else if(oUl.offsetLeft>0){
                        oUl.style.left =-oUl.offsetWidth/2+'px';
                    }
                    oUl.style.left = oUl.offsetLeft-iSpeed+'px';//正负为方向
                }
                timer =setInterval(Slider,30);
                aA[0].onclick = function(){
                    iSpeed = 1; //控制速度的正负
                }
                aA[1].onclick = function(){
                    iSpeed = -1;
                }
                oDiv.onmouseover = function(){
                    clearInterval(timer);
                }
                oDiv.onmouseout = function(){
                    timer =setInterval(Slider,30);
                }
            };
        </script></div></div>
    <div class="dbg1"><div class="main"><div class="djs"><span class="t">00</span><span class="s">00</span><span class="f">00</span><span class="m">00</span></div></div></div>
    <div class="dbg2"><div class="main"><div class="pic wow zx" data-wow-duration="1000ms"></div></div></div>
    <div class="dbg3"><div class="main"><a href="javascript:void(0)" class="txt3 b">※ 刮 刮 乐 活 动 规 则 请 戳→</a><a href="javascript:void(0)" class="txt2 b">※ 补贴活动规则请戳→</a>
        <a href="javascript:void(0)" class="btn1 dh linkTO"></a><marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="3" direction="left" class="txt1">
            185****5037 王女士 <span class="red">订购成功</span>      131*****818 崔女士 <span class="red">订购成功</span>    158****8387 章先生 <span class="red">订购成功</span>    189****6867 龚女士 <span class="red">订购成功</span>    157****7068 王先生 <span class="red">订购成功</span>
            183****1988  孙女士  <span class="red">订购成功</span>     150****0355   周女士  <span class="red">订购成功</span>    130****0198     王女士 <span class="red">订购成功</span>       181****5985  陈先生 <span class="red">订购成功</span>   136****1933  于先生  <span class="red">订购成功</span>
            181****9258  朱女士 <span class="red">订购成功</span>       183****9169  孙女士 <span class="red">订购成功</span>       139****7306    李先生  <span class="red">订购成功</span>  139****5168   孟女士 <span class="red">订购成功</span>    157****7987  任先生  <span class="red">订购成功</span>
            152****3459  黎先生  <span class="red">订购成功</span>    134****7068  刘先生  <span class="red">订购成功</span>    138****8597     强女士 <span class="red">订购成功</span>    177****5178   沈女士  <span class="red">订购成功</span>   180****9008   何女士 <span class="red">订购成功</span>
            175****8192      刘先生  <span class="red">订购成功</span>     135****4778  柳先生 <span class="red">订购成功</span>   185****5037 王女士 <span class="red">订购成功</span>      131*****818 崔女士 <span class="red">订购成功</span>    158****8387 章先生 <span class="red">订购成功</span>    189****6867 龚女士 <span class="red">订购成功</span>    157****7068 王先生 <span class="red">订购成功</span>
            183****1988  孙女士  <span class="red">订购成功</span>     150****0355   周女士  <span class="red">订购成功</span>    130****0198     王女士 <span class="red">订购成功</span>       181****5985  陈先生 <span class="red">订购成功</span>   136****1933  于先生  <span class="red">订购成功</span>
            181****9258  朱女士 <span class="red">订购成功</span>       183****9169  孙女士 <span class="red">订购成功</span>       139****7306    李先生  <span class="red">订购成功</span>  139****5168   孟女士 <span class="red">订购成功</span>    157****7987  任先生  <span class="red">订购成功</span>
            152****3459  黎先生  <span class="red">订购成功</span>    134****7068  刘先生  <span class="red">订购成功</span>    138****8597     强女士 <span class="red">订购成功</span>    177****5178   沈女士  <span class="red">订购成功</span>   180****9008   何女士 <span class="red">订购成功</span> 185****5037 王女士 <span class="red">订购成功</span>      131*****818 崔女士 <span class="red">订购成功</span>    158****8387 章先生 <span class="red">订购成功</span>    189****6867 龚女士 <span class="red">订购成功</span>    157****7068 王先生 <span class="red">订购成功</span>
            183****1988  孙女士  <span class="red">订购成功</span>     150****0355   周女士  <span class="red">订购成功</span>    130****0198     王女士 <span class="red">订购成功</span>       181****5985  陈先生 <span class="red">订购成功</span>   136****1933  于先生  <span class="red">订购成功</span>
            181****9258  朱女士 <span class="red">订购成功</span>       183****9169  孙女士 <span class="red">订购成功</span>       139****7306    李先生  <span class="red">订购成功</span>  139****5168   孟女士 <span class="red">订购成功</span>    157****7987  任先生  <span class="red">订购成功</span>
            152****3459  黎先生  <span class="red">订购成功</span>    134****7068  刘先生  <span class="red">订购成功</span>    138****8597     强女士 <span class="red">订购成功</span>    177****5178   沈女士  <span class="red">订购成功</span>   180****9008   何女士 <span class="red">订购成功</span> </marquee></div></div>
</div>
<div class="footer">
    欢迎登录益盟官方网站 <a class="b" style="color:white;" href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340
</div>
<div class="pf">
    <a href="javascript:void(0)" class="close pfx"></a><img th:src="@{${staticPath}+'static/new2025/20250701/ty/images/pf.png'}" alt="" usemap="#Map" border="0" />
    <map name="Map" id="Map"><area shape="rect" coords="55,129,219,160" href="#al3"/><area shape="rect" coords="55,171,222,206" href="#al2" />
        <!--<area shape="rect" coords="48,239,224,274" href="#al5" class="s5" />-->
        <area shape="rect" coords="55,214,221,249" href="#al1" />
        <area shape="rect" coords="55,259,220,291" href="#al4" />
        <area shape="rect" coords="55,301,221,338" href="#al6" />
        <area shape="rect" coords="42,372,233,432" href="javascript:void(0)" class="dh linkTO" />
    </map>
</div>
<div class="h">
    <div class="tc2 help1">
        <a href="javascript:void(0)" class="close">
        </a><div class="txt">
        团购码：<span class="yellow" th:text="${redisCode}"></span>
        <div class="f16">
            再次输入确认：
            <input type="text" id="code1" onkeyup="document.getElementById('code2').focus()" />
            <input type="text" id="code2" onkeyup="document.getElementById('code3').focus()" />
            <input type="text" id="code3" onkeyup="document.getElementById('code4').focus()" />
            <input type="text" id="code4" />
        </div>
        <a onclick="conformjoin()" href="javascript:void(0)" class="tc-btn2" id="popupD"></a>
        <div class="f14">*一号一码，确认无误后自动锁定名额</div>
    </div>
    </div>
    <div class="tc2 help2" style="display: none">
        <a href="javascript:void(0)" class="close"></a><div class="txt">
        您已获取团购资格<br />团购码：<span class="yellow" th:text="${redisCode}"></span><a href="javascript:void(0)" class="tc-btn3" target="_blank"></a>
        <div class="f14">*请及时锁定您的团购资格，以免超时取消。</div>
    </div>
    </div>
</div>
<div class="h2"><div class="hdgz"><a href="javascript:void(0)" class="close"></a><div class="bt1">金豆补贴软件费活动详情</div><div class="bt2">合作券商</div>湘财证券
    <div class="bt2">活动规则</div>1、开户时间在<span class="rq">2025年7月1日-2025年7月17日</span>的益盟操盘手用户，可获得操盘手金豆，金豆可用于兑换软件费补贴<br />
    2、<span class="red2">补贴金额为实付金额-1元，且补贴金额最高不超过288元</span><br />
    3、金豆数量=补贴金额*100，100金豆=1元<br />
    举例：<br />
    实付388的用户，获得28800金豆，补贴288元<br />
    实付288的用户，获得28700金豆，补贴287元<br />
    实付188的用户，获得18700金豆，补贴187元
    <div class="bt2">活动说明</div>
    1、本活动仅限益盟操盘手年版及以上用户参与，每位用户仅可参与一次开户活动，历史已经参与过的不可再重复参加活动；<br />
    2、本活动需通过益盟专属二维码或链接进行开户，开户手机号需与订单手机号一致，开户营业部需为湘财证券国权北路营业部，且需为该券商新用户；<br />
    3、开户成功:沪深双市场均开立成功并绑定银行卡；<br />
    4、金豆将在开户成功后的3个交易日内到账，获得金豆之日起15-365天可在益盟软件中一次性兑换软件费补贴，具体规则可咨询您的益盟服务专员；<br />
    5、本活动中得券商福利需通过湘财证券百宝湘APP进行开通领取，活动开户账户仅为普通沪深账户，需办理其他证券相关业务或咨询券商福利相关问题可拨打湘财服务热线021-60331188转2；<br />
    6、本次开户活动有效期为<span class="rq">2025年7月1日-2025年7月17日</span>；<br />
    7、本活动最终解释权归益盟股份有限公司。</div></div>
<div class="h4"><div class="hdgz2"><a href="javascript:void(0)" class="close"></a><div class="bt1">刮刮乐活动规则详情</div><div class="bt2">规则说明</div>1、2025年7月1日-7月17日活动期间购买操盘手的新用户获得3次刮刮乐机会(100%中奖)，每个手机号仅限参与1次活动；<br />
    2、使用期以延期方式赠送，例如A用户成功购买操盘手VIP，在活动期间抽中操盘手1个月，将额外获得操盘手1个月使用期，到手1年+1个月；<br />
    3、刮刮乐奖品将在1个工作日内充值至购买账号；<br />
    4、若后续发生退货行为，抽奖奖品将一律退还；<br />
    5、活动最终解释权归益盟股份有限公司所有。</div>
</div>
<div class="h3"><div class="tc3"><a href="javascript:void(0)" class="close"></a>1、活动对象：仅限操盘手正式用户；<br />
    2、听课福利：听课任意1节奖励1个月操盘手使用期，听课任意3节再奖励1个月操盘手使用期，听课5节再奖励1个月操盘手使用期，共奖励3个月操盘手使用期（听课≥30分钟/课，直播和回放时长不互相累加）；<br />
    3、《操盘手第一课》听课奖励使用期活动，每位用户仅限参加一次，往期活动中已获得3个月操盘手使用期奖励用户不重复奖励。<br />
    4、本活动最终解释权归益盟股份有限公司所有。</div></div>
<a class="" href="javascript:void(0)" id="popupD3"> </a>
<form target="_blank" id="form" method="post"></form>
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">
<script>
    var staticPath = $("#hid_staticPath").val();
    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    $('.hdgzk').click(function () {$('#hdgz').show()})
    $(".s1").click(function(){
        $(".h3").show();
    });
    $(".qa1").click(function(){
        $(".ico4").css('background-position', '0 0');
        $(".swf3").show();
        $(".swf3b").hide();
    });
    $(".qa2").click(function(){
        $(".ico4").css('background-position', '0 -92px');
        $(".swf3").hide();
        $(".swf3b").show();
    });

    $('.pfx').click(function () {$(".pf").hide();downflag = "1";});
    $(window).scroll(function(e){
        if(($(window).scrollTop() >= 840) && (downflag == "0")){
            $('.pf').fadeIn(300);
        }else{
            $('.pf').fadeOut(300);
        }
        if(($(window).scrollTop() > 1200) && (al1 == "0")){
            $("[name='al1']").attr("src","https://www.emoney.cn/dianjin/mf/sbql/sbql.html");
            al1 = "1";
        }
    });

</script>

<script type="text/javascript">
    $('.al2 a').click(function () {
        $(this).addClass('current').siblings('a').removeClass('current');
    });

    function conformjoin() {
        var joincode = $("#code1").val() + $("#code2").val() + $("#code3").val() + $("#code4").val();

        var actcode = $("#hid_actcode").val();
        var rediscode = $("#hid_redisCode").val();
        var islogin = $("#hid_isLogin").val();
        if (joincode != rediscode) {
            alert("请输入正确的团购码");
            return false;
        }
        var tid = getQueryString("tid");
        var paysource = "A12048";
        if (tid == "242") {
            paysource = "A12079";
        }
        if (islogin == "1") {
            $.post("/activity/new2023/conformhelp", {yzm: joincode, actCode: actcode}, function (data) {
                // $(".h").hide();
                $('#popupD3').attr("href", payurl + "&ordersource=" + paysource + "&" + sso);
                $('#popupD3')[0].click();

                $(".help2").show();
                $(".help1").hide();
                // window.location.reload();
            })
        } else {
            $(".h").hide();
            $('#popupD3')[0].click();
        }
    }

</script>
<script>
    function SetTimeout(year, month, day, hour, minute, second, targetElement) {
        var leftTime = (new Date(year, month - 1, day, hour, minute, second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10); //计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10); //计算剩余的秒数


        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(targetElement).find(".t").html(days);
        $(targetElement).find(".s").html(hours);
        $(targetElement).find(".f").html(minutes);
        $(targetElement).find(".m").html(seconds);
    }

    function checkTime(i) { //将0-9的数字前面加上0，例1变为01
        if (i < 10) {
            i = "0" + i;
        }
        return i;
    }
    djs=setInterval(function() { SetTimeout(2025,7, 17, 24, 0, 0, ".djs"); }, 1000);

    var classdate = new Date();

    var a=new Date("2025/7/17 24:00:00");
    var c=new Date("2025/7/11 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("00");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
    if (classdate.getTime() > c.getTime()) {
        $(".djs").show();
    }
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=F1906E5A3FE0C4277B7FA93CECAA1E07' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>