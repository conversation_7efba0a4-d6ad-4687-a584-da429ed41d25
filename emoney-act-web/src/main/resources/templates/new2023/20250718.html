<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="盛夏战龙头 刮出好彩头" />
    <link th:href="@{${staticPath}+'static/new2025/20250718/new/style/common.css?r=20'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>
<body>
<div class="bod">
    <div class="dbg1"></div>
    <div class="dbg2">
        <div class="main"><div class="txt1">可开次数：</div><div class="ico dh2"></div>
            <div class="zj">
                <ul id="my_list"></ul>
            </div>
            <div class="md">
                <ul id="user_list">
                </ul>
            </div>
            <a href="javascript:void(0)" class="btn1 dh"></a>
        </div>
    </div>
    <div class="dbg3">
        <div class="main"><a href="javascript:void(0)" class="txt3 b">※ 开 盲 盒 活 动 规 则 请 戳→</a></div>
    </div>
</div>

<div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="h4"><div class="hdgz2"><a href="javascript:void(0)" class="close"></a><div class="bt1">盲盒活动规则详情</div><div class="bt2">规则说明</div>1、2025年7月18日-7月31日活动期间购买操盘手的新用户获得1次开盲盒机会(100%中奖)，每个手机号仅限参与1次活动；<br />
    2、使用期以延期方式赠送，例如A用户成功购买操盘手VIP，在活动期间抽中操盘手6个月，将额外获得操盘手6个月使用期，到手1年+6个月；<br />
    3、盲盒奖品将在1个工作日内充值至购买账号；<br />
    4、若后续发生退货行为，抽奖奖品将一律退还；<br />
    5、活动最终解释权归益盟股份有限公司所有。</div>
</div>
<div class="h" style="display: none;">
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
    <div class="tc" style="display: none">
        <a href="javascript:void(0)" class="close"></a><div class="t1">恭喜您获得</div>
        <div class="t2"></div>
        <a href="javascript:void(0)" class="btn">收下奖品</a>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20250515'}"></script>
<script th:src="@{${staticPath}+'static/new2025/20250718/new/js/main.js?r=20250515'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=F1906E5A3FE0C4277B7FA93CECAA1E07' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>