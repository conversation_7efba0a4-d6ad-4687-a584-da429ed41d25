<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,操盘手,抽奖,赢大奖" />
    <meta name="Description" content="【强势动力学】+【龙行量王】限时联合招生 学形态 懂趋势 学量能 找爆发" />
    <link th:href="@{${staticPath}+'static/qyh/20250707/css/css.css?r=20250527'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script th:src="@{${staticPath}+'static/js/awardRotate.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "五星投教抢优惠活动");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>
<body>
<div class="dbg1"></div>
<div class="dbg2"><div class="main"><div class="day"><span class="t"></span></div><div class="djs d1">000</div><a href="" class="btn1 dh"></a></div></div>
<div class="dbg3"><div class="main">
    <div class="zp"></div>
    <div class="zp0"></div>
    <div class="bg1"></div>
    <a href="###" class="zz" id="rotate"></a>
    <div class="go" id="btn-lottery"><img th:src="@{${staticPath}+'static/qyh/20250707/images/go.png'}"/></div>
    <div class="md">
        <marquee onmouseover="this.stop()" onmouseout="this.start()" scrollamount="2" direction="up" height="140px">
            <ul id="user_list">
                <li>恭喜 em19**53 用户喜获<span>五星投教使用期30天</span></li>
                <li>恭喜 em64**59 用户喜获<span>500积分</span></li>
                <li>恭喜 em24**61 用户喜获<span>五星投教使用期90天</span></li>
                <li>恭喜 em34**37 用户喜获<span>2000积分</span></li>
                <li>恭喜 em86**95 用户喜获<span>五星投教使用期60天</span></li>
                <li>恭喜 em88**83 用户喜获<span>五星投教使用期30天</span></li>
                <li>恭喜 em71**11 用户喜获<span>1000积分</span></li>
                <li>恭喜 em10**65 用户喜获<span>500积分</span></li>
                <li>恭喜 em97**07 用户喜获<span>2000积分</span></li>
                <li>恭喜 em35**78 用户喜获<span>1000积分</span></li>
                <li>恭喜 em69**79 用户喜获<span>1000积分</span></li>
                <li>恭喜 em45**75 用户喜获<span>五星投教使用期60天</span></li>
                <li>恭喜 em56**70 用户喜获<span>五星投教使用期90天</span></li>
                <li>恭喜 em32**10 用户喜获<span>五星投教使用期30天</span></li>
                <li>恭喜 em42**92 用户喜获<span>五星投教使用期30天</span></li>
                <li>恭喜 emy82**25 用户喜获<span>500积分期</span></li>
                <li>恭喜 em18**37 用户喜获<span>500积分</span></li>
            </ul>
        </marquee>
        <ul style="margin-top: 45px;" id="my_list">
        </ul>
    </div>
    <div class="hdgz">抽奖规则
        <ul>
            <li>6月24日至7月13日新购投教用户，可获抽奖机会（老用户换购投教不参与抽奖）</li>
            <li>新购：无论单投教/双投教均可抽奖，100%中奖；</li>
            <li>抽奖礼品将在活动结束后的10个工作日内发送；</li>
            <li>抽奖开放时间:6月24日-7月13日，限时活动，错过不再有；</li>
            <li>若发生退货行为，奖品一律退还；</li>
            <li>活动最终解释权归益盟股份有限公司所有。</li>
        </ul>
    </div></div></div>
<div class="dbg4"><div class="main"><a href="" class="btn3 dh"></a></div></div>
<div class="dbg5"><div class="main">
    <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=93e2029f07464388a2da35fcd1c22c0d&rep=1&py=0" scrolling="no" class="sp an1" name="sp"></iframe>
</div></div>
<div class="dbg6"><div class="main">
    <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a296cd77525b47a7b505f30755e7fab9&rep=1&py=0" scrolling="no" class="sp an2" name="sp"></iframe>
</div></div>
<div class="dbg7">
    <div class="main">
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=91a40865a96441abbdeb0b6f8f73711f&rep=1&py=0" scrolling="no" class="sp an3" name="sp"></iframe>
    </div></div>
<div class="dbg8"></div>
<div class="dbg9"></div>
<div class="dbg10"><div class="main"><!-- 代码 开始 -->
    <ul class="silder_nav">
        <li class=""></li>
        <li class=""></li>
    </ul>
    <div class="slider_name slider_box pic1">
        <ul class="silder_con">
            <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qyh/20250707/images/a2.png'}" alt="" /></li>
            <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qyh/20250707/images/a1.png'}" alt="" /></li>
        </ul>
    </div>
    <!-- 代码 结束 -->
</div></div>
<div class="footer">欢迎登录益盟官方网站www.emoney.cn<br />
    本活动最终解释权归上海益盟软件技术股份有限公司　沪ICP备06000340<br />
    软件需数据支持，仅提供辅助建议，风险需谨慎；投资顾问意见仅作参考。</div>
<div class="pf">
    <div class="main"><a href="" class="btn2 dh"></a></div></div>
<div class="bg" style="display: none">
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
    <div class="tc" style="display: none">
        <a href="###" class="close"></a>
        <div class="tc_1">恭喜您获得<br />
            投教使用期<span class="red">1个月</span></div>
    </div>
</div>
<a href="" class="pf2"></a>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/qyh/20250707/js/main.js?r=20241112'}"></script>
<script type="text/javascript" th:src="@{${staticPath}+'static/qyh/20250707/js/jquery.slides.js'}"></script>
<script>
    function SetTimeout(year, month, day, hour, minute, second, targetElement) {
        var leftTime = (new Date(year, month - 1, day, hour, minute, second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10); //计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10); //计算剩余的秒数


        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(targetElement).find(".t").html(days);
    }

    function checkTime(i) { //将0-9的数字前面加上0，例1变为01
        if (i < 10) {
            i = i;
        }
        return i;
    }
    day=setInterval(function() { SetTimeout(2025,7, 13, 24, 0, 0, ".day"); }, 1000);

    var classdate = new Date();

    var a=new Date("2025/7/13 24:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(day);
        $(".t").html("0");
    }
</script>
<script type="text/javascript">
    var videoList = $('iframe')
    var wHeigt = window.innerHeight
    document.addEventListener('scroll', function () {
        var isPlay = false
        //滚动条高度+视窗高度 = 可见区域底部高度
        var visibleBottom = window.scrollY + document.documentElement.clientHeight
        //可见区域顶部高度
        var visibleTop = window.scrollY
        for (var i = 0; i < videoList.length; i++) {
            var centerY = $(videoList[i]).offset().top + (videoList[i].offsetHeight / 2)
            if (centerY > visibleTop && centerY < visibleBottom) {
                if (!isPlay) {
                    videoList[i].src.match(/py=0/) && (videoList[i].src = videoList[i].src.replace(/py=0/, 'py=1'))
                    isPlay = true
                } else {
                    videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
                }
            } else {
                videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
            }
        }
    })
</script>
</body>
</html>