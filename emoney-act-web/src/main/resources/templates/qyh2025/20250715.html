<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>高效擒龙头，稳赢三季度</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="稳增长 新经济 新机遇 跟主力 盯主线 决战大波段 智盈大师掘金版--对的时间做对的选择">
    <link th:href="@{${staticPath}+'static/qyh/20250715/style/common.css?r=20250711'}" rel="stylesheet" type="text/css" />
    <link th:href="@{${staticPath}+'static/qyh/20250715/style/popup.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qyh/20231205/js/json2.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qgn/20231011/js/popup.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var www = "../";
        function GetExternal () {
            return window.external.EmObj
        }

        function PC_JH (type, c) {
            try {
                var obj =
                    GetExternal()
                return obj.EmFunc(type, c)
            } catch (e) {}
        }

        (function () {

            function LoadComplete () {
                try {
                    PC_JH('EM_FUNC_DOWNLOAD_COMPLETE', '')
                } catch (ex) {}
            }

            function EM_FUNC_HIDE () {
                try {
                    PC_JH('EM_FUNC_HIDE', '')
                } catch (ex) {}
            }

            function EM_FUNC_SHOW () {
                try {
                    PC_JH('EM_FUNC_SHOW', '')
                } catch (ex) {}
            }

            function IsShow () {
                try {
                    return PC_JH('EM_FUNC_WND_ISSHOW', '')
                } catch (ex) {
                    return '0'
                }
            }

            function openWindow () {
                LoadComplete()

                PC_JH('EM_FUNC_WND_SIZE', 'w=1300,h=820,mid')

                if (IsShow() != '1') {
                    EM_FUNC_SHOW()
                }
            }

            openWindow()
        })()

    </script>
</head>
<body>
<div class="bg9">
    <div class="sppf">
        <div class="main"><a href="javascript:void(0)" class="sppfbtn"></a></div>
    </div>
    <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="sp"></iframe>
    <a href="javascript:void(0)" class="sppfbtn2"></a></div>
<div class="bod">
    <div class="dbg1"></div>
    <div class="dbg2"></div>
    <div class="dbg3">
        <div class="main">
            <div class="txt1">已有<span name="usedCount"></span>人申请</div>
            <div class="djs"></div><a href="javascript:void(0)" class="btn1 dh" clickkey="20250715act" clickdata="btn1"></a>
        </div>
    </div>
    <div class="dbg4">
        <div class="main"><a href="javascript:void(0)" class="btn4 dh3 popupD" flag="openpop" data-accode="PREFERENTIAL20250703292514"
                             data-addclass="btn4h" data-type="1" clickkey="20250715act" clickdata="buy1"></a> <a
                href="javascript:void(0)" class="btn5 dh3 popupD" flag="openpop" data-accode="PREFERENTIAL20250703292518"
                data-addclass="btn5h" data-type="2" clickkey="20250715act" clickdata="buy2"></a> <a id="con"
                href="javascript:void(0)" class="btn6 dh popupD" flag="openpop" data-accode="PREFERENTIAL20250703292522"
                data-addclass="btn6h" data-type="3" clickkey="20250715act" clickdata="buy3"></a><div class="ico2b dh2"></div>
            <div id="k"></div>
            <script type="text/javascript">
                function bh () {
                    var i = document.getElementById('k')
                    i.style.display = 'block'
                    setTimeout('clock()', 3000)
                }

                function clock () {
                    var i = document.getElementById('k')
                    i.style.display = 'none'
                }

            </script></div>
    </div>
    <div class="bg bgc" id="a1">
        <ul id="yd2b">
            <a class="current iframe-link" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=782bf425ce774c55a5803eb64e08951a&rep=1&py=1" target="al2">
                <div class="f1">【伏击活跃】2025.5.7出击</div>
                <div class="f3">利君股份 11个交易日 涨<span class="f2">101.64%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=500c78c83d8d4957939cdbd915ad9692&rep=1&py=1" target="al2" class="iframe-link">
                <div class="f1">【资金金叉】2025.6.3出击</div>
                <div class="f3">昂利康 9个交易日 涨<span class="f2">74.97%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=dbc32f6ff9334cf49232a2de3f7ac283&rep=1&py=1" target="al2" class="iframe-link">
                <div class="f1">【量王叠现】2025.5.29出击</div>
                <div class="f3">汇金股份 10个交易日 涨<span class="f2">74.67%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=b29be07210504ba88b766be18bcc4a01&rep=1&py=1" target="al2" class="iframe-link">
                <div class="f1">【大阳起势】2025.6.6出击</div>
                <div class="f3">杰普特 8个交易日 涨<span class="f2">38.32%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=9782be43e91c49069c96f3a4accc431a&rep=1&py=1" target="al2" class="iframe-link">
                <div class="f1">【纵横趋势-突破K】2025.5.20出击</div>
                <div class="f3">新诺威 20个交易日 涨<span class="f2">33.08%</span></div>
            </a>
        </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=782bf425ce774c55a5803eb64e08951a&rep=1&py=0" scrolling="no" class="swf1" name="al2"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250715act" clickdata="btn10"></a> </div>
    </div>
    <div class="bg bgd" id="a2">
        <ul id="yd3b">
            <a class="current iframe-link" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=998e3d52b697489baeeb057b60cfef54&rep=1&py=1" target="al3">
                <div class="f1">【锅底右侧】2025.6.3出击</div>
                <div class="f3">百洋医药 9个交易日 涨<span class="f2">36.09%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=7bfccda5cb944c3188369fd2a60543a1&rep=1&py=1" target="al3" class="iframe-link">
                <div class="f1">【蹦极新生】2025.6.3出击</div>
                <div class="f3">康力源 6个交易日 涨<span class="f2">64.82%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=4e035cf52ea94901b598ca808944d219&rep=1&py=1" target="al3" class="iframe-link">
                <div class="f1">【黄金回踩】2025.6.4出击</div>
                <div class="f3">华脉科技 4个交易日 涨<span class="f2">38.99%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=cb64f85268604129bbc94c84006f9f0d&rep=1&py=1" target="al3" class="iframe-link">
                <div class="f1">【高星掘金】2025.5.27出击</div>
                <div class="f3">锦泓集团 7个交易日 涨<span class="f2">62.30%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=2e7aba3179554aac9d1c4dd9b629f9d8&rep=1&py=1" target="al3" class="iframe-link">
                <div class="f1">【星级监控】2025.5.26出击</div>
                <div class="f3">巨人网络 16个交易日 涨<span class="f2">44.14%</span></div>
            </a>
        </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=998e3d52b697489baeeb057b60cfef54&rep=1&py=0" scrolling="no" class="swf1" name="al3"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250715act" clickdata="btn10"></a> </div>
    </div>

    <div class="bg" id="a3">
        <ul id="yd1b">
            <a class="current iframe-link" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=788304b2f58d434b8d3f887c61c73fcd&rep=1&py=1" target="al1">
                <div class="f1">【黄金坑】2025.4.10出击</div>
                <div class="f3">威星智能 22个交易日 涨<span class="f2">43.78%</span></div>
            </a><a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=950356d6a3ff492295b2723950e4f417&rep=1&py=1" target="al1" class="iframe-link">
            <div class="f1">【冰谷+潜龙】2025.3.13出击</div>
            <div class="f3">长白山 10个交易日 涨<span class="f2">24.77%</span></div>
        </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=8a0ec016c32c4f26bdb3afbb295020c1&rep=1&py=1" target="al1" class="iframe-link">
                <div class="f1">【价格冲击波】2025.4.30出击</div>
                <div class="f3">斯菱股份 19个交易日 涨<span class="f2">58.03%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=6497758b3fbb460b9bd5344f3079bb38&rep=1&py=1" target="al1" class="iframe-link">
                <div class="f1">【纵横趋势-底部K】2025.4.18出击</div>
                <div class="f3">硕贝德 13个交易日 涨<span class="f2">33.19%</span></div>
            </a>
        </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=788304b2f58d434b8d3f887c61c73fcd&rep=1&py=0" scrolling="no" class="swf1"
                name="al1"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250715act" clickdata="btn10"></a> </div>
    </div>

    <div class="bg bge" id="a4">
        <ul id="yd4b">
            <a class="current iframe-link" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=bb805295a2084f809d1793bbd5359c6c&rep=1&py=1" target="al4">
                <div class="f1">【穿越长线龙头】2023.1.4锁定</div>
                <div class="f3">农业银行 区间累计 涨<span class="f2">130+%</span></div>
            </a><a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a31a3d3c90cc4c22b5367c71b12e8978&rep=1&py=1" target="al4" class="iframe-link">
            <div class="f1" style="margin-top: -7px">【操盘全球】全球资产ETF平台</div>
            <div class="f3">2025.4.10锁定  东南亚科技ETF<br />17个交易日 涨<span class="f2">22.32%</span></div>
        </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=7d9862ddbbe144e0a7b831619b08bcab&rep=1&py=1" target="al4" class="iframe-link">
                <div class="f1">【基石F4】稳健资产组合模板</div>
                <div class="f3">打造多元资产 享专业配置红利</div>
                <div class="ico1"></div>
            </a>
        </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=bb805295a2084f809d1793bbd5359c6c&rep=1&py=0" scrolling="no" class="swf1" name="al4"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250715act" clickdata="btn10"></a> </div>
    </div>
    <div class="bg bgb" id="a5">
        <ul>
            <a class="current iframe-link" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=956e5e0f1a6745c19a84dc1d3847ce4c&rep=1&py=1" target="al4b">
                <div class="f1">【牛散资金】2025.4.28锁定</div>
                <div class="f3">南都物业 23个交易日 涨<span class="f2">36.58%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=3b455db6510449018afa59f21aa24b95&rep=1&py=1" target="al4b" class="iframe-link">
                <div class="f1">【主力识别】2025.4.11锁定</div>
                <div class="f3">今创集团 40个交易日 涨<span class="f2">74.25%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a9012fc5a3d44073abc0dc5d51c9b4d6&rep=1&py=1" target="al4b" class="iframe-link">
                <div class="f1">【黑马雷达】2025.5.19锁定</div>
                <div class="f3">棕榈股份 4个交易日 涨<span class="f2">46.46%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=81564d8b64dc48089b52bbf8b33a287d&rep=1&py=1" target="al4b" class="iframe-link">
                <div class="f1">【席位龙虎榜】2025.5.15锁定</div>
                <div class="f3">拉芳家化 6个交易日 涨<span class="f2">45.67%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=d3006d1a05674f9d81c0e942b849eecc&rep=1&py=1" target="al4b" class="iframe-link">
                <div class="f1">【机构调研】2025.4.29锁定</div>
                <div class="f3">潮宏基 12个交易日 涨<span class="f2">54.30%</span></div>
            </a>
        </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=956e5e0f1a6745c19a84dc1d3847ce4c&rep=1&py=0" scrolling="no" class="swf1" name="al4b"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250715act" clickdata="btn10"></a> </div>
    </div>
    <div class="bg10" id="a6"></div>
    <div class="bgf"><ul>
        <a class="current iframe-link" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=ae621983c65d43c98726af41e33c3be4&rep=1&py=1" target="al5">
            <div class="f1">【主题布局】主题强</div>
            <div class="f3">选有业绩、政策支持的大主题</div>
        </a><a th:href="@{${staticPath}+'static/qyh/20250715/images/a1.png'}" target="al5" class="iframe-link">
        <div class="f1">【风口全景】预期强</div>
        <div class="f3">看近期持续性较长的强风口</div>
    </a>
        <a th:href="@{${staticPath}+'static/qyh/20250715/images/a2.png'}" target="al5" class="iframe-link">
            <div class="f1">【三线强势】趋势强</div>
            <div class="f3">寻走势强于指数的板块</div>
        </a>
        <a th:href="@{${staticPath}+'static/qyh/20250715/images/a3.png'}" target="al5" class="iframe-link">
            <div class="f1">【主力动向】资金进</div>
            <div class="f3">锁资金流入多的板块/个股</div>
        </a>
    </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=ae621983c65d43c98726af41e33c3be4&rep=1&py=0" scrolling="no" class="swf2" name="al5"></iframe>
        <div class="txt4"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250715act" clickdata="btn10"></a> </div>
    </div>
    <div class="bg12" id="a7"></div>
    <div class="bgg">
        <ul>
            <a class="current iframe-link" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=c8773c69b391449490c95dfd89ac38bb&rep=1&py=1" target="al6">
                <div class="f1">【股力值】</div>
                <div class="f3">优选蓄势潜伏买点</div>
                <div class="ico2"></div>
                <div class="ico3"></div>
            </a><a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=45bf7293932e4b61aee4b153358e36f5&rep=1&py=1" target="al6" class="iframe-link">
            <div class="f1">【K线故事】</div>
            <div class="f3">看关键K线、估成本均价</div>
        </a>
            <a th:href="@{${staticPath}+'static/qyh/20250715/images/b1.png'}" target="al6" class="iframe-link">
                <div class="f1">【价格冲击波】</div>
                <div class="f3">寻底部拐点、突破加速</div>
            </a>
            <a th:href="@{${staticPath}+'static/qyh/20250715/images/b2.png'}" target="al6" class="iframe-link">
                <div class="f1">【纵横趋势】</div>
                <div class="f3">辨趋势、找突破</div>
            </a>
            <a th:href="@{${staticPath}+'static/qyh/20250715/images/b3.png'}" target="al6" class="iframe-link">
                <div class="f1">【强龙起势】</div>
                <div class="f3">识转折、起势信号</div>
            </a>
        </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=c8773c69b391449490c95dfd89ac38bb&rep=1&py=0" scrolling="no" class="swf2" name="al6"></iframe>
        <div class="txt4"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250715act" clickdata="btn10"></a> </div>
    </div>
    <div class="dbg5" id="a8"></div>
    <div class="dbg6"><div class="main"><!-- 代码 开始 -->
        <ul class="silder_nav">
            <li class=""></li>
            <li class=""></li>
        </ul>
        <div class="slider_name slider_box pic1">
            <ul class="silder_con">
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qyh/20250715/images/c1.jpg'}" alt="" /></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qyh/20250715/images/c2.png'}" alt="" /></li>
            </ul>
        </div>
        <script type="text/javascript" th:src="@{${staticPath}+'static/qyh/20250715/js/jquery.slides.js'}"></script>
        <!-- 代码 结束 -->
    </div></div>
    <div class="dbg7">
        <div class="main">
            <div id="div1">
                <ul>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250715/images/zs10.jpg'}" /></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250715/images/zs1.png'}" /></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250715/images/zs2.png'}" /></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250715/images/zs3.png'}" /></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250715/images/zs4.png'}" /></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250715/images/zs5.png'}" /></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250715/images/zs6.png'}" /></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250715/images/zs7.png'}" /></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250715/images/zs8.png'}" /></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250715/images/zs9.png'}" /></li>
                </ul>
                <a href="javascript:;" style="left:10px;"></a> <a href="javascript:;" style="right:10px;"></a></div>
            <script type="text/javascript">
                window.onload = function () {
                    var oDiv = document.getElementById('div1')
                    var oUl = oDiv.getElementsByTagName('ul')[0]
                    var aLi = oUl.getElementsByTagName('li')
                    var aA = oDiv.getElementsByTagName('a')
                    var iSpeed = 1//正左负右
                    var timer = null
                    //计算ul的宽为所有li的宽的和;
                    oUl.innerHTML += oUl.innerHTML + oUl.innerHTML
                    oUl.style.width = aLi[0].offsetWidth * aLi.length + 'px'

                    function Slider () {
                        if (oUl.offsetLeft < -oUl.offsetWidth / 2) {
                            oUl.style.left = 0
                        } else if (oUl.offsetLeft > 0) {
                            oUl.style.left = -oUl.offsetWidth / 2 + 'px'
                        }
                        oUl.style.left = oUl.offsetLeft - iSpeed + 'px'//正负为方向
                    }

                    timer = setInterval(Slider, 30)
                    aA[0].onclick = function () {
                        iSpeed = 1 //控制速度的正负
                    }
                    aA[1].onclick = function () {
                        iSpeed = -1
                    }
                    oDiv.onmouseover = function () {
                        clearInterval(timer)
                    }
                    oDiv.onmouseout = function () {
                        timer = setInterval(Slider, 30)
                    }
                }
            </script>
        </div>
    </div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br/>
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340 </div>
<a href="javascript:void(0)" class="wddd">我的订单</a>
<div class="pf pf1"><a href="javascript:void(0)" class="close pfx"></a><img th:src="@{${staticPath}+'static/qyh/20250715/images/pf.png'}" alt="" usemap="#Map" border="0">
    <map name="Map" id="Map"><area shape="rect" coords="-4,349,204,380" href="#a8"/><area shape="rect" coords="-11,309,197,340" href="#a7"/><area shape="rect" coords="-10,272,198,303" href="#a6"/><area shape="rect" coords="-6,234,202,265" href="#a5"/><area shape="rect" coords="-5,195,203,226" href="#a4"/>
        <area shape="rect" coords="-1,80,204,111" href="#a1"/>
        <area shape="rect" coords="3,120,210,149" href="#a2"/>
        <area shape="rect" coords="-1,158,207,189" href="#a3"/>
    </map>
</div>
<div class="pf2">
    <div class="main"><a href="javascript:void(0)" class="btn10 dh" clickkey="20250715Act" clickdata="btn10"></a></div>
</div>
<div class="h" id="ddcx">
    <div class="wddd2 orderlist"><a href="javascript:void(0)" class="close hdgzx"></a>
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tbody id="tbody">
            <tr>
                <td class="bt">订单号</td>
                <td class="bt">订单类型</td>
                <td class="bt">支付时间</td>
                <td class="bt">支付金额</td>
            </tr>
            <!--<tr>
              <td>202306200011</td>
              <td>订金</td>
              <td>2023.6.20</td>
              <td>¥1000</td>
            </tr>-->
            </tbody>
        </table>
    </div>
</div>
<div id="popwin1" style="display:none;">
    <div class="tc0" id="divCheck" style="display:block;">
        <div class="font1"><span id="checkcode">8888</span></div>
        <div class="t1">
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
        </div>
        <div class="clear"></div>
        <div class="btn_tc0"><a href="javascript:void(0)" id="btn_submit"></a></div>
    </div>
</div>
<div id="popwin2" style="display:none;">
    <div class="tc1b" id="divSuc" style="display:block;"> <a href="javascript:void(0)" class="tc3-btn dh"></a> <a href="javascript:void(0)" class="tc4-btn dh"></a> </div>
</div>
<div id="popwin3" style="display:none;">
    <div class="tc1" id="divCheckMobile" style="display:block;">
        <div style="font-size: 30px; color: #000; padding-top: 110px;">请在软件端登录参与活动</div>
    </div>
</div>
<div class="h" style="display: none" id="logindiv">
    <div class="logintc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>

<script>
    var initCount = 2000;
    var countActCode = "20250715a";
    var payCmpCode = "DEPOSIT20250703292528";//支付推送cmpcode
    var uid = $("#hid_uid").val();
    var pid = $("#hid_pid").val();
    var mobileX = $("#hid_mobilex").val();
    var staticPath = $("#hid_staticPath").val();
    var timer = null
    var OrderPaymentItemPayTypeEnum = {
        normal: 0, prePay: 1, finalPaid: 2
    }
    var UserPayStatusIdEnum = {
        noPay: 0, prePaid: 1, finalPaid: 200, reimburse: 201
    }
    /**
     *  "payStatusId":支付状态(0:等待支付,1:支付中,101:支付失败,200:支付成功,201:已退款)
     */
    var PayStatusIdEnum = {
        waiting: 0, paying: 1, fail: 101, success: 200, reimburse: 201
    }
    var acCode = 'ac-1250707102816253'
    //付了尾款
    var isFinalPaid = false
    //付了尾款
    var isPaid = false
    //订单
    var orderList = []
    var timer = null
    var phoneEncrypt = getQueryString('phoneEncrypt')
    var UPcid = getQueryString('UPcid')
    var wz=getQueryString("wz");
    var downflag = '0'

    $(document).ready(function () {
        if($("#hid_isLogin").val() == "1") {
            getPayStatus()
            InitCount();
            bindEvents();
        }else{
            //弹出登录窗口
            $("#logindiv").show();
            $(".logintc").show();
        }

    })
    //点击事件
    function bindEvents() {
        $('.btn1,.btn10').click(function (e) {

            document.querySelector('#k').scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'  // 改为nearest而非start
            });

            //setTimeout(bh, 300);
        })
        $('.pfx').click(function () {
            $('.pf').hide()
            downflag = '1'
        })

        //查询订单列表
        $('.wddd').click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            getPayList(uid)
        })
        //三年
        $('.btn6').click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            if (isFinalPaid) {
                //定金已经付了
                openPopup(function () {
                    $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250715/images/tc4.png')")
                    $('.tc3-btn').hide()
                    $('.tc4-btn').show()
                })

            } else if (isPaid) {
                //尾款已经付了
                openPopup(function () {
                    $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250715/images/tc5.png')")
                    $('.tc3-btn').hide()
                    $('.tc4-btn').hide()
                })
            } else {
                var accode = $(".btn6").attr('data-accode')
                var type = $(".btn6").attr('data-type')
                var addclass = $(".btn6").attr('data-addclass')

                $('#btn_submit').attr('data-accode', accode)
                $('#btn_submit').attr('data-type', type)
                $('#btn_submit').attr('data-addclass', addclass)
                var randnum = rand(1000, 9999)
                $('#checkcode').html(randnum)

                layer.open({
                    type: 1,
                    title: false,
                    //closeBtn: 0,
                    area: ['auto'],
                    //shadeClose: true,
                    skin: 'layui-layer-nobg', //没有背景色
                    content: $('#popwin1').html(),
                    success: function (layero, index) {
                        goNextInput('.layui-layer-content .text1')
                    },
                    end: function () {
                    }
                })
            }
        })
        //一年、2年
        $('.btn4,.btn5').click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            var accode = $(this).attr("data-accode");
            var type = $(this).attr("data-type");

            PushDataToCMP(accode, uid, "", type);
            $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250715/images/tc" + type + ".png')")
            $('.tc3-btn').hide()
            $('.tc4-btn').hide()
        })

        //抢套餐
        $(document).on('click', '#btn_submit', function () {
            var code = ''
            var checkcode = $('#checkcode').html()
            var txt = $('.text1') // 获取所有文本框
            for (var i = 0; i < txt.length; i++) {
                code += txt.eq(i).val()
            }
            if (code == '') {
                layer.msg('请输入您专属的验证码')
                return false
            }
            if (code != checkcode) {
                layer.msg('您的验证码输入有误，请确认后输入')
                return false
            }

            var accode = $(this).attr('data-accode')
            var type = $(this).attr('data-type')

            PushDataToCMP(accode, uid, '', type)

            openPopup(function () {
                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250715/images/tc3.png')")
                $('.tc3-btn').show()
                $('.tc4-btn').hide()
            })
        })

        //去支付
        $(document).on('click', '.tc3-btn,.tc4-btn', function () {
            var payurl = "";
            if (!!wz) {
                payurl = 'https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&resourcestypeid=234&resourcesid=1244376&businessType=biztyp-szds'
            } else {
                payurl = 'https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&resourcestypeid=234&resourcesid=1244376&businessType=biztyp-szds'
            }

            if (!!GetExternal()) {
                PC_JH('EM_FUNC_OPEN_LIVE_VIDEO', '15,' + payurl)
            } else {
                //0x 密文手机 + uid  拼在payurl
                payurl += "&phoneEncrypt=" + mobileX + "&UPcid=" + uid;
                $(this).attr("target", "_blank");
                $(this).attr("href", payurl);
            }

            pushdatatocmp(uid,payCmpCode);
            if (!isFinalPaid && !isPaid) {
                if (timer) clearInterval(timer)

                timer = setInterval(function () {
                    getPayStatus(function () {
                        if (isFinalPaid) {
                            //定金已经付了
                            openPopup(function () {
                                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250715/images/tc4.png')")
                                $('.tc3-btn').hide()
                                $('.tc4-btn').show()
                            })
                            clearInterval(timer)

                        } else if (isPaid) {
                            //尾款已经付了
                            openPopup(function () {

                                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250715/images/tc5.png')")
                                $('.tc3-btn').hide()
                                $('.tc4-btn').hide()
                            })
                            clearInterval(timer)
                            if (!timer) {
                                closePopup()
                            }
                        }
                    })

                }, 9000)
            }
            if (isFinalPaid) {
                layer.closeAll()
            }
        })

        $('.bg a').click(function () {
            $(this).addClass('current').siblings('a').removeClass('current');
        })
        $('.nav li').click(function () {
            $(this).addClass('current').siblings('li').removeClass('current');
        });

        $('.sppfbtn2').click(function () {
            $('.bg9').animate({
                height: '80px'
            })
            $('.sppfbtn2').hide(600)
            $('.sp').attr('src','about:blank')
        })
        $('.sppfbtn').click(function () {
            $('.bg9').animate({
                height: '500px'
            })
            $('.sppfbtn2').show(600)
            $('.sp').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=596715cbd25543baa9fd8de30a3727c7&rep=1&py=1')
        })

        $(window).scroll(function (e) {
            if (($(window).scrollTop() >= 640) && (downflag == '0')) {
                $('.pf').fadeIn(300)
            }
            if ($(window).scrollTop() >= 640) {
                $('.pf2').fadeIn(300)
            }else {
                $('.pf,.pf2').fadeOut(300)
            }
        })
        $('.hdgzk').click(function () {$('#hdgz').show()})
        $('.hdgzx').click(function () {$('.h').hide()})
    }

    //获取定金尾款订单列表
    function getPayList () {
        $('#tbody tr').eq(0).siblings().remove()
        if (orderList.length) {
            var list = orderList

            function computedOrderTypeName (orderPayments) {
                let result = ''
                if (orderPayments && orderPayments.length) {
                    orderPayments.forEach(i => {
                        if (i.payStatusId === PayStatusIdEnum.success && i.payType === OrderPaymentItemPayTypeEnum.prePay) {
                        result += '订金'
                    }
                    if (i.payStatusId === PayStatusIdEnum.success && i.payType === OrderPaymentItemPayTypeEnum.finalPaid) {
                        result += '+尾款'
                    }
                })
                }
                return result
            }

            if (!!list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    var item = list[i]

                    var trhtml = document.createElement('tr')
                    trhtml.id = 'mDiv'
                    trhtml.innerHTML = '<td>' + item.orderNumber + '</td><td>' + computedOrderTypeName(item.orderPayments) +
                        '</td><td>' +
                        timestampToTime(item.createTime) + '</td><td>￥' + item.payTotal + '</td>'

                    $('#tbody').append(trhtml)
                }
            } else {
                var trhtml = document.createElement('tr')
                trhtml.id = 'mDiv'
                trhtml.innerHTML = '<td colspan=\'4\'>您好，当前未查询到任何有效订单</td>'

                $('#tbody').append(trhtml)
            }

            $('#ddcx').show()
        } else {
            var trhtml = document.createElement('tr')
            trhtml.id = 'mDiv'
            trhtml.innerHTML = '<td colspan=\'4\'>您好，当前未查询到任何有效订单</td>'

            $('#tbody').append(trhtml)
        }

        $('#ddcx').show()
    }

    //获取支付状态
    function getPayStatus (cb) {
        var mobileX = $("#hid_mobilex").val();
        $.ajax({
            url: "https://emapp.emoney.cn/buy/order/QueryOrderList?Emapp-Format=EmappJsonp",
            timeout: 5000,
            type: 'get',
            dataType: 'jsonp',
            cache: false,
            data: {"phoneEncrypt": mobileX, "type": 0},
            success: function (data) {
                if (data.result.code === 0) {
                    var arr = data.detail
                    var status = ''
                    orderList = []
                    arr.forEach(item => {
                        if (item && item.orderLogistice && item.orderLogistice.length) {
                        item.orderLogistice.forEach(i => {
                            if (i.activityCode === acCode &&
                                [UserPayStatusIdEnum.finalPaid, UserPayStatusIdEnum.prePaid].includes(item.payStatusId)) {
                            //定金已付
                            isFinalPaid = item.payStatusId === UserPayStatusIdEnum.prePaid
                            //尾款已付
                            isPaid = item.payStatusId === UserPayStatusIdEnum.finalPaid

                            orderList.push(item)
                        }
                    })
                    }
                })

                    cb && cb()
                }
            }
        })
    }

    //初始化申请人数
    function InitCount () {
        $.ajax({
            type: 'get',
            url: 'https://act.emoney.cn/activity/user/getcountbyactcode?actcode='+countActCode,
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if (data.code == '200') {
                    var num = 0
                    if (!!data.data) {
                        num = data.data.split(',')[0];
                        if(num == ''){
                            num = 0;
                        }
                    }
                    $('.djs').html(addPreZero(initCount - parseInt(num)))//剩余席位
                    $('[name=usedCount]').html(num>initCount?initCount:num)//已申请席位

                    setTimeout('InitCount()', 60000 * 5)
                }
            }
        })
    }

    function PushDataToCMP (accode, uid, uname, type) {
        var data = {
            'appid': '10088',
            'logtype': 'click',
            'mid': '',
            'pid': getQueryString('pid'),
            'sid': getQueryString('sid'),
            'tid': getQueryString('tid'),
            'uid': uid,
            'uname': uname,
            'adcode': accode,
            'targeturl': '',
            'pageurl': window.top.location.href
        }
        var saasUrl = 'http://ds.emoney.cn/saas/queuepush'
        var saasSrc = saasUrl + '?v=' + Math.random()
            + '&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID'
            + '&message=' + encodeURIComponent(JSON.stringify(data))

        var elm = document.createElement('img')
        elm.src = saasSrc
        elm.style.display = 'none'
        document.body.appendChild(elm)

        $.ajax({
            type: 'get',
            url: 'https://act.emoney.cn/activity/user/addcountbyactcode?actcode='+countActCode,
            dataType: 'jsonp',
            data: {
                uid: uid ? uid : uname,
                value: '1'
            },
            success: function (data) {
                if (data.code == '200') {
                    layer.closeAll()
                    layer.open({
                        type: 1,
                        title: false,
                        area: ['auto'],
                        skin: 'layui-layer-nobg', //没有背景色
                        content: $('#popwin2').html()
                    })
                }
            }
        })

    }
</script>
<script type="text/javascript">
    //打开弹窗
    function openPopup (cb) {
        layer.close(layer.index)
        layer.open({
            type: 1,
            title: false,
            //closeBtn: 0,
            area: ['auto'],
            //shadeClose: true,
            skin: 'layui-layer-nobg', //没有背景色
            content: $('#popwin2').html(),
            success: function (layero, index) {
                cb()
            },
            end: function () { }
        })
    }

    // 处理时间戳
    /* 时间戳转换为时间 */
    function timestampToTime (timestamp) {
        timestamp = timestamp ? timestamp : null
        var date = new Date(timestamp)//时间戳为10位需*1000，时间戳为13位的话不需乘1000
        var Y = date.getFullYear() + '-'
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
        var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
        var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
        var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
        var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
        return Y + M + D + h + m + s
    }

    // input框输入1位数字后自动跳到下一个input聚焦
    function goNextInput (el) {
        var txts = document.querySelectorAll(el)
        for (var i = 0; i < txts.length; i++) {
            var t = txts[i]
            t.index = i
            t.setAttribute('readonly', true)
            t.onkeyup = function () {
                this.value = this.value.replace(/^(.).*$/, '$1')
                var next = this.index + 1
                if (next > txts.length - 1) return
                txts[next].removeAttribute('readonly')
                if (this.value) {
                    txts[next].focus()
                }
            }
        }
        setTimeout(function () {txts[0].focus() }, 100)
        txts[0].removeAttribute('readonly')
    }

    function rand (min, max) {
        return Math.floor(Math.random() * (max - min)) + min
    }

    function getQueryString (name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
        var r = window.location.search.substr(1).match(reg)
        if (r != null) return unescape(r[2])
        return null
    }

    function setCookie (name, value) {
        var expdate = new Date()
        expdate.setTime(expdate.getTime() + 30 * 60 * 1000)
        document.cookie = name + '=' + value + ';expires=' + expdate.toGMTString() + ';path=/'
    }

    function getCookie (c_name) {
        if (document.cookie.length > 0) {
            c_start = document.cookie.indexOf(c_name + '=')
            if (c_start != -1) {
                c_start = c_start + c_name.length + 1
                c_end = document.cookie.indexOf(';', c_start)
                if (c_end == -1) c_end = document.cookie.length
                return unescape(document.cookie.substring(c_start, c_end))
            }
        }
        return ''
    }

    function addPreZero (num) {
        if (num < 0) {
            return '0000'
        } else if (num < 10) {
            return '000' + num
        } else if (num < 100) {
            return '00' + num
        } else if (num < 1000) {
            return '0' + num
        } else {
            return num
        }
    }

    function checkPermission (pid) {
        // if (pid != "888010000" && pid != "888010400") {
        //     layer.msg("本活动仅限小智盈用户参与");
        //     return false;
        // }
        return true;
    }
</script>
<script type="text/javascript">
    document.querySelectorAll('.iframe-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const newSrcWithParams = this.href;
            // 优先通过DOM查询获取iframe，兼容性更好
            const targetIframe = document.querySelector(`iframe[name="${this.target}"]`);

            if (targetIframe) { // 直接判断DOM元素是否存在
                targetIframe.src = newSrcWithParams;

                // 移除未定义函数的调用，或补充定义
                // if (typeof initIframes === 'function') {
                //   initIframes();
                // }
            } else {
                console.error(`未找到name为"${this.target}"的iframe`);
            }
        });
    });

    // 声明变量存储iframe列表
    var videoList = [];

    // 初始化函数：获取iframe并筛选视频iframe
    function initIframes() {
        // 获取所有iframe元素
        var allIframes = $('iframe');

        // 筛选出视频iframe（根据实际情况调整筛选条件）
        videoList = allIframes.filter(function() {
            return this.src.includes('dsclient')
        });
    }

    // 页面加载完成后初始化一次
    $(document).ready(function() {
        initIframes();
    });

    // 给所有a标签绑定点击事件，点击后重新获取iframe
    $(document).on('click', 'a', function() {
        // 延迟一点时间再获取，确保新内容已加载（如果是异步加载内容）
        setTimeout(initIframes, 300);
    });

    // 滚动事件处理（保持原有逻辑）
    document.addEventListener('scroll', function() {
        var isPlay = false;
        var visibleBottom = window.scrollY + document.documentElement.clientHeight;
        var visibleTop = window.scrollY;

        // 只处理视频iframe
        for (var i = 0; i < videoList.length; i++) {
            var centerY = $(videoList[i]).offset().top + (videoList[i].offsetHeight / 2);

            if (centerY > visibleTop && centerY < visibleBottom) {
                if (!isPlay) {
                    if (videoList[i].src.match(/py=0/)) {
                        videoList[i].src = videoList[i].src.replace(/py=0/, 'py=1');
                        isPlay = true;
                    }
                } else {
                    if (videoList[i].src.match(/py=1/)) {
                        videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0');
                    }
                }
            } else {
                if (videoList[i].src.match(/py=1/)) {
                    videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0');
                }
            }
        }
    });
</script>
<script type="text/javascript">document.write(unescape(
    '%3Cscript src=\'https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F\' type=\'text/javascript\'%3E%3C/script%3E'))</script>
</body>
</html>
