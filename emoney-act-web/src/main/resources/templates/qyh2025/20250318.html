<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,操盘手,抽奖,赢大奖">
    <meta name="Description" content="挑战5178 新牛市 新高点 挑战半年内上证指数攻上5178点，不上不计费">
    <link th:href="@{${staticPath}+'static/qyh/20250318/css/css.css?r=20250318'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script th:src="@{${staticPath}+'static/js/awardRotate.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="dbg1"></div>
<div class="dbg2"></div>
<div class="dbg3"><div class="main"><a href="" class="btn1 dh"></a></div></div>
<div class="dbg4"><div class="main"><div class="n1">
    <ul>
        <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=397ad24e0e724ccb8cd879ffa7b60a37&rep=1&py=0" target="sp" class="current">往期学员感言</a>
        <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=28f4818af11844b28cc4b59713e9c4cd&rep=1&py=0" target="sp">波段产品概要</a>
    </ul>
</div>
    <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=397ad24e0e724ccb8cd879ffa7b60a37&rep=1&py=0" scrolling="no" class="sp" name="sp"></iframe>
</div></div>
<div class="dbg5"></div>
<div class="dbg6"></div>
<div class="dbg7"></div>
<div class="dbg8"><div class="main"><!-- 代码 开始 -->
    <ul class="silder_nav">
        <li class=""></li>
        <li class=""></li>
    </ul>
    <div class="slider_name slider_box pic1">
        <ul class="silder_con">
            <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qyh/20250318/images/a1.png'}" alt=""></li>
            <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qyh/20250318/images/a2.png'}" alt=""></li>
        </ul>
    </div>
    <ul class="silder_nav2" style="top: 1460px">
        <li class=""></li>
        <li class=""></li>
    </ul>
    <div class="slider_name2 slider_box2 pic2">
        <ul class="silder_con2">
            <li class="silder_panel2 clearfix2"><img th:src="@{${staticPath}+'static/qyh/20250318/images/b1.png'}" src="images/b1.png" alt=""></li>
            <li class="silder_panel2 clearfix2"><img th:src="@{${staticPath}+'static/qyh/20250318/images/b2.png'}" alt=""></li>
        </ul>
    </div>
    <!-- 代码 结束 -->
</div></div>
<div class="dbg9"></div>
<div class="dbg10">
    <div class="main">
        <div class="zp"></div>
        <div class="zp0"></div>
        <div class="bg1"></div>
        <a href="###" class="zz" id="rotate"></a><div class="go" id="btn-lottery"><img th:src="@{${staticPath}+'static/qyh/20250318/images/go.png'}"/></div>
        <div class="md">
            <marquee onmouseover="this.stop()" onmouseout="this.start()" scrollamount="2" direction="up" height="150px">
                <ul id="user_list">
<!--                    <li>恭喜 em19**53 用户 <span>获五星使用期30天</span></li>-->
<!--                    <li>恭喜 em64**59 用户 <span>获得500积分</span></li>-->
                </ul>
            </marquee>
            <ul style="margin-top: 50px;" id="my_list">
                <!--            <li>恭喜 em****118 用户喜获 <span>智盈大师 3个月使用期</span></li>-->
                <!--            <li>恭喜 em****118 用户喜获 <span>智盈大师 1个月使用期</span></li>-->
                <!--            <li>恭喜 em****118 用户喜获 <span>智盈大师 15天使用期</span></li>-->
            </ul>
        </div>
        <div class="hdgz">抽奖规则
            <ul>
                <li>3月1日至4月3日新购波段用户，可获得抽福袋机会；</li>
                <li>无论单双策略均可抽奖，100%中奖；</li>
                <li>抽奖礼品将在活动结束后的10个工作日内发送；</li>
                <li>抽奖开放时间:3月18日-4月3日，限时活动，错过不再有；</li>
                <li>若发生退货行为，奖品一律退还；</li>
                <li>活动最终解释权归益盟股份有限公司所有。</li>
            </ul>
        </div></div></div>
<div class="footer">欢迎登录益盟官方网站www.emoney.cn<br />
    本活动最终解释权归上海益盟软件技术股份有限公司　沪ICP备06000340<br />
    软件需数据支持，仅提供辅助建议，风险需谨慎；投资顾问意见仅作参考。</div>
<a href="###" class="pf2"></a>
<div class="pf">
    <div class="main"><a href="" class="btn2 dh2"></a></div></div>
<div class="bg" style="display: none">
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
    <div class="tc" style="display: none">
        <a href="###" class="close"></a>
        <div class="tc_1">恭喜您获得<br />
            波段使用期<span class="red">1个月</span></div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/qyh/20250318/js/main.js?r=20250318'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "qyh_20250318";//模块名称(焦点图2)
    var Remark = "波段抢优惠";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>

<script type="text/javascript" th:src="@{${staticPath}+'static/qyh/20250312/js/jquery.slides.js'}"></script>
</body>
</html>
