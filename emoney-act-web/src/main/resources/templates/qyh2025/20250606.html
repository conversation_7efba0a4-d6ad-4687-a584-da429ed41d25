<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="学大师 用大师 高效擒龙 益盟操盘手大师·掘金版--精选股 好体系" />
    <link th:href="@{${staticPath}+'static/qyh/20250606/style/common.css?r=20250606'}" rel="stylesheet" type="text/css" />
    <link th:href="@{${staticPath}+'static/qyh/20250606/style/popup.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qyh/20231205/js/json2.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qgn/20231011/js/popup.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var www = "../";
        function GetExternal () {
            return window.external.EmObj
        }

        function PC_JH (type, c) {
            try {
                var obj =
                    GetExternal()
                return obj.EmFunc(type, c)
            } catch (e) {}
        }

        (function () {

            function LoadComplete () {
                try {
                    PC_JH('EM_FUNC_DOWNLOAD_COMPLETE', '')
                } catch (ex) {}
            }

            function EM_FUNC_HIDE () {
                try {
                    PC_JH('EM_FUNC_HIDE', '')
                } catch (ex) {}
            }

            function EM_FUNC_SHOW () {
                try {
                    PC_JH('EM_FUNC_SHOW', '')
                } catch (ex) {}
            }

            function IsShow () {
                try {
                    return PC_JH('EM_FUNC_WND_ISSHOW', '')
                } catch (ex) {
                    return '0'
                }
            }

            function openWindow () {
                LoadComplete()

                PC_JH('EM_FUNC_WND_SIZE', 'w=1300,h=820,mid')
                PC_JH('EM_FUNC_SET_STYLE', '/caption=')
                PC_JH('EM_FUNC_SET_TITLE', '')
                if (IsShow() != '1') {
                    EM_FUNC_SHOW()
                }
            }

            openWindow()
        })()

    </script>
</head>
<body>
<div class="bg9">
    <div class="sppf">
        <div class="main"><a href="javascript:void(0)" class="sppfbtn"></a></div>
    </div>
    <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="sp"></iframe>
    <a href="javascript:void(0)" class="sppfbtn2"></a></div>
<div class="bod">
    <div class="dbg1"></div>
    <div class="dbg2">
    </div>
    <div class="dbg3">
        <div class="main"><div class="djs2"><span class="t">00</span><span class="s">00</span><span class="f">00</span><span class="m">00</span></div><a href="javascript:void(0)" class="btn1 dh" clickkey="20250415Act" clickdata="btn1"></a>
        </div>
    </div>
    <div class="dbg4">
        <div class="main"><div id="a1" style="position: absolute; top: 0px"></div>
            <a
                    href="javascript:void(0)" class="btn6 dh popupD" flag="openpop" data-accode="AC588Pop2025041503"
                    data-addclass="btn6h" data-type="3" clickkey="20250415Act" clickdata="buy3"></a>
            <div id="k"></div>
            <script type="text/javascript">
                function bh () {
                    var i = document.getElementById('k')
                    i.style.display = 'block'
                    setTimeout('clock()', 1300)
                }

                function clock () {
                    var i = document.getElementById('k')
                    i.style.display = 'none'
                }

            </script></div>
    </div>
    <div class="bg bgc" id="ala1"><div id="a1b" style="position: absolute; top: -210px"></div>
        <ul id="yd2b">
            <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=7267283d9ed94d2eb518772f387ba4cc&rep=1&py=1" target="al2">
                <div class="f1">【伏击活跃】2025.2.5出击</div>
                <div class="f3">北京君正 9个交易日 涨<span class="f2">26.09%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=6725e4a473604ecead58e1e3811f1989&rep=1&py=1" target="al2">
                <div class="f1">【资金金叉】2025.3.12出击</div>
                <div class="f3">宁波东力 8个交易日 涨<span class="f2">62.62%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=e7815ac17eca4a18927a88697d27df19&rep=1&py=1" target="al2">
                <div class="f1">【量王叠现】2025.3.5出击</div>
                <div class="f3">方正电机 13个交易日 涨<span class="f2">70.93%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=3ac5546449944a6e8185249fe5a777d9&rep=1&py=1" target="al2">
                <div class="f1">【大阳起势】2025.2.6出击</div>
                <div class="f3">万朗磁塑 15个交易日 涨<span class="f2">47.81%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=525bce36a4e64b74b828ea8dbab31b1b&rep=1&py=1" target="al2">
                <div class="f1">【纵横趋势-突破K】2025.3.4出击</div>
                <div class="f3">东土科技 14个交易日 涨<span class="f2">53.98%</span></div>
            </a>
        </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=7267283d9ed94d2eb518772f387ba4cc&rep=1&py=0" scrolling="no" class="swf1" name="al2"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250415Act" clickdata="btn10"></a> </div>
    </div>
    <div class="bg bgd" id="ala2" style="display: none">
        <ul id="yd3b">
            <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=847d69e8b5804d9aacbfb290188d5ba9&rep=1&py=1" target="al3">
                <div class="f1">【锅底右侧】2025.2.26出击</div>
                <div class="f3">大名城 3个交易日 涨<span class="f2">17.14%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=f6f62c8c95fa4dfc9e16f89b16ce137f&rep=1&py=1" target="al3">
                <div class="f1">【蹦极新生】2025.2.17出击</div>
                <div class="f3">汉威科技 9个交易日 涨<span class="f2">77.72%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=b91830e5626147e3b4db03b18e231314&rep=1&py=1" target="al3">
                <div class="f1">【黄金回踩】2025.2.27出击</div>
                <div class="f3">绿能慧充 14个交易日 涨<span class="f2">27.72%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=338d315093a74022ab9cf7448fd011a5&rep=1&py=1" target="al3">
                <div class="f1">【龙腾长波】2025.2.6出击</div>
                <div class="f3">神州数码 13个交易日 涨<span class="f2">65.66%</span></div>
            </a>
        </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=847d69e8b5804d9aacbfb290188d5ba9&rep=1&py=0" scrolling="no" class="swf1" name="al3"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250415Act" clickdata="btn10"></a> </div>
    </div>

    <div class="bg" id="ala3" style="display: none">
        <ul id="yd1b">
            <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=136773abe3b1403685abb9446499413c&rep=1&py=1" target="al1">
                <div class="f1">【黄金坑】2024.2.8出击</div>
                <div class="f3">科德教育 29个交易日 涨<span class="f2">74.03%</span></div>
            </a><a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=85e513552d124978be3f3b61af36cc4e&rep=1&py=1" target="al1">
            <div class="f1">【冰谷+潜龙】2024.9.24出击</div>
            <div class="f3">兴齐眼药 6个交易日 涨<span class="f2">88.59%</span></div>
        </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=00c9356459c249d789a1b2ec0322a166&rep=1&py=1" target="al1">
                <div class="f1">【价格冲击波】2024.9.18出击</div>
                <div class="f3">彩讯股份 10个交易日 涨<span class="f2">72.82%</span></div>
            </a>
            <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=486c13c94dae465cb51f8adb0d3dd62c&rep=1&py=1" target="al1">
                <div class="f1">【纵横趋势-底部K】2025.2.20出击</div>
                <div class="f3">航天电器 16个交易日 涨<span class="f2">35.06%</span></div>
            </a>
        </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=136773abe3b1403685abb9446499413c&rep=1&py=0" scrolling="no" class="swf1"
                name="al1"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250415Act" clickdata="btn10"></a> </div>
    </div>

    <div class="bg bge" id="ala4" style="display: none">
        <ul>
        </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=f44c748d0b82451b8ae8a7afd9427b53&rep=1&py=0" scrolling="no" class="swf1" name="al4"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250415Act" clickdata="btn10"></a> </div>
    </div>
    <div class="nav">
        <ul>
            <li class="current video-btn" id="b1"></li>
            <li class="video-btn" id="b2"></li>
            <li class="video-btn" id="b3"></li>
            <li class="video-btn" id="b4"></li>
        </ul></div>
    <div class="bg10"></div>
    <div class="bg bgb">
        <ul></ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=31337006988f4bbdbab26eb2f9519c43&rep=1&py=0" scrolling="no" class="swf1" name="al4b"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250415Act" clickdata="btn10"></a> </div>
    </div>
    <div class="nav">
        <ul>
            <li class="current" id="s1" th:data-bg-image="@{${staticPath}+'static/qyh/20250606/images/bgb.png'}" data-src-url="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=31337006988f4bbdbab26eb2f9519c43&rep=1&py=1"></li>
            <li class="" id="s2" th:data-bg-image="@{${staticPath}+'static/qyh/20250606/images/bgh.png'}" data-src-url="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=c7d844799eb343e6a52e320559ede175&rep=1&py=1"></li>
            <li class="" id="s3" th:data-bg-image="@{${staticPath}+'static/qyh/20250606/images/bgi.png'}" data-src-url="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=86302c82817c4b2e9a120f9018236603&rep=1&py=1"></li>
            <li class="" id="s4" th:data-bg-image="@{${staticPath}+'static/qyh/20250606/images/bgj.png'}" data-src-url="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=c4c136fecf0e42d9afef1bda8b685a7a&rep=1&py=1"></li>
        </ul></div>
    <div class="bg11"></div>
    <div class="bg bgf"><div class="ico2"></div><ul></ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=e1309bccf4c94dfaa271be4b0aae2d47&rep=1&py=0" scrolling="no" class="swf1" name="al5"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250415Act" clickdata="btn10"></a> </div>
    </div>
    <div class="nav" style="width: 555px;">
        <ul>
            <li class="current" id="c1" th:data-bg-image="@{${staticPath}+'static/qyh/20250606/images/bgf.png'}" data-src-url="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=e1309bccf4c94dfaa271be4b0aae2d47&rep=1&py=1"></li>
            <li class="" id="c2" th:data-bg-image="@{${staticPath}+'static/qyh/20250606/images/bgf2.png'}" data-src-url="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a0b210032d9f4279a8a2edd674267c59&rep=1&py=1"></li>
            <li class="" id="c3" th:data-bg-image="@{${staticPath}+'static/qyh/20250606/images/bgf3.png'}" data-src-url="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=96bb7b1db04a4255b06dfcf8f2994192&rep=1&py=1"></li>
            <li class="" id="c4" th:data-bg-image="@{${staticPath}+'static/qyh/20250606/images/bgf4.png'}" data-src-url="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=229104ddc3204678ad12d50a9475bd2f&rep=1&py=1"></li>
            <li class="" id="c5" th:data-bg-image="@{${staticPath}+'static/qyh/20250606/images/bgf5.png'}" data-src-url="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=b1ff874bb17a41a18f4db0950ee94992&rep=1&py=1"></li>
            <li class="" id="c6" th:data-bg-image="@{${staticPath}+'static/qyh/20250606/images/bgf6.png'}" data-src-url="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=80441a2b1d894692be173dac495229b9&rep=1&py=1"></li>
        </ul></div>
    <div class="bg12"></div>
    <div class="bg bgg">
        <ul>
        </ul>
        <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=bd3f1a90b8f04c638fab0ee8eda22939&rep=1&py=0" scrolling="no" class="swf1" name="al6"></iframe>
        <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20250415Act" clickdata="btn10"></a> </div>
    </div>
    <div class="bg13" id="a1c"></div>

    <div class="dbg8"><div class="main"><!-- 代码 开始 -->
        <ul class="silder_nav">
            <li class=""></li>
            <li class=""></li>
            <li class=""></li>
        </ul>
        <div class="slider_name slider_box pic1">
            <ul class="silder_con">
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qyh/20250606/images/a1.png'}" alt="" /></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qyh/20250606/images/a2.png'}" alt="" /></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qyh/20250606/images/a3.png'}" alt="" /></li>
            </ul>
        </div>
        <script type="text/javascript" th:src="@{${staticPath}+'static/qyh/20250606/js/jquery.slides.js'}"></script>
        <!-- 代码 结束 -->
    </div></div>
    <div class="dbg5" id="a1d"></div>
    <div class="dbg6"></div>
    <div class="dbg7">
        <div class="main">
            <div id="div1">
                <ul>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250606/images/zs10.jpg'}" /></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250606/images/zs1.png'}"/></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250606/images/zs2.png'}"/></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250606/images/zs3.png'}"/></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250606/images/zs4.png'}"/></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250606/images/zs5.png'}"/></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250606/images/zs6.png'}"/></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250606/images/zs7.png'}"/></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250606/images/zs8.png'}"/></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20250606/images/zs9.png'}"/></li>
                </ul>
                <a href="javascript:;" style="left:10px;"></a> <a href="javascript:;" style="right:10px;"></a></div>
            <script type="text/javascript">
                window.onload = function () {
                    var oDiv = document.getElementById('div1')
                    var oUl = oDiv.getElementsByTagName('ul')[0]
                    var aLi = oUl.getElementsByTagName('li')
                    var aA = oDiv.getElementsByTagName('a')
                    var iSpeed = 1//正左负右
                    var timer = null
                    //计算ul的宽为所有li的宽的和;
                    oUl.innerHTML += oUl.innerHTML + oUl.innerHTML
                    oUl.style.width = aLi[0].offsetWidth * aLi.length + 'px'

                    function Slider () {
                        if (oUl.offsetLeft < -oUl.offsetWidth / 2) {
                            oUl.style.left = 0
                        } else if (oUl.offsetLeft > 0) {
                            oUl.style.left = -oUl.offsetWidth / 2 + 'px'
                        }
                        oUl.style.left = oUl.offsetLeft - iSpeed + 'px'//正负为方向
                    }

                    timer = setInterval(Slider, 30)
                    aA[0].onclick = function () {
                        iSpeed = 1 //控制速度的正负
                    }
                    aA[1].onclick = function () {
                        iSpeed = -1
                    }
                    oDiv.onmouseover = function () {
                        clearInterval(timer)
                    }
                    oDiv.onmouseout = function () {
                        timer = setInterval(Slider, 30)
                    }
                }
            </script>
        </div>
    </div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br/>
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340 </div>
<a href="javascript:void(0)" class="wddd">我的订单</a>
<div class="pf pf1"><a href="javascript:void(0)" class="close pfx"></a><img th:src="@{${staticPath}+'static/qyh/20250606/images/pf.png'}" alt="" usemap="#Map" border="0">
    <map name="Map" id="Map"><area shape="rect" coords="-5,205,203,236" href="#a1d" class="s3"/>
        <area shape="rect" coords="-1,91,204,122" href="#a1" class="s1"/>
        <area shape="rect" coords="2,130,209,159" href="#a1b" class="s2"/>
        <area shape="rect" coords="-1,168,207,199" href="#a1c" class="s3"/>
    </map>
</div>
<div class="pf2">
    <div class="main"><a href="javascript:void(0)" class="btn10 dh" clickkey="20250606Act" clickdata="btn10"></a></div>
</div>
<a href="javascript:void(0)" class="wddd">我的订单</a>
<div class="h" id="hdgz">
    <div class="hdgz"><a href="javascript:void(0)" class="close hdgzx"></a> <strong>开户返金豆活动规则：</strong>
        <ul>
            <li>本活动仅限2024年9月10日至2024年9月30日新升级益盟操盘手大师的用户参与，每位用户仅可参与一次开户活动，历史已经参与过的不可再重复参加活动；</li>
            <li>本活动需通过益盟专属二维码或链接进行开户，<strong class="red2">开户手机号需与订单手机号一致</strong>，开户营业部需为湘财证券国权北路营业部，且需为该券商新用户；</li>
            <li>开户成功：沪深双市均开立成功并绑定银行卡；资产达标：开户成功且连续20个交易日日均资产大于等于5万；</li>
            <li>2024年9月10日至2024年9月30日内资产达标可获得5万金豆，金豆会在资产达标后15个自然日内到账；</li>
            <li>本活动获得的金豆，可一次性兑换500元软件费补贴或1000元产品优惠券。可兑换时间为获得金豆之日起365个自然日内，一经兑换不可修改。详细规则可在获得金豆后进入益盟软件-个人中心-我的福利查看；</li>
            <li>若您在兑换金豆前退货，则取消活动资格；</li>
            <li>参加活动开户的账户仅为普通沪深账户，如需办理其他证券相关业务或咨询开户相关问题，请拨打湘财证券开户热线：021-60331188转2；</li>
            <li>如遇活动相关问题可咨询您的益盟服务专员或拨打10108688；</li>
            <li>风险提示：投资有风险，入市需谨慎；</li>
        </ul>
        <strong>*本活动最终解释权归益盟股份有限公司所有。</strong></div>
</div>
<div class="h" id="ddcx">
    <div class="wddd2 orderlist"><a href="javascript:void(0)" class="close hdgzx"></a>
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tbody id="tbody">
            <tr>
                <td class="bt">订单号</td>
                <td class="bt">订单类型</td>
                <td class="bt">支付时间</td>
                <td class="bt">支付金额</td>
            </tr>
            <!--<tr>
              <td>202306200011</td>
              <td>订金</td>
              <td>2023.6.20</td>
              <td>¥1000</td>
            </tr>-->
            </tbody>
        </table>
    </div>
</div>
<div id="popwin1" style="display:none;">
    <div class="tc0" id="divCheck" style="display:block;">
        <div class="font1"><span id="checkcode">8888</span></div>
        <div class="t1">
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
        </div>
        <div class="clear"></div>
        <div class="btn_tc0"><a href="javascript:void(0)" id="btn_submit"></a></div>
    </div>
</div>
<div id="popwin2" style="display:none;">
    <div class="tc1b" id="divSuc" style="display:block;"> <a href="javascript:void(0)" class="tc3-btn dh"></a> <a href="javascript:void(0)" class="tc4-btn dh"></a> </div>
</div>
<div id="popwin3" style="display:none;">
    <div class="tc1" id="divCheckMobile" style="display:block;">
        <div style="font-size: 30px; color: #000; padding-top: 110px;">请在软件端登录参与活动</div>
        <!--<div class="font3">智盈大师（掘金版）<br/>
                <span id="showtype"></span>年版套餐预定
            </div>
            <div class="t2">
                <input type="text" id="txt_mobile" class="srk" placeholder="请输入您的订购手机号"/>
                <a id="btnsend" href="javascript:void(0)" class="yzm">获取验证码</a>
                <div class="clear"></div>
            </div>
            <div class="t2">
                <input type="text" id="txt_code" class="srk" placeholder="请输入验证码"/>
                <a href="javascript:void(0)" class="btn_tc2">点击确定</a>
                <div class="clear"></div>
            </div>-->
    </div>
</div>
<div class="h" style="display: none" id="logindiv">
    <div class="logintc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>

<script>
    var payCmpCode = "PREFERENTIAL20250528282486";//支付推送cmpcode
    var uid = $("#hid_uid").val();
    var pid = $("#hid_pid").val();
    var mobileX = $("#hid_mobilex").val();
    var staticPath = $("#hid_staticPath").val();
    var timer = null
    var OrderPaymentItemPayTypeEnum = {
        normal: 0, prePay: 1, finalPaid: 2
    }
    var UserPayStatusIdEnum = {
        noPay: 0, prePaid: 1, finalPaid: 200, reimburse: 201
    }
    /**
     *  "payStatusId":支付状态(0:等待支付,1:支付中,101:支付失败,200:支付成功,201:已退款)
     */
    var PayStatusIdEnum = {
        waiting: 0, paying: 1, fail: 101, success: 200, reimburse: 201
    }
    //上一期抢优惠
    var acCode_pre = 'ac-125051914223255'
    var acCode = 'ac-1250528173327439'
    //付了尾款
    var isFinalPaid = false
    //付了尾款
    var isPaid = false
    //订单
    var orderList = []
    var timer = null
    var phoneEncrypt = getQueryString('phoneEncrypt')
    var UPcid = getQueryString('UPcid')
    var wz=getQueryString("wz");
    var downflag = '0'

    $(document).ready(function () {
        if($("#hid_isLogin").val() == "1") {
            getPayStatus()
            bindEvents();
        }else{
            //弹出登录窗口
            $("#logindiv").show();
            $(".logintc").show();
        }

    })
    //点击事件
    function bindEvents() {

        $('.pfx').click(function () {
            $('.pf').hide()
            downflag = '1'
        })

        //查询订单列表
        $('.wddd').click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            getPayList(uid)
        })
        //抢团购
        $('.btn1,.btn2,.btn10,.btn6').click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            if (isFinalPaid) {
                //定金已经付了
                openPopup(function () {
                    $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250606/images/tc4.png')")
                    $('.tc3-btn').hide()
                    $('.tc4-btn').show()
                })

            } else if (isPaid) {
                //尾款已经付了
                openPopup(function () {
                    $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250606/images/tc5.png')")
                    $('.tc3-btn').hide()
                    $('.tc4-btn').hide()
                })
            } else {
                PushDataToCMP(payCmpCode, uid, "");
                openPopup(function () {
                    $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250606/images/tc3.png')")
                    $('.tc3-btn').hide()
                    $('.tc4-btn').show()
                })
            }
        })

        //抢套餐
        $(document).on('click', '#btn_submit', function () {
            var code = ''
            var checkcode = $('#checkcode').html()
            var txt = $('.text1') // 获取所有文本框
            for (var i = 0; i < txt.length; i++) {
                code += txt.eq(i).val()
            }
            if (code == '') {
                layer.msg('请输入您专属的验证码')
                return false
            }
            if (code != checkcode) {
                layer.msg('您的验证码输入有误，请确认后输入')
                return false
            }

            var accode = $(this).attr('data-accode')
            var type = $(this).attr('data-type')

            PushDataToCMP(accode, uid, '', type)

            openPopup(function () {
                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250606/images/tc3.png')")
                $('.tc3-btn').show()
                $('.tc4-btn').hide()
            })
        })

        //去支付
        $(document).on('click', '.tc3-btn,.tc4-btn', function () {
            var payurl = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&resourcestypeid=234&resourcesid=1244376&businessType=biztyp-szds";

            if (!!GetExternal()) {
                PC_JH('EM_FUNC_OPEN_LIVE_VIDEO', '15,' + payurl)
            } else {
                //0x 密文手机 + uid  拼在payurl
                payurl += "&phoneEncrypt=" + mobileX + "&UPcid=" + uid;
                $(this).attr("target", "_blank");
                $(this).attr("href", payurl);
            }

            pushdatatocmp(uid,payCmpCode);
            if (!isFinalPaid && !isPaid) {
                if (timer) clearInterval(timer)

                timer = setInterval(function () {
                    getPayStatus(function () {
                        if (isFinalPaid) {
                            //定金已经付了
                            openPopup(function () {
                                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250606/images/tc4.png')")
                                $('.tc3-btn').hide()
                                $('.tc4-btn').show()
                            })
                            clearInterval(timer)

                        } else if (isPaid) {
                            //尾款已经付了
                            openPopup(function () {

                                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20250606/images/tc5.png')")
                                $('.tc3-btn').hide()
                                $('.tc4-btn').hide()
                            })
                            clearInterval(timer)
                            if (!timer) {
                                closePopup()
                            }
                        }
                    })

                }, 9000)
            }
            if (isFinalPaid) {
                layer.closeAll()
            }
        })
        $('.bg a').click(function () {
            $(this).addClass('current').siblings('a').removeClass('current')
        })

        $('.sppfbtn').click(function () {
            $('.bg9').animate({
                height: '520px'
            })
            $('.sppfbtn2').show(600)
            $('.sp').attr('src','https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=f4663d21a76f452a82f8290db057d96d&rep=1&py=1')
        })
        $('.sppfbtn2').click(function () {
            $('.bg9').animate({
                height: '105px'
            })
            $('.sppfbtn2').hide(600)
            $('.sp').attr('src','about:blank')
        })

        $('.hdgzk').click(function () {$('#hdgz').show()})
        $('.hdgzx').click(function () {$('.h').hide()})
        $(window).scroll(function (e) {
            if (($(window).scrollTop() >= 640) && (downflag == '0')) {
                $('.pf').fadeIn(300)
            }
            if ($(window).scrollTop() >= 640) {
                $('.pf2').fadeIn(300)
            }else {
                $('.pf,.pf2').fadeOut(300)
            }
        })
    }

    //获取定金尾款订单列表
    function getPayList () {
        $('#tbody tr').eq(0).siblings().remove()
        if (orderList.length) {
            var list = orderList

            function computedOrderTypeName (orderPayments) {
                let result = ''
                if (orderPayments && orderPayments.length) {
                    orderPayments.forEach(i => {
                        if (i.payStatusId === PayStatusIdEnum.success && i.payType === OrderPaymentItemPayTypeEnum.prePay) {
                        result += '订金'
                    }
                    if (i.payStatusId === PayStatusIdEnum.success && i.payType === OrderPaymentItemPayTypeEnum.finalPaid) {
                        result += '+尾款'
                    }
                })
                }
                return result
            }

            if (!!list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    var item = list[i]

                    var trhtml = document.createElement('tr')
                    trhtml.id = 'mDiv'
                    trhtml.innerHTML = '<td>' + item.orderNumber + '</td><td>' + computedOrderTypeName(item.orderPayments) +
                        '</td><td>' +
                        timestampToTime(item.createTime) + '</td><td>￥' + item.payTotal + '</td>'

                    $('#tbody').append(trhtml)
                }
            } else {
                var trhtml = document.createElement('tr')
                trhtml.id = 'mDiv'
                trhtml.innerHTML = '<td colspan=\'4\'>您好，当前未查询到任何有效订单</td>'

                $('#tbody').append(trhtml)
            }

            $('#ddcx').show()
        } else {
            var trhtml = document.createElement('tr')
            trhtml.id = 'mDiv'
            trhtml.innerHTML = '<td colspan=\'4\'>您好，当前未查询到任何有效订单</td>'

            $('#tbody').append(trhtml)
        }

        $('#ddcx').show()
    }

    //获取支付状态
    function getPayStatus (cb) {
        var mobileX = $("#hid_mobilex").val();
        $.ajax({
            url: "https://emapp.emoney.cn/buy/order/QueryOrderList?Emapp-Format=EmappJsonp",
            timeout: 5000,
            type: 'get',
            dataType: 'jsonp',
            cache: false,
            data: {"phoneEncrypt": mobileX, "type": 0},
            success: function (data) {
                if (data.result.code === 0) {
                    var arr = data.detail
                    var status = ''
                    orderList = []
                    arr.forEach(item => {
                        if (item && item.orderLogistice && item.orderLogistice.length) {
                        item.orderLogistice.forEach(i => {
                            if (i.activityCode === acCode_pre &&
                                [UserPayStatusIdEnum.finalPaid, UserPayStatusIdEnum.prePaid].includes(item.payStatusId)) {
                            //定金已付
                            isFinalPaid = item.payStatusId === UserPayStatusIdEnum.prePaid
                            //尾款已付
                            isPaid = item.payStatusId === UserPayStatusIdEnum.finalPaid

                            orderList.push(item)
                        }

                        if (i.activityCode === acCode &&
                            [UserPayStatusIdEnum.finalPaid, UserPayStatusIdEnum.prePaid].includes(item.payStatusId)) {
                            //定金已付
                            isFinalPaid = item.payStatusId === UserPayStatusIdEnum.prePaid
                            //尾款已付
                            isPaid = item.payStatusId === UserPayStatusIdEnum.finalPaid

                            orderList.push(item)
                        }
                    })
                    }
                })

                    cb && cb()
                }
            }
        })
    }

    function PushDataToCMP (accode, uid, uname) {
        var data = {
            'appid': '10088',
            'logtype': 'click',
            'mid': '',
            'pid': getQueryString('pid'),
            'sid': getQueryString('sid'),
            'tid': getQueryString('tid'),
            'uid': uid,
            'uname': uname,
            'adcode': accode,
            'targeturl': '',
            'pageurl': window.top.location.href
        }
        var saasUrl = 'http://ds.emoney.cn/saas/queuepush'
        var saasSrc = saasUrl + '?v=' + Math.random()
            + '&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID'
            + '&message=' + encodeURIComponent(JSON.stringify(data))

        var elm = document.createElement('img')
        elm.src = saasSrc
        elm.style.display = 'none'
        document.body.appendChild(elm)
    }
</script>
<script type="text/javascript">
    //打开弹窗
    function openPopup (cb) {
        layer.close(layer.index)
        layer.open({
            type: 1,
            title: false,
            //closeBtn: 0,
            area: ['auto'],
            //shadeClose: true,
            skin: 'layui-layer-nobg', //没有背景色
            content: $('#popwin2').html(),
            success: function (layero, index) {
                cb()
            },
            end: function () { }
        })
    }

    // 处理时间戳
    /* 时间戳转换为时间 */
    function timestampToTime (timestamp) {
        timestamp = timestamp ? timestamp : null
        var date = new Date(timestamp)//时间戳为10位需*1000，时间戳为13位的话不需乘1000
        var Y = date.getFullYear() + '-'
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
        var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
        var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
        var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
        var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
        return Y + M + D + h + m + s
    }

    // input框输入1位数字后自动跳到下一个input聚焦
    function goNextInput (el) {
        var txts = document.querySelectorAll(el)
        for (var i = 0; i < txts.length; i++) {
            var t = txts[i]
            t.index = i
            t.setAttribute('readonly', true)
            t.onkeyup = function () {
                this.value = this.value.replace(/^(.).*$/, '$1')
                var next = this.index + 1
                if (next > txts.length - 1) return
                txts[next].removeAttribute('readonly')
                if (this.value) {
                    txts[next].focus()
                }
            }
        }
        setTimeout(function () {txts[0].focus() }, 100)
        txts[0].removeAttribute('readonly')
    }

    function rand (min, max) {
        return Math.floor(Math.random() * (max - min)) + min
    }

    function getQueryString (name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
        var r = window.location.search.substr(1).match(reg)
        if (r != null) return unescape(r[2])
        return null
    }

    function setCookie (name, value) {
        var expdate = new Date()
        expdate.setTime(expdate.getTime() + 30 * 60 * 1000)
        document.cookie = name + '=' + value + ';expires=' + expdate.toGMTString() + ';path=/'
    }

    function getCookie (c_name) {
        if (document.cookie.length > 0) {
            c_start = document.cookie.indexOf(c_name + '=')
            if (c_start != -1) {
                c_start = c_start + c_name.length + 1
                c_end = document.cookie.indexOf(';', c_start)
                if (c_end == -1) c_end = document.cookie.length
                return unescape(document.cookie.substring(c_start, c_end))
            }
        }
        return ''
    }

    function checkPermission (pid) {
        // if (pid != "888010000" && pid != "888010400") {
        //     layer.msg("本活动仅限小智盈用户参与");
        //     return false;
        // }
        return true;
    }
</script>
<script type="text/javascript">
    $('.nav li').click(function () {
        $(this).addClass('current').siblings('li').removeClass('current');
    });
    // 使用事件委托优化代码结构
    $('.video-btn').click(function () {
        const btnId = this.id;
        const index = btnId.replace('b', '');
        const currentId = `ala${index}`;

        // 显示当前视频容器，隐藏其他容器
        $(`#${currentId}`).show();
        $(`#ala1, #ala2, #ala3, #ala4`).not(`#${currentId}`).hide();

        // 修改当前视频的py参数为1并播放
        const currentVideo = $(`#${currentId} iframe`)[0];
        if (currentVideo) {
            // 保存当前src
            let src = currentVideo.src;

            // 如果src中没有py参数，添加它
            if (!src.includes('py=')) {
                src += (src.includes('?') ? '&' : '?') + 'py=1';
            } else {
                // 否则更新py参数
                src = updatePyParam(src, '1');
            }

            // 重新设置src以触发加载
            currentVideo.src = src;
        }

        // 修改其他视频的py参数为0
        $(`iframe`).not(`#${currentId} iframe`).each(function() {
            this.src = updatePyParam(this.src, '0');
        });
    });

    $(document).on('click', '#s1, #s2, #s3, #s4', function() {
        // 从data属性中获取背景图片和链接地址
        const bgImage = $(this).data('bg-image');
        const srcUrl = $(this).data('src-url');

        // 更新背景图片
        $('.bgb').css('background-image', `url("${bgImage}")`);

        // 更新链接地址
        $('[name="al4b"]').attr('src', srcUrl);
    });

    $(document).on('click', '#c1, #c2, #c3, #c4, #c5, #c5, #c6', function() {
        // 从data属性中获取背景图片和链接地址
        const bgImage = $(this).data('bg-image');
        const srcUrl = $(this).data('src-url');

        // 更新背景图片
        $('.bgf').css('background-image', `url("${bgImage}")`);

        // 更新链接地址
        $('[name="al5"]').attr('src', srcUrl);

        // 特别处理#c6点击事件
        if (this.id === 'c6') {
            $('.ico2').show(); // 显示.ico2元素
        } else {
            $('.ico2').hide(); // 点击其他按钮时隐藏.ico2元素
        }
    });


    // 辅助函数：更新URL中的py参数
    function updatePyParam(url, value) {
        const urlObj = new URL(url);
        urlObj.searchParams.set('py', value);
        return urlObj.toString();
    }

    var videoList = $('iframe')
    var wHeigt = window.innerHeight
    document.addEventListener('scroll', function () {
        var isPlay = false
        //滚动条高度+视窗高度 = 可见区域底部高度
        var visibleBottom = window.scrollY + document.documentElement.clientHeight
        //可见区域顶部高度
        var visibleTop = window.scrollY
        for (var i = 0; i < videoList.length; i++) {
            var centerY = $(videoList[i]).offset().top + (videoList[i].offsetHeight / 2)
            if (centerY > visibleTop && centerY < visibleBottom) {
                if (!isPlay) {
                    videoList[i].src.match(/py=0/) && (videoList[i].src = videoList[i].src.replace(/py=0/, 'py=1'))
                    isPlay = true
                } else {
                    videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
                }
            } else {
                videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
            }
        }
    })
</script>
<script>
    function SetTimeout(year, month, day, hour, minute, second, targetElement) {
        var leftTime = (new Date(year, month - 1, day, hour, minute, second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10); //计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10); //计算剩余的秒数


        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(targetElement).find(".t").html(days);
        $(targetElement).find(".s").html(hours);
        $(targetElement).find(".f").html(minutes);
        $(targetElement).find(".m").html(seconds);
    }

    function checkTime(i) { //将0-9的数字前面加上0，例1变为01
        if (i < 10) {
            i = "0" + i;
        }
        return i;
    }
    djs2=setInterval(function() { SetTimeout(2025,6,10, 18, 0, 0, ".djs2"); }, 1000);

    var classdate = new Date();

    var a=new Date("2025/6/10 18:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs2);
        $(".t").html("00");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
</script>
<script type="text/javascript">document.write(unescape(
    '%3Cscript src=\'https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F\' type=\'text/javascript\'%3E%3C/script%3E'))</script>
</body>
</html>
