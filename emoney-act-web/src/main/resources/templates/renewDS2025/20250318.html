<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>春躁揭福运，续个好彩头</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="节后“第一棒”，大师来添礼 春躁揭福运 续个好彩头"/>
    <link th:href="@{${staticPath}+'static/renewds/20250318/style/css.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH('EM_FUNC_SET_STYLE', '/caption=')
                PC_JH('EM_FUNC_SET_TITLE', '')

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"></div>
<div class="img_3"><div class="main"><div class="j1"></div><!--状态2<div class="j1h"></div>--><a href="javascript:void(0)" class="btn1 dh" clickkey="btn1" clickdata="btn1"></a></div></div>
<div class="img_4"></div>
<div class="img_5"></div>
<div class="img_6">
    <div class="main">
        <a href="javascript:void(0)" class="btn3 dh" clickkey="btn3" clickdata="btn3"></a>
    </div></div>
<div class="bg1"><ul>
    <li>签满7天砸金蛋<a href="javascript:void(0)" class="btn5 dh2" clickkey="btn5" clickdata="btn5"></a></li>
    <li>签满7天砸金蛋<a href="javascript:void(0)" class="btn5 dh2" clickkey="btn5" clickdata="btn5"></a></li>
    <li>签满7天砸金蛋<a href="javascript:void(0)" class="btn5 dh2" clickkey="btn5" clickdata="btn5"></a></li>
    <li>签满7天砸金蛋<a href="javascript:void(0)" class="btn5 dh2" clickkey="btn5" clickdata="btn5"></a></li>
    <li>签满7天砸金蛋<a href="javascript:void(0)" class="btn5 dh2" clickkey="btn5" clickdata="btn5"></a></li>
    <li>签满7天砸金蛋<a href="javascript:void(0)" class="btn5 dh2" clickkey="btn5" clickdata="btn5"></a></li>
    <!--状态2<li><img src="images/d1.png" class="d1" alt=""><img src="images/d1h.png" class="d1h" alt=""></li>
    <li><img src="images/d2.png" class="d1" alt=""><img src="images/d2h.png" class="d1h" alt=""></li>
    <li><img src="images/d3.png" class="d1" alt=""><img src="images/d3h.png" class="d1h" alt=""></li>
    <li><img src="images/d4.png" class="d1" alt=""><img src="images/d4h.png" class="d1h" alt=""></li>
    <li><img src="images/d5.png" class="d1" alt=""><img src="images/d5h.png" class="d1h" alt=""></li>
    <li><img src="images/d6.png" class="d1" alt=""><img src="images/d6h.png" class="d1h" alt=""></li>-->
</ul></div>
<div class="bg2"><div class="ico wobble2"></div><a href="javascript:void(0)" class="btn4 dh" clickkey="btn4" clickdata="btn4"></a></div>
<!--状态2-->
<div class="bg2h" style="display: none;"><div class="txt1">已获得7天软件使用期</div></div>
<div class="hdgz">*活动规则<br>
    2025年3月17日—4月20日期间参与大师续费的用户可参与签到活动，签满7天解锁砸金蛋机会，
    最高可获3个月大师版软件使用期，每位用户仅有1次砸金蛋机会；<br>
    软件使用期将在10个工作日内发放到账号上，若发生退货行为，奖品一律退还；</div>
<div class="foot">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><div class="main"><div class="j2"></div><!--状态2<div class="j2h"></div>--><a href="javascript:void(0)" class="btn2 dh" clickkey="btn2" clickdata="btn2"></a></div></div>
<div class="h" style="display: none;">
    <!--未获得活动权限-->
    <div class="tc" style="display: none;"><a href="javascript:void(0)" class="close"></a><a href="javascript:void(0)" class="tc-btn dh" clickkey="tc-btn1" clickdata="tc-btn1"></a></div>
    <div class="tc2" style="display: none;"><a href="javascript:void(0)" class="close"></a><a href="javascript:void(0)" class="tc-btn dh" clickkey="tc-btn2" clickdata="tc-btn2"></a></div>
    <!--签到成功-->
    <div class="tc3" style="display: none;">
        <a href="javascript:void(0)" class="close"></a>
        再签到<span class="red">6天</span>解锁砸金蛋权限，最高可获<span class="red">3个月</span>大师版软件使用期</div>
    <!--砸蛋中奖-->
    <div class="tc4" style="display: none;"><a href="javascript:void(0)" class="close"></a>您已获得15天软件使用期!</div>
    <div class="tc5" style="display: none;"><a href="javascript:void(0)" class="close"></a><span id="tipmsg"></span></div>
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renewds/20250318/js/mainnew2.js?r=20241209'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renewds_20250318";//模块名称(焦点图2)
    var Remark = "大师续费";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script></body>
</html>
