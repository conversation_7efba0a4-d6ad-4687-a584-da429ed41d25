<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="升级大师3年版即可参与抽奖，赢大师版软件额外使用权！" />
    <link th:href="@{${staticPath}+'static/renewds/20250527/style/common.css?r=20250527'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script th:src="@{${staticPath}+'static/js/awardRotate.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>
<body>
<div class="bod">
    <div class="dbg1"></div>
    <div class="dbg2"></div>
    <div class="dbg3"></div>
    <div class="dbg4">
        <div class="main">
            <div class="zp"></div>
            <div class="zp0"></div>
            <a href="javascript:void(0)" class="zz" id="rotate"></a>
            <div class="go" id="btn-lottery"><img th:src="@{${staticPath}+'static/renewds/20250527/images/go.png'}"/></div>
        </div></div>
    <div class="dbg5">
        <div class="main">
            <div class="md">
                <marquee onmouseover="this.stop()" onmouseout="this.start()" scrollamount="2" direction="up" height="155px">
                    <ul id="user_list">
                        <li>恭喜137****4090用户抽中大师版30天使用权！</li>
                        <li>恭喜198****9960用户抽中大师版120天使用权！</li>
                        <li>恭喜189****2296用户抽中大师版15天使用权！</li>
                        <li>恭喜159****9898用户抽中大师版365天使用权！</li>
                        <li>恭喜189****5045用户抽中大师版90天使用权！</li>
                        <li>恭喜181****6962用户抽中大师版30天使用权！</li>
                        <li>恭喜139****0795用户抽中大师版90天使用权！</li>
                        <li>恭喜131****1077用户抽中大师版180天使用权！</li>
                        <li>恭喜184****6328用户抽中大师版120天使用权！</li>
                        <li>恭喜186****1959用户抽中大师版90天使用权！</li>
                    </ul>
                </marquee>
            </div>
            <div class="cjjl">
                <ul id="my_list">
                </ul>
            </div>
        </div></div>
    <div class="dbg6"></div>
</div>
<div class="h">
    <div class="tc1" style="display: none;">
        <a href="javascript:;" class="close"></a>
        <div class="t1">
            <input type="text" name="name" id="loginname" class="srk1" maxlength="11" />
            <input type="text" name="tel" id="loginmobile" class="srk1" maxlength="12" data-tel />
            <a href="javascript:void(0)" class="tc1-btn1" id="btnlogin"></a>
            <a href="javascript:void(0)" class="tc1-btn2" id="btnclean"></a>
        </div>
    </div>
    <div class="tc2" style="display: none;" id="pop1">
        <a href="javascript:;" class="close"></a>
        <div class="t1">您非升级大师3年版的用户，<br />请升级3年版后再参与抽奖活动~</div>
    </div>
    <div class="tc2" style="display: none;" id="pop2">
        <a href="javascript:;" class="close"></a>
        <div class="t2">您已经参与过本次活动，<br />抽奖机会已用完！</div>
        <div class="t3">请登录/更新软件查看</div>
    </div>
    <div class="tc3" style="display: none;">
        <a href="javascript:;" class="close"></a>
        已成功领取大师版<span class="f1">15天</span>使用权！<br />
        更多详情请联系您的服务专员！</div>
</div>
<input type="hidden" id="ischeckcode" value="0">
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renewds/20250527/js/main.js?r=20241112'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=F1906E5A3FE0C4277B7FA93CECAA1E07' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>