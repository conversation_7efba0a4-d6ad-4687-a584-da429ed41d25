<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>益盟23年周年庆</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="老板派福利，豪横又给力！88财富节 | 益盟23年周年庆"/>
    <link th:href="@{${staticPath}+'static/renewds/20250714/style/css.css?r=20250804'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"><div class="main"><a href="javascript:void(0)" class="btn1 dh" clickkey="btn1" clickdata="btn1"></a></div></div>
<div class="img_3" id="a1"><div class="main"><div class="jg"><div class="t1">大师1年版续费价3380元</div>
    <div class="t2">95折权益续费价</div>
    <div class="t3">3380*95%=<span class="red">3211</span>元/年</div>（下单立省169元）</div></div></div>
<div class="img_4"><div class="main"><a href="javascript:void(0)" class="btn2 dh" clickkey="btn2" clickdata="btn2"></a><div class="hdgz">折扣仅限大师版软件续费以及部分产品升级使用(具体可在使用前咨询客服专员)；<br>
    折扣不与其他红包、优惠券、满减券叠加；<br>
    折扣自领取之日起1年内有效，使用不限次数；<br>
    如遇其他问题，可咨询客服专员；</div></div></div>
<div class="img_5" id="a2"><div class="main"><div class="line">
    <div class="x"><div class="ico2"></div><div class="ico3">68%看涨</div></div><div class="ico3b">32%看跌</div>
</div><a href="javascript:void(0)" class="btn3 dh" clickkey="btn3" clickdata="btn3" data-isup="true">看涨</a><a href="javascript:void(0)" class="btn4 dh" clickkey="btn4" clickdata="btn4" data-isup="false">看跌</a><a href="javascript:void(0)" class="btn9 b">猜涨跌活动结果查询</a>
    <div class="hdgz2">活动共分为5周，每周猜测上证指数周五收盘后的周K线涨跌；<br>
        活动时间为7.28-8.29，猜对1次可获得5天大师软件使用期，最高可获得25天；<br>
        每位用户每周猜涨跌仅可提交1次，每周提交截止时间为周五13:00；<br>
        奖品（软件使用期）将在当周猜对后的3个工作日内赠送到账号中；</div>
</div></div>
<div class="img_6" id="a3"><div class="main"><div class="line2">
    <div class="x"></div>
</div>
    <div class="ico5">
        <ul>
        </ul>
    </div>
    <div class="ico4">
        <ul>
        </ul>
    </div><a href="javascript:void(0)" class="btn5 dh" clickkey="btn5" clickdata="btn5"></a>
</div></div>
<div class="img_7">
    <div class="main"><!-- 代码 开始 -->
        <ul class="silder_nav">
            <li class=""><div class="f1">天玑功能【牛散资金】</div>跟牛散，抢先机，谋长远</li>
            <li class=""><div class="f1">天玑功能【基石F4】</div>稳又不累，长投必备</li>
            <li class=""><div class="f1">全新利器【股力值】</div>强弱轻松识，就用股力值</li>
        </ul>
        <div class="slider_name slider_box pic1">
            <ul class="silder_con">
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renewds/20250714/images/a1.png'}" alt=""></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renewds/20250714/images/a2.png'}" alt=""></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renewds/20250714/images/a3.png'}" alt=""></li>
            </ul>
        </div>
        <script type="text/javascript" th:src="@{${staticPath}+'static/renewds/20250714/js/jquery.slides.js'}"></script>
        <!-- 代码 结束 -->
    </div></div>
<div class="img_8" id="a4">
    <div class="main"><a href="javascript:void(0)" class="btn6 dh" clickkey="btn6" clickdata="btn6"></a><a href="javascript:void(0)" class="btn7 dh" clickkey="btn8" clickdata="btn8"></a><a href="javascript:void(0)" class="btn10 b">奖池内容及中奖结果查询</a>
        <div class="hdgz3">*在7.14-8.29期间参与大师续费的用户，将在8.8/8.18/8.28分别获得1次砸金蛋次数（超过对应时间点续费，则无相应砸蛋机会。仅在8号前参与续费方可累计获得3次机会，多次续费或续费1年以上，同样最多获得3次砸蛋机会）。奖品于3个工作日内发放；</div></div></div>
<div class="foot">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><div class="main"><a href="javascript:void(0)" class="btn8 dh" clickkey="btn8" clickdata="btn8"></a></div></div>
<img th:src="@{${staticPath}+'static/renewds/20250714/images/pf.png'}" alt="" usemap="#Map" class="pf2">
<map name="Map">
    <area shape="rect" coords="12,70,195,107" href="#a1">
    <area shape="rect" coords="9,109,193,146" href="#a2">
    <area shape="rect" coords="11,150,194,185" href="#a3">
    <area shape="rect" coords="13,186,193,224" href="#a4">
</map>
<div class="h" style="display: none;">
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tclogin-btn1">登录</a><a href="javascript:;" class="tclogin-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
    <div class="tc1" style="display: none;"><a href="javascript:void(0)" class="close"></a><a href="javascript:void(0)" class="tc-btn dh"></a></div>
    <div class="tc2" style="display: none;"><a href="javascript:void(0)" class="close"></a></div>
    <div class="tc3" style="display: none;" id="prognoseTip"><a href="javascript:void(0)" class="close"></a><div class="t1">对不起　<br>您本周已经　<br>参与过活动！</div></div>
    <div class="tc3" style="display: none;" id="prognoseDetailDiv"><a href="javascript:void(0)" class="close"></a>
        <div class="t2">猜涨跌活动结果及中奖人数公示</div>
        <div class="t3">
        <ul id="prognoseDetailUl">
<!--            <li>第一周（7.28-8.1）：上证指数周K线收盘结果：<span class="green">跌</span><br>-->
<!--                恭喜<span class="red2">895</span>位用户猜对，并获得<span class="red2">5天大师软件使用期</span></li>-->
<!--            <li>第二周（8.4-8.8）：上证指数周K线收盘结果：<span class="red2">涨</span><br>-->
<!--                恭喜<span class="red2">895</span>位用户猜对，并获得<span class="red2">5天大师软件使用期</span></li>-->
<!--            <li>第三周（8.11-8.15）：上证指数周K线收盘结果：<span class="red2">涨</span><br>-->
<!--                恭喜<span class="red2">621</span>位用户猜对，并获得<span class="red2">5天大师软件使用期</span></li>-->
<!--            <li>第四周（8.18-8.22）：上证指数周K线收盘结果：<span class="green">跌</span><br>-->
<!--                恭喜<span class="red2">338</span>位用户猜对，并获得<span class="red2">5天大师软件使用期</span></li>-->
<!--            <li>第五周（8.25-8.29）：上证指数周K线收盘结果：<span class="red2">涨</span><br>-->
<!--                恭喜<span class="red2">1013</span>位用户猜对，并获得<span class="red2">5天大师软件使用期</span></li>-->
        </ul>
        </div>
    </div>
    <div class="tc3" style="display: none;" id="rewardsPop"><a href="javascript:void(0)" class="close"></a><div class="t2">砸金蛋奖品展示</div><div class="t4"><ul>
        <li>10天大师软件使用期</li>
        <li>45天大师软件使用期</li>
        <li>60天大师软件使用期</li>
        <li>3个月牛散资金</li>
        <li>3个月基石F4</li>
        <li>3个月αβ策略池</li>
        <li>3个月ETF价值掘金</li>
        <li>3个月股力值</li>
    </ul></div><div class="t5">您的中奖结果</div><div class="t6"></div>
    </div>
    <div class="tc4" style="display: none;" id="luckcydrawResult"><a href="javascript:void(0)" class="close"></a><span id="pname"></span></div>
    <div class="tc5" style="display: none;" id="luckcydrawTip"><a href="javascript:void(0)" class="close"></a></div>
    <div class="tc6" style="display: none;"><a href="javascript:void(0)" class="close"></a><a href="javascript:void(0)" class="tc-btn1">确认</a><a href="javascript:void(0)" class="tc-btn2">取消</a>
        <span class="red2" id="preResult">看涨</span>
    </div>
</div>
<input type="hidden" id="ischeckcode" value="1">
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renewds/20250714/js/main.js?r=20250807'}"></script>
<script src="https://imgtongji.emoney.cn/scripts/https/emoneyanalytics.js" type="text/javascript"></script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "dsxf-20250701";//大师续费
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
