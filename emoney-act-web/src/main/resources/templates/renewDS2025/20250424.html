<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,操盘手,抽奖,赢大奖" />
    <meta name="Description" content="518股民节 益盟狂撒千万补贴 续费送万元大礼" />
    <link th:href="@{${staticPath}+'static/renewds/20250424/css/cssnew.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script th:src="@{${staticPath}+'static/js/awardRotate.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>
<body>
<div class="dbg1"></div>
<div class="dbg2"></div>
<div class="dbg3"></div>
<div class="dbg4"></div>
<div class="dbg5"><div class="main"><a href="javascript:void(0)" class="btn1 dh"></a></div></div>
<div class="dbg6"><div class="main"><a href="" class="btn3 a1 dh"><div class="ico"></div></a><a href="" class="btn3 a2 dh"></a></div></div>
<div class="dbg7"><div class="main"><a href="" class="btn3 a3 dh"></a><a href="https://www.emoney.cn/dianjin/hd/sz-ynsww/index.html" target="_blank" class="txt1">点我了解详细的功能介绍&rarr;</a></div></div>
<div class="dbg8"><div class="main"><a href="" class="btn3 a4 dh"></a><a href="https://www.emoney.cn/dianjin/hd/sz-zhsbq/index.html" target="_blank" class="txt2">点我了解详细的功能介绍&rarr;</a></div></div>
<div class="dbg9">
    <div class="main">
        <div class="zp"></div>
        <div class="zp0"></div>
        <a href="###" class="zz" id="rotate"></a>
        <div class="go" id="btn-lottery">
            <div><img th:src="@{${staticPath}+'static/renewds/20250424/images/go.png'}"/></div>
            <div style="margin-top: -6px;">(<span id="rft_num"></span>次)</div>
        </div>
        <div class="md">
            <marquee onmouseover="this.stop()" onmouseout="this.start()" scrollamount="2" direction="up" height="155px">
                <ul id="user_list">
                    <li>恭喜 136****3178 用户喜获财富券 3个月</li>
                    <li>恭喜 135****6942 用户喜获财富券 2个月</li>
                    <li>恭喜 139****5747 用户喜获免单券</li>
                    <li>恭喜 159****8351 用户喜获财富券 3个月</li>
                    <li>恭喜 157****2609 用户喜获财富券 5个月</li>
                    <li>恭喜 150****7435 用户喜获财富券 1个月</li>
                    <li>恭喜 188****5287 用户喜获财富券 3个月</li>
                    <li>恭喜 186****3694 用户喜获财富券 5个月</li>
                    <li>恭喜 185****1076 用户喜获财富券 1个月</li>
                    <li>恭喜 183****6520 用户喜获财富券 2个月</li>
                    <li>恭喜 181****8937 用户喜获财富券 5个月</li>
                    <li>恭喜 177****2465 用户喜获财富券 3个月</li>
                    <li>恭喜 177****0409 用户喜获免单券</li>
                    <li>恭喜 156****9072 用户喜获财富券 3个月</li>
                    <li>恭喜 137****3649 用户喜获财富券 2个月</li>
                    <li>恭喜 153****2180 用户喜获财富券 1个月</li>
                    <li>恭喜 134****6795 用户喜获财富券 3个月</li>
                </ul>
            </marquee>
            <ul style="margin-top:50px;" id="my_list">
            </ul>
        </div>
        <div class="hdgz">活动说明：
            <ul>
                <li>续1年转1次，续2年转2次，续3年转3次，4.21-4.30续费用户多送1次</li>
                <li>仅限5.18日前续费的用户参与</li>
                <li>最终解释权归益盟股份所有！</li>
            </ul>
        </div></div></div>
<div class="footer">欢迎登录益盟官方网站www.emoney.cn<br />
    本活动最终解释权归上海益盟软件技术股份有限公司　沪ICP备06000340<br />
    软件需数据支持，仅提供辅助建议，风险需谨慎；投资顾问意见仅作参考。</div>
<div class="pf">
    <div class="main"><a href="" class="btn2 dh"></a></div></div>
<div class="h">
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
    <div class="tc" style="display: none;">
        <a href="#" class="close"></a>
        <div class="tc_1">获得财富券</div>
        <div class="tc_2"><strong>1个月</strong>产品使用期已赠送至账户</div>
    </div>
    <div class="tc2" style="display: none;"><a href="javascript:void(0)" class="close"></a></div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renewds/20250424/js/mainnew.js?r=20241112'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renewds_20250424";//模块名称(焦点图2)
    var Remark = "大师续费";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>