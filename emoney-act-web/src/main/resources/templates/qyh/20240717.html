<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="【蓄势盘龙】+【主题博弈】限时联合招生 2024 唯一一次五星波段招生季">
    <link th:href="@{${staticPath}+'static/qyh/20240717/css/style.css?r='+${resourceVersion}}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qyh/20231205/js/json2.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var www="../";
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj =
                    GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {}
        }
        (function() {
            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body><div class="bod">
    <div class="img_1"></div>
    <div class="img_2"></div>
    <div class="img_3 wow fadeInUp" data-wow-duration="1000ms"><div class="main"><a class="btn2 dh" href="javascript:void(0)"></a><div class="ico"></div></div></div>
    <div class="bg">
        <div class="img_4 wow fadeInUp" data-wow-duration="1000ms"><div class="main"><iframe src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=50450704393a4a8e9ecdc6d1c6dbf29b&rep=1&py=1" allowfullscreen="true" class="swf1" name="al2"></iframe></div></div>
        <div class="img_5 wow fadeInUp" data-wow-duration="1000ms"></div>
        <div class="img_6 wow fadeInUp" data-wow-duration="1000ms"></div>
        <div class="img_7 wow fadeInUp" data-wow-duration="1000ms"></div>
        <div class="img_8 wow fadeInUp" data-wow-duration="1000ms"><div class="main"><div class="al1 d1"><!-- 代码 开始 -->
            <div class="slider_name slider_box">
                <ul class="silder_con" style=" width: 5200px;">
                    <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20240717/images/p1.png?r='+${resourceVersion}}"></li>
                    <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20240717/images/p2.png?r='+${resourceVersion}}"></li>
                    <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20240717/images/p3.png?r='+${resourceVersion}}"></li>
                    <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20240717/images/p4.png?r='+${resourceVersion}}"></li>
                    <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20240717/images/p5.png?r='+${resourceVersion}}"></li>
                </ul>
            </div><a href="javascript:void(0)" class="prev"></a><a href="javascript:void(0)" class="next"></a>
            <!-- 代码 结束 -->
        </div></div></div>
    </div>
    <div class="img_9 wow fadeInUp" data-wow-duration="1000ms"></div>
    <div class="img_13">
        <div class="main"><a class="btn3 dh" href="javascript:void(0)"></a></div></div>

    <div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" class="b">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
        本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
</div>
<a class="pf" href="javascript:void(0)"></a>
<div class="h" style="display: none">
    <div class="logintc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">

<script type="text/javascript" th:src="@{${staticPath}+'static/qyh/20240717/js/jquery.slides.js'}"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/qyh/20240717/js/main.js?r=20240110'}" type="text/javascript"></script>

<script src="https://www.emoney.cn/dianjin/bb/wow2.js"></script>
<link href="https://www.emoney.cn/dianjin/bb/animate.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">
    document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=8B9093887C7B911EA8A566613F19F22D' type='text/javascript'%3E%3C/script%3E"));
</script>
</body>
</html>
