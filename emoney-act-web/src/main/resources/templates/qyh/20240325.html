<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="稳增长 新经济 新机遇 跟主力 盯主线 决战大波段 智盈大师掘金版--对的时间做对的选择">
    <link th:href="@{${staticPath}+'static/qyh/20240325/style/common_new.css?r=20240327'}" rel="stylesheet" type="text/css" />
    <link th:href="@{${staticPath}+'static/qyh/20240325/style/popup.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qyh/20231205/js/json2.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qgn/20231011/js/popup.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var www="../";
        function GetExternal () {
            return window.external.EmObj
        }

        function PC_JH (type, c) {
            try {
                var obj =
                    GetExternal()
                return obj.EmFunc(type, c)
            } catch (e) {}
        }

        (function () {

            function LoadComplete () {
                try {
                    PC_JH('EM_FUNC_DOWNLOAD_COMPLETE', '')
                } catch (ex) {}
            }

            function EM_FUNC_HIDE () {
                try {
                    PC_JH('EM_FUNC_HIDE', '')
                } catch (ex) {}
            }

            function EM_FUNC_SHOW () {
                try {
                    PC_JH('EM_FUNC_SHOW', '')
                } catch (ex) {}
            }

            function IsShow () {
                try {
                    return PC_JH('EM_FUNC_WND_ISSHOW', '')
                } catch (ex) {
                    return '0'
                }
            }

            function openWindow () {
                LoadComplete()

                PC_JH('EM_FUNC_WND_SIZE', 'w=1300,h=820,mid')

                if (IsShow() != '1') {
                    EM_FUNC_SHOW()
                }
            }

            openWindow()
        })()

    </script>
    <script type="text/javascript">
        function getQueryString (name) {
            var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
            var r = window.location.search.substr(1).match(reg)
            if (r != null) return unescape(r[2])
            return null
        }
    </script>
</head>
<body>
<div class="bg9">
    <div class="sppf">
        <div class="main"><a href="javascript:void(0)" class="sppfbtn"></a></div>
    </div>
    <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="sp"></iframe>
    <a href="javascript:void(0)" class="sppfbtn2"></a></div>
<div class="bod">
    <div class="dbg1"><div class="main">
        <div class="djs2">
            <div class="t">00</div>
            <div class="s">00</div>
            <div class="f">00</div>
        </div></div></div>
    <div class="dbg2"></div>
    <div class="dbg3">
        <div class="main"><a href="javascript:void(0)" class="btn1 dh" clickkey="20240325Act" clickdata="btn1"></a>
            <div class="txt1">已有<span name="usedCount"></span>人申请</div>
            <!--<a href="#al2" class="btn7"></a><a href="#al3" class="btn8"></a><a href="#al4" class="btn9"></a>-->
            <div class="djs"></div>
        </div>
    </div>
    <div class="dbg7">
        <div class="main"><div id="a1" style="position: absolute; top: 237px"></div>
            <a href="javascript:void(0)" class="txt2l5 hdgzk c">详情查看活动规则</a>
            <a href="javascript:void(0)" class="btn6 dh popupD" flag="openpop" data-accode="AC588Pop2024032501" data-addclass="btn6h" data-type="3" clickkey="20240325Act" clickdata="buy3"></a>
            <div id="k"></div>
            <script type="text/javascript">
                function bh () {
                    var i = document.getElementById('k')
                    i.style.display = 'block'
                    setTimeout('clock()', 1300)
                }

                function clock () {
                    var i = document.getElementById('k')
                    i.style.display = 'none'
                }

            </script>
        </div>

    </div>
    <div class="dbg4"></div>
    <div id="dbg5"><div class="main"><a href="javascript:void(0)" class="btn2 an1 dh" clickkey="20240325Act" clickdata="btn2"></a> </div></div>

    <div class="dbg6"><div class="main"><a href="javascript:void(0)" class="btn2 an2 dh" clickkey="20240325Act" clickdata="btn2"></a> </div></div>
    <div id="dbg8a">
        <div class="bg" style="padding-top: 325px;">
            <ul>
                <a class="current" th:href="@{${staticPath}+'static/qyh/20240325/images/01.png'}" target="al1">
                    <div class="f1">纵横趋势          </div>
                    <div class="f3">横看蓄势，纵看突破，区间波段</div>
                </a> <a th:href="@{${staticPath}+'static/qyh/20240325/images/02.png'}" target="al1">
                <div class="f1">价格冲击波</div>
                <div class="f3">最大值等突破，最小值等反转</div>
            </a>
            </ul>
            <iframe allowfullscreen="true" frameborder="0" th:src="@{${staticPath}+'static/qyh/20240325/images/01.png'}" scrolling="no" class="swf1" name="al1"></iframe>
        </div>
    </div>
    <div id="dbg8">
        <div class="bg" style="padding-top: 105px;">
            <ul>
                <a class="current" th:href="@{${staticPath}+'static/qyh/20240325/images/a1.png'}" target="al3">
                    <div class="f1">强弱乾坤</div>
                    <div class="f3">强识别强周期，增强概率</div>
                </a>
                <a th:href="@{${staticPath}+'static/qyh/20240325/images/a2.png'}" target="al3">
                    <div class="f1">三线强势</div>
                    <div class="f3">多周期共振，锁定超强板块</div>
                </a>
                <a th:href="@{${staticPath}+'static/qyh/20240325/images/a3.png'}" target="al3">
                    <div class="f1">强龙起势</div>
                    <div class="f3">洞察强势股启动关键，踩准时机</div>
                </a>
            </ul>
            <iframe allowfullscreen="true" frameborder="0"  th:src="@{${staticPath}+'static/qyh/20240325/images/a1.png'}" scrolling="no" class="swf1" name="al3"></iframe>
        </div>
    </div>
    <div id="dbg9"><div class="main"><a href="javascript:void(0)" class="btn2 an3 dh" clickkey="20240325Act" clickdata="btn2"></a></div><div class="bg" style="padding-top: 82px;">
        <ul>
            <a class="current" th:href="@{${staticPath}+'static/qyh/20240325/images/b1.png'}" target="al2">
                <div class="f1">黄金三买</div>
                <div class="f3">出击三类黄金买点</div>
            </a> <a th:href="@{${staticPath}+'static/qyh/20240325/images/b2.png'}" target="al2">
            <div class="f1">量价异动</div>
            <div class="f3">监测形态异动拐点</div>
        </a>
            <a th:href="@{${staticPath}+'static/qyh/20240325/images/b3.png'}" target="al2">
                <div class="f1">资金异动</div>
                <div class="f3">精选主力资金加持</div>
            </a>
            <a th:href="@{${staticPath}+'static/qyh/20240325/images/b4.png'}" target="al2">
                <div class="f1">长线价值</div>
                <div class="f3">基本面优中选优</div>
            </a>
        </ul>
        <iframe allowfullscreen="true" frameborder="0"  th:src="@{${staticPath}+'static/qyh/20240325/images/b1.png'}" scrolling="no" class="swf1" name="al2"></iframe>
    </div></div>
    <div id="dbg10"></div>
    <div id="dbg11"></div>
    <div id="dbg12">
        <div class="main">
            <div id="div1">
                <ul>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240325/images/zs10.jpg'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240325/images/zs1.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240325/images/zs2.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240325/images/zs3.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240325/images/zs4.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240325/images/zs5.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240325/images/zs6.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240325/images/zs7.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240325/images/zs8.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240325/images/zs9.png'}"></li>
                </ul>
                <a href="javascript:;" style="left:10px;"></a> <a href="javascript:;" style="right:10px;"></a></div>
            <script type="text/javascript">
                window.onload = function () {
                    var oDiv = document.getElementById('div1')
                    var oUl = oDiv.getElementsByTagName('ul')[0]
                    var aLi = oUl.getElementsByTagName('li')
                    var aA = oDiv.getElementsByTagName('a')
                    var iSpeed = 1//正左负右
                    var timer = null
                    //计算ul的宽为所有li的宽的和;
                    oUl.innerHTML += oUl.innerHTML + oUl.innerHTML
                    oUl.style.width = aLi[0].offsetWidth * aLi.length + 'px'

                    function Slider () {
                        if (oUl.offsetLeft < -oUl.offsetWidth / 2) {
                            oUl.style.left = 0
                        } else if (oUl.offsetLeft > 0) {
                            oUl.style.left = -oUl.offsetWidth / 2 + 'px'
                        }
                        oUl.style.left = oUl.offsetLeft - iSpeed + 'px'//正负为方向
                    }

                    timer = setInterval(Slider, 30)
                    aA[0].onclick = function () {
                        iSpeed = 1 //控制速度的正负
                    }
                    aA[1].onclick = function () {
                        iSpeed = -1
                    }
                    oDiv.onmouseover = function () {
                        clearInterval(timer)
                    }
                    oDiv.onmouseout = function () {
                        timer = setInterval(Slider, 30)
                    }
                }
            </script>
            <div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" class="d" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br/>
                本活动最终解释权归益盟股份有限公司　沪ICP备06000340 </div>
        </div>
    </div>
</div>
<div class="pf"><div class="main"><a href="javascript:void(0)" class="btn3 dh" clickkey="20240325Act" clickdata="btn2"></a></div></div>
<a href="javascript:void(0)" class="pf2" clickkey="20240325Act" clickdata="btn1"></a>
<a href="javascript:void(0)" class="wddd"></a>
<div class="h" id="ddcx">
    <div class="wddd2 orderlist"><a href="javascript:void(0)" class="close hdgzx"></a>
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tbody id="tbody">
            <tr>
                <td class="bt">订单号</td>
                <td class="bt">订单类型</td>
                <td class="bt">支付时间</td>
                <td class="bt">支付金额</td>
            </tr>
            <!--<tr>
              <td>202306200011</td>
              <td>订金</td>
              <td>2023.6.20</td>
              <td>¥1000</td>
            </tr>-->
            </tbody>
        </table>
    </div>
</div>
<div class="h" id="hdgz">
    <div class="hdgz"><a href="javascript:void(0)" class="close hdgzx"></a>
        <ul>
            <li>本活动仅限（2024/3/6/-3/31）从智盈升级到智盈大师且在益盟指定券商营业部首次开户的用户；</li>
            <li> 升级智盈大师之日起的2个月内完成开户，可参与累计金豆用于补贴软件费用或兑换优惠券（金豆累计期为：开户成功后的365天内）； </li>
            <li>补贴期内，最高可补贴金额为：实际支付软件费用，且补贴期内不可退货（补贴期为：沪深双市开户成功后的365天）；</li>
            <li>未到补贴期出现退货，即取消活动资格；</li>
            <li class="red2">开户手机号须与软件绑定手机号一致；</li>
            <li>软件费用或优惠券仅可补贴一次，如完成金豆补贴，即活动结束；</li>
            <li>详细补贴规则可在活动参与成功之后进入补贴页面查看。</li>
        </ul>
        <strong>*本活动最终解释权归益盟股份有限公司所有。</strong></div>
</div>
<div id="popwin1" style="display:none;">
    <div class="tc0" id="divCheck" style="display:block;">
        <div class="font1"><span id="checkcode">8888</span></div>
        <div class="t1">
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
        </div>
        <div class="clear"></div>
        <div class="btn_tc0"><a href="javascript:void(0)" id="btn_submit"></a></div>
    </div>
</div>
<div id="popwin2" style="display:none;">
    <div class="tc1b" id="divSuc" style="display:block;"> <a href="javascript:void(0)" class="tc3-btn dh"></a> <a href="javascript:void(0)" class="tc4-btn dh"></a> </div>
</div>

<div class="h" style="display: none" id="logindiv">
    <div class="logintc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>

<script>
    $('.btn1,.btn2,.btn3,.pf2').click(function () {
        document.querySelector('#a1').scrollIntoView({ behavior: 'smooth' })
        bh()
    })

    $('.wddd').click(function () {
        getPayList(uid)
    })

    $('.btn6').click(function () {
        if (isFinalPaid) {
            //3年版定金已经付了
            openPopup(function () {
                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20240325/images/tc4.png')")
                $('.tc3-btn').hide()
                $('.tc4-btn').show()
            })

        } else if (isPaid || isNormalPaid) {
            //三年版尾款已经付了
            openPopup(function () {
                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20240325/images/tc5.png')")
                $('.tc3-btn').hide()
                $('.tc4-btn').hide()
            })
        }else{
            //1年版支付弹窗
            openPopup(function () {
                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20240325/images/tc3.png')")
                $('.tc3-btn').show()
                $('.tc4-btn').hide()
            })

            var cmpCode = $(this).attr("data-accode");
            PushDataToCMP(cmpCode, uid, '', '');
        }
    })

    function getPayList () {
        $('#tbody tr').eq(0).siblings().remove()
        if (orderList.length) {
            var list = orderList

            function computedOrderTypeName (orderPayments) {
                let result = ''
                if (orderPayments && orderPayments.length) {
                    orderPayments.forEach(i => {
                        if (i.payStatusId === PayStatusIdEnum.success && i.payType === OrderPaymentItemPayTypeEnum.prePay) {
                        result += '订金'
                    }
                    if (i.payStatusId === PayStatusIdEnum.success && i.payType === OrderPaymentItemPayTypeEnum.finalPaid) {
                        result += '+尾款'
                    }
                    if (i.payStatusId === PayStatusIdEnum.success && i.payType === OrderPaymentItemPayTypeEnum.normal) {
                        result += '订购'
                    }
                })
                }
                return result
            }

            if (!!list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    var item = list[i]

                    var trhtml = document.createElement('tr')
                    trhtml.id = 'mDiv'
                    trhtml.innerHTML = '<td>' + item.orderNumber + '</td><td>' + computedOrderTypeName(item.orderPayments) +
                        '</td><td>' +
                        timestampToTime(item.createTime) + '</td><td>￥' + item.payTotal + '</td>'

                    $('#tbody').append(trhtml)
                }
            } else {
                var trhtml = document.createElement('tr')
                trhtml.id = 'mDiv'
                trhtml.innerHTML = '<td colspan=\'4\'>您好，当前未查询到任何有效订单</td>'

                $('#tbody').append(trhtml)
            }

            $('#ddcx').show()
        } else {
            var trhtml = document.createElement('tr')
            trhtml.id = 'mDiv'
            trhtml.innerHTML = '<td colspan=\'4\'>您好，当前未查询到任何有效订单</td>'

            $('#tbody').append(trhtml)
        }

        $('#ddcx').show()
    }

    var downflag = '0'
    var al1 = '0'
    var al2 = '0'
    var al3 = '0'
    var al4 = '0'
    var al5 = '0'
    var al6 = '0'
    var al7 = '0'
    var al8 = '0'

    $('.bg a').click(function () {
        $(this).addClass('current').siblings('a').removeClass('current')
    })
    $('#yd1').click(function () {$('#yd1b a').removeClass('current')})
    $('#yd2').click(function () {$('#yd2b a').removeClass('current')})
    $('#yd3').click(function () {$('#yd3b a').removeClass('current')})
    $('#yd4').click(function () {$('#yd4b a').removeClass('current')})

    $('.a1').click(function () {
        $('.pc').show()
        $('.sj').hide()
        $('.s1').attr('href', '#al1')
        $('.s2').attr('href', '#al2')
        $('.s3').attr('href', '#al3')
        $('.s4').attr('href', '#al4')
        $('.s5').attr('href', '#al5')
        $('.s6').attr('href', '#al6')
        $('.s7').attr('href', '#al7')
        $('.btn7').attr('href', '#al1')
        $('.btn8').attr('href', '#al3')
        $('.btn9').attr('href', '#al4')
    })
    $('.a2').click(function () {
        $('.pc').hide()
        $('.sj').show()
        $('.s1').attr('href', '#al1b')
        $('.s2').attr('href', '#al2b')
        $('.s3').attr('href', '#al3b')
        $('.s4').attr('href', '#al4b')
        $('.s5').attr('href', '#al5b')
        $('.s6').attr('href', '#al6b')
        $('.s7').attr('href', '#al7b')
        $('.btn7').attr('href', '#al1b')
        $('.btn8').attr('href', '#al3b')
        $('.btn9').attr('href', '#al4b')
    })

    $('.sppfbtn').click(function () {
        $('.bg9').animate({
            height: '500px'
        })
        $('.sppfbtn2').show(600)
        $('.sp').attr('src','https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=d552d5afc8af48578393f6ee0558b228&rep=1&py=1')
    })
    $('.sppfbtn2').click(function () {
        $('.bg9').animate({
            height: '80px'
        })
        $('.sppfbtn2').hide(600)
        $('.sp').attr('src','https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=d552d5afc8af48578393f6ee0558b228&rep=1&py=0')
    })

    $('.hdgzk').click(function () {$('#hdgz').show()})
    $('.hdgzx').click(function () {$('.h').hide()})
</script>
<script type="text/javascript">
    var cookiename = 'emoney.act20240325'
    var initCount = 500
    var flag = true
    var userid = $("#hid_uid").val();
    var uid = $("#hid_uid").val();
    var pid = $("#hid_pid").val();
    var mobileX = $("#hid_mobilex").val();
    var staticPath = $("#hid_staticPath").val();
    var timer = null
    var OrderPaymentItemPayTypeEnum = {
        normal: 0, prePay: 1, finalPaid: 2
    }
    var UserPayStatusIdEnum = {
        noPay: 0, prePaid: 1, finalPaid: 200, reimburse: 201
    }
    /**
     *  "payStatusId":支付状态(0:等待支付,1:支付中,101:支付失败,200:支付成功,201:已退款)
     */
    var PayStatusIdEnum = {
        waiting: 0, paying: 1, fail: 101, success: 200, reimburse: 201
    }
    var acCode_pre = 'ac-1240304110554102';//3年版物流包
    var acCode = 'ac-124031818185089'//1年版
    //付了尾款
    var isFinalPaid = false
    //付了尾款
    var isPaid = false
    //普通订单
    var isNormalPaid = false
    //订单
    var orderList = []

    var timer = null

    var phoneEncrypt = getQueryString('phoneEncrypt')
    var UPcid = getQueryString('UPcid')

    $(document).ready(function () {
        if($("#hid_isLogin").val() == "1") {
            getPayStatus()
            InitCount();
        }else{
            //弹出登录窗口
            $("#logindiv").show();
            $(".logintc").show();
        }
    })

    function getPayStatus (cb) {
        var mobileX = $("#hid_mobilex").val();
        $.ajax({
            url: "https://emapp.emoney.cn/buy/order/QueryOrderList?Emapp-Format=EmappJsonp",
            timeout: 5000,
            type: 'get',
            dataType: 'jsonp',
            cache: false,
            data: {"phoneEncrypt": mobileX, "type": 0},
            success: function (data) {
                if (data.result.code === 0) {
                    var arr = data.detail
                    var status = ''
                    orderList = []
                    arr.forEach(item => {
                        if (item && item.orderLogistice && item.orderLogistice.length) {
                            item.orderLogistice.forEach(i => {
                                if (i.activityCode === acCode_pre &&
                                    [UserPayStatusIdEnum.finalPaid, UserPayStatusIdEnum.prePaid].includes(item.payStatusId)) {
                                    //定金已付
                                    isFinalPaid = item.payStatusId === UserPayStatusIdEnum.prePaid
                                    //尾款已付
                                    isPaid = item.payStatusId === UserPayStatusIdEnum.finalPaid

                                    orderList.push(item)
                                }
                                if (i.activityCode === acCode &&
                                    [UserPayStatusIdEnum.finalPaid, UserPayStatusIdEnum.prePaid].includes(item.payStatusId)) {

                                    isNormalPaid = item.payStatusId === UserPayStatusIdEnum.finalPaid

                                    orderList.push(item)
                                }
                            })
                        }
                    })

                    cb && cb()
                }
            }
        })
    }

    function InitCount () {
        $.ajax({
            type: 'get',
            url: 'https://act.emoney.cn/activity/user/getcountbyactcode?actcode=20240325',
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if (data.code == '200') {
                    var num = 0
                    if (!!data.data) {
                        num = data.data.split(',')[0]
                    }
                    $('.djs').html(addPreZero(initCount - parseInt(num)))//剩余席位
                    $('[name=usedCount]').html(num>initCount?initCount:num)//已申请席位

                    setTimeout('InitCount()', 60000 * 5)
                }
            }
        })
    }
    //打开弹窗
    function openPopup (cb) {
        layer.close(layer.index)
        layer.open({
            type: 1,
            title: false,
            //closeBtn: 0,
            area: ['414px','324px'],
            //shadeClose: true,
            skin: 'layui-layer-nobg', //没有背景色
            content: $('#popwin2').html(),
            success: function (layero, index) {
                cb()
            },
            end: function () { }
        })
    }

    // 处理时间戳
    /* 时间戳转换为时间 */
    function timestampToTime (timestamp) {
        timestamp = timestamp ? timestamp : null
        var date = new Date(timestamp)//时间戳为10位需*1000，时间戳为13位的话不需乘1000
        var Y = date.getFullYear() + '-'
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
        var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
        var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
        var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
        var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
        return Y + M + D + h + m + s
    }

    var wz=getQueryString("wz");

    $(document).on('click', '.tc3-btn,.tc4-btn', function () {
        var payurl = "";
        if (!!wz){
            payurl = 'https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&resourcestypeid=234&resourcesid=1244376&ordersource=A12112&businessType=biztyp-szds'

            //payurl = 'https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&ordersource=A12112&businessType=biztyp-szds'
            //if($(this).attr("class").indexOf("tc4-btn")>-1){
                payurl = 'https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&resourcestypeid=234&resourcesid=1244376&ordersource=A12112&businessType=biztyp-szds'
            //}
        }
        else {
            payurl = 'https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&resourcestypeid=234&resourcesid=1244376&ordersource=A12113&businessType=biztyp-szds'

            //payurl = 'https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&ordersource=A12113&businessType=biztyp-szds'
            //if($(this).attr("class").indexOf("tc4-btn")>-1){
                payurl = 'https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&resourcestypeid=234&resourcesid=1244376&ordersource=A12113&businessType=biztyp-szds'
            //}
        }

        if (!!GetExternal()) {
            PC_JH('EM_FUNC_OPEN_LIVE_VIDEO', '15,' + payurl)
        } else {
            //0x 密文手机 + uid  拼在payurl
            payurl += "&phoneEncrypt=" + mobileX + "&UPcid=" + uid;
            $(this).attr("target", "_blank");
            $(this).attr("href", payurl);
        }

        if (!isFinalPaid && !isPaid) {
            if (timer) clearInterval(timer)

            timer = setInterval(function () {
                getPayStatus(function () {
                    if (isFinalPaid) {
                        //定金已经付了
                        openPopup(function () {
                            $('.tc1b').css('background-image',  "url('"+staticPath+"static/qyh/20240325/images/tc4.png')")
                            $('.tc3-btn').hide()
                            $('.tc4-btn').show()
                        })
                        clearInterval(timer)

                    } else if (isPaid) {
                        //尾款已经付了
                        openPopup(function () {

                            $('.tc1b').css('background-image', "url('"+staticPath+"static/qyh/20240325/images/tc5.png')")
                            $('.tc3-btn').hide()
                            $('.tc4-btn').hide()
                        })
                        clearInterval(timer)
                        if(!timer) {
                            closePopup()
                        }
                    }
                })

            }, 1000*60)
        }
        // 如果付了定金,去付尾款
        if(isFinalPaid) {
            layer.closeAll()
        }
    })

    function PushDataToCMP (accode, uid, uname, type) {
        var data = {
            'appid': '10088',
            'logtype': 'click',
            'mid': '',
            'pid': getQueryString('pid'),
            'sid': getQueryString('sid'),
            'tid': getQueryString('tid'),
            'uid': uid,
            'uname': uname,
            'adcode': accode,
            'targeturl': '',
            'pageurl': window.top.location.href
        }
        var saasUrl = 'http://ds.emoney.cn/saas/queuepush'
        var saasSrc = saasUrl + '?v=' + Math.random()
            + '&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID'
            + '&message=' + encodeURIComponent(JSON.stringify(data))

        var elm = document.createElement('img')
        elm.src = saasSrc
        elm.style.display = 'none'
        document.body.appendChild(elm)

        $.ajax({
            type: 'get',
            url: 'https://act.emoney.cn/activity/user/addcountbyactcode?actcode=20240325',
            dataType: 'jsonp',
            data: {
                uid: uid ? uid : uname,
                value: '1'
            },
            success: function (data) {
                if (data.code == '200') {
                    // layer.closeAll()
                    // layer.open({
                    //     type: 1,
                    //     title: false,
                    //     area: ['auto'],
                    //     skin: 'layui-layer-nobg', //没有背景色
                    //     content: $('#popwin2').html()
                    // })
                }
            }
        })

    }

    // input框输入1位数字后自动跳到下一个input聚焦
    function goNextInput (el) {
        var txts = document.querySelectorAll(el)
        for (var i = 0; i < txts.length; i++) {
            var t = txts[i]
            t.index = i
            t.setAttribute('readonly', true)
            t.onkeyup = function () {
                this.value = this.value.replace(/^(.).*$/, '$1')
                var next = this.index + 1
                if (next > txts.length - 1) return
                txts[next].removeAttribute('readonly')
                if (this.value) {
                    txts[next].focus()
                }
            }
        }
        setTimeout(function () {txts[0].focus() }, 100)
        txts[0].removeAttribute('readonly')
    }

    function rand (min, max) {
        return Math.floor(Math.random() * (max - min)) + min
    }

    function addPreZero (num) {
        if (num < 0) {
            return '000'
        } else if (num < 10) {
            return '00' + num
        } else if (num < 100) {
            return '0' + num
        } else {
            return num
        }
    }

    var fivebd = ['']

    function IsAssignUser (userid) {
        if (inArray(fivebd, userid)) {
            return true
        }
        return false
    }

    var inArray = function (arr, item) {
        for (var i = 0; i < arr.length; i++) {
            if (arr[i] == item) {
                return true
            }
        }
        return false
    }

    function IsNewMasterUser (userid) {

    }
</script>
<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs2=setInterval("SetTimeout(2024,3,29,22,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0" +i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2024/3/29 22:00:00");
    var b=new Date("2024/3/27 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs2);
        $(".t").html("00");
        $(".s").html("00");
        $(".f").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs2").show();
    }
</script>
<script type="text/javascript">document.write(unescape(
    '%3Cscript src=\'https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F\' type=\'text/javascript\'%3E%3C/script%3E'))</script>
</body>
</html>
