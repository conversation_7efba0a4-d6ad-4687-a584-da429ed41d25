<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,操盘手,抽奖,赢大奖">
    <meta name="Description" content="新一代智能高端决策系统 2024天玑升级">
    <link th:href="@{${staticPath}+'static/qyh/20241113/css/css.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/js/awardRotate.js'}"></script>
    <script type="text/javascript">
        var www = "../";
        function GetExternal () {
            return window.external.EmObj
        }

        function PC_JH (type, c) {
            try {
                var obj =
                    GetExternal()
                return obj.EmFunc(type, c)
            } catch (e) {}
        }

        (function () {

            function LoadComplete () {
                try {
                    PC_JH('EM_FUNC_DOWNLOAD_COMPLETE', '')
                } catch (ex) {}
            }

            function EM_FUNC_HIDE () {
                try {
                    PC_JH('EM_FUNC_HIDE', '')
                } catch (ex) {}
            }

            function EM_FUNC_SHOW () {
                try {
                    PC_JH('EM_FUNC_SHOW', '')
                } catch (ex) {}
            }

            function IsShow () {
                try {
                    return PC_JH('EM_FUNC_WND_ISSHOW', '')
                } catch (ex) {
                    return '0'
                }
            }

            function openWindow () {
                LoadComplete()

                PC_JH('EM_FUNC_WND_SIZE', 'w=1300,h=820,mid')

                if (IsShow() != '1') {
                    EM_FUNC_SHOW()
                }
            }

            openWindow()
        })()

    </script>
</head>
<body>
<div class="dbg1"><div class="main"><div class="t"></div></div></div>
<div class="dbg2"><div class="main">
    <div class="djs"></div><a href="###" class="btn1 dh2"></a></div></div>
<div class="dbg3">
    <div class="main">
        <div class="zp"></div>
        <a href="###" class="zz" id="rotate"></a><div class="go" id="btn-lottery"><img th:src="@{${staticPath}+'static/qyh/20241113/images/go.png'}"/></div>
        <div class="rftime">剩余抽奖次数：<span id="rft_num"></span>次</div>
        <div class="md">
            <marquee onmouseover="this.stop()" onmouseout="this.start()" scrollamount="2" direction="up" height="150px">
                <ul id="user_list">
<!--                    <li>恭喜 em****537 用户喜获 <span>智盈大师 3个月使用期</span></li>-->
<!--                    <li>恭喜 em****537 用户喜获 <span>智盈大师 3个月使用期</span></li>-->
<!--                    <li>恭喜 em****537 用户喜获 <span>智盈大师 3个月使用期</span></li>-->
                </ul>
            </marquee>
            <ul style="margin-top: 50px;height: 75px;overflow: auto;" id="my_list">
<!--                <li>恭喜 em****118 用户喜获 <span>智盈大师 3个月使用期</span></li>-->
<!--                <li>恭喜 em****118 用户喜获 <span>智盈大师 1个月使用期</span></li>-->
<!--                <li>恭喜 em****118 用户喜获 <span>智盈大师 15天使用期</span></li>-->
            </ul>
        </div>

    </div></div>
<div class="dbg4"><div class="main"><div class="hdgz2">1、10月8日至11月22日新购天玑或续费天玑的用户可获得抽取超级锦鲤的机会；<br />
    2、新购/续费1年版用户可获得1次抽奖机会，新购/续费3年版用户可获得3次抽奖机会；<br />
    3、抽奖礼品将在活动结束后的10个工作日内发送；<br />
    4、抽奖开放时间:11月12日-11月22日，限时活动，错过不再有；<br />
    5、活动最终解释权归益盟股份有限公司所有。</div></div></div>
<div class="dbg5">
</div>
<div class="dbg6"><div class="main"><div class="al1"><!-- 代码 开始 -->
    <div class="slider_name slider_box">
        <ul class="silder_con" style=" width: 7680px;">
            <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20241113/images/a1.png'}"></li>
            <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20241113/images/a2.png'}"></li>
            <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20241113/images/a3.png'}"></li>
        </ul>
    </div>
    <ul class="silder_nav">
        <li class=""></li>
        <li class=""></li>
        <li class=""></li>
    </ul>
    <!-- 代码 结束 -->
</div>
    <div class="al2"><!-- 代码 开始 -->
        <div class="slider_name2 slider_box2">
            <ul class="silder_con2" style=" width: 7680px;">
                <li class="silder_panel2"><img th:src="@{${staticPath}+'static/qyh/20241113/images/b1.png'}"></li>
                <li class="silder_panel2"><img th:src="@{${staticPath}+'static/qyh/20241113/images/b2.png'}"></li>
                <li class="silder_panel2"><img th:src="@{${staticPath}+'static/qyh/20241113/images/b3.png'}"></li>
                <li class="silder_panel2"><img th:src="@{${staticPath}+'static/qyh/20241113/images/b4.png'}"></li>
            </ul>
        </div><ul class="silder_nav2">
            <li class=""></li>
            <li class=""></li>
            <li class=""></li>
            <li class=""></li>
        </ul>
        <!-- 代码 结束 -->
    </div>
</div></div>
<div class="al3"><!-- 代码 开始 -->
    <div class="slider_name3 slider_box3">
        <ul class="silder_con3" style=" width: 7680px;">
            <li class="silder_panel3"><img th:src="@{${staticPath}+'static/qyh/20241113/images/c1.png'}"></li>
            <li class="silder_panel3"><img th:src="@{${staticPath}+'static/qyh/20241113/images/c2.png'}"></li>
            <li class="silder_panel3"><img th:src="@{${staticPath}+'static/qyh/20241113/images/c3.png'}"></li>
            <li class="silder_panel3"><img th:src="@{${staticPath}+'static/qyh/20241113/images/c4.png'}"></li>
            <li class="silder_panel3"><img th:src="@{${staticPath}+'static/qyh/20241113/images/c5.png'}"></li>
            <li class="silder_panel3"><img th:src="@{${staticPath}+'static/qyh/20241113/images/c6.png'}"></li>
            <li class="silder_panel3"><img th:src="@{${staticPath}+'static/qyh/20241113/images/c7.png'}"></li>
        </ul>
    </div><ul class="silder_nav3">
        <li class=""></li>
        <li class=""></li>
        <li class=""></li>
        <li class=""></li>
        <li class=""></li>
        <li class=""></li>
        <li class=""></li>
    </ul>
    <!-- 代码 结束 -->
</div>
<div class="footer">欢迎登录益盟官方网站www.emoney.cn<br />
    本活动最终解释权归上海益盟软件技术股份有限公司　沪ICP备06000340<br />
    软件需数据支持，仅提供辅助建议，风险需谨慎；投资顾问意见仅作参考。</div>
<div class="pf">
    <div class="main"><a href="###" class="btn2 dh2"></a></div></div>
<div class="bg" style="display: none">
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc">
        <a href="###" class="close"></a>
        <div class="tc_1">恭喜您获得<br />
            <span class="red">3个月</span>智盈使用期</div>
    </div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241113'}"></script>
<script th:src="@{${staticPath}+'static/qyh/20241113/js/main.js?r=20241114'}"></script>
<script>
    function SetTimeout(year, month, day, hour, minute, second, targetElement) {
        var leftTime = (new Date(year, month - 1, day, hour, minute, second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数

        $(targetElement).find(".t").html(days);
    }

    function checkTime(i) { //将0-9的数字前面加上0，例1变为01
        return i;
    }
    main=setInterval(function() { SetTimeout(2024, 11,24, 00, 0, 0, ".main"); }, 1000);

    var classdate = new Date();
    var a=new Date("2024/11/24 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(main);
        $(".t").html("0");
    }
</script>
<script type="text/javascript" th:src="@{${staticPath}+'static/qyh/20241113/js/jquery.slides.js'}"></script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "qyh_20241113";//模块名称(焦点图2)
    var Remark = "天玑抢优惠";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>