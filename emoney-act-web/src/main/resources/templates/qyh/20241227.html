<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="稳增长 新经济 新机遇 跟主力 盯主线 决战大波段 智盈大师掘金版--对的时间做对的选择">
    <link th:href="@{${staticPath}+'static/qyh/20241227/style/common.css?r=20241227'}" rel="stylesheet" type="text/css" />
    <link th:href="@{${staticPath}+'static/qyh/20241227/style/popup.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qyh/20231205/js/json2.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qgn/20231011/js/popup.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var www = "../";
        function GetExternal () {
            return window.external.EmObj
        }

        function PC_JH (type, c) {
            try {
                var obj =
                    GetExternal()
                return obj.EmFunc(type, c)
            } catch (e) {}
        }

        (function () {

            function LoadComplete () {
                try {
                    PC_JH('EM_FUNC_DOWNLOAD_COMPLETE', '')
                } catch (ex) {}
            }

            function EM_FUNC_HIDE () {
                try {
                    PC_JH('EM_FUNC_HIDE', '')
                } catch (ex) {}
            }

            function EM_FUNC_SHOW () {
                try {
                    PC_JH('EM_FUNC_SHOW', '')
                } catch (ex) {}
            }

            function IsShow () {
                try {
                    return PC_JH('EM_FUNC_WND_ISSHOW', '')
                } catch (ex) {
                    return '0'
                }
            }

            function openWindow () {
                LoadComplete()

                PC_JH('EM_FUNC_WND_SIZE', 'w=1300,h=820,mid')
                PC_JH('EM_FUNC_SET_STYLE', '/caption=')
                PC_JH('EM_FUNC_SET_TITLE', '')
                if (IsShow() != '1') {
                    EM_FUNC_SHOW()
                }
            }

            openWindow()
        })()

    </script>
</head>
<body>
<div class="bg9">
    <div class="sppf">
        <div class="main"><a href="javascript:void(0)" class="sppfbtn"></a></div>
    </div>
    <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=109e4cd4c2c14a4e96b1fee9366d8490&rep=1&py=0"
            scrolling="no" class="sp"></iframe>
    <a href="javascript:void(0)" class="sppfbtn2"></a></div>
<div class="bod">
    <div class="dbg1"><div class="main"><div class="ico"></div></div></div>
    <div class="dbg2"><div class="main"><div class="djs2"><span class="t">0</span><span class="s">0</span><span class="f">0</span><span class="m">0</span></div><a href="javascript:void(0)" class="btn1 dh" clickkey="20240910Act" clickdata="btn1"></a></div></div>
    <div class="dbg3">

    </div>
    <div class="dbg4"><div class="main">
        <a href="javascript:void(0)" class="btn2 dh"></a>
        <div id="a1" style="position: absolute; top: -40px"></div>
        <div id="k"></div>
        <script type="text/javascript">
            function bh () {
                var i = document.getElementById('k')
                i.style.display = 'block'
                setTimeout('clock()', 1300)
            }

            function clock () {
                var i = document.getElementById('k')
                i.style.display = 'none'
            }

        </script>
    </div></div>
    <div class="dbg5">
        <div class="bg bgd" id="al2">
            <ul id="yd3b">
                <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=7a2748e688924bbf8c6a64a1ade9936c&rep=1&py=1" target="al3">
                    <div class="f1">【锅底右侧】9月26日出击</div>
                    <div class="f3">三美股份  4个交易日 涨<span class="f2">22.64%</span></div>
                </a>
                <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=b2c58582569a4ee4b7e3a0f8cee73ca9&rep=1&py=1" target="al3">
                    <div class="f1">【蹦极新生】11月4日出击</div>
                    <div class="f3">筑博设计  15个交易日 涨<span class="f2">46.23%</span></div>
                </a>
                <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=bafea268ac054e3c99d49a2da6ac73ec&rep=1&py=1" target="al3">
                    <div class="f1">【黄金回踩】11月4日出击</div>
                    <div class="f3">凯龙股份 8个交易日 涨<span class="f2">23.49%</span></div>
                </a>
                <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=b7245bbca6274b6aab3ef0e2f514909e&rep=1&py=1" target="al3">
                    <div class="f1">【冲量变速】10月18日出击</div>
                    <div class="f3">联创光电 14个交易日 涨<span class="f2">118.30%</span></div>
                </a>
                <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a559b4dab56f4a10a8fe6cbc0cf0f104&rep=1&py=1" target="al3">
                    <div class="f1">【龙腾长波】11月8日出击</div>
                    <div class="f3">真爱美家 12个交易日 涨<span class="f2">40.05%</span></div>
                </a>
            </ul>
            <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al3"></iframe>
            <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240910Act" clickdata="btn10"></a> </div>
        </div>
        <div class="bg bgc" id="al3">
            <ul id="yd2b">
                <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=abc90df78f404d9cb02e7ddcbd10a5ba&rep=1&py=1" target="al2">
                    <div class="f1">【伏击活跃】10月11日出击</div>
                    <div class="f3">华立股份 15个交易日 涨<span class="f2">295.52%</span></div>
                </a>
                <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=f9389c8110c645bb8dd68247b95b8718&rep=1&py=1" target="al2">
                    <div class="f1">【资金金叉】11月4日出击</div>
                    <div class="f3">拓斯达  14个交易日 涨<span class="f2">162.52%</span></div>
                </a>
                <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=d2fa5caeffcb4fa5b2a93b807284367f&rep=1&py=1" target="al2">
                    <div class="f1">【量王叠现】11月4日出击</div>
                    <div class="f3">中科星图 6个交易日 涨<span class="f2">81.42%</span></div>
                </a>
                <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=6da0f00091844b45ac26a4c9c931b118&rep=1&py=1" target="al2">
                    <div class="f1">【大阳起势】10月25日出击</div>
                    <div class="f3">时创能源 13个交易日 涨<span class="f2">91.42%</span></div>
                </a><a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=5fa8ec537ae6420a88fe0010cb57e2c3&rep=1&py=1" target="al2">
                <div class="f1" style="font-size: 15px">【纵横趋势-突破K】11月4日锁定</div>
                <div class="f3">天汽模 7个交易日 涨<span class="f2">77.17%</span></div>
            </a>
            </ul>
            <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al2"></iframe>
            <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240910Act" clickdata="btn10"></a> </div>
        </div>
        <div class="bg" id="al1">
            <ul id="yd1b">
                <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=84bbe2de01204369bb88fb5299ae7bc7&rep=1&py=1" target="al1">
                    <div class="f1">【黄金坑】2月6日锁定</div>
                    <div class="f3">科德教育  29个交易日 涨<span class="f2">72.74%</span></div>
                </a><a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=03e02fe31eec451dae8ef9a259f84940&rep=1&py=1" target="al1">
                <div class="f1">【冰谷+潜龙】9月24日出击</div>
                <div class="f3">兴齐眼药 6个交易日 涨<span class="f2">87.92%</span></div>
            </a>
                <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=d628191b73a741aebd53de070b897534&rep=1&py=1" target="al1">
                    <div class="f1">【价格冲击波】9月18日锁定</div>
                    <div class="f3">彩讯股份 10个交易日 涨<span class="f2">72.59%</span></div>
                </a>
                <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=b672f051402242db835025e07edf64e5&rep=1&py=1" target="al1">
                    <div class="f1">【纵横趋势-底部K】9月3日锁定</div>
                    <div class="f3">银信科技 19个交易日 涨<span class="f2">94.78%</span></div>
                </a>
                <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=785530eced454bd58563b1f8c18315d6&rep=1&py=1" target="al1">
                    <div class="f1">【高星掘金】8月29日锁定</div>
                    <div class="f3">盛弘股份 22个交易日 涨<span class="f2">70.63%</span></div>
                </a>
            </ul>
            <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1"
                    name="al1"></iframe>
            <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240910Act" clickdata="btn10"></a> </div>
        </div>
        <div class="bg10"></div>
        <div class="bg bge">
            <ul>
            </ul>
            <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al4"></iframe>
            <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240910Act" clickdata="btn10"></a> </div>
        </div>
        <div class="bg bgb">
            <ul>
            </ul>
            <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al4b"></iframe>
            <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240910Act" clickdata="btn10"></a> </div>
        </div>
        <div class="bg bgg">
            <ul>

            </ul>
            <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al6"></iframe>
            <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240910Act" clickdata="btn10"></a> </div>
        </div>
    </div>
    <div class="dbg6"></div>
    <div class="dbg7"></div>
    <div id="dbg8"></div>
    <div id="dbg9"></div>
    <div id="dbg10">
        <div class="main">
            <div id="div1">
                <ul>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240910/images/zs10.jpg'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240910/images/zs1.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240910/images/zs2.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240910/images/zs3.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240910/images/zs4.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240910/images/zs5.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240910/images/zs6.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240910/images/zs7.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240910/images/zs8.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240910/images/zs9.png'}"></li>
                </ul>
                <a href="javascript:;" style="left:10px;"></a> <a href="javascript:;" style="right:10px;"></a></div>
            <script type="text/javascript">
                window.onload = function () {
                    var oDiv = document.getElementById('div1')
                    var oUl = oDiv.getElementsByTagName('ul')[0]
                    var aLi = oUl.getElementsByTagName('li')
                    var aA = oDiv.getElementsByTagName('a')
                    var iSpeed = 1//正左负右
                    var timer = null
                    //计算ul的宽为所有li的宽的和;
                    oUl.innerHTML += oUl.innerHTML + oUl.innerHTML
                    oUl.style.width = aLi[0].offsetWidth * aLi.length + 'px'

                    function Slider () {
                        if (oUl.offsetLeft < -oUl.offsetWidth / 2) {
                            oUl.style.left = 0
                        } else if (oUl.offsetLeft > 0) {
                            oUl.style.left = -oUl.offsetWidth / 2 + 'px'
                        }
                        oUl.style.left = oUl.offsetLeft - iSpeed + 'px'//正负为方向
                    }

                    timer = setInterval(Slider, 30)
                    aA[0].onclick = function () {
                        iSpeed = 1 //控制速度的正负
                    }
                    aA[1].onclick = function () {
                        iSpeed = -1
                    }
                    oDiv.onmouseover = function () {
                        clearInterval(timer)
                    }
                    oDiv.onmouseout = function () {
                        timer = setInterval(Slider, 30)
                    }
                }
            </script>
        </div>
    </div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br/>
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340 </div>
<div class="pf pf1"><a href="javascript:void(0)" class="close pfx"></a><img th:src="@{${staticPath}+'static/qyh/20240910/images/pf.png'}" alt="" usemap="#Map" border="0">
    <map name="Map" id="Map">
        <area shape="rect" coords="-1,91,204,122" href="#al1" class="s1"/>
        <area shape="rect" coords="2,130,209,159" href="#al2" class="s2"/>
        <area shape="rect" coords="-1,168,207,199" href="#al3" class="s3"/>
    </map>
</div>
<div class="pf2">
    <div class="main"><a href="javascript:void(0)" class="btn9 dh" clickkey="20240910Act" clickdata="btn10"></a></div>
</div>
<a href="javascript:void(0)" class="wddd">我的订单</a>
<div class="h" id="ddcx">
    <div class="wddd2 orderlist"><a href="javascript:void(0)" class="close hdgzx"></a>
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tbody id="tbody">
            <tr>
                <td class="bt">订单号</td>
                <td class="bt">订单类型</td>
                <td class="bt">支付时间</td>
                <td class="bt">支付金额</td>
            </tr>
            <!--<tr>
              <td>202306200011</td>
              <td>订金</td>
              <td>2023.6.20</td>
              <td>¥1000</td>
            </tr>-->
            </tbody>
        </table>
    </div>
</div>
<div id="popwin1" style="display:none;">
    <div class="tc0" id="divCheck" style="display:block;">
        <div class="font1"><span id="checkcode">8888</span></div>
        <div class="t1">
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
        </div>
        <div class="clear"></div>
        <div class="btn_tc0"><a href="javascript:void(0)" id="btn_submit"></a></div>
    </div>
</div>
<div id="popwin2" style="display:none;">
    <div class="tc1b" id="divSuc" style="display:block;"> <a href="javascript:void(0)" class="tc3-btn dh"></a> <a href="javascript:void(0)" class="tc4-btn dh"></a> </div>
</div>
<div id="popwin3" style="display:none;">
    <div class="tc1" id="divCheckMobile" style="display:block;">
        <div style="font-size: 30px; color: #000; padding-top: 110px;">请在软件端登录参与活动</div>
        <!--<div class="font3">智盈大师（掘金版）<br/>
                <span id="showtype"></span>年版套餐预定
            </div>
            <div class="t2">
                <input type="text" id="txt_mobile" class="srk" placeholder="请输入您的订购手机号"/>
                <a id="btnsend" href="javascript:void(0)" class="yzm">获取验证码</a>
                <div class="clear"></div>
            </div>
            <div class="t2">
                <input type="text" id="txt_code" class="srk" placeholder="请输入验证码"/>
                <a href="javascript:void(0)" class="btn_tc2">点击确定</a>
                <div class="clear"></div>
            </div>-->
    </div>
</div>
<div class="h" style="display: none" id="logindiv">
    <div class="logintc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>

<script>
    var payCmpCode = "FUNC20241218150683";//支付推送cmpcode
    var uid = $("#hid_uid").val();
    var pid = $("#hid_pid").val();
    var mobileX = $("#hid_mobilex").val();
    var staticPath = $("#hid_staticPath").val();
    var timer = null
    var OrderPaymentItemPayTypeEnum = {
        normal: 0, prePay: 1, finalPaid: 2
    }
    var UserPayStatusIdEnum = {
        noPay: 0, prePaid: 1, finalPaid: 200, reimburse: 201
    }
    /**
     *  "payStatusId":支付状态(0:等待支付,1:支付中,101:支付失败,200:支付成功,201:已退款)
     */
    var PayStatusIdEnum = {
        waiting: 0, paying: 1, fail: 101, success: 200, reimburse: 201
    }
    //上一期抢优惠
    var acCode_pre = 'ac-1241204133231715'
    var acCode = 'ac-124121810065425'
    //付了尾款
    var isFinalPaid = false
    //付了尾款
    var isPaid = false
    //订单
    var orderList = []
    var timer = null
    var phoneEncrypt = getQueryString('phoneEncrypt')
    var UPcid = getQueryString('UPcid')
    var wz=getQueryString("wz");
    var downflag = '0'

    $(document).ready(function () {
        if($("#hid_isLogin").val() == "1") {
            getPayStatus()
            bindEvents();
        }else{
            //弹出登录窗口
            $("#logindiv").show();
            $(".logintc").show();
        }

    })
    //点击事件
    function bindEvents() {
        $('.btn10,.btn9').click(function () {
            document.querySelector('#a1').scrollIntoView({ behavior: 'smooth' })
            bh()
        })
        $('.pfx').click(function () {
            $('.pf').hide()
            downflag = '1'
        })

        //查询订单列表
        $('.wddd').click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            getPayList(uid)
        })
        //抢团购
        $('.btn1,.btn2').click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            if (isFinalPaid) {
                //定金已经付了
                openPopup(function () {
                    $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20241227/images/tc4.png')")
                    $('.tc3-btn').hide()
                    $('.tc4-btn').show()
                })

            } else if (isPaid) {
                //尾款已经付了
                openPopup(function () {
                    $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20241227/images/tc5.png')")
                    $('.tc3-btn').hide()
                    $('.tc4-btn').hide()
                })
            } else {
                PushDataToCMP(payCmpCode, uid, "");
                openPopup(function () {
                    $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20241227/images/tc1.png')")
                    $('.tc3-btn').show()
                    $('.tc4-btn').hide()
                })
            }
        })

        //抢套餐
        $(document).on('click', '#btn_submit', function () {
            var code = ''
            var checkcode = $('#checkcode').html()
            var txt = $('.text1') // 获取所有文本框
            for (var i = 0; i < txt.length; i++) {
                code += txt.eq(i).val()
            }
            if (code == '') {
                layer.msg('请输入您专属的验证码')
                return false
            }
            if (code != checkcode) {
                layer.msg('您的验证码输入有误，请确认后输入')
                return false
            }

            var accode = $(this).attr('data-accode')
            var type = $(this).attr('data-type')

            PushDataToCMP(accode, uid, '', type)

            openPopup(function () {
                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20241227/images/tc3.png')")
                $('.tc3-btn').show()
                $('.tc4-btn').hide()
            })
        })

        //去支付
        $(document).on('click', '.tc3-btn,.tc4-btn', function () {
            var payurl = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&resourcestypeid=234&resourcesid=1244376&businessType=biztyp-szds";

            if (!!GetExternal()) {
                PC_JH('EM_FUNC_OPEN_LIVE_VIDEO', '15,' + payurl)
            } else {
                //0x 密文手机 + uid  拼在payurl
                payurl += "&phoneEncrypt=" + mobileX + "&UPcid=" + uid;
                $(this).attr("target", "_blank");
                $(this).attr("href", payurl);
            }

            pushdatatocmp(uid,payCmpCode);
            if (!isFinalPaid && !isPaid) {
                if (timer) clearInterval(timer)

                timer = setInterval(function () {
                    getPayStatus(function () {
                        if (isFinalPaid) {
                            //定金已经付了
                            openPopup(function () {
                                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20241227/images/tc4.png')")
                                $('.tc3-btn').hide()
                                $('.tc4-btn').show()
                            })
                            clearInterval(timer)

                        } else if (isPaid) {
                            //尾款已经付了
                            openPopup(function () {

                                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20241227/images/tc5.png')")
                                $('.tc3-btn').hide()
                                $('.tc4-btn').hide()
                            })
                            clearInterval(timer)
                            if (!timer) {
                                closePopup()
                            }
                        }
                    })

                }, 9000)
            }
            if (isFinalPaid) {
                layer.closeAll()
            }
        })

        $('.bg a').click(function () {
            $(this).addClass('current').siblings('a').removeClass('current');
        })
        $('.bgi a').mouseenter(function () {
            $(this).addClass('current').siblings('a').removeClass('current');
        })
        $('#s1').mouseenter(function () {
            $("#s4").attr('src',staticPath + "static/qyh/20241227/images/a1.png");
        })
        $('#s2').mouseenter(function () {
            $("#s4").attr('src',staticPath + "static/qyh/20241227/images/a2.png");
        })
        $('#s3').mouseenter(function () {
            $("#s4").attr('src',staticPath + "static/qyh/20241227/images/a3.png");
        })

        $('#yd1').click(function () {$('#yd1b a').removeClass('current')})
        $('#yd2').click(function () {$('#yd2b a').removeClass('current')})
        $('#yd3').click(function () {$('#yd3b a').removeClass('current')})
        $('#yd4').click(function () {$('#yd4b a').removeClass('current')})
        $('.nav a').click(function () {
            $(this).addClass('current').siblings('a').removeClass('current')
        })

        $('.a1').click(function () {
            $('.pc').show()
            $('.sj').hide()
            $('.s1').attr('href', '#al1')
            $('.s2').attr('href', '#al2')
            $('.s3').attr('href', '#al3')
            $('.s4').attr('href', '#al4')
            $('.s5').attr('href', '#al5')
            $('.s6').attr('href', '#al6')
            $('.s7').attr('href', '#al7')
        })
        $('.a2').click(function () {
            $('.pc').hide()
            $('.sj').show()
            $('.s1').attr('href', '#al1b')
            $('.s2').attr('href', '#al2b')
            $('.s3').attr('href', '#al3b')
            $('.s4').attr('href', '#al4b')
            $('.s5').attr('href', '#al5b')
            $('.s6').attr('href', '#al6b')
            $('.s7').attr('href', '#al7b')
        })

        $('.sppfbtn').click(function () {
            $('.bg9').animate({
                height: '500px'
            })
            $('.sppfbtn2').show(600)
        })
        $('.sppfbtn2').click(function () {
            $('.bg9').animate({
                height: '80px'
            })
            $('.sppfbtn2').hide(600)
            $('.sp').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a27ccbb26e454f8fab41f2bfdd18dac9&rep=1&py=0')
        })

        $('.hdgzk').click(function () {$('#hdgz').show()})
        $('.hdgzx').click(function () {$('.h').hide()})
    }

    //获取定金尾款订单列表
    function getPayList () {
        $('#tbody tr').eq(0).siblings().remove()
        if (orderList.length) {
            var list = orderList

            function computedOrderTypeName (orderPayments) {
                let result = ''
                if (orderPayments && orderPayments.length) {
                    orderPayments.forEach(i => {
                        if (i.payStatusId === PayStatusIdEnum.success && i.payType === OrderPaymentItemPayTypeEnum.prePay) {
                        result += '订金'
                    }
                    if (i.payStatusId === PayStatusIdEnum.success && i.payType === OrderPaymentItemPayTypeEnum.finalPaid) {
                        result += '+尾款'
                    }
                })
                }
                return result
            }

            if (!!list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    var item = list[i]

                    var trhtml = document.createElement('tr')
                    trhtml.id = 'mDiv'
                    trhtml.innerHTML = '<td>' + item.orderNumber + '</td><td>' + computedOrderTypeName(item.orderPayments) +
                        '</td><td>' +
                        timestampToTime(item.createTime) + '</td><td>￥' + item.payTotal + '</td>'

                    $('#tbody').append(trhtml)
                }
            } else {
                var trhtml = document.createElement('tr')
                trhtml.id = 'mDiv'
                trhtml.innerHTML = '<td colspan=\'4\'>您好，当前未查询到任何有效订单</td>'

                $('#tbody').append(trhtml)
            }

            $('#ddcx').show()
        } else {
            var trhtml = document.createElement('tr')
            trhtml.id = 'mDiv'
            trhtml.innerHTML = '<td colspan=\'4\'>您好，当前未查询到任何有效订单</td>'

            $('#tbody').append(trhtml)
        }

        $('#ddcx').show()
    }

    //获取支付状态
    function getPayStatus (cb) {
        var mobileX = $("#hid_mobilex").val();
        $.ajax({
            url: "https://emapp.emoney.cn/buy/order/QueryOrderList?Emapp-Format=EmappJsonp",
            timeout: 5000,
            type: 'get',
            dataType: 'jsonp',
            cache: false,
            data: {"phoneEncrypt": mobileX, "type": 0},
            success: function (data) {
                if (data.result.code === 0) {
                    var arr = data.detail
                    var status = ''
                    orderList = []
                    arr.forEach(item => {
                        if (item && item.orderLogistice && item.orderLogistice.length) {
                        item.orderLogistice.forEach(i => {
                            if (i.activityCode === acCode_pre &&
                                [UserPayStatusIdEnum.finalPaid, UserPayStatusIdEnum.prePaid].includes(item.payStatusId)) {
                                //定金已付
                                isFinalPaid = item.payStatusId === UserPayStatusIdEnum.prePaid
                                //尾款已付
                                isPaid = item.payStatusId === UserPayStatusIdEnum.finalPaid

                                orderList.push(item)
                            }

                            if (i.activityCode === acCode &&
                                [UserPayStatusIdEnum.finalPaid, UserPayStatusIdEnum.prePaid].includes(item.payStatusId)) {
                            //定金已付
                            isFinalPaid = item.payStatusId === UserPayStatusIdEnum.prePaid
                            //尾款已付
                            isPaid = item.payStatusId === UserPayStatusIdEnum.finalPaid

                            orderList.push(item)
                        }
                    })
                    }
                })

                    cb && cb()
                }
            }
        })
    }

    function PushDataToCMP (accode, uid, uname) {
        var data = {
            'appid': '10088',
            'logtype': 'click',
            'mid': '',
            'pid': getQueryString('pid'),
            'sid': getQueryString('sid'),
            'tid': getQueryString('tid'),
            'uid': uid,
            'uname': uname,
            'adcode': accode,
            'targeturl': '',
            'pageurl': window.top.location.href
        }
        var saasUrl = 'http://ds.emoney.cn/saas/queuepush'
        var saasSrc = saasUrl + '?v=' + Math.random()
            + '&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID'
            + '&message=' + encodeURIComponent(JSON.stringify(data))

        var elm = document.createElement('img')
        elm.src = saasSrc
        elm.style.display = 'none'
        document.body.appendChild(elm)
    }
</script>
<script type="text/javascript">
    //打开弹窗
    function openPopup (cb) {
        layer.close(layer.index)
        layer.open({
            type: 1,
            title: false,
            //closeBtn: 0,
            area: ['auto'],
            //shadeClose: true,
            skin: 'layui-layer-nobg', //没有背景色
            content: $('#popwin2').html(),
            success: function (layero, index) {
                cb()
            },
            end: function () { }
        })
    }

    // 处理时间戳
    /* 时间戳转换为时间 */
    function timestampToTime (timestamp) {
        timestamp = timestamp ? timestamp : null
        var date = new Date(timestamp)//时间戳为10位需*1000，时间戳为13位的话不需乘1000
        var Y = date.getFullYear() + '-'
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
        var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
        var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
        var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
        var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
        return Y + M + D + h + m + s
    }

    // input框输入1位数字后自动跳到下一个input聚焦
    function goNextInput (el) {
        var txts = document.querySelectorAll(el)
        for (var i = 0; i < txts.length; i++) {
            var t = txts[i]
            t.index = i
            t.setAttribute('readonly', true)
            t.onkeyup = function () {
                this.value = this.value.replace(/^(.).*$/, '$1')
                var next = this.index + 1
                if (next > txts.length - 1) return
                txts[next].removeAttribute('readonly')
                if (this.value) {
                    txts[next].focus()
                }
            }
        }
        setTimeout(function () {txts[0].focus() }, 100)
        txts[0].removeAttribute('readonly')
    }

    function rand (min, max) {
        return Math.floor(Math.random() * (max - min)) + min
    }

    function getQueryString (name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
        var r = window.location.search.substr(1).match(reg)
        if (r != null) return unescape(r[2])
        return null
    }

    function setCookie (name, value) {
        var expdate = new Date()
        expdate.setTime(expdate.getTime() + 30 * 60 * 1000)
        document.cookie = name + '=' + value + ';expires=' + expdate.toGMTString() + ';path=/'
    }

    function getCookie (c_name) {
        if (document.cookie.length > 0) {
            c_start = document.cookie.indexOf(c_name + '=')
            if (c_start != -1) {
                c_start = c_start + c_name.length + 1
                c_end = document.cookie.indexOf(';', c_start)
                if (c_end == -1) c_end = document.cookie.length
                return unescape(document.cookie.substring(c_start, c_end))
            }
        }
        return ''
    }

    function checkPermission (pid) {
        // if (pid != "888010000" && pid != "888010400") {
        //     layer.msg("本活动仅限小智盈用户参与");
        //     return false;
        // }
        return true;
    }
</script>
<script type="text/javascript">
    var al1 = '0'
    var al2 = '0'
    var al3 = '0'
    var al4 = '0'
    var al4b = '0'
    var al5 = '0'
    var al6 = '0'
    var al7 = '0'
    var al8 = '0'

    $(window).scroll(function (e) {
        if (($(window).scrollTop() >= 1440) && (downflag == '0')) {
            $('.pf').fadeIn(300)
        }
        if ($(window).scrollTop() >= 1440) {
            $('.pf2').fadeIn(300)
        }else {
            $('.pf,.pf2').fadeOut(300)
        }
        if (($(window).scrollTop() > 2520) && (al1 == '0')) {
            $('[name=\'al1\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=84bbe2de01204369bb88fb5299ae7bc7&rep=1&py=0')

            al1 = '1'
        }
        if (($(window).scrollTop() > 1900) && (al2 == '0')) {
            $('[name=\'al2\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=abc90df78f404d9cb02e7ddcbd10a5ba&rep=1&py=0')

            al2 = '1'
        }
        if (($(window).scrollTop() > 1306) && (al3 == '0')) {
            $('[name=\'al3\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=7a2748e688924bbf8c6a64a1ade9936c&rep=1&py=0')

            al3 = '1'
        }
        if (($(window).scrollTop() > 3240) && (al4 == '0')) {
            $('[name=\'al4\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=1847c260b7004da99240dd6ee80664ad&rep=1&py=0')

            al4 = '1'
        }
        if (($(window).scrollTop() > 3870) && (al4b == '0')) {
            $('[name=\'al4b\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=e2d7a035eb82450cbd3bdd45492d43e3&rep=1&py=0')

            al4b = '1'
        }
        if (($(window).scrollTop() > 4570) && (al5 == '0')) {
            $('[name=\'al5\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=933d8932725d4b23bff129b9ba537955&rep=1&py=0')

            al5 = '1'
        }
        if (($(window).scrollTop() > 5270) && (al6 == '0')) {
            $('[name=\'al6\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=de913aec3d2d4be7992eb44e2fc60802&rep=1&py=0')

            al6 = '1'
        }
    })

    var videoList = $('iframe')
    var wHeigt = window.innerHeight
    document.addEventListener('scroll', function () {
        var isPlay = false
        //滚动条高度+视窗高度 = 可见区域底部高度
        var visibleBottom = window.scrollY + document.documentElement.clientHeight
        //可见区域顶部高度
        var visibleTop = window.scrollY
        for (var i = 0; i < videoList.length; i++) {
            var centerY = $(videoList[i]).offset().top + (videoList[i].offsetHeight / 2)
            if (centerY > visibleTop && centerY < visibleBottom) {
                if (!isPlay) {
                    videoList[i].src.match(/py=0/) && (videoList[i].src = videoList[i].src.replace(/py=0/, 'py=1'))
                    isPlay = true
                } else {
                    videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
                }
            } else {
                videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
            }
        }
    })
</script>

<script>
    function SetTimeout(year, month, day, hour, minute, second, targetElement) {
        var leftTime = (new Date(year, month - 1, day, hour, minute, second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10); //计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10); //计算剩余的秒数


        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(targetElement).find(".t").html(days);
        $(targetElement).find(".s").html(hours);
        $(targetElement).find(".f").html(minutes);
        $(targetElement).find(".m").html(seconds);
    }

    function checkTime(i) { //将0-9的数字前面加上0，例1变为01
        if (i < 10) {
            i = i;
        }
        return i;
    }
    djs2=setInterval(function() { SetTimeout(2024, 12, 31, 18, 0, 0, ".djs2"); }, 1000);

    var classdate = new Date();
    var a=new Date("2024/12/31 18:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs2);
        $(".t").html("0");
        $(".s").html("0");
        $(".f").html("0");
        $(".m").html("0");
    }
</script>
<script type="text/javascript">document.write(unescape(
    '%3Cscript src=\'https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F\' type=\'text/javascript\'%3E%3C/script%3E'))</script>
</body>
</html>
