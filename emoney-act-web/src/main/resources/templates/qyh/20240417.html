<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="稳增长 新经济 新机遇 跟主力 盯主线 决战大波段 智盈大师掘金版--对的时间做对的选择">
    <link th:href="@{${staticPath}+'static/qyh/20240417/style/common.css?r=20240327'}" rel="stylesheet" type="text/css" />
    <link th:href="@{${staticPath}+'static/qyh/20240417/style/popup.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qyh/20231205/js/json2.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qgn/20231011/js/popup.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var www="../";
        function GetExternal () {
            return window.external.EmObj
        }

        function PC_JH (type, c) {
            try {
                var obj =
                    GetExternal()
                return obj.EmFunc(type, c)
            } catch (e) {}
        }

        (function () {

            function LoadComplete () {
                try {
                    PC_JH('EM_FUNC_DOWNLOAD_COMPLETE', '')
                } catch (ex) {}
            }

            function EM_FUNC_HIDE () {
                try {
                    PC_JH('EM_FUNC_HIDE', '')
                } catch (ex) {}
            }

            function EM_FUNC_SHOW () {
                try {
                    PC_JH('EM_FUNC_SHOW', '')
                } catch (ex) {}
            }

            function IsShow () {
                try {
                    return PC_JH('EM_FUNC_WND_ISSHOW', '')
                } catch (ex) {
                    return '0'
                }
            }

            function openWindow () {
                LoadComplete()

                PC_JH('EM_FUNC_WND_SIZE', 'w=1300,h=820,mid')

                if (IsShow() != '1') {
                    EM_FUNC_SHOW()
                }
            }

            openWindow()
        })()

    </script>
</head>
<body>
<div class="bod">
    <div class="dbg1">
        <div class="main">
            <div class="djs2">
                <div class="t">00</div>
                <div class="s">00</div>
                <div class="f">00</div>
            </div>
        </div>
    </div>
    <div class="dbg2"></div>
    <div class="dbg3">
        <div class="main"><a href="javascript:void(0)" class="btn1 dh" clickkey="20230222Act" clickdata="btn1"></a>
            <div class="txt1">已有<span name="usedCount"></span>人申请</div>
            <!--<a href="#al2" class="btn7"></a><a href="#al3" class="btn8"></a><a href="#al4" class="btn9"></a>-->
            <div class="djs"></div>
        </div>
    </div>
    <div class="dbg4"></div>
    <div id="dbg5">
        <div class="main"><a href="javascript:void(0)" class="btn2 an1 dh" clickkey="20230222Act" clickdata="btn2"></a> </div>
    </div>
    <div class="dbg7">
        <div class="main">
            <div id="a1" style="position: absolute; top: 200px"></div>
            <div class="ico2 dh2"><a href="javascript:void(0)" class="btn4 dh"></a></div>
            <div class="ico3 dh2"><a href="javascript:void(0)" class="btn6 dh">

            </a></div>
            <div id="k"></div>
            <script type="text/javascript">
                function bh () {
                    var i = document.getElementById('k')
                    i.style.display = 'block'
                    setTimeout('clock()', 1300)
                }

                function clock () {
                    var i = document.getElementById('k')
                    i.style.display = 'none'
                }

            </script>
        </div>
    </div>
    <div class="dbg6">
        <div class="main"><a href="javascript:void(0)" class="btn2 an2 dh" clickkey="20230222Act" clickdata="btn2"></a> </div>
    </div>
    <div id="dbg8a">
        <div class="bg" style="padding-top: 325px;">
            <ul>
                <a class="current" th:href="@{${staticPath}+'static/qyh/20240417/images/01.png'}" target="al1">
                    <div class="f1">纵横趋势 </div>
                    <div class="f3">横看蓄势，纵看突破，区间波段</div>
                </a> <a th:href="@{${staticPath}+'static/qyh/20240417/images/02.png'}" target="al1">
                <div class="f1">价格冲击波</div>
                <div class="f3">最大值等突破，最小值等反转</div>
            </a>
            </ul>
            <iframe allowfullscreen="true" frameborder="0" th:src="@{${staticPath}+'static/qyh/20240417/images/01.png'}" scrolling="no" class="swf1" name="al1"></iframe>
        </div>
    </div>
    <div id="dbg8">
        <div class="bg" style="padding-top: 105px;">
            <ul>
                <a class="current" th:href="@{${staticPath}+'static/qyh/20240417/images/a1.png'}" target="al3">
                    <div class="f1">强弱乾坤</div>
                    <div class="f3">强识别强周期，增强概率</div>
                </a> <a th:href="@{${staticPath}+'static/qyh/20240417/images/a2.png'}" target="al3">
                <div class="f1">三线强势</div>
                <div class="f3">多周期共振，锁定超强板块</div>
            </a> <a th:href="@{${staticPath}+'static/qyh/20240417/images/a3.png'}" target="al3">
                <div class="f1">强龙起势</div>
                <div class="f3">洞察强势股启动关键，踩准时机</div>
            </a>
            </ul>
            <iframe allowfullscreen="true" frameborder="0" th:src="@{${staticPath}+'static/qyh/20240417/images/a1.png'}" scrolling="no" class="swf1" name="al3"></iframe>
        </div>
    </div>
    <div id="dbg9">
        <div class="main"><a href="javascript:void(0)" class="btn2 an3 dh" clickkey="20230222Act" clickdata="btn2"></a></div>
        <div class="bg" style="padding-top: 82px;">
            <ul>
                <a class="current" th:href="@{${staticPath}+'static/qyh/20240417/images/b1.png'}" target="al2">
                    <div class="f1">黄金三买</div>
                    <div class="f3">出击三类黄金买点</div>
                </a> <a th:href="@{${staticPath}+'static/qyh/20240417/images/b2.png'}" target="al2">
                <div class="f1">量价异动</div>
                <div class="f3">监测形态异动拐点</div>
            </a> <a th:href="@{${staticPath}+'static/qyh/20240417/images/b3.png'}" target="al2">
                <div class="f1">资金异动</div>
                <div class="f3">精选主力资金加持</div>
            </a> <a th:href="@{${staticPath}+'static/qyh/20240417/images/b4.png'}" target="al2">
                <div class="f1">长线价值</div>
                <div class="f3">基本面优中选优</div>
            </a>
            </ul>
            <iframe allowfullscreen="true" frameborder="0" th:src="@{${staticPath}+'static/qyh/20240417/images/b1.png'}" scrolling="no" class="swf1" name="al2"></iframe>
        </div>
    </div>
    <div id="dbg10"></div>
    <div id="dbg11"></div>
    <div id="dbg12">
        <div class="main">
            <div id="div1">
                <ul>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240417/images/zs10.jpg'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240417/images/zs1.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240417/images/zs2.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240417/images/zs3.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240417/images/zs4.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240417/images/zs5.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240417/images/zs6.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240417/images/zs7.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240417/images/zs8.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240417/images/zs9.png'}"></li>
                </ul>
                <a href="javascript:;" style="left:10px;"></a> <a href="javascript:;" style="right:10px;"></a></div>
            <script type="text/javascript">
                window.onload = function () {
                    var oDiv = document.getElementById('div1')
                    var oUl = oDiv.getElementsByTagName('ul')[0]
                    var aLi = oUl.getElementsByTagName('li')
                    var aA = oDiv.getElementsByTagName('a')
                    var iSpeed = 1//正左负右
                    var timer = null
                    //计算ul的宽为所有li的宽的和;
                    oUl.innerHTML += oUl.innerHTML + oUl.innerHTML
                    oUl.style.width = aLi[0].offsetWidth * aLi.length + 'px'

                    function Slider () {
                        if (oUl.offsetLeft < -oUl.offsetWidth / 2) {
                            oUl.style.left = 0
                        } else if (oUl.offsetLeft > 0) {
                            oUl.style.left = -oUl.offsetWidth / 2 + 'px'
                        }
                        oUl.style.left = oUl.offsetLeft - iSpeed + 'px'//正负为方向
                    }

                    timer = setInterval(Slider, 30)
                    aA[0].onclick = function () {
                        iSpeed = 1 //控制速度的正负
                    }
                    aA[1].onclick = function () {
                        iSpeed = -1
                    }
                    oDiv.onmouseover = function () {
                        clearInterval(timer)
                    }
                    oDiv.onmouseout = function () {
                        timer = setInterval(Slider, 30)
                    }
                }
            </script>
            <div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" class="d" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br/>
                本活动最终解释权归益盟股份有限公司　沪ICP备06000340 </div>
        </div>
    </div>
</div>
<div class="pf">
    <div class="main"><a href="javascript:void(0)" class="btn3 dh" clickkey="20230222Act" clickdata="btn2"></a></div>
</div>
<div id="popwin1" style="display:none;">
    <div class="tc0" id="divCheck" style="display:block;">
        <div class="font1"><span id="checkcode">8888</span></div>
        <div class="t1">
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
        </div>
        <div class="clear"></div>
        <div class="btn_tc0"><a href="javascript:void(0)" id="btn_submit"></a></div>
    </div>
</div>
<div id="popwin2" style="display:none;">
    <div class="tc1b" id="divSuc" style="display:block;"> <a href="javascript:void(0)" class="tc3-btn dh"></a> <a href="javascript:void(0)" class="tc4-btn dh"></a> </div>
</div>
<div id="popwin3" style="display:none;">
    <div class="tc1" id="divCheckMobile" style="display:block;">
        <div style="font-size: 30px; color: #000; padding-top: 110px;">请在软件端登录参与活动</div>
    </div>
</div>
<div class="h" style="display: none" id="logindiv">
    <div class="logintc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script>
    var userid = $('#hid_uid').val();
    var uid = $('#hid_uid').val();
    var pid = $('#hid_pid').val();
    var mobileX = $('#hid_mobilex').val();
    var cookiename_isshow = "emoney.act20240417IsShow";
    $(document).ready(function () {
        if($("#hid_isLogin").val() == "1") {
            InitCount();
        }else{
            //弹出登录窗口
            $("#logindiv").show();
            $(".logintc").show();
        }
    })
    $('.dh').click(function () {
        if($(this).hasClass("btn1")){
            document.querySelector('#a1').scrollIntoView({ behavior: 'smooth' })
            return;
        }

        var val = getCookie(cookiename_isshow);
        if (val == null || val == "") {
            $.ajax({
                type: 'get',
                url: 'https://act.emoney.cn/activity/user/addcountbyactcode?actcode=ACMasterPop20240417',
                dataType: 'jsonp',
                data: {
                    uid: uid,
                    value: '1'
                },
                success: function (data) {
                    if (data.code == '200') {
                        var num = $('[name=usedCount]').html();
                        $('[name=usedCount]').html(parseInt(num) + 1);
                    }
                }
            })
            setCookie(cookiename_isshow, getQueryString("uid"), 60);
            cmp()
        }

        var payurl = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888020000,888080000&groupCode=4&businessType=biztyp-dsxf";
        if (!!GetExternal()) {
            PC_JH('EM_FUNC_OPEN_LIVE_VIDEO', '15,' + payurl)
        } else {
            //0x 密文手机 + uid  拼在payurl
            payurl += "&phoneEncrypt=" + mobileX + "&UPcid=" + uid;
            $(this).attr("target", "_blank");
            $(this).attr("href", payurl);
        }
    })

    var downflag = '0'
    var al1 = '0'
    var al2 = '0'
    var al3 = '0'
    var al4 = '0'
    var al5 = '0'
    var al6 = '0'
    var al7 = '0'
    var al8 = '0'

    $('.bg a').click(function () {
        $(this).addClass('current').siblings('a').removeClass('current')
    })
    $('#yd1').click(function () {$('#yd1b a').removeClass('current')})
    $('#yd2').click(function () {$('#yd2b a').removeClass('current')})
    $('#yd3').click(function () {$('#yd3b a').removeClass('current')})
    $('#yd4').click(function () {$('#yd4b a').removeClass('current')})

    $('.a1').click(function () {
        $('.pc').show()
        $('.sj').hide()
        $('.s1').attr('href', '#al1')
        $('.s2').attr('href', '#al2')
        $('.s3').attr('href', '#al3')
        $('.s4').attr('href', '#al4')
        $('.s5').attr('href', '#al5')
        $('.s6').attr('href', '#al6')
        $('.s7').attr('href', '#al7')
        $('.btn7').attr('href', '#al1')
        $('.btn8').attr('href', '#al3')
        $('.btn9').attr('href', '#al4')
    })
    $('.a2').click(function () {
        $('.pc').hide()
        $('.sj').show()
        $('.s1').attr('href', '#al1b')
        $('.s2').attr('href', '#al2b')
        $('.s3').attr('href', '#al3b')
        $('.s4').attr('href', '#al4b')
        $('.s5').attr('href', '#al5b')
        $('.s6').attr('href', '#al6b')
        $('.s7').attr('href', '#al7b')
        $('.btn7').attr('href', '#al1b')
        $('.btn8').attr('href', '#al3b')
        $('.btn9').attr('href', '#al4b')
    })

    $('.hdgzk').click(function () {$('#hdgz').show()})
    $('.hdgzx').click(function () {$('.h').hide()})
</script>
<script type="text/javascript">
    var initCount = 3000
    var flag = true
    var timer = null

    function InitCount() {
        $.ajax({
            type: 'get',
            url: 'https://act.emoney.cn/activity/user/getcountbyactcode?actcode=ACMasterPop20240417',
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if (data.code == '200') {
                    var num = 0
                    if (!!data.data) {
                        num = data.data.split(',')[0]
                    }
                    $('.djs').html(addPreZero(initCount - parseInt(num)))//剩余席位
                    $('[name=usedCount]').html(num)//已申请席位

                    setTimeout('InitCount()', 60000 * 5)
                }
            }
        })
    }

    function cmp() {
        var url = "";
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": "",
            "adcode": 'ACMasterPop20240417',
            "targeturl": url,
            "pageurl": window.top.location.href
        }
        var saasUrl = "http://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }

    function setCookie(c_name, value, expiredays) {
        var exdate = new Date();
        exdate.setDate(exdate.getDate() + expiredays);
        document.cookie = c_name + "=" + escape(value) + ";expires=" + exdate.toGMTString() + ";path=/";
    }

    function getCookie(c_name) {

        if (document.cookie.length > 0) {
            c_start = document.cookie.indexOf(c_name + "=")

            if (c_start != -1) {
                c_start = c_start + c_name.length + 1
                c_end = document.cookie.indexOf(";", c_start)
                if (c_end == -1) c_end = document.cookie.length
                return unescape(document.cookie.substring(c_start, c_end))
            }
        }
        return ""
    }


    function addPreZero (num) {
        if (num < 0) {
            return '0000'
        } else if (num < 10) {
            return '000' + num
        } else if (num < 100) {
            return '00' + num
        } else if (num < 1000) {
            return '0' + num
        } else {
            return num
        }
    }
</script>
<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs2=setInterval("SetTimeout(2024,4,30,24,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0" +i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2024/4/30 24:00:00");
    var b=new Date("2024/4/25 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs2);
        $(".t").html("00");
        $(".s").html("00");
        $(".f").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs2").show();
    }
</script>
<script type="text/javascript">document.write(unescape(
    '%3Cscript src=\'https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F\' type=\'text/javascript\'%3E%3C/script%3E'))</script>
</body>
</html>
