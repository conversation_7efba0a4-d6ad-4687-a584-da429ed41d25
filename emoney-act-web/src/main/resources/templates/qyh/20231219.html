<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手天玑版</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="益盟天玑 全网首发">
    <link th:href="@{${staticPath}+'static/qyh/20231219/css/style.css?r='+${resourceVersion}}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qyh/20231205/js/json2.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var www="../";
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj =
                    GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {}
        }
        (function() {
            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="bod">
    <div class="img_1"></div>
    <div class="img_2"></div>
    <div class="img_3">
        <div class="main">
            <div class="txt1">31800</div>
            <div class="txt2">该价格适用于大师掘金版用户</div>
            <a class="btn1 dh" href="javascript:void(0)"></a></div>
    </div>
    <div class="img_4"></div>
    <div class="img_5"></div>
    <div class="img_6"><div class="main">
        <div class="al2"><!-- 代码 开始 -->
            <div class="slider_name2 slider_box2">
                <ul class="silder_con2">
                    <li class="silder_panel2"><img th:src="@{${staticPath}+'static/qyh/20231219/images/c1.png?r='+${resourceVersion}}"></li>
                    <li class="silder_panel2"><img th:src="@{${staticPath}+'static/qyh/20231219/images/c2.png?r='+${resourceVersion}}"></li>
                    <li class="silder_panel2"><img th:src="@{${staticPath}+'static/qyh/20231219/images/c3.png?r='+${resourceVersion}}"></li>
                </ul>
            </div>
            <a href="javascript:void(0)" class="prev"></a><a href="javascript:void(0)" class="next"></a>
            <!-- 代码 结束 --></div>
    </div></div>
    <div class="img_7"></div>
    <div class="img_8"></div>
    <div class="img_9"></div>
    <div class="img_10"></div>
    <div class="img_11"></div>
    <div class="bg1">
        <div class="d1">
            <iframe frameborder="0" webkitallowfullscreen="true" mozallowfullscreen="true" allowfullscreen="true" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=033204c1136e446c8d3ae8df9e119b33&rep=1&py=0" scrolling="no" class="bk1"></iframe>
        </div>
        <div class="d2">
            <div class="al"><!-- 代码 开始 -->
                <div class="slider_name slider_box">
                    <ul class="silder_con">
                        <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20231219/images/e1.png?r='+${resourceVersion}}"></li>
                        <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20231219/images/e2.png?r='+${resourceVersion}}"></li>
                        <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20231219/images/e3.png?r='+${resourceVersion}}"></li>
                        <li class="silder_panel"><img th:src="@{${staticPath}+'static/qyh/20231219/images/e4.png?r='+${resourceVersion}}"></li>
                    </ul>

                    <ul class="silder_nav">
                        <li class=""></li>
                        <li class=""></li>
                        <li class=""></li>
                        <li class=""></li>
                    </ul>
                </div>
                <a href="javascript:void(0)" class="prev"></a><a href="javascript:void(0)" class="next"></a>
                <!-- 代码 结束 --></div>
        </div>
        <div class="b1">
            <div class="n1">
                <a href="javascript:void(0)" class="na1"></a>
                <a href="javascript:void(0)" class="na2"></a>
                <a href="javascript:void(0)" class="na3"></a>
                <a href="javascript:void(0)" class="na4"></a>
                <a href="javascript:void(0)" class="na5"></a>
                <a href="javascript:void(0)" class="na6"></a>
            </div>
            <img th:src="@{${staticPath}+'static/qyh/20231219/images/a1.png?r='+${resourceVersion}}" class="bk sx a1" alt="">
            <img th:src="@{${staticPath}+'static/qyh/20231219/images/a2.png?r='+${resourceVersion}}" class="bk sx a2" alt="" style="display: none">
            <img th:src="@{${staticPath}+'static/qyh/20231219/images/a3.png?r='+${resourceVersion}}" class="bk sx a3" alt="" style="display: none">
            <img th:src="@{${staticPath}+'static/qyh/20231219/images/a4.png?r='+${resourceVersion}}" class="bk sx a4" alt="" style="display: none">
            <img th:src="@{${staticPath}+'static/qyh/20231219/images/a5.png?r='+${resourceVersion}}" class="bk sx a5" alt="" style="display: none">
            <img th:src="@{${staticPath}+'static/qyh/20231219/images/a6.png?r='+${resourceVersion}}" class="bk sx a6" alt="" style="display: none">
            <iframe frameborder="0" webkitallowfullscreen="true" mozallowfullscreen="true" allowfullscreen="true" src="about:blank" scrolling="no" class="bk yc al1"></iframe>
        </div>
    </div>
    <div class="img_12">
        <div class="main"><a class="btn2 dh" href="javascript:void(0)"></a></div>
    </div>
    <div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" class="b">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
        本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
</div>
<a class="pf" href="javascript:void(0)"></a>
<div class="h">
    <div class="tc"><a href="javascript:void(0)" class="close"></a></div>
</div>

<div class="bg" style="display: none">
    <div class="logintc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">

<script type="text/javascript" th:src="@{${staticPath}+'static/qyh/20231205/js/jquery.slides.js'}"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/qyh/20231219/js/main.js?r=20240110'}" type="text/javascript"></script>

<script type="text/javascript">
    var videoList = $('iframe');
    var wHeigt = window.innerHeight;
    document.addEventListener('scroll',function(){
        var isPlay = false;
        //滚动条高度+视窗高度 = 可见区域底部高度
        var visibleBottom = window.scrollY + document.documentElement.clientHeight;
        //可见区域顶部高度
        var visibleTop = window.scrollY;
        for (var i = 0; i < videoList.length; i++) {
            var centerY = $(videoList[i]).offset().top+(videoList[i].offsetHeight/2);
            if(centerY>visibleTop&&centerY<visibleBottom){
                if(!isPlay) {
                    videoList[i].src.match(/py=0/) && (videoList[i].src = videoList[i].src.replace(/py=0/, 'py=1'))
                    isPlay = true
                } else {
                    videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
                }
            }else{
                videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
            }
        }
    })
</script>
<script type="text/javascript">
    document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=8B9093887C7B911EA8A566613F19F22D' type='text/javascript'%3E%3C/script%3E"));
</script>
</body>
</html>
