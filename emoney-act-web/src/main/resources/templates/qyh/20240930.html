<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="稳增长 新经济 新机遇 跟主力 盯主线 决战大波段 智盈大师掘金版--对的时间做对的选择">
    <link th:href="@{${staticPath}+'static/qyh/20240930/style/common.css?r=20240930'}" rel="stylesheet" type="text/css" />
    <link th:href="@{${staticPath}+'static/qyh/20240930/style/popup.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qyh/20231205/js/json2.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qgn/20231011/js/popup.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var www = "../";
        function GetExternal () {
            return window.external.EmObj
        }

        function PC_JH (type, c) {
            try {
                var obj =
                    GetExternal()
                return obj.EmFunc(type, c)
            } catch (e) {}
        }

        (function () {

            function LoadComplete () {
                try {
                    PC_JH('EM_FUNC_DOWNLOAD_COMPLETE', '')
                } catch (ex) {}
            }

            function EM_FUNC_HIDE () {
                try {
                    PC_JH('EM_FUNC_HIDE', '')
                } catch (ex) {}
            }

            function EM_FUNC_SHOW () {
                try {
                    PC_JH('EM_FUNC_SHOW', '')
                } catch (ex) {}
            }

            function IsShow () {
                try {
                    return PC_JH('EM_FUNC_WND_ISSHOW', '')
                } catch (ex) {
                    return '0'
                }
            }

            function openWindow () {
                LoadComplete()

                PC_JH('EM_FUNC_WND_SIZE', 'w=1300,h=820,mid')

                if (IsShow() != '1') {
                    EM_FUNC_SHOW()
                }
            }

            openWindow()
        })()

    </script>
</head>
<body>
<div class="bg9">
    <div class="sppf">
        <div class="main"><a href="javascript:void(0)" class="sppfbtn"></a></div>
    </div>
    <iframe allowfullscreen="true" frameborder="0" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=0d0abc503391405fa274167403d98167&rep=1&py=0"
            scrolling="no" class="sp"></iframe>
    <a href="javascript:void(0)" class="sppfbtn2"></a></div>
<div class="bod">
    <div class="dbg1"></div>
    <div class="dbg2"></div>
    <div class="dbg3">

    </div>
    <div class="dbg4">
        <div class="main"><div class="djs2">
            <div class="s">00</div>
            <div class="f">00</div>
            <div class="m">00</div>
        </div><a href="javascript:void(0)" class="btn1 dh" clickkey="20240930Act" clickdata="btn1"></a></div></div>
    <div id="dbg5"></div>
    <div class="dbg6">
        <div class="main">
            <div id="a1" style="position: absolute; top: -70px"></div>

            <a href="javascript:void(0)" class="btn6 dh popupD" flag="openpop" data-accode="AC588Pop2024093001" data-addclass="btn6h" data-type="1" clickkey="20240930Act" clickdata="buy1"></a>
            <div class="ico4"></div>
            <div id="k"></div>
            <script type="text/javascript">
                function bh () {
                    var i = document.getElementById('k')
                    i.style.display = 'block'
                    setTimeout('clock()', 1300)
                }

                function clock () {
                    var i = document.getElementById('k')
                    i.style.display = 'none'
                }

            </script><a href="javascript:void(0)" class="txt2l8b hdgzk b">详情查看活动规则</a>
        </div>
    </div>
    <div class="dbg7"></div>
    <div class="nav"><a href="javascript:void(0)" class="current a1"></a><a href="javascript:void(0)" class="a2"></a><a href="#al8" class="a3"></a> </div>
    <div class="bg_h">
        <div class="pc">
            <div class="bg" id="al1">
                <ul id="yd1b">
                    <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=ff20da0e2b3b454ea21995433eab9bf4&rep=1&py=1" target="al1">
                        <div class="f1">【价格冲击波】2月8日锁定</div>
                        <div class="f3">朗威股份 24个交易日 涨约<span class="f2">143%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=85419e3b7c9a42e9a782f5f1c04fa820&rep=1&py=1" target="al1">
                        <div class="f1">【黄金坑】2月19日锁定</div>
                        <div class="f3">正强股份 23个交易日 涨约<span class="f2">44%</span></div>
                        <span class="ico2 i1"></span> </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=7c3de755041840efa322148187d3ac1e&rep=1&py=1" target="al1">
                        <div class="f1">【纵横趋势-底部K】2月8日锁定</div>
                        <div class="f3">罗博特科 33个交易日 涨约<span class="f2">167%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=227b4f1434e5487a9fdc9c4b46128674&rep=1&py=1" target="al1">
                        <div class="f1">【高星掘金】5月6日锁定</div>
                        <div class="f3">立讯精密 56个交易日 涨约<span class="f2">32%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=4e96874c1eb04b199add2d5dde2b68f1&rep=1&py=1" target="al1">
                        <div class="f1">【冰谷+潜龙】6月25日出击</div>
                        <div class="f3">锦浪科技 31个交易日 涨约<span class="f2">48%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a3e930df5f954408b9bc3cd92e94b88e&rep=1&py=1" target="al1">
                        <div class="f1">【星级监控】基本面动态评分模型</div>
                        <div class="f3">多类好股 精挑细选</div>
                        <span class="ico2 i2"></span> </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=f53ce5e522d24ac1a1b01e097c47e119&rep=1&py=1" target="al1">
                        <div class="f1">【抄底三剑客】8月6日出击</div>
                        <div class="f3">凯普生物 10个交易日 涨约<span class="f2">44%</span></div>
                    </a>
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1"
                        name="al1"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
            <div class="bg bgc" id="al3">
                <ul id="yd2b">
                    <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=54f0a613377748bf90c4155f55505934&rep=1&py=1" target="al2">
                        <div class="f1">【量王叠现】8月12日出击</div>
                        <div class="f3">博士眼镜 7个交易日 涨约<span class="f2">100%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=785643f49ef7485ca5e3e0f4069f4657&rep=1&py=1" target="al2">
                        <div class="f1">【伏击活跃】7月22日出击</div>
                        <div class="f3">腾达科技 13个交易日 涨约<span class="f2">135%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=888848d1ae36430ca6e74310845861eb&rep=1&py=1" target="al2">
                        <div class="f1">【大阳起势】7月24日出击</div>
                        <div class="f3">腾达科技 11个交易日 涨约<span class="f2">94%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=b8dd006b677346d699d669d9c7808fc2&rep=1&py=1" target="al2">
                        <div class="f1">【资金金叉】6月3日出击</div>
                        <div class="f3">协和电子 9个交易日 涨约<span class="f2">63%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=4b8311b112494abfa303de60038291b0&rep=1&py=1" target="al2">
                        <div class="f1">【K线故事】7月30日锁定</div>
                        <div class="f3">航天晨光 4个交易日 涨约<span class="f2">46%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=f65ef3c381bd4895aa79e5d31833f28b&rep=1&py=1" target="al2">
                        <div class="f1" style="padding-left: 28px">【黑马雷达】7月10日锁定</div>
                        <div class="f3" style="padding-left: 40px; font-size: 13px;">金龙汽车 20个交易日 涨约<span class="f2">123%</span></div>
                        <span class="ico3"></span> </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=62f01a389d444d4db9e3955321463d10&rep=1&py=1" target="al2">
                        <div class="f1" style="font-size: 15px">【纵横趋势-关键突破】7月9日出击</div>
                        <div class="f3">大众交通 23个交易日 涨约<span class="f2">224%</span></div>
                    </a>
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al2"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
            <div class="bg bgd" id="al2">
                <ul id="yd3b">
                    <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=c903f5d3f9d64185842d9089af37d900&rep=1&py=1" target="al3">
                        <div class="f1">【锅底右侧】7月10日出击</div>
                        <div class="f3">富临运业 21个交易日 涨约<span class="f2">17%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=8f185f2fb0e74c8baf4dee9a7f16f56e&rep=1&py=1" target="al3" style="margin-top: 18px">
                        <div class="f1">【冲量变速】6月12日锁定</div>
                        <div class="f3">崇德科技  8个交易日 涨约<span class="f2">49%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=fc09cd9bf99d471495cd8e73430b3b80&rep=1&py=1" target="al3">
                        <div class="f1">【蹦极新生】6月11日出击</div>
                        <div class="f3">依顿电子 27个交易日 涨约<span class="f2">21%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=729bf203c614469ab2ccbc7bedd3234e&rep=1&py=1" target="al3">
                        <div class="f1">【黄金回踩】6月12日出击</div>
                        <div class="f3">宁沪高速 54个交易日 涨约<span class="f2">29%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=96560e26d0ef4b9987295bfc04a848bc&rep=1&py=1" target="al3" style="margin-top: 18px">
                        <div class="f1">【龙腾长波】 6月5日出击</div>
                        <div class="f3">世运电路 30个交易日 涨约<span class="f2">30%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=ad6e3a0ca55f4ba0aa6db82500109db0&rep=1&py=1" target="al3">
                        <div class="f1">【主力动向】 5月28日锁定</div>
                        <div class="f3">上海贝岭 22个交易日 涨约<span class="f2">36%</span></div>
                    </a>
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al3"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
            <div class="bg bge">
                <ul id="yd4b">
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al4"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
            <div class="bg bgf">
                <ul>
                    <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=f65ef3c381bd4895aa79e5d31833f28b&rep=1&py=1" target="al5">
                        <div class="f1">【黑马雷达】7月10日锁定</div>
                        <div class="f3">金龙汽车 20个交易日 涨约<span class="f2">123%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=145c734a57ea4e2d993b8b6c6054c9d3&rep=1&py=1" target="al5">
                        <div class="f1">【主力识别】7月10日锁定</div>
                        <div class="f3">启明信息 19个交易日 涨约<span class="f2">90%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=b378ad833ff34b38a2b9ee3f53edb9e6&rep=1&py=1" target="al5">
                        <div class="f1">【席位龙虎榜】7月15日锁定</div>
                        <div class="f3">雷尔伟 10个交易日 涨约<span class="f2">95%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=dfdcfbbee685427790d9b0352e8d6857&rep=1&py=1" target="al5">
                        <div class="f1">【机构调研】4月23日锁定</div>
                        <div class="f3">新易盛 41个交易日 涨约<span class="f2">53%</span></div>
                    </a>
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al5"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
            <div class="bg bgg">
                <ul>
                    <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=3d77ec5289624093a9a20a2480879ce5&rep=1&py=1" target="al6">
                        <div class="f1" style="font-size: 26px">小黄灯</div>
                        <div class="f3">锁利神器，卖出胜率提升35%</div>
                        <span class="ico2b"></span> </a>
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al6"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
        </div>

        <!--手机-->
        <div class="sj" style="display: none">
            <div class="bg bgb" id="al1b">
                <ul>
                    <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=c19643c1bcfb4573848728eb78ddd99e&rep=1&py=1" target="al1b">
                        <div class="f1">【价格冲击波】2月8日锁定</div>
                        <div class="f3">朗威股份 24个交易日 涨约<span class="f2">143%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=f00cd5103aeb4061bd73c9c1f1766b70&rep=1&py=1" target="al1b">
                        <div class="f1">【黄金坑】2月19日锁定</div>
                        <div class="f3">正强股份 23个交易日 涨约<span class="f2">44%</span></div>
                        <span class="ico2 i1"></span> </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=bbbc6cdfcda642679e5220b4526c9500&rep=1&py=1" target="al1b">
                        <div class="f1">【纵横趋势-底部K】2月8日锁定</div>
                        <div class="f3">罗博特科 33个交易日 涨约<span class="f2">167%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=f3a033faf9a74988a4a966002cb4ccd2&rep=1&py=1" target="al1b" style="margin-top: 13px">
                        <div class="f1">【高星掘金】5月6日锁定</div>
                        <div class="f3">立讯精密 56个交易日 涨约<span class="f2">32%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=7a58b63f98ed4eb7b4b825931935031b&rep=1&py=1" target="al1b">
                        <div class="f1">【冰谷+潜龙】6月25日出击</div>
                        <div class="f3">锦浪科技 31个交易日 涨约<span class="f2">48%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=5fa2ccecd2cf4e0bb8f18fc16896004f&rep=1&py=1" target="al1b" style="margin-top: 13px">
                        <div class="f1">【抄底三剑客】8月6日出击</div>
                        <div class="f3">凯普生物 10个交易日 涨约<span class="f2">44%</span></div>
                    </a>
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al1b"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
            <div class="bg bgc" id="al3b">
                <ul>
                    <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=dc5cee20b6cd41ee867e6f8fd6a8e48c&rep=1&py=1" target="al2b">
                        <div class="f1">【量王叠现】8月12日出击</div>
                        <div class="f3">博士眼镜 7个交易日 涨约<span class="f2">100%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=13d93f239c364873ade26e9b41f87540&rep=1&py=1" target="al2b">
                        <div class="f1">【伏击活跃】7月22日出击</div>
                        <div class="f3">腾达科技 13个交易日 涨约<span class="f2">135%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=b1634898a8604f76bc62c19660eba5ed&rep=1&py=1" target="al2b">
                        <div class="f1">【大阳起势】7月24日出击</div>
                        <div class="f3">腾达科技 11个交易日 涨约<span class="f2">94%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=835b47c54e1f408597a1ea3c0285ec3c&rep=1&py=1" target="al2b">
                        <div class="f1">【资金金叉】6月17日出击</div>
                        <div class="f3">东山精密 24个交易日 涨约<span class="f2">51%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=41ad387e91a5419bbe33ba1065a8e9a3&rep=1&py=1" target="al2b">
                        <div class="f1">【K线故事】7月30日锁定</div>
                        <div class="f3">航天晨光 4个交易日 涨约<span class="f2">46%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=9f739a3d46b140de86da87515b1496d4&rep=1&py=1" target="al2b">
                        <div class="f1" style="padding-left: 28px">【黑马雷达】8月15日锁定</div>
                        <div class="f3" style="padding-left: 40px;font-size: 13px;">力源信息 8个交易日 涨约<span class="f2">58%</span></div>
                        <span class="ico3"></span> </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=97d8dee7308c43288012f200a12bb4db&rep=1&py=1" target="al2b">
                        <div class="f1" style="font-size: 15px">【纵横趋势-关键突破】7月9日出击</div>
                        <div class="f3">大众交通 23个交易日 涨约<span class="f2">224%</span></div>
                    </a>
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al2b"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
            <div class="bg bgd" id="al2b">
                <ul>
                    <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=5abc2218611f45b3a9ae38d0163352b2&rep=1&py=1" target="al3b">
                        <div class="f1">【锅底右侧】7月10日出击</div>
                        <div class="f3">富临运业 21个交易日 涨约<span class="f2">17%</span></div>
                    </a> <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=5839d09480cc4b66a174ac1e2c9875eb&rep=1&py=1" target="al3b" style="margin-top: 18px">
                    <div class="f1">【冲量变速】6月12日锁定</div>
                    <div class="f3">崇德科技  8个交易日 涨约<span class="f2">49%</span></div>
                </a> <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=050874fcb6f24d7e8b8ec926e53c225a&rep=1&py=1" target="al3b">
                    <div class="f1">【蹦极新生】6月11日出击</div>
                    <div class="f3">依顿电子 27个交易日 涨约<span class="f2">21%</span></div>
                </a> <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=ceb4357b93344177b5d446b3909a162e&rep=1&py=1" target="al3b">
                    <div class="f1">【黄金回踩】6月18日出击</div>
                    <div class="f3">宁沪高速 51个交易日 涨约<span class="f2">30%</span></div>
                </a> <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a4489f6967794e3eaf71319964096d2a&rep=1&py=1" target="al3b" style="margin-top: 18px">
                    <div class="f1">【龙腾长波】 6月5日出击</div>
                    <div class="f3">世运电路 30个交易日 涨约<span class="f2">30%</span></div>
                </a> <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=d86b8fd477054d56aff361e3cfefbd85&rep=1&py=1" target="al3b">
                    <div class="f1">【主力动向】 5月28日锁定</div>
                    <div class="f3">上海贝岭 22个交易日 涨约<span class="f2">36%</span></div>
                </a>
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al3b"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
            <div class="bg bge">
                <ul >
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al4b"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
            <div class="bg bgf">
                <ul>
                    <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=9f739a3d46b140de86da87515b1496d4&rep=1&py=1" target="al5b">
                        <div class="f1">【黑马雷达】8月15日锁定</div>
                        <div class="f3">力源信息 8个交易日 涨约<span class="f2">58%</span></div>
                    </a>
                    <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=2ed604a09fa74bf896084f8d1f9e4672&rep=1&py=1" target="al5b">
                        <div class="f1">【席位龙虎榜】7月15日锁定</div>
                        <div class="f3">雷尔伟 10个交易日 涨约<span class="f2">95%</span></div>
                    </a>
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al5b"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
            <div class="bg bgg">
                <ul>
                    <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=bd0845a86d3e4a1bb370e527391991dc&rep=1&py=1" target="al6b">
                        <div class="f1" style="font-size: 26px">小黄灯</div>
                        <div class="f3">锁利神器，卖出胜率提升35%</div>
                        <span class="ico2b"></span> </a>
                </ul>
                <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al6b"></iframe>
                <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a> </div>
            </div>
        </div>

        <div class="bg bgh" id="al8">
            <ul>
                <a class="current" href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=12f341a6278b489eb0d35507f3df0d89&rep=1&py=1" target="al7">
                    <div class="f1">智盈大师微信屏</div>
                    <div class="f3">公众号+小程序，便利又高效</div>
                </a> <a href="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=b83745874e22412f895b5c964ec8fbb4&rep=1&py=1" target="al7">
                <div class="f1">微信屏定制策略</div>
                <div class="f3">选股简便操作，触发提醒</div>
            </a>
            </ul>
            <iframe allowfullscreen="true" frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al7"></iframe>
            <div class="txt3"><a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a></div>
        </div>
        <div class="bg bgi">
            <ul>
                <a class="current" th:href="@{${staticPath}+'static/qyh/20240930/images/a1.png'}" target="al8" id="s1">
                    <div class="f1" style="padding-top: 7px; font-size: 24px">2个月<br />
                        从亏损到稳定盈利！</div>
                </a>
                <a th:href="@{${staticPath}+'static/qyh/20240930/images/a2.png'}" target="al8" id="s2">
                    <div class="f1" style="padding-top: 7px; font-size: 24px">别人亏损<br />
                        我收益超70%账户！</div>
                </a>
                <a th:href="@{${staticPath}+'static/qyh/20240930/images/a3.png'}" target="al8" id="s3">
                    <div class="f1" style="padding-top: 7px; font-size: 24px">只要股票还在做<br />
                        大师软件就会一直用！</div>
                </a>
            </ul>
            <iframe allowfullscreen="true" frameborder="0" th:src="@{${staticPath}+'static/qyh/20240930/images/a1.png'}" src="images/a1.png" scrolling="no" class="swf1" name="al8" style="border: 1px solid #FDE0A4" id="s4"></iframe>
            <div class="txt3"> <a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a></div>
        </div>
    </div>
    <div id="dbg8"></div>
    <div id="dbg9"></div>
    <div id="dbg10">
        <div class="main">
            <div id="div1">
                <ul>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240930/images/zs10.jpg'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240930/images/zs1.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240930/images/zs2.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240930/images/zs3.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240930/images/zs4.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240930/images/zs5.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240930/images/zs6.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240930/images/zs7.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240930/images/zs8.png'}"></li>
                    <li><img th:src="@{${staticPath}+'static/qyh/20240930/images/zs9.png'}"></li>
                </ul>
                <a href="javascript:;" style="left:10px;"></a> <a href="javascript:;" style="right:10px;"></a></div>
            <script type="text/javascript">
                window.onload = function () {
                    var oDiv = document.getElementById('div1')
                    var oUl = oDiv.getElementsByTagName('ul')[0]
                    var aLi = oUl.getElementsByTagName('li')
                    var aA = oDiv.getElementsByTagName('a')
                    var iSpeed = 1//正左负右
                    var timer = null
                    //计算ul的宽为所有li的宽的和;
                    oUl.innerHTML += oUl.innerHTML + oUl.innerHTML
                    oUl.style.width = aLi[0].offsetWidth * aLi.length + 'px'

                    function Slider () {
                        if (oUl.offsetLeft < -oUl.offsetWidth / 2) {
                            oUl.style.left = 0
                        } else if (oUl.offsetLeft > 0) {
                            oUl.style.left = -oUl.offsetWidth / 2 + 'px'
                        }
                        oUl.style.left = oUl.offsetLeft - iSpeed + 'px'//正负为方向
                    }

                    timer = setInterval(Slider, 30)
                    aA[0].onclick = function () {
                        iSpeed = 1 //控制速度的正负
                    }
                    aA[1].onclick = function () {
                        iSpeed = -1
                    }
                    oDiv.onmouseover = function () {
                        clearInterval(timer)
                    }
                    oDiv.onmouseout = function () {
                        timer = setInterval(Slider, 30)
                    }
                }
            </script>
        </div>
    </div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br/>
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340 </div>
<a href="javascript:void(0)" class="wddd">我的订单</a>
<div class="pf pf1"><a href="javascript:void(0)" class="close pfx"></a><img th:src="@{${staticPath}+'static/qyh/20240930/images/pf.png'}" alt="" usemap="#Map" border="0">
    <map name="Map" id="Map">
        <area shape="rect" coords="-1,91,204,122" href="#al1" class="s1"/>
        <area shape="rect" coords="2,130,209,159" href="#al2" class="s2"/>
        <area shape="rect" coords="-1,168,207,199" href="#al3" class="s3"/>
    </map>
</div>
<div class="pf2">
    <div class="main"><a href="javascript:void(0)" class="btn10 dh" clickkey="20240930Act" clickdata="btn10"></a></div>
</div>
<div class="h" id="ddcx">
    <div class="wddd2 orderlist"><a href="javascript:void(0)" class="close hdgzx"></a>
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tbody id="tbody">
            <tr>
                <td class="bt">订单号</td>
                <td class="bt">订单类型</td>
                <td class="bt">支付时间</td>
                <td class="bt">支付金额</td>
            </tr>
            <!--<tr>
              <td>202306200011</td>
              <td>订金</td>
              <td>2023.6.20</td>
              <td>¥1000</td>
            </tr>-->
            </tbody>
        </table>
    </div>
</div>
<div class="h" id="hdgz">
    <div class="hdgz"><a href="javascript:void(0)" class="close hdgzx"></a> <strong>开户返金豆活动规则：</strong>
        <ul>
            <li>本活动仅限2024年9月10日至2024年9月30日新升级益盟操盘手大师的用户参与，每位用户仅可参与一次开户活动，历史已经参与过的不可再重复参加活动；</li>
            <li>本活动需通过益盟专属二维码或链接进行开户，<strong class="red2">开户手机号需与订单手机号一致</strong>，开户营业部需为湘财证券国权北路营业部，且需为该券商新用户；</li>
            <li>开户成功：沪深双市均开立成功并绑定银行卡；资产达标：开户成功且连续20个交易日日均资产大于等于5万；</li>
            <li>2024年9月10日至2024年9月30日内资产达标可获得5万金豆，金豆会在资产达标后15个自然日内到账；</li>
            <li>本活动获得的金豆，可一次性兑换500元软件费补贴或1000元产品优惠券。可兑换时间为获得金豆之日起365个自然日内，一经兑换不可修改。详细规则可在获得金豆后进入益盟软件-个人中心-我的福利查看；</li>
            <li>若您在兑换金豆前退货，则取消活动资格；</li>
            <li>参加活动开户的账户仅为普通沪深账户，如需办理其他证券相关业务或咨询开户相关问题，请拨打湘财证券开户热线：021-60331188转2；</li>
            <li>如遇活动相关问题可咨询您的益盟服务专员或拨打10108688；</li>
            <li>风险提示：投资有风险，入市需谨慎；</li>
        </ul>
        <strong>*本活动最终解释权归益盟股份有限公司所有。</strong></div>
</div>
<div id="popwin1" style="display:none;">
    <div class="tc0" id="divCheck" style="display:block;">
        <div class="font1"><span id="checkcode">8888</span></div>
        <div class="t1">
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
            <input name="" type="text" class="text1"/>
        </div>
        <div class="clear"></div>
        <div class="btn_tc0"><a href="javascript:void(0)" id="btn_submit"></a></div>
    </div>
</div>
<div id="popwin2" style="display:none;">
    <div class="tc1b" id="divSuc" style="display:block;"> <a href="javascript:void(0)" class="tc3-btn dh"></a> <a href="javascript:void(0)" class="tc4-btn dh"></a> </div>
</div>
<div id="popwin3" style="display:none;">
    <div class="tc1" id="divCheckMobile" style="display:block;">
        <div style="font-size: 30px; color: #000; padding-top: 110px;">请在软件端登录参与活动</div>
    </div>
</div>
<div class="h" style="display: none" id="logindiv">
    <div class="logintc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>

<script>
    var initCount = 5000;
    var countActCode = "20240930jslt";
    var cmpCode = "PREFERENTIAL20240920129201";//抢购推送cmp
    var payCmpCode = "DEPOSIT20240830128770";//原三年版支付推送cmpcode
    var uid = $("#hid_uid").val();
    var pid = $("#hid_pid").val();
    var mobileX = $("#hid_mobilex").val();
    var staticPath = $("#hid_staticPath").val();
    var timer = null
    var OrderPaymentItemPayTypeEnum = {
        normal: 0, prePay: 1, finalPaid: 2
    }
    var UserPayStatusIdEnum = {
        noPay: 0, prePaid: 1, finalPaid: 200, reimburse: 201
    }
    /**
     *  "payStatusId":支付状态(0:等待支付,1:支付中,101:支付失败,200:支付成功,201:已退款)
     */
    var PayStatusIdEnum = {
        waiting: 0, paying: 1, fail: 101, success: 200, reimburse: 201
    }
    //上一期抢优惠
    var acCode_pre = 'ac-1240906154347706'
    var acCode = 'ac-1240920214442921'
    //付了尾款
    var isFinalPaid = false
    //付了尾款
    var isPaid = false
    //订单
    var orderList = []
    var timer = null
    var phoneEncrypt = getQueryString('phoneEncrypt')
    var UPcid = getQueryString('UPcid')
    var wz=getQueryString("wz");
    var downflag = '0'

    $(document).ready(function () {
        if($("#hid_isLogin").val() == "1") {
            getPayStatus()
            bindEvents();
        }else{
            //弹出登录窗口
            $("#logindiv").show();
            $(".logintc").show();
        }

    })
    //点击事件
    function bindEvents() {
        $('.btn1,.btn2,.btn3,.btn3_new,.btn10').click(function () {
            document.querySelector('#a1').scrollIntoView({behavior: 'smooth'})
            bh()
        })
        $('.pfx').click(function () {
            $('.pf').hide()
            downflag = '1'
        })

        //查询订单列表
        $('.wddd').click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            getPayList(uid)
        })
        //一年
        $('.btn6,.btn10').click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            if (isFinalPaid) {
                //定金已经付了
                openPopup(function () {
                    $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20240930/images/tc4.png')")
                    $('.tc3-btn').hide()
                    $('.tc4-btn').show()
                })

            } else if (isPaid) {
                //尾款已经付了
                openPopup(function () {
                    $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20240930/images/tc5.png')")
                    $('.tc3-btn').hide()
                    $('.tc4-btn').hide()
                })
            } else {
                PushDataToCMP(cmpCode, uid, '');

                openPopup(function () {
                    $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20240930/images/tc1.png')")
                    $('.tc3-btn').hide()
                    $('.tc4-btn').show()
                })
            }
        })

        //去支付
        $(document).on('click', '.tc3-btn,.tc4-btn', function () {
            var payurl = "";
            if (!!wz) {
                payurl = 'https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&resourcestypeid=234&resourcesid=1244376&businessType=biztyp-szds'
            } else {
                payurl = 'https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888080000&groupCode=0&resourcestypeid=234&resourcesid=1244376&businessType=biztyp-szds'
            }

            if (!!GetExternal()) {
                PC_JH('EM_FUNC_OPEN_LIVE_VIDEO', '15,' + payurl)
            } else {
                //0x 密文手机 + uid  拼在payurl
                payurl += "&phoneEncrypt=" + mobileX + "&UPcid=" + uid;
                $(this).attr("target", "_blank");
                $(this).attr("href", payurl);
            }

            if (!isFinalPaid && !isPaid) {
                if (timer) clearInterval(timer)

                timer = setInterval(function () {
                    getPayStatus(function () {
                        if (isFinalPaid) {
                            //定金已经付了
                            openPopup(function () {
                                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20240930/images/tc4.png')")
                                $('.tc3-btn').hide()
                                $('.tc4-btn').show()
                            })
                            clearInterval(timer)

                            pushdatatocmp(uid,payCmpCode);

                        } else if (isPaid) {
                            //尾款已经付了
                            openPopup(function () {

                                $('.tc1b').css('background-image', "url('" + staticPath + "static/qyh/20240930/images/tc5.png')")
                                $('.tc3-btn').hide()
                                $('.tc4-btn').hide()
                            })
                            clearInterval(timer)
                            if (!timer) {
                                closePopup()
                            }
                        }
                    })

                }, 9000)
            }
            if (isFinalPaid) {
                layer.closeAll()
            }
        })

        $('.bg a').click(function () {
            $(this).addClass('current').siblings('a').removeClass('current');
        })
        $('.bgi a').mouseenter(function () {
            $(this).addClass('current').siblings('a').removeClass('current');
        })
        $('#s1').mouseenter(function () {
            $("#s4").attr('src',staticPath + "static/qyh/20240930/images/a1.png");
        })
        $('#s2').mouseenter(function () {
            $("#s4").attr('src',staticPath + "static/qyh/20240930/images/a2.png");
        })
        $('#s3').mouseenter(function () {
            $("#s4").attr('src',staticPath + "static/qyh/20240930/images/a3.png");
        })

        $('#yd1').click(function () {$('#yd1b a').removeClass('current')})
        $('#yd2').click(function () {$('#yd2b a').removeClass('current')})
        $('#yd3').click(function () {$('#yd3b a').removeClass('current')})
        $('#yd4').click(function () {$('#yd4b a').removeClass('current')})
        $('.nav a').click(function () {
            $(this).addClass('current').siblings('a').removeClass('current')
        })

        $('.a1').click(function () {
            $('.pc').show()
            $('.sj').hide()
            $('.s1').attr('href', '#al1')
            $('.s2').attr('href', '#al2')
            $('.s3').attr('href', '#al3')
            $('.s4').attr('href', '#al4')
            $('.s5').attr('href', '#al5')
            $('.s6').attr('href', '#al6')
            $('.s7').attr('href', '#al7')
        })
        $('.a2').click(function () {
            $('.pc').hide()
            $('.sj').show()
            $('.s1').attr('href', '#al1b')
            $('.s2').attr('href', '#al2b')
            $('.s3').attr('href', '#al3b')
            $('.s4').attr('href', '#al4b')
            $('.s5').attr('href', '#al5b')
            $('.s6').attr('href', '#al6b')
            $('.s7').attr('href', '#al7b')
        })

        $('.sppfbtn').click(function () {
            $('.bg9').animate({
                height: '500px'
            })
            $('.sppfbtn2').show(600)
        })
        $('.sppfbtn2').click(function () {
            $('.bg9').animate({
                height: '80px'
            })
            $('.sppfbtn2').hide(600)
            $('.sp').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a27ccbb26e454f8fab41f2bfdd18dac9&rep=1&py=0')
        })

        $('.hdgzk').click(function () {$('#hdgz').show()})
        $('.hdgzx').click(function () {$('.h').hide()})
    }

    //获取定金尾款订单列表
    function getPayList () {
        $('#tbody tr').eq(0).siblings().remove()
        if (orderList.length) {
            var list = orderList

            function computedOrderTypeName (orderPayments) {
                let result = ''
                if (orderPayments && orderPayments.length) {

                    orderPayments.forEach(i => {
                        if (i.payStatusId === PayStatusIdEnum.success && i.payType === OrderPaymentItemPayTypeEnum.prePay) {
                        result += '订金'
                    }
                    if (i.payStatusId === PayStatusIdEnum.success && i.payType === OrderPaymentItemPayTypeEnum.finalPaid) {
                        result += '+尾款'
                    }
                })
                }
                return result
            }

            if (!!list && list.length > 0) {
                for (var i = 0; i < list.length; i++) {
                    var item = list[i]

                    var trhtml = document.createElement('tr')
                    trhtml.id = 'mDiv'
                    trhtml.innerHTML = '<td>' + item.orderNumber + '</td><td>' + computedOrderTypeName(item.orderPayments) +
                        '</td><td>' +
                        timestampToTime(item.createTime) + '</td><td>￥' + item.payTotal + '</td>'

                    $('#tbody').append(trhtml)
                }
            } else {
                var trhtml = document.createElement('tr')
                trhtml.id = 'mDiv'
                trhtml.innerHTML = '<td colspan=\'4\'>您好，当前未查询到任何有效订单</td>'

                $('#tbody').append(trhtml)
            }

            $('#ddcx').show()
        } else {
            var trhtml = document.createElement('tr')
            trhtml.id = 'mDiv'
            trhtml.innerHTML = '<td colspan=\'4\'>您好，当前未查询到任何有效订单</td>'

            $('#tbody').append(trhtml)
        }

        $('#ddcx').show()
    }

    //获取支付状态
    function getPayStatus (cb) {
        var mobileX = $("#hid_mobilex").val();
        $.ajax({
            url: "https://emapp.emoney.cn/buy/order/QueryOrderList?Emapp-Format=EmappJsonp",
            timeout: 5000,
            type: 'get',
            dataType: 'jsonp',
            cache: false,
            data: {"phoneEncrypt": mobileX, "type": 0},
            success: function (data) {
                if (data.result.code === 0) {
                    var arr = data.detail
                    var status = ''
                    orderList = []
                    arr.forEach(item => {
                        if (item && item.orderLogistice && item.orderLogistice.length) {
                        item.orderLogistice.forEach(i => {
                            if (i.activityCode === acCode_pre &&
                                [UserPayStatusIdEnum.finalPaid, UserPayStatusIdEnum.prePaid].includes(item.payStatusId)) {
                                //定金已付
                                isFinalPaid = item.payStatusId === UserPayStatusIdEnum.prePaid
                                //尾款已付
                                isPaid = item.payStatusId === UserPayStatusIdEnum.finalPaid

                                orderList.push(item)
                            }
                            if (i.activityCode === acCode &&
                                [UserPayStatusIdEnum.finalPaid, UserPayStatusIdEnum.prePaid].includes(item.payStatusId)) {

                                isNormalPaid = item.payStatusId === UserPayStatusIdEnum.finalPaid

                                orderList.push(item)
                            }
                    })
                    }
                })

                    cb && cb()
                }
            }
        })
    }

    function PushDataToCMP (accode, uid, uname) {
        var data = {
            'appid': '10088',
            'logtype': 'click',
            'mid': '',
            'pid': getQueryString('pid'),
            'sid': getQueryString('sid'),
            'tid': getQueryString('tid'),
            'uid': uid,
            'uname': uname,
            'adcode': accode,
            'targeturl': '',
            'pageurl': window.top.location.href
        }
        var saasUrl = 'http://ds.emoney.cn/saas/queuepush'
        var saasSrc = saasUrl + '?v=' + Math.random()
            + '&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID'
            + '&message=' + encodeURIComponent(JSON.stringify(data))

        var elm = document.createElement('img')
        elm.src = saasSrc
        elm.style.display = 'none'
        document.body.appendChild(elm)
    }
</script>
<script type="text/javascript">
    //打开弹窗
    function openPopup (cb) {
        layer.close(layer.index)
        layer.open({
            type: 1,
            title: false,
            //closeBtn: 0,
            area: ['auto'],
            //shadeClose: true,
            skin: 'layui-layer-nobg', //没有背景色
            content: $('#popwin2').html(),
            success: function (layero, index) {
                cb()
            },
            end: function () { }
        })
    }

    // 处理时间戳
    /* 时间戳转换为时间 */
    function timestampToTime (timestamp) {
        timestamp = timestamp ? timestamp : null
        var date = new Date(timestamp)//时间戳为10位需*1000，时间戳为13位的话不需乘1000
        var Y = date.getFullYear() + '-'
        var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
        var D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
        var h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
        var m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
        var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
        return Y + M + D + h + m + s
    }

    function getQueryString (name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
        var r = window.location.search.substr(1).match(reg)
        if (r != null) return unescape(r[2])
        return null
    }

    function checkPermission (pid) {
        if (pid != "888010000" && pid != "888010400") {
            layer.msg("本活动仅限小智盈用户参与");
            return false;
        }
        return true;
    }
</script>
<script type="text/javascript">
    var al1 = '0'
    var al2 = '0'
    var al3 = '0'
    var al4 = '0'
    var al5 = '0'
    var al6 = '0'
    var al7 = '0'
    var al8 = '0'

    $(window).scroll(function (e) {
        if (($(window).scrollTop() >= 1440) && (downflag == '0')) {
            $('.pf').fadeIn(300)
        }
        if ($(window).scrollTop() >= 1440) {
            $('.pf2').fadeIn(300)
        }else {
            $('.pf,.pf2').fadeOut(300)
        }
        if (($(window).scrollTop() > 1306) && (al1 == '0')) {
            $('[name=\'al1\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=ff20da0e2b3b454ea21995433eab9bf4&rep=1&py=0')
            $('[name=\'al1b\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=c19643c1bcfb4573848728eb78ddd99e&rep=1&py=0')
            al1 = '1'
        }
        if (($(window).scrollTop() > 1900) && (al2 == '0')) {
            $('[name=\'al2\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=54f0a613377748bf90c4155f55505934&rep=1&py=0')
            $('[name=\'al2b\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=dc5cee20b6cd41ee867e6f8fd6a8e48c&rep=1&py=0')
            al2 = '1'
        }
        if (($(window).scrollTop() > 2520) && (al3 == '0')) {
            $('[name=\'al3\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=c903f5d3f9d64185842d9089af37d900&rep=1&py=0')
            $('[name=\'al3b\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=5abc2218611f45b3a9ae38d0163352b2&rep=1&py=0')
            al3 = '1'
        }
        if (($(window).scrollTop() > 3240) && (al4 == '0')) {
            $('[name=\'al4\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=e6c2558cd7f8494db29d1717df2acc1a&rep=1&py=0')
            $('[name=\'al4b\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=3e63a7c5869c4945841a2733fae9ecba&rep=1&py=0')
            al4 = '1'
        }
        if (($(window).scrollTop() > 3870) && (al5 == '0')) {
            $('[name=\'al5\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=f65ef3c381bd4895aa79e5d31833f28b&rep=1&py=0')
            $('[name=\'al5b\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=9f739a3d46b140de86da87515b1496d4&rep=1&py=0')
            al5 = '1'
        }
        if (($(window).scrollTop() > 4570) && (al6 == '0')) {
            $('[name=\'al6\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=3d77ec5289624093a9a20a2480879ce5&rep=1&py=0')
            $('[name=\'al6b\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=bd0845a86d3e4a1bb370e527391991dc&rep=1&py=0')
            al6 = '1'
        }
        if (($(window).scrollTop() > 5210) && (al7 == '0')) {
            $('[name=\'al7\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=12f341a6278b489eb0d35507f3df0d89&rep=1&py=0')
            $('[name=\'al7b\']').
            attr('src',
                'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a3ad39e9a7b24bcf9fd6926080cd7d4e&rep=1&py=0')
            al7 = '1'
        }
    })

    var videoList = $('iframe')
    var wHeigt = window.innerHeight
    document.addEventListener('scroll', function () {
        var isPlay = false
        //滚动条高度+视窗高度 = 可见区域底部高度
        var visibleBottom = window.scrollY + document.documentElement.clientHeight
        //可见区域顶部高度
        var visibleTop = window.scrollY
        for (var i = 0; i < videoList.length; i++) {
            var centerY = $(videoList[i]).offset().top + (videoList[i].offsetHeight / 2)
            if (centerY > visibleTop && centerY < visibleBottom) {
                if (!isPlay) {
                    videoList[i].src.match(/py=0/) && (videoList[i].src = videoList[i].src.replace(/py=0/, 'py=1'))
                    isPlay = true
                } else {
                    videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
                }
            } else {
                videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
            }
        }
    })
</script>
<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs2=setInterval("SetTimeout(2024,9,30,18,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0" +i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2024/9/30 18:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs2);
        $(".t").html("00");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
</script>
<script type="text/javascript">document.write(unescape(
    '%3Cscript src=\'https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F\' type=\'text/javascript\'%3E%3C/script%3E'))</script>
</body>
</html>
