<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2023/20230131/css/style.css'}" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        .tc5{display: none}
    </style>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"></div>
<div class="img_3"></div>
<div class="img_4"><div class="main"><a  href="javascript:void(0)" name="payurl" target="_blank" class="btn1 an1 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a><a  href="javascript:void(0)" name="payurl1" target="_blank" class="btn1 an2 dh" clickkey="openPay1" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div></div>
<div class="img_5">
    <div class="main">
        <a href="#aaa1" class="btn5_1"></a><a href="#aaa2" class="btn5_2"></a><a href="#aaa3" class="btn5_3"></a><a href="#aaa4" class="btn5_4"></a><a href="#aaa5" class="btn5_5"></a><a href="#aaa6" class="btn5_6"></a>
    </div>
</div>
<div id="aaa1" class="img_6"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al1"></iframe></div></div>
<div id="aaa2" class="img_7"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al2"></iframe></div></div>
<div id="aaa3" class="img_8"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al3"></iframe></div></div>
<div id="aaa4" class="img_9"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al4"></iframe></div></div>
<div id="aaa5" class="img_10"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al5"></iframe></div></div>
<div id="aaa6" class="img_11"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al6"></iframe></div></div>
<a href="javascript:;" class="btn2 dh"></a>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<a href="javascript:void(0);" class="server" id="goIM"></a>
<div class="bott">
    <div class="main"><a href="javascript:void(0)" name="payurl" target="_blank" class="btn3 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a><a href="javascript:void(0)" name="payurl1" target="_blank" class="btn4 dh" clickkey="openPay1" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div>
</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc4" style="display: none;"><a href="javascript:;" class="close"></a>
        1、活动参与对象：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。<br />
        2、福利发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限和【赠送功能】权限。<br />
        3、优惠券规则：续费2年版的用户，赠送1张100元续费抵用券，可用于下次智盈续费。<br />
        4、积分规则：续费返还积分，在完成续费和适当性测评后发放，且为冻结状态，适当性测评完成30天后，积分解冻，可在积分商城使用。
    </div>
    <div class="tc5" id="pop1"><a href="javascript:;" class="close"></a><a href="" target="_blank" name="payurl1" clickkey="openPay1" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"><img th:src="@{${staticPath}+'static/renew2023/20230131/images/tc1.png'}" alt=""></a></div>
    <div class="tc5" id="pop2"><a href="javascript:;" class="close"></a><a href="" target="_blank" name="payurl1" clickkey="openPay1" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"><img th:src="@{${staticPath}+'static/renew2023/20230131/images/tc2.png'}" alt=""></a></div>
    <div class="tc5" id="pop3"><a href="javascript:;" class="close"></a><a href="javascript:;" data-productid="599" name="btn_exchange"><img th:src="@{${staticPath}+'static/renew2023/20230131/images/tc3.png'}" alt=""></a></div><!--100积分兑换100优惠券-->
    <div class="tc5" id="pop4"><a href="javascript:;" class="close"></a><a href="" target="_blank" name="payurl1" clickkey="openPay1" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"><img th:src="@{${staticPath}+'static/renew2023/20230131/images/tc4.png'}" alt=""></a></div><!--100兑换成功-->
    <div class="tc5" id="pop5"><a href="javascript:;" class="close"></a><a href="javascript:;" data-productid="598" name="btn_exchange"><img th:src="@{${staticPath}+'static/renew2023/20230131/images/tc5.png'}" alt=""></a></div><!--30积分兑换60优惠券-->
    <div class="tc5" id="pop6"><a href="javascript:;" class="close"></a><a href="" target="_blank" name="payurl1" clickkey="openPay1" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"><img th:src="@{${staticPath}+'static/renew2023/20230131/images/tc6.png'}" alt=""></a></div><!--30兑换成功-->

</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">

    var www="../";
    var actcode = /*[[${actcode}]]*/ "0"
    $(document).ready(function () {
        var uid = "";
        var pid = "";
        var uname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        var allpoint =  /*[[${allPoint}]]*/ "0";
        var hasCoupon100 =  /*[[${hasCoupon100}]]*/ "0";
        var hasCoupon30 =  /*[[${hasCoupon30}]]*/ "0";

        if (!channelcode) {
            channelcode = "A12050";
        }

        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
        }

        var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=90&channelcode=" + channelcode + "&" + location.search;
        var payUrl1 = "http://pay.emoney.cn/newpayv2/pay/order?actid=91&channelcode=" + channelcode + "&" + location.search;
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search;
            payUrl1 = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search;
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
            payUrl1 += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl1]").attr("href", payUrl1);
        $("[name=payurl],[name=payurl1]").click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20230329");
        });

        $("#goIM").click(function () {
            goIM("小智盈续费");
        });

        $(".btn2").click(function (){
            $(".bg").show();
            $(".tc4").show();
        });

        $(".close").click(function (){
            $(".bg").hide();
            $(".tc4").hide();
        });

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        } else {
            if (!checkPermission(pid)) {
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20230329");

            //3秒后弹出对应窗口
            setTimeout(function (){
                if(hasCoupon100 && hasCoupon30){
                    $(".bg").show();
                    $("#pop1").show();
                }else if(hasCoupon30){
                    $(".bg").show();
                    $("#pop2").show();
                }else if(hasCoupon100){
                    $(".bg").show();
                    $("#pop1").show();
                }else if(!hasCoupon100 && !hasCoupon30){
                    if(parseInt(allpoint)>=100){
                        $(".bg").show();
                        $("#pop3").show();
                    }
                    else if(parseInt(allpoint)>=60){
                        $(".bg").show();
                        $("#pop5").show();
                    }
                }

            },3000);
        }
        //立即兑换
        $("[name=btn_exchange]").click(function () {
            var $this = $(this);
            var productid = $(this).attr("data-productid");

            $.ajax({
                type: 'get',
                url: "/activity/renew588/pointOrderExchange?&productId=" + productid + "&actcode=" + actcode,
                dataType: 'json',
                success: function (data) {
                    if (data.code == "200") {
                        $(".bg").show();
                        if (productid == "599") {
                            $("#pop3").hide();
                            $("#pop4").show();
                        } else {
                            $("#pop5").hide();
                            $("#pop6").show();
                        }
                    } else {
                        layer.msg(data.msg);
                        return;
                    }
                }
            });
        });
    });
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
    function GetExternal() {
        return window.external.EmObj;
    }

    function PC_JH(type, c) {
        try {
            var obj =
                GetExternal();
            return obj.EmFunc(type, c);
        } catch (e) {
        }
    }
    // 检查用户是否有权限参与
    function checkPermission(pid) {
        if (pid != "888010000" && pid != "888010400") {
            layer.msg("本活动仅限小智盈用户参与");
            return false;
        }
        return true;
    }
    //跳转 IM
    function goIM(fromname) {
        var b = new Base64();
        var ret = PC_JH('EM_FUNC_START_IM', '0,AC20230131,' + b.encode(fromname));
    }
</script>
<script>
    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";
    var al7 = "0";
    var al8 = "0";



    $(window).scroll(function(e){

        if(($(window).scrollTop() > 2235) && (al1 == "0")){
            $("[name='al1']").attr("src","https://www.emoney.cn/dianjin/mf/bs/bs.html");
            al1 = "1";
        }
        if(($(window).scrollTop() > 2969) && (al2 == "0")){
            $("[name='al2']").attr("src","https://www.emoney.cn/dianjin/mf/m-ggsfz/ggsfz.html");
            al2 = "1";
        }
        if(($(window).scrollTop() > 3706) && (al3 == "0")){
            $("[name='al3']").attr("src","https://www.emoney.cn/dianjin/mf/sslt3/sslt.html");
            al3 = "1";
        }
        if(($(window).scrollTop() > 4443) && (al4 == "0")){
            $("[name='al4']").attr("src","https://www.emoney.cn/dianjin/mf/m-qsdd/qsdd.html");
            al4 = "1";
        }
        if(($(window).scrollTop() > 5182) && (al5 == "0")){
            $("[name='al5']").attr("src","https://www.emoney.cn/dianjin/mf/dblb2/dblb.html");
            al5 = "1";
        }
        if(($(window).scrollTop() > 5909) && (al6 == "0")){
            $("[name='al6']").attr("src","https://www.emoney.cn/dianjin/mf/tydp/tydp.html");
            al6 = "1";
        }

    });
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20230131";//模块名称(焦点图2)
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
