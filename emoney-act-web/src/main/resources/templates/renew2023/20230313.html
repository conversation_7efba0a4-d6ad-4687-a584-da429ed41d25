<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2023/20230313/css/style.css'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"><div class="main"><div class="txt1">1888</div>
    <a th:style="'display:' + @{(${hasCoupon200} ? 'none' : 'block')} + ''" href="javascript:;" class="btn1 an1 dh" clickkey="getCoupon" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a><!--无券点击赠送优惠券-->
    <!--状态2-->
    <a th:style="'display:' + @{(${hasCoupon200} ? 'block' : 'none')} + ''" href="javascript:void(0)" name="payurl" target="_blank" class="btn1h an1" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a><div th:style="'display:' + @{(${hasCoupon200} ? 'block' : 'none')} + ''" class="ico">恭喜您领取成功，请在领券后15天内使用！</div>
    <marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="4" direction="left" class="txt2">
        恭喜！<span id="em1"></span>*** 使用200元立减券，￥88元续费1年会员！
        恭喜！<span id="em2"></span>*** 使用200元立减券，￥88元续费1年会员！
        恭喜！<span id="em3"></span>*** 使用200元立减券，￥88元续费1年会员！
        恭喜！<span id="em4"></span>*** 使用200元立减券，￥88元续费1年会员！
        恭喜！<span id="em5"></span>*** 使用200元立减券，￥88元续费1年会员！
        恭喜！<span id="em6"></span>*** 使用200元立减券，￥88元续费1年会员！
        恭喜！<span id="em7"></span>*** 使用200元立减券，￥88元续费1年会员！
        恭喜！<span id="em8"></span>*** 使用200元立减券，￥88元续费1年会员！
        恭喜！<span id="em9"></span>*** 使用200元立减券，￥88元续费1年会员！
        恭喜！<span id="em10"></span>*** 使用200元立减券，￥88元续费1年会员！
        恭喜！<span id="em11"></span>*** 使用200元立减券，￥88元续费1年会员！
        恭喜！<span id="em12"></span>*** 使用200元立减券，￥88元续费1年会员！</marquee></div></div>
<div class="img_3"><div class="main"><a href="javascript:void(0)" name="payurl" target="_blank" class="btn2" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div></div>
<div class="img_4" id="al1"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al1"></iframe></div></div>
<div class="img_5" id="al2"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al2"></iframe></div></div>
<div class="img_6" id="al3"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al3"></iframe></div></div>
<div class="img_7" id="al4"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al4"></iframe></div></div>
<div class="img_8" id="al5"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al5"></iframe></div></div>
<div class="img_9"  id="al6"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al6"></iframe></div></div>
<div class="img_10">
    <div class="main"><div class="hdgz">1、活动参与对象：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。<br />
        2、福利发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限。<br />
        3、优惠券规则：持本活动有效优惠券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用。</div></div></div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><img th:src="@{${staticPath}+'static/renew2023/20230313/images/pf.png'}" alt="" usemap="#Map" border="0"><a href="javascript:void(0)" name="payurl" target="_blank" class="btn3 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a>
    <map name="Map" id="Map">
        <area shape="rect" coords="18,68,178,129" href="#al1" />
        <area shape="rect" coords="19,130,177,192" href="#al2" />
        <area shape="rect" coords="19,192,178,255" href="#al3" />
        <area shape="rect" coords="21,255,178,326" href="#al4" />
        <area shape="rect" coords="20,327,177,388" href="#al5" />
        <area shape="rect" coords="21,389,177,456" href="#al6" />
    </map></div>
<div class="bott" th:style="'display:' + @{(${hasCoupon200} ? 'none' : 'block')} + ''">
    <div class="main"><div class="ico2">限时领￥200优惠券</div><a href="javascript:void(0)" name="payurl" target="_blank" class="btn4 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div>
</div>
<!--状态2-->
<div class="bott2" th:style="'display:' + @{(${hasCoupon200} ? 'block' : 'none')} + ''">
    <div class="main"><div class="ico2">￥200优惠券已生效</div><a href="javascript:void(0)" name="payurl" target="_blank" class="btn4 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div>
</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc5" style="display: none;"><a href="javascript:;" class="close"></a><a href="javascript:void(0)" class="btn5" clickkey="getCoupon" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div><!--抢券-->
    <div class="tc6" style="display: none;"><a href="javascript:;" class="close"></a><a href="javascript:void(0)" name="payurl" target="_blank" class="btn6" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:inline="javascript">
    var www="../";
    var uid = "";
    var actcode = /*[[${actcode}]]*/ "0"
    $(document).ready(function () {
        var pid = "";
        var uname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        var hasCoupon = false;

        if (!channelcode) {
            channelcode = "A12050";
        }
        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
            hasCoupon =  /*[[${hasCoupon200}]]*/ false;
        }
        RandEMCode();
        InitCount();

        var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=99&channelcode=" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search.replace(/sid=\d{0,10}&/gi, '').slice(1);
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20230401");
        });
        //领取200优惠券
        $(".btn5,.btn1").click(function (){
            if (!checkPermission(pid)) {
                return false;
            }

            $.post("sendcoupon", {
                actcode:actcode,
                activityID:"cp-1220210154041730",
                couponprice:200
            }, function (data) {
                if (data == null) {
                    layer.msg("领取异常，请稍后重试！");
                    return false;
                }
                if (data.code == 200) {
                    $(".bg").show();
                    $(".tc5").hide();
                    $(".tc6").show();

                    $(".bott2").show();
                    $(".bott").hide();

                    $(".btn1h").show();
                    $(".btn1").hide();

                    addCount(actcode,uid);
                } else {
                    layer.msg(data.msg);
                }
            });
        });

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        } else {
            if (!checkPermission(pid)) {
                return false;
            }
            if(!hasCoupon){
                $(".bg").show();
                $(".tc5").show();
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20230401");
        }

        $(".close").click(function (){
            $(".bg").hide();
            $(".tc4").hide();
            $(".tc5").hide();
        });
    });
    function RandEMCode() {
        for (var y = 0; y < 13; y++) {
            var code = Math.floor(Math.random() * (999 - 100 + 1) + 100);
            var emcode = "Emy" + code;

            $("#em" + y).html("Emy" + code);
        }
    }
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }

    function addCount(actcode,uid){
        //名额-1
        $.ajax({
            type: 'get',
            url: '/activity/user/addcountbyactcode?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                uid: uid ,
                value: "1"
            },
            success: function (data) {
                if(data.code=="200"){
                }
            }
        });
    }
    function InitCount() {
        var initCount = 1888;
        $.ajax({
            type: 'get',
            url: '/activity/user/getcountbyactcode?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if (data.code == "200") {
                    var num=0;
                    if (!!data.data) {
                        num = data.data.split(",")[0];
                    }
                    var usedCount = parseInt(num);
                    $(".txt1").html(initCount - parseInt(usedCount));//剩余席位

                    setTimeout("InitCount()", 60000 * 5);
                }
            }
        });
    }
    // 检查用户是否有权限参与
    function checkPermission(pid) {
        if (pid != "888010000" && pid != "888010400") {
            layer.msg("本活动仅限小智盈用户参与");
            return false;
        }
        return true;
    }
</script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script>
    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";
    var al7 = "0";
    var al8 = "0";

    $(window).scroll(function(e){

        if(($(window).scrollTop() > 535) && (al1 == "0")){
            $("[name='al1']").attr("src","https://www.emoney.cn/dianjin/mf/bs/bs.html");
            al1 = "1";
        }
        if(($(window).scrollTop() > 1269) && (al2 == "0")){
            $("[name='al2']").attr("src","https://www.emoney.cn/dianjin/mf/m-ggsfz/ggsfz.html");
            al2 = "1";
        }
        if(($(window).scrollTop() > 1806) && (al3 == "0")){
            $("[name='al3']").attr("src","https://www.emoney.cn/dianjin/mf/sslt3/sslt.html");
            al3 = "1";
        }
        if(($(window).scrollTop() > 2743) && (al4 == "0")){
            $("[name='al4']").attr("src","https://www.emoney.cn/dianjin/mf/m-qsdd/qsdd.html");
            al4 = "1";
        }
        if(($(window).scrollTop() > 3382) && (al5 == "0")){
            $("[name='al5']").attr("src","https://www.emoney.cn/dianjin/mf/dblb2/dblb.html");
            al5 = "1";
        }
        if(($(window).scrollTop() > 3909) && (al6 == "0")){
            $("[name='al6']").attr("src","https://www.emoney.cn/dianjin/mf/tydp/tydp.html");
            al6 = "1";
        }

    });
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20230313";//模块名称(焦点图2)
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
