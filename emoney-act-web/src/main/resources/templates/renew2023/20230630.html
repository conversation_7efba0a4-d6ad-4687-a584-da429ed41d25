<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2023/20230630/css/style.css?r=20230630'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"><div class="main"><div class="djs">4999</div></div></div>
<div class="img_2"></div>
<div class="img_3"></div>
<div class="img_4"><div class="main"><a href="" class="btn1 an1 dh" name="payurl288" target="_blank" clickkey="btn288" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="288支付"></a><a href="" class="btn1 an2 dh" name="payurl158" target="_blank"clickkey="btn158" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="158支付"></a><a href="#" class="btn3 an4 dh" name="payurl158" target="_blank"></a></div></div>
<div class="img_5"><div class="main">
    <div class="txt2" style="margin-top: -30px;"><a href="#a1" class="b1"></a> <a href="#a2" class="b2"></a> <a href="#a3" class="b3"></a> <a href="#a4" class="b4"></a> <a href="#a5" class="b5"></a> <a href="#a6" class="b6"></a> </div>
</div></div>
<div class="img_6" id="a1"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al1"></iframe></div></div>
<div class="img_7" id="a2"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al2"></iframe></div></div>
<div class="img_8" id="a3"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al3"></iframe></div></div>
<div class="img_9" id="a4"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al4"></iframe></div></div>
<div class="img_10" id="a5"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al5"></iframe></div></div>
<div class="img_11" id="a6"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al6"></iframe></div></div>
<a href="###" class="btn2 dh"></a>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="bg2"><a href="#" class="btn3 an3"></a><!--状态2<a href="#" class="btn3h an3"></a>--></div>
<a href="##" class="server" id="goIM"></a>
<div class="bott">
    <div class="main"><a href="" class="btn3 dh"></a>
        <!--状态2<a href="" class="btn3 dh"></a>--><a href="" class="btn4 dh" name="payurl158" target="_blank" clickkey="btn158" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="158支付"></a></div>
</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc4" style="display: none;"><a href="##" class="close"></a>
        <span class="black">1、活动参与对象：</span>仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。<br />
        <span class="black">2、福利发放规则：</span>完成续费和适当性测评后开通【智盈软件使用期】权限。<br />
        <span class="black">3、优惠券规则：</span>领券后15天有效。持本活动有效优惠券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用。<br />
        <span class="black">4、积分规则：</span>续费返还积分，在完成续费和适当性测评后发放，且为冻结状态，适当性测评完成30天后，积分解冻，可在积分商城使用。
    </div>
    <div class="tc5" style="display: none;"><a href="##" class="close"></a><a href="###" target="_blank" name="payurl158" clickkey="btn158" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="158支付"><img th:src="@{${staticPath}+'static/renew2023/20230630/images/tc1.png'}" alt=""></a></div>
</div>


<input type="hidden" id="hid_actcode" th:value="${actcode}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var www="../";
    var uid = "";
    var actcode = /*[[${actcode}]]*/ "0"
    $(document).ready(function () {
        var pid = "";
        var uname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        var hasCoupon = false;

        if (!channelcode) {
            channelcode = "A12050";
        }
        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
            hasCoupon =  /*[[${hasCoupon30}]]*/ false;
        }
        InitCount();

        var payUrl158 = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=0&businessType=biztyp-xzyxf&" + location.search.slice(1);

        if (!token) {
            payUrl158 += "&phoneEncrypt=" + uname + "&UPcid=" + uid;
        }
        $("[name=payurl158]").attr("href", payUrl158);
        $("[name=payurl288]").attr("href", payUrl158);

        $("[name=payurl158]").click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            //记录点击人数
            addCount(actcode,uid);
        });

        $("#goIM").click(function () {
            goIM("小智盈续费领福利活动");
        });

        //领取30优惠券
        $(".an3,.an4").click(function (){
            if (!checkPermission(pid)) {
                return false;
            }
            if($(this).hasClass("btn3h")){
                return false;
            }

            $.post("sendcoupon", {
                actcode:actcode,
                activityID:"cp-1230625135802788",
                couponprice:30
            }, function (data) {
                if (data == null) {
                    layer.msg("领取异常，请稍后重试！");
                    return false;
                }
                if (data.code == 200) {
                    $(".an3,.an4").removeClass("btn3").addClass("btn3h");

                    $(".bg").show();
                    $(".tc5").show();
                } else {
                    layer.msg(data.msg);
                }
            });
        });

        $(".btn2").click(function (){
            $(".bg").show();
            $(".tc4").show();
        });

        $(".close").click(function (){
            $(".bg").hide();
            $(".tc4").hide();
            $(".tc5").hide();
        });
        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        } else {
            if (!checkPermission(pid)) {
                return false;
            }
            if(hasCoupon){
                $(".an3,.an4").removeClass("btn3").addClass("btn3h");
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20230701");
        }
    });
    //跳转 IM
    function goIM(fromname) {
        var b = new Base64();
        var ret = PC_JH('EM_FUNC_START_IM', '0,AC20230601,' + b.encode(fromname));
    }
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }

    function addCount(actcode,uid){
        //名额-1
        $.ajax({
            type: 'get',
            url: '/activity/user/addcountbyactcode?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                uid: uid ,
                value: "1"
            },
            success: function (data) {
                if(data.code=="200"){
                }
            }
        });
    }
    function InitCount() {
        var initCount = 5000;
        $.ajax({
            type: 'get',
            url: '/activity/user/getcountbyactcode?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if (data.code == "200") {
                    var num=0;
                    if (!!data.data && data.data !=",") {
                        num = data.data.split(",")[0];

                    }
                    var usedCount = parseInt(num);
                    $(".djs").html(initCount - parseInt(usedCount));//剩余席位

                    setTimeout("InitCount()", 60000 * 5);
                }
            }
        });
    }
    // 检查用户是否有权限参与
    function checkPermission(pid) {
        if (pid != "888010000" && pid != "888010400") {
            layer.msg("本活动仅限小智盈用户参与");
            return false;
        }
        return true;
    }

    function GetExternal() {
        return window.external.EmObj;
    }

    function PC_JH(type, c) {
        try {
            var obj =
                GetExternal();
            return obj.EmFunc(type, c);
        } catch (e) {
        }
    }
</script>
<script>
    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";
    var al7 = "0";
    var al8 = "0";

    $(window).scroll(function(e){

        if(($(window).scrollTop() > 2235) && (al1 == "0")){
            $("[name='al1']").attr("src","https://www.emoney.cn/dianjin/mf/bs/bs.html");
            al1 = "1";
        }
        if(($(window).scrollTop() > 2969) && (al2 == "0")){
            $("[name='al2']").attr("src","https://www.emoney.cn/dianjin/mf/m-ggsfz/ggsfz.html");
            al2 = "1";
        }
        if(($(window).scrollTop() > 3706) && (al3 == "0")){
            $("[name='al3']").attr("src","https://www.emoney.cn/dianjin/mf/sslt3/sslt.html");
            al3 = "1";
        }
        if(($(window).scrollTop() > 4443) && (al4 == "0")){
            $("[name='al4']").attr("src","https://www.emoney.cn/dianjin/mf/m-qsdd/qsdd.html");
            al4 = "1";
        }
        if(($(window).scrollTop() > 5182) && (al5 == "0")){
            $("[name='al5']").attr("src","https://www.emoney.cn/dianjin/mf/dblb2/dblb.html");
            al5 = "1";
        }
        if(($(window).scrollTop() > 5909) && (al6 == "0")){
            $("[name='al6']").attr("src","https://www.emoney.cn/dianjin/mf/tydp/tydp.html");
            al6 = "1";
        }

    });
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20230630";//模块名称(焦点图2)
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
