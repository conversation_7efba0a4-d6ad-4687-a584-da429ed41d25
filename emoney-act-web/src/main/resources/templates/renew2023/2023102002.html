<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2023/20231020/css/style.css?r=20231020'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img2_1">
    <div class="main">
        <div class="ico2">
            <div class="ico3"></div>
        </div>
        <div class="txt2">0天</div>
        <a id="btn_gotostudy" href="javascript:;" class="btn3 dh" clickkey="btngotostudy" th:clickdata="(${isLogin}?${loginUserInfo.uid}:'')">去学习</a>
        <div class="txt4">满<span class="red2" id="mydays">7天</span>可获得<span class="red2" id="adddays">90天</span>智盈使用期</div>
        <div class="txt5"></div>
    </div>
</div>
<div class="img2_2"></div>
<div class="img2_3">
    <div class="main">
        <div class="txt3">
            <ul>
<!--                <li>2023年9月23日  已完成当日学习挑战！</li>-->
            </ul>
</div></div></div>
<div class="hdgz">
    <ul>
        <li>活动参与对象:仅限智盈付费用户参加，其他产品用户不参与本活动。</li>
        <li>活动次数限制:<br/>
            ①若支付后退货，则不可再次参与本活动。<br/>
            ②同期仅可选择一个挑战进行支付参与，且挑战失败后才可再次支付参与。</li>
        <li>立得福利发放规则:完成支付和适当性测评后开通智盈软件使用期权限。</li>
        <li>完成支付和适当性后请前往活动中心开启挑战。</li>
        <li>挑战赛参与权限：<br/>
            ①限完成指定续费活动的小智盈用户参与。<br/>
            ②用户EM账号过期，则无法参与挑战。<br/>
            ③仅限使用续费时的手机号参与挑战。续费后更换手机号，则无法参与挑战。</li>
        <li>挑战周期自首次进入挑战页面起计算。</li>
        <li>本活动最终解释权归益盟股份有限公司所有。</li>
        <li>活动开展期间，如出现不可抗力等情况，如发生自然灾害，网络攻击，电信故障停机维护、疫情等本平台免于承担责任。</li>
    </ul>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc4" style="display: none;"><a href="javascript:;" class="close"></a><a href="javascript:;" class="btn dh"></a><div class="txt">1234</div>
    </div>
    <div class="tc5" style="display: none;"><a href="javascript:;" class="close"></a><a href="/activity/renew2023/index102001" class="btn dh"></a>
    </div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/renew2023/20231020/js/index.js'}"></script>
<script type="text/javascript">var www="../";</script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20231020";//模块名称(焦点图2)
    var Remark = "学习挑战赛";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
