<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="快速上手择时择基，把握结构行情波段机遇，择时大波段，决战领涨基">
    <link th:href="@{${staticPath}+'static/renew2023/20230601/style.css?r=20230630'}" rel="stylesheet" type="text/css" />
    <style>
        .tc_1,.tc_2,.tc_3,.tc_4,.tc_5,.tc_6,.tc_7,.tc_8,.tc9{display: none}
    </style>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"><div class="main"><div class="toptxt">今日已有<span class="f28">8888</span>位用户成功领取福利！</div></div></div>
<div class="img_2"></div>
<div class="img_3">
    <div class="box">
        <div class="box_1"></div>
        <div class="box_2">
            <div class="bfcnt" th:each="collect,iterStat : ${benefitList}">
                <div class="day_a fl" th:if="${iterStat.count == 16}"><!--第一种形态-->
                    <div class="font1" th:text="'第'+${collect.dayOfMonth}+'天'">第1天</div>
                    <div class="font2">最多送<br>365天使用期</div>
                    <a class="btn_1a dh" id="btn_active" href="javascript:;" clickkey="btn_16" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'立即激活_'+${dayOfMonth}">立即激活</a>
                </div>
                <div class="day_a fl" th:if="${dayOfMonth == collect.dayOfMonth && iterStat.count != 16}"><!--第一种形态-->
                    <div class="font1" th:text="'第'+${collect.dayOfMonth}+'天'">第1天</div>
                    <div class="font2" th:text="${collect.benefitName}">3天<br>北上资金</div>
                    <a href="javascript:;" class="btn_1a dh" clickkey="getbenefitbtn" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'立即领取_'+${dayOfMonth}" name="btn_getbenefit" th:data-benefitid="${collect.id}" th:data-day="${collect.dayOfMonth}" th:data-icon="${collect.iconName}" th:data-type="${collect.benefitType}" th:unless="${collect.hasBenefitRecord}">今日领取</a><!--领取按钮第一种形态-->
                    <a href="javascript:;" class="btn_1b" th:style="'display:' + @{(${collect.hasBenefitRecord} ? 'block' : 'none')} + ''">已领取</a><!--领取按钮第二种形态-->
                </div>
                <div class="day_b fl" th:unless="${dayOfMonth == collect.dayOfMonth || iterStat.count == 16}"><!--第二种形态 -->
                    <div class="font1" th:text="'第'+${collect.dayOfMonth}+'天'">第1天</div>
                    <div class="icon"><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_type'+${collect.iconName}+'.png'}"></div>
                    <div th:class="@{(${collect.benefitName=='3天顶级机构持股'} ? 'font4':'font3')}" class="font3" th:text="${collect.benefitName}">3天北上资金</div>
                </div>
            </div>
            <div class="clear"></div>
        </div>

        <div class="box_3">
            <div class="btn3">
                <a href="javascript:void(0)" class="btn" style="display: none">∧ 点击收起</a>
                <a href="javascript:void(0)" class="btn2" >∨ 查看全部奖励</a></div>
        </div>
        <div class="box_4"><div class="ban"><a id="bannerurl" target="_blank" href=""><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bannerNew.png?r=20230630'}" alt=""></a></div></div>
        <div class="box_5">
            <ur>
             </ur>
        </div>
        <a href="javascript:void(0)" class="btn_1c dh"></a>

    </div>
</div>

<div class="img_4">
    <div class="main">
        <a class="btn_2 dh" href="" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'底部漂浮立即续费_'+${dayOfMonth}"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_4.png?r=20230630'}" class="bg_4">
    </div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #FF5348; ">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<a class="fc_1" href="javascript:void(0);" id="goIM"></a>
<a class="fc_2" href="" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'右侧浮窗36个月智盈_'+${dayOfMonth}"></a>
<div class="h"><a href="javascript:void(0)" class="close"></a>
    <div class="hdgz"><a href="javascript:void(0)" class="close"></a>
        <div class="xynr2"><div class="f30">活动规则</div>
            1、活动参与对象：仅限智盈付费用户参加1次，智盈大师或其他产品用户不参与本活动。<br>
            2、福利发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限。<br>
            3、每日福利领取规则：当日福利仅限当日领取，过期则无法领取。<br>
            4、积分规则：页面领取积分立即到账。<br>
            5、优惠券规则：持本活动有效优惠券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用。<br>
            6、打卡活动参与权限：<br>
            　①限完成指定续费活动的小智盈用户参与。<br>
            　②仅限使用续费时的手机号参与打卡。续费后更换手机号，则无法完成打卡。<br>
            　③用户EM账号过期，则无法打卡。<br>
            7、打卡及使用期赠送规则：<br>
            　①登录打卡页面视为有效打卡；一个自然周内打卡满3天，可领取7天使用期。<br>
            　②打卡期限自第一次打卡之日起计算，打卡活动最多送365天使用期，可与续费所得使用期叠加。<br>
            　③若参加多轮打卡活动，则打卡周期累计顺延。<br>
            　④每周一0点刷新打卡天数，未领取的使用期自动作废。<br>
            　⑤若参加打卡后退单，赠送的使用期也随之失效。<br>
            8、本活动最终解释权归益盟股份有限公司所有。
        </div>
    </div>
</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div name="tc" class="tc_1" id="pop_2_sbql"><!--三步擒龙-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc_sbql.png'}">
        <a href="" class="tc-btn" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'三步擒龙窗口点击_'+${dayOfMonth}"></a>
    </div>
    <div name="tc" class="tc_1" id="pop_2_bszj"><!--北上资金-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc1.png'}">
        <a href="" class="tc-btn" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'北上资金窗口点击_'+${dayOfMonth}"></a>
    </div>
    <div name="tc" class="tc_2" id="pop_2_lwdx"><!--量王叠现-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc2.png'}">
        <a href="" class="tc-btn" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'量王叠现窗口点击_'+${dayOfMonth}"></a>
    </div>
    <div name="tc" class="tc_3" id="pop_2_hxtc"><!--火线题材-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc3.png'}">
        <a href="" class="tc-btn" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'火线题材窗口点击_'+${dayOfMonth}"></a>
    </div>
    <div name="tc" class="tc_4" id="pop_2_djjg"><!--顶级机构持股-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc4.png'}">
        <a href="" class="tc-btn" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'顶级机构窗口点击_'+${dayOfMonth}"></a>
    </div>
    <div name="tc" class="tc_5" id="pop_2_wxyb"><!--五星研报-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc5.png'}">
        <a href="" class="tc-btn" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'五星研报窗口点击_'+${dayOfMonth}"></a>
    </div>
    <div name="tc" class="tc_1" id="pop_2_hymx"><!--行业明显-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc6.png'}">
        <a href="" class="tc-btn" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'行业明星窗口点击_'+${dayOfMonth}"></a>
    </div>
    <div name="tc" class="tc_1" id="pop_2_kxgs"><!--k线故事-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc_kxgs.png'}">
        <a href="" class="tc-btn" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'k线故事窗口点击_'+${dayOfMonth}"></a>
    </div>
    <div name="tc" class="tc_1" id="pop_2_gzd"><!--估值带-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc_gzd.png'}">
        <a href="" class="tc-btn" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'估值带窗口点击_'+${dayOfMonth}"></a>
    </div>
    <div name="tc" class="tc_6" id="pop_0"><!--点击当日续费-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc7.png'}" usemap="#Map">
        <map name="Map">
            <area shape="rect" coords="94,489,348,546" href="" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'打卡窗口立即续费_'+${dayOfMonth}">
        </map>
    </div>
    <div name="tc" class="tc_7" id="pop_1_60"><!--无优惠券积分小于100-60积分换30券-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc8.png'}" usemap="#Map2">
        <map name="Map2">
            <area shape="rect" coords="36,443,434,539" href="javascript:;" data-productid="598" name="btn_exchange" clickkey="btn_exchange" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'30元券立即兑换_'+${dayOfMonth}">
        </map>
        <div class="f1">您的账户有 <span class="f2">8888</span> 积分</div>
    </div>
    <div name="tc" class="tc_7" id="pop_1_100"><!--无优惠券积分大于100-100积分换100券-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc9.png'}" usemap="#Map3">
        <map name="Map3">
            <area shape="rect" coords="39,447,425,543" href="javascript:;" data-productid="599" name="btn_exchange" clickkey="btn_exchange" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'100元券立即兑换_'+${dayOfMonth}">
        </map>
        <div class="f1">您的账户有 <span class="f2">8888</span> 积分</div>
    </div>
    <div name="tc" class="tc_7" id="pop_1_0"><!--有优惠券或小于60分-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc10.png'}" usemap="#Map4">
        <map name="Map4">
            <area shape="rect" coords="40,449,433,543" href="" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'继续参与_'+${dayOfMonth}">
        </map>
        <div class="f1">您的账户有 <span class="f2">8888</span> 积分</div>
    </div>
    <div name="tc" class="tc_8" id="pop_1_60_1"><!--30元券兑换成功-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc11.png?r=20230630'}" usemap="#Map5">
        <map name="Map5">
            <area shape="rect" coords="27,479,417,579" href="" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'30元券兑换成功立即使用_'+${dayOfMonth}">
        </map>
    </div>
    <div name="tc" class="tc_8" id="pop_1_100_1"><!--100元券兑换成功-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc12.png?r=20230630'}" usemap="#Map6">
        <map name="Map6">
            <area shape="rect" coords="30,489,417,577" href="" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'100元券兑换成功立即使用_'+${dayOfMonth}">
        </map>
    </div>
    <div name="tc" class="tc_8" id="pop_345"><!--优惠券、使用期、行业深度报告窗口-->
        <a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230601/images/bg_tc13.png'}" usemap="#Map7">
        <map name="Map7">
            <area shape="rect" coords="37,478,408,567" data-type="" href="" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" th:clickremark="'7天使用期去使用_'+${dayOfMonth}">
        </map>
    </div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var www="../";
    var uid = "";
    var actcode = /*[[${actcode}]]*/ "0"
    var dayOfMonth = /*[[${dayOfMonth}]]*/ "0"
    var actcode_day = actcode + dayOfMonth;
    $(document).ready(function(){
        var pid = "",uname = "",realname = "",maskname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");

        if (!channelcode) {
            channelcode = "A12050";
        }

        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
        }

        InitCount();
        InitBenefitStatus();

        var payUrl = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=0&businessType=biztyp-xzyxf";
        var bannerPayUrl = "https://appstatic.emoney.cn/ymstock/payment-middle/?goodsPid=888010000&groupCode=4&businessType=biztyp-xzyxf";
        $("#bannerurl").click(function (){
            goToPay($(this),bannerPayUrl,uname,uid);
        });
        $("[name=payurl]").click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            if(!!$(this).attr("data-type") && $(this).attr("data-type") == "4"){
                //跳转积分
                OpenPoint();
                return false;
            }
            goToPay($(this),payUrl,uname,uid);
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20230601");
        });
        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        } else {
            if (!checkPermission(pid)) {
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20230601");
            getbenefitrecord(actcode);
        }

        $("#btn_active").click(function (){
            $(".bg").show();
            $("#pop_0").show();
        });

        //领取福利
        $("[name=btn_getbenefit]").click(function () {
            var _this = $(this);
            var benefitId = _this.attr("data-benefitid");
            var benefitType = _this.attr("data-type");
            var dayofmonth = _this.attr("data-day");
            var iconName = _this.attr("data-icon");

            $.ajax({
                type: 'get',
                url: "/activity/renew2023/getbenefit20230601",
                data: {"benefitid": benefitId, "actcode": actcode, "day": dayofmonth, "source": "pc"},
                dataType: 'json',
                success: function (data) {
                    if (data.code == "200") {
                        _this.hide();$(".btn_1b").show();

                        $(".bg").show();
                        if (benefitType == "1") {
                            //积分
                            getcouponpop(actcode);
                        } else if (benefitType == "2") {
                            //显示不同特权窗口
                            $("#pop_" + iconName).show();
                        } else {
                            $("#pop_345").show();
                            $("#pop_345 area").attr("data-type", benefitType);
                        }
                        getbenefitrecord(actcode);
                        addCount(actcode_day,uid);
                    } else {
                        layer.msg(data.msg);
                        return;
                    }
                }
            });
        });

        //立即兑换
        $("[name=btn_exchange]").click(function () {
            var $this = $(this);
            var productid = $(this).attr("data-productid");

            $.ajax({
                type: 'get',
                url: "/activity/renew588/pointOrderExchange?&productId=" + productid + "&actcode=" + actcode,
                dataType: 'json',
                success: function (data) {
                    if (data.code == "200") {
                        $(".bg").show();
                        if (productid == "598") {
                            $(".tc7").hide();
                            $("#pop_1_60_1").show();
                        } else {
                            $(".tc7").hide();
                            $("#pop_1_100_1").show();
                        }
                    } else {
                        layer.msg(data.msg);
                        return;
                    }
                }
            });
        });
        $("#goIM").click(function () {
            goIM("小智盈续费领福利活动");
        });

        $(".btn").click(function(){
            $(".box_2").css("height","380px");
            $(".btn2").show();
            $(".btn").hide();
            InitBenefitStatus();
        });
        $(".btn2").click(function(){
            $(".box_2").css("height","1580px");
            $(".btn2").hide();
            $(".btn").show();
            $(".box_2 .bfcnt").show();
        });

        $(".close").click(function(){
            $(".bg").hide();
            $("[name=tc]").hide();
            $(".h").hide();
        });
        $(".btn_1c").click(function(){
            $(".h").show();
            $(".hdgz").show();
        });
    });
    function goToPay($obj,payUrl,uname,uid){
        if (!!GetExternal()) {
            $obj.removeAttr("target");
            PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "15," + payUrl);
        } else {
            payUrl += "&phoneEncrypt=" + uname + "&UPcid=" + uid;
            $obj.attr("href", payUrl);
        }
    }
    function getbenefitrecord(actcode){
        $.ajax({
            type: 'get',
            url: "/activity/renew2023/getbenefitrecord",
            data: {"actcode": actcode},
            dataType: 'json',
            success: function (data) {
                if (data.code == "200") {
                    $(".box_5 ur li").remove();
                    var obj = data.data;
                    var lihtml="";
                    for(var i=0;i<obj.length;i++) {
                        var item = obj[i];
                        lihtml += "<li>" + item.showTime + "参加福利活动，成功领取" + item.benefitName + "！</li>";
                    }
                    $(".box_5 ur").append(lihtml);
                }
            }
        });
    }
    function getcouponpop(actcode){
        $.ajax({
            type: 'get',
            url: "/activity/renew2023/getcouponpop",
            data: {"actcode": actcode},
            dataType: 'json',
            success: function (data) {
                if (data.code == "200") {
                    var retobj = data.data.split("||");
                    $("#" + retobj[0]).show();
                    $(".f2").html(retobj[1]);
                } else {
                    layer.msg("查询优惠券或可用积分异常");
                }
            }
        });
    }
    function addCount(actcode,uid){
        //名额-1
        $.ajax({
            type: 'get',
            url: '/activity/user/addcountbyactcode?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                uid: uid ,
                value: "1"
            },
            success: function (data) {
                if(data.code=="200"){
                }
            }
        });
    }
    function InitBenefitStatus() {
        var _benefitObj = $(".box_2 .bfcnt");
        var designDay = 4;
        var day = parseInt(dayOfMonth);
        var ys = day % designDay;
        var mulival = Math.floor(day / designDay);
        if (ys == 0) {
            mulival--;
        }
        for (var i = 0; i < designDay * mulival; i++) {
            _benefitObj.eq(i).hide();
        }
    }
    function InitCount() {
        $.ajax({
            type: 'get',
            url: '/activity/user/getcountbyactcode?actcode=' + actcode_day,
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if (data.code == "200") {
                    var num=0;
                    if (!!data.data) {
                        num = data.data.split(",")[0];
                    }
                    var usedCount = parseInt(!!num?num:0);
                    $(".f28").html(1435 + parseInt(usedCount));//剩余席位

                    setTimeout("InitCount()", 60000 * 5);
                }
            }
        });
    }
    // 检查用户是否有权限参与
    function checkPermission(pid) {
        if (pid != "888010000" && pid != "888010400") {
            layer.msg("本活动仅限小智盈用户参与");
            return false;
        }
        return true;
    }
    //打开积分窗口
    function OpenPoint() {
        try {
            PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "17,https://static.emoney.cn/ds/point/middlejump.html?middlefrom=point&popstr=myRedemption||report|");
        } catch (ex) {
        }
    }
    //跳转 IM
    function goIM(fromname) {
        var b = new Base64();
        var ret = PC_JH('EM_FUNC_START_IM', '0,**********,' + b.encode(fromname));
    }
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
    function GetExternal() {
        return window.external.EmObj;
    }

    function PC_JH(type, c) {
        try {
            var obj =
                GetExternal();
            return obj.EmFunc(type, c);
        } catch (e) {
        }
    }
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20230601";//模块名称(焦点图2)
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
