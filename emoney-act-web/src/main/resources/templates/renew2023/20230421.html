<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="快速上手择时择基，把握结构行情波段机遇，择时大波段，决战领涨基">
    <style type="text/css">
        .tc3{display: none}
    </style>
    <link th:href="@{${staticPath}+'static/renew2023/20230421/style/style.css'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="bod">
    <div class="img_1"></div>
    <div class="img_2"></div>
    <div class="img_3"><div class="main">
        <a href="javascript:void(0)" name="payurl" target="_blank" class="btn2 an3 dh popupD" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" ></a>
        <div class="txt an1"><input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 20px; height: 20px;"> <a href="javascript:void(0)" class="ys">我已阅读并同意《信息服务协议》</a></div>
        <div class="bg1" th:style="'display:' + @{(${hasCoupon100} ? 'none' : 'block')} + ''"></div>
        <!--状态2-->
        <div class="bg1h" th:style="'display:' + @{(${hasCoupon100} ? 'block' : 'none')} + ''"></div>

        <a href="javascript:void(0)" name="payurl" target="_blank" class="btn3 dh" th:style="'display:' + @{(${hasCoupon100} ? 'none' : 'block')} + ''" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" ></a>
        <!--状态2-->
        <a href="javascript:void(0)" name="payurl" target="_blank" class="btn3h dh" th:style="'display:' + @{(${hasCoupon100} ? 'block' : 'none')} + ''" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" ></a>
    </div></div>
    <div class="img_4"></div>
    <div class="img_5"></div>
    <div class="img_6"></div>
    <div class="img_7"></div>
    <div class="img_8"></div>
    <div class="img_9"></div>
    <a href="javascript:void(0)" class="btn1"></a>
    <div th:class="(${hasCoupon100} ? 'pfb' : 'pf')"><!--状态2<div class="pfb">-->
        <div class="main"><a href="javascript:void(0)" name="payurl" target="_blank" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"  class="btn2 an4 dh popupD"></a><div class="txt an2"><input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 20px; height: 20px;"> <a href="javascript:void(0)" class="ys b">我已阅读并同意《信息服务协议》</a></div></div></div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <!--100元优惠券窗口-->
    <div class="tc3" id="pop1"><a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230421/images/tc1.png'}"  alt="" usemap="#Map" border="0">
        <map name="Map" id="Map">
            <area shape="rect" coords="162,472,533,614" href="" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" />
        </map>
        <div class="txt an5"><input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 20px; height: 20px;"> <a href="javascript:void(0)" class="ys b">我已阅读并同意《信息服务协议》</a></div>
    </div>
    <!--30元优惠券窗口-->
    <div class="tc3" id="pop2"><a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230421/images/tc2.png'}" alt="" usemap="#Map2" border="0">
        <map name="Map2" id="Map2">
            <area shape="rect" coords="159,474,535,615" href="" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" />
        </map>
        <div class="txt an5"><input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 20px; height: 20px;"> <a href="javascript:void(0)" class="ys b">我已阅读并同意《信息服务协议》</a></div>
    </div>
    <!--100积分兑换100优惠券-->
    <div class="tc3" id="pop3"><a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230421/images/tc3.png'}" alt="" usemap="#Map3" border="0">
        <map name="Map3" id="Map3">
            <area shape="rect" coords="162,476,531,618" href="javascript:;" data-productid="599" name="btn_exchange" clickkey="btnExchange" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"  />
        </map>
    </div>
    <!--100元优惠券兑换成功-->
    <div class="tc3" id="pop4"><a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230421/images/tc4.png'}" alt="" usemap="#Map4" border="0">
        <map name="Map4" id="Map4">
            <area shape="rect" coords="163,480,532,620" href="" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" />
        </map>
        <div class="txt an5"><input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 20px; height: 20px;"> <a href="javascript:void(0)" class="ys b">我已阅读并同意《信息服务协议》</a></div>
    </div>
    <!--60积分兑换30优惠券-->
    <div class="tc3" id="pop5"><a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230421/images/tc5.png'}" alt="" usemap="#Map5" border="0">
        <map name="Map5" id="Map5">
            <area shape="rect" coords="160,475,535,617"  href="javascript:;" data-productid="598" name="btn_exchange" clickkey="btnExchange" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" />
        </map>
    </div>
    <!--30元优惠券兑换窗口-->
    <div class="tc3" id="pop6"><a href="javascript:void(0)" class="close"></a><img th:src="@{${staticPath}+'static/renew2023/20230421/images/tc6.png'}" alt="" usemap="#Map6" border="0">
        <map name="Map6" id="Map6">
            <area shape="rect" coords="165,479,530,621" href="" target="_blank" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"/>
        </map>
        <div class="txt an5"><input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 20px; height: 20px;"> <a href="javascript:void(0)" class="ys b">我已阅读并同意《信息服务协议》</a></div>
    </div>
</div>
<div class="h"><div class="xy"><a href="javascript:void(0)" class="close"></a>
    <div class="xynr"><div class="f30">信息服务协议</div>
        　　本协议是您与益盟操盘手以电子合同方式签署的，关于使用益盟操盘手提供的信息服务的具有法律效力的协议。<strong>您在使用信息服务前应当认真、审慎阅读本协议并充分理解各条款内容，特别是免除或者限制责任的条款、法律适用和争议解决条款。如果您对本协议条款有异议，可以选择不使用益盟操盘手提供的信息服务，在益盟操盘手规定的退货期内按照本协议及益盟操盘手的规定办理退货手续。您勾选位于页面的"确认"或与确认具有同等含义的选项之后，本协议即产生法律效力。</strong><br />
        <br />
        <strong>第一条信息服务内容</strong><br />
        1、本协议项下益盟操盘手提供的信息服务内容为“投教课程”，益盟操盘手指定讲师将通过投教课程向用户传授证券投资知识和/或益盟操盘手总结的投资理念和/或投资方法。<br />
        2、信息服务形式：益盟操盘手将通过线上益盟操盘手软件平台向您提供投教课程服务，课程具体形式应以益盟操盘手通知为准，益盟操盘手应提前通过官网或指定形式发出通知。<br />
        3、课程的相关事项（包括但不限于课程名称、课时设置、课程表、课程价格、授课讲师等事项）均以公示为准，益盟操盘手保留全部或部分修改上述内容的权利，但应提前通过官网或其他形式发出通知。<br />
        4、非投资顾问服务:益盟操盘手提供的本信息服务中不涉及投资顾问服务，不包含益盟操盘手对证券品种的走势的判断或提供证券投资的可行性分析、预测或建议或提供其他任何形式的证券投资建议，服务中涉及的个股及行情的分析仅作为案例教学用途。若您参照服务中提及的个股进行投资，由此产生的风险应由您自行承担，益盟操盘手不承担任何责任。<br />
        <br />
        <strong>第二条使用规则</strong><br />
        1、本协议项下信息服务需接入互联网方可被享受。您可以通过登录益盟操盘手指定软件平台接受本协议项下的信息服务。<br />
        2、您在登录益盟操盘手指定软件平台时，应遵守平台上公示的相关规则（包括但不限于知识产权声明等）。<br />
        3、通过益盟操盘手指定软件平台为您提供相关信息服务仅是为了方便您登录而设置，并不意味着您取得了指定软件平台或除您购买的信息服务以外其他产品的使用权限。<br />
        <br />
        <strong>第三条服务订购</strong><br />
        1、您可依据益盟操盘手相关政策，订购相关信息服务。<br />
        2、服务费用购买时益盟操盘手执行的销售政策为准。<br />
        　2.1、益盟操盘手有权根据营销计划制定优惠政策，在全国/部分城市执行。<br />
        　2.2、服务期限：您享受的服务期限以订购时的政策为准。<br />
        3、除益盟操盘手特别同意外，益盟操盘手一律以公司账户收取服务费用。<br />
        <br />
        <strong>第四条退货</strong><br />
        1、自益盟操盘手向您交付您所购买的相关服务之日起：1）10个工作日内，若您对订购的服务不满意，可无理由书面通知益盟操盘手解除协议，办理退货手续；2）超出10个工作日，除出现法律规定情形外，益盟操盘手有权拒绝受理您的退货申请。若您是再次（二次以上）购买或退货后再购买的，则不能享受本条规定的退货政策。<br />
        2、您在退货时，应同时返还下述资料，否则益盟操盘手有权拒绝办理退货手续：<br />
        　2.1、所有发票和/或收据原件；<br />
        　2.2、益盟操盘手赠送给您的所有赠品；如赠品已拆封或损坏或开始使用，您应按赠品市场价值向益盟操盘手支付补偿款，益盟操盘手将在向您退还的款项中直接扣除该部分款项，此种情形下赠品将交付您本人继续使用；<br />
        　2.3、益盟操盘手赠送或交付的其他资料、物品原件。<br />
        3、益盟操盘手将在受理您的退货申请及您退还的上述资料后15日内，将应退款项支付至您指定账户，若您指定账户并非本人账户，则您需提供书面的同意书。<br />
        <br />
        <strong>第五条您保证</strong><br />
        1、您承诺以终端用户的身份使用益盟操盘手提供的信息服务。<br />
        2、您获得的有关本协议项下益盟操盘手提供的的任何信息服务只能由您本人为本协议许可的目的而使用，不得从事下述行为：<br />
        　2.1、不在本协议规定的条款之外，使用、复制、修改、租赁、出借或转让信息服务或其中的任一部分；<br />
        　2.2、不以任何方式向任何机构或个人提供全部或部分益盟操盘手提供的信息服务；<br />
        　2.3、保证不将全部或部分益盟操盘手提供的信息服务用于开发衍生产品，或用于其它商业目的；<br />
        　2.4、保证不对益盟操盘手提供的信息服务进行进行反向工程、反编译、反汇编；<br />
        　2.5、不以违反法律的任何方式使用益盟操盘手软件平台、信息服务。<br />
        3、不侵犯益盟操盘手及其子公司、关联方的任何合法权益，包括但不限于著作权、商标权及其他合法权益。<br />
        <br />
        <strong>第六条知识产权保护</strong><br />
        1、益盟操盘手提供的信息服务（包括但不限于其中所含的图像、照片、动画、录像、录音、音乐、文字和附加程序）、附随的印刷材料、及信息服务的任何复制品、副本的著作权，均由益盟操盘手拥有。<br />
        2、本协议对您使用信息服务的授权并不意味着信息服务的著作权人对其享有的知识产权也进行了转让或进行独家授权使用。<br />
        3、您为了防止复制品的损坏而制作备份复制品的，备份复制品不得通过任何方式提供给他人使用，并在您丧失益盟操盘手提供的信息服务的使用权时，您有责任将备份复制品彻底删除或销毁。<br />
        <br />
        <strong>第七条用户信息的收集、使用、保护及披露</strong><br />
        1、您在使用益盟操盘手软件平台及相关信息服务时，同意并授权益盟操盘手为改善提升服务质量以及符合国家法律法规及监管规定的要求等目的收集您的下述个人信息：<br />
        　1.1、在您登录益盟操盘手软件平台、使用平台服务之前，您需要提供姓名、联系方式等。如您不提供前述信息，可能无法使用平台产品/服务；<br />
        　1.2、为提升服务安全性，防止您的个人信息被不法分子获取，您同意益盟操盘手记录您使用软件平台的方式及相关操作信息，例如设备MAC地址、IP地址、设备识别码、移动设备版本、产品使用习惯以及其他与软件平台服务相关的日志信息；<br />
        　1.3、为提高您对益盟操盘手产品/服务使用感受和满意度，益盟操盘手可能会收集您的操作行为信息、搜索记录、您与益盟操盘手服务团队联系时提供的信息及您参与问卷调查时向益盟操盘手发送的信息，以便进行调查研究和分析，从而进一步优化服务。<br />
        2、根据相关法律法规及国家标准，在以下情形中，益盟操盘手可能会依法收集并使用您的个人信息无需征得您的同意：<br />
        　2.1、与国家安全、国防安全直接相关的；<br />
        　2.2、与公共安全、公共卫生、重大公共利益直接相关的；<br />
        　2.3、与犯罪侦查、起诉、审判和判决执行等直接相关的；<br />
        　2.4、出于维护您或他人的生命、财产等重大合法权益但又很难得到您本人同意的；<br />
        　2.5、所收集的个人信息是您自行向社会公众公开的；<br />
        　2.6、从合法公开披露的信息中收集个人信息，例如：合法的新闻报道、政府信息公开等渠道；<br />
        　2.7、签订和履行合同所必需的；<br />
        　2.8、用于维护所提供的服务的安全稳定运行所必需的，例如：发现、处置产品或服务的故障；<br />
        　2.9、法律法规规定的其他情形。<br />
        3、益盟操盘手对于您提供的、益盟操盘手自行收集的个人信息将按照本协议予以保护、使用或者披露。益盟操盘手无需您同意即可向益盟操盘手的关联实体转让与益盟操盘手有关的部分或全部权利和义务。<br />
        4、按照您在软件平台上的行为自动追踪关于您的某些资料。在不透露您的隐私资料的前提下，益盟操盘手有权对整个用户数据库进行分析并对用户数据库进行商业上的利用。<br />
        5、您同意，益盟操盘手可在软件平台或益盟操盘手的网站上使用诸如“Cookies”的资料收集装置。<br />
        6、虽有第3条之约定，但在下列情况下，益盟操盘手有权全部或部分披露您的保密信息：<br />
        　6.1、根据法律规定，或应行政机关、司法机关要求，向第三人或行政机关、司法机关披露；<br />
        　6.2、提供独立服务且仅要求服务相关的必要信息的供应商，如印刷厂、邮递公司等；<br />
        　6.3、提供给益盟操盘手的关联实体；<br />
        　6.4、根据法律、本协议约定及益盟操盘手规定，益盟操盘手因提供服务需要披露或其他益盟操盘手认为适合披露的。<br />
        <strong>7、您充分理解并同意：为了保证您对软件平台及信息服务的正常使用并提升您的服务体验，益盟操盘手可以根据您的用户信息，通过短信、电话、邮件等各种方式与您联系并向您提供关于益盟操盘手的服务信息、风险提示信息、产品营销及活动推广通知等各类信息。</strong><br />
        8、益盟操盘手承诺将信息安全防护达到合理的安全水平。为保障您的信息安全，益盟操盘手致力于使用各种安全技术及配套的管理体系来尽量降低您的信息被泄露、毁损、误用、非授权访问、非授权披露和更改的风险。<br />
        <br />
        <strong>第八条免责条款<br />
            1、您充分理解，益盟操盘手信息服务的提供依赖于网络传输的稳定性，故益盟操盘手对于以下情形免责：<br />
            　1.1、益盟操盘手不对因您自身原因（包括但不限于电脑病毒、错误操作、网络接连错误等）导致信息服务无法正常接收的后果承担责任。<br />
            　1.2、益盟操盘手不对因任何非益盟操盘手过错原因（如因电信运营商、在线视频服务提供商、网络服务提供商等任何原因）造成的用户接受信息服务异常情况所致后果承担责任，但有义务在能力范围内积极协助相关企业、单位、部门使之恢复正常。<br />
            　1.3、益盟操盘手将不对所提供信息的完整性、及时性、准确性承担任何责任。<br />
            　1.4、由于政府禁令、政府管制、现行生效的适用法律或法规的变更、火灾、地震、动乱、战争、停电、通讯线路中断、他人蓄意破坏、黑客攻击、计算机病毒侵入或发作、电信部门技术调整之影响等不可预见、不可避免、不可克服和不可控制的事件，均属于不可抗力事件。由此造成您或任何第三人出现或蒙受任何损失或损害，益盟操盘手不承担责任。但益盟操盘手应当在不可抗力事件发生后或终止后，立即采取必要的措施，尽可能的降低或消除已经发生的损失或损害。<br />
            　1.5、因可归责于益盟操盘手的原因造成接受服务异常或信息传递中断等异常情况，益盟操盘手将尽力予以弥补，并于收到您的通知后按照中断、异常时间（不足1小时的按1小时计算）相应延长服务期，但您确认，益盟操盘手不对因任何原因造成的信息异常或信息传递异常情况所致后果（包括但不限于您投资行为所引致的后果）承担责任。<br />
            2、您与益盟操盘手工作人员私自签订的任何形式的协议或达成的任何约定，均与益盟操盘手无关，益盟操盘手不承担任何责任；若发生益盟操盘手工作人员口头或书面承诺的内容超越本协议约定，益盟操盘手不应当将此理解为本协议条款的补充或变更，而应当立即通过本协议或益盟操盘手官网公布的益盟操盘手投诉电话进行投诉。<br />
            3、除非双方特别约定，益盟操盘手对因任何原因未支付对价或免费/无偿使用益盟操盘手产品的行为及用户不承担任何义务和责任，且益盟操盘手有权在不事先通知的情况下随时终止提供上述服务。</strong><br />
        <br />
        <strong>第九条公司联系方式</strong><br />
        1、公司联系电话：<br />
        　　总机号码：021-61958888<br />
        　　客服热线：10108688<br />
        　　投诉热线：021-61954567<br />
        2、传真：021-61958887<br />
        3、邮箱：<EMAIL>（客户服务部）<br />
        4、公司联系地址：上海市杨浦区国权北路1688弄湾谷科技园B3（邮编：200438）<br />
        <br />
        <strong>第十条其他</strong><br />
        如您违反本协议所述任何条款，则益盟操盘手有权随时终止向您提供本协议项下的服务，且益盟操盘手无须向您承担任何补偿或赔偿；如因您违反本协议造成益盟操盘手损失的，则益盟操盘手有权要求您承担相应的赔偿责任。<br />
        <br />
        <strong>第十一条适用法律</strong><br />
        本协议受中华人民共和国法律管辖，如双方就本协议的相关问题发生争议，双方均可向上海市杨浦区人民法院提起诉讼。
    </div>
</div>
    <div class="hdgz"><a href="javascript:void(0)" class="close"></a>
        <div class="xynr2"><div class="f30">购买须知</div>
            1、参与对象：益盟智盈用户<br/>
            2、活动时间：2023年4月18日-5月中旬<br/>
            3、活动流程：完成支付获得在智盈软件端观看3节线上观看筹码实战课的参课资格及对应智盈软件使用期时长，课程3个月内不限次数观看回放<br/>
            4、优惠券规则：续费本活动3年智盈的用户，完成续费和适当性测评后返100元续费券。持本活动有效优惠券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用。<br/>
            5、积分规则：续费本活动3年智盈的用户，完成续费和适当性测评后发放100积分（冻结状态），适当性测评完成30天后，积分解冻，可在积分商城使用。<br/>
            6、本活动涉及课程仅用于软件基础教学，不涉及投资咨询及投资顾问服务，课程内容不构成投资建议，股市有风险，投资需谨慎<br/>
            7、本活动退货规则：10个工作日无理由退款，超过10个工作日不接受任何形式的退款<br/>
            8、本活动最终解释权归益盟股份有限公司，如有疑问请致电客服：10108688
        </div>
    </div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var flag = false;
    var downflag = "0";
    var www="../";
    var actcode = /*[[${actcode}]]*/ "0"
    $(document).ready(function () {
        var uid = "";
        var pid = "";
        var uname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        var realname = "";
        var maskname = "";
        var channelcode = getQueryString("channelcode");
        var token = getQueryString("token");
        var allpoint =  /*[[${allPoint}]]*/ "0";
        var hasCoupon100 =  /*[[${hasCoupon100}]]*/ "0";
        var hasCoupon30 =  /*[[${hasCoupon30}]]*/ "0";

        if (!channelcode) {
            channelcode = "A12050";
        }

        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
        }

        var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=107&channelcode=" + channelcode + "&" + location.search;
        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        if (isMobile) {
            payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search;
        }
        if (!token) {
            payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
        }
        $("[name=payurl]").attr("href", payUrl);
        $("[name=payurl]").click(function () {
            if (!checkPermission(pid)) {
                return false;
            }
            if (!flag){
                layer.msg('请先阅读并同意该《信息服务协议》');
                $(".txt").focus();
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20230424");
        });

        $("#goIM").click(function () {
            goIM("小智盈续费");
        });

        $(".close").click(function (){
            $(".bg").hide();
            $(".tc3").hide();
            $(".h").hide();
        });

        if (isLogin == "0") {
            //未登录
            $(".bg").show();
            $(".tc").show();
        } else {
            if (!checkPermission(pid)) {
                return false;
            }
            //推送cmp
            pushdatatocmp(!!uid ? uid : uname, "ACRenew20230424");

            //3秒后弹出对应窗口
            setTimeout(function (){
                if(hasCoupon100 && hasCoupon30){
                    $(".bg").show();
                    $("#pop1").show();
                }else if(hasCoupon30){
                    $(".bg").show();
                    $("#pop2").show();
                }else if(hasCoupon100){
                    $(".bg").show();
                    $("#pop1").show();
                }else if(!hasCoupon100 && !hasCoupon30){
                    if(parseInt(allpoint)>=100){
                        $(".bg").show();
                        $("#pop3").show();
                    }
                    else if(parseInt(allpoint)>=60){
                        $(".bg").show();
                        $("#pop5").show();
                    }
                }

            },3000);
        }
        //立即兑换
        $("[name=btn_exchange]").click(function () {
            var $this = $(this);
            var productid = $(this).attr("data-productid");

            $.ajax({
                type: 'get',
                url: "/activity/renew588/pointOrderExchange?&productId=" + productid + "&actcode=" + actcode,
                dataType: 'json',
                success: function (data) {
                    if (data.code == "200") {
                        $(".bg").show();
                        if (productid == "599") {
                            $("#pop3").hide();
                            $("#pop4").show();
                        } else {
                            $("#pop5").hide();
                            $("#pop6").show();
                        }
                    } else {
                        layer.msg(data.msg);
                        return;
                    }
                }
            });
        });

        $(".txt,.notips").click(function () {
            if (downflag == "0") { $(".notips").prop('checked', true); downflag = "1";flag = true;}
            else { $(".notips").prop('checked', false); downflag = "0";flag = false;}
        });
        $(".ys").click(function(){
            $(".h").show();
            $(".xy").show();
            $(".hdgz").hide();
        });
        $(".btn1").click(function(){
            $(".h").show();
            $(".xy").hide();
            $(".hdgz").show();
        });
    });
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
    function GetExternal() {
        return window.external.EmObj;
    }

    function PC_JH(type, c) {
        try {
            var obj =
                GetExternal();
            return obj.EmFunc(type, c);
        } catch (e) {
        }
    }
    // 检查用户是否有权限参与
    function checkPermission(pid) {
        if (pid != "888010000" && pid != "888010400") {
            layer.msg("本活动仅限小智盈用户参与");
            return false;
        }
        return true;
    }
    //跳转 IM
    function goIM(fromname) {
        var b = new Base64();
        var ret = PC_JH('EM_FUNC_START_IM', '0,AC20230424,' + b.encode(fromname));
    }
</script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20230421";//模块名称(焦点图2)
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script></body>
</html>
