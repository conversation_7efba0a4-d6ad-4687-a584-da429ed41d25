<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2023/20231127/css/style.css?r=20231127'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2">
    <div class="main">
        <div class="txt1"><span class="white">券后</span>￥<span class="f60">58/12</span><span class="white">个月</span>
        </div>
        <marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="3" direction="left" class="mar">
            <div class="list"></div>
        </marquee>
        <a href="javascript:;" class="btn1 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"><div class="ico8" style="display: none;"></div></a>
        <div class="an1">
            <div class="txt"><input type="checkbox" class="notips" clickkey="ck_xy" clickdata="" style="width: 20px; height: 20px;"> <a href="javascript:void(0)" class="ys">我已阅读并同意《信息服务协议》</a></div>
        </div>
        <div class="txt5">30积分：<span class="org">已领取</span></div><div class="txt6">续费返<span class="org">200积分</span></div>
    </div>
</div>
<div class="img_3">
    <div class="bg7"></div><div class="bg8"></div>
    <ul>
    <a href="#a1"><div class="ico1"></div><div class="t1">BS点指标</div><div class="t2">提示机会 警示风险</div></a>
    <a href="#a2"><div class="ico2"></div><div class="t1">3分钟深度诊断</div><div class="t2">全面诊断 减少排雷</div></a>
    <a href="#a3"><div class="ico3"></div><div class="t1">战法-顺势龙腾</div><div class="t2">3步精选 强势龙头</div></a>
    <a href="#a4"><div class="ico4"></div><div class="t1">指标-趋势顶底</div><div class="t2">识别顶底信号</div></a>
    <a href="#a5"><div class="ico5"></div><div class="t1">策略-底部变量</div><div class="t2">抄底池 实时刷新</div></a>
    <a href="#a6"><div class="ico6"></div><div class="t1">天眼盯盘</div><div class="t2">时事热点追踪</div></a>
</ul>
    <div class="bg1" id="a1"><iframe frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al1"></iframe></div>
    <div class="bg2" id="a2"><iframe frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al2"></iframe></div>
    <div class="bg3" id="a3"><iframe frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al3"></iframe></div>
    <div class="bg4" id="a4"><iframe frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al4"></iframe></div>
    <div class="bg5" id="a5"><iframe frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al5"></iframe></div>
    <div class="bg6" id="a6"><iframe frameborder="0" src="about:blank" scrolling="no" class="swf1" name="al6"></iframe></div>
</div>
<a href="javascript:;" class="btn3 dh">查看活动规则 &gt;</a>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<a href="javascript:;" class="pf2" style="display: none;"></a>
<div class="bott">
    <div class="main">
        <div class="txt2"><span class="gray">券后</span>￥<span class="f68">58/1年</span><span class="gray">智盈</span></div>
        <div class="ico7">先降再抵，共省￥230</div>
        <a href="javascript:;" class="btn2 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台3"></a>
        <div class="an2">
            <div class="txt"><input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 20px; height: 20px;"> <a href="javascript:void(0)" class="ys b">我已阅读并同意《信息服务协议》</a></div>
        </div>
    </div>
</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>

    <div class="hdgz" style="display: none;"><a href="javascript:void(0)" class="close"></a> <strong>活动规则：</strong>
        <ul>
            <li><strong>活动参与对象</strong>：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。</li>
            <li><strong>福利发放规则</strong>：完成续费和适当性测评后开通【智盈软件使用期】和【北上资金】权限。</li>
            <li><strong>优惠券规则</strong>：持本活动有效优惠券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用。</li>
            <li><strong>积分规则</strong>：页面领取30积分立即到账。在完成续费和适当性测评后发放100积分（冻结状态），适当性测评完成30天后，积分解冻，可在积分商城使用。</li>
            <li>本活动最终解释权归益盟股份有限公司所有。</li>
            <li>活动开展期间，如出现不可抗力等情况，如发生自然灾害，网络攻击，电信故障停机维护、疫情等本平台免于承担责任</li>
        </ul></div>
    <div class="tc3" style="display: none;"><img th:src="@{${staticPath}+'static/renew2023/20231127/images/tc1.png'}" alt=""><a href="javascript:void(0)" class="close" style="margin: 130px 100px 0 0"></a>
        <div class="txt3">直降￥200，用券共省￥230</div><div class="txt4">券后￥<span class="f40">58=12</span>个月智盈</div>
        <div class="an3">
            <div class="txt"><input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 20px; height: 20px;"> <a href="javascript:void(0)" class="ys">我已阅读并同意《信息服务协议》</a></div>
        </div>
        <a href="javascript:;" class="tc-btn dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台3"></a>
    </div>
    <div class="yhxy" style="display: none;"><a href="javascript:void(0)" class="ruleClose"></a>
        <iframe frameborder="0" src="https://www.emoney.cn/dianjin/bb/yhxy.pdf" class="nr" name="sp"></iframe>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/renew2023/20231127/js/main.js'}"></script>

<script>
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";
    $(window).scroll(function(e){
        if(($(window).scrollTop() > 156) && (al1 == "0")){
            $("[name='al1']").attr("src","https://www.emoney.cn/dianjin/mf/bs/bs.html");
            al1 = "1";
        }
        if(($(window).scrollTop() > 900) && (al2 == "0")){
            $("[name='al2']").attr("src","https://www.emoney.cn/dianjin/mf/ggsfz/ggsfz.html");
            al2 = "1";
        }
        if(($(window).scrollTop() > 1920) && (al3 == "0")){
            $("[name='al3']").attr("src","https://www.emoney.cn/dianjin/mf/sslt3/sslt.html");
            al3 = "1";
        }
        if(($(window).scrollTop() > 2640) && (al4 == "0")){
            $("[name='al4']").attr("src","https://www.emoney.cn/dianjin/mf/m-qsdd/qsdd.html");
            al4 = "1";
        }
        if(($(window).scrollTop() > 3480) && (al5 == "0")){
            $("[name='al5']").attr("src","https://www.emoney.cn/dianjin/mf/dblb2/dblb.html");
            al5 = "1";
        }
        if(($(window).scrollTop() > 4210) && (al6 == "0")){
            $("[name='al6']").attr("src","https://www.emoney.cn/dianjin/mf/tydp/tydp.html");
            al6 = "1";
        }
    });
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_2023112701";//模块名称(焦点图2)
    var Remark = "小智盈续费-双12";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
