<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2023/20230103/css/style.css'}" rel="stylesheet" type="text/css"/>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1">
    <div class="main"><div class="djs"><div class="t">0</div>
        <div class="s">00</div>
        <div class="f">00</div><div class="m">00</div></div></div></div>
<div class="img_2"></div>
<div class="img_3">
    <div class="main">
        <a href="javascript:;" class="btn1 dh" clickkey="btn_getvip" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a>
        <!--状态2-已领取-->
        <div class="btn1h" style="display: none;"></div>
        <a  href="javascript:void(0)" name="payurl" target="_blank" class="btn2 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a>
    </div>
</div>
<div class="img_4">

</div>
<div class="img_5"></div>
<div class="img_6">
    <div class="main"><a href="javascript:void(0)" name="payurl" target="_blank" class="btn3 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a><div class="ico2"></div></div></div>
<div class="img_7">
    <div class="main"><a  href="javascript:void(0)" name="payurl" target="_blank" class="btn4 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div></div>
<div class="img_8">
    <div class="main"><div class="hdgz">1、活动有效时间：2023年1月3日-2023年1月20日。<br /><br />
        2、活动参与对象：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。<br /><br />
        3、福利发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限。<br /><br />
        4、积分规则：页面领取30积分立即到账。<br /><br />
        5、优惠券规则：持本活动有效优惠券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用。<br /><br />
        6、赠送课程仅用于软件基础教学，不涉及投资咨询及投资顾问服务，课程内容不构成投资建议，股市有风险，投资需谨慎。<br /><br />
        7、本活动最终解释权归益盟股份有限公司所有。
    </div></div></div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<a href="javascript:void(0);" class="server" id="goIM"></a>
<div class="bott">
    <div class="main"><a href="javascript:void(0)" name="payurl" target="_blank" class="btn5 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a>
    </div>
</div>

<div class="bg" style="display: none">
    <div class="tc" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc4"style="display: none;"><a href="javascript:void(0)" class="close"></a>
        <a href="javascript:void(0)" name="payurl" target="_blank" class="btn6 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a>
    </div>
</div>

    <input type="hidden" id="hid_actcode" th:value="${actcode}" />
    <script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
    <script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
    <script th:inline="javascript">
        var www="../";
        var actcode = /*[[${actcode}]]*/ "0"
        $(document).ready(function () {
            var uid = "";
            var pid = "";
            var uname = "";
            var isLogin = /*[[${isLogin}]]*/ "0";
            var realname = "";
            var maskname = "";
            var channelcode = getQueryString("channelcode");
            var token = getQueryString("token");

            if (!channelcode) {
                channelcode = "A12050";
            }

            if(isLogin == "1") {
                uid = /*[[${loginUserInfo.uid}]]*/ "0";
                pid = /*[[${loginUserInfo.pid}]]*/ "0";
                uname = /*[[${loginUserInfo.MobileX}]]*/ "";
                maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
                realname = /*[[${loginUserInfo.RealName}]]*/ "";
            }

            var payUrl = "http://pay.emoney.cn/newpayv2/pay/order?actid=89&channelcode=" + channelcode + "&" + location.search;
            var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
            if (isMobile) {
                payUrl = "http://pay.emoney.cn/newpayv2/home/<USER>" + channelcode + "&" + location.search;
            }
            if (!token) {
                payUrl += "&name=" + realname + "&encmobile=" + uname + "&makmobile=" + maskname;
            }
            $("[name=payurl]").attr("href", payUrl);
            $("[name=payurl]").click(function () {
                if (!checkPermission(pid)) {
                    return false;
                }
                //推送cmp
                pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
            });
            //一键领取
            $(".btn1").click(function (){
                $.ajax({
                    type: 'get',
                    url: "getBenefits?uid=" + uid + "&pid=" + pid + "&actcode=" + actcode,
                    dataType: 'json',
                    success: function (data) {
                        if (data.code == "200") {
                            //申请成功
                            $(".btn1").hide();
                            $(".btn1h").show();

                            $(".bg").show();
                            $(".tc4").show();

                            addCount(uid);
                        }else {
                            layer.msg(data.msg);
                            return;
                        }
                    }
                });
            });

            if (isLogin == "0") {
                //未登录
                $(".bg").show();
                $(".tc").show();
            } else {
                if (!checkPermission(pid)) {
                    return false;
                }
                InitStatus(uid);
                //推送cmp
                pushdatatocmp(!!uid ? uid : uname, "ACRenew20210104");
            }
            $("#goIM").click(function () {
                goIM("小智盈续费打卡");
            });

            $(".close").click(function (){
                $(".bg").hide();
                $(".tc4").hide();
            });
        });

        function getQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null;
        }
        function pushdatatocmp(uname, adcode) {
            var data = {
                "appid": '10088',
                "logtype": 'click',
                "mid": '',
                "pid": getQueryString("pid"),
                "sid": getQueryString("sid"),
                "tid": getQueryString("tid"),
                "uid": getQueryString("uid"),
                "uname": uname,
                "adcode": adcode,
                "targeturl": "",
                "pageurl": window.top.location.href
            }
            var saasUrl = "http://ds.emoney.cn/saas/queuepush";
            var saasSrc = saasUrl + "?v=" + Math.random()
                + "&queuekey=EMoney:softsupport:AdClickToCMPQueueID"
                + "&message=" + encodeURIComponent(JSON.stringify(data));

            var elm = document.createElement("img");
            elm.src = saasSrc;
            elm.style.display = "none";
            document.body.appendChild(elm);
        }
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj =
                    GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {
            }
        }
        // 检查用户是否有权限参与
        function checkPermission(pid) {
            if (pid != "888010000" && pid != "888010400") {
                layer.msg("本活动仅限小智盈用户参与");
                return false;
            }
            return true;
        }
        //名额-1
        function addCount(uid) {
            $.ajax({
                type: 'get',
                url: window.location.protocol + '//act2017.emoney.cn/ActShort20200408/FlashSale/AddCountByActCode?actcode=' + actcode,
                dataType: 'jsonp',
                data: {
                    uid: uid,
                    value: "1"
                },
                success: function (data) {
                    if (data.retCode == "0") {
                        //申请成功
                        $(".btn1").hide();
                        $(".btn1h").show();

                        $(".bg").show();
                        $("#pop_box").show();
                    }
                }
            });
        }

        function InitStatus(uid) {
            $.ajax({
                type: 'get',
                url: window.location.protocol + '//act2017.emoney.cn/ActShort20200408/FlashSale/IsSubmit?actcode=' + actcode,
                dataType: 'jsonp',
                data: {
                    uid: uid
                },
                success: function (data) {
                    if (data.retCode == 0) {
                        $(".btn1").hide();
                        $(".btn1h").show();
                    }
                }
            });
        }
        //跳转 IM
        function goIM(fromname) {
            var b = new Base64();
            var ret = PC_JH('EM_FUNC_START_IM', '0,AC20230103,' + b.encode(fromname));
        }
    </script>
    <script th:src="@{${staticPath}+'static/js/login.js'}"></script>
    <script th:src="@{${staticPath}+'static/js/base64.js'}"></script>

<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs=setInterval("SetTimeout(2023,1,20,24,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0"+i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2023/1/20 24:00:00");
    var b=new Date("2023/1/16 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("0");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs").show();
    }

</script>

    <script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20230103";//模块名称(焦点图2)
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
