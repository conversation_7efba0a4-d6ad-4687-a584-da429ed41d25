<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>订单确认</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="">
    <link href="../static/userlogin/app/style/common.css" rel="stylesheet" type="text/css"/>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        var phoneWidth = parseInt(window.screen.width);
        var phoneScale = phoneWidth / 750;
        var ua = navigator.userAgent;
        if (/Android (\d+\.\d+)/.test(ua)) {
            var version = parseFloat(RegExp.$1);
            if (version > 2.3) {
                document.write('<meta name="viewport" content="width=750, minimum-scale =' + phoneScale + ', maximum-scale =' + phoneScale + ', target-densitydpi=device-dpi">');
            } else {
                document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
            }
        } else {
            document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
        }
    </script>
</head>
<body>
<div class="dl">
    <ul id="dx" style="display: none">
        <li><input type="text" class="srk1" id="mobile_1" placeholder="请输入手机号"></li>
        <li><input type="text" class="srk2" id="code" placeholder="请输入短信验证码"><a href="javascript:void(0)" id="sendcode" class="yzm">获取验证码</a></li>
        <li><input type="text" class="srk1" id="sale_1" placeholder="业务员工号，没有可不填"></li>
        <li><a href="javascript:void(0)" class="btn">登 录</a></li>
    </ul>
    <ul id="kj">
        <li><input type="text" class="srk1" id="mobile_2" placeholder="请输入手机号"></li>
        <li><input type="text" class="srk1" id="mobile_3" placeholder="请再次输入手机号确认"></li>
        <li><input type="text" class="srk1" id="sale_2" placeholder="业务员工号，没有可不填"></li>
        <li><a href="javascript:void(0)" class="btn">登 录</a></li>
    </ul>
    <div class="t1">如无法登录，请联系客服<span class="red">10108688</span></div>
    <div class="header">
        <span class="line"></span>
        <span class="text">更多信息确认方式</span>
        <span class="line"></span>
    </div>
    <a href="javascript:void(0)" class="sjdl"></a>
</div>
<script th:inline="javascript">

    const callback = /*[[${callback}]]*/"";

    $(".btn").click(function () {
        if ($("#dx").css("display") !== "none") {
            const mobile = $("#mobile_1").val();
            const code = $("#code").val();
            const jobNum = $("#sale_1").val();
            const keepNotExpire = $("#checkMenu_1").attr("checked");

            if (!mobile || !code) {
                alert("请填写手机号及验证码！")
                return false;
            }
            if (!validateTel(mobile)) {
                return false;
            }

            $.ajax({
                type: 'POST',
                url: "smsLogin",
                dataType: 'json',
                data: {
                    mobile: mobile,
                    code: code,
                    jobNum: jobNum,
                    keepNotExpire: keepNotExpire
                },
                success: function (data) {
                    if (data.code == "200") {
                        window.location = callback + (callback.indexOf("?") === -1 ? "?" : "&") + "ticket=" + data.data;
                    } else {
                        alert("请输入正确的账号及验证码");
                    }
                }
            });
        } else {
            const mobile = $("#mobile_2").val();
            const mobile1 = $("#mobile_3").val();
            const jobNum = $("#sale_2").val();
            const keepNotExpire = $("#checkMenu_2").attr("checked");

            if (!validateTel(mobile)) {
                return false;
            }
            if (mobile !== mobile1) {
                alert("请确认手机号是否正确！")
                return false;
            }

            $.post("mobileLogin", {
                mobile: mobile,
                jobNum: jobNum,
                keepNotExpire: keepNotExpire
            }, function (data) {
                if (data.code == "200") {
                    window.location = callback + (callback.indexOf("?") === -1 ? "?" : "&") + "ticket=" + data.data;
                } else {
                    alert("请输入正确的账号及密码");
                }
            });
        }
    });

    //获取验证码
    $("#sendcode").click(function () {
        var mobile = $("#mobile_1").val();

        if (!validateTel(mobile)) {
            return false;
        }

        $.post("sendSms", {mobile: mobile}, function (data) {
            if (data.code == "200") {
                CalcTime();
            } else {
                alert("请输入正确的账号及密码");
            }
        });
    });

    // 手机验证
    function validateTel(invitname) {
        var telReg = /^1([2356789][0-9]|4[579]|66|7[0135678]|9[89])[0-9]{8}$/g;
        if (!$.trim(invitname) || !telReg.test(invitname)) {
            alert("请输入正确的手机号！");
            return false;
        }
        return true;
    }

    function CalcTime() {
        var num = 90;
        $("#sendcode").data("num", num).attr("disabled", true).css("pointer-events", "none").html("验证码(" + num + ")");
        var v = setInterval(function () {
            var thisv = Number($("#sendcode").data("num"));

            thisv = thisv - 1;
            if (thisv < 0) {
                $("#sendcode").attr("disabled", false).css("pointer-events", "").html("获取验证码");
                $("#sendcode").data("num", 0);

                clearInterval(v);
                return false;
            }
            $("#sendcode").html("验证码(" + thisv + ")");
            $("#sendcode").data("num", thisv);

        }, 1000);
    }

    $(".sjdl").click(function () {
        if ($('#dx').is(':hidden')) {
            $("#dx").show();
            $("#kj").hide();
        } else {
            $("#dx").hide();
            $("#kj").show();
        }
    });
</script>
</body>
</html>