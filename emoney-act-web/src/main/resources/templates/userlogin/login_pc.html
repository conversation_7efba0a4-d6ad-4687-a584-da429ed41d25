<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>用户登录</title>
    <link href="../static/userlogin/style/common.css" rel="stylesheet" type="text/css">
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script src="https://static.emoney.cn/static/libs/qrcode/qrcode.min.js"></script>
</head>
<body>
<div class="h2">
    <div class="dl">
        <div class="dl1"></div>
        <div class="dl2" style="display: none;"></div>
        <div class="t1">
            <ul id="sj">
                <li class="kj m on">快捷登录</li>
                <li class="dx ">短信快捷登录</li>
            </ul>
            <ul id="sm" style="display: none">打开微信扫码登录</ul>
        </div>
        <div class="t2">
            <ul id="dx" style="display: none">
                <li><input type="text" class="srk1" id="mobile_1" placeholder="请输入手机号"></li>
                <li><input type="text" class="srk2" id="code" placeholder="请输入短信验证码"><a
                        href="javascript:void(0)" class="yzm" id="sendcode">发送验证码</a>
                </li>
                <li><input type="text" class="srk1" id="sale_1" placeholder="业务员工号，没有可不填"></li>
                <li><input type="checkbox" class="checkMenu" id="checkMenu_1">7天免密登录</li>
                <li><a href="javascript:void(0)" class="btn">登 录</a></li>
            </ul>
            <ul id="kj">
                <li><input type="text" class="srk1" id="mobile_2" placeholder="请输入手机号"></li>
                <li><input type="text" class="srk1" id="mobile_3" placeholder="请再次输入手机号确认"></li>
                <li><input type="text" class="srk1" id="sale_2" placeholder="业务员工号，没有可不填"></li>
                <li><input type="checkbox" class="checkMenu" id="checkMenu_2">7天免密登录</li>
                <li><a href="javascript:void(0)" class="btn">登 录</a></li>
            </ul>
            <ul id="sm2" style="display: none">
                <li>
                    <div id="ewm"></div>
                </li>
                <li class="t-c">使用微信扫一扫登录<br>“益盟操盘手”</li>
            </ul>
            <div class="logo"></div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(".btn").click(function () {
        if ($("#dx").css("display") !== "none") {
            const mobile = $("#mobile_1").val();
            const code = $("#code").val();
            const jobNum = $("#sale_1").val();
            const keepNotExpire = $("#checkMenu_1").attr("checked");

            if (!mobile || !code) {
                alert("请填写手机号及验证码！")
                return false;
            }
            if (!validateTel(mobile)) {
                return false;
            }

            $.ajax({
                type: 'POST',
                url: "smsLogin",
                dataType: 'json',
                data: {
                    mobile: mobile,
                    code: code,
                    jobNum: jobNum,
                    keepNotExpire: keepNotExpire
                },
                success: function (data) {
                    if (data.code == "200") {
                        window.location = callback + (callback.indexOf("?") === -1 ? "?" : "&") + "ticket=" + data.data;
                    } else {
                        alert("请输入正确的账号及验证码");
                    }
                }
            });
        } else {
            const mobile = $("#mobile_2").val();
            const mobile1 = $("#mobile_3").val();
            const jobNum = $("#sale_2").val();
            const keepNotExpire = $("#checkMenu_2").attr("checked");

            if (!validateTel(mobile)) {
                return false;
            }
            if (mobile !== mobile1) {
                alert("请确认手机号是否正确！")
                return false;
            }

            $.post("mobileLogin", {
                mobile: mobile,
                jobNum: jobNum,
                keepNotExpire: keepNotExpire
            }, function (data) {
                if (data.code == "200") {
                    window.location = callback + (callback.indexOf("?") === -1 ? "?" : "&") + "ticket=" + data.data;
                } else {
                    alert("请输入正确的账号及密码");
                }
            });
        }
    });

    //获取验证码
    $("#sendcode").click(function () {
        var mobile = $("#mobile_1").val();

        if (!validateTel(mobile)) {
            return false;
        }

        $.post("sendSms", {mobile: mobile}, function (data) {
            if (data.code == "200") {
                CalcTime();
            } else {
                alert("请输入正确的账号及密码");
            }
        });
    });

    // 手机验证
    function validateTel(invitname) {
        var telReg = /^1([2356789][0-9]|4[579]|66|7[0135678]|9[89])[0-9]{8}$/g;
        if (!$.trim(invitname) || !telReg.test(invitname)) {
            alert("请输入正确的手机号！");
            return false;
        }
        return true;
    }

    function CalcTime() {
        var num = 90;
        $("#sendcode").data("num", num).attr("disabled", true).css("pointer-events", "none").html("验证码(" + num + ")");
        var v = setInterval(function () {
            var thisv = Number($("#sendcode").data("num"));

            thisv = thisv - 1;
            if (thisv < 0) {
                $("#sendcode").attr("disabled", false).css("pointer-events", "").html("获取验证码");
                $("#sendcode").data("num", 0);

                clearInterval(v);
                return false;
            }
            $("#sendcode").html("验证码(" + thisv + ")");
            $("#sendcode").data("num", thisv);

        }, 1000);
    }

    $('.dl .t1 li').click(function () {
        $(this).addClass('on').siblings('li').removeClass('on');
    });

    $('.cp li').click(function () {
        $(this).removeClass('off').siblings('li').addClass('off');
    });
    $('.t4 li').click(function () {
        $(this).addClass('current').siblings('li').removeClass('current');
    });
    $('.kj').click(function () {
        $("#kj").show();
        $("#dx").hide();
    });
    $('.dx').click(function () {
        $("#kj").hide();
        $("#dx").show();
    });
    $('.dl1').click(function () {
        $("#sj").hide();
        $("#kj").hide();
        $("#dx").hide();
        $(".dl1").hide();
        $(".dl2").show();
        $("#sm").show();
        $("#sm2").show();
    });
    $('.dl2').click(function () {
        $("#sj").show();
        $(".dx").addClass('on').siblings('li').removeClass('on');
        $("#kj").hide();
        $("#dx").show();
        $(".dl1").show();
        $(".dl2").hide();
        $("#sm").hide();
        $("#sm2").hide();
    });

    $(".ico10").click(function () {
        if ($('.left').is(':hidden')) {
            $(".ico10").css("transform", "rotate(0deg)");
            $(".right").css("width", "697px");
            $(".left").show();
        } else {
            $(".ico10").css("transform", "rotate(180deg)");
            $(".right").css("width", "945px");
            $(".left").hide();
        }
    });
    $('.c').click(function () {
        $(".h").hide();
    });
    $('.btn2').click(function () {
        $(".h").show();
    });
</script>
<script th:inline="javascript">
    const eventId = /*[[${incrNum}]]*/ "";
    const callback = /*[[${callback}]]*/"";

    var CometCom = {
        server: "https://swsc.emoney.cn",

        checkJquery: function (callback) {
            if (!(window.jQuery)) {
                var s = document.createElement('script');
                s.setAttribute('src', 'https://static.emoney.cn/static/libs/jquery/2.2.4/jquery.min.js');
                s.setAttribute('type', 'text/javascript');
                document.getElementsByTagName('head')[0].appendChild(s);
                setTimeout(callback, 1000);
            }
        },

        pull: function (group, eventId, dealfunc, delay) {

            if (CometCom.server === "") {
                alert("required immediately communicate server.");
                return;
            }

            const cometUrl = CometCom.server + "/comet/connect?uid=" + eventId + "&group=" + group;

            const pullTick = $.ajax({
                type: "GET",
                cache: false,
                url: cometUrl,
                dataType: "jsonp",

                success: function (data, textStatus, jqXHR) {
                    dealfunc(data);
                    setTimeout(function () {
                        CometCom.pull(group, uid, dealfunc, delay);
                    }, delay);

                },
                complete: function (XMLHttpRequest, textStatus) {
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    if (textStatus === "timeout") {
                        setTimeout(function () {
                            CometCom.pull(group, uid, dealfunc, delay);
                        }, delay);

                    }
                }
            });
        }
    }


    $(document).ready(function () {
        CometCom.server = "https://swsc.emoney.cn";
        const group = "actlogin";
        const delay = 1000;

        CometCom.pull(group, eventId, function (data) {
            if (data.length > 0) {
                if (data === "-1") {
                    alert("登录异常,请重新扫码登录");
                } else {
                    window.location = callback + (callback.indexOf("?") === -1 ? "?" : "&") + "ticket=" + data;
                }
            }
        }, delay);
    });

    const qrUrl = "https://apiminapp.emoney.cn/mp?action=actlogin&eventid=" + eventId;

    const qrcode = new QRCode("ewm", {
        text: qrUrl,
        width: 230,
        height: 230,
        colorDark: "#000000",
        colorLight: "#ffffff",
        correctLevel: QRCode.CorrectLevel.H
    });
</script>
</body>
</html>