<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="518益起发 股民节狂欢 百万壕礼来袭"/>
    <link th:href="@{${staticPath}+'static/renew2025/20250601/style/css.css?r=20250603'}" rel="stylesheet" type="text/css" />
    <link href="https://www.emoney.cn/dianjin/bb/animate.css" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script th:src="@{${staticPath}+'static/js/awardRotate.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
    <script src="../../static/renew2025/20250601/js/jquery.slides.js"></script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"></div>
<div class="img_3"><div class="main"><a href="javascript:;" class="btn1 dh">一键直达 &gt;&gt;&gt;</a></div></div>
<div class="img_4 wow fadeInUp" data-wow-duration="1000ms">
    <div class="main"><a href="javascript:void(0)" class="btn2 an1 dh">立即续费 &gt;&gt; 为投资加能</a><a href="javascript:void(0)" class="btn2 an2 dh">立即续费 &gt;&gt; 为投资加能</a><a href="javascript:void(0)" class="btn3 dh"></a></div></div>
<div class="img_5 wow fadeInUp" data-wow-duration="1000ms"></div>
<div class="img_6">
    <div class="main"><a href="javascript:void(0)" class="btn2 an3 dh">立即续费 &gt;&gt; 解锁量能识别</a></div></div>
<div class="img_7">
    <div class="main"><a href="javascript:void(0)" class="btn2 an4 dh">立即续费 &gt;&gt; 解锁五星研报</a></div></div>
<div class="img_8">
    <div class="main"><div class="pic1">
        <div class="slider_name slider_box">
            <ul class="silder_con">
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renew2025/20250601/images/a1.png'}" alt="" /></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renew2025/20250601/images/a2.png'}" alt="" /></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renew2025/20250601/images/a3.png'}" alt="" /></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renew2025/20250601/images/a4.png'}" alt="" /></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renew2025/20250601/images/a5.png'}" alt="" /></li>
            </ul>
        </div>
        <a class="prev" style="opacity: 0.2;"></a><a class="next" style="opacity: 0.2;"></a></div><a href="javascript:void(0)" class="btn2 an5 dh">立即续费 &gt;&gt; 学《五好价投》</a><a href="javascript:void(0)" class="btn4 dh"></a></div></div>
<div class="img_9"></div>
<div class="img_10 wow fadeInUp" data-wow-duration="1000ms" id="challenge">
    <div class="main">
        <div class="dt1" style="display: none;">
            <div class="bt">挑战赛 <span class="red2">第1题</span></div><div class="black">（单选题）有效提高B点胜率的方法（  ）</div>A. 只做大幅放量的B点<br>
            B. 叠加中期均线辅助趋势判断，均线下方的B点更有操作性<br>
            C. 看大做小，用操盘线大周期看趋势，小周期定操作
            <a href="javascript:;" class="btn5 dh">立即闯关</a>
        </div>
        <!--获取闯关奖励提示-->
        <div class="tc" id="rewardtip"><div class="t3">恭喜您！</div><div class="t4"><div class="t4a"><span class="red">30天</span>操盘手使用期已到账</div>祝您日赚斗金，月月翻红！</div></div>

        <!--未全部答对-->
        <div class="tc" id="partcorrect">
            <div class="t5">
                <div class="t5c">很可惜！错了1道题<br>再试试吧</div>
                <div class="t5d">
                    <a href="javascript:void(0)" id="redo">复习一遍<br>重新闯关</a>
                    <a href="javascript:void(0)" name="getreward">结束闯关<div class="f21">领取25天使用期</div></a>
                </div>学习以下对应题目视频，重新闯关，冲刺100分！
                <div class="t5e">
                </div>
            </div>
        </div>

        <!--全部答对-->
        <div class="tc" id="allcorrect">
            <div class="t5">
                <div class="t5c">太棒了！答对6道题</div>
                <div class="t5d">
                    <a href="javascript:void(0)" class="pt" name="getreward">结束闯关<div class="f21">领取25天使用期</div></a>
                </div>学习以下对应题目视频，加强记忆，投资“稳操胜券”
                <div class="t5e">

                </div>
            </div>
        </div>

        <!--其它状态-->
        <div class="tc" id="loading"><div class="t1">请稍等!</div><div class="t2 loading-text">正在计算答题获得使用期</div></div>

        <div class="wdjp"></div>
        <a href="javascript:void(0)" class="btn2 an6 dh">立即续费 &gt;&gt; 答题领奖</a><div class="md">
        <ul id="user_list">
        </ul>
    </div></div></div>
<div class="foot">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><div class="main"><a href="javascript:void(0)" class="btn6 dh">立即续费</a></div></div>
<div class="h" style="display: none;">
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script src="https://www.emoney.cn/dianjin/bb/wow2.js"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renew2025/20250601/js/main.js?r=20250208'}"></script>
<script th:src="@{${staticPath}+'static/renew2025/20250601/js/jquery.slides.js?r=20250208'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>

<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "xzyxf-20250601";//操盘手续费
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
