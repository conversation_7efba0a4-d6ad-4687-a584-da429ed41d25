<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <link href="style/css.css" rel="stylesheet" type="text/css">
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="518益起发 股民节狂欢 百万壕礼来袭"/>
    <link th:href="@{${staticPath}+'static/renew2025/20250501/style/cssnew.css'}" rel="stylesheet" type="text/css" />
    <link href="https://www.emoney.cn/dianjin/bb/animate.css" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script th:src="@{${staticPath}+'static/js/awardRotate.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"><div class="main"><div class="pic wow zx" data-wow-duration="1000ms"></div></div></div>
<div class="img_3 wow fadeInUp" data-wow-duration="1000ms">
    <div class="main">
        <a href="javascript:void(0)" class="btn1 dh" clickkey="btn1" clickdata="btn1"></a>
        <div class="hdgz"><ul>
            <li>① 参与对象：操盘手老用户</li>
            <li>② 抢红包时间：2025年5月1日-5月31日 （活动期间仅限1次，不可重复抽取）</li>
            <li>③ 抽奖红包以立减券的形式发放，仅限本次操盘手续费活动使用，过期作废</li>
        </ul></div></div></div>
<div class="img_4 wow fadeInUp" data-wow-duration="1000ms">
    <div class="main"><a href="javascript:void(0)" class="btn3 dh" clickkey="btn3" clickdata="btn3"></a></div></div>
<div class="img_5 wow fadeInUp" data-wow-duration="1000ms"></div>
<div class="img_6 wow fadeInUp" data-wow-duration="1000ms">
    <div class="main"><a href="javascript:void(0)" class="btn4 dh" clickkey="btn4" clickdata="btn4"></a></div></div>
<div class="img_7 wow fadeInUp" data-wow-duration="1000ms">
    <div class="main">
        <a href="###" class="zz" id="rotate"></a>
        <div class="go" id="btn-lottery"><img th:src="@{${staticPath}+'static/renew2025/20250501/images/go.png'}"/></div>
        <div class="zjxx">
            <ul id="my_list">
    <!--            <li>用户177****1234 中奖 操盘手使用期<span class="red">1个月</span></li>-->
    <!--            <li>用户177****1234 中奖 操盘手使用期<span class="red">3个月</span></li>-->
            </ul>
        </div>
        <div class="hdsm">
            <ul>
                <li>① 活动时间：2025年5月10日-6月30日</li>
                <li>② 活动期间续费成功即可获得抽奖机会</li>
                <li>③ 续一年抽1次，续两年抽2次</li>
                <li>④ 奖品将在15个工作日内到账</li>
            </ul>
        </div>
    </div></div>
<div class="foot">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><div class="main"><a href="#a1" class="btn2 dh" clickkey="btn2" clickdata="btn2"></a></div></div>
<div class="h" style="display: none;">
    <div class="tc" style="display: none;">
        <a href="javascript:void(0)" class="close"></a>
        <div class="txt"><span class="f60">￥</span>28.88</div>
        <a href="javascript:void(0)" class="tc-btn dh" clickkey="tc-btn" clickdata="tc-btn"></a>
    </div>
    <div class="tc2" style="display: none;">
        <a href="javascript:void(0)" class="close"></a>
        <div class="t1">获得财富券</div>
        <div class="t2">
<!--            <span class="red">1个月</span>-->
            产品使用期已赠送至账户</div>
    </div>
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script src="https://www.emoney.cn/dianjin/bb/wow2.js"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renew2025/20250501/js/mainnew.js?r=20250208'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>

<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "xzyxf-20250501";//操盘手续费
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
