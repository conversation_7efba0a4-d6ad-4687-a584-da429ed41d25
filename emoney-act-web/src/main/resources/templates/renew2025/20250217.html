<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="新春开门红 账户稳盈利 先续费 接彩头  后学习 鼓钱包"/>
    <link th:href="@{${staticPath}+'static/renew2025/20250217/style/css.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"></div>
<div class="img_3"><div class="main"><a href="javascript:void(0)" class="btn1 dh" clickkey="btn1" clickdata="btn1"></a></div></div>
<div class="img_4"><div class="main"><!-- 代码 开始 -->
    <ul class="silder_nav">
        <li class=""></li>
        <li class=""></li>
    </ul>
    <div class="slider_name slider_box pic1">
        <ul class="silder_con">
            <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renew2025/20250217/images/a1.png'}" alt=""></li>
            <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renew2025/20250217/images/a2.png'}" alt=""></li>
        </ul>
    </div>
    <script type="text/javascript" th:src="@{${staticPath}+'static/renew2025/20250217/js/jquery.slides.js'}"></script>
    <!-- 代码 结束 -->
</div></div>
<div class="img_5"><div class="bg" id="al1">
    <ul>
        <a class="current"  target="al1">
            <div class="f1">第一节</div>趋势稳波段
        </a>
        <a  target="al1">
            <div class="f1">第二节</div>多周期共振法
        </a>
        <a  target="al1">
            <div class="f1">第三节</div>低位长远N倍股
        </a>
        <a  target="al1">
            <div class="f1">第四节</div>又稳又快大波段
        </a>
    </ul>
    <iframe allowfullscreen="true" frameborder="0" scrolling="no" class="swf1" name="al1"></iframe>
    <a href="javascript:void(0)" class="btn3 dh" clickkey="btn2" clickdata="btn2"></a>
    <!--状态2<div class="btn3h"></div>-->
</div></div>
<div class="img_6">
    <div class="main"><div class="ico2">
        <ul>
            <!--<li></li>
            <li></li>
            <li></li>
            <li></li>-->
        </ul>
    </div>
        <a href="javascript:void(0)" class="btn4 dh" clickkey="btn3" clickdata="btn3"></a>
        <!--状态2<div class="btn4h"></div>-->
    </div></div>
<div class="foot">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><div class="main"><a href="javascript:void(0)" class="btn2 dh" clickkey="btn4" clickdata="btn4"></a></div></div>
<div class="h" style="display: none">
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
    <!--未完成课程学习提示窗-->
    <div class="tc" style="display: none;">
        <div class="txt1">抱歉</div>
        <div class="txt2">您尚未完成课程学习，无法领取1个月使用期奖励</div>

        <a href="javascript:void(0)" class="tc-btn dh" clickkey="tc-btn5" clickdata="tc-btn5"></a>
    </div>
</div>
<script type="text/javascript">
    var videoList = $('iframe')
    var wHeigt = window.innerHeight
    document.addEventListener('scroll', function () {
        var isPlay = false
        //滚动条高度+视窗高度 = 可见区域底部高度
        var visibleBottom = window.scrollY + document.documentElement.clientHeight
        //可见区域顶部高度
        var visibleTop = window.scrollY
        for (var i = 0; i < videoList.length; i++) {
            var centerY = $(videoList[i]).offset().top + (videoList[i].offsetHeight / 2)
            if (centerY > visibleTop && centerY < visibleBottom) {
                if (!isPlay) {
                    videoList[i].src.match(/py=0/) && (videoList[i].src = videoList[i].src.replace(/py=0/, 'py=1'))
                    isPlay = true
                } else {
                    videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
                }
            } else {
                videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
            }
        }
    })
</script>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renew2025/20250217/js/mainnew.js?r=20250208'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "xzyxf-20250217";//小智盈续费
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script></body>
</html>
