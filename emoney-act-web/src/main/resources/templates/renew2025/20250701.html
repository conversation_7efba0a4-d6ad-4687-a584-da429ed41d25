<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="7月量能挑战赛 一眼看穿资金底牌，蜕变市场‘量’化赢家"/>
    <link th:href="@{${staticPath}+'static/renew2025/20250701/style/css.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"><div class="main"><div class="pic1 wow zx" data-wow-duration="1000ms"></div></div></div>
<div class="img_3 wow fadeInUp" data-wow-duration="1000ms"><div class="main"><div class="hdgz">活动时间：7月1日-31日<br>
    参与条件：益盟操盘手在期用户（过期用户续费成功后可参与）<br>
    活动奖励：每答对1题得5天操盘手软件使用期+ 5元无门槛立减券，共6题<br>
    其他说明：每位用户限参与1次；<strong>软件使用期领取后立即发放；</strong> </div><div class="dt">
    <ul id="ul_qalist">
        <li data-qid="1"><a href="javascript:void(0)" class="btn1c">正确</a><a href="javascript:void(0)" class="btn1c">错误</a></li>
        <li data-qid="2"><a href="javascript:void(0)" class="btn1c">正确</a><a href="javascript:void(0)" class="btn1c">错误</a></li>
        <li data-qid="3"><a href="javascript:void(0)" class="btn1c">正确</a><a href="javascript:void(0)" class="btn1c">错误</a></li>
        <li data-qid="4"><a href="javascript:void(0)" class="btn1c">正确</a><a href="javascript:void(0)" class="btn1c">错误</a></li>
        <li data-qid="5"><a href="javascript:void(0)" class="btn1c">正确</a><a href="javascript:void(0)" class="btn1c">错误</a></li>
        <li data-qid="6"><a href="javascript:void(0)" class="btn1c">正确</a><a href="javascript:void(0)" class="btn1c">错误</a></li>
    </ul>
</div>
    <a href="javascript:void(0)" class="btn2 dh">立即提交</a>
    <div class="txt" style="display: none;"></div>
    <!--<a href="javascript:void(0)" class="btn2b">已领取</a><div class="txt">恭喜你！累计获得30元立减券+30天使用期</div>-->
</div>
</div>
<div class="img_4 wow fadeInUp" data-wow-duration="1000ms">
    <div class="main"><a href="javascript:void(0)" class="btn5 dh">立即续费</a></div></div>
<div class="img_5 wow fadeInUp" data-wow-duration="1000ms"><div class="main"><a href="javascript:void(0)" class="btn6 an1 dh">立即续费→解锁功能</a><a href="javascript:void(0)" class="btn6 an2 dh">立即续费→学习实战</a><a href="javascript:void(0)" class="btn4 dh"></a></div></div>

<div class="foot">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><div class="main"><a href="javascript:void(0)" class="btn3 dh"></a></div></div>
<div class="h" style="display: none;">
    <div class="tc" style="display: none;">
        <a href="javascript:void(0)" class="close"></a>
        <div class="t1"></div>
        <div class="t2"></div>
    </div>
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt1">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renew2025/20250701/js/main.js?r=20250208'}"></script>
<script src="https://www.emoney.cn/dianjin/bb/wow2.js"></script>
<link href="https://www.emoney.cn/dianjin/bb/animate.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
