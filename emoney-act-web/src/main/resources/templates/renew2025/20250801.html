<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <link href="style/css.css" rel="stylesheet" type="text/css">
    <title>益盟23年周年庆</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="千人拼团大作战 瓜分百万使用期,续费自动拼团，拼团人数越多，加赠使用期越多，续费1年版最高累计8个月"/>
    <link th:href="@{${staticPath}+'static/renew2025/20250801/style/css.css?r=20250804'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"></div>
<div class="img_3"></div>
<div class="img_4">
    <div class="main"><div class="line2">
        <div class="x"></div>
    </div><div class="ico4">
        <ul>
        </ul>
    </div><a href="javascript:void(0)" class="btn1 dh"></a></div></div>
<div class="img_5">
    <div class="main"><!-- 代码 开始 -->

        <div class="slider_name slider_box pic1">
            <ul class="silder_con">
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renew2025/20250801/images/a1.png'}" alt=""></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renew2025/20250801/images/a2.png'}" alt=""></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renew2025/20250801/images/a3.png'}" alt=""></li>
            </ul>
        </div>
        <ul class="silder_nav">
            <li class=""><div class="f1">量能识别</div>一眼识量能，辅捉趋势浪</li>
            <li class=""><div class="f1">机构调研</div>站机构肩膀，选价值绩优<div class="ico2"></div></li>
            <li class=""><div class="f1">五星研报</div>择优好研报，选股更高效</li>
        </ul>
        <script type="text/javascript" th:src="@{${staticPath}+'static/renew2025/20250801/js/jquery.slides.js'}"></script>
        <!-- 代码 结束 -->
        <a href="javascript:void(0)" class="btn2 dh"></a>
    </div></div>
<div class="img_6" id="a2">
    <div class="main">
        <div class="ico5">
            <ul>

            </ul>
        </div>
        <div class="line">
            <div class="x"></div>
        </div><div class="ico3">68%看涨</div><div class="ico3b">32%看跌</div><a href="javascript:void(0)" class="btn3 dh" data-isup="true">看涨</a><a href="javascript:void(0)" class="btn4 dh" data-isup="false">看跌</a><a href="javascript:void(0)" class="btn9 b">“猜涨跌”活动结果公示</a><a href="javascript:void(0)" class="btn10 b">“猜涨跌”我的参与记录</a>
        <div class="hdgz2">1、活动对象：8月完成操盘手软件续费<br>
            2、活动共分为4周，每周猜测上证指数周五收盘后的周K线涨跌；<br>
            3、活动时间为8.1-8.31，猜对1次可获得5天操盘手软件使用期，最高可获得20天； <br>
            4、每位用户每周猜涨跌仅可提交1次，每周提交截止时间为周五13:00；奖品（操盘手软件使用期）将在当周猜对后的3个工作日内赠送到账号中。</div>
    </div></div>

<div class="foot">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><div class="main"><a href="javascript:void(0)" class="btn5 dh" clickkey="btn8" clickdata="btn8"></a></div></div>
<div class="h" style="display: none;">
    <div class="tc1" style="display: none;"><a href="javascript:void(0)" class="close"></a></div>
    <div class="tc2" style="display: none;"><a href="javascript:void(0)" class="close"></a><ul id="prognoseDetailUl">
    </ul></div>
    <div class="tc3" style="display: none;"><a href="javascript:void(0)" class="close"></a><ul id="myPrognoseDetailUl">
    </ul></div>
    <div class="tc4" style="display: none;"><a href="javascript:void(0)" class="close"></a><a href="javascript:void(0)" class="tc-btn1">确认</a><a href="javascript:void(0)" class="tc-btn2">取消</a>
        <span class="red2" id="preResult">看涨</span>
    </div>
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt1">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tclogin-btn1">登录</a><a href="javascript:;" class="tclogin-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renew2025/20250801/js/main.js?r=20250804'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script src="https://imgtongji.emoney.cn/scripts/https/emoneyanalytics.js" type="text/javascript"></script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "cps-20250724";//操盘手续费
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
