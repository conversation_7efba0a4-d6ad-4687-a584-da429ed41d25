<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="10月龙腾祥瑞《稳波快波实战精选》系列">
    <link rel="stylesheet" href="static/dsclass/style/common.css">
    <script src="http://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        (function () {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) { }
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) { }
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) { }
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) { }
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>
<body>
<div class="bod">
    <div id="dbg1"></div>
    <div id="dbg2"></div>
    <div id="dbg3">
        <div class="main">
            <div class="an1" clickKey="dj" clickData="dianzanup">
                <div id="ThumupNum">想听点赞哦（已赞<span class="dz"></span>）</div>
            </div>
        </div>
    </div>
    <div id="dbg4">
        <div class="main">
            <table class="tab wow fadeInUp" data-wow-duration="1000ms" border="0" cellspacing="0" cellpadding="0">
                <tbody>
                <tr>
                    <td class="bt red2">课程章节</td>
                    <td>第一节</td>
                    <td>第二节</td>
                    <td>第三节</td>
                    <td>第四节</td>
                </tr>
                <tr>
                    <td class="bt red2">课程时间</td>
                    <td>
                        11月3日<br />
                        周三13:30
                    </td>
                    <td>
                        11月10日<br />
                        周三13:30
                    </td>
                    <td>
                        11月17日<br />
                        周三13:30
                    </td>
                    <td>
                        11月24日<br />
                        周三13:30
                    </td>
                </tr>
                <tr>
                    <td class="bt red2">课程主题</td>
                    <td>经典指标选时篇</td>
                    <td>市场龙甄选篇</td>
                    <td>巴菲特点金篇</td>
                    <td>内外资共振优选篇</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div id="dbg5">
        <div class="main">
            <table class="tab wow fadeInUp" data-wow-duration="1000ms" border="0" cellspacing="0" cellpadding="0">
                <tbody>
                <tr>
                    <td class="bt red2">课程章节</td>
                    <td>第五节</td>
                    <td>第六节</td>
                    <td>第七节</td>
                    <td>第八节</td>
                    <td>第九节</td>
                </tr>
                <tr>
                    <td class="bt red2">课程时间</td>
                    <td>
                        12月1日<br />
                        周三13:30
                    </td>
                    <td>
                        12月8日<br />
                        周三13:30
                    </td>
                    <td>
                        12月15日<br />
                        周三13:30
                    </td>
                    <td>
                        12月22日<br />
                        周三13:30
                    </td>
                    <td>
                        12月29日<br />
                        周三13:30
                    </td>
                </tr>
                <tr>
                    <td class="bt red2">课程主题</td>
                    <td>
                        好资金选股之<br />
                        公募(上)
                    </td>
                    <td>
                        好资金选股之<br />
                        公募(下)
                    </td>
                    <td>
                        好资金选股之<br />
                        社保
                    </td>
                    <td>
                        好资金选股之<br />
                        QFII(上)
                    </td>
                    <td>
                        好资金选股之<br />
                        QFII(下)
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div id="dbg6">
        <div class="al1 d1">
            <!-- 代码 开始 -->
            <div class="slider_name slider_box">
                <ul class="silder_con" style=" width: 7680px;">
                    <li class="silder_panel"><img src="static/dsclass/images/a1.png" /></li>
                    <li class="silder_panel"><img src="static/dsclass/images/a2.png" /></li>
                    <li class="silder_panel"><img src="static/dsclass/images/a3.png" /></li>
                    <li class="silder_panel"><img src="static/dsclass/images/a4.png" /></li>
                </ul>
            </div>
            <a href="javascript:void(0)" class="prev"></a><a href="javascript:void(0)" class="next"></a>
            <!-- 代码 结束 -->
        </div>
        <div class="al1 d2">
            <!-- 代码 开始 -->
            <div class="slider_name2 slider_box2">
                <ul class="silder_con2" style=" width: 7680px;">
                    <li class="silder_panel2"><img src="static/dsclass/images/b1.png" /></li>
                    <li class="silder_panel2"><img src="static/dsclass/images/b2.png" /></li>
                    <li class="silder_panel2"><img src="static/dsclass/images/b3.png" /></li>
                    <li class="silder_panel2"><img src="static/dsclass/images/b4.png" /></li>
                    <li class="silder_panel2"><img src="static/dsclass/images/b5.png" /></li>
                </ul>
            </div>
            <a href="javascript:void(0)" class="prev"></a><a href="javascript:void(0)" class="next"></a>
            <!-- 代码 结束 -->
        </div>
        <div class="bg2"></div>
        <div class="kc">
            <ul>
                <li>
                    <div class="txt1">《稳波段炼金术》　第一节：经典指标选时篇</div>
                    <div class="txt2">11月3日 周三13:30</div>
                    <a href="javascript:;" data-href="https://emoney.gensee.com/webcast/site/vod/play-c0ccc20c57fb4299a04e2fa665c6589b" target="sp" class="btn2"></a>
                </li>
                <li>
                    <div class="txt1">《稳波段炼金术》　第二节：市场龙甄选篇</div>
                    <div class="txt2">11月10日 周三13:30</div>
                    <a href="javascript:;" data-href="https://emoney.gensee.com/webcast/site/vod/play-4e636953e5734f30a0d41b26ec9c1176" target="sp" class="btn2"></a>
                </li>
                <li>
                    <div class="txt1">《稳波段炼金术》　第三节：巴菲特点金篇</div>
                    <div class="txt2">11月17日 周三13:30</div>
                    <a href="javascript:;" data-href="https://emoney.gensee.com/webcast/site/vod/play-c437669324104d35bd40f0429070edf9" target="sp" class="btn2"></a>
                </li>
                <li>
                    <div class="txt1">《稳波段炼金术》　第四节：内外资共振优选篇</div>
                    <div class="txt2">11月24日周三13:30</div>
                    <a href="javascript:;" data-href="https://emoney.gensee.com/webcast/site/vod/play-161afd658c9649ee8714e3bc2d07d63b" target="sp" class="btn2"></a>
                </li>
                <li>
                    <div class="txt1">《好资金选股》　第五节：好资金选股之公募(上)</div>
                    <div class="txt2">12月1日  周三 13:30</div>
                    <a href="javascript:;" data-href="https://emoney.gensee.com/webcast/site/vod/play-0545c7b90dad424abec013bedd190855" target="sp" class="btn2"></a>
                </li>
                <li>
                    <div class="txt1">《好资金选股》　第六节：好资金选股之公募(下)</div>
                    <div class="txt2">12月8日  周三 13:30</div>
                    <a href="javascript:;" data-href="https://emoney.gensee.com/webcast/site/vod/play-08ac2ddc4a934af29ada169ba239277c" target="sp" class="btn2"></a>
                </li>
                <li>
                    <div class="txt1">《好资金选股》　第七节：好资金选股之社保</div>
                    <div class="txt2">12月15日 周三13:30</div>
                    <a href="javascript:;" data-href="https://emoney.gensee.com/webcast/site/vod/play-91760156003644ca837b622aad78f335" target="sp" class="btn2"></a>
                </li>
                <li>
                    <div class="txt1">《好资金选股》　第八节：好资金选股之QFII(上)</div>
                    <div class="txt2">12月22日 周三13:30</div>
                    <a href="javascript:;" data-href="https://emoney.gensee.com/webcast/site/vod/play-8f2c0cc99ded4b2cb8cc3ec7fe501bea" target="sp" class="btn2"></a>
                </li>
                <li>
                    <div class="txt1">《好资金选股》　第九节：好资金选股之QFII(下)</div>
                    <div class="txt2">12月29日 周三 13:30</div>
                    <a href="javascript:;" data-href="https://emoney.gensee.com/webcast/site/vod/play-3abb069acbc04220a24fb82cb1418f33" target="sp" class="btn2"></a>
                </li>
            </ul>
        </div>
    </div>
    <div id="dbg11">
        <div class="main">
            <div class="btn an2 dh" clickkey="dj" clickdata="dianzandwn">
                <div>（已赞<span class="dz"></span>）</div>
            </div>
        </div>
    </div>
</div>
<div class="footer">
    欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340
</div>
<div class="h" style="display: none">
    <div id="sp">
        <a href="javascript:void(0)" class="close"></a>
        <iframe frameborder="0" src="" scrolling="no" class="if" name="sp"></iframe>
    </div>
</div>
<div class="bg" style="display:none;">
    <div class="tc" style="display:none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<script src="static/libs/layer/layer.js"></script>
<script src="static/js/jsencrypt.min.js"></script>
<script th:inline="javascript">
    var www = "";
    var downflag = "0";
    var uid = "";
    var count = 1000; // 初始值
    var cookiename = "masterClass051001";

    var App = "10088";   //APPID 没有请申请
    var Module = "dz-pc-tj";// 点赞活动
    var Remark = "huodong20210420";     //备注可为空
    var ClickFlag = true;//默认为true.btn2

    $(document).ready(function () {
        var pid = "";
        var isLogin = /*[[${isLogin}]]*/ "0";

        if(isLogin=="1"){
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
        }
        $(".btn2").click(function () {
            if (isLogin == "0") {
                //未登录
                $(".bg").show();
                $(".tc").show();
                return false;
            } else {
                if (pid != "888020000" && pid != "888080000") {
                    layer.msg("非智盈大师用户，不能参与本次活动。");
                    return false;
                }
                var hrefurl = $(this).attr("data-href");
                $(".if").attr("src", hrefurl);
                $(".h").show();
            }
        });
        $(".close").click(function () {
            $(".h").hide();
            $(".if").attr("src", "about:blank");
        });

        var _uid = (new Date()).getTime();
        var _newsId = '20210420';
        var _appId = App;
        var _moduleId = '99';
        var $clickBtns = $(".an1, .btn.an2");
        var $numShowbox = $('.dz', $clickBtns);
        var $ThumupNum = $('#ThumupNum', $clickBtns);

        function ClickThumup() {
            if ($clickBtns.hasClass('clicked')) {
                return;
            }
            $.ajax({
                url: "http://ds.emoney.cn/newsapi/api/zans/submitjsonp",
                type: "GET",
                dataType: "JSONP",
                data: {
                    newsId: _newsId,
                    uid: _uid,
                    appId: _appId,
                    moduleId: _moduleId,
                    groupId: "group10001",
                    from: "clientHDPage"
                },
                success: function (data) {
                    var curThumupCount = count + (data.message.likes || 1)
                    if (data.isSucess) {
                        alert("投票成功");
                        $numShowbox.text(curThumupCount);
                    } else {
                        alert("您已投过票了");
                    }
                }
            });
        }

        function getThumups() {
            $.ajax({
                url: "http://ds.emoney.cn/newsapi/api/zans/getlistjsonp",
                type: "GET",
                dataType: "JSONP",
                data: {
                    jsondata: '[{ newsId: ' + _newsId + ', uid: ' + _uid + ', appId: ' + _appId + ', moduleId: ' + _moduleId + ' }]'
                },
                success: function (data) {
                    // {"retCode":null,"retMsg":null,"isSucess":true,"tipMessage":"点赞信息查询成功","message":[{"liked":true,"newsId":"20210168","likes":2,"appId":"10150","moduleId":"99"}]}
                    if (data.isSucess) {
                        var curThumupCount = count + (data.message[0].likes || 1)
                        if (data.message[0].liked) {
                            $clickBtns.addClass('clicked');
                        } else {
                            $clickBtns.removeClass('clicked');
                        }
                        $numShowbox.text(curThumupCount);
                    }

                }
            });
        }

        // 获取状态
        getThumups();

        // 点赞
        $clickBtns.click(ClickThumup);

    });


    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }

    function setCookie(name, value) {
        var expdate = new Date();
        expdate.setTime(expdate.getTime() + 30 * 60 * 1000);
        document.cookie = name + "=" + value + ";expires=" + expdate.toGMTString() + ";path=/";
    }


    function getCookie(c_name) {
        if (document.cookie.length > 0) {
            c_start = document.cookie.indexOf(c_name + "=")
            if (c_start != -1) {
                c_start = c_start + c_name.length + 1
                c_end = document.cookie.indexOf(";", c_start)
                if (c_end == -1) c_end = document.cookie.length
                return unescape(document.cookie.substring(c_start, c_end))
            }
        }
        return ""
    }
</script>
<script src="static/js/login.js"></script>
<script src="static/dsclass/js/jquery.slides.js"></script>
<script src="http://www.emoney.cn/dianjin/bb/wow2.js"></script>
<link href="http://www.emoney.cn/dianjin/bb/animate.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">document.write(unescape("%3Cscript src='http://api2.tongji.emoney.cn/scripts/emoneyanalyticspv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
