<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>《龙行趋势》系列课</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="听《备战跨年龙》系列课，直播会场免费领门票">
    <link th:href="@{${staticPath}+'static/renewds/20241204/style/common.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "《龙行趋势》系列课");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>
<body>
<div class="bod">
    <div id="dbg1"></div>
    <div id="dbg2">
        <div class="main"><a href="javascript:void(0)" class="btn1 dh popupD2"></a></div>
    </div>
    <div id="dbg3"></div>
    <div id="dbg4"></div>
    <div id="dbg5"></div>
    <div id="dbg6"></div>

    <div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br/>
        本活动最终解释权归益盟股份有限公司　沪ICP备06000340
    </div>
    <div class="txt1"><span class="djs">0000</span></div>
    <div class="pf">
        <div class="main"><a href="javascript:void(0)" class="btn4 dh popupD2"></a></div>
    </div>
</div>

<div class="h" style="display: none;">
    <div class="tc" style="display: none;"><a href="javascript:void(0)" class="close"></a></div>
    <div class="tc_qgn" style="display: none;"><a href="javascript:void(0)" class="close"></a></div>
</div>
<div class="bg" style="display: none">
    <div class="tclogin" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<script>
    $(window).scroll(function(e){
        if($(window).scrollTop() >= 740){
            $('.pf').fadeIn(300);
        }else{
            $('.pf').fadeOut(300);
        }
    });

</script>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renewds/20241204/js/main.js?r=20241213'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='http://api2.tongji.emoney.cn/scripts/emoneyanalyticspv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renewds_20241204";//模块名称(焦点图2)
    var Remark = "大师续费抢票";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script></body>
</html>
