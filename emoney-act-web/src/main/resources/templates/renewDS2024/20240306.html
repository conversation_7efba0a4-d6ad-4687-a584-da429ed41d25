<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renewds/20240325/css/style.css?r=20240325'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_djs">
    <div class="main">
    <div class="djs2">
        <div class="t">00</div>
        <div class="s">00</div>
        <div class="f">00</div>
        <div class="m">00</div>
    </div></div></div>
<div class="img_2">
    <div class="main">
        <div class="djs">仅剩<span class="a1"></span>席位</div>
        <a href="javascript:void(0)" class="btn1 dh an1" name="payurl"><img th:src="@{${staticPath}+'static/renewds/20240325/images/ico1.png'}" class="s1 dh2" alt=""></a>
    <div class="btn3">活动规则&gt;</div>
    </div>
</div>
<div class="img_3">
    <div class="main">
        <div class="zp">
            <ul>
                <li data-num="3" class="on"></li>
                <li data-num="7"></li>
                <li data-num="5"></li>
                <li data-num="6"></li>
                <li></li>
                <li data-num="2"></li>
                <li data-num="1"></li>
                <li data-num="4"></li>
                <li data-num="8"></li>
            </ul>
        </div>
    <a href="javascript:void(0)" class="btn2 dh" id="doLottery">立即<br />抽奖<img th:src="@{${staticPath}+'static/renewds/20240325/images/ico2.png'}" class="s2 dh2" alt=""></a>
    <a href="javascript:void(0)" class="btn2 dh" id="renew" name="payurl" style="display: none;">续费<br />抽奖<img th:src="@{${staticPath}+'static/renewds/20240325/images/ico2.png'}" class="s2 dh2" alt=""></a>

    <div class="txt1">剩余<span class="f1"></span>次抽奖机会</div>
    <marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="2" direction="up" class="txt3">恭喜EM****35用户喜获<span class="yellow">投研速递365天</span><br />
        恭喜EM****47用户成功续费<br />
        恭喜EM****01用户成功续费<br />
        恭喜EM****87用户喜获<span class="yellow">使用期30天</span><br />
        恭喜EM****92用户成功续费<br />
        恭喜EM****32用户喜获<span class="yellow">使用期90天</span><br />
        恭喜EM****21用户喜获<span class="yellow">使用期30天</span><br />
        恭喜EM****18用户成功续费<br />
        恭喜EM****28用户成功续费<br />
        恭喜EM****74用户喜获<span class="yellow">投研速递365天</span><br />
        恭喜EM****55用户成功续费<br />
        恭喜EM****64用户喜获<span class="yellow">888积分</span><br />
        恭喜EM****38用户喜获<span class="yellow">投研速递180天</span><br />
        恭喜EM****29用户成功续费<br />
        恭喜EM****97用户成功续费<br />
        恭喜EM****72用户喜获<span class="yellow">666积分</span><br />
        恭喜EM****81用户成功续费<br />
        恭喜EM****41用户喜获<span class="yellow">投研速递90天</span><br />
        恭喜EM****39用户喜获<span class="yellow">使用期60天</span><br />
        恭喜EM****85用户喜获<span class="yellow">投研速递365天</span><br />
        恭喜EM****59用户喜获<span class="yellow">使用期30天</span><br />
        恭喜EM****63用户成功续费<br />
        恭喜EM****79用户喜获<span class="yellow">使用期90天</span><br />
        恭喜EM****18用户喜获<span class="yellow">投研速递365天</span><br />
        恭喜EM****66用户成功续费<br />
        恭喜EM****94用户喜获<span class="yellow">888积分</span><br />
        恭喜EM****89用户喜获<span class="yellow">投研速递180天</span><br />
        恭喜EM****44用户成功续费<br />
        恭喜EM****37用户喜获<span class="yellow">投研速递365天</span><br />
        恭喜EM****28用户喜获<span class="yellow">使用期60天</span>
    </marquee>

    <div class="txt4" id="recordWrap">
    </div>
</div></div>
<div class="img_4"><div class="main"><iframe frameborder="0" webkitallowfullscreen="true" mozallowfullscreen="true" allowfullscreen="true" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=0e63080af89e437a8e971adea2c5e6b2 &rep=1&py=0" scrolling="no" class="txt5"></iframe></div></div>
<div class="img_5"></div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><div class="main"><a href="javascript:void(0)" class="btn1 dh an2" name="payurl"><img th:src="@{${staticPath}+'static/renewds/20240325/images/ico1.png'}" class="s3 dh2" alt=""></a></div></div>

<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc1" style="display: none;">
        <a href="javascript:void(0)" class="tc1-btn dh"></a>
        <a href="javascript:void(0)" class="close" style="position: absolute;right: -30px;top: 150px;"></a>
        <div class="txt">获得使用期30天</div>
    </div>
</div>

<div class="h">
    <div class="hdgz"><div class="t1">活动规则</div>
        活动说明：<br />
        1.活动期内成功续费智盈大师方可参与抽奖;<br/>
        2.续费1年有一次抽奖机会，续费3年有3次抽奖机会；<br/>
        3.抽奖机会自续费成功起保留1个月，请及时参与抽奖；<br/>
        4.积分自抽到之日起有效期3个月，逾期不用将自动失效；<br/>
        5.投研速递（重新登录软件后）在软件左上角基本面频道-投研速递位置查看。
        <br />
        <br />
        活动时间：2024/3/6-2024/3/31<br />
        活动对象：智盈大师用户<br />
        <br />
        本次活动最终解释权归益盟股份所有
        <a href="javascript:void(0)" class="close"></a>
    </div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script type="text/javascript" th:src="@{${staticPath}+'static/renewds/20240325/js/main.js?r=20240325'}"></script>
<script type="text/javascript">
    var www="../";
</script>
<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数
        days = checkTime(days);
        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs2=setInterval("SetTimeout(2024,3,31,24,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0" +i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2024/3/31 24:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs2);
        $(".t").html("00");
        $(".s").html("00");
        $(".f").html("00");
    }
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
