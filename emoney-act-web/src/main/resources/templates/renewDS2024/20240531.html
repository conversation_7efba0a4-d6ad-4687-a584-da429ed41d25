<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renewds/20240531/style/common_new.css?r=20240325'}" rel="stylesheet" type="text/css" />
    <link href="https://www.emoney.cn/dianjin/bb/animate.css" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>
<body>
<div class="bod">
    <div class="dbg1"></div>
    <div class="dbg2"><div class="main"><div class="pic1 wow bounceIn" data-wow-duration="1000ms"></div>
        <div class="pic2 wow fadeInLeft" data-wow-duration="1000ms" data-wow-delay=".8s"></div></div></div>
    <div class="dbg3">
        <div class="main">
            <a href="javascript:void(0)" class="btn2 an1 dh"></a>
            <a href="javascript:void(0)" class="b txt1 hdgzk"><span style="
    color: darkgrey;
    text-decoration: underline;
">活动规则</span></a>
        </div>
    </div>
    <div class="dbg4"></div>
    <div id="dbg5"></div>
    <div class="dbg6"><div class="main"><a href="javascript:void(0)" class="btn2 an2 dh"></a></div>
    </div>
    <div class="dbg7">
        <div class="main">
            <a href="javascript:void(0)" class="btn2 an3 dh"></a>
        </div>
    </div>

    <div id="dbg8"></div>
    <div id="dbg9"></div>
    <div id="dbg10"></div>
    <div id="dbg11"></div>
    <div id="dbg12"></div>
</div>

<div class="footer">
    欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br/>
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340 </div>
<div class="pf">
    <div class="main"><a href="javascript:void(0)" class="btn3 dh" clickkey="20230222Act" clickdata="btn2"></a></div>
</div>
<div class="h"><div class="hdgz"><a href="javascript:void(0)" class="close hdgzx"></a>
    <strong>活动说明：</strong>
    <ul>
        <li>
            用户续费并完成测评后，需到益盟官网下载PC版最新版大师安装包，才能显示对应的菜单权限；<br/>
            ①【α+β研究池】：仅限PC端使用，APP暂无此功能；<br/>
            ②【ETF深度掘金功能】:目前功能还在开发中，预计7月中旬上线；<br/>
            ③【投研速递】：除独家调研及行业追踪外，新增天玑2大权限：益研晨会（天玑专享）及行业深度（天玑专享）</li>
        <li>插件赠送规则: 续费1年原未到期插件对齐使用期；续费3年四大插件功能对齐软件使用期；完成适当性测评后开通。</li>
        <li>插件说明：<br/>
            智盈大师掘金版(三年版)含4个插件功能：黑马雷达、席位龙虎榜、机构调研、主力识别；<br/>
            智盈大师深度资金版(三年版)含2个插件功能:席位龙虎榜、机构调研， 深度资金版标配功能中包含黑马雷达、主力识别功能；</li>
        <li>活动时间: 2024/6/1-2024/6/30</li>
        <li>活动对象:智盈大师用户</li>
    <strong>*本活动最终解释权归益盟股份有限公司所有。</strong>
    </ul>
</div></div>

<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<script src="https://www.emoney.cn/dianjin/bb/wow2.js"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js'}"></script>
<script type="text/javascript" th:src="@{${staticPath}+'static/renewds/20240531/js/main.js?r=20240325'}"></script>
<script type="text/javascript">
    var www="../";
</script>
<script type="text/javascript">document.write(unescape(
    '%3Cscript src=\'https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F\' type=\'text/javascript\'%3E%3C/script%3E'))</script>
</body>
</html>