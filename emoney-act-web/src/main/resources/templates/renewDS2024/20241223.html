<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>双十二大促 备战跨年龙</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="决胜2025，擒龙上新，五星送礼，大师加量"/>
    <link th:href="@{${staticPath}+'static/renewds/20241223/style/css.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH('EM_FUNC_SET_STYLE', '/caption=')
                PC_JH('EM_FUNC_SET_TITLE', '')

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"><div class="main"><div class="jg1"></div><!--价格2<div class="jg2"></div>--><a href="javascript:void(0)" class="btn1 dh" clickkey="btn1" clickdata="btn1"></a></div></div>
<div class="img_3"><div class="main"><div class="t1"><a href="javascript:void(0)" class="btn4 dh" clickkey="btn4" clickdata="btn4"></a></div><!--状态2--><div class="t2" style="display: none;"></div></div></div>
<div class="img_4"></div>
<div class="img_5"></div>
<div class="img_6"><div class="main"><div class="ico3 wobble2"></div><a href="javascript:void(0)" id="btn-lottery" class="btn2 dh" clickkey="btn2" clickdata="btn2"></a><div class="ico4">剩余砸蛋次数：<span id="rft_num"></span>次</div>
    <div class="jp" id="my_list">恭喜您，砸中45天使用期<br></div>
    <div class="hdgz">①12月4日至12月31日参与大师续费的用户可获得砸金蛋机会，续约1年版用户可获得1次砸金蛋机会，续约3年版用户可获得3次砸金蛋机会。多续多砸，无砸金蛋限制。<br>
        ②砸金蛋活动奖品将在活动结束后的10个工作日内发送，限时活动，错过不再有。<br>
        ③若发生退货行为，奖品一律退还<br>
        ④活动最终解释权归益盟股份有限公司所有</div></div></div>
<div class="img_7">
    <div class="main"><!-- 代码 开始 -->
        <ul class="silder_nav">
            <li class="">擒龙2.0 上新穿越龙头</li>
            <li class="">主题布局上新 三步锁定强轮动</li>
        </ul>
        <div class="slider_name slider_box pic1">
            <ul class="silder_con">
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renewds/20241223/images/a1.png'}" alt=""></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/renewds/20241223/images/a2.png'}" alt=""></li>
            </ul>
        </div>
        <script type="text/javascript" th:src="@{${staticPath}+'static/renewds/20241223/js/jquery.slides.js'}"></script>
        <!-- 代码 结束 -->
    </div></div>
<div class="foot">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><div class="main"><a href="javascript:void(0)" class="btn3 dh" clickkey="btn3" clickdata="btn3"></a></div></div>
<div class="h" style="display: none">
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2" id="btnclean">清除</a>
            </div>
        </div>
    </div>
    <div class="tc" style="display: none"><a href="javascript:void(0)" class="close"></a><div id="pname">成功砸中45天试用期</div><a href="javascript:void(0)" class="tc-btn"></a></div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renewds/20241223/js/main.js?r=20250106'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renewds_20241223";//模块名称(焦点图2)
    var Remark = "大师续费";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script></body>
</html>
