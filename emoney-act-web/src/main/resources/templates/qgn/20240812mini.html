<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title></title>
    <meta http-equiv="X-UA-Compatible" content="IE=8">
    <meta http-equiv="Expires" content="0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <meta http-equiv="Cache" content="no-cache">
    <meta http-equiv="Cache-control" content="no-cache">
    <link th:href="@{${staticPath}+'static/qgn/20240812/style/main_tc.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var www="../";
    </script>
</head>

<body><div class="bod"><div id="content">
    <div class="content">
        <div class="bzts"><input type="checkbox" id="notips"> 不再提示</div>
        <a href="javascript:void(0)" class="btn2 dh popupD" clickkey="btn" clickdata="btn"></a>
        <a href="javascript:void(0)" class="btn1 dh popupD" clickkey="btn" clickdata="btn"></a>
    </div></div>
</div>
<div class="h" style="display: none;">
    <div class="tc_qgn" style="display: none;"><a href="javascript:void(0)" class="close"></a><a href="javascript:void(0)" class="tc_btn1 dh"></a></div>
    <div class="tc1" style="display: none;"><a href="javascript:void(0)" class="close"></a><a href="javascript:void(0)" class="tc_btn2 dh"></a></div>
    <div class="tc2" style="display: none;"><a href="javascript:void(0)" class="close"></a></div>
    <div class="tc3" style="display: none;"><a href="javascript:void(0)" class="close"></a></div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script src="https://www.emoney.cn/dianjin/js/ad_production.js" type="text/javascript"></script>
<script th:src="@{${staticPath}+'static/qgn/20240812/js/main.js?r=20240218'}"></script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script src="https://imgtongji.emoney.cn/scripts/https/emoneyanalytics.js" type="text/javascript"></script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "tc_qgn20240812";//大师上拽
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>

</html>