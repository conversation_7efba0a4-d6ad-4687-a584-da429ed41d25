<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>掘金指数ETF</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="「决战大波段」延展投资课＆高端功能组合来袭，备战底部价值掘金指数ETF">
    <link th:href="@{${staticPath}+'static/qgn/20231109/style/common.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        var phoneWidth = parseInt(window.screen.width);
        var phoneScale = phoneWidth/750;
        var ua = navigator.userAgent;
        if (/Android (\d+\.\d+)/.test(ua)){
            var version = parseFloat(RegExp.$1);
            if(version>2.3){
                document.write('<meta name="viewport" content="width=750, minimum-scale ='+phoneScale+', maximum-scale ='+phoneScale+', target-densitydpi=device-dpi">');
            }else{
                document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
            }
        } else {
            document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
        }
    </script>
    <script type="text/javascript">
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj =
                    GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {}
        }
        (function() {

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>
<body>
<div class="bod">
    <div id="dbg1"></div>
    <div id="dbg2"></div>
    <div id="dbg3"></div>
    <div id="dbg4"></div>
    <div id="dbg5"><div class="main"><!-- 代码 开始 -->
        <ul class="silder_nav">
            <li class="">主题基金优选</li>
            <li class="">基金估值带</li>
        </ul>
        <div class="slider_name slider_box pic1">
            <ul class="silder_con">
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qgn/20231109/images/p1.png'}" alt=""></li>
                <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qgn/20231109/images/p2.png'}" alt=""></li>
            </ul>
        </div>
        <script type="text/javascript" th:src="@{${staticPath}+'static/qgn/20231109/js/jquery.slides.js'}"></script>
        <!-- 代码 结束 -->
    </div></div>
</div>
<div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br>
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><div class="main"><a href="javascript:void(0)" class="btn dh" clickkey="btnqgn" th:clickdata="(${isLogin}?${loginUserInfo.uid}:'')"></a></div></div>
<div class="h">
</div>
<div class="bg" style="display: none">
    <div class="tc1" style="display: none;"><a href="javascript:void(0)" class="close"></a></div>
    <div class="tc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}" />
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var actcode = /*[[${actcode}]]*/ "0";
    var www="../";
    var cookiename = "emoney.act20231109";
    var flag = true;
    var limitMsg = "本活动仅限智盈用户参与。";
    $(document).ready(function() {
        var uid = "";
        var pid = "";
        var uname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        if (isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            isSubmit = /*[[${isSubmit}]]*/ "";
        } else {
            //未登录
            $(".bg").show();
            $(".tc").show();
        }

        if (!!isSubmit) {
            $('.dh').removeClass("btn").addClass("btnh");
            flag = false;
        }

        $('.dh').click(function () {
            if (!checkPermission(pid)) {
                return false;
            }

            if (flag) {
                flag = false;
                //领取
                $.ajax({
                    type: 'get',
                    url: 'sendprivilege_qgn',
                    dataType: 'json',
                    data: {
                        "actcode": "privilege20231109",
                        "actType": 2,
                        "activityID": "PAC1231106181538635",
                        "reason": "基金延展投资课活动"
                    },
                    success: function (data) {
                        flag = true;
                        if (data.code == "200") {
                            //变灰色
                            $('.dh').removeClass("btn").addClass("btnh");
                            $(".bg").show();$(".tc1").show();
                        }else{
                            layer.msg(data.msg);
                        }
                    }
                });
            }
        });
        $(".close").click(function(){
            $(".bg").hide();
        });

    });
    // 检查用户是否有权限参与
    function checkPermission(pid) {
        if (pid != "888010000" && pid != "888020000" && pid != "888080000") {
            layer.msg(limitMsg);
            return false;
        }
        return true;
    }
</script>
<script src="https://www.emoney.cn/dianjin/bb/wow2.js"></script>
<link href="https://www.emoney.cn/dianjin/bb/animate.css" rel="stylesheet" type="text/css" />
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "qgn_20231109";//模块名称(焦点图2)
    var Remark = "基金延展投资课";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>