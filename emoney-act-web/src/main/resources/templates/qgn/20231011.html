<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>高端利器免费抢</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="5大高端利器免费抢">
    <link th:href="@{${staticPath}+'static/qgn/20231011/style/common.css'}" rel="stylesheet" type="text/css" />
    <link th:href="@{${staticPath}+'static/qgn/20231011/js/popup.css'}" rel="stylesheet" type="text/css" >
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qgn/20231011/js/popup.js'}" type="text/javascript"></script>
    <script th:src="@{${staticPath}+'static/qgn/20231011/js/json2.js'}" type="text/javascript"></script>
    <script type="text/javascript">
        //----函数定义-----
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj = GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {}
        }

        function getQueryString(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);return null;
        }

        function setCookie(c_name, value, expiredays) {
            var exdate = new Date();
            exdate.setDate(exdate.getDate() + expiredays);
            document.cookie = c_name + "=" + escape(value) + ";expires=" + exdate.toGMTString() + ";path=/";
        }

        function getCookie(c_name) {

            if (document.cookie.length > 0) {
                c_start = document.cookie.indexOf(c_name + "=")

                if (c_start != -1) {
                    c_start = c_start + c_name.length + 1
                    c_end = document.cookie.indexOf(";", c_start)
                    if (c_end == -1) c_end = document.cookie.length
                    return unescape(document.cookie.substring(c_start, c_end))
                }
            }
            return ""
        }
    </script>
    <script type="text/javascript">
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "5大盯盘利器免费送");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>
<body>

<div class="bod">
    <div id="dbg1"></div>
    <div id="dbg2"></div>
    <div id="dbg3"></div>
    <div id="dbg4"></div>
    <div id="dbg5"></div>
    <div id="dbg6"></div>
    <div id="dbg7"><div class="main"><!-- 代码 开始 -->
        <div class="al1"><div class="d1"><a href="javascript:void(0)" class="d2a"></a></div>
            <div class="silder_nav">
                <li class="">超级资金</li>
                <li class="">资金博弈</li>
                <li class="">大单比率</li>
                <li class="">资金流变</li>
            </div>
            <div class="slider_name slider_box">

                <ul class="silder_con">
                    <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qgn/20231011/images/a1.png'}" alt=""></li>
                    <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qgn/20231011/images/a2.png'}" alt=""></li>
                    <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qgn/20231011/images/a3.png'}" alt=""></li>
                    <li class="silder_panel clearfix"><img th:src="@{${staticPath}+'static/qgn/20231011/images/a4.png'}" alt=""></li>
                </ul>
            </div>
        </div>
        <div class="al2" style="display: none"><div class="d2"><a href="javascript:void(0)" class="d1a"></a></div>
            <div class="silder_nav2">
                <li class="">量王叠现-PC端</li>
                <li class="">量王叠现-手机端</li>
            </div>
            <div class="slider_name2 slider_box2">

                <ul class="silder_con2">
                    <li class="silder_panel2 clearfix2"><img th:src="@{${staticPath}+'static/qgn/20231011/images/b1.png'}" alt=""></li>
                    <li class="silder_panel2 clearfix2"><img th:src="@{${staticPath}+'static/qgn/20231011/images/b2.png'}" alt=""></li>
                </ul>
            </div>
        </div>
        <!-- 代码 结束 --><a href="javascript:void(0)" class="btn2 dh popupD"></a><div class="ico an2"></div></div></div>
    <div class="footer">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br/>
        本活动最终解释权归益盟股份有限公司　沪ICP备06000340
    </div>
    <div class="pf">
        <div class="main"><a href="javascript:void(0)" class="btn4 dh popupD"></a></div>
    </div>
    <div class="pf2"><a href="javascript:void(0)" class="btn1 dh popupD"></a></div>
</div>
<div class="h">
    <div class="tc1"><a href="javascript:void(0)" class="close"></a></div>
</div>
<div class="bg" style="display: none">
    <div class="tc" style="display: none">
        <div class="bt1">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a id="btnclean" href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div><input type="hidden" id="hid_actcode" th:value="${actcode}" />
<script type="text/javascript" th:src="@{${staticPath}+'static/qgn/20231011/js/jquery.slides.js'}"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var actcode = /*[[${actcode}]]*/ "0";
    var www="../";
    var cookiename = "emoney.act20231011";
    var flag = true;
    $(document).ready(function() {
        var uid = "";
        var pid = "";
        var uname = "";
        var realname = "";
        var maskname = "";
        var isLogin = /*[[${isLogin}]]*/ "0";
        if(isLogin == "1") {
            uid = /*[[${loginUserInfo.uid}]]*/ "0";
            pid = /*[[${loginUserInfo.pid}]]*/ "0";
            uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            realname = /*[[${loginUserInfo.RealName}]]*/ "";
        }else{
            //未登录
            $(".bg").show();
            $(".tc").show();
        }

        var val = getCookie(cookiename);
        if (val != null && val != "") {
            $(".bod").attr("class","bo");
            flag = false;
        }

        //页面展示后 点击领取
        $(".popupD").click(function () {
            if (!checkPermission(pid)) {
                return false;
            }

            if (flag) {
                flag = false;
                //领取
                $.ajax({
                    type: 'get',
                    url: 'sendprivilege_qgn',
                    dataType: 'json',
                    data: {
                        "actcode": "privilege20231011",
                        "actType": 2,
                        "activityID": "PAC1230913174804755",
                        "reason": "2023年10月11日5大高端利器免费抢"
                    },
                    success: function (data) {
                        flag = true;
                        if (data.code == "200") {
                            var popup = new Popup({
                                'type': 'info',
                                'text': '',
                                'background': 'url(../images/tc.png) no-repeat center',
                                'width': '454px',
                                'height': '419px'
                            });

                            //变灰色
                            setCookie(cookiename, getQueryString("uid"));
                            $(".bod").attr("class", "bo");

                            pushdatatocmp(uid,"AC588Pop20231011");

                        } else {
                            layer.msg(data.msg);
                        }
                    }
                });
            }
        });

        var timer,
            visibleDiv = $('.al1'),
            hiddenDiv = $('.al2');

        //初始设定: div1显示，div2隐藏
        visibleDiv.show();
        hiddenDiv.hide();

        function toggleDivs() {
            // 交换div的显示状态
            var temp = visibleDiv;
            visibleDiv = hiddenDiv;
            hiddenDiv = temp;
            visibleDiv.show();
            hiddenDiv.hide();
        }

        // 开始时启动计时器
        timer = setInterval(toggleDivs, 20000); // 每20秒切换一次

        // 在两个div上添加鼠标进入和离开的事件处理
        $('.al1, .al2').on({
            mouseenter: function() {
                // 鼠标进入时停止计时器
                clearInterval(timer);
            },
            mouseleave: function() {
                // 鼠标离开时恢复计时器
                timer = setInterval(toggleDivs, 20000);
            }
        });
        $(".d2a").click(function(){
            $(".al1").hide();
            $(".al2").show();
        });
        $(".d1a").click(function(){
            $(".al1").show();
            $(".al2").hide();
        });
    });

    // 检查用户是否有权限参与
    function checkPermission(pid) {
        if (pid != "888010000") {
            if(pid =='888020000' || pid=='888080000'){
                layer.msg("您软件已有该指标");
                return false;
            }else{
                layer.msg("本活动仅限小智盈用户参与");
                return false;
            }
        }
        return true;
    }
    function pushdatatocmp(uname, adcode) {
        var data = {
            "appid": '10088',
            "logtype": 'click',
            "mid": '',
            "pid": getQueryString("pid"),
            "sid": getQueryString("sid"),
            "tid": getQueryString("tid"),
            "uid": getQueryString("uid"),
            "uname": uname,
            "adcode": adcode,
            "targeturl": "",
            "pageurl": window.top.location.href
        }
        var saasUrl = "https://ds.emoney.cn/saas/queuepush";
        var saasSrc = saasUrl + "?v=" + Math.random()
            + "&queuekey=EMoney:softsupport:ActivityClickToCMPQueueID"
            + "&message=" + encodeURIComponent(JSON.stringify(data));

        var elm = document.createElement("img");
        elm.src = saasSrc;
        elm.style.display = "none";
        document.body.appendChild(elm);
    }
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
