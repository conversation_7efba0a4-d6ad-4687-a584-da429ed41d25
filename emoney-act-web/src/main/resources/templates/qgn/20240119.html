<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>三步擒龙解锁活动</title>
    <link rel="shortcut icon" href="https://www.emoney.cn/favicon.ico" type="image/x-icon">
    <meta name="description" content="三步擒龙解锁活动">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link th:href="@{${staticPath}+'static/qgn/20240119/styles/style.css?r=20240129'}" rel="stylesheet" type="text/css" />
</head>
<body>
<div class="container" id="container">
    <div class="header">
        <div class="btn-video-play" id="videoPlay" clickkey="videoPlay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="播放视频"><div class="iner"><div class="btn-play"></div></div></div>
    </div>
    <div class="main-mid">
        <div class="card-pack" id="cardPack">
            <div class="card">
                <div th:class="'iner-box ' + @{(${showCard == 1}?'sel':'')}" data-num="1" data-actcode="qgn2024011901">
                    <div class="card-lock card-san"></div>
                    <div class="card-unlock card-san"></div>
                </div>
                <div class="title-box">1.22-1.28<span class="gold">解锁</span></div>
            </div>
            <div class="card">
                <div th:class="'iner-box ' + @{(${showCard == 2}?'sel':'')}" data-num="2" data-actcode="qgn2024011902">
                    <div class="card-lock card-bu"></div>
                    <div class="card-unlock card-bu"></div>
                </div>
                <div class="title-box">1.29-2.4<span class="gold">解锁</span></div>
            </div>
            <div class="card">
                <div th:class="'iner-box ' + @{(${showCard == 3}?'sel':'')}" data-num="3" data-actcode="qgn2024011903">
                    <div class="card-lock card-qin"></div>
                    <div class="card-unlock card-qin"></div>
                </div>
                <div class="title-box">2.5-2.11<span class="gold">解锁</span></div>
            </div>
            <div class="card">
                <div th:class="'iner-box ' + @{(${showCard == 4}?'sel':'')}" data-num="4" data-actcode="qgn2024011904">
                    <div class="card-lock card-long"></div>
                    <div class="card-unlock card-long"></div>
                </div>
                <div class="title-box">2.12-2.18<span class="gold">解锁</span></div>
            </div>
        </div>
    </div>
    <div class="btn-footer">
        <button class="btn-unlock">解锁按钮</button>
    </div>
    <div class="footer">
        <div class="link-rules" id="linkRules">
            活动规则说明
        </div>
    </div>

</div>
<div class="loadingmark" id="loadingmark">
    <div class="loading"></div>
</div>
<input type="hidden" id="hid_showcard" th:value="${showCard}">
<input type="hidden" id="hid_uid" th:value="(${isLogin == '1'}?${loginUserInfo.uid}:'')">
<script type="text/javascript" src="https://static.emoney.cn/static/libs/jquery/3.7.1/jquery.min.js"></script>
<script type="text/javascript" src="https://static.emoney.cn/static/libs/dayjs/1.10.3/dayjs.min.js"></script>
<script type="text/javascript" src="https://static.emoney.cn/static/libs/layer/3.5.1/layer.js"></script>
<script type="text/javascript" src="https://static.emoney.cn/static/libs/echarts/5.4.3/echarts.min.js"></script>
<script type="text/javascript"> ~(function () { })();  </script>
<noscript><strong>请开启 JavaScript，应用才能继续进行</strong></noscript>
<script>
    window.COMMONFUNC = { ClosePopwin: function () { } };
    var actcodeList = "qgn2024011901,qgn2024011902,qgn2024011903,qgn2024011904";
    // 环境判断
    $(function(){
        var showCard = $("#hid_showcard").val();
        var uid = $("#hid_uid").val();
        //点击卡片 选中
        var cards = $('#cardPack .card').click(function(){
            var $this = $(this);
            $(".iner-box").removeClass("sel");
            $this.find(".iner-box").addClass("sel");
        })

        //解锁
        $(".btn-unlock").click(function () {
            if (!uid) {
                layer.msg("您还未登录，请登录后解锁");
                return false;
            }
            var $unlock = $(".btn-unlock");
            var $currBox = $(".iner-box.sel");
            var $currCard = $(".iner-box.sel").parent();
            var cardnum = $currBox.attr("data-num");
            var actcode = $currBox.attr("data-actcode");

            if ($currCard.hasClass('unlock')) {
                return false;
            }

            if (showCard == "4" || parseInt(cardnum) <= parseInt(showCard)) {
                $currCard.addClass('unlock');
                $currCard.find(".title-box").html("已解锁").css({"color": "#fcd9ab"});
                //记录解锁状态
                addCount(actcode, uid);

                $unlock.attr("clickkey", actcode);
                $unlock.attr("clickdata", uid);
                $unlock.attr("clickRemark", "卡片" + cardnum);
                //打点
                pageClick($unlock);
            } else {
                var tipMsg = cardnum == "2" ? "1月29日—2月04日" : cardnum == "3" ? "2月5日—2月11日" : "2月12日—2月18日";
                layer.msg("请在" + tipMsg + "解锁");
            }
        });
        // 视频
        $("#videoPlay").click(function() {
            layer.open({
                type: 2,
                title: '视频播放',
                shadeClose: false,
                shade: false,
                skin: 'video-window',
                maxmin: true, //开启最大化最小化按钮
                area: ['893px', '560px'],
                success: function (layero, index) {
                    layer.iframeAuto(index)
                    $('iframe', layero[0]).attr('allowfullscreen', 'true').css({'height': '100%'})
                },
                content: 'https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=2f6bc8f0b7114fee9de29bdf47fb8fd6&rep=1&py=0'
            });
        });

        // 活动规则：
        $("#linkRules").click(function(){
            layer.open({
                type: 1,
                title: false,
                closeBtn: 1,
                shadeClose: true,
                scrollbar: false,
                skin: 'active-rules',
                content: `<div class="active-content">
              <b> 解锁规则：</b>
              <p>1、活动对象：智盈用户;</p>
              <p>2、活动时间：2024年1月19日—2024年2月18日</p>
              <p>3、活动规则：在2月18日前解锁4张福龙卡，可参与2月18日抢“三步擒龙”和资金指标的优先体验资格。</p>
              <p>卡片“三”：选中卡片“三”，在1月28日23:59:59前点击解锁按钮，即可解锁“三”福龙图案；</p>
              <p>卡片“步”：选中卡片“步”，在1月29日00:00:00至2月04日23:59:59时间内，点击解锁按钮，即可解锁“步”福龙图案；</p>
              <p>卡片“擒”：选中卡片“擒”，在2月05日00:00:00至2月11日23:59:59时间内，点击解锁按钮，即可解锁“擒”福龙图案；</p>
              <p>卡片“龙”：选中卡片“龙”，在2月12日00:00:00至2月18日23:59:59时间内，点击解锁按钮，即可解锁“龙”福龙图案；</p>
              <p>4、活动最终解释权归益盟所有。</p>
              </div>`
            });
        })

        if(!!uid){
            InitCardStatus(uid);
        }
    });

    function addCount(actcode,uid){
        //名额-1
        $.ajax({
            type: 'get',
            url: '/activity/user/addcountbyactcode?actcode=' + actcode,
            dataType: 'jsonp',
            data: {
                uid: uid ,
                value: new Date().getTime()
            },
            success: function (data) {
                if(data.code=="200"){
                }
            }
        });
    }
    function InitCardStatus(uid) {
        $.ajax({
            type: 'get',
            url: '/activity/user/issubmitbyactcodes?actcodes=' + actcodeList,
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if (data.code == "200") {
                    if (!!data.data) {
                        var retList = data.data.split(",");
                        for(var i=0;i<retList.length;i++){
                            if(!!retList[i]){
                                $("#cardPack .card").eq(i).addClass("unlock");
                                $("#cardPack .card").eq(i).find(".title-box").html("已解锁").css({"color":"#fcd9ab"});
                            }
                        }
                    }
                }
            }
        });
    }
    function pageClick(obj){
        var App = "10013";   //APPID 没有请申请
        var Module = "qgn20240119";//模块名称
        var Remark = "";     //备注可为空
        var ClickFlag = true;//默认为true
        var Host = "https://api2-tongji.emoney.cn";
        var ClickUrl = Host + "/Page/PageClick";
        var PageViewUrl = Host + "/Page/PageView";
        var pageUrl = window.top.location.href;

        var _clickkey = obj.attr("clickkey");
        var _clickdata = obj.attr("clickdata");
        var _clickremark = obj.attr("clickremark");
        var _htmltype = obj.attr("type");
        if (App != "" && _clickdata != "") {
            var src = ClickUrl + "?v=" + Math.random()
                + "&app=" + App
                + "&module=" + Module
                + "&clickkey=" + _clickkey
                + "&clickdata=" + _clickdata
                + "&clickremark=" + _clickremark
                + "&htmltype=" + _htmltype
                + "&pageurl=" + encodeURIComponent(pageUrl)
                + "&remark=" + Remark;
            var elm = document.createElement("img");
            elm.src = src;
            elm.style.display = "none";
            document.body.appendChild(elm);
        }
    }
    // 客户端调用
    (function() {
        function GetExternal() {
            return window.external.EmObj;
        }

        function PC_JH(type, c) {
            try {
                var obj =
                    GetExternal();
                return obj.EmFunc(type, c);
            } catch (e) {}
        }

        function LoadComplete() {
            try {
                PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
            } catch (ex) {}
        }

        function EM_FUNC_HIDE() {
            try {
                PC_JH("EM_FUNC_HIDE", "");
            } catch (ex) {}
        }

        function EM_FUNC_SHOW() {
            try {
                PC_JH("EM_FUNC_SHOW", "");
            } catch (ex) {}
        }

        function IsShow() {
            try {
                return PC_JH("EM_FUNC_WND_ISSHOW", "");
            } catch (ex) {
                return "0";
            }
        }

        function openWindow() {
            LoadComplete();
            PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
            PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
            PC_JH("EM_FUNC_DLGSIZE","820&1300");


            if (IsShow() != "1") {
                EM_FUNC_SHOW();
            }
        }
        openWindow();
    })();

</script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "qgn20240119";//模块名称(焦点图2)
    var Remark = "小智盈续费";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>