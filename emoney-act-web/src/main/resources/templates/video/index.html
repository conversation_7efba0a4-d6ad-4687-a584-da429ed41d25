<!DOCTYPE html >

<html>
<head>
    <meta name="viewport" content="width=device-width"/>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, minimal-ui">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="keywords" content="">
    <meta name="description" content="">
    <title>益盟操盘手</title>
    <link rel="stylesheet" th:href="@{/video/css/fonticon.css?rev=20230904}">
    <!-- <link rel="stylesheet" href="http://static.dsclient.emoney.cn/livevideo/static/css/biglivewebv2.css?rev=20201012"> -->
    <link rel="stylesheet" th:href="@{/video/css/biglive.css?rev=20201208}">
    <script src="https://static-dsclient.emoney.cn/livevideo/static/libs/jquery/jquery.handlebars.js?rev=20201012"></script>
    <script src="https://static-dsclient.emoney.cn/livevideo/static/libs/layer/layer.js?rev=20201012"></script>
    <script th:src="@{/video/libs/qrcode/qrcode.min.js}"></script>
    <script th:src="@{/video/libs/qrcode/qrcodeU.js}"></script>
    <style>
        .share-markbg.bg2 {
            background-image: url("https://static-dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/markbg2.png")
        }
    </style>
    <script>
        function checkBrowserEnv() {
            function browserEnv() {
                var curenv = '';
                var curSys = '';
                var ua = navigator.userAgent;
                var res;
                var browser = {
                    versions: function () {
                        return {
                            trident: ua.indexOf('Trident') > -1,
                            presto: ua.indexOf('Presto') > -1,
                            webKit: ua.indexOf('AppleWebKit') > -1,
                            gecko: ua.indexOf('Gecko') > -1 && ua.indexOf('KHTML') == -1,
                            mobile: !!ua.match(/AppleWebKit.*Mobile.*/),
                            ios: !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
                            android: ua.indexOf('Android') > -1 || ua.indexOf('Linux') > -1,
                            iPad: ua.indexOf('iPad') > -1,
                            webApp: ua.indexOf('Safari') == -1
                        }
                    }(),
                    language: (navigator.browserLanguage || navigator.language).toLowerCase()
                };
                if (browser.versions.mobile) {
                    if (ua.match(/MicroMessenger/i) == "MicroMessenger") {
                        curenv = 'weixin'
                    } else if (ua.match(/QQTheme/i) == "QQTheme") {
                        curenv = 'QQ'
                    } else if (ua.match(/WeiBo/i) == "Weibo") {
                        curenv = 'weibo'
                    } else if (ua.match(/QQBrowser/i) == "QQBrowser") {
                        curenv = 'QQbrowser'
                    } else {
                        curenv = 'otherbrowser'
                    }


                    if (browser.versions.ios) {
                        curSys = 'ios'
                    } else if (browser.versions.android) {
                        curSys = 'android'
                    } else {
                        curSys = 'others'
                    }

                    res = curSys + '-' + curenv

                } else {
                    res = 'pcbrowser'
                }
                res
                return res;
            }

            function judgeBrand(sUserAgent) {
                var sUserAgent = navigator.userAgent.toLowerCase();
                var isIphone = sUserAgent.match(/iphone/i) == "iphone";
                var isHuawei = sUserAgent.match(/huawei/i) == "huawei";
                var isHonor = sUserAgent.match(/honor/i) == "honor";
                var isOppo = sUserAgent.match(/oppo/i) == "oppo";
                var isOppoR15 = sUserAgent.match(/pacm00/i) == "pacm00";
                var isVivo = sUserAgent.match(/vivo/i) == "vivo";
                var isXiaomi = sUserAgent.match(/mi\s/i) == "mi ";
                var isXiaomi2s = sUserAgent.match(/mix\s/i) == "mix ";
                var isRedmi = sUserAgent.match(/redmi/i) == "redmi";
                var isSamsung = sUserAgent.match(/sm-/i) == "sm-";

                if (isIphone) {
                    return 'iphone';
                } else if (isHuawei || isHonor) {
                    return 'huawei';
                } else if (isOppo || isOppoR15) {
                    return 'oppo';
                } else if (isVivo) {
                    return 'vivo';
                } else if (isXiaomi || isRedmi || isXiaomi2s) {
                    return 'xiaomi';
                } else if (isSamsung) {
                    return 'samsung';
                } else {
                    return 'default';
                }
            }

            function checkAgent() {
                var sUserAgent = navigator.userAgent.toLowerCase();
                var bIsIpad = sUserAgent.match(/ipad/i) == "ipad";
                var bIsIphoneOs = sUserAgent.match(/iphone os/i) == "iphone os";
                var bIsMidp = sUserAgent.match(/midp/i) == "midp";
                var bIsUc7 = sUserAgent.match(/rv:*******/i) == "rv:*******";
                var bIsUc = sUserAgent.match(/ucweb/i) == "ucweb";
                var bIsAndroid = sUserAgent.match(/android/i) == "android";
                var bIsCE = sUserAgent.match(/windows ce/i) == "windows ce";
                var bIsWM = sUserAgent.match(/windows mobile/i) == "windows mobile";

                if (!(bIsIphoneOs || bIsMidp || bIsUc7 || bIsUc || bIsAndroid || bIsCE || bIsWM || bIsIpad)) {
                    return 'pc';
                } else if (bIsIpad) {
                    return 'pad';
                } else {
                    return 'phone';
                }
            }

            var browser = browserEnv();
            var brand = judgeBrand();
            var curAgent = checkAgent();
            var result = (browser == 'android-weixin' || browser == 'android-QQ') && brand == 'huawei' && curAgent != 'pc'
            return result
        }

        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        var IEVersion = (function () {
            var userAgent = navigator.userAgent;
            var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1;
            var isEdge = userAgent.indexOf("Edge") > -1 && !isIE;
            var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
            if (isIE) {
                var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
                reIE.test(userAgent);
                var fIEVersion = parseFloat(RegExp["$1"]);
                if (fIEVersion == 7) {
                    return 7;
                } else if (fIEVersion == 8) {
                    return 8;
                } else if (fIEVersion == 9) {
                    return 9;
                } else if (fIEVersion == 10) {
                    return 10;
                } else {
                    return 6;
                }
            } else if (isEdge) {
                return 13;
            } else if (isIE11) {
                return 11;
            } else {
                return -1;
            }
        })();
        if (!(IEVersion == -1 || IEVersion > 10 || isMobile)) {
            document.write('<link rel="stylesheet" href="https:\/\/static-dsclient.emoney.cn\/livevideo/static/css/biglivewebv3.css?rev=20201012">');
        }

        //if (IEVersion <= 7 && IEVersion != -1) {
        //    alert("当前浏览器版本较低，建议升级版本或更换浏览器观看");
        //    document.execCommand("stop");
        //}

    </script>
    <!-- <script src="http://static.dsclient.emoney.cn/livevideo/static/scripts/biglivewebv2/webzoom.js?rev=20201012"></script> -->
    <script th:src="@{/video/libs/webzoom2.js?rev=20201225}"></script>
    <script src="https://static.emoney.cn/ds/jrtpad/soft/jrtpadtongji.js"></script>
</head>
<body>
<div th:replace="video/common :: header_fragment"></div>
<div class="container">
    <div class="main-pack" id="mainPack" data-isliving="">
        <div class="rect-video p-r">
            <div class="video">
                <div class="video-detail-inner">
                    <div id="videoDetailBox" class="videoPack" style="display:none;">
                        <iframe src="" id="videoMedia" frameborder="0" th:data-midtype="${video.mediaType}"
                                style="width:100%;height:100%;"></iframe>
                    </div>
                </div>
            </div>

        </div>
        <div class="video-detail tab-cont">
            <div class="video-interactinfo">
                <div class="btn-line interactinfo" id="coursedetailBox">
                    <div style="float:right;padding-top: 8px;font-size: 18px;color:#333;" th:if="${video!=null}">
                        课程时间：[(${video?.courseTime})]
                    </div>
                    <ul>
                        <li class="sbtn btn-share" th:if="${video!=null}">
                            <span><i class="icon-1"></i></span>
                            <div class="yc p2">
                                <div class="fx">
                                    <div class="line"></div>
                                    <div class="jt"></div>
                                    <div class="ewm">
                                        <div id="imgurl"></div>
                                        微信扫码观看&amp;分享<a class="qq" id="btn_shareQQ" data-title="益盟操盘手"
                                                                data-pics="https://static.emoney.cn/webupload/LiveVideo/2020/12/anser_BEFAGdIE-DceB-EJfF-JCJA-dFbDEFAGdHcc.jpg"
                                                                data-summary="益盟操盘手" data-desc="">
                                        <div class="ico5a dh"></div>
                                        QQ好友
                                    </a><a href="javascript:void(0)" class="fz" id="btn_copyUrl">
                                        <div class="ico5b dh"></div>
                                        <span id="fzlj">复制链接</span>
                                    </a>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                <!--                    <div class="interact-btnbox">-->
                <!--                            <div class="gotoAPPbtn">-->
                <!--                                打开智盈APP观看，速度快到飞-->
                <!--                            </div>-->
                <!--                        </div>-->
            </div>
            <a href="http://wpa.b.qq.com/cgi/wpa.php?ln=1&amp;key=XzkzODAzMjUyMF80OTEyNDhfNDAwNjcwODY4OF8yXw"
               target="_blank"><span class="abtn"><i class="icon-1"></i>咨询助理</span></a>
            <div class="vd-topic">
                [[${video?.courseName}]]
                <br>
                <span style="font-size: 14px;color:#333;">[[${video?.courseTime}]]</span>
            </div>
            <div class="vd-moreinfo" id="div_h5AD">
                <img src="https://static-dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/banner.png" alt="">
            </div>
        </div>
    </div>
    <div class="ext-finfobox">
        <div class="adver-banner">
            <div class="banner" id="div_pcAD">
                <a target="_blank" href="http://pay.emoney.cn/newpay/smallpay/index?source=A12016&amp;&amp;id=3766">
                    <img height="100" width="903"
                         src="http://edf.emoney.cn/emoney/M00/00/0E/rBwBVl_FFFaARl0WAAB-RCU27hk050.jpg">
                </a>
            </div>
        </div>
        <div class="ext-right-box">
            <div class="right-box-phon"></div>
        </div>
        <div class="clearfloat"></div>
    </div>
</div>
<div th:replace="video/common :: footer_fragment"></div>

<div class="pop-viewer" id="popViewBox" th:if="${denied}" style="display: block">
    <div class="popwin">
        <div class="logo2"></div>
        <div class="dl">
            <div class="tab-login" id="tab-login" style="display: none">
                <a href="javascript:void(0)" data-tab-name="user-login"
                   th:if="${authType?.startsWith('auth') || authType?.startsWith('pw')}">帐号密码登录</a>
                <a href="javascript:void(0)" data-tab-name="sms-login"
                   th:if="${authType?.startsWith('auth') || authType?.startsWith('sms')}">验证码登录</a>
                <a href="javascript:void(0)" data-tab-name="mobile-check"
                   th:if="${authType?.startsWith('phone')}">手机号验证</a>
                <a href="javascript:void(0)" data-tab-name="code-check"
                   th:if="${authType?.startsWith('code')}">访问口令验证</a>
            </div>
            <div class="zc user-auth">
                <form data-tab-name="user-login" id="user-login" style="display: none">
                    <ul>
                        <li>
                            <input type="text" name="account" class="inp-acount" placeholder="请输入手机号／EM卡号"
                                   th:readonly="${session.user?.username != null}"
                                   th:value="${session.user?.username}"
                                   autocomplete="username" tabindex="1"/>
                            <div th:if="${session.user?.username != null}">
                                <a href="javascript:void(0)" data-cancel="account">使用另一个帐户登录</a>
                            </div>
                        </li>
                        <li>
                            <input type="password" name="password" class="inp-pwd" placeholder="请输入登录密码"
                                   autocomplete="current-password" tabindex="2"/>
                        </li>
                        <li>
                            <!--                                <label class="inp-checkbox">-->
                            <!--                                    <input type="checkbox" name="loginMod" tabindex="3"/>-->
                            <!--                                    下次自动登录-->
                            <!--                                </label>-->
                        </li>
                        <li th:if="${denied?.equals('needAuthUpgrade')}" style="color: red">
                            用户认证等级过低，请重新认证
                        </li>
                        <li th:if="${denied?.equals('pidNotMatch')}" style="color: red">
                            用户版本不符，请联系客服 10108688
                        </li>
                        <li>
                            <input type="submit" class="btn-login" tabindex="4" value="登录"/>
                        </li>
                    </ul>
                </form>
                <form data-tab-name="sms-login" id="sms-login" style="display: none">
                    <ul>
                        <li>
                            <input type="tel" name="mobile" class="inp-mobile" placeholder="请输入手机号"
                                   th:readonly="${session.user?.username != null}"
                                   th:value="${session.user?.username}"
                                   autocomplete="tel-national" tabindex="1"/>
                            <a href="javascript:void(0)" class="btn-getchkcode">获取验证码</a>
                            <div th:if="${session.user?.username != null}">
                                <a href="javascript:void(0)" data-cancel="mobile">使用另一个帐户登录</a>
                            </div>
                        </li>
                        <li>
                            <input type="tel" name="code" class="inp-pwd"
                                   placeholder="请输入验证码" autocomplete="one-time-code" maxlength="4" tabindex="2"/>
                        </li>
                        <li>
                            <!--                                <label class="inp-checkbox">-->
                            <!--                                    <input type="checkbox" name="loginMod" tabindex="3"/>-->
                            <!--                                    下次自动登录-->
                            <!--                                </label>-->
                        </li>
                        <li th:if="${denied?.equals('needAuthUpgrade')}" style="color: red">
                            用户认证等级过低，请重新认证
                        </li>
                        <li th:if="${denied?.equals('pidNotMatch')}" style="color: red">
                            用户版本不符，请联系客服 10108688
                        </li>
                        <li>
                            <input type="submit" class="btn-login" tabindex="4" value="登录"/>
                        </li>
                    </ul>
                </form>
                <form data-tab-name="mobile-check" id="mobile-check" style="display: none">
                    <ul>
                        <li>
                            <input type="tel" name="mobile" placeholder="请输入手机号" class="inp-acount"
                                   autocomplete="tel-national" tabindex="1"/>
                        </li>
                        <li th:if="${denied?.equals('pidNotMatch')}" style="color: red">
                            用户版本不符，请联系客服 10108688
                        </li>
                        <li>
                            <input type="submit" class='btn-login'/>
                        </li>
                    </ul>
                </form>
                <form data-tab-name="code-check" id="code-check" style="display: none">
                    <input type="hidden" name="id" th:value="${courseId}"/>
                    <ul>
                        <li>
                            <input type="text" name="code" placeholder="请输入访问口令" class="inp-acount"
                                   autocomplete="one-time-code" tabindex="1"/>
                        </li>
                        <li>
                            <input type="submit" class='btn-login'/>
                        </li>
                    </ul>
                </form>
                <div class="login-tips" th:if="${!authType?.startsWith('code')}">
                        <span>还没有账号？赶快
                            <a href="https://product.emoney.cn/">下载客户端</a>
                            来注册吧。
                        </span>
                </div>
            </div>
        </div>
    </div>
</div>

<input type="hidden" id="hid_pid" th:value="${session.user?.pid}"/>
<input type="hidden" id="hid_uid" th:value="${session.user?.uid}"/>
<input type="hidden" id="hid_ownerid" th:value="${video?.ownerId}"/>
<input type="hidden" id="hid_appid" th:value="${APPID}"/>

<script src="https://static-dsclient.emoney.cn/livevideo/static/scripts/modules/public/global_config_production.js?rev=20201012"></script>
<script src="https://static-dsclient.emoney.cn/livevideo/static/libs/nicescroll/jquery.nicescroll.min.js?rev=20201012"></script>
<script src="https://static-dsclient.emoney.cn/livevideo/static/scripts/modules/public/utils.opt.js?rev=20201012"></script>
<script th:src="@{/static/js/jdJsencrypt.min.js}"></script>
<script src="https://static-dsclient.emoney.cn/livevideo/static/libs/clipboard/clipboard.js?rev=20201012"></script>
<script src="https://static-dsclient.emoney.cn/livevideo/static/libs/qqFace/jquery.qqFace.js?rev=20201012"></script>
<script src="https://static-dsclient.emoney.cn/livevideo/static/scripts/Mess.js?rev=20201012"></script>
<script th:src="@{/video/libs/linktool.js?rev=************}"></script>
<script type="text/javascript">

    $(document).on('click', '.share-markbg.bg2', function () {
        $('.share-markbg.bg2').remove();
    });
    if (checkBrowserEnv()) {
        if ($('.share-markbg.bg2').length > 0) {

            $('.share-markbg.bg2').show();
        } else {
            $('body').append('<div class="share-markbg bg2"></div>');
        }

        setTimeout(function () {
            $('.share-markbg.bg2').remove();
        }, 4000)
    }
    document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=2CA91A710A36B508EEA3EEC8F611C367' type='text/javascript'%3E%3C/script%3E"));
</script>
<!-- <script src="~/Areas/ActShort20201208/Static/libs/chatroom.js?rev=202101221606"></script> -->
</body>
</html>
