<!DOCTYPE html >

<html>
<head>
    <meta name="viewport" content="width=device-width"/>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, minimal-ui">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta name="keywords" content="">
    <meta name="description" content="">
    <title>益盟操盘手</title>
    <link rel="stylesheet" href="https://static-dsclient.emoney.cn/livevideo/static/css/sharecommon.css?rev=20201012">
    <link rel="stylesheet" th:href="@{/video/css/biglive.css?rev=20201208}">
    <script src="https://static-dsclient.emoney.cn/livevideo/static/libs/jquery/jquery.handlebars.js?rev=20201012"></script>
    <script src="https://static-dsclient.emoney.cn/livevideo/static/libs/layer/layer.js?rev=20201012"></script>
    <script th:src="@{/video/libs/qrcode/qrcode.min.js}"></script>
    <script th:src="@{/video/libs/qrcode/qrcodeU.js}"></script>
    <style>
        .share-markbg.bg2 {
            background-image: url("https://static-dsclient.emoney.cn/livevideo/static/images/biglivewebv2.0/markbg2.png")
        }

        .video {
            border-radius: 0.133333rem 0.133333rem 0.133333rem 0.133333rem;
            /*height: 11.466667rem;*/
            /*height:100%;*/
        }

        .video-detail-inner {
            /*height: 100%;*/
        }
    </style>
    <script>
        function checkBrowserEnv() {
            function browserEnv() {
                var curenv = '';
                var curSys = '';
                var ua = navigator.userAgent;
                var res;
                var browser = {
                    versions: function () {
                        return {
                            trident: ua.indexOf('Trident') > -1,
                            presto: ua.indexOf('Presto') > -1,
                            webKit: ua.indexOf('AppleWebKit') > -1,
                            gecko: ua.indexOf('Gecko') > -1 && ua.indexOf('KHTML') == -1,
                            mobile: !!ua.match(/AppleWebKit.*Mobile.*/),
                            ios: !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
                            android: ua.indexOf('Android') > -1 || ua.indexOf('Linux') > -1,
                            iPad: ua.indexOf('iPad') > -1,
                            webApp: ua.indexOf('Safari') == -1
                        }
                    }(),
                    language: (navigator.browserLanguage || navigator.language).toLowerCase()
                };
                if (browser.versions.mobile) {
                    if (ua.match(/MicroMessenger/i) == "MicroMessenger") {
                        curenv = 'weixin'
                    } else if (ua.match(/QQTheme/i) == "QQTheme") {
                        curenv = 'QQ'
                    } else if (ua.match(/WeiBo/i) == "Weibo") {
                        curenv = 'weibo'
                    } else if (ua.match(/QQBrowser/i) == "QQBrowser") {
                        curenv = 'QQbrowser'
                    } else {
                        curenv = 'otherbrowser'
                    }


                    if (browser.versions.ios) {
                        curSys = 'ios'
                    } else if (browser.versions.android) {
                        curSys = 'android'
                    } else {
                        curSys = 'others'
                    }

                    res = curSys + '-' + curenv

                } else {
                    res = 'pcbrowser'
                }
                res
                return res;
            }

            function judgeBrand(sUserAgent) {
                var sUserAgent = navigator.userAgent.toLowerCase();
                var isIphone = sUserAgent.match(/iphone/i) == "iphone";
                var isHuawei = sUserAgent.match(/huawei/i) == "huawei";
                var isHonor = sUserAgent.match(/honor/i) == "honor";
                var isOppo = sUserAgent.match(/oppo/i) == "oppo";
                var isOppoR15 = sUserAgent.match(/pacm00/i) == "pacm00";
                var isVivo = sUserAgent.match(/vivo/i) == "vivo";
                var isXiaomi = sUserAgent.match(/mi\s/i) == "mi ";
                var isXiaomi2s = sUserAgent.match(/mix\s/i) == "mix ";
                var isRedmi = sUserAgent.match(/redmi/i) == "redmi";
                var isSamsung = sUserAgent.match(/sm-/i) == "sm-";

                if (isIphone) {
                    return 'iphone';
                } else if (isHuawei || isHonor) {
                    return 'huawei';
                } else if (isOppo || isOppoR15) {
                    return 'oppo';
                } else if (isVivo) {
                    return 'vivo';
                } else if (isXiaomi || isRedmi || isXiaomi2s) {
                    return 'xiaomi';
                } else if (isSamsung) {
                    return 'samsung';
                } else {
                    return 'default';
                }
            }

            function checkAgent() {
                var sUserAgent = navigator.userAgent.toLowerCase();
                var bIsIpad = sUserAgent.match(/ipad/i) == "ipad";
                var bIsIphoneOs = sUserAgent.match(/iphone os/i) == "iphone os";
                var bIsMidp = sUserAgent.match(/midp/i) == "midp";
                var bIsUc7 = sUserAgent.match(/rv:*******/i) == "rv:*******";
                var bIsUc = sUserAgent.match(/ucweb/i) == "ucweb";
                var bIsAndroid = sUserAgent.match(/android/i) == "android";
                var bIsCE = sUserAgent.match(/windows ce/i) == "windows ce";
                var bIsWM = sUserAgent.match(/windows mobile/i) == "windows mobile";

                if (!(bIsIphoneOs || bIsMidp || bIsUc7 || bIsUc || bIsAndroid || bIsCE || bIsWM || bIsIpad)) {
                    return 'pc';
                } else if (bIsIpad) {
                    return 'pad';
                } else {
                    return 'phone';
                }
            }

            var browser = browserEnv();
            var brand = judgeBrand();
            var curAgent = checkAgent();
            var result = (browser == 'android-weixin' || browser == 'android-QQ') && brand == 'huawei' && curAgent != 'pc'
            return result
        }

        var isMobile = (/(iPhone|iPad|iPod|iOS|Android)/i.test(navigator.userAgent));
        var IEVersion = (function () {
            var userAgent = navigator.userAgent;
            var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1;
            var isEdge = userAgent.indexOf("Edge") > -1 && !isIE;
            var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
            if (isIE) {
                var reIE = new RegExp("MSIE (\\d+\\.\\d+);");
                reIE.test(userAgent);
                var fIEVersion = parseFloat(RegExp["$1"]);
                if (fIEVersion == 7) {
                    return 7;
                } else if (fIEVersion == 8) {
                    return 8;
                } else if (fIEVersion == 9) {
                    return 9;
                } else if (fIEVersion == 10) {
                    return 10;
                } else {
                    return 6;
                }
            } else if (isEdge) {
                return 13;
            } else if (isIE11) {
                return 11;
            } else {
                return -1;
            }
        })();
        if (!(IEVersion == -1 || IEVersion > 10 || isMobile)) {
            document.write('<link rel="stylesheet" href="https:\/\/static-dsclient.emoney.cn\/livevideo/static/css/biglivewebv3.css?rev=20201012">');
        }

        //if (IEVersion <= 7 && IEVersion != -1) {
        //    alert("当前浏览器版本较低，建议升级版本或更换浏览器观看");
        //    document.execCommand("stop");
        //}

    </script>
    <script th:src="@{/video/libs/webzoom2.js?rev=20201225}"></script>
    <script src="https://static.emoney.cn/ds/jrtpad/soft/jrtpadtongji.js"></script>
</head>
<body>
<div th:replace="video/common :: header_fragment"></div>
<div class="container">
    <div class="main-pack" id="mainPack" data-isliving="">
        <div class="rect-video p-r">
            <div class="video">
                <div class="video-detail-inner">
                    <div class="video-alert" id="classHasNoright">
                        非常抱歉，你要找的页面不见了
<!--/*                        <a href='#' class='alink-help' onclick="history.go(-1); return false;">返回上一页</a> */-->
                    </div>
                </div>
            </div>
        </div>
        <div class="clearfloat"></div>
    </div>
    <div class="ext-finfobox">
        <div class="adver-banner">
            <div class="banner" id="div_pcAD"><a target="_blank"
                                                 href="http://pay.emoney.cn/newpay/smallpay/index?source=A12016&amp;&amp;id=3766"><img
                    height="100" width="903"
                    src="http://edf.emoney.cn/emoney/M00/00/0E/rBwBVl_FFFaARl0WAAB-RCU27hk050.jpg"></a></div>
        </div>
        <div class="ext-right-box">
            <div class="right-box-phon"></div>
        </div>
        <div class="clearfloat"></div>

    </div>
</div>
<div th:replace="video/common :: footer_fragment"></div>
<script th:src="@{/static/js/jdJsencrypt.min.js}"></script>
<script th:src="@{/video/libs/linktool.js?rev=202112141554}"></script>
</body>
</html>
