<!doctype html>
<html>
<head>
    <meta charset="gb2312">
    <title>股市新春训练营 赢战新年新行情！</title>
    <link th:href="@{${staticPath}+'static/dr588/20230808/style/common.css?r=20230808'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
</head>
<body>
<div class="main">
    <div class="left">
        <ul id="ulleft">
        </ul>
    </div>
    <div id="itemcontent">
    </div>
    <div class="hdgz">1、每周至少有一节课【听课时间大于等于30分钟+随堂小测试80分以上】，算完成一周打卡，得100积分（听课和随堂小测试仅完成一项，没有积分）<br>
        2、累计得400积分，可兑换半年智盈使用期（不满400积分，无法兑换任何使用期或其他权益）<br>
        3、累计得800积分，可兑换1年智盈使用期（大于400不满800积分，仅可兑换半年使用期）<br>
        4、活动期间，每周打卡最多得100积分；本活动兑换2023年10月15日截止并仅限智盈用户参与。</div>
    <a href="javascript:void(0)" class="btn1 dh" clickkey="2023.8-9zyfwj-week-biji" th:clickdata="${currentweek}" th:clickremark="'2023年8-9月智盈服务季-第'+${currentweek}+'周-课堂笔记'"></a>

</div>
<div class="tc1" style="display:none" id="btn_IM" clickkey="2023.8-9zyfwj-week-lingqu" th:clickdata="${currentweek}" th:clickremark="'2023年8-9月智盈服务季-第'+${currentweek}+'周-领取礼包'"></div>
<div class="tc2" style="display:none;" id="btn_close" clickkey="2023.8-9zyfwj-week-jixu" th:clickdata="${currentweek}" th:clickremark="'2023年8-9月智盈服务季-第'+${currentweek}+'周-继续学习'"></div>


<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var uid = getQueryString("uid");
    var queryString = location.search.slice(1);
    if (!!uid && uid.split(',').length > 0) {
        uid = uid.split(',')[0];
    }

    var cookiename_isshow = "emoney.dr58820230808IsShow";

    var actcode_week1 = "2023080811,2023080812,2023080813,2023080814,2023080815";
    var actcode_week2 = "2023080821,2023080822,2023080823,2023080824,2023080825";
    var actcode_week3 = "2023080831,2023080832,2023080833,2023080834,2023080835";
    var actcode_week4 = "2023080841,2023080842,2023080843,2023080844,2023080845";
    var actcode_week5 = "2023080851,2023080852,2023080853,2023080854,2023080855";
    var actcode_week6 = "2023080861,2023080862,2023080863,2023080864,2023080865";
    var actcode_week7 = "2023080871,2023080872,2023080873,2023080874,2023080875";
    var actcode_week8 = "2023080881,2023080882,2023080883,2023080884,2023080885";

    var array = new Array();
    array[1] = 0, array[2] = 0; array[3] = 0;array[4] = 0;array[5] = 0;array[6] = 0;array[7] = 0;array[8] = 0;

    $(document).ready(function () {
        try {
            PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
            PC_JH("EM_FUNC_WND_SIZE", "w=1000,h=600,mid");

            var val = getCookie(cookiename_isshow);
            if (val == null || val == "") {
                PC_JH("EM_FUNC_SHOW", "1");
            } else { PC_JH("EM_FUNC_CLOSE", ""); }
        } catch (ex) { }

        var currentweek = /*[[${currentweek}]]*/ 1;
        var dayofweek = /*[[${dayofweek}]]*/ 1;
        var currentDate = /*[[${currentdate}]]*/ 0;


        InitClickStatus(GetActCodes(currentweek), currentweek);

        let itemlist = {"weeks":[{"week":"1","name":"第一周","time":"（8.07-8.11）","desc":"特色功能 明趋势知拐点","clickkey":"第一周-展开","desc2":"会买会卖是炒股第一要领！如何识别拐点、拒绝盲目，轻松把握买卖良机？ 以上种种，均将在本周的直播课程中，结合益盟经典功能，给你答案！","items":[{"date":"2023-08-08","remark":"2023年8-9月智盈服务季-第一周-周二","desc":"《操盘线-洞悉买卖时机》","clikekey":"2023080811","title":"特色功能第1讲","week":"1","day":"1","title2":"8/08","desc2":"星期二","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第一周-随堂小测试（对应第一周的周二课程）","desc":"随堂小测试","clikekey":"2023080812","title":"特色功能第1讲","week":"1","day":"2","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/tUjiYvk.aspx"},{"date":"2023-08-10","remark":"2023年8-9月智盈服务季-第一周-周四","desc":"《趋势顶底-明察趋势回转》","clikekey":"2023080813","title":"特色功能第2讲","week":"1","day":"3","title2":"8/10","desc2":"星期四","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第一周-随堂小测试（对应第一周的周四课程）","desc":"随堂小测试","clikekey":"2023080814","title":"特色功能第2讲","week":"1","day":"4","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/rUlTVDt.aspx"}]},{"week":"2","name":"第二周","time":"（8.14-8.18）","desc":"经典指标 识形态判强弱","clickkey":"第二周-展开","desc2":"明白趋势强弱能够帮助我们震荡不慌，上涨有数，拿得稳坐得住，本周课程将教您如何利用益盟经典指标LTSH/ABJB明趋势判强弱","items":[{"date":"2023-08-15","remark":"2023年8-9月智盈服务季-第二周-周二","desc":"《按部就班-识别形态走强》","clikekey":"2023080821","title":"经典指标第1讲","week":"2","day":"1","title2":"8/15","desc2":"星期二","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第二周-随堂小测试（对应第二周的周二课程）","desc":"随堂小测试","clikekey":"2023080822","title":"经典指标第1讲","week":"2","day":"2","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/mq4TAPo.aspx"},{"date":"2023-08-17","remark":"2023年8-9月智盈服务季-第二周-周四","desc":"《龙腾四海-把握强弱变换》","clikekey":"2023080823","title":"经典指标第2讲","week":"2","day":"3","title2":"8/17","desc2":"星期四","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第二周-随堂小测试（对应第二周的周四课程）","desc":"随堂小测试","clikekey":"2023080824","title":"经典指标第2讲","week":"2","day":"4","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/PR7ihS0.aspx"}]},{"week":"3","name":"第三周","time":"（8.21-8.25）","desc":"操作秘法 看大做小","clickkey":"第三周-展开","desc2":"如何走出一买就跌，一卖就涨死循环，跟着市场趋势顺势而为？本周课程将教您益盟两个经典战法，看大做小，波段操作/短线操作有诀窍。","items":[{"date":"2023-08-22","remark":"2023年8-9月智盈服务季-第三周-周二","desc":"《周期共振-活用同频规律》","clikekey":"2023080831","title":"操作秘法第1讲","week":"3","day":"1","title2":"8/22","desc2":"星期二","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第三周-随堂小测试（对应第三周的周二课程）","desc":"随堂小测试","clikekey":"2023080832","title":"操作秘法第1讲","week":"3","day":"2","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/OtscF5c.aspx"},{"date":"2023-08-24","remark":"2023年8-9月智盈服务季-第三周-周四","desc":"《板块共振秘法-顺势买入》","clikekey":"2023080833","title":"操作秘法第2讲","week":"3","day":"3","title2":"8/24","desc2":"星期四","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第三周-随堂小测试（对应第三周的周四课程）","desc":"随堂小测试","clikekey":"2023080834","title":"操作秘法第2讲","week":"3","day":"4","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/mp7PGsP.aspx"}]},{"week":"4","name":"第四周","time":"（8.28-9.01）","desc":"买卖技巧 明买卖严纪律","clickkey":"第四周-展开","desc2":"选到好股票，没有好卖点，一样可能会被套，本周课程将教您益盟两个关于交易的经典战法，会买会卖，心中自在。","items":[{"date":"2023-08-29","remark":"2023年8-9月智盈服务季-第四周-周二","desc":"《止盈止损-严格交易法则》","clikekey":"2023080841","title":"买卖技巧第1讲","week":"4","day":"1","title2":"8/29","desc2":"星期二","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第四周-随堂小测试（对应第四周的周二课程）","desc":"随堂小测试","clikekey":"2023080842","title":"买卖技巧第1讲","week":"4","day":"2","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/rXTnSVD.aspx"},{"date":"2023-08-31","remark":"2023年8-9月智盈服务季-第四周-周四","desc":"《资金复位解套秘法-快速解套》","clikekey":"2023080843","title":"买卖技巧第2讲","week":"4","day":"3","title2":"8/31","desc2":"星期四","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第四周-随堂小测试（对应第四周的周四课程）","desc":"随堂小测试","clikekey":"2023080844","title":"买卖技巧第2讲","week":"4","day":"4","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/mwJCP7H.aspx"}]},{"week":"5","name":"第五周","time":"（9.04-9.08）","desc":"选股妙招 技术优选","clickkey":"第五周-展开","desc2":"会操作，不会选股，一样可能关灯吃面！本周课程将教您如何巧妙利用量价时空，选择技术形态即将突破向上的股票。","items":[{"date":"2023-09-05","remark":"2023年8-9月智盈服务季-第五周-周二","desc":"《均线共振-挖掘共性机会》","clikekey":"2023080851","title":"选股妙招第1讲","week":"5","day":"1","title2":"9/05","desc2":"星期二","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第五周-随堂小测试（对应第五周的周二课程）","desc":"随堂小测试","clikekey":"2023080852","title":"选股妙招第1讲","week":"5","day":"2","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/OQGcsgV.aspx"},{"date":"2023-09-07","remark":"2023年8-9月智盈服务季-第五周-周四","desc":"《技术选股-优选强势股》","clikekey":"2023080853","title":"选股妙招第2讲","week":"5","day":"3","title2":"9/07","desc2":"星期四","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第五周-随堂小测试（对应第五周的周四课程）","desc":"随堂小测试","clikekey":"2023080854","title":"选股妙招第2讲","week":"5","day":"4","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/wghNQLd.aspx"}]},{"week":"6","name":"第六周","time":"（9.11-9.15）","desc":"选股妙招 挑选绩优","clickkey":"第六周-展开","desc2":"形态即将突破，业绩暴雷一样可能黑天鹅，本周课程将教您如何快速看懂个股基本面，技术为矛，价值为盾，双重过滤，选股更安心。","items":[{"date":"2023-09-12","remark":"2023年8-9月智盈服务季-第六周-周二","desc":"《基本面选股-挑选绩优股》","clikekey":"2023080861","title":"选股妙招第3讲","week":"6","day":"1","title2":"9/12","desc2":"星期二","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第六周-随堂小测试（对应第六周的周二课程）","desc":"随堂小测试","clikekey":"2023080862","title":"选股妙招第3讲","week":"6","day":"2","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/tUIPc5j.aspx"},{"date":"2023-09-14","remark":"2023年8-9月智盈服务季-第六周-周四","desc":"《黄金一买-伏击拐点抄底》","clikekey":"2023080863","title":"选股妙招第4讲","week":"6","day":"3","title2":"9/14","desc2":"星期四","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第六周-随堂小测试（对应第六周的周四课程）","desc":"随堂小测试","clikekey":"2023080864","title":"选股妙招第4讲","week":"6","day":"4","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/Q3ds7Xz.aspx"}]},{"week":"7","name":"第七周","time":"（9.18-9.22）","desc":"战法实操 黄金买卖","clickkey":"第七周-展开","desc2":"只做右侧上升趋势是基本原则，中继二买安全性高，突破三买弹性大，本周课程将带入实战，教您综合判断趋势，识别中继二买、突破三买拐点。","items":[{"date":"2023-09-19","remark":"2023年8-9月智盈服务季-第七周-周二","desc":"《黄金二买-发掘顺势中继》","clikekey":"2023080871","title":"战法实操第1讲","week":"7","day":"1","title2":"9/19","desc2":"星期二","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第七周-随堂小测试（对应第七周的周二课程）","desc":"随堂小测试","clikekey":"2023080872","title":"战法实操第1讲","week":"7","day":"2","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/O38scsg.aspx"},{"date":"2023-09-21","remark":"2023年8-9月智盈服务季-第七周-周四","desc":"《黄金三买-捕捉强势突破》","clikekey":"2023080873","title":"战法实操第2讲","week":"7","day":"3","title2":"9/21","desc2":"星期四","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第七周-随堂小测试（对应第七周的周四课程）","desc":"随堂小测试","clikekey":"2023080874","title":"战法实操第2讲","week":"7","day":"4","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/OrsgZJc.aspx"}]},{"week":"8","name":"第八周","time":"（9.25-9.28）","desc":"热点追踪 综合实战","clickkey":"第八周-展开","desc2":"都说站在风口，猪都能起飞，顺风的板块上涨概率更高，本周课程将教您如何选择风口，把握板块轮动机会","items":[{"date":"2023-09-26","remark":"2023年8-9月智盈服务季-第八周-周二","desc":"《风口综合实战》","clikekey":"2023080881","title":"综合实战第1讲","week":"8","day":"1","title2":"9/26","desc2":"星期二","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第八周-随堂小测试（对应第八周的周二课程）","desc":"随堂小测试","clikekey":"2023080882","title":"综合实战第1讲","week":"8","day":"2","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/huFjI49.aspx"},{"date":"2023-09-28","remark":"2023年8-9月智盈服务季-第八周-周四","desc":"《主力综合实战》","clikekey":"2023080883","title":"综合实战第2讲","week":"8","day":"3","title2":"9/28","desc2":"星期四","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"","remark":"2023年8-9月智盈服务季-第八周-随堂小测试（对应第八周的周四课程）","desc":"随堂小测试","clikekey":"2023080884","title":"综合实战第2讲","week":"8","day":"4","title2":"","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://act.emoney.cn/api/survey/gotopc?scene=BeforeClass&wjx=http://survey.emoney.cn/vm/YcyrB5J.aspx"}]}]};

        $.each(itemlist.weeks, function () {

            //左侧动态生成
            let inner_lli = document.createElement('li');
            inner_lli.setAttribute("data-name", this.name);
            inner_lli.setAttribute("data-num", this.week);
            inner_lli.setAttribute("id", "l" + this.week);
            if (currentweek == this.week)
                inner_lli.setAttribute("class", "on");

            let inner_ldiv1 = document.createElement('div');
            inner_ldiv1.setAttribute("class", "bt");


            let inner_ldiv2 = document.createElement('div');
            inner_ldiv2.setAttribute("class", "l1");
            inner_ldiv2.innerText = this.name;

            let inner_ldiv2span = document.createElement('span');
            inner_ldiv2span.setAttribute("class", "f12");
            inner_ldiv2span.innerText = this.time;

            inner_ldiv2.appendChild(inner_ldiv2span);


            let inner_ldiv3 = document.createElement('div');
            inner_ldiv3.setAttribute("class", "l2");
            inner_ldiv3.innerText = this.desc;

            let inner_ldiv4 = document.createElement('div');
            inner_ldiv4.setAttribute("class", "nr");
            inner_ldiv4.innerText = this.desc2;


            inner_ldiv1.appendChild(inner_ldiv2);
            inner_ldiv1.appendChild(inner_ldiv3);

            inner_lli.appendChild(inner_ldiv1);

            inner_lli.appendChild(inner_ldiv4);

            let inner_l_A_Element = document.createElement('a');
            inner_l_A_Element.setAttribute("class", "btn3");
            inner_l_A_Element.setAttribute("data-num", this.week);
            inner_l_A_Element.setAttribute("clickkey", "2023.8-9zyfwj-week-open");
            inner_l_A_Element.setAttribute("clickdata", this.week);
            inner_l_A_Element.setAttribute("clickremark", "2023年8-9月智盈服务季-" + this.clickkey);
            inner_l_A_Element.setAttribute("href", "javascript:void(0)");

            if (currentweek < this.week)
                inner_l_A_Element.setAttribute("style", "display:none");

            inner_l_A_Element.innerText = "点击查看";
            inner_lli.appendChild(inner_l_A_Element);


            let inner_ldiv5 = document.createElement('div');
            inner_ldiv5.setAttribute("class", "btn4");

            if (currentweek >= this.week)
                inner_ldiv5.setAttribute("style", "display:none");

            inner_ldiv5.innerText = "即将开启";
            inner_lli.appendChild(inner_ldiv5);


            document.getElementById("ulleft").appendChild(inner_lli);

            //右侧动态生成
            let inner_rdiv = document.createElement('div');
            inner_rdiv.setAttribute("class", "right");
            inner_rdiv.setAttribute("id", "r" + this.week);

            if (currentweek != this.week)
                inner_rdiv.setAttribute("style", "display:none");

            inner_ul = document.createElement('ul');

            inner_rdiv_div = document.createElement("a");
            inner_rdiv_div.setAttribute("class", "btn5b");
            inner_rdiv_div.setAttribute("data-week", this.week);
            inner_rdiv_div.setAttribute("name", "btn-unlock");

            inner_rdiv_div.setAttribute("clickdata", this.week);
            inner_rdiv_div.setAttribute("clickremark", "2023年8-9月智盈服务季-"+this.clickkey.substr(0,3)+"-礼包");
            inner_rdiv_div.setAttribute("data-classdate", "");
            inner_rdiv_div.setAttribute("data-actcode", "20230808" + this.week +"5");
            inner_rdiv_div.setAttribute("data-dayofweek", "");
            inner_rdiv_div.setAttribute("clickkey", "2023.8-9zyfwj-week-libao"+this.week);
            inner_rdiv_div.setAttribute("href", "javascript:void(0);");
            inner_rdiv_div.innerText="等待解锁";

            $.each(this.items, function () {
                let inner_li = document.createElement('li');

                var inner_div1 = document.createElement('div');
                inner_div1.setAttribute("class", "txt1");

                var inner_div1_strong = document.createElement("strong");
                var inner_div1_strong_span = document.createElement("span");
                inner_div1_strong_span.setAttribute("class","f24");
                inner_div1_strong_span.innerText = this.title2;

                var inner_div1_strong_span1 = document.createElement("span");
                inner_div1_strong_span1.innerText = " 19:30";
                inner_div1_strong.appendChild(inner_div1_strong_span);
                inner_div1_strong.appendChild(inner_div1_strong_span1);
                inner_div1.appendChild(inner_div1_strong);

                var inner_div1_span = document.createElement("span");
                inner_div1_span.setAttribute("class","f12");
                inner_div1_span.innerText = "●";
                inner_div1.appendChild(inner_div1_span);

                var inner_div1_span1 = document.createElement("span");
                inner_div1_span1.innerText = this.desc2;
                inner_div1.appendChild(inner_div1_span1);

                var inner_div2 = document.createElement('div');
                inner_div2.setAttribute("class", "txt2");
                inner_div2.innerText = this.title;
                var inner_div3 = document.createElement('div');
                inner_div3.setAttribute("class", "txt3");
                inner_div3.innerText = this.desc;

                var inner_div4 = document.createElement('div');
                inner_div4.setAttribute("class", "txt4");
                inner_div4.innerText = this.desc;

                inner_li.appendChild(inner_div1);
                inner_li.appendChild(inner_div2);
                inner_li.appendChild(inner_div3);
                inner_li.appendChild(inner_div4);
                inner_ul.appendChild(inner_li);

                if(this.desc=="随堂小测试"){
                    inner_div1.setAttribute("style","display:none;");
                    inner_div3.setAttribute("style","display:none;");
                    inner_div2.setAttribute("style","padding-top:36px;")
                }else{
                    inner_div4.setAttribute("style","display:none;");
                }

                let inner_A_Element = document.createElement('a');

                /*data-week="@week4"
                        data-dayofweek="4"
                        name="btn_openclass"
                        data-classdate="2022-09-30"
                        data-actcode="2022093045"
                        clickkey="zyfwycgp-week4-class"
                        clickdata="5"
                        clickremark="第四周-周五"*/
                inner_A_Element.setAttribute("class", "btn2 dh");
                inner_A_Element.setAttribute("data-week", this.week);
                inner_A_Element.setAttribute("name", "btn-unlock");

                inner_A_Element.setAttribute("clickdata", this.day);
                inner_A_Element.setAttribute("clickremark", this.remark);
                inner_A_Element.setAttribute("data-classdate", this.date);
                inner_A_Element.setAttribute("data-actcode", this.clikekey);
                inner_A_Element.setAttribute("data-dayofweek", this.day);


                if (this.title2 != "领取礼包") {
                    inner_A_Element.setAttribute("name", "btn_openclass");
                    inner_A_Element.setAttribute("clickkey", "2023.8-9zyfwj-week" + this.week + "-class");
                } else {
                    inner_A_Element.setAttribute("class", "btn5b");
                    inner_A_Element.setAttribute("name", "btn-unlock");
                    inner_A_Element.setAttribute("clickkey", "2023.8-9zyfwj-week-libao");
                }

                if (this.blank == "blank")
                    inner_A_Element.setAttribute("target", "_blank");

                inner_A_Element.setAttribute("href", this.url);

                inner_A_Element.innerText = this.btntxt;
                inner_li.appendChild(inner_A_Element);

                inner_rdiv.appendChild(inner_ul);
                inner_rdiv.appendChild(inner_rdiv_div);
                document.getElementById("itemcontent").appendChild(inner_rdiv);
            });

        });

        $("[name=btn_openclass]").each(function (index, element) {
            var week = parseInt($(this).attr("data-week"));
            var classdate = parseInt($(this).attr("data-classdate").replace(/-/g, ""));
            var thisdayofweek = parseInt($(this).attr("data-dayofweek"));

            if (classdate > parseInt(currentDate)) {
                $(this).removeClass("btn2").removeClass("dh").addClass("btn2b");
                $(this).attr("style","cursor: default;");
                $(this).attr("disabled","disabled");
                $(this).html("即将开启");
            }
            else {
                if ($(this).html() == "点击测试") {
                    var targetUrl = $(this).attr("href");
                    $(this).attr("href", targetUrl + "&" + queryString);
                    //随堂小测试单独判断
                    var preBtn = $(this).parent().prev().find("a").html();
                    if (preBtn == "点击听课" || preBtn == "即将开启") {
                        $(this).removeClass("btn2").removeClass("dh").addClass("btn2b");
                        $(this).attr("style", "cursor: default;");
                        $(this).attr("disabled", "disabled");
                        $(this).html("即将开启");
                    }
                }
            }
        });

        $("[name=btn_openclass]").click(function () {
            var _this = $(this);
            if (_this.html() == "即将开启") {
                return false;
            }
            if (!uid) {
                layer.msg("您尚未登陆，请登陆后再操作");
                return false;
            }
            var actcode = _this.attr("data-actcode");
            var classdate = _this.attr("data-classdate");
            var week = parseInt(_this.attr("data-week"));
            var dayofweek = parseInt(_this.attr("data-dayofweek"));
            var isBlank = _this.attr("target") == "_blank";

            //打开课程
            if (!!classdate && !isBlank) {
                PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "10,http://live.emoney.cn/livevideo/page/homepage?someday=" + classdate);

                setTimeout(function () {
                    //开启对应课程的随堂测
                    var testBtn = _this.parent().next().find("a");
                    if (testBtn != "再做一遍") {
                        testBtn.removeClass("btn2b").addClass("btn2").addClass("dh");
                        testBtn.removeAttr("disabled");
                        testBtn.removeAttr("style");
                        testBtn.html("点击测试");
                    }
                }, 3000);
            }

            var btntext = "再看一遍";
            var lastChar = actcode.charAt(actcode.length - 1);
            if ( lastChar == 2 || lastChar == 4) {
                btntext = "再做一遍";
            }

            //观看记录
            $.ajax({
                type: 'get',
                url: "/activity/user/addcountbyactcode?actcode=" + actcode,
                dataType: 'jsonp',
                data: {
                    uid: uid,
                    value: "1"
                },
                success: function (data) {
                    if (_this.html() != "再看一遍" && _this.html() != "再做一遍") {
                        array[week]++;
                        if (array[week]>=4) {
                            $("#r" + week).find("[name=btn-unlock]").removeClass("btn5b").addClass("btn5").addClass("unlock").html("立即领取");
                        }
                    }
                    _this.html(btntext);
                }
            });

            pageClick(_this);

        });

        $(".left li").click(function () {
            var num = $(this).attr("data-num");
            $(this).siblings().removeClass("on");
            $(this).addClass("on");

            $(".right").hide();
            $("#r" + num).show();

            InitLeftLiClick(num,$(this).find(".btn3"));
        });

        $("[name=btn-unlock]").click(function () {
            if ($(this).hasClass("unlock")) {
                //领取大礼包
                layer.open({
                    type: 1,
                    title: false,
                    skin: 'layui-layer-nobg', //没有背景色
                    area: ['auto'],
                    shade: 0.6,
                    shadeClose: false,
                    content: $(".tc1")
                });
            } else {
                layer.open({
                    type: 1,
                    title: false,
                    skin: 'layui-layer-nobg', //没有背景色
                    area: ['auto'],
                    shade: 0.6,
                    shadeClose: false,
                    content: $(".tc2")
                });
            }
            pageClick($(this));
        });

        $("#btn_close").click(function () {
            layer.closeAll();
        });

        $("#btn_IM").click(function () {
            var name = "PC-2023年8-9月智盈服务季-"+$(".left ul li.on").attr("data-name") + "-领取礼包";
            var b = new Base64();
            PC_JH('EM_FUNC_START_IM', '0,20230808dr588,' + b.encode(name));
            layer.closeAll();
            pageClick($(this));
        });

        $(".btn1").click(function () {
            var name = "PC-2023年8-9月智盈服务季-" +$(".left ul li.on").attr("data-name") + "-课堂笔记";
            var b = new Base64();
            PC_JH('EM_FUNC_START_IM', '0,20230808dr588,' + b.encode(name));
            pageClick($(this));
        });

        // 点击不再提示
        $("#notips").click(function () {
            if ($(this).prop('checked')) {
                setCookie(cookiename_isshow, getQueryString("uid"), 60);
            } else {
                setCookie(cookiename_isshow, "", -1);//清除cookie
            }
        });
    });

    function InitLeftLiClick(num,$obj) {
        var remarkPre = "2023年8-9月智盈服务季-第" + num + "周";

        $("#btn_close,.btn1,#btn_IM").attr("clickdata", num);
        $("#btn_close").attr("clickremark", remarkPre + "-继续学习");
        $("#btn_IM").attr("clickremark", remarkPre + "-领取礼包");
        $(".btn1").attr("clickremark", remarkPre + "-课堂笔记");

        //获取大礼包按钮状态
        InitClickStatus(GetActCodes(num), num);

        pageClick($obj);
    }
    function InitClickStatus(actcodes,week) {
        $.ajax({
            type: 'get',
            url: "/activity/user/issubmitbyactcodes?actcodes=" + actcodes,
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if (data.code == '200') {
                    var count = 0;
                    var ret = data.data;
                    var val = ret.split(",");
                    var codes = actcodes.split(',');
                    for (var i = 0; i < val.length; i++) {
                        if (!!val[i]) {
                            count++;

                            //按钮状态变为“再看一遍”
                            var btntext = "再看一遍";

                            var lastChar = codes[i].charAt(codes[i].length - 1);
                            if ( lastChar == 2 || lastChar == 4) {
                                btntext = "再做一遍";
                            }

                            var thisBtn = $("#r" + week).find("[data-actcode=" + codes[i] + "]");
                            thisBtn.html(btntext);
                            thisBtn.removeClass("btn2b").addClass("btn2").addClass("dh");
                            thisBtn.removeAttr("disabled");
                            thisBtn.removeAttr("style");

                            if(btntext == "再看一遍") {
                                var testBtn = thisBtn.parent().next().find("a");
                                testBtn.html("点击测试");
                                testBtn.removeClass("btn2b").addClass("btn2").addClass("dh");
                                testBtn.removeAttr("disabled");
                                testBtn.removeAttr("style");
                            }

                        }
                    }
                    array[week] = count;
                    if (count >= 4) {
                        $("#r" + week).find("[name=btn-unlock]").removeClass("btn5b").addClass("btn5").addClass("unlock").html("立即领取");
                    } else {
                        $("#r" + week).find("[name=btn-unlock]").attr("clickdata","");
                    }
                }
            }
        });
    }

    function GetActCodes(week) {
        var codes = "";
        switch (week.toString()) {
            case "1":
                codes = actcode_week1;
                break;
            case "2":
                codes = actcode_week2;
                break;
            case "3":
                codes = actcode_week3;
                break;
            case "4":
                codes = actcode_week4;
                break;
            case "5":
                codes = actcode_week5;
                break;
            case "6":
                codes = actcode_week6;
                break;
            case "7":
                codes = actcode_week7;
                break;
            case "8":
                codes = actcode_week8;
                break;
            default:
                break;
        }
        return codes;
    }

    function pageClick(obj){
        var App = "10013";   //APPID 没有请申请
        var Module = "dr588_20230808";//模块名称
        var Remark = "";     //备注可为空
        var ClickFlag = true;//默认为true
        var Host = "https://api2-tongji.emoney.cn";
        var ClickUrl = Host + "/Page/PageClick";
        var PageViewUrl = Host + "/Page/PageView";
        var pageUrl = window.top.location.href;

        var _clickkey = obj.attr("clickkey");
        var _clickdata = obj.attr("clickdata");
        var _clickremark = obj.attr("clickremark");
        var _htmltype = obj.attr("type");
        if (App != "" && _clickdata != "") {
            var src = ClickUrl + "?v=" + Math.random()
                + "&app=" + App
                + "&module=" + Module
                + "&clickkey=" + _clickkey
                + "&clickdata=" + _clickdata
                + "&clickremark=" + _clickremark
                + "&htmltype=" + _htmltype
                + "&pageurl=" + encodeURIComponent(pageUrl)
                + "&remark=" + Remark;
            var elm = document.createElement("img");
            elm.src = src;
            elm.style.display = "none";
            document.body.appendChild(elm);
        }
    }

    function GetExternal() {
        return window.external.EmObj;
    }

    function PC_JH(type, c) {
        try {
            var obj =
                GetExternal();
            return obj.EmFunc(type, c);
        } catch (e) { }
    }
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function setCookie(c_name, value, expiredays) {
        var exdate = new Date();
        exdate.setDate(exdate.getDate() + expiredays);
        document.cookie = c_name + "=" + escape(value) + ";expires=" + exdate.toGMTString() + ";path=/";
    }

    function getCookie(c_name) {

        if (document.cookie.length > 0) {
            c_start = document.cookie.indexOf(c_name + "=")

            if (c_start != -1) {
                c_start = c_start + c_name.length + 1
                c_end = document.cookie.indexOf(";", c_start)
                if (c_end == -1) c_end = document.cookie.length
                return unescape(document.cookie.substring(c_start, c_end))
            }
        }
        return ""
    }
</script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "dr588_20230808";//模块名称
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    //$.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>