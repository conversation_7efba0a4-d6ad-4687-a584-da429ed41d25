<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="gb2312">
    <title>智盈专属迎战红五月</title>
    <link th:href="@{${staticPath}+'static/dr588/20230504/style/common.css?r=20230418'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
</head>
<body>
<div class="main">
    <div class="left">
        <ul id="ulleft">

        </ul>
    </div>

    <div id="itemcontent"></div>

    <a href="javascript:void(0)" class="btn1 dh" clickkey="zyfwycgp-week-biji" th:clickdata="${currentweek}" th:clickremark="'智盈专属迎战红五月-第'+${currentweek}+'周-课堂笔记'"></a>
</div>
<div class="tc1" style="display:none" id="btn_IM" clickkey="zyfwycgp-week-lingqu" th:clickdata="${currentweek}" th:clickremark="'智盈专属迎战红五月-第'+${currentweek}+'周-领取礼包'"></div>
<div class="tc2" style="display:none;" id="btn_close" clickkey="zyfwycgp-week-jixu" th:clickdata="${currentweek}" th:clickremark="'智盈专属迎战红五月-第'+${currentweek}+'周-继续学习'"></div>


<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:inline="javascript">
    var uid = getQueryString("uid");
    if (!!uid && uid.split(',').length > 0) {
        uid = uid.split(',')[0];
    }

    var cookiename_isshow = "emoney.dr58820230504IsShow";

    var actcode_week1 = "2023050411,2023050412,2023050413,2023050414,2023050415";
    var actcode_week2 = "2023050421,2023050422,2023050423,2023050424,2023050425";
    var actcode_week3 = "2023050431,2023050432,2023050433,2023050434,2023050435";

    var array = new Array();
    array[1] = 0, array[2] = 0; array[3] = 0;

    $(document).ready(function () {
        try {
            PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
            PC_JH("EM_FUNC_WND_SIZE", "w=970,h=625,mid");

            var val = getCookie(cookiename_isshow);
            if (val == null || val == "") {
                PC_JH("EM_FUNC_SHOW", "1");
            } else { PC_JH("EM_FUNC_CLOSE", ""); }
        } catch (ex) { }

        var currentweek = /*[[${currentweek}]]*/ 1;
        var dayofweek = /*[[${dayofweek}]]*/ 1;
        var currentDate = /*[[${currentdate}]]*/ 0;


        InitClickStatus(GetActCodes(currentweek), currentweek);

        let itemlist = {"weeks":[{"week":"1","name":"第一周","time":"(5.8--5.12)","desc":"益盟买卖技巧","clickkey":"第一周-展开","desc2":"散户股民炒股，容易受情绪影响、凭感觉行事，没有制定计划去执行，常常盲目买卖，结果往往跑输市场，难以真正盈利。 本周的课程讲教你如何做到审时度势，顺势买卖。","items":[{"date":"2023-05-08","remark":"智盈专属迎战红五月-第一周-周一","desc":"《稳健获利的诀窍——把握四大关键环节》","clikekey":"2023050411","title":"看文章","week":"1","day":"1","title2":"5.8周一","desc2":"","btntxt":"点击查看","blank":"blank","url":"http://www.emoney.cn/dianjin/bb/sidaguanjianhuanjie.pdf"},{"date":"2023-05-09","remark":"智盈专属迎战红五月-第一周-周二","desc":"《把握板块共振秘法-顺势买入》","clikekey":"2023050412","title":"买卖技巧第1讲","week":"1","day":"2","title2":"5.9周二","desc2":"","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"2023-05-10","remark":"智盈专属迎战红五月-第一周-周三","desc":"《APP策略选股-量化策略池优选股票》","clikekey":"2023050413","title":"看视频","week":"1","day":"3","title2":"5.10周三","desc2":"","btntxt":"点击前往","blank":"blank","url":"https://emoney.gensee.com/webcast/site/vod/play-ec07725a6ae54637a735bdf2564eee32"},{"date":"2023-05-11","remark":"智盈专属迎战红五月-第一周-周四","desc":"《资金复位解套秘法-择时卖出》","clikekey":"2023050414","title":"买卖技巧第2讲","week":"1","day":"4","title2":"5.11周四","desc2":"","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"2023-05-12","remark":"智盈专属迎战红五月-第一周-周五","desc":"做课后练习题，巩固知识检验学习质量~","clikekey":"2023050415","title":"做测试","week":"1","day":"5","title2":"5.12周五","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://survey.emoney.cn/vm/rAGvIW9.aspx"},{"date":"2023-05-13","remark":"智盈专属迎战红五月-第一周-礼包","desc":"《投资机会火线速报》","clikekey":"2023050416","title":"本周礼包","week":"1","day":"6","title2":"领取礼包","desc2":"学习满三天，领取大礼包","btntxt":"等待解锁","blank":"","url":"javascript:void(0)"}]},{"week":"2","name":"第二周","time":"(5.15--5.19)","desc":"益盟策略叠战","clickkey":"第二周-展开","desc2":"行情反复，板块轮动频繁，如何拨开迷雾寻找强势？ 如何运用软件策略进行叠加，优选好标的？本周策略学习第二部分，通过对益盟经典策略叠战的运用，给你答案！","items":[{"date":"2023-05-15","remark":"智盈专属迎战红五月-第二周-周一","desc":"《教你益招之热点聚焦》","clikekey":"2023050421","title":"看视频","week":"2","day":"1","title2":"5.15周一","desc2":"","btntxt":"点击前往","blank":"blank","url":"https://emoney.gensee.com:443/webcast/site/vod/play-aded53b524784d12941e630f7c11491d"},{"date":"2023-05-16","remark":"智盈专属迎战红五月-第二周-周二","desc":"《技术选股-中途上车好机会》","clikekey":"2023050422","title":"技术选股第1讲","week":"2","day":"2","title2":"5.16周二","desc2":"","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"2023-05-17","remark":"智盈专属迎战红五月-第二周-周三","desc":"《强势突破选股方法梳理》","clikekey":"2023050423","title":"看文章","week":"2","day":"3","title2":"5.17周三","desc2":"","btntxt":"点击查看","blank":"blank","url":"http://www.emoney.cn/dianjin/bb/qstpxgffsl.pdf"},{"date":"2023-05-18","remark":"智盈专属迎战红五月-第二周-周四","desc":"《技术选股-优选强势股》","clikekey":"2023050424","title":"技术选股第2讲","week":"2","day":"4","title2":"5.18周四","desc2":"","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"2023-05-19","remark":"智盈专属迎战红五月-第二周-周五","desc":"做课后练习题，巩固知识检验学习质量~","clikekey":"2023050425","title":"做测试","week":"2","day":"5","title2":"5.19周五","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://survey.emoney.cn/vm/hx0eX5Y.aspx"},{"date":"2023-05-20","remark":"智盈专属迎战红五月-第二周-礼包","desc":"《投资机会火线速报》","clikekey":"2023050426","title":"本周礼包","week":"2","day":"6","title2":"领取礼包","desc2":"学习满三天，领取大礼包","btntxt":"等待解锁","blank":"","url":"javascript:void(0)"}]},{"week":"3","name":"第三周","time":"(5.22--5.26)","desc":"益盟综合实战","clickkey":"第三周-展开","desc2":"学了买卖和选股的方法、判断了市场节奏，无法有效运用到实战中帮助交易，提高投资能力，也是白搭！因此，本周的课程从实战出发，带你组合运用益盟特色功能指标与经典战法，精选良机，提高胜率！","items":[{"date":"2023-05-22","remark":"智盈专属迎战红五月-第三周-周一","desc":"《何为底部量变》","clikekey":"2023050431","title":"看文章","week":"3","day":"1","title2":"5.22周一","desc2":"","btntxt":"点击查看","blank":"blank","url":"http://www.emoney.cn/dianjin/bb/dibuliangbian.pdf"},{"date":"2023-05-23","remark":"智盈专属迎战红五月-第三周-周二","desc":"《风口综合实战-学以致用》","clikekey":"2023050432","title":"选股策略第1讲","week":"3","day":"2","title2":"5.23周二","desc2":"","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"2023-05-24","remark":"智盈专属迎战红五月-第三周-周三","desc":"《教你益招之板块监测》","clikekey":"2023050433","title":"看视频","week":"3","day":"3","title2":"5.24周三","desc2":"","btntxt":"点击前往","blank":"blank","url":"https://emoney.gensee.com/webcast/site/vod/play-3f44654df6c245f697c1a5a31b712da5"},{"date":"2023-05-25","remark":"智盈专属迎战红五月-第三周-周四","desc":"《主力综合实战-用以学精》","clikekey":"2023050434","title":"选股策略第2讲","week":"3","day":"4","title2":"5.25周四","desc2":"","btntxt":"点击听课","blank":"","url":"javascript:void(0)"},{"date":"2023-05-26","remark":"智盈专属迎战红五月-第三周-周五","desc":"做课后练习题，巩固知识检验学习质量~","clikekey":"2023050435","title":"做测试","week":"3","day":"5","title2":"5.26周五","desc2":"","btntxt":"点击测试","blank":"blank","url":"http://survey.emoney.cn/vm/wY4v2vh.aspx"},{"date":"2023-05-27","remark":"智盈专属迎战红五月-第三周-礼包","desc":"《投资机会火线速报》","clikekey":"2023050436","title":"本周礼包","week":"3","day":"6","title2":"领取礼包","desc2":"学习满三天，领取大礼包","btntxt":"等待解锁","blank":"","url":"javascript:void(0)"}]}]};

        $.each(itemlist.weeks, function () {

            //左侧动态生成
            let inner_lli = document.createElement('li');
            inner_lli.setAttribute("data-name", this.name);
            inner_lli.setAttribute("data-num", this.week);
            inner_lli.setAttribute("id", "l" + this.week);
            if (currentweek == this.week)
                inner_lli.setAttribute("class", "on");

            let inner_ldiv1 = document.createElement('div');
            inner_ldiv1.setAttribute("class", "bt");


            let inner_ldiv2 = document.createElement('div');
            inner_ldiv2.setAttribute("class", "f20");
            inner_ldiv2.innerText = this.name;

            let inner_ldiv2span = document.createElement('span');
            inner_ldiv2span.setAttribute("class", "f12");
            inner_ldiv2span.innerText = this.time;

            inner_ldiv2.appendChild(inner_ldiv2span);



            let inner_ldiv3 = document.createElement('div');
            inner_ldiv3.setAttribute("class", "f16");
            inner_ldiv3.innerText = this.desc;

            let inner_ldiv4 = document.createElement('div');
            inner_ldiv4.setAttribute("class", "nr");
            inner_ldiv4.innerText = this.desc2;



            inner_ldiv1.appendChild(inner_ldiv2);
            inner_ldiv1.appendChild(inner_ldiv3);

            inner_lli.appendChild(inner_ldiv1);

            inner_lli.appendChild(inner_ldiv4);

            let inner_l_A_Element = document.createElement('a');
            inner_l_A_Element.setAttribute("class", "btn3");
            inner_l_A_Element.setAttribute("data-num", this.week);
            inner_l_A_Element.setAttribute("clickkey", "zyzsyzhwy-week-open");
            inner_l_A_Element.setAttribute("clickdata", this.week);
            inner_l_A_Element.setAttribute("clickremark", this.clickkey);

            if (currentweek < this.week)
                inner_l_A_Element.setAttribute("style", "display:none");

            inner_l_A_Element.innerText = "点击查看";
            inner_lli.appendChild(inner_l_A_Element);


            let inner_ldiv5 = document.createElement('div');
            inner_ldiv5.setAttribute("class", "btn4");

            if (currentweek >= this.week)
                inner_ldiv5.setAttribute("style", "display:none");

            inner_ldiv5.innerText = "即将开启";
            inner_lli.appendChild(inner_ldiv5);


            document.getElementById("ulleft").appendChild(inner_lli);

            //右侧动态生成
            let inner_rdiv = document.createElement('div');
            inner_rdiv.setAttribute("class", "right");
            inner_rdiv.setAttribute("id", "r" + this.week);

            if (currentweek != this.week)
                inner_rdiv.setAttribute("style", "display:none");

            inner_ul = document.createElement('ul');

            $.each(this.items, function () {
                let inner_li = document.createElement('li');

                var inner_div1 = document.createElement('div');
                inner_div1.setAttribute("class", "txt1");

                inner_div1.innerText = this.title2;

                var inner_div2 = document.createElement('div');
                inner_div2.setAttribute("class", "txt2");
                inner_div2.innerText = this.title;
                var inner_div3 = document.createElement('div');
                inner_div3.setAttribute("class", "txt3");
                inner_div3.innerText = this.desc;

                var inner_div4 = document.createElement('div');
                inner_div4.setAttribute("class", "txt4");
                inner_div4.setAttribute("style", "padding-top: 0px;");
                inner_div4.innerText = this.desc2;

                inner_div3.appendChild(inner_div4);

                inner_li.appendChild(inner_div1);
                inner_li.appendChild(inner_div2);
                inner_li.appendChild(inner_div3);
                inner_ul.appendChild(inner_li);

                let inner_A_Element = document.createElement('a');

            /*data-week="@week4"
                data-dayofweek="4"
                name="btn_openclass"
                data-classdate="2022-09-30"
                data-actcode="2022093045"
                clickkey="zyfwycgp-week4-class"
                clickdata="5"
                clickremark="智盈专属迎战红五月-第四周-周五"*/
                inner_A_Element.setAttribute("class", "btn2");
                inner_A_Element.setAttribute("data-week", this.week);
                inner_A_Element.setAttribute("name", "btn-unlock");

                inner_A_Element.setAttribute("clickdata", this.day);
                inner_A_Element.setAttribute("clickremark", this.remark);
                inner_A_Element.setAttribute("data-classdate", this.date);
                inner_A_Element.setAttribute("data-actcode", this.clikekey);
                inner_A_Element.setAttribute("data-dayofweek", this.day);


                if (this.title2 != "领取礼包") {
                    inner_A_Element.setAttribute("name", "btn_openclass");
                    inner_A_Element.setAttribute("clickkey", "zyzsyzhwy-week" + this.week + "-class");

                }
                else {
                    inner_A_Element.setAttribute("class", "btn2b");
                    inner_A_Element.setAttribute("name", "btn-unlock");
                    inner_A_Element.setAttribute("clickkey", "zyzsyzhwy-week-libao");
                }

                if (this.blank == "blank")
                    inner_A_Element.setAttribute("target", "_blank");

                inner_A_Element.setAttribute("href", this.url);

                inner_A_Element.innerText = this.btntxt;
                inner_li.appendChild(inner_A_Element);


                inner_rdiv.appendChild(inner_ul);
                document.getElementById("itemcontent").appendChild(inner_rdiv);
            });

        });

        $("[name=btn_openclass]").each(function (index, element) {
            var week = parseInt($(this).attr("data-week"));
            var classdate = parseInt($(this).attr("data-classdate").replace(/-/g, ""));
            var thisdayofweek = parseInt($(this).attr("data-dayofweek"));

            if (classdate > parseInt(currentDate)) {
                $(this).removeClass("btn2").addClass("btn2b");
                $(this).removeAttr("href");
                $(this).html("即将开启");
                $(this).attr("clickdata", "");
            }
            else {

            }
        });

        $("[name=btn_openclass]").click(function () {
            var _this = $(this);
            if (_this.html() == "即将开启") {
                return false;
            }
            if (!uid) {
                layer.msg("您尚未登陆，请登陆后再操作");
                return false;
            }
            var actcode = _this.attr("data-actcode");
            var classdate = _this.attr("data-classdate");
            var week = parseInt(_this.attr("data-week"));
            var dayofweek = parseInt(_this.attr("data-dayofweek"));
            var isBlank = _this.attr("target") == "_blank";

            //打开课程
            if (!!classdate && !isBlank) {
                PC_JH("EM_FUNC_OPEN_LIVE_VIDEO", "10,http://live.emoney.cn/livevideo/page/homepage?someday=" + classdate);
            }

            var btntext = "再看一遍";
            if (actcode == "2023050415" ||actcode == "2023050425" || actcode == "2023050435" ) {
                btntext = "再做一遍";
            }

            //观看记录
            $.ajax({
                type: 'get',
                url: "/activity/user/addcountbyactcode?actcode=" + actcode,
                dataType: 'jsonp',
                data: {
                    uid: uid,
                    value: "1"
                },
                success: function (data) {
                    if (_this.html() != "再看一遍" && _this.html() != "再做一遍") {
                        array[week]++;
                        if (array[week]>=3) {
                            $("#r" + week).find("[name=btn-unlock]").removeClass("btn2b").addClass("btn2").addClass("unlock").html("立即领取");
                        }
                    }
                    _this.html(btntext);

                }
            });

            pageClick(_this);
        });

        $(".left li").click(function () {
            var num = $(this).attr("data-num");
            $(this).siblings().removeClass("on");
            $(this).addClass("on");

            $(".right").hide();
            $("#r" + num).show();
        });

        //立即查看
        $(".btn3").click(function () {

            var num = $(this).attr("data-num");
            $(this).parent().siblings().removeClass("on");

            $(this).parent().siblings().find(".btn3").each(function (index, element) {
                var num = $(this).attr("data-num");
                if (parseInt(num) <= currentweek) {
                    $(this).show();
                }
            });

            $(this).parent().addClass("on");
            $(this).parent().find(".nr").show();

            $(".right").hide();
            $("#r" + num).show();

            $("#btn_close").attr("clickdata", num);
            $("#btn_close").attr("clickremark", "智盈专属迎战红五月-第" + num + "周-继续学习");

            //获取大礼包按钮状态
            InitClickStatus(GetActCodes(num), num);

            pageClick($(this));
        });

        $("[name=btn-unlock]").click(function () {
            if ($(this).hasClass("unlock")) {
                //领取大礼包
                layer.open({
                    type: 1,
                    title: false,
                    skin: 'layui-layer-nobg', //没有背景色
                    area: ['auto'],
                    shade: 0.6,
                    shadeClose: false,
                    content: $(".tc1")
                });
            } else {
                layer.open({
                    type: 1,
                    title: false,
                    skin: 'layui-layer-nobg', //没有背景色
                    area: ['auto'],
                    shade: 0.6,
                    shadeClose: false,
                    content: $(".tc2")
                });
            }
            pageClick($(this));
        });

        $("#btn_close").click(function () {
            layer.closeAll();
        });

        $("#btn_IM").click(function () {
            var name = "PC-智盈专属迎战红五月-"+$(".left ul li.on").attr("data-name") + "-领取礼包";
            var b = new Base64();
            PC_JH('EM_FUNC_START_IM', '0,20230504dr588,' + b.encode(name));
            layer.closeAll();
            pageClick($(this));
        });

        $(".btn1").click(function () {
            var name = "PC-智盈专属迎战红五月-" +$(".left ul li.on").attr("data-name") + "-课堂笔记";
            var b = new Base64();
            PC_JH('EM_FUNC_START_IM', '0,20230504dr588,' + b.encode(name));
            pageClick($(this));
        });

        // 点击不再提示
        $("#notips").click(function () {
            if ($(this).prop('checked')) {
                setCookie(cookiename_isshow, getQueryString("uid"), 60);
            } else {
                setCookie(cookiename_isshow, "", -1);//清除cookie
            }
        });
    });

    function InitClickStatus(actcodes,week) {
        $.ajax({
            type: 'get',
            url: "/activity/user/issubmitbyactcodes?actcodes=" + actcodes,
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if (data.code == '200') {
                    var count = 0;
                    var ret = data.data;
                    var val = ret.split(",");
                    var codes = actcodes.split(',');
                    for (var i = 0; i < val.length; i++) {
                        if (!!val[i]) {
                            count++;

                            //按钮状态变为“再看一遍”
                            var btntext = "再看一遍";

                            if (codes[i] == "2023050415" ||codes[i] == "2023050425" || codes[i] == "2023050435" ) {
                                btntext = "再做一遍";
                            }

                            $("#r" + week).find("[data-actcode=" + codes[i] + "]").html(btntext);
                        }
                    }
                    array[week] = count;
                    if (count >= 3) {
                        $("#r" + week).find("[name=btn-unlock]").removeClass("btn2b").addClass("btn2").addClass("unlock").html("立即领取");
                    } else {
                        $("#r" + week).find("[name=btn-unlock]").attr("clickdata","");
                    }
                }
            }
        });
    }

    function GetActCodes(week) {
        var codes = "";
        switch (week.toString()) {
            case "1":
                codes = actcode_week1;
                break;
            case "2":
                codes = actcode_week2;
                break;
            case "3":
                codes = actcode_week3;
                break;
            default:
                break;
        }
        return codes;
    }

    function pageClick(obj){
        var App = "10013";   //APPID 没有请申请
        var Module = "dr588_20230504";//模块名称
        var Remark = "";     //备注可为空
        var ClickFlag = true;//默认为true
        var Host = "https://api2-tongji.emoney.cn";
        var ClickUrl = Host + "/Page/PageClick";
        var PageViewUrl = Host + "/Page/PageView";
        var pageUrl = window.top.location.href;

        var _clickkey = obj.attr("clickkey");
        var _clickdata = obj.attr("clickdata");
        var _clickremark = obj.attr("clickremark");
        var _htmltype = obj.attr("type");
        if (App != "" && _clickdata != "") {
            var src = ClickUrl + "?v=" + Math.random()
                + "&app=" + App
                + "&module=" + Module
                + "&clickkey=" + _clickkey
                + "&clickdata=" + _clickdata
                + "&clickremark=" + _clickremark
                + "&htmltype=" + _htmltype
                + "&pageurl=" + encodeURIComponent(pageUrl)
                + "&remark=" + Remark;
            var elm = document.createElement("img");
            elm.src = src;
            elm.style.display = "none";
            document.body.appendChild(elm);
        }
    }

    function GetExternal() {
        return window.external.EmObj;
    }

    function PC_JH(type, c) {
        try {
            var obj =
                GetExternal();
            return obj.EmFunc(type, c);
        } catch (e) { }
    }
    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
    function setCookie(c_name, value, expiredays) {
        var exdate = new Date();
        exdate.setDate(exdate.getDate() + expiredays);
        document.cookie = c_name + "=" + escape(value) + ";expires=" + exdate.toGMTString() + ";path=/";
    }

    function getCookie(c_name) {

        if (document.cookie.length > 0) {
            c_start = document.cookie.indexOf(c_name + "=")

            if (c_start != -1) {
                c_start = c_start + c_name.length + 1
                c_end = document.cookie.indexOf(";", c_start)
                if (c_end == -1) c_end = document.cookie.length
                return unescape(document.cookie.substring(c_start, c_end))
            }
        }
        return ""
    }
</script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "dr588_20230504";//模块名称
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    //$.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>

</body>
</html>
