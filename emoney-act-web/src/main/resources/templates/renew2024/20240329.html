<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2024/20240329/css/style.css?r=20240329'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"><div class="main"><marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="3" direction="left" class="txt1">正值一季报披露季，好工具在手才能赢得先机，限时开放五星研报领取通道！
</marquee></div></div>
<div class="img_2">
    <div class="main">
        <a href="javascript:void(0);" class="btn1 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
        <a href="javascript:void(0);" class="btn3" id="btn_getReward" clickkey="btn_getreward" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
        <!--状态2-已领取-->
        <a href="javascript:void(0);" class="btn3h toPay" style="display: none;" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
    </div>
</div>
<div class="img_3"><div class="main"><a href="javascript:void(0);" class="btn4 dh" id="goIM" clickkey="btn_im" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a></div></div>
<div class="img_4">
    <div class="main"><a href="javascript:void(0);" class="btn5 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a></div></div>
<div class="img_5">
    <div class="main">
        <div class="hdgz">
            <ul>
                <li>活动有效时间：2024年4月1日-2024年4月14日。</li>
                <li>活动参与对象：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。</li>
                <li>福利发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限和【五星研报】权限。</li>
                <li>积分规则：在完成续费和适当性测评后发放200积分（冻结状态），适当性测评完成30天后，积分解冻，可在积分商城使用。</li>
            </ul>
        </div>
        <div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #63202e;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
            本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
    </div></div>

<div class="bott">
    <div class="main"><a href="javascript:void(0);" class="btn2 dh toPay"></a></div>
</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc3" style="display: none;"><a href="javascript:void(0)" class="close"></a><a href="javascript:void(0);" class="tc-btn dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a></div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js'}"></script>
<script th:src="@{${staticPath}+'static/renew2024/20240329/js/main.js'}"></script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20240401";//模块名称(焦点图2)
    var Remark = "小智盈续费";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
