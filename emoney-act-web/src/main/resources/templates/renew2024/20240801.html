<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2024/20240801/css/style.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>
<body>
<div class="img_1"><div class="main">
    <div class="djs"><span class="t">0</span><span class="s">23</span><span class="f">59</span><span class="m">59</span>
    </div></div></div>
<div class="img_2"></div>
<div class="img_3">
    <div class="main">
        <div class="jdt"><div class="bg3"></div></div>
        <div class="yes">60%</div>
        <div class="no">40%</div>
        <a href="javascript:void(0)" class="bg1">能</a><a href="javascript:void(0)" class="bg2">不能</a>
    <!--状态2-->
        <div class="txt2" style="display: none;"><div class="bt">您已预测“能”</div>
            若预测成功，奖励将于10月8日前到账</div>
        <a href="javascript:void(0)" class="b an1">详细规则&gt;&gt;</a><a href="javascript:void(0)" class="btn1 dh" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"><div class="ico" style="display: none;"></div></a>
        <marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="3" direction="left" class="txt1"></marquee></div></div>
<div class="img_4"></div>
<div class="img_5">
    <div class="main"><a href="javascript:void(0)" class="btn3 dh" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div></div>
<div class="img_6"><div class="main"><a href="javascript:void(0)" class="btn4 dh" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div></div>
<div class="img_7"></div>
<a href="javascript:void(0)" class="btn5 dh" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a><a href="javascript:void(0)" class="an2">活动规则</a>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="bott">
    <div class="main"><a href="javascript:void(0)" class="btn2 dh" name="payurl" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div>
</div>
<a href="javascript:void(0)" class="pf2"></a>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>
<div class="h"><div class="hdgz"><a href="javascript:void(0)" class="close hdgzx"></a>
    <strong>活动规则：</strong><br />
    一、活动时间：2024年8月1日-8月31日。<br />
    二、行情预测规则
    <ul>
        <li>奖励发放对象：对参与本次续费，并预测成功的用户，发放6个月智盈软件使用期奖励。</li>
        <li>预测规则：若2024年9月30日之前，上证收盘后达3122点，则视为站上3122点，否则即视为未站上；活动期间仅可预测1次，选择后无法更改</li>
        <li>福利发放时间：若2024年9月30日之前，上证收盘后达3122点，则对预测成功的用户在站上3122点当日后5个工作日内发放奖励；若2024年9月30日之前，上证收盘后均未达3122点，则在10月8日前，对预测成功的用户发放奖励。</li>
        <li>如用户在活动期发起退货，则视为放弃参加活动。</li>
    </ul>
    三、续费活动规则
    <ul>
        <li>活动参与对象：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。</li>
        <li>福利发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限和【三步擒龙】权限。3节《赢在趋势》私享课，仅限智盈软件端（PC大直播/APP课堂）观看直播/回放，若课程开始后参与续费，则仅能观看回放，回放3个月内不限次数观看。</li>
        <li>优惠券规则：持本活动有效优惠券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用，不可用于其他续费活动。</li>
        <li>本活动涉及课程仅用于软件基础教学，不涉及投资咨询及投资顾问服务，课程内容不构成投资建议，股市有风险，投资需谨慎。</li>
        <li>本活动最终解释权归益盟股份有限公司，如有疑问请致电客服10108688。</li>
    </ul>
</div></div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">
<input type="hidden" id="hid_hasCoupon" th:value="${hasCoupon90}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script type="text/javascript" th:src="@{${staticPath}+'static/js/jquery.slides.js'}"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js'}"></script>
<script th:src="@{${staticPath}+'static/renew2024/20240801/js/main.js'}"></script>
<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数


        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs=setInterval("SetTimeout(2024,8,31,24,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0"+i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2024/8/31 24:00:00");
    var b=new Date("2024/8/22 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("0");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs").show();
    }

    $(window).scroll(function(e){
        if($(window).scrollTop() >= 700){
            $('.bott').fadeIn(300);
        }else{
            $('.bott').fadeOut(300);
        }
    })
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "sz-xe20240801";//上拽小额课程
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>