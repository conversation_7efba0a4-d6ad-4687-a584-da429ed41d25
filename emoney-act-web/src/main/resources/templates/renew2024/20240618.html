<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>益盟操盘手</title>
    <meta name="Keywords" content="益盟,炒股软件">
    <meta name="Description" content="好功能·好福利·好课程·好服务 年中钜惠一促即发">
    <link th:href="@{${staticPath}+'static/renew2024/20240618/style/common.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();

                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");


                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();

    </script>
</head>
<body>
<div data-container>
    <div class="bod">
        <div id="dbg1"><div class="main"><div class="djs"><span class="t">0</span><span class="s">23</span><span class="f">59</span><span class="m">59</span>
        </div></div></div>
        <div id="dbg2">

        </div>
        <div id="dbg3">
            <div class="main"> <a href="javascript:void(0)" class="btn dh2 toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"><div class="hend wobble2"></div></a>
                <div class="an1">
                    <div class="txt">
                        <input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 16px; height:16px;">
                        <a href="javascript:void(0)" class="ys">我已阅读并同意《信息服务协议》</a></div>
                </div>
            </div>
        </div>
        <div id="dbg4"></div>
        <div id="dbg5"><div class="main">
            <div class="an3"> <a href="javascript:void(0)" class="btn2 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
                <div class="txt">
                    <input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 16px; height: 16px;">
                    <a href="javascript:void(0)" class="ys">我已阅读并同意《信息服务协议》</a></div>
            </div>
        </div></div>
        <div id="dbg6"><div class="main">
            <div class="an2"> <a href="javascript:void(0)" class="btn4 dh2 toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"><span class="ico"></span><div class="hend wobble2"></div></a>
                <div class="txt">
                    <input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 16px; height: 16px;">
                    <a href="javascript:void(0)" class="ys">我已阅读并同意《信息服务协议》</a></div>
            </div>
        </div></div>
        <div id="dbg7"><div class="main">
            <div class="an5"> <a href="javascript:void(0)" class="btn4 dh2 toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"><div class="hend wobble2"></div></a>
                <div class="txt">
                    <input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 16px; height: 16px;">
                    <a href="javascript:void(0)" class="ys">我已阅读并同意《信息服务协议》</a></div>
            </div>
            <div class="txt1">1、活动有效时间：2024年6月17日-2024年7月31日。<br />
                2、活动参与对象：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。<br />
                3、权限发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限和【三步擒龙】权限。3节《赢在趋势》私享课，仅限智盈软件端（PC大直播/APP课堂）观看直播/回放，回放3个月内不限次数观看。<br />
                4、本活动涉及课程仅用于软件基础教学，不涉及投资咨询及投资顾问服务，课程内容不构成投资建议，股市有风险，投资需谨慎。<br />
                5、本活动最终解释权归益盟股份有限公司，如有疑问请致电客服10108688。</div>
        </div></div>
        <div id="dbg12">
            <div class="main">
                <div class="an4"> <a href="javascript:void(0)" class="btn3 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
                    <div class="txt">
                        <input type="checkbox" class="notips" clickkey="close" clickdata="close" style="width: 16px; height: 16px;">
                        <a href="javascript:void(0)" class="b ys">我已阅读并同意《信息服务协议》</a></div>
                </div>
            </div>
        </div>
    </div>
    <div class="footer">投资有风险 入市需谨慎<br>
        从事证券投资或期货交易，请通过合法证券期货经营机构进行。<br>
        合法机构名单可到中国证监会网站（www.csrc.gov.cn）查询<br>
        益盟股份有限公司 电话：021-61958888 沪ICP备06000340<br>
        地址：上海市杨浦区国权北路1688弄湾谷科技园8-11号</div>
    <div class="h2">
        <div class="wxts"><div class="bt">温馨提示</div><div class="t1">为了更好的保障您的合法权益，请您<br />阅读并同意以下协议<a href="javascript:void(0)" class="ys">《信息服务协议》</a></div><div class="t2"><li id="no">不同意</li>
            <a class="toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"><li id="yes" class="txt"><span class="red">同意</span></li></a></div></div>
    </div>
    <div class="h" data-htc>
        <div class="yhxy"><a href="javascript:void(0)" class="close"></a>
            <iframe frameborder="0" src="https://www.emoney.cn/dianjin/bb/yhxy.pdf" class="txt2" name="sp"></iframe>
        </div>
    </div>
</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script type="text/javascript" th:src="@{${staticPath}+'static/js/jquery.slides.js'}"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js'}"></script>
<script th:src="@{${staticPath}+'static/renew2024/20240618/js/main.js'}"></script>
<script>
    $(window).scroll(function(e){
        if($(window).scrollTop() >= 840){
            $('#dbg12').fadeIn(300);
        }else{
            $('#dbg12').fadeOut(300);
        }});
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数


        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs=setInterval("SetTimeout(2024,7,31,24,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0"+i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2024/7/31 24:00:00");
    var b=new Date("2024/7/22 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("0");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs").show();
    }
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "sz-xe20240618";//上拽小额课程
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>