<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2024/20240531/css/style.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>
<body>
<div class="img_1"><div class="main"><div class="djs">4999</div></div></div>
<div class="img_2"></div>
<div class="img_3"></div>
<div class="img_4">
    <div class="main">
        <a href="###" class="btn1 an1 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
        <a href="###" class="btn1 an2 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
        <a href="#" class="btn3 an4 dh"></a>
    </div>
</div>
<div class="img_5"><div class="main">
    <div class="txt2" style="margin-top: -30px;"><a href="#a1" class="b1"></a> <a href="#a2" class="b2"></a> <a href="#a3" class="b3"></a> <a href="#a4" class="b4"></a> <a href="#a5" class="b5"></a> <a href="#a6" class="b6"></a> </div>
</div></div>
<div class="img_6" id="a1"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al1"></iframe></div></div>
<div class="img_7" id="a2"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al2"></iframe></div></div>
<div class="img_8" id="a3"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al3"></iframe></div></div>
<div class="img_9" id="a4"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al4"></iframe></div></div>
<div class="img_10" id="a5"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al5"></iframe></div></div>
<div class="img_11" id="a6"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al6"></iframe></div></div>
<a href="###" class="btn2 dh"></a>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="bg2">
    <a href="###" class="btn3 an3"></a>
</div>
<a href="#" class="server" id="goIM"></a>
<div class="bott">
    <div class="main">
        <a href="###" class="btn3 dh"></a>
        <a href="###" class="btn4 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
    </div>
</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc4" style="display: none;"><a href="###" class="close"></a>
        <span class="black">1、活动参与对象：</span>仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。<br />
        <span class="black">2、福利发放规则：</span>完成续费和适当性测评后开通【智盈软件使用期】权限。<br />
        <span class="black">3、优惠券规则：</span>领券后15天有效。持本活动有效优惠券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用。<br />
        <span class="black">4、积分规则：</span>续费返还积分，在完成续费和适当性测评后发放，且为冻结状态，适当性测评完成30天后，积分解冻，可在积分商城使用。
    </div>
    <div class="tc5" style="display: none;"><a href="###" class="close"></a><a href="###" class="toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"><img th:src="@{${staticPath}+'static/renew2024/20240531/images/tc1.png'}" alt=""></a></div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_hascoupon" th:value="${hasCoupon30}">

<script type="text/javascript" th:src="@{${staticPath}+'static/js/jquery.slides.js'}"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js'}"></script>
<script th:src="@{${staticPath}+'static/renew2024/20240531/js/main.js'}"></script>
<script>
    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";
    var al7 = "0";
    var al8 = "0";

    $(window).scroll(function(e){

        if(($(window).scrollTop() > 2235) && (al1 == "0")){
            $("[name='al1']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=4657bc07ff4c4454a551f9ab054ef967&rep=1&py=0");
            al1 = "1";
        }
        if(($(window).scrollTop() > 2969) && (al2 == "0")){
            $("[name='al2']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a91c1c3760a5493b96ff80a1ee6156e5&rep=1&py=0");
            al2 = "1";
        }
        if(($(window).scrollTop() > 3706) && (al3 == "0")){
            $("[name='al3']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=7e21a20e948143f996916654fc1b15bd&rep=1&py=0");
            al3 = "1";
        }
        if(($(window).scrollTop() > 4443) && (al4 == "0")){
            $("[name='al4']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=762180b0b3444952b7c5bbe83a6d342a&rep=1&py=0");
            al4 = "1";
        }
        if(($(window).scrollTop() > 5182) && (al5 == "0")){
            $("[name='al5']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=026641ddcab74f8e89427033e4d04463&rep=1&py=0");
            al5 = "1";
        }
        if(($(window).scrollTop() > 5909) && (al6 == "0")){
            $("[name='al6']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=19ba234f7bc34b69ba0d2a6a7a801bab&rep=1&py=0");
            al6 = "1";
        }

    });

    var videoList = $('iframe')
    var wHeigt = window.innerHeight
    document.addEventListener('scroll', function () {
        var isPlay = false
        //滚动条高度+视窗高度 = 可见区域底部高度
        var visibleBottom = window.scrollY + document.documentElement.clientHeight
        //可见区域顶部高度
        var visibleTop = window.scrollY
        for (var i = 0; i < videoList.length; i++) {
            var centerY = $(videoList[i]).offset().top + (videoList[i].offsetHeight / 2)
            if (centerY > visibleTop && centerY < visibleBottom) {
                if (!isPlay) {
                    videoList[i].src.match(/py=0/) && (videoList[i].src = videoList[i].src.replace(/py=0/, 'py=1'))
                    isPlay = true
                } else {
                    videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
                }
            } else {
                videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
            }
        }
    })
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20240531";//模块名称(焦点图2)
    var Remark = "小智盈续费";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>