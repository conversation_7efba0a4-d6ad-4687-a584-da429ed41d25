<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2024/20240412/css/style.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2">
    <div class="main"><a href="#a1" class="txt2">含2节实战小课&gt;</a></div></div>
<div class="img_3"><div class="main"><a href="javascript:void(0)" class="btn1 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a><marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="3" direction="left" class="txt1">恭喜EMY124****续费成功，成功领取3个月三步擒龙！  恭喜EMY584****续费成功，成功领取3个月三步擒龙！恭喜EMY544****续费成功，成功领取3个月三步擒龙！恭喜EMY921****续费成功，成功领取3个月三步擒龙！恭喜EMY614****续费成功，成功领取3个月三步擒龙！恭喜EMY873****续费成功，成功领取3个月三步擒龙！恭喜EMY811****续费成功，成功领取3个月三步擒龙！恭喜EMY581****续费成功，成功领取3个月三步擒龙！恭喜EMY284****续费成功，成功领取3个月三步擒龙！恭喜EMY221****续费成功，成功领取3个月三步擒龙！
</marquee></div></div>
<div class="img_4">
    <div class="main">
        <iframe frameborder="0" webkitallowfullscreen="true" mozallowfullscreen="true" allowfullscreen="true" src="https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=b96f73a523c548ddb9d0d957ced725f6&py=1" scrolling="no" class="sp"></iframe><a href="javascript:void(0)" class="btn4 dh toPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
    </div>
</div>
<div class="img_5">
    <div class="main"><a href="javascript:void(0)" class="btn4 dh toPay" style="margin-top: 70px;" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a></div></div>
<div class="img_6" id="a1"></div>
<div class="img_7">
    <div class="main"><a href="javascript:void(0)" class="btn5 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
        <div class="hdgz"><div class="f28">活动规则</div>
            1、活动有效时间：2024年4月15日-2024年5月5日。<br />
            2、活动参与对象：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。<br />
            3、福利发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限和【三步擒龙】权限。<br />
            4、本活动涉及课程仅用于软件基础教学，不涉及投资咨询及投资顾问服务，课程内容不构成投资建议，股市有风险，投资需谨慎。<br />
            5、积分规则：在完成续费和适当性测评后发放200积分（冻结状态），适当性测评完成30天后，积分解冻，可在积分商城使用。<br />
            6、本活动最终解释权归益盟股份有限公司，如有疑问请致电客服10108688。
        </div>
        <div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
            本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
    </div></div>

<div class="bott">
    <div class="main"><a href="javascript:void(0)" class="btn2 toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a></div>
</div>
<div class="bg" style="display: none;">
	<div class="tc" style="display: none;">
            <div class="bt">请登录</div>
            <div class="txt">
                <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
                <div class="red">*请填写您的真实姓名</div>
                手机号码 <input id="loginmobile" type="text" />
                <div class="red">*请输入购买智盈产品时用的手机号</div>
                <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
                <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
                <div>
                    <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
                </div>
            </div>
        </div>
	</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_allpoint" th:value="${allPoint}">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js'}"></script>
<script th:src="@{${staticPath}+'static/renew2024/20240412/js/main.js'}"></script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20240412";//模块名称(焦点图2)
    var Remark = "小智盈续费";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
