<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2024/20240101/css/style.css?r=20231227'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2">
    <div class="main">
        <div class="txt1">￥<span class="f60">128</span><span class="f14">先降再送</span></div>
        <div class="txt3">18个月智盈+3节筹码私享课</div>
        <a href="javascript:void(0)" class="btn1 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
        <a th:if="${allPoint >= 60}" href="javascript:void(0)" class="ico1" name="btn_exchange">花60积分兑换￥30券</a>
        <a th:if="${hasCoupon30}" href="javascript:void(0)" class="ico1 toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台">￥30积分券待使用</a>
        <div class="an1">
            <div class="txt"><input type="checkbox" class="notips"  clickkey="close" clickdata="close"> <a href="javascript:void(0)" class="ys">我已阅读并同意《信息服务协议》</a></div>
        </div>
    </div>
</div>
<div class="img_3"></div>
<div class="img_4"></div>
<div class="img_5"></div>
<div class="img_6">
    <div class="hdgz">
    <ul>
        <li><strong>参与对象</strong>：益盟智盈用户；</li>
        <li><strong>活动时间</strong>：2024年1月1日-1月31日；</li>
        <li><strong>活动流程</strong>：完成支付即可获得智盈软件端3节线上筹码实战课的听课资格，及原智盈软件18个月使用期，课程3个月内不限次数观看回放；</li>
        <li>本活动涉及课程仅用于软件基础教学，不涉及投资咨询及投资顾问服务，课程内容不构成投资建议，股市有风险，投资需谨慎；</li>
        <li><strong>本活动退货规则</strong>：10个工作日无理由退款，超过10个工作日不接受任何形式的退款；</li>
        <li>本活动最终解释权归益盟股份有限公司，如有疑问请致电客服：10108688</li>
    </ul></div>
    <div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
        本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
</div>
<div class="bott">
    <div class="main">
        <div class="txt2">￥<span class="f60">128</span><span class="f25">先降再送</span></div>
        <div class="txt5">18个月智盈+3节筹码私享课</div>
        <a href="javascript:void(0)" class="btn2 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台">续费返200积分</a><div class="an2">
        <div class="txt"><input type="checkbox" class="notips"  clickkey="close" clickdata="close"> <a href="javascript:void(0)" class="ys b">我已阅读并同意《信息服务协议》</a></div>
    </div></div>
</div>
<div class="bg" style="display: none">
	<div class="tc" style="display: none">
            <div class="bt">请登录</div>
            <div class="txt">
                <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
                <div class="red">*请填写您的真实姓名</div>
                手机号码 <input id="loginmobile" type="text" />
                <div class="red">*请输入购买智盈产品时用的手机号</div>
                <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
                <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
                <div>
                    <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
                </div>
            </div>
    </div>
    <div class="tc3" style="display: none"><a href="javascript:void(0)" class="close" style="margin: 100px 20px 0 0"></a>
        <div class="txt7"><span class="org">￥128</span>享18个月智盈</div>
        <ul>
            <li>直降<br />￥160</li>
            <li>含<span class="yellow">3</span>节<br /><span class="yellow">筹码课</span></li>
            <li>续1年<br />送6个月</li>
        </ul>
        <a th:if="${hasCoupon30}" href="javascript:void(0)" class="tc-btn dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台">￥30积分券待使用</a>
        <a th:if="${!hasCoupon30}" href="javascript:void(0)" class="tc-btnb dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
        <div class="an3">
            <div class="txt"><input type="checkbox" class="notips"  clickkey="close" clickdata="close"> <a href="javascript:void(0)" class="ys b">我已阅读并同意《信息服务协议》</a></div>
        </div>
    </div>
    <div class="tc4" style="display: none">
        <a href="javascript:void(0)" class="close"></a>
        <div class="txt6">先降再抵，每月仅需￥5.5</div>
        <a href="javascript:void(0)" class="tc-btn2 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
        <div class="an4">
            <div class="txt"><input type="checkbox" class="notips"  clickkey="close" clickdata="close"> <a href="javascript:void(0)" class="ys b">我已阅读并同意《信息服务协议》</a></div>
        </div>
    </div>
    <div class="yhxy" style="display: none"><a href="javascript:void(0)" class="close"></a>
        <iframe frameborder="0" src="https://www.emoney.cn/dianjin/bb/yhxy.pdf" class="nr" name="sp"></iframe>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_allpoint" th:value="${allPoint}">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js'}"></script>
<script th:src="@{${staticPath}+'static/renew2024/20240101/js/main.js'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_2024010102";//模块名称(焦点图2)
    var Remark = "小智盈续费";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script></body>
</html>
