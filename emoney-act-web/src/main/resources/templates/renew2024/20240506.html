<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2024/20240506/css/style.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"><div class="main"><div class="ico"></div><div class="djs"><span class="t">00</span><span class="s">23</span><span class="f">59</span><span class="m">59</span>
</div></div></div>
<div class="img_2"></div>
<div class="img_3">
    <div class="main">
        <div id="cont1">
            <div class="txt4">回归福利价</div>
            <div class="txt2">288</div>
            <div class="txt3">福袋省￥90<br />到手
                <span class="red">￥198</span>
            </div>
        </div>
        <div id="cont2" style="display: none;">
            <div class="ico2">已入账</div><div class="ico2" style="margin-top:70px;">待使用</div>
            <div class="txt4">回归到手价</div>
            <div class="txt2">198</div>
            <div class="txt3">
                续费券<br />已抵扣￥90
            </div>
        </div>
    </div>
</div>
<div class="img_4">
    <div class="main">
        <a href="javascript:void(0)" class="btn1 dh"></a>
        <a href="javascript:void(0)" class="btn3 dh toPay" style="display: none;" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
        <marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="3" direction="left" class="txt1">
            恭喜EMY156****成功领取福袋，￥198续费3年智盈+享6个月北上资金！
        恭喜EMY019****成功领取福袋，￥198续费3年智盈+享6个月北上资金！
        恭喜EMY167****成功领取福袋，￥198续费3年智盈+享6个月北上资金！
        恭喜EMY178****成功领取福袋，￥198续费3年智盈+享6个月北上资金！
        恭喜EMY137****成功领取福袋，￥198续费3年智盈+享6个月北上资金！
        恭喜EMY002****成功领取福袋，￥198续费3年智盈+享6个月北上资金！
        恭喜EMY009****成功领取福袋，￥198续费3年智盈+享6个月北上资金！
        恭喜EMY069****成功领取福袋，￥198续费3年智盈+享6个月北上资金！
        恭喜EMY135****成功领取福袋，￥198续费3年智盈+享6个月北上资金！
        恭喜EMY150****成功领取福袋，￥198续费3年智盈+享6个月北上资金！
    </marquee></div></div>
<div class="img_5"><div class="main"><a href="javascript:void(0)" class="btn4 an1 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a></div></div>
<div class="img_6"><div class="main"><a href="javascript:void(0)" class="btn4 an2 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a></div></div>
<div class="img_7">
    <div class="main"><div class="al"><!-- 代码 开始 -->
        <div class="slider_name slider_box">
            <ul class="silder_con">
                <li class="silder_panel"><img th:src="@{${staticPath}+'static/renew2024/20240506/images/a1.png'}"></li>
                <li class="silder_panel"><img th:src="@{${staticPath}+'static/renew2024/20240506/images/a2.png'}"></li>
                <li class="silder_panel"><img th:src="@{${staticPath}+'static/renew2024/20240506/images/a3.png'}"></li>
            </ul>

            <ul class="silder_nav">
                <li class=""></li>
                <li class=""></li>
                <li class=""></li>
            </ul>
        </div>
        <a href="javascript:void(0)" class="prev"></a><a href="javascript:void(0)" class="next"></a>
        <!-- 代码 结束 --></div></div></div>
<div class="img_8">
    <div class="main"><a href="javascript:void(0)" class="btn4 an3 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a>
        <div class="hdgz"><div class="f28">活动规则</div>
            1、活动有效时间：2024年5月6日-2024年5月31日。<br />
            2、活动参与对象：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。<br />
            3、福利发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限和【北上资金】权限。<br />
            4、优惠券规则：持本活动有效优惠券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用，不可用于其他续费活动。<br />
            5、积分规则：福袋领取积分立即到账，可在积分商城使用。<br />
            6、本活动最终解释权归益盟股份有限公司，如有疑问请致电客服10108688。
        </div>
    </div></div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="bott">
    <div class="main"><a href="javascript:void(0)" class="btn2 toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a></div>
</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>

    <div class="tc3" style="display: none;"><a href="javascript:void(0)" class="close"></a><a href="javascript:void(0)" class="tc-btn dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台"></a></div>
</div>
<script>
    function SetTimeout(year,month,day,hour,minute,second){
        var leftTime = (new Date(year,month-1,day,hour,minute,second)) - (new Date()); //计算剩余的毫秒数
        var days = parseInt(leftTime / 1000 / 60 / 60 / 24 , 10); //计算剩余的天数
        var hours = parseInt(leftTime / 1000 / 60 / 60 % 24 , 10); //计算剩余的小时
        var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);//计算剩余的分钟
        var seconds = parseInt(leftTime / 1000 % 60, 10);//计算剩余的秒数


        hours = checkTime(hours);
        minutes = checkTime(minutes);
        seconds = checkTime(seconds);

        $(".t").html(days);
        $(".s").html(hours);
        $(".f").html(minutes);
        $(".m").html(seconds);
    }
    djs=setInterval("SetTimeout(2024,5,31,24,00,00)",1000);
    function checkTime(i){ //将0-9的数字前面加上0，例1变为01
        if(i<10)
        {
            i = "0"+i;
        }
        return i;
    }
    SetTimeout();

    var classdate = new Date();
    var a=new Date("2024/5/31 24:00:00");
    var b=new Date("2024/5/22 00:00:00");
    if (classdate.getTime() > a.getTime()) {
        clearInterval(djs);
        $(".t").html("0");
        $(".s").html("00");
        $(".f").html("00");
        $(".m").html("00");
    }
    if (classdate.getTime() > b.getTime()) {
        $(".djs").show();
        $(".ico").hide();
    }
</script>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_allpoint" th:value="${allPoint}">

<script type="text/javascript" th:src="@{${staticPath}+'static/js/jquery.slides.js'}"></script>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js'}"></script>
<script th:src="@{${staticPath}+'static/renew2024/20240506/js/main.js'}"></script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20240506";//模块名称(焦点图2)
    var Remark = "小智盈续费";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>

</body>
</html>
