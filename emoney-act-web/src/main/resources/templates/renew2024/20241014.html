<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2024/20241014/css/css.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script th:src="@{${staticPath}+'static/js/awardRotate.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="dbg1"></div>
<div class="dbg2"></div>
<div class="dbg3"></div>
<div class="dbg4"><div class="main"><a href="" class="btn1 an1 dh2">立即续费</a></div></div>
<div class="dbg5">
    <div class="main"><div class="hdgz2">1、凡是在活动期间参与续费的用户，从11月1日起至2025年4月30日半年内，上证指数收盘后，未达5178点，在此期间产生的服务信息费，将以使用期形式补贴给用户。<br /><br />
        <span class="org">举例:用户王女士10月25日进行续费活动，上证指数在1月15日收盘价5178点，计算日11月1日-1月15日历时2个半月，给予王女士2个半月的产品使用期进行补贴，若王女士在11月22日购买产品，计算日从11月22日-1月15日，计55日的使用期补贴，早买早享受。</span><br />
        <br />
        2、使用期补贴在5178点条件达成后10个交易日内完成延期补贴。如果截止2025年4月30日，5178点条件仍未达成，将在2025年5月11日前完成延期补贴。<br />
        <br />
        3、若发生退货行为，5178点活动不再生效，已产生信息费按照折算规则进行计费。<br />活动最终解释权归益盟股份有限公司所有

    </div></div></div>
<div class="dbg6"><div class="main"><div class="zp"></div>
    <a href="###" class="zz" id="rotate"></a><div class="go" id="btn-lottery"><img th:src="@{${staticPath}+'static/renew2024/20241014/images/go.png'}"/></div>
    <div class="rftime">剩余抽奖次数：<span id="rft_num"></span>次</div>
    <div class="md">
        <marquee onmouseover="this.stop()" onmouseout="this.start()" scrollamount="2" direction="up" height="150px">
            <ul id="user_list">
<!--                <li>恭喜 em****537 用户喜获 <span>智盈 3个月使用期</span></li>-->
<!--                <li>恭喜 em****537 用户喜获 <span>智盈 3个月使用期</span></li>-->
<!--                <li>恭喜 em****537 用户喜获 <span>智盈 3个月使用期</span></li>-->
            </ul>
        </marquee>
        <ul style="margin-top: 50px;height: 75px;overflow: auto;" id="my_list">
<!--            <li>恭喜 em****118 用户喜获 <span>智盈 3个月使用期</span></li>-->
<!--            <li>恭喜 em****118 用户喜获 <span>智盈 1个月使用期</span></li>-->
        </ul>
    </div>
    </div>

</div></div>
<div class="dbg7">
    <div class="main"><div class="hdgz">
        <ul>
            <li>10月12日至11月22日参与智盈续费的用户可参与抽超级锦鲤的机会，续约1年版用户可获得1次抽奖机会，续约2年版用户可获得2次抽奖机会。</li>
            <li>超级锦鲤免单奖励以服务期延期方式赠送。<br /><span class="org">例如王先生在活动期间抽中免单，如果王先生参与了续费1年，就会额外获得一年使用期。如果王先生参与了续费2年，就会额外获得两年使用期。</span></li>
            <li>抽奖礼品将在活动结束后的10个工作日内发送，限时活动，错过不再有。</li>
        </ul>
    </div></div></div>
<div class="footer">欢迎登录益盟官方网站www.emoney.cn<br />
    本活动最终解释权归上海益盟软件技术股份有限公司　沪ICP备06000340<br />
    软件需数据支持，仅提供辅助建议，风险需谨慎；投资顾问意见仅作参考。</div>
<div class="pf">
    <div class="main"><a href="" class="btn2 dh2">我要抢优惠</a></div></div>

<div class="bg" style="display: none">
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc">
        <a href="###" class="close"></a>
        <div class="tc_1">恭喜您获得<br />
            <span class="red">3个月</span>智盈使用期</div>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renew2024/20241014/js/main.js?r=20241112'}"></script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_20241014";//模块名称(焦点图2)
    var Remark = "小智盈续费";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script></body>
</html>
