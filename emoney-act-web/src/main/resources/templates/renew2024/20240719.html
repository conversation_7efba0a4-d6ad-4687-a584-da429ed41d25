<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/renew2024/20240719/css/style.css'}" rel="stylesheet" type="text/css" />
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2">
    <div class="main">
        <a th:style="'display:' + @{(${hasCoupon200} ? 'none' : 'block')} + ''" href="javascript:;" class="btn1 an1 dh" clickkey="getCoupon" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a><!--无券点击赠送优惠券-->
        <!--状态2-->
        <a th:style="'display:' + @{(${hasCoupon200} ? 'block' : 'none')} + ''" href="javascript:void(0)" name="payurl" class="btn1h an1" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a>
        <div th:style="'display:' + @{(${hasCoupon200} ? 'block' : 'none')} + ''" class="ico">恭喜您领取成功，请在领券后7天内使用！</div>

        <marquee onMouseOver="this.stop()" onMouseOut="this.start()" scrollamount="1" direction="up" class="txt2">
            恭喜！Emy20****使用200元立减券，￥88元续费1年会员！<br />
    恭喜！Emy20****使用200元立减券，￥88元续费1年会员！<br />
    恭喜！Emy20****使用200元立减券，￥88元续费1年会员！<br />
    恭喜！Emy20****使用200元立减券，￥88元续费1年会员！</marquee></div></div>
<div class="img_3"><div class="main"><a href="javascript:void(0)" name="payurl" class="btn2" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div></div>
<div class="img_4" id="al1"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al1"></iframe></div></div>
<div class="img_5" id="al2"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al2"></iframe></div></div>
<div class="img_6" id="al3"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al3"></iframe></div></div>
<div class="img_7" id="al4"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al4"></iframe></div></div>
<div class="img_8" id="al5"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al5"></iframe></div></div>
<div class="img_9"  id="al6"><div class="main"><iframe frameborder="0" src="about:blank" scrolling="no" class="sp" name="al6"></iframe></div></div>
<div class="img_10">
    <div class="main"><div class="hdgz">1、活动参与对象：仅限智盈老用户参加本活动1次，智盈大师或其他产品用户不参与本续费活动。<br />
        2、福利发放规则：完成续费和适当性测评后开通【智盈软件使用期】权限。<br />
        3、优惠券规则：持本活动有效优惠券的用户，续费时可抵扣续费金额。本优惠券不可与其他优惠叠加使用。</div></div></div>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><img th:src="@{${staticPath}+'static/renew2024/20240719/images/pf.png'}" alt="" usemap="#Map" border="0"><a href="javascript:void(0)" name="payurl" class="btn3 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a>
    <map name="Map" id="Map">
        <area shape="rect" coords="18,68,178,129" href="#al1" />
        <area shape="rect" coords="19,130,177,192" href="#al2" />
        <area shape="rect" coords="19,192,178,255" href="#al3" />
        <area shape="rect" coords="21,255,178,326" href="#al4" />
        <area shape="rect" coords="20,327,177,388" href="#al5" />
        <area shape="rect" coords="21,389,177,456" href="#al6" />
    </map></div>

<div class="bott" th:style="'display:' + @{(${hasCoupon200} ? 'none' : 'block')} + ''">
    <div class="main"><div class="ico2">限时领￥200优惠券</div><a href="javascript:void(0)" name="payurl" class="btn4 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div>
</div>
<!--状态2-->
<div class="bott2" th:style="'display:' + @{(${hasCoupon200} ? 'block' : 'none')} + ''">
    <div class="main"><div class="ico2">￥200优惠券已生效</div><a href="javascript:void(0)" name="payurl" class="btn4 dh" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div>
</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc5" style="display: none;"><a href="javascript:;" class="close"></a><a href="javascript:void(0)" class="btn5" clickkey="getCoupon" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div><!--抢券-->
    <div class="tc6" style="display: none;"><a href="javascript:;" class="close"></a><a href="javascript:void(0)" name="payurl" target="_blank" class="btn6" clickkey="openPay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}"></a></div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">
<input type="hidden" id="hid_hasCoupon" th:value="${hasCoupon200}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">

<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js'}"></script>
<script th:src="@{${staticPath}+'static/renew2024/20240719/js/main.js'}"></script>
<script>
    var downflag = "0";
    var al1 = "0";
    var al2 = "0";
    var al3 = "0";
    var al4 = "0";
    var al5 = "0";
    var al6 = "0";
    var al7 = "0";
    var al8 = "0";

    $(window).scroll(function(e){

        if(($(window).scrollTop() > 535) && (al1 == "0")){
            $("[name='al1']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=4657bc07ff4c4454a551f9ab054ef967&rep=1&py=0");
            al1 = "1";
        }
        if(($(window).scrollTop() > 1269) && (al2 == "0")){
            $("[name='al2']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=a91c1c3760a5493b96ff80a1ee6156e5&rep=1&py=0");
            al2 = "1";
        }
        if(($(window).scrollTop() > 1806) && (al3 == "0")){
            $("[name='al3']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=7e21a20e948143f996916654fc1b15bd&rep=1&py=0");
            al3 = "1";
        }
        if(($(window).scrollTop() > 2743) && (al4 == "0")){
            $("[name='al4']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=762180b0b3444952b7c5bbe83a6d342a&rep=1&py=0");
            al4 = "1";
        }
        if(($(window).scrollTop() > 3382) && (al5 == "0")){
            $("[name='al5']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=026641ddcab74f8e89427033e4d04463&rep=1&py=0");
            al5 = "1";
        }
        if(($(window).scrollTop() > 3909) && (al6 == "0")){
            $("[name='al6']").attr("src","https://static-dsclient.emoney.cn/emvplayer/normal/videoMediaSDKnew.html?midtype=vod&uid=&pid=null&appid=&voteid=0&uname=&authcode=888888&ischeckpwd=0&courseid=0&bztype=&ownerid=19ba234f7bc34b69ba0d2a6a7a801bab&rep=1&py=0");
            al6 = "1";
        }

    });

    var videoList = $('iframe')
    var wHeigt = window.innerHeight
    document.addEventListener('scroll', function () {
        var isPlay = false
        //滚动条高度+视窗高度 = 可见区域底部高度
        var visibleBottom = window.scrollY + document.documentElement.clientHeight
        //可见区域顶部高度
        var visibleTop = window.scrollY
        for (var i = 0; i < videoList.length; i++) {
            var centerY = $(videoList[i]).offset().top + (videoList[i].offsetHeight / 2)
            if (centerY > visibleTop && centerY < visibleBottom) {
                if (!isPlay) {
                    videoList[i].src.match(/py=0/) && (videoList[i].src = videoList[i].src.replace(/py=0/, 'py=1'))
                    isPlay = true
                } else {
                    videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
                }
            } else {
                videoList[i].src.match(/py=1/) && (videoList[i].src = videoList[i].src.replace(/py=1/, 'py=0'))
            }
        }
    })
</script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "sz-xe20240719";//上拽小额课程
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
