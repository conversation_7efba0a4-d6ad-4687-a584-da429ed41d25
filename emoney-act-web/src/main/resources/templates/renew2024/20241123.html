<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <meta name="Keywords" content="益盟,炒股软件" />
    <meta name="Description" content="双12大促 千人半价团 花1年的钱用2年！能赚更能省！"/>
    <link th:href="@{${staticPath}+'static/renew2024/20241123/style/css.css'}" rel="stylesheet" type="text/css" />
    <script th:src="@{${staticPath}+'static/js/jquery-1.11.0.min.js'}"></script>
    <script type="text/javascript">
        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"></div>
<div class="img_3"><div class="main"><div class="pic wow zx" data-wow-duration="1000ms"></div></div></div>
<div class="img_4"><div class="main"><div class="ico1 wow zx" data-wow-duration="1000ms"></div><a href="javascript:void(0)" class="btn an1 dh" clickkey="btn" clickdata="btn"></a></div></div>
<div class="img_5"><div class="main"><div class="cj"><div class="btn2">
    <ul>
        <li></li>
        <li></li>
        <li></li>
    </ul>
</div>
    <div class="ico2">
        <ul>
            <li>11月23日-12月15日期间参与续费，即可获得1次抽奖机会，抽奖活动不可重复参与。</li>
            <li>若发生退货行为，奖品一律退还</li>
            <li>活动最终解释权归益盟股份有限公司所有</li>
        </ul>
    </div>
</div>
    <div class="jp wow zx" data-wow-duration="1000ms"><img src="images/jp1.png" alt=""></div>
    <!--奖品2<div class="jp wow zx" data-wow-duration="1000ms"><img src="images/jp2.png" alt=""></div>
       奖品3<div class="jp wow zx" data-wow-duration="1000ms"><img src="images/jp3.png" alt=""></div>-->
</div></div>
<div class="img_6"></div>
<div class="foot">欢迎登录益盟官方网站 <a href="http://www.emoney.cn" target="_blank">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<div class="pf"><div class="main"><a href="javascript:void(0)" class="btn an2 dh" clickkey="btn" clickdata="btn"></a></div></div>

<div class="bg" style="display: none">
    <div class="tc-login" style="display: none">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-btn2">清除</a>
            </div>
        </div>
    </div>
    <div class="tc">
        <a href="###" class="close"></a>
        <div class="tc_1">恭喜您获得<br />
            <span class="red">3个月</span>智盈使用期</div>
    </div>
</div>

<input type="hidden" id="hid_actcode" th:value="${actCode}">
<input type="hidden" id="hid_isLogin" th:value="${isLogin}">

<input type="hidden" id="hid_uid" th:value="(${isLogin}?${loginUserInfo.uid}:'')">
<input type="hidden" id="hid_pid" th:value="(${isLogin}?${loginUserInfo.pid}:'')">
<input type="hidden" id="hid_mobilex" th:value="(${isLogin}?${loginUserInfo.MobileX}:'')">
<input type="hidden" id="hid_maskmobile" th:value="(${isLogin}?${loginUserInfo.MaskMobile}:'')">
<input type="hidden" id="hid_staticPath" th:value="${staticPath}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/js/utils.opt.js?r=20241112'}"></script>
<script th:src="@{${staticPath}+'static/renew2024/20241123/js/main.js?r=20241127'}"></script>
<script src="https://www.emoney.cn/dianjin/bb/wow2.js"></script>
<link href="https://www.emoney.cn/dianjin/bb/animate.css" rel="stylesheet" type="text/css" />
<script src="https://imgtongji.emoney.cn/scripts/https/emoneyanalytics.js" type="text/javascript"></script>
<script type="text/javascript">
    var App = "10088";   //APPID 没有请申请
    var Module = "xzyxf-20241123";//小智盈续费
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>
