<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="https://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>益盟操盘手智盈</title>
    <link th:href="@{${staticPath}+'static/lottery2023/20231031/css/style.css?r=20230808'}" rel="stylesheet" type="text/css"/>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js" type="text/javascript"></script>
    <script type="text/javascript">

        var www="../";
        (function() {
            function GetExternal() {
                return window.external.EmObj;
            }

            function PC_JH(type, c) {
                try {
                    var obj =
                        GetExternal();
                    return obj.EmFunc(type, c);
                } catch (e) {}
            }

            function LoadComplete() {
                try {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                } catch (ex) {}
            }

            function EM_FUNC_HIDE() {
                try {
                    PC_JH("EM_FUNC_HIDE", "");
                } catch (ex) {}
            }

            function EM_FUNC_SHOW() {
                try {
                    PC_JH("EM_FUNC_SHOW", "");
                } catch (ex) {}
            }

            function IsShow() {
                try {
                    return PC_JH("EM_FUNC_WND_ISSHOW", "");
                } catch (ex) {
                    return "0";
                }
            }

            function openWindow() {
                LoadComplete();
                PC_JH("EM_FUNC_SET_STYLE", "/resize=0  /maxbox=1 /savepos=1");
                PC_JH("EM_FUNC_WND_SIZE", "w=1300,h=820,mid");
                PC_JH("EM_FUNC_SET_TITLE", "益盟操盘手智盈");

                if (IsShow() != "1") {
                    EM_FUNC_SHOW();
                }
            }
            openWindow();
        })();
    </script>
</head>

<body>
<div class="img_1"></div>
<div class="img_2"></div>
<div class="img_3">
    <div class="main">
        <a href="javascript:void(0)" class="btn6"></a>
        <a href="javascript:void(0)" class="zz dh" name="doLottery" clickkey="btn_doLottery" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="抽奖"></a>
        <div class="ico1" id="lotteryTips"><span class="red2">1</span>次免费抽奖待使用</div>
        <!--状态2<div class="ico1">20积分兑换1次抽奖</div>-->
    <div class="ico6">
        <div class="process" id="hotCount"></div>
        <div class="ico3"></div>
        <div class="ico4"></div>
        <div class="ico5"></div>
    </div>
    <div class="txt1">8895</div>
    <div class="txt2">88积分：<span class="f1">已领取</span>　　
        积分余额：<span class="f1" id="point">888</span></div>
</div></div>
<div class="img_4">
    <div class="main">
        <div class="marquee" id="marquee">
            <div class="list"></div>
        </div>
        <a target="_blank" href="javascript:void(0)" class="btn1 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台1"></a>
        <a target="_blank" href="javascript:void(0)" class="btn4 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台2"></a>
    </div>
</div>
<div class="img_5"></div>
<a target="_blank" href="javascript:void(0)" class="btn3 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台3"></a>
<div class="bg3">
    <ul id="recordWrap" class="recordWrap">
    </ul>
</div>
<a href="javascript:void(0)" class="btn2 dh">活动规则&gt;</a>
<div class="footer">欢迎登录益盟官方网站 <a href="https://www.emoney.cn" target="_blank" style="color: #fff;">www.emoney.cn</a> 股市有风险，投资需谨慎<br />
    本活动最终解释权归益盟股份有限公司　沪ICP备06000340</div>
<a href="javascript:void(0)" class="server goIM"></a>
<div class="bott">
    <div class="main"><a target="_blank" href="javascript:void(0)" class="btn5 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台4"></a></div>
</div>
<div class="bg" style="display: none;">
    <div class="tc" style="display: none;">
        <div class="bt">请登录</div>
        <div class="txt">
            <span style="padding-right:46px;">姓名</span><input id="loginname" type="text" />
            <div class="red">*请填写您的真实姓名</div>
            手机号码 <input id="loginmobile" type="text" />
            <div class="red">*请输入购买智盈产品时用的手机号</div>
            <span style="padding-right:26px;">验证码</span><input type="text" id="loginsmscode" />
            <a id="sendcode" href="javascript:;" class="yzm">发送验证码</a>
            <div>
                <a id="btnlogin" href="javascript:;" class="tc-btn1">登录</a><a href="javascript:;" class="tc-clear">清除</a>
            </div>
        </div>
    </div>
</div>
<div class="mask" style="display: none" id="rulePopup">
    <div class="hdgz">
        <a href="javascript:void(0)" class="close" id="ruleClose"></a>
        <strong>活动规则：</strong>
        <ul>
            <li><strong>活动时间</strong>：2023年11月1日-2023年11月30日。</li>
            <li><strong>活动参与对象</strong>：仅限智盈付费用户参加，其他产品用户不参与本活动。</li>
            <li><strong>续费福利发放规则</strong>：<br />
                ①完成续费和适当性测评后开通【智盈软件使用期】权限和功能权限。<br />
                ②续费返还积分，在完成续费和适当性测评后5个工作日内发放，且为冻结状态，适当性完成30天后积分解冻。</li>
            <li><strong>抽奖规则</strong>：<br />
                ①智盈付费用户每天免费获得1次抽奖机会；每消耗20积分可增加1次抽奖机会；每天最多抽奖2次。<br />
                ②功能权限中奖后即刻开通；用户重新登录客户端，可见对应功能入口。<br />
                ③积分奖品、优惠券奖品中奖后即刻到账。<br />
                ④奖品记录可在“奖励领取记录”查询。</li>
            <li><strong>优惠券规则</strong>：持本活动有效优惠券的用户，续费时可抵扣续费金额。本活动优惠券不可与其他活动优惠叠加使用。</li>
            <li><strong>功能特权叠加规则</strong>：本活动领取的功能使用期不与其他活动领取的功能使用期叠加。</li>
            <li><strong>打卡活动规则</strong>：<br />
                ①打卡活动参与权限：<br />
                -限完成指定续费活动的小智盈用户参与。<br />
                -仅限使用续费时的手机号参与打卡。续费后更换手机号，则无法完成打卡。<br />
                -用户EM账号过期，则无法打卡。<br />
                ②打卡及使用期赠送规则：<br />
                -登录打卡页面视为有效打卡；一个自然周内打卡满3天，可领取7天使用期。<br />
                -打卡期限自第一次打卡之日起计算，打卡活动最多送365天使用期，可与续费所得使用期叠加。<br />
                -若参加多轮打卡活动，则打卡周期累计顺延。<br />
                -每周一0点刷新打卡天数，未领取的使用期自动作废。<br />
                -若参加打卡后退单，赠送的使用期也随之失效。</li>
            <li>本活动最终解释权归益盟股份有限公司所有。</li>
            <li>活动开展期间，如出现不可抗力等情况，如发生自然灾害，网络攻击，电信故障停机维护、疫情等本平台免于承担责任</li>
        </ul></div>
</div>

<div class="mask" style="display: none" id="tc1">
    <!--？号-->
    <div class="popup-center">
        <img th:src="@{${staticPath}+'static/lottery2023/20231031/images/tc1.png'}" alt="">
<!--        <a href="javascript:void(0)" class="close"></a>-->
    </div>
</div>

<div class="mask" style="display: none" id="firstPopup">
    <!--88 + 100优惠券-->
    <div class="popup-center">
        <img th:src="@{${staticPath}+'static/lottery2023/20231031/images/tc2.png'}" alt="">
        <a href="javascript:;" class="tc-btn an7 dh" name="doLottery" clickkey="btn_doLottery" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="抽奖pop">点击抽奖</a>
    </div>
</div>

<div class="mask" style="display: none" id="dayPopup">
    <!--一天弹一次-->
    <div class="popup-center">
        <img th:src="@{${staticPath}+'static/lottery2023/20231031/images/tc4.png'}" alt="">
        <a target="_blank" href="javascript:void(0)" class="tc-btn an3 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台pop">立即参与</a>
    </div>
</div>

<div class="mask" style="display: none" id="pointPopup">
    <!--10积分/21天使用期-->
    <div class="popup-center">
        <img th:src="@{${staticPath}+'static/lottery2023/20231031/images/tc3.png'}" alt="">
        <div class="txt3">积分x30</div>
        <a target="_blank" href="javascript:void(0)" class="tc-btn an2 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台pop">激活更多福利</a>
    </div>
</div>

<div class="mask" style="display: none" id="northPopup">
    <!--30天北上资金-->
    <div class="popup-center">
        <img th:src="@{${staticPath}+'static/lottery2023/20231031/images/tc5.png'}" alt="">
        <a target="_blank" href="javascript:void(0)" class="tc-btn an4 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台pop">激活更多福利</a>
    </div>
</div>

<div class="mask" style="display: none" id="starPopup">
    <!--3/7天五星研报-->
    <div class="popup-center">
        <img th:src="@{${staticPath}+'static/lottery2023/20231031/images/tc6.png'}" alt="">
        <div class="txt4">7天五星研报</div>
        <a target="_blank" href="javascript:void(0)" class="tc-btn an4 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台pop">激活更多福利</a>
    </div>
</div>

<div class="mask" style="display: none" id="gzdPopup">
    <!--3天估值带-->
    <div class="popup-center">
        <img th:src="@{${staticPath}+'static/lottery2023/20231031/images/tc7.png'}" alt="">
        <a target="_blank" href="javascript:void(0)" class="tc-btn an5 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台pop">激活更多福利</a>
    </div>
</div>

<div class="mask" style="display: none" id="kingPopup">
    <!--3天k线故事-->
    <div class="popup-center">
        <img th:src="@{${staticPath}+'static/lottery2023/20231031/images/tc8.png'}" alt="">
        <a target="_blank" href="javascript:void(0)" class="tc-btn an5 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台pop">激活更多福利</a>
    </div>
</div>

<div class="mask" style="display: none" id="bsPopup">
    <!--3天买卖频谱-->
    <div class="popup-center">
        <img th:src="@{${staticPath}+'static/lottery2023/20231031/images/tc9.png'}" alt="">
        <a target="_blank" href="javascript:void(0)" class="tc-btn an6 dh toPay" clickkey="btn_pay" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="跳转收银台pop">激活更多福利</a>
    </div>
</div>

    <!--抽奖弹窗-->
<div class="mask" style="display: none" id="zzPopup">
    <div class="popup-center">
        <img th:src="@{${staticPath}+'static/lottery2023/20231031/images/tc10.png'}"  alt="">
        <a href="javascript:void(0)" class="tc-btn2 dh" name="doLottery" clickkey="btn_doLottery" th:clickdata="${isLogin=='1'?loginUserInfo.getUid():''}" clickremark="抽奖pop"></a>
    </div>
</div>
<input type="hidden" id="hid_actcode" th:value="${actcode}">
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script th:src="@{${staticPath}+'static/js/jsencrypt.min.js'}"></script>
<script th:src="@{${staticPath}+'static/js/login.js'}"></script>
<script th:src="@{${staticPath}+'static/js/base64.js'}"></script>
<script th:src="@{${staticPath}+'static/lottery2023/20231031/js/main.js'}" type="text/javascript"></script>
<script th:inline="javascript">
    function init () {
        setUserInfo()

        getHotCount()
        generateMarquee()
    }
    function setUserInfo () {
        var isLogin = /*[[${isLogin}]]*/ "0";
        if(isLogin == "1") {
            var channelcode = getQueryString("channelcode");
            if (!channelcode) {
                channelcode = "A12050";
            }
            userInfo.token = getQueryString("token");
            userInfo.uid = /*[[${loginUserInfo.uid}]]*/ "0";
            userInfo.pid = /*[[${loginUserInfo.pid}]]*/ "0";
            userInfo.uname = /*[[${loginUserInfo.MobileX}]]*/ "";
            userInfo.maskname = /*[[${loginUserInfo.MaskMobile}]]*/ "";
            userInfo.realname = /*[[${loginUserInfo.RealName}]]*/ "";
            userInfo.channelcode = channelcode;
            userInfo.type = "2";

            checkIsSendpp()
            getTotalCount()
            getUserPoints()
            getRecordList()
            getPayUrl()

            pushdatatocmp(userInfo.uname,"ACRenew20231101");
        }else{
            $(".bg").show();
            $(".tc").show();
        }
    }

</script>

<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://api2-tongji.emoney.cn/scripts/emoneyanalytics_s.js' type='text/javascript'%3E%3C/script%3E"));</script>
<script type="text/javascript">
    var App = "10013";   //APPID 没有请申请
    var Module = "renew588_2023103101";//模块名称(焦点图2)
    var Remark = "";     //备注可为空
    var ClickFlag = true;//默认为true
    $.EMoneyAnalytics.Init(App, Module, Remark, false, ClickFlag);
</script>
</body>
</html>
