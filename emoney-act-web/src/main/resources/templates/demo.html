<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>demo</title>
    <script src="static/js/jsencrypt.min.js"></script>
    <script src="https://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
</head>
<body>
<img th:src="${qr}">
<h1 th:text="${msg}">缺省文字</h1>

<input id="mobile" type="text">
<input type="button" id="send" value="提交">

<a target="_blank" id="btn_xufei">点击续费</a>
<img id="img"/>
<script src="static/js/paytransfer.js"></script>
<script>
    var uid = getQueryString("uid");
    var pid = getQueryString("pid");

    function bingoevent(payurl,qrurl){
        $("#btn_xufei").attr("href",payurl);
        $("#img").attr("src",qrurl);
    }
    payTransfer.getPayUrl(uid,pid,bingoevent);

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }
</script>
<script>
    $(function (){
        var encrypt = new JSEncrypt();
        $.ajax({
            type: "get",
            url: "/getPublicKey",//访问路径
            contentType: 'application/json;charset=utf-8',//返回json结果
            success: function (data) {
                encrypt.setPublicKey(data);
            }
        });

        $("#send").click(function () {
            var mobile = $("#mobile").val();
            var encryptmobile = encrypt.encrypt(mobile);
            $.ajax({
                type: "post",
                url: "/rsa",//访问路径
                contentType: 'application/json;charset=utf-8',//返回json结果
                data: JSON.stringify({"mobile": encryptmobile}),
                success: function (data) {
                    console.log(data)
                }
            });
        });
    });
</script>
</body>
</html>