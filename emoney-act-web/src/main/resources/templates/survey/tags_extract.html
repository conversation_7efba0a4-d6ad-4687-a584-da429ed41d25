<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>标签提取</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            let firstButton = document.getElementById("button0");
            if (firstButton) {
                firstButton.click();
            }
        });

        function openTab(evt, button) {
            let surveyId = button.getAttribute('surveyId');
            let i, tabContent, tabLinks;
            tabLinks = document.getElementsByClassName("tab-links");
            for (i = 0; i < tabLinks.length; i++) {
                tabLinks[i].className = tabLinks[i].className.replace(" active", "");
            }
            evt.currentTarget.className += " active";
            tabContent = document.getElementsByClassName("wrapper");
            for (i = 0; i < tabContent.length; i++) {
                tabContent[i].style.display = "none";
            }
            tabContent = document.getElementById(surveyId);
            tabContent.style.display = "block";


        }
    </script>
    <style>

        /* 设定标签页按钮的样式 */
        .tab-links {
            background-color: #f2f2f2;
            border: 1px solid #ccc;
            cursor: pointer;
            padding: 10px 20px;
        }

        /* 激活状态下的标签页按钮样式 */
        .tab-links.active {
            background-color: #ccc;
        }

        body, html {
            height: 100%;
            margin: 0;
            display: flex;
            /*background-color: #fff7dc;*/
            background-image: url(https://static-dsclient.emoney.cn/mins3/emoney-pro/tags_extract_backgroud.png);
            background-size: cover;
            background-repeat: no-repeat;
            background-attachment: fixed;
            flex-direction: column;
        }

        /* 设置包含两行的容器 */
        .container {
            display: flex;
            flex-wrap: wrap; /* 允许自动换行 */
            max-width: 1100px;
            margin: 0 auto;
        }

        /* 创建正方形的样式 */
        .square {
            width: 160px; /* 设置正方形的宽度 */
            height: 160px; /* 设置正方形的高度，与宽度相等 */
            background-color: #ffffff; /* 设置正方形的背景颜色 */
            border-radius: 10px; /* 为正方形的四个角添加10px的圆弧过度 */
            margin: 10px 30px; /* 设置正方形之间的间距 */
            position: relative; /* 设置相对定位，以便子元素相对于父元素定位 */
        }

        /* 创建上方文字区域，位于正方形上方的四分之一区域 */
        .text-top {
            position: absolute; /* 设置绝对定位，相对于父元素定位 */
            top: 0; /* 位于父元素的顶部 */
            left: 0; /* 位于父元素的左侧 */
            width: 100%; /* 宽度为父元素的宽度，实现四分之一区域 */
            height: 25%; /* 高度为父元素的四分之一，实现四分之一区域 */
            color: black; /* 设置文字颜色为白色 */
            text-align: center; /* 文字居中对齐 */
            line-height: 40px; /* 行高等于文字区域的高度，实现垂直居中 */
            border-bottom: 1px solid black; /* 为红色区域添加1px的灰色底边线 */
            border-radius: 10px; /* 为正方形的四个角添加10px的圆弧过度 */
        }

        /* 创建下方文字区域，位于正方形下方的三分之三区域 */
        .text-bottom {
            position: absolute; /* 设置绝对定位，相对于父元素定位 */
            bottom: 0; /* 位于父元素的底部 */
            left: 0; /* 位于父元素的左侧 */
            width: 100%; /* 宽度为父元素的宽度，实现三分之三区域 */
            height: 75%; /* 高度为父元素的三分之三，实现三分之三区域 */
            color: black; /* 设置文字颜色为白色 */
            text-align: center; /* 文字居中对齐 */
            display: flex; /* 使用 Flex 布局 */
            flex-direction: column; /* 设置垂直方向排列 */
            justify-content: center; /* 水平居中 */
            font-size: 18px; /* 将文字大小设置为10px，根据需要调整 */
            font-weight: bold;
            border-radius: 10px; /* 为正方形的四个角添加10px的圆弧过度 */
        }

        /* 创建下方文字区域中的每一行文字 */
        .text-line {
            display: flex; /* 使用 Flex 布局 */
            justify-content: center; /* 水平居中 */
            align-items: center; /* 垂直居中 */
        }

        /* 前部分普通字体 */
        .normal-font {
            font-weight: normal;
            font-size: 10px;
            margin-right: 4px;
        }

        /* 后部分字体大20%且加粗 */
        .bold-font {
            font-weight: bold;
            font-size: 18px; /* 将文字大小设置为12px，根据需要调整 */
        }

    </style>

</head>
<body>

<div style="padding-left: 100px">
    <p style="color: red; font-size: 15px; margin: 5px 0">
        <span th:if="${userName != null && !userName.isEmpty()}"
              style="font-size: 18px; font-weight: bold; margin-right: 10px" th:text="${userName}"/>
        <span th:unless="${userName != null && !userName.isEmpty()} "
              style="font-size: 18px; font-weight: bold; margin-right: 10px" th:text="${uid}"/>
        用户调查结果</p>
</div>
<div th:if="${list != null}" style="padding-left: 100px">
    <button th:each="dto, curr : ${list}"
            class="tab-links"
            th:id="'button' + ${curr.index}"
            th:attr="surveyId=${dto.surveyId}"
            onclick="openTab(event, this)"
            th:text="${dto.surveyName}"
            style="padding: 5px"
    />
</div>
<div th:if="${list == null}" style="font-size: 20px; padding-top: 100px; padding-left: 300px; color: red">无数据~~</div>

<div id="display">
    <div th:each="dto : ${list}" th:id="${dto.surveyId}" class="wrapper">
        <div style="padding-left: 100px">调查时间： <span th:text="${dto.submitTime}"/></div>
        <div class="container">
            <div th:if="${dto.tags != null}" th:each="tag : ${dto.tags}" class="square">
                <!-- 上方文字区域 -->
                <div class="text-top" th:text="${tag.title}"/>
                <!-- 判断children是否为空 -->
                <div class="text-bottom" th:if="${tag.children != null}">
                    <!-- 如果children不为空，递归展示children -->
                    <div class="text-line" th:each="child : ${tag.children}">
                        <span class="normal-font" th:text="${child.title}"></span>
                        <span class="bold-font" th:text="${child.detail}"></span>
                    </div>
                </div>
                <!-- 如果children为空，显示detail属性 -->
                <div class="text-bottom" th:if="${tag.children == null}" th:text="${tag.detail}"></div>
            </div>
        </div>

    </div>
</div>
</body>
</html>
