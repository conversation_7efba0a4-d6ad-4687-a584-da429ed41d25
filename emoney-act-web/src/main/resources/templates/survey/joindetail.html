
<!DOCTYPE html>
<html xmlns:th=“http://www.thymeleaf.org”>
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>答卷</title>
    <style>
        * {
            margin: 0;
        }
        body {
            background: #ebedf0;
        }
        .submit {
            width: 130px;
            letter-spacing: 20px;
            padding-left: 20px;
            border-radius: 6px;
            height: 45px;
            line-height: 45px;
            text-align: center;
            color: #fff;
            font-size: 16px;
            background: #1ea0fa;
            margin: 20px auto 0;
            cursor: pointer;
        }
        .submit:hover {
            background: #0987db;
            color: #fff;
        }
        .question-list {
            width: calc(100% - 140px);
            max-width: 800px;
            margin: 0 auto;
            background: #fff;
            padding: 20px 70px;
            height: calc(100vh - 125px);
            overflow-y: auto;
        }
        .question-list__item {
            margin-top: 16px;
        }
        .title {
            font-size: 16px;
            font-weight: bold;
            line-height: 26px;
            margin-bottom: 10px;
        }
        .answer {
            line-height: 1.4;
            font-size: 16px;
            color: #333;
            margin-left: 20px;
        }
        input {
            margin-bottom: 15px;
            cursor: pointer;
        }
        label {
            cursor: pointer;
            padding-left: 10px;
        }
        textarea {
            width: 500px;
            max-width: 100%;
            height: 150px;
            font-size: 16px;
        }
    </style>
</head>
<body>
<div class="question-list" id="question"></div>
<!--div class="submit" onclick="getValue()">提交</div-->
<script type="text/javascript" th:inline="javascript">
    var data = eval([[${template}]]);
    var result= eval("["+[[${result}]]+"]");
    var answer;

    // 绘制问卷
    window.onload = function () {
        var questionHtml = ''
        answer = result[0];
        for (var i = 0; i < data.length; i++) {
            var curData = data[i]
            var answerHtml = ''
            var answerItem = undefined;

            if (answer["q"+(i+1)]!=undefined){
                answerItem = answer["q"+(i+1)];
            }

            var answerType = judgeAnswerType(curData.TypeName);

            // 选项
            switch (answerType) {
                case '单选':
                    for (var j = 0; j < curData.Values.length; j++) {
                        var answerData = curData.Values[j];

                        var checked = "";
                        if (answerItem == j+1){
                            checked = "checked";
                        }
                        answerHtml += '<input type="radio" name="' + curData.Id + '" value="' + answerData.Id + '" id="' + curData.Id + '-' + answerData.Id + '" '+ checked +'/><label for="' + curData.Id + '-' + answerData.Id + '">' + answerData.Text + '</label><br />'
                    }
                    break
                case '多选':
                    var combin = [] ;
                    if (answerItem.indexOf(",")>-1){
                        combin = answerItem.split(",");
                    }else{
                        combin[0] = answerItem;
                    }

                    for (var j = 0; j < curData.Values.length; j++) {
                        var answerData = curData.Values[j];
                        var checked = "";

                        if(combin[j]!=undefined){
                            if (combin[j]==answerData.Id || combin[j]==answerData.id){
                                checked = "checked";
                            }
                        }

                        answerHtml += '<input type="checkbox" name="' + curData.Id + '" value="' + answerData.Id + '" id="' + curData.Id + '-' + answerData.Id + '" '+checked+'/><label for=' + curData.Id + '-' + answerData.Id + '">' + answerData.Text + '</label><br />'
                    }
                    break
                case '填空':
                    answerHtml += '<textarea name="' + curData.Id + '" value="'+answerItem+'">'+answerItem+'</textarea>'
                    break
                default:
                    break
            }
            var html = '<div class="question-list__item">' + '<div class="title">' + curData.Id.toUpperCase() + '、' + curData.Title + '</div>' + '<div class="answer">' + answerHtml + '</div>' + '</div>'
            questionHtml += html
        }
        var question = document.getElementById('question');
        question.innerHTML = questionHtml
    }

    // 获取问卷选择值
    function getValue() {
        var answer = {}
        for (var i = 0; i < data.length; i++) {
            answer[data[i].Id + 'A'] = getInputVal(data[i].Id, data[i].TypeName)
        }
        console.log(answer)
    }

    // 获取单个输入值
    function getInputVal(name, type) {
        var question = document.getElementsByName(name)
        var checkArr = []
        for (k in question) {
            if (question[k].checked) checkArr.push(question[k].value)
        }
        type = judgeAnswerType(type);
        switch (type) {
            case '单选':
                return checkArr[0] || ''
                break
            case '多选':
                return checkArr
                break
            case '填空':
                return question[0].value
                break
            default:
                return checkArr
                break
        }
    }


    function judgeAnswerType(oriType){

        if(oriType.indexOf('单选')>-1){return '单选';
        }else if (oriType.indexOf('多选')>-1) { return '多选';
        }else if (oriType.indexOf('填空')>-1) { return '填空';
        }

    }
</script>
</body>
</html>
