<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问卷提交结果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header.error {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .header .icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        
        .content {
            padding: 30px;
        }
        
        .info-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 4px solid #007bff;
        }
        
        .info-section h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
        }
        
        .info-label {
            font-weight: 600;
            color: #6c757d;
            min-width: 80px;
            margin-right: 10px;
        }
        
        .info-value {
            color: #495057;
            flex: 1;
        }
        
        .answers-section {
            margin-top: 30px;
        }
        
        .answers-section h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .question-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .question-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .question-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .question-no {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 10px;
        }
        
        .question-text {
            color: #495057;
            font-weight: 500;
            display: inline;
        }
        
        .answer-content {
            padding: 20px;
        }
        
        .answer-text {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #2196f3;
            color: #1565c0;
            font-weight: 500;
        }
        
        .empty-answers {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .empty-answers .icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 25px;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }
        
        .footer .success-message {
            color: #28a745;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #007bff;
            display: block;
        }
        
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .container {
                border-radius: 8px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .content {
                padding: 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .stats {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部状态 -->
        <div class="header" th:classappend="${surveyData.status == 'error'} ? 'error' : ''">
            <span class="icon" th:if="${surveyData.status == 'success'}">✓</span>
            <span class="icon" th:if="${surveyData.status == 'error'}">✗</span>
            <h1 th:if="${surveyData.status == 'success'}">问卷提交成功</h1>
            <h1 th:if="${surveyData.status == 'error'}">处理失败</h1>
            <p th:text="${surveyData.message}">感谢您的参与！</p>
        </div>
        
        <div class="content">
            <!-- 问卷基本信息 -->
            <div class="info-section" th:if="${surveyData.status == 'success'}">
                <h3>📋 问卷信息</h3>
                <div class="info-grid">
                    <div class="info-item" th:if="${surveyData.surveyName}">
                        <span class="info-label">问卷名称</span>
                        <span class="info-value" th:text="${surveyData.surveyName}">问卷名称</span>
                    </div>
                    <div class="info-item" th:if="${surveyData.userName}">
                        <span class="info-label">姓名</span>
                        <span class="info-value" th:text="${surveyData.userName}">用户姓名</span>
                    </div>
                    <div class="info-item" th:if="${surveyData.submitTime}">
                        <span class="info-label">提交时间</span>
                        <span class="info-value" th:text="${surveyData.submitTime}">提交时间</span>
                    </div>
                    <div class="info-item" th:if="${surveyData.ipAddress}">
                        <span class="info-label">IP地址</span>
                        <span class="info-value" th:text="${surveyData.ipAddress}">IP地址</span>
                    </div>
                    <div class="info-item" th:if="${surveyData.totalValue != null}">
                        <span class="info-label">总分</span>
                        <span class="info-value" th:text="${surveyData.totalValue}">总分</span>
                    </div>
                    <div class="info-item" th:if="${surveyData.timeTaken != null}">
                        <span class="info-label">用时</span>
                        <span class="info-value" th:text="${surveyData.formattedTimeTaken}">用时</span>
                    </div>
                    <div class="info-item" th:if="${surveyData.locationInfo != '未知'}">
                        <span class="info-label">地区</span>
                        <span class="info-value" th:text="${surveyData.locationInfo}">地区</span>
                    </div>
                    <div class="info-item" th:if="${surveyData.joinId}">
                        <span class="info-label">流水号</span>
                        <span class="info-value" th:text="${surveyData.joinId}">流水号</span>
                    </div>
                </div>
            </div>
            
            <!-- 答题内容 -->
            <div class="answers-section" th:if="${surveyData.status == 'success'}">
                <h3>📝 您的答题内容</h3>
                
                <div th:if="${surveyData.hasAnswers()}">
                    <div class="question-item" th:each="answer : ${surveyData.answers}">
                        <div class="question-header">
                            <span class="question-no" th:text="'Q' + ${answer.displayQuestionNo}">Q1</span>
                            <span class="question-text" th:text="${answer.questionText}">题目内容</span>
                        </div>
                        <div class="answer-content">
                            <div class="answer-text" th:text="${answer.formattedAnswerText}">答案内容</div>
                        </div>
                    </div>
                </div>
                
                <div class="empty-answers" th:unless="${surveyData.hasAnswers()}">
                    <div class="icon">📝</div>
                    <p>暂无答题内容</p>
                </div>
            </div>
        </div>
        
        <!-- 页脚 -->
        <div class="footer">
            <div class="success-message" th:if="${surveyData.status == 'success'}">
                ✨ 您的答卷已经成功提交并保存
            </div>
            <p>感谢您的参与！</p>
            
            <!-- 统计信息 -->
            <div class="stats" th:if="${surveyData.status == 'success'}">
                <div class="stat-item">
                    <span class="stat-value" th:text="${surveyData.answerCount}">0</span>
                    <div class="stat-label">答题数量</div>
                </div>
                <div class="stat-item" th:if="${surveyData.totalValue != null}">
                    <span class="stat-value" th:text="${surveyData.totalValue}">0</span>
                    <div class="stat-label">总分</div>
                </div>
                <div class="stat-item" th:if="${surveyData.timeTaken != null}">
                    <span class="stat-value" th:text="${surveyData.formattedTimeTaken}">0</span>
                    <div class="stat-label">用时</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
