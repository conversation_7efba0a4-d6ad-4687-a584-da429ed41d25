<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="gb2312">
    <title>五星波段六期用户专属</title>
    <link th:href="@{${staticPath}+'static/fiveband/style/common.css'}" rel="stylesheet" type="text/css"/>
    <style>
        .left{display: none;}
    </style>
    <script src="http://www.emoney.cn/dianjin/bb/jquery-1.9.1.js"></script>
    <script>
        (function () {
            function PC_JH(type, c) {
                try {
                    var obj = GetExternal();
                    return obj.EmFunc(type, c);
                }
                catch (e)
                { }
            }
            function GetExternal() {
                return window.external.EmObj;
            }
            function CloseWindow(){
                try{
                    PC_JH("EM_FUNC_CLOSE","");
                }catch(ex){}
            }
            function openWindow(){
                try
                {
                    PC_JH("EM_FUNC_DOWNLOAD_COMPLETE", "");
                    PC_JH("EM_FUNC_WND_SIZE", "w=970,h=630,mid");
                    PC_JH("EM_FUNC_SHOW", "");
                }catch(ex){}
            }
            openWindow();

        })();
    </script>
</head>
<body>
<div class="main">
    <div class="bg2">
        <ul>
            <li></li>
            <li></li>
            <li><a href="javascript:void(0)" class="btn3" data-day="3">领15天权益</a></li>
            <li></li>
            <li><a href="javascript:void(0)" class="btn3" data-day="5">领15天权益</a></li>
        </ul>
    </div>
    <div class="txt1">活动规则</div>
    <div class="txt3">活动期间完成学习任务即可获得五星波段使用期延长福利<br>每位学员每天最多只能学习一节新课，学完次日解锁下一节课</div>
    <div class="txt4">学习任务一：完成3天课程学习可领取15天五星波段使用期<br>学习任务二：完成5天课程学习可额外领取15天五星波段使用期</div>
    <div class="txt2">福利解锁进度</div>
    <div class="left" id="r1" style="display: block">课程1 《大师与波段》<img th:src="@{${staticPath}+'static/fiveband/images/p1.png'}" alt=""></div>
    <div class="left" id="r2">课程2《认识主题》<img th:src="@{${staticPath}+'static/fiveband/images/p2.png'}" alt=""></div>
    <div class="left" id="r3">课程3 《趋势与指标》<img th:src="@{${staticPath}+'static/fiveband/images/p3.png'}" alt=""></div>
    <div class="left" id="r4">课程4 《抄底形态》<img th:src="@{${staticPath}+'static/fiveband/images/p4.png'}" alt=""></div>
    <div class="left" id="r5">课程5 《中继回踩》<img th:src="@{${staticPath}+'static/fiveband/images/p5.png'}" alt=""></div>

    <div class="right"><ul>
        <li id="l1"><div class="t1">课程1</div><div class="t2"><span class="f18">《大师与波段》</span>褚伟锋</div><a href="javascript:void(0)" class="btn2b" data-videoid="ca73cbe033e7412bb03d81261c2f5c7c" data-actcode="2022090801">立即学习</a></li>
        <li id="l2"><div class="t1">课程2</div><div class="t2"><span class="f18">《认识主题》</span>涂举华</div><a href="javascript:void(0)" class="btn2" data-videoid="4a4fe576327b456c90facbf1347c8f8d" data-actcode="2022090802">即将开启</a></li>
        <li id="l3"><div class="t1">课程3</div><div class="t2"><span class="f18">《趋势与指标》</span>褚伟锋</div><a href="javascript:void(0)" class="btn2" data-videoid="aaaeeefb76bd4afaa463e813a51f01e7" data-actcode="2022090803">即将开启</a></li>
        <li id="l4"><div class="t1">课程4</div><div class="t2"><span class="f18">《抄底形态》</span>涂举华</div><a href="javascript:void(0)" class="btn2" data-videoid="4c46583c77e94e859d042335d4f2db44" data-actcode="2022090804">即将开启</a></li>
        <li id="l5"><div class="t1">课程5</div><div class="t2"><span class="f18">《中继回踩》</span>俞 湧</div><a href="javascript:void(0)" class="btn2" data-videoid="ef8da54dd41c45d588ca5fc003596136" data-actcode="2022090805">即将开启</a></li>
    </ul></div>
</div>
<div class="bg" style="display: none">
<div class="tc1" style="display: none;" id="pop3">完成三天课程学习任务，将获得延长15天五星波段使用时间的奖励，我们将于10个工作日内为您进行该奖励的绑定与发放，如有问题，可以咨询您的益盟五星助教。<a href="javascript:void(0)" class="btn">我已了解</a></div>
<div class="tc1" style="display: none;" id="pop5">即将完成五天课程学习任务，快将您的学习心得发送给您的益盟五星
    助教老师，领取额外延长15天五星波段使用时间的奖励吧！
    如有问题，可以咨询您的益盟五星助教。<a href="javascript:void(0)" class="btn">我已了解</a></div>
</div>

<input type="hidden" th:value="${staticPath}+'static/html/videoMediaSDK_fiveband.html' " id="hid_sdkurl"/>
<script th:src="@{${staticPath}+'static/libs/layer/layer.js'}"></script>
<script>
    var actcodes = "2022090801,2022090802,2022090803,2022090804,2022090805";
    $(document).ready(function () {
        var uid = getQueryString("uid");
        InitClickStatus(actcodes,uid);

        $('.left li').click(function () {
            $(this).addClass('on').siblings('li').removeClass('on');
        });
        $('.right li').click(function (){
            var index = $(this).index();
            $('.left').hide();
            $('.left').eq(index).show();
        });
        $(".btn3").click(function (){
            if($(this).parent().hasClass("on")) {
                var day = $(this).attr("data-day");

                $.ajax({
                    type: 'get',
                    url: "addclickrecord",
                    dataType: 'json',
                    data: {
                        uid: uid,
                        classid: day
                    },
                    success: function (data) {
                        if (data.code == "1") {
                            //二次点击
                            var cnt = "完成三天课程学习任务，您已经申请领取延长15天五星波段使用时间的奖励，我们将于10个工作日内为您进行该奖励的绑定与发放，请耐心等待，如有问题，可以咨询您的益盟五星助教。<a href=\"javascript:void(0)\" class=\"btn\">我已了解</a>";
                            if(day == "5"){
                                cnt = "完成五天课程学习任务，您已经申请领取过此任务延长15天五星波段使用时间的奖励，我们将于10个工作日内为您进行该奖励的绑定与发放，请耐心等待，如有问题，可以咨询您的益盟五星助教。<a href=\"javascript:void(0)\" class=\"btn\">我已了解</a>";
                            }
                            $("#pop"+day).html(cnt);
                        }

                        $(".bg").show();
                        $(".tc1").hide();
                        $("#pop"+day).show();
                    }
                });
            }
        });
        //我已了解
        $('.tc1').on('click', ".btn", function () {
            $(".bg").hide();
            $(".tc1").hide();
        });
        //立即学习  再看一遍
        $('.right').on('click', ".btn2b", function () {
        //$(".btn2b").click(function (){
            var videoid = $(this).attr("data-videoid"); //"9c43057891134db791da18775fe6a95e";
            var actcode = $(this).attr("data-actcode");
            var sdkurl = $("#hid_sdkurl").val() + "?actcode=" + actcode + "&timeduration=0&midtype=vod&ownerid=" + videoid + "&authcode=888888&uname=&" + location.search;
            var frameLoaded = !0;
           var layerID = layer.open({
                type: 2,
                title: '',
                skin: 'videopopframe',
                shadeClose: false,
                shade: 0.5,
                area: ['670px', '444px'],
                content: sdkurl,
                //content: ['http://', 'no'],
                success: function (layero, index) {
                    if(frameLoaded){
                        $(".videopopframe iframe")[0].allowFullscreen="true";
                        $(".videopopframe iframe")[0].src = sdkurl;
                        frameLoaded = !1;
                    }

                    // layer.iframeSrc(layerID, sdkurl);

                    $(".layui-layer-iframe").css("overflow-y", "visible");
                },
                cancel: function (index, layero) {
                    try {
                        InitClickStatus(actcodes,uid);
                        $(".videopopframe iframe")[0].contentWindow.parentCancelCallback(-2);
                    } catch (e) {
                    }
                }
            });
        });
    });

    function InitClickStatus(actcodes,uid) {
        var requestDate = getQueryString("date");
        var nowDate = new Date();
        if (!!requestDate) {
            nowDate = new Date(requestDate);
        }
        $.ajax({
            type: 'get',
            url: "https://act2017.emoney.cn/ActShort20200408/FlashSale/IsSubmitByActCodes?actcodes=" + actcodes,
            dataType: 'jsonp',
            data: {
                uid: uid
            },
            success: function (data) {
                if (data.retCode == "0") {
                    var ret = data.Message;
                    var val = ret.split(",");
                    for (var i = 0; i < val.length - 1; i++) {
                        if (!!val[i]) {
                            $(".bg2 li").eq(i).removeClass().addClass("on");
                            $(".right li").eq(i).find("a").removeClass().addClass("btn2b").html("再看一遍");
                        } else {
                            if (i > 0) {
                                var preDate = val[i - 1];
                                if (!!preDate) {
                                    var days = new Date(nowDate.toDateString()) - new Date(new Date(parseInt(preDate)).toDateString());
                                    if (days / (24*1000*3600) >= 1) {
                                        $(".right li").eq(i).find("a").removeClass().addClass("btn2b").html("立即学习");
                                        $('.left').hide();
                                        $('.left').eq(i).show();
                                    }else{
                                        $(".right li").eq(i).find("a").removeClass().addClass("btn2").html("即将开启");
                                    }
                                }
                            } else {
                                $(".right li").eq(i).find("a").removeClass().addClass("btn2b").html("立即学习");
                            }
                        }
                    }
                }
            }
        });
    }

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]); return null;
    }
</script>
<script type="text/javascript">document.write(unescape("%3Cscript src='https://imgtongji.emoney.cn/scripts/https/emoneyanalyticsalipv.js%3Fcode=9686870E00377E232D245F42D89AD16F' type='text/javascript'%3E%3C/script%3E"));</script>
</body>
</html>