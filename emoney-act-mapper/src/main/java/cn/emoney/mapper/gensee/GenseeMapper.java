package cn.emoney.mapper.gensee;

import cn.emoney.pojo.CourseWatchDO;
import cn.emoney.pojo.GenseeLiveBasicDO;
import cn.emoney.pojo.GenseeRecordedBasicDO;
import cn.emoney.pojo.bo.CourseWatchDetailDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface GenseeMapper {
    Optional<GenseeRecordedBasicDO> getRecordedBasic(@Param("lbId") String lbId);

    Optional<GenseeLiveBasicDO> getLiveBasic(@Param("webCastId") String webCastId);

    List<CourseWatchDetailDTO> getLiveWatchByUidAndVid(@Param("uid") Long uid,
                                                       @Param("vid") String vid);

    List<CourseWatchDetailDTO> getRecordedWatchByUidAndVid(@Param("uid") Long uid,
                                                           @Param("vid") String vid);

    List<Integer> getUserByLiveWatchTimeSumGte(@Param("vid") String vid,
                                               @Param("duration") Long duration);

    List<Integer> getUserByRecordedWatchTimeSumGte(@Param("vid") String vid,
                                                   @Param("duration") Long duration);

    List<CourseWatchDO> getUserByLiveWatchTimeSumGteByOffset(@Param("vid") String vid,
                                                             @Param("duration") Long duration,
                                                             @Param("offset") Integer offset,
                                                             @Param("limit") Integer limit);

    List<CourseWatchDO> getUserByLiveWatchDurationGte(@Param("vid") String vid,
                                                      @Param("duration") Long duration,
                                                      @Param("offset") Integer offset,
                                                      @Param("limit") Integer limit);

    List<CourseWatchDO> getUserByRecordedWatchDurationGteAndDate(@Param("vid") String vid,
                                                                 @Param("duration") Long duration,
                                                                 @Param("date") LocalDate date,
                                                                 @Param("offset") Integer offset,
                                                                 @Param("limit") Integer limit);

    List<CourseWatchDO> getUserByRecordedWatchDurationGteAndStartTimeLte(@Param("vid") String vid,
                                                                         @Param("duration") Long duration,
                                                                         @Param("startTime") LocalDateTime startTime,
                                                                         @Param("offset") Integer offset,
                                                                         @Param("limit") Integer limit);

    List<CourseWatchDO> getUserByRecordedWatchDurationGteAndStartTimeGteLte(@Param("vid") String vid,
                                                                            @Param("duration") Long duration,
                                                                            @Param("inTimeHead") LocalDateTime inTimeHead, @Param("inTimeTail") LocalDateTime inTimeTail,
                                                                            @Param("offset") Integer offset, @Param("limit") Integer limit);

    List<CourseWatchDO> getUserByRecordedWatchDurationGteAndLeaveTimeLte(@Param("vid") String vid,
                                                                         @Param("duration") Long duration,
                                                                         @Param("leaveTime") LocalDateTime leaveTime,
                                                                         @Param("offset") Integer offset,
                                                                         @Param("limit") Integer limit);
}
