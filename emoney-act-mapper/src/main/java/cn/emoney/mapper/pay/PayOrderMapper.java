package cn.emoney.mapper.pay;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Date;

import cn.emoney.pojo.PayOrderDO;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PayOrderMapper {
    /**
     * 根据订单状态和创建时间查询XE订单
     *
     * @param startCreateTime
     * @param orderStatus
     * @return java.util.List<cn.emoney.pojo.PayOrderDO>
     * <AUTHOR>
     * @date 2022/3/28 15:22
     */
    List<PayOrderDO> queryByCreateTimeAndOrderStatus(@Param("startCreateTime") Date startCreateTime, @Param("endCreateTime") Date endCreateTime, @Param("orderStatus") Integer orderStatus);
}