package cn.emoney.mapper.transfer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import cn.emoney.pojo.WechatChannelCodeDO;

@Mapper
public interface WechatChannelCodeMapper {
    /**
     * 根据sid获取二维码
     * <AUTHOR>
     * @date 2022/2/15 17:20
     * @param sid
     * @return cn.emoney.pojo.WechatChannelCodeDO
     */
    WechatChannelCodeDO queryBySid(String sid);
}