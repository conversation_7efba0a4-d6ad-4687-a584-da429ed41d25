package cn.emoney.mapper.activity;

import cn.emoney.pojo.InviteAwardDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-12-06
 */
@Mapper
public interface InviteAwardDetailMapper {
    List<InviteAwardDetailDO> getAll();

    List<InviteAwardDetailDO> getUnSendUser();

    List<InviteAwardDetailDO> getByMobileXAndOrderMobile(String mobileX,String orderMobileX);

    InviteAwardDetailDO getByOrderId(String orderId);
}
