package cn.emoney.mapper.activity;

import cn.emoney.pojo.FirstClassViewRecordDO;

import java.util.List;

public interface FirstClassViewRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(FirstClassViewRecordDO record);

    int insertSelective(FirstClassViewRecordDO record);

    FirstClassViewRecordDO selectByPrimaryKey(Integer id);

    List<FirstClassViewRecordDO> selectByClassIdAndUid(String uid, Integer classid);
}