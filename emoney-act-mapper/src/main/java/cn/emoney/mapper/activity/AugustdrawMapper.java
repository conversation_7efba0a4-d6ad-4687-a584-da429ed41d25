package cn.emoney.mapper.activity;

import cn.emoney.pojo.AugustdrawDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AugustdrawMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AugustdrawDO record);

    AugustdrawDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKey(AugustdrawDO record);

    List<AugustdrawDO> findAllByActivityid(@Param("activityid")String activityid);

}