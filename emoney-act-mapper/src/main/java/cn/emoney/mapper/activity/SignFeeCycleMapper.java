package cn.emoney.mapper.activity;

import cn.emoney.pojo.SignFeeCycle;
import cn.emoney.pojo.vo.SignFeeCycleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

@Mapper
public interface SignFeeCycleMapper {

    int insert(SignFeeCycle record);

    SignFeeCycleVO findLatestFeeCycleByUId(String uid);

    Integer selectFeeCount(@Param("paramsMap") Map<String,Object> map);

}
