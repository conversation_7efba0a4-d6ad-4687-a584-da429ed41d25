package cn.emoney.mapper.activity;

import cn.emoney.pojo.LotteryPrizelimitDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

/**
 * @Entity cn.emoney.pojo.LotteryPrizelimitDO
 */
@Mapper
public interface LotteryPrizelimitMapper {

    Integer increasePrizeSend(String batchno, String group, Date lotterytime,Integer level);
    LotteryPrizelimitDO getLotteryPrizeLimitByBatchNo(String batchno, String group,Integer level,Date lotterytime);

}




