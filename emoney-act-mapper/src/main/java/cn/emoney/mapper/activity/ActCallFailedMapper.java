package cn.emoney.mapper.activity;

import cn.emoney.pojo.ActCallFailedDO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ActCallFailedDO】的数据库操作Mapper
 * @createDate 2023-11-22 15:37:24
 * @Entity cn.emoney.mapper.activity.domain.ActCallFailedMapper
 */
@Repository
public interface ActCallFailedMapper {

    List<ActCallFailedDO> selectByUidAndActCode(String uid, String actCode);

    int insert(ActCallFailedDO record);

    int insertBatch(List<ActCallFailedDO> record);

}
