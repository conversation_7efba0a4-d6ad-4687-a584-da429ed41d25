package cn.emoney.mapper.activity;

import cn.emoney.pojo.LotteryDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Entity cn.emoney.pojo.LotteryDetailDO
 */
@Mapper
public interface LotteryDetailMapper {

    Integer insert(LotteryDetailDO lotteryDetailDO);

    Integer updatePrizeConfirm(Integer id);

    List<LotteryDetailDO> getUserLotteryInfoListByUid(String uid,String batchno);

    List<LotteryDetailDO> getRecentlyLotteryDetailList(String batchno);

    List<LotteryDetailDO> getLatestLotteryDetailList(String batchno);

    List<LotteryDetailDO> getLotteryDetailsFromDate(String batchno,String fromdate);
}




