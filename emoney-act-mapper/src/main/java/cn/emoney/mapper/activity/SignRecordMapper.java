package cn.emoney.mapper.activity;

import cn.emoney.pojo.SignRecord;
import cn.emoney.pojo.vo.NoReceiveRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface SignRecordMapper {

    int insert(SignRecord record);

    SignRecord selectOneByUId(String uid);

    List<String> selectAllUId();

    List<NoReceiveRecordVO> selectNoReceiveRecordList(String timeZone);
}