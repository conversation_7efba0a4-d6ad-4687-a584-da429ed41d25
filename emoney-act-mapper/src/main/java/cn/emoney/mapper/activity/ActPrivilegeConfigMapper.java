package cn.emoney.mapper.activity;

import cn.emoney.pojo.ActCallFailedDO;
import cn.emoney.pojo.Act_PrivilegeConfigDO;
import cn.emoney.pojo.Act_PrivilegeRecordDO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【Act_PrivilegeRecordDO】的数据库操作Mapper
 * @createDate 2024-01-22
 * @Entity cn.emoney.mapper.activity.domain.ActPrivilegeRecordMapper
 */
@Repository
public interface ActPrivilegeConfigMapper {

    int insert(ActCallFailedDO record);

    List<Act_PrivilegeConfigDO> selectByActCode(String actCode);

}
