package cn.emoney.mapper.activity;


import cn.emoney.pojo.FirstClassSpeFunUserDO;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description 针对表【Act_FirstClassSpeFunUser】的数据库操作Mapper
 * @createDate 2023-02-10 16:37:24
 * @Entity cn.emoney.mapper.activity.domain.ActFirstclassspefunuser
 */
public interface ActFirstclassSpeFunUserMapper {

    Optional<FirstClassSpeFunUserDO> selectByParentid(String uid);
}
