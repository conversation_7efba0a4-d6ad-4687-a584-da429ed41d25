package cn.emoney.mapper.activity;

import cn.emoney.pojo.YiyuangouadminuserDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface YiyuangouadminuserMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(YiyuangouadminuserDO record);

    int insertSelective(YiyuangouadminuserDO record);

    YiyuangouadminuserDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(YiyuangouadminuserDO record);

    int updateByPrimaryKey(YiyuangouadminuserDO record);
}