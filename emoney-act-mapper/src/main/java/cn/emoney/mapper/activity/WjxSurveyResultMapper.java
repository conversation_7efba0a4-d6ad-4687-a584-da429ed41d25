package cn.emoney.mapper.activity;
import cn.emoney.pojo.WjxSurveyResultDO;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * @Entity cn.emoney.survey.WjxSurveyResult
 */
@Mapper
public interface WjxSurveyResultMapper  {

    /***
     * 功能描述:
     * 新增答卷
     * @Param: [wjxSurveyResultDO]
     * @Return: int
     * @Author: tengdengming
     * @Date: 2022/3/9 15:23
     */
    Integer insert(WjxSurveyResultDO wjxSurveyResultDO);

    /***
     * 功能描述:
     * 分页获取答卷
     * @Param: [pageSize, pageStart]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2022/3/9 15:22
     */
    List<WjxSurveyResultDO> getSurveyList(Integer pageSize,Integer pageStart);

    /***
     * 功能描述:
     * 分页获取用户的答卷列表
     * @Param: [emUid, pageSize, pageStart]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2022/3/9 15:22
     */
    List<WjxSurveyResultDO> getUserSurveyList(Long emUid, Integer pageSize, Integer pageStart);

    /***
     * 功能描述:
     * 根据微信的UnionId获取对应的答卷
     * @Param: [unionId, pageSize, pageStart]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2022/3/15 11:40
     */
    List<WjxSurveyResultDO> getUserSurveyListByUnionId(String unionId, Integer pageSize, Integer pageStart);

    /***
     * 功能描述:
     * 根据用户的Em号，0X、Em获取对应的答卷
     * @Param: [emName, pageSize, pageStart]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2022/3/15 11:47
     */
    List<WjxSurveyResultDO> getUserSurveyListByEmName(String emName, Integer pageSize, Integer pageStart);


    /***
     * 功能描述:
     * 获取用户最近几条答卷
     * @Param: [uid, lastCounts]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2022/3/9 15:22
     */
    List<WjxSurveyResultDO> getUserLatestSurvey(Long emUid, Integer lastCounts);

    /***
     * 功能描述:
     * 获取用户最后一条答卷
     * @Param: [emUid]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2022/3/9 15:22
     */
    List<WjxSurveyResultDO> getLastByUid(Integer emUid);

    /***
     * 功能描述:
     * 获取某个参与文件问卷
     * @Param: [joinId]
     * @Return: cn.emoney.pojo.WjxSurveyResultDO
     * @Author: tengdengming
     * @Date: 2022/3/1 15:47
     */
    WjxSurveyResultDO getSurvey(String joinId);

    /***
     * 功能描述:
     * 根据问卷号获取所有答卷
     * @Param: [activity]
     * @Return: java.util.List<cn.emoney.pojo.WjxSurveyResultDO>
     * @Author: tengdengming
     * @Date: 2022/3/9 15:00
     */
    List<WjxSurveyResultDO> getActivitySurveyList(Long activity,Integer pageSize,Integer pageStart);


    /**
     * 功能描述:
     * 根据问卷ID获取用户的答卷
     * @Param: [uid, activityID]
     * @Return: cn.emoney.pojo.WjxSurveyResultDO
     * @Author: tengdengming
     * @Date: 2023/11/27 14:13
     */

    WjxSurveyResultDO getUserSurveyResultByActivityID(Long emUid,Long activity);

    /**
     * 查询用户做过的问卷
     * @param emUid uid
     * @param activityList 检查的id列表
     * @return 做过的问卷id
     */
    List<Long> getUserFinishedSurvey(Long emUid, List<Long> activityList);
}




