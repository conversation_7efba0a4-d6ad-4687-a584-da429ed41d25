package cn.emoney.mapper.activity;


import cn.emoney.pojo.Act588BenifitRecordDO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【Act_588BenifitRecord】的数据库操作Mapper
 * @createDate 2023-05-22 15:37:24
 * @Entity cn.emoney.mapper.activity.domain.Act588benifitrecordMapper
 */
public interface Act588benifitrecordMapper {

    List<Act588BenifitRecordDO> selectByUidAndActCode(String uid, String actCode);

    List<Act588BenifitRecordDO> selectByActCode(Integer pagesize,String actCode);

    int insert(Act588BenifitRecordDO record);

    int insertBatch(List<Act588BenifitRecordDO> record);

    int updateBenefitConfirm(Integer id);

    int updateBenefitReBack(Integer id);
}
