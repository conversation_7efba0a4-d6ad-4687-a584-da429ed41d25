<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.YiyuangouadminuserMapper">
    <resultMap id="BaseResultMap" type="cn.emoney.pojo.YiyuangouadminuserDO">
        <id column="Id" jdbcType="INTEGER" property="id"/>
        <result column="UserName" jdbcType="VARCHAR" property="username"/>
        <result column="Password" jdbcType="VARCHAR" property="password"/>
        <result column="IsValid" jdbcType="INTEGER" property="isvalid"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createtime"/>
        <result column="UpdateTime" jdbcType="TIMESTAMP" property="updatetime"/>
    </resultMap>
    <sql id="Base_Column_List">
        Id,
        User<PERSON><PERSON>,
        "Password",
        IsValid,
        CreateTime,
        UpdateTime
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from YiYuanGouAdminUser
        where Id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from YiYuanGouAdminUser
        where Id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="Id" keyProperty="id"
            parameterType="cn.emoney.pojo.YiyuangouadminuserDO" useGeneratedKeys="true">
        insert into YiYuanGouAdminUser (UserName, "Password", IsValid,
                                        CreateTime, UpdateTime)
        values (#{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{isvalid,jdbcType=INTEGER},
                #{createtime,jdbcType=TIMESTAMP}, #{updatetime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="Id" keyProperty="id"
            parameterType="cn.emoney.pojo.YiyuangouadminuserDO" useGeneratedKeys="true">
        insert into YiYuanGouAdminUser
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="username != null">
                UserName,
            </if>
            <if test="password != null">
                "Password",
            </if>
            <if test="isvalid != null">
                IsValid,
            </if>
            <if test="createtime != null">
                CreateTime,
            </if>
            <if test="updatetime != null">
                UpdateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="username != null">
                #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                #{password,jdbcType=VARCHAR},
            </if>
            <if test="isvalid != null">
                #{isvalid,jdbcType=INTEGER},
            </if>
            <if test="createtime != null">
                #{createtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatetime != null">
                #{updatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="cn.emoney.pojo.YiyuangouadminuserDO">
        update YiYuanGouAdminUser
        <set>
            <if test="username != null">
                UserName = #{username,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                "Password" = #{password,jdbcType=VARCHAR},
            </if>
            <if test="isvalid != null">
                IsValid = #{isvalid,jdbcType=INTEGER},
            </if>
            <if test="createtime != null">
                CreateTime = #{createtime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatetime != null">
                UpdateTime = #{updatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where Id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.emoney.pojo.YiyuangouadminuserDO">
        update YiYuanGouAdminUser
        set UserName   = #{username,jdbcType=VARCHAR},
            "Password" = #{password,jdbcType=VARCHAR},
            IsValid    = #{isvalid,jdbcType=INTEGER},
            CreateTime = #{createtime,jdbcType=TIMESTAMP},
            UpdateTime = #{updatetime,jdbcType=TIMESTAMP}
        where Id = #{id,jdbcType=INTEGER}
    </update>
</mapper>