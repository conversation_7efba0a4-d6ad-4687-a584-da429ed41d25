<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.Act588benifitrecordMapper">

    <resultMap id="BaseResultMap" type="cn.emoney.pojo.Act588BenifitRecordDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="uid" column="uid" jdbcType="VARCHAR"/>
            <result property="uname" column="uname" jdbcType="VARCHAR"/>
            <result property="benefitId" column="benefitId" jdbcType="INTEGER"/>
            <result property="benefitName" column="benefitName" jdbcType="VARCHAR"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="actCode" column="actCode" jdbcType="VARCHAR"/>
            <result property="writeTime" column="writeTime" jdbcType="TIMESTAMP"/>
            <result property="benefitConfirm" column="benefitConfirm" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,uid,uname,benefitId,benefitName,actCode,
        source,writeTime,benefitConfirm
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.emoney.pojo.Act588BenifitRecordDO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into Act_588BenifitRecord ([uid], uname, benefitId, benefitName,source,actCode,
        writeTime,benefitConfirm)
        values (#{uid,jdbcType=VARCHAR},#{uname,jdbcType=VARCHAR}, #{benefitId,jdbcType=INTEGER}, #{benefitName,jdbcType=VARCHAR},#{source,jdbcType=VARCHAR},#{actCode,jdbcType=VARCHAR},
        #{writeTime,jdbcType=TIMESTAMP},#{benefitConfirm,jdbcType=INTEGER})
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        insert into Act_588BenifitRecord ([uid],uname, benefitId, benefitName, source, actCode, writeTime, benefitConfirm)
        values
        <foreach collection="list" item="t" index="index" separator=",">
            (
            #{t.uid,jdbcType=VARCHAR},
            #{t.uname,jdbcType=VARCHAR},
            #{t.benefitId,jdbcType=INTEGER},
            #{t.benefitName,jdbcType=VARCHAR},
            #{t.source,jdbcType=VARCHAR},
            #{t.actCode,jdbcType=VARCHAR},
            #{t.writeTime,jdbcType=TIMESTAMP},
            #{t.benefitConfirm,jdbcType=INTEGER}
            )
        </foreach>
    </insert>


    <select id="selectByUidAndActCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from Act_588BenifitRecord
        where uid = #{uid,jdbcType=VARCHAR} and actCode = #{actCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByActCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select top ${pagesize}
        <include refid="Base_Column_List" />
        from Act_588BenifitRecord with(nolock)
        where actCode = #{actCode,jdbcType=VARCHAR} order by writeTime desc
    </select>

    <update id="updateBenefitConfirm" parameterType="java.lang.Integer">
        update [dbo].[Act_588BenifitRecord]
        set benefitConfirm = '1'
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateBenefitReBack" parameterType="java.lang.Integer">
        update [dbo].[Act_588BenifitRecord]
        set benefitConfirm = '-1'
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>
