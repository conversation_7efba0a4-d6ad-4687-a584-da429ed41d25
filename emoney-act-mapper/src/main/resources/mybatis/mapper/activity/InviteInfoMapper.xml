<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.emoney.mapper.activity.InviteInfoMapper">
    <resultMap id="BaseReusltMap" type="cn.emoney.pojo.InviteInfoDO">
        <id column="Id" property="id" jdbcType="INTEGER"/>
        <result column="UserName" property="userName" jdbcType="VARCHAR"/>
        <result column="EMAccount" property="emAccount" jdbcType="VARCHAR"/>
        <result column="MobileX" property="mobileX" jdbcType="VARCHAR"/>
        <result column="HeadImg" property="headImg" jdbcType="VARCHAR"/>
        <result column="MobileMask" property="mobileMask" jdbcType="VARCHAR"/>
        <result column="Pid" property="pid" jdbcType="VARCHAR"/>
        <result column="BeHeadImg" property="beHeadImg" jdbcType="VARCHAR"/>
        <result column="BeInvited" property="beInvited" jdbcType="VARCHAR"/>
        <result column="BeMobileMask" property="beMobileMask" jdbcType="VARCHAR"/>
        <result column="BuyTime" property="buyTime" jdbcType="TIMESTAMP"/>
        <result column="BuyProduct" property="buyProduct" jdbcType="VARCHAR"/>
        <result column="IsSend" property="isSend" jdbcType="TIMESTAMP"/>
        <result column="PushResult" property="pushResult" jdbcType="VARCHAR"/>
        <result column="JobNum" property="jobNum" jdbcType="VARCHAR"/>
        <result column="Source" property="source" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@sql SELECT -->id, userName, emAccount, mobileX, headImg, mobileMask, pid, beHeadImg, beInvited,
        beMobileMask, buyTime,
        buyProduct, isSend, pushResult, jobNum, source, createTime<!--@sql FROM dbo.InviteInfo -->
    </sql>

    <select id="getAll" resultMap="BaseReusltMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dbo.InviteInfo
    </select>

    <select id="getUnSendUser" resultMap="BaseReusltMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dbo.InviteInfo
        WHERE IsSend is null
    </select>

    <select id="getByBeInvited" resultMap="BaseReusltMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dbo.InviteInfo
        WHERE BeInvited = #{mobile}
    </select>
</mapper>