<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.emoney.mapper.activity.GroupInfoMapper">
    <resultMap id="BaseResultMap" type="cn.emoney.pojo.GroupInfoDO">
        <id column="Id" property="id" jdbcType="INTEGER"/>
        <result column="Teacher" property="teacher" jdbcType="VARCHAR"/>
        <result column="GroupImgUrl" property="groupImgUrl" jdbcType="VARCHAR"/>
        <result column="IsValid" property="isValid" jdbcType="VARCHAR"/>
        <result column="CreateTime" property="createTime" jdbcType="DATE"/>
        <result column="UpdateTime" property="updateTime" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@sql SELECT -->I<PERSON>,Teacher,GroupImgUrl,IsValid,CreateTime,UpdateTime<!--@sql FROM dbo.GroupInfo -->
    </sql>
    <select id="getAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dbo.GroupInfo
    </select>

    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dbo.GroupInfo
        WHERE id = #{id}
    </select>
</mapper>



