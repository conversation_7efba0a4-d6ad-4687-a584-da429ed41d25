<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.emoney.mapper.activity.WjxNoticeSceneMapper">
    <resultMap id="BaseResultMap" type="cn.emoney.pojo.WjxSurveyNoticeDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="scene" column="scene" jdbcType="VARCHAR"/>
            <result property="pushurl" column="pushurl" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
            <result property="isdel" column="isdel" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,pushurl,scene,createtime,isdel
    </sql>

    <select id="getNoticeSceneList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM WJX_Survey_NoticeUrl WITH(NOLOCK)
    </select>
</mapper>
