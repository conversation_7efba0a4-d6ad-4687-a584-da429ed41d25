<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.AugustdrawMapper">
  <resultMap id="BaseResultMap" type="cn.emoney.pojo.AugustdrawDO">
    <id column="Id" jdbcType="INTEGER" property="id" />
    <result column="ActivityId" jdbcType="VARCHAR" property="activityid" />
    <result column="EMAccount" jdbcType="VARCHAR" property="emaccount" />
    <result column="Award" jdbcType="VARCHAR" property="award" />
    <result column="IsGrant" jdbcType="INTEGER" property="isgrant" />
    <result column="CreateTime" jdbcType="DATE" property="createtime" />
    <result column="Remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    Id, ActivityId, EMAccount, Award, IsGrant, CreateTime, Remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from AugustDraw
    where Id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from AugustDraw
    where Id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="Id" keyProperty="id" parameterType="cn.emoney.pojo.AugustdrawDO" useGeneratedKeys="true">
    insert into AugustDraw (ActivityId, EMAccount, Award, 
      IsGrant, CreateTime, Remark
      )
    values (#{activityid,jdbcType=VARCHAR}, #{emaccount,jdbcType=VARCHAR}, #{award,jdbcType=VARCHAR}, 
      #{isgrant,jdbcType=INTEGER}, #{createtime,jdbcType=DATE}, #{remark,jdbcType=VARCHAR}
      )
  </insert>

  <update id="updateByPrimaryKey" parameterType="cn.emoney.pojo.AugustdrawDO">
    update AugustDraw
    set ActivityId = #{activityid,jdbcType=VARCHAR},
      EMAccount = #{emaccount,jdbcType=VARCHAR},
      Award = #{award,jdbcType=VARCHAR},
      IsGrant = #{isgrant,jdbcType=INTEGER},
      CreateTime = #{createtime,jdbcType=DATE},
      Remark = #{remark,jdbcType=VARCHAR}
    where Id = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2021-12-22-->
  <select id="findAllByActivityid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from AugustDraw
    where ActivityId=#{activityid,jdbcType=VARCHAR}
  </select>
</mapper>