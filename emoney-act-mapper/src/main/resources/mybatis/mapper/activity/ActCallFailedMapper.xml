<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.ActCallFailedMapper">

    <resultMap id="BaseResultMap" type="cn.emoney.pojo.ActCallFailedDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="uid" column="uid" jdbcType="VARCHAR"/>
            <result property="actionType" column="actionType" jdbcType="INTEGER"/>
            <result property="traceId" column="traceId" jdbcType="VARCHAR"/>
            <result property="actionContext" column="actionContext" jdbcType="VARCHAR"/>
            <result property="actCode" column="actCode" jdbcType="VARCHAR"/>
            <result property="actionTime" column="actionTime" jdbcType="TIMESTAMP"/>
        <result property="lastModifyTime" column="lastModifyTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,uid,traceId,actionType,actionContext,
        actCode,actionTime,lastModifyTime
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.emoney.pojo.ActCallFailedDO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into Act_Call_Failed ([uid], traceId,actionType,actionContext,
        actCode,actionTime,lastModifyTime)
        values (#{uid,jdbcType=VARCHAR},#{traceId,jdbcType=VARCHAR}, #{actionType,jdbcType=INTEGER}, #{actionContext,jdbcType=VARCHAR},#{actCode,jdbcType=VARCHAR},
        #{actionTime,jdbcType=TIMESTAMP},#{lastModifyTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        insert into Act_Call_Failed ([uid], traceId,actionType,actionContext,
        actCode,actionTime,lastModifyTime)
        values
        <foreach collection="list" item="t" index="index" separator=",">
            (
            #{t.uid,jdbcType=VARCHAR},
            #{t.traceId,jdbcType=VARCHAR},
            #{t.actionType,jdbcType=INTEGER},
            #{t.actionContext,jdbcType=VARCHAR},
            #{t.actCode,jdbcType=VARCHAR},
            #{t.actionTime,jdbcType=TIMESTAMP},
            #{t.lastModifyTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>


    <select id="selectByUidAndActCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from Act_Call_Failed
        where uid = #{uid,jdbcType=VARCHAR} and actCode = #{actCode,jdbcType=VARCHAR}
    </select>


</mapper>
