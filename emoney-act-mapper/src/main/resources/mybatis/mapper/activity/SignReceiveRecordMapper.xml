<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.emoney.mapper.activity.SignReceiveRecordMapper" >
  <resultMap id="BaseResultMap" type="cn.emoney.pojo.SignReceiveRecord" >
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="uid" property="uid" jdbcType="NVARCHAR" />
    <result column="account" property="account" jdbcType="NVARCHAR" />
    <result column="mobile" property="mobile" jdbcType="NVARCHAR" />
    <result column="year" property="year" jdbcType="INTEGER" />
    <result column="week" property="week" jdbcType="INTEGER" />
    <result column="activity_id" property="activityId" jdbcType="INTEGER" />
    <result column="receive_time" property="receiveTime" jdbcType="TIMESTAMP" />
    <result column="receive_time_zone" property="receiveTimeZone" jdbcType="NVARCHAR" />
    <result column="use_age" property="useAge" jdbcType="INTEGER" />
    <result column="product_id" property="productId" jdbcType="NVARCHAR" />
    <result column="order_id" property="orderId" jdbcType="NVARCHAR" />
    <result column="det_id" property="detId" jdbcType="NVARCHAR" />
    <result column="logistic_code" property="logisticCode" jdbcType="NVARCHAR" />
    <result column="remark" property="remark" jdbcType="NVARCHAR" />
  </resultMap>

  <resultMap id="receiveRecordResultMap" type="cn.emoney.pojo.vo.SignReceiveRecordVO" >
    <result column="year" property="year" jdbcType="INTEGER" />
    <result column="week" property="week" jdbcType="INTEGER" />
    <result column="receive_time" property="receiveTime" jdbcType="TIMESTAMP" />
    <result column="receive_time_zone" property="receiveTimeZone" jdbcType="NVARCHAR" />
    <result column="use_age" property="useAge" jdbcType="INTEGER" />
    <result column="product_id" property="productId" jdbcType="NVARCHAR" />
  </resultMap>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.emoney.pojo.SignReceiveRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sign_receive_record ([uid], account, mobile,
    [year],week,activity_id,receive_time,receive_time_zone,use_age,product_id,
    order_id,det_id,logistic_code,remark)
    values (#{uid,jdbcType=NVARCHAR}, #{account,jdbcType=NVARCHAR}, #{mobile,jdbcType=NVARCHAR},
    #{year,jdbcType=INTEGER},#{week,jdbcType=INTEGER},#{activityId,jdbcType=INTEGER},#{receiveTime,jdbcType=TIMESTAMP},
     #{receiveTimeZone,jdbcType=NVARCHAR},#{useAge,jdbcType=INTEGER},#{productId,jdbcType=NVARCHAR},#{orderId,jdbcType=NVARCHAR},
    #{detId,jdbcType=NVARCHAR},#{logisticCode,jdbcType=NVARCHAR},#{remark,jdbcType=NVARCHAR})
  </insert>

  <select id="findAllByUId" resultMap="receiveRecordResultMap" parameterType="java.lang.String">
    select [year],
            week,
            receive_time,
            receive_time_zone,
            use_age,
            product_id
    from sign_receive_record
    where uid=#{uid,jdbcType=NVARCHAR} order by receive_time desc
  </select>
</mapper>