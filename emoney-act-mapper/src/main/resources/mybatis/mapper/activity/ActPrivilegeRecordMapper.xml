<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.ActPrivilegeRecordMapper">

    <resultMap id="BaseResultMap" type="cn.emoney.pojo.Act_PrivilegeRecordDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="uid" column="uid" jdbcType="VARCHAR"/>
            <result property="actCode" column="actCode" jdbcType="VARCHAR"/>
            <result property="validDays" column="validDays" jdbcType="INTEGER"/>
            <result property="source" column="source" jdbcType="INTEGER"/>
            <result property="startTime" column="startTime" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,[uid],actCode,startTime,validDays,
        [source],createTime
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.emoney.pojo.Act_PrivilegeRecordDO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into Act_PrivilegeRecord (uid, actCode,validDays,source,
        startTime,createTime)
        values (#{uid,jdbcType=VARCHAR},#{actCode,jdbcType=VARCHAR}, #{validDays,jdbcType=INTEGER}, #{source,jdbcType=INTEGER},#{startTime,jdbcType=TIMESTAMP},
        #{createTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        insert into Act_PrivilegeRecord ([uid], actCode,validDays,source,
        startTime,createTime)
        values
        <foreach collection="list" item="t" index="index" separator=",">
            (
            #{t.uid,jdbcType=VARCHAR},
            #{t.actCode,jdbcType=VARCHAR},
            #{t.validDays,jdbcType=INTEGER},
            #{t.source,jdbcType=INTEGER},
            #{t.startTime,jdbcType=TIMESTAMP},
            #{t.createTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>


    <select id="selectByUidAndActCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from Act_PrivilegeRecord
        where [uid] = #{uid,jdbcType=VARCHAR} and actCode = #{actCode,jdbcType=VARCHAR} order by createTime desc
    </select>


</mapper>
