<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.ActPrivilegeConfigMapper">

    <resultMap id="BaseResultMap" type="cn.emoney.pojo.Act_PrivilegeConfigDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="privilegeName" column="privilegeName" jdbcType="VARCHAR"/>
            <result property="validDays" column="validDays" jdbcType="INTEGER"/>
            <result property="actCode" column="actCode" jdbcType="VARCHAR"/>
            <result property="isShow" column="isShow" jdbcType="BOOLEAN"/>
            <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,privilegeName,validDays,actCode,isShow
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.emoney.pojo.Act_PrivilegeConfigDO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into Act_PrivilegeConfig (privilegeName,actCode,isShow,
        createTime,validDays)
        values (#{privilegeName,jdbcType=VARCHAR},#{actCode,jdbcType=VARCHAR}, #{isShow,jdbcType=BOOLEAN},#{createTime,jdbcType=TIMESTAMP},#{validDays,jdbcType=INTEGER})
    </insert>

    <select id="selectByActCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from Act_PrivilegeConfig
        where actCode = #{actCode,jdbcType=VARCHAR}
    </select>


</mapper>
