<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.emoney.mapper.activity.WjxSurveyResultMapper">
    <resultMap id="BaseResultMap" type="cn.emoney.pojo.WjxSurveyResultDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="activity" column="activity" jdbcType="BIGINT"/>
            <result property="joinid" column="joinid" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="ipaddress" column="ipaddress" jdbcType="VARCHAR"/>
            <result property="source" column="source" jdbcType="VARCHAR"/>
            <result property="thirdusername" column="thirdusername" jdbcType="VARCHAR"/>
            <result property="nickname" column="nickname" jdbcType="VARCHAR"/>
            <result property="sojumpparam" column="sojumpparam" jdbcType="VARCHAR"/>
            <result property="realname" column="realname" jdbcType="VARCHAR"/>
            <result property="reldept" column="reldept" jdbcType="VARCHAR"/>
            <result property="relext" column="relext" jdbcType="VARCHAR"/>
            <result property="province" column="province" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
            <result property="index" column="index" jdbcType="INTEGER"/>
            <result property="timetaken" column="timetaken" jdbcType="INTEGER"/>
            <result property="totalvalue" column="totalvalue" jdbcType="INTEGER"/>
            <result property="answer" column="answer" jdbcType="VARCHAR"/>
            <result property="sign" column="sign" jdbcType="VARCHAR"/>
            <result property="emuid" column="emuid" jdbcType="INTEGER"/>
            <result property="emno" column="emno" jdbcType="VARCHAR"/>
            <result property="pid" column="pid" jdbcType="VARCHAR"/>
            <result property="emscene" column="emscene" jdbcType="VARCHAR"/>
            <result property="ext1" column="ext1" jdbcType="VARCHAR"/>
            <result property="ext2" column="ext2" jdbcType="VARCHAR"/>
            <result property="ext3" column="ext3" jdbcType="VARCHAR"/>
            <result property="submittime" column="submittime" jdbcType="TIMESTAMP"/>
            <result property="receivetime" column="receivetime" jdbcType="TIMESTAMP"/>
            <result property="writetime" column="writetime" jdbcType="TIMESTAMP"/>
            <result property="isdel" column="isdel" jdbcType="BIT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,activity,joinid,
        name,ipaddress,source,
        thirdusername,nickname,sojumpparam,
        realname,reldept,relext,
        province,city,index,
        timetaken,totalvalue,answer,sign,
        emuid,emno,unionid,emscene,ext1,ext2,ext3,
        submittime,receivetime,writetime,
        isdel
    </sql>

    <insert id="insert" keyColumn="Base_Column_List" keyProperty="id" parameterType="cn.emoney.pojo.WjxSurveyResultDO" useGeneratedKeys="true">

        INSERT INTO [dbo].[WJX_Survey_Result]
        ([activity]
        ,[joinid]
        ,[name]
        ,[ipaddress]
        ,[source]
        ,[thirdusername]
        ,[nickname]
        ,[sojumpparam]
        ,[realname]
        ,[reldept]
        ,[relext]
        ,[province]
        ,[city]
        ,[index]
        ,[timetaken]
        ,[totalvalue]
        ,[answer]
        ,[sign]
        ,[emuid]
        ,[emno]
        ,[pid]
        ,[unionid]
        ,[emscene]
        ,[ext1]
        ,[ext2]
        ,[ext3]
        ,[submittime]
        ,[receivetime])
        values
        (#{activity,jdbcType=BIGINT},
        #{joinid, jdbcType=VARCHAR},
        #{name, jdbcType=VARCHAR},
        #{ipaddress, jdbcType=VARCHAR},
        #{source, jdbcType=VARCHAR},
        #{thirdusername, jdbcType=VARCHAR},
        #{nickname,jdbcType=VARCHAR},
        #{sojumpparam, jdbcType=VARCHAR},
        #{realname, jdbcType=VARCHAR},
        #{reldept, jdbcType=VARCHAR},
        #{relext,jdbcType=VARCHAR},
        #{province, jdbcType=VARCHAR},
        #{city, jdbcType=VARCHAR},
        #{index, jdbcType=INTEGER},
        #{timetaken, jdbcType=INTEGER},
        #{totalvalue, jdbcType=INTEGER},
        #{answer, jdbcType=VARCHAR},
        #{sign, jdbcType=VARCHAR},
        #{emuid, jdbcType=INTEGER},
        #{emno, jdbcType=VARCHAR},
        #{pid, jdbcType=VARCHAR},
        #{unionid,jdbcType=VARCHAR},
        #{emscene, jdbcType=VARCHAR},
        #{ext1, jdbcType=VARCHAR},
        #{ext2, jdbcType=VARCHAR},
        #{ext3, jdbcType=VARCHAR},
        #{submittime, jdbcType=TIMESTAMP},
        #{receivetime, jdbcType=TIMESTAMP}
        )
    </insert>

    <select id="getLastByUid" resultMap="BaseResultMap">
        SELECT TOP 1
        <include refid="Base_Column_List"/>
        FROM WJX_Survey_Result WITH(NOLOCK)
        WHERE [emuid]=${emUid}
        ORDER BY submittime DESC
    </select>

    <select id="getSurveyList" resultMap="BaseResultMap" parameterType="Map">
        SELECT top ${pagesize} *
        FROM WJX_Survey_Result WITH(NOLOCK)
        WHERE
            id NOT IN (
                SELECT TOP ${pagestart} id FROM WJX_Survey_Result  WITH(NOLOCK)
            )
        ORDER BY submittime DESC
    </select>

    <select id="getUserSurveyList" resultMap="BaseResultMap" parameterType="Map">
        SELECT TOP ${pagesize} *
        FROM WJX_Survey_Result WITH(NOLOCK)
        WHERE
            id NOT IN (
                SELECT TOP ${pagestart} id FROM WJX_Survey_Result  WITH(NOLOCK)
                WHERE [emuid]=${emUid}
            )
            AND [emuid]=${emUid}
        ORDER BY submittime DESC
    </select>

    <select id="getUserSurveyListByUnionId" resultMap="BaseResultMap" parameterType="Map">
        SELECT TOP ${pagesize} *
        FROM WJX_Survey_Result WITH(NOLOCK)
        WHERE
            id NOT IN (
                SELECT TOP ${pagestart} id FROM WJX_Survey_Result  WITH(NOLOCK)
                WHERE [unionid]=${unionId}
            )
            AND [unionid]=${unionId}
        ORDER BY submittime DESC
    </select>

    <select id="getUserSurveyListByEmName" resultMap="BaseResultMap" parameterType="Map">
        SELECT TOP ${pagesize} *
        FROM WJX_Survey_Result WITH(NOLOCK)
        WHERE
            id NOT IN (
                SELECT TOP ${pagestart} id FROM WJX_Survey_Result  WITH(NOLOCK)
                WHERE [emno]=${emName}
            )
            AND [emno]=${emName}
        ORDER BY submittime DESC
    </select>


    <select id="getUserLatestSurvey" resultMap="BaseResultMap" parameterType="Map">
        SELECT TOP ${lastCounts} *
        FROM WJX_Survey_Result WITH(NOLOCK)
        WHERE [emuid]=${emUid}
        ORDER BY submittime DESC
    </select>

    <select id="getSurvey" resultMap="BaseResultMap" parameterType="Map">
        SELECT TOP 1 *
        FROM WJX_Survey_Result WITH(NOLOCK)
        WHERE joinId = ${joinId}
    </select>

    <select id="getUserSurveyResultByActivityID" resultMap="BaseResultMap" parameterType="Map">
        SELECT TOP 1 *
        FROM WJX_Survey_Result WITH(NOLOCK)
        WHERE [emuid]=${emUid} AND activity=${activity}
    </select>

    <select id="getUserFinishedSurvey" resultType="java.lang.Long">
        SELECT DISTINCT activity
        FROM WJX_Survey_Result WITH(NOLOCK)
        WHERE [emuid]=${emUid}
        <if test="activityList != null and !activityList.isEmpty()">
            AND activity IN
                <foreach collection="activityList" separator="," open="(" close=")" item="activity">
                    #{activity}
                </foreach>
        </if>
    </select>

    <select id="getActivitySurveyList" resultMap="BaseResultMap" parameterType="Map">
        SELECT TOP ${pageSize} *
        FROM WJX_Survey_Result WITH(NOLOCK)
        WHERE
            id NOT IN (
                SELECT TOP ${pageStart} id FROM WJX_Survey_Result WITH(NOLOCK)
                WHERE activity = ${activity}
            )
            AND activity = ${activity}
        ORDER BY submittime DESC
    </select>

</mapper>
