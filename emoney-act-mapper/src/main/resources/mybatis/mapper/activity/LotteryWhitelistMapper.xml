<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.LotteryWhitelistMapper">

    <resultMap id="BaseResultMap" type="cn.emoney.pojo.LotteryWhitelistDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="batchno" column="batchno" jdbcType="VARCHAR"/>
            <result property="uid" column="uid" jdbcType="VARCHAR"/>
            <result property="group" column="group" jdbcType="VARCHAR"/>
            <result property="writetime" column="writetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,batchno,uid,
        [group],writetime
    </sql>

    <select id="getWhiteListByBatchNo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM [dbo].[Lottery_WhiteList] WITH(NOLOCK)
        WHERE batchno = ${batchno}
    </select>

    <select id="checkInWhiteList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM [dbo].[Lottery_WhiteList] WITH(NOLOCK)
        WHERE batchno = ${batchno} AND uid=${uid}
    </select>

</mapper>
