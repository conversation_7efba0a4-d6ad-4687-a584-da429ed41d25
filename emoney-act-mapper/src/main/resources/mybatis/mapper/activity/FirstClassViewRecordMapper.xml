<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.FirstClassViewRecordMapper">
  <resultMap id="BaseResultMap" type="cn.emoney.pojo.FirstClassViewRecordDO">
    <!--@mbg.generated-->
    <!--@Table Act_FirstClassViewRecord-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="uid" jdbcType="VARCHAR" property="uid" />
    <result column="classid" jdbcType="INTEGER" property="classid" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, [uid], classid, platform, createtime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from Act_FirstClassViewRecord
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from Act_FirstClassViewRecord
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.emoney.pojo.FirstClassViewRecordDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into Act_FirstClassViewRecord ([uid], classid, platform, 
      createtime)
    values (#{uid,jdbcType=VARCHAR}, #{classid,jdbcType=INTEGER}, #{platform,jdbcType=VARCHAR}, 
      #{createtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.emoney.pojo.FirstClassViewRecordDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into Act_FirstClassViewRecord
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="uid != null">
        [uid],
      </if>
      <if test="classid != null">
        classid,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="createtime != null">
        createtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="uid != null">
        #{uid,jdbcType=VARCHAR},
      </if>
      <if test="classid != null">
        #{classid,jdbcType=INTEGER},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <select id="selectByClassIdAndUid" parameterType="cn.emoney.pojo.FirstClassViewRecordDO" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from Act_FirstClassViewRecord
    where uid = #{uid,jdbcType=VARCHAR} and classid = #{classid,jdbcType=INTEGER}
  </select>
</mapper>