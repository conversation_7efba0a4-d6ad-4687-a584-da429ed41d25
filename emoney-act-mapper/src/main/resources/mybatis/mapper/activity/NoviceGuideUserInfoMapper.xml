<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.NoviceGuideUserInfoMapper">
  <resultMap id="BaseResultMap" type="cn.emoney.pojo.NoviceGuideUserInfoDO">
    <!--@mbg.generated-->
    <!--@Table NoviceGuideUserInfo-->
    <id column="Id" jdbcType="INTEGER" property="id" />
    <result column="Uid" jdbcType="VARCHAR" property="uid" />
    <result column="UserCode" jdbcType="VARCHAR" property="usercode" />
    <result column="QrCode" jdbcType="VARCHAR" property="qrcode" />
    <result column="CreateTime" jdbcType="TIMESTAMP" property="createtime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    Id, [Uid], UserCode, QrCode, CreateTime
  </sql>
  <insert id="insert" keyColumn="Id" keyProperty="id" parameterType="cn.emoney.pojo.NoviceGuideUserInfoDO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into NoviceGuideUserInfo ([Uid], UserCode, QrCode, 
      CreateTime)
    values (#{uid,jdbcType=VARCHAR}, #{usercode,jdbcType=VARCHAR}, #{qrcode,jdbcType=VARCHAR}, 
      #{createtime,jdbcType=TIMESTAMP})
  </insert>

<!--auto generated by MybatisCodeHelper on 2022-01-06-->
  <select id="queryByUid" resultMap="BaseResultMap">
    select TOP 1
    <include refid="Base_Column_List"/>
    from NoviceGuideUserInfo
    where [Uid]=#{uid,jdbcType=VARCHAR}
  </select>


</mapper>