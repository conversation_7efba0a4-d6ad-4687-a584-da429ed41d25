<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.LotteryDetailMapper">

    <resultMap id="BaseResultMap" type="cn.emoney.pojo.LotteryDetailDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="mobile0x" column="mobile0x" jdbcType="VARCHAR"/>
            <result property="uid" column="uid" jdbcType="VARCHAR"/>
            <result property="em" column="em" jdbcType="VARCHAR"/>
            <result property="mobilemask" column="mobilemask" jdbcType="VARCHAR"/>
            <result property="lotterytarget" column="lotterytarget" jdbcType="VARCHAR"/>
            <result property="lotterytime" column="lotterytime" jdbcType="TIMESTAMP"/>
            <result property="prizename" column="prizename" jdbcType="VARCHAR"/>
            <result property="prizecode" column="prizecode" jdbcType="VARCHAR"/>
            <result property="prizeconfirm" column="prizeconfirm" jdbcType="VARCHAR"/>
            <result property="batchno" column="batchno" jdbcType="VARCHAR"/>
            <result property="group" column="group" jdbcType="VARCHAR"/>
            <result property="level" column="level" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,mobile0x,uid,
        em,mobilemask,lotterytarget,
        lotterytime,prizename,prizecode,
        prizeconfirm,batchno,[group],[level]
    </sql>

    <sql id="Stat_Column_List">
        uid,em,mobile0x,mobilemask,[level],[group],detid,lotterytime,datestr
    </sql>

    <insert id="insert" keyColumn="Base_Column_List" keyProperty="id" parameterType="cn.emoney.pojo.WjxSurveyResultDO" useGeneratedKeys="true">
        insert into [dbo].[Lottery_Detail]
        ([mobile0x]
        ,[uid]
        ,[em]
        ,[mobilemask]
        ,[lotterytarget]
        ,[prizename]
        ,[prizecode]
        ,[prizeconfirm]
        ,[batchno]
        ,[group]
        ,[level])
        VALUES
        (
        #{mobile0x,jdbcType=VARCHAR},
        #{uid,jdbcType=VARCHAR},
        #{em,jdbcType=VARCHAR},
        #{mobilemask,jdbcType=VARCHAR},
        #{lotterytarget,jdbcType=VARCHAR},
        #{prizename,jdbcType=VARCHAR},
        #{prizecode,jdbcType=VARCHAR},
        #{prizeconfirm,jdbcType=VARCHAR},
        #{batchno,jdbcType=VARCHAR},
        #{group,jdbcType=VARCHAR},
        #{level,jdbcType=INTEGER}
        )
    </insert>

    <update id="updatePrizeConfirm" parameterType="java.lang.Integer">
        update [dbo].[Lottery_Detail]
        set prizeconfirm = '1'
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="getUserLotteryInfoListByUid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM [dbo].[Lottery_Detail]
        WHERE [uid]=${uid} AND [batchno]=${batchno}
        ORDER BY lotterytime DESC
    </select>

    <select id="getUserLotteryInfoListByMobile" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM [dbo].[Lottery_Detail]
        WHERE [mobile0x]=${mobile0x}
        ORDER BY lotterytime DESC
    </select>

    <select id="getRecentlyLotteryDetailList" resultMap="BaseResultMap">
        SELECT TOP 20
        <include refid="Base_Column_List"/>
        FROM [dbo].[Lottery_Detail] WITH (NOLOCK)
        WHERE [batchno]=${batchno}
        <![CDATA[
        AND [lotterytime] >= DATEADD(day, -10, GETDATE())
        ]]>
        ORDER BY [level]
    </select>

    <select id="getLatestLotteryDetailList" resultMap="BaseResultMap">
        SELECT TOP 20
        <include refid="Base_Column_List"/>
        FROM [dbo].[Lottery_Detail] WITH (NOLOCK)
        WHERE [batchno]=${batchno}
        ORDER BY [lotterytime] DESC
    </select>

    <select id="getLotteryDetailsFromDate" resultMap="BaseResultMap">
        <![CDATA[
       	SELECT a.[uid],a.[em],a.[mobile0x],a.mobilemask,a.[level],isnull(b.[group],'') AS [group],
        a.lotterytarget AS lotterytarget,a.lotterytime,CONVERT(varchar(10),a.lotterytime,111) AS dateStr  FROM
        (
        SELECT * FROM dbo.Lottery_Detail WITH(nolock) WHERE  lotterytime>='${fromdate}' AND batchno = ${batchno}
        ) AS a
        left JOIN dbo.Lottery_WhiteList AS b
        ON a.[uid] = b.[uid]
        ORDER BY a.lotterytime DESC
        ]]>
    </select>



</mapper>
