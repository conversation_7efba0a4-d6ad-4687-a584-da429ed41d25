<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.emoney.mapper.activity.InviteAwardDetailMapper">
    <resultMap id="BaseReusltMap" type="cn.emoney.pojo.InviteAwardDetailDO">
        <id column="Id" property="id" jdbcType="INTEGER"/>
        <result column="MobileX" property="mobileX" jdbcType="VARCHAR"/>
        <result column="MobileVer" property="mobileVer" jdbcType="VARCHAR"/>
        <result column="OrderMobileX" property="orderMobileX" jdbcType="VARCHAR"/>
        <result column="OrderProduct" property="orderProduct" jdbcType="VARCHAR"/>
        <result column="OrderId" property="orderId" jdbcType="VARCHAR"/>
        <result column="DetID" property="detId" jdbcType="VARCHAR"/>
        <result column="Award" property="award" jdbcType="VARCHAR"/>
        <result column="SendType" property="sendType" jdbcType="VARCHAR"/>
        <result column="Status" property="status" jdbcType="INTEGER"/>
        <result column="CreateTime" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@sql SELECT -->Id, MobileX, MobileVer, OrderMobileX, OrderProduct, OrderId, DetID, Award, SendType, Status,
        CreateTime<!--@sql FROM dbo.InviteAwardDetail -->
    </sql>

    <select id="getAll" resultMap="BaseReusltMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dbo.InviteAwardDetail
    </select>

    <select id="getUnSendUser" resultMap="BaseReusltMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dbo.InviteAwardDetail
        WHERE Award = ''
    </select>

    <select id="getByMobileXAndOrderMobile" resultMap="BaseReusltMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM dbo.InviteAwardDetail
        WHERE SendType IS NOT NULL
          AND MobileX = #{mobileX}
          AND OrderMobileX = #{orderMobileX}
    </select>
    <select id="getByOrderId" resultMap="BaseReusltMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM InviteAwardDetail
        WHERE OrderId = #{orderId} AND
    </select>
</mapper>