<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.emoney.mapper.activity.SignRecordMapper" >
  <resultMap id="BaseResultMap" type="cn.emoney.pojo.SignRecord" >
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="uid" property="uid" jdbcType="NVARCHAR" />
    <result column="account" property="account" jdbcType="NVARCHAR" />
    <result column="mobile" property="mobile" jdbcType="NVARCHAR" />
    <result column="year" property="year" jdbcType="INTEGER" />
    <result column="week" property="week" jdbcType="INTEGER" />
    <result column="activity_id" property="activityId" jdbcType="INTEGER" />
    <result column="sign_time" property="signTime" jdbcType="TIMESTAMP" />
    <result column="sign_time_zone" property="signTimeZone" jdbcType="NVARCHAR" />
  </resultMap>

  <resultMap id="threeSignNumRecordMap" type="cn.emoney.pojo.vo.NoReceiveRecordVO">
    <result column="uid" property="uid" jdbcType="NVARCHAR" />
    <result column="account" property="account" jdbcType="NVARCHAR" />
    <result column="mobile" property="mobile" jdbcType="NVARCHAR" />
    <result column="activity_id" property="activityId" jdbcType="INTEGER" />
    <result column="sign_num" property="signNum" jdbcType="INTEGER" />
  </resultMap>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.emoney.pojo.SignRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sign_record ([uid], account, mobile,
    [year],week,activity_id,sign_time,sign_time_zone)
    values (#{uid,jdbcType=NVARCHAR}, #{account,jdbcType=NVARCHAR}, #{mobile,jdbcType=NVARCHAR},
    #{year,jdbcType=INTEGER},#{week,jdbcType=INTEGER},#{activityId,jdbcType=INTEGER},#{signTime,jdbcType=TIMESTAMP},
    #{signTimeZone,jdbcType=NVARCHAR})
  </insert>

  <select id="selectOneByUId" parameterType="java.lang.String" resultMap="BaseResultMap">
      SELECT top 1
        id,
        uid,
        account,
        mobile,
        [year],
        week,
        activity_id,
        sign_time,
        sign_time_zone
      FROM [dbo].[sign_record] WITH(NOLOCK)
      WHERE uid = #{uid}
      ORDER BY sign_time
  </select>

  <select id="selectAllUId" resultType="java.lang.String">
      SELECT
        distinct uid
      FROM [dbo].[sign_record] WITH(NOLOCK)
  </select>

  <select id="selectNoReceiveRecordList" parameterType="java.lang.String" resultMap="threeSignNumRecordMap">
    SELECT t.* FROM (SELECT [uid]
    ,[mobile]
    ,account
    ,activity_id
    ,count(1) AS sign_num
    FROM [dbo].[sign_record] with(nolock)
    where sign_time_zone = #{timeZone}
    group by [uid],mobile,account,activity_id) t where t.sign_num = 3
    and t.uid not in (select uid from [dbo].[sign_receive_record] with(nolock)
    where receive_time_zone = #{timeZone});
  </select>
</mapper>