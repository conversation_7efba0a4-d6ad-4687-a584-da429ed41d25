<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.emoney.mapper.activity.SignFeeCycleMapper" >
    <resultMap id="BaseResultMap" type="cn.emoney.pojo.SignFeeCycle" >
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="activity_id" property="activityId" jdbcType="INTEGER" />
        <result column="uid" property="uid" jdbcType="NVARCHAR" />
        <result column="account" property="account" jdbcType="NVARCHAR" />
        <result column="mobile" property="mobile" jdbcType="NVARCHAR" />
        <result column="activity_code" property="activityCode" jdbcType="NVARCHAR" />
        <result column="fee_begin_time" property="feeBeginTime" jdbcType="TIMESTAMP" />
        <result column="fee_end_time" property="feeEndTime" jdbcType="TIMESTAMP" />
        <result column="fee_week" property="feeWeek" jdbcType="INTEGER" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="is_valid" property="isValid" jdbcType="INTEGER" />
    </resultMap>

    <resultMap id="feeCycleResultMap" type="cn.emoney.pojo.vo.SignFeeCycleVO" >
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="activity_id" property="activityId" jdbcType="INTEGER" />
        <result column="activity_code" property="activityCode" jdbcType="NVARCHAR" />
        <result column="fee_begin_time" property="feeBeginTime" jdbcType="TIMESTAMP" />
        <result column="fee_end_time" property="feeEndTime" jdbcType="TIMESTAMP" />
        <result column="fee_week" property="feeWeek" jdbcType="INTEGER" />
    </resultMap>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.emoney.pojo.SignFeeCycle" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into [dbo].[sign_fee_cycle](activity_id,[uid], account, mobile,
        activity_code,fee_begin_time,fee_end_time,fee_week,create_time,update_time,is_valid)
        values (#{activityId,jdbcType=INTEGER},#{uid,jdbcType=NVARCHAR}, #{account,jdbcType=NVARCHAR}, #{mobile,jdbcType=NVARCHAR},
        #{activityCode,jdbcType=NVARCHAR},#{feeBeginTime,jdbcType=TIMESTAMP},#{feeEndTime,jdbcType=TIMESTAMP},#{feeWeek,jdbcType=INTEGER},
        #{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP},#{isValid,jdbcType=INTEGER})
    </insert>

    <select id="findLatestFeeCycleByUId" resultMap="feeCycleResultMap" parameterType="java.lang.String">
    select  top 1
            id,
            activity_id,
            activity_code,
            fee_begin_time,
            fee_end_time,
            fee_week
    from [dbo].[sign_fee_cycle]
    where uid=#{uid,jdbcType=NVARCHAR} AND is_valid = 1 order by fee_begin_time desc
  </select>

    <select id="selectFeeCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(1) from [dbo].[sign_fee_cycle]
        where uid=#{paramsMap.uid} and activity_id=#{paramsMap.actId} and is_valid = 1
    </select>
</mapper>