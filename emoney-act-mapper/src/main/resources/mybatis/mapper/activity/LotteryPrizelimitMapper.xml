<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.LotteryPrizelimitMapper">

    <resultMap id="BaseResultMap" type="cn.emoney.pojo.LotteryPrizelimitDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="batchno" column="batchno" jdbcType="VARCHAR"/>
            <result property="group" column="group" jdbcType="VARCHAR"/>
            <result property="zonestart" column="zonestart" jdbcType="TIMESTAMP"/>
            <result property="zoneend" column="zoneend" jdbcType="TIMESTAMP"/>
            <result property="prizelimit" column="prizelimit" jdbcType="INTEGER"/>
            <result property="prizesend" column="prizesend" jdbcType="INTEGER"/>
            <result property="writetime" column="writetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,batchno,[group],
        zonestart,zoneend,prizelimit,
        prizesend,writetime
    </sql>

    <select id="getLotteryPrizeLimitByBatchNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM [dbo].[Lottery_PrizeLimit]
        WHERE
        batchno = '${batchno}'
        AND [group] = '${group}'
        AND [level] = ${level}
        <![CDATA[
        AND zonestart <= '${lotterytime}'
        AND zoneend >= '${lotterytime}'
        ]]>

    </select>

    <update id="increasePrizeSend" >
        UPDATE [dbo].[Lottery_PrizeLimit]
        SET
        prizesend = prizesend + 1
        WHERE
        batchno = '${batchno}'
        AND [group] = '${group}'
        AND [level] = ${level}
        <![CDATA[
        AND zonestart <= '${lotterytime}'
        AND zoneend >= '${lotterytime}'
        ]]>
    </update>


</mapper>
