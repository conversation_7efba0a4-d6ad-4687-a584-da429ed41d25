<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.ActTaskConfMapper">

    <resultMap id="BaseResultMap" type="cn.emoney.pojo.ActTaskConfDO">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="taskType" column="taskType" jdbcType="INTEGER"/>
            <result property="context" column="context" jdbcType="VARCHAR"/>
            <result property="actCode" column="actCode" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,taskType,context,actCode
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.emoney.pojo.ActTaskConfDO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into Act_TaskConf (id,taskType,context,actCode)
        values (#{id,jdbcType=INTEGER},#{taskType,jdbcType=INTEGER}, #{context,jdbcType=VARCHAR}, #{actCode,jdbcType=VARCHAR})
    </insert>

    <select id="selectByActCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from Act_TaskConf
        where actCode = #{actCode,jdbcType=VARCHAR}
    </select>


</mapper>
