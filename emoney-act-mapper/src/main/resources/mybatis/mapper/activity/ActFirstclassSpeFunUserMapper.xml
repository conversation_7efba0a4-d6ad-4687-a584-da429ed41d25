<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.activity.ActFirstclassSpeFunUserMapper">

    <resultMap id="BaseResultMap" type="cn.emoney.pojo.FirstClassSpeFunUserDO">
            <id property="id" column="Id" jdbcType="INTEGER"/>
            <result property="em_username" column="em_username" jdbcType="VARCHAR"/>
            <result property="parentid" column="parentid" jdbcType="VARCHAR"/>
            <result property="core_use_days" column="core_use_days" jdbcType="INTEGER"/>
            <result property="query_date" column="query_date" jdbcType="VARCHAR"/>
            <result property="createtime" column="createtime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        Id,em_username,parentid,
        core_use_days,query_date
    </sql>

    <select id="selectByParentid" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from Act_FirstClassSpeFunUser
        where parentid = #{uid,jdbcType=VARCHAR}
    </select>
</mapper>
