<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.pay.PayOrderMapper">
    <resultMap id="BaseResultMap" type="cn.emoney.pojo.PayOrderDO">
        <!--@mbg.generated-->
        <!--@Table Pay_Order-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="OrderID" jdbcType="VARCHAR" property="orderid"/>
        <result column="CustomerName" jdbcType="VARCHAR" property="customername"/>
        <result column="MidPwd" jdbcType="VARBINARY" property="midpwd"/>
        <result column="Mid" jdbcType="VARCHAR" property="mid"/>
        <result column="EmCode" jdbcType="VARCHAR" property="emcode"/>
        <result column="OrderPrice" jdbcType="DECIMAL" property="orderprice"/>
        <result column="OrderStatus" jdbcType="INTEGER" property="orderstatus"/>
        <result column="CreateTime" jdbcType="TIMESTAMP" property="createtime"/>
        <result column="UpdateTime" jdbcType="TIMESTAMP" property="updatetime"/>
        <result column="IsNeedInvoice" jdbcType="BIT" property="isneedinvoice"/>
        <result column="InvoiceType" jdbcType="VARCHAR" property="invoicetype"/>
        <result column="InvoiceTitle" jdbcType="VARCHAR" property="invoicetitle"/>
        <result column="Note" jdbcType="VARCHAR" property="note"/>
        <result column="OrderSource" jdbcType="VARCHAR" property="ordersource"/>
        <result column="TsrCode" jdbcType="VARCHAR" property="tsrcode"/>
        <result column="ExtendedSource" jdbcType="VARCHAR" property="extendedsource"/>
        <result column="DealerCode" jdbcType="VARCHAR" property="dealercode"/>
        <result column="ReceiveName" jdbcType="VARCHAR" property="receivename"/>
        <result column="Province" jdbcType="VARCHAR" property="province"/>
        <result column="ProvinceCode" jdbcType="VARCHAR" property="provincecode"/>
        <result column="City" jdbcType="VARCHAR" property="city"/>
        <result column="CityCode" jdbcType="VARCHAR" property="citycode"/>
        <result column="Area" jdbcType="VARCHAR" property="area"/>
        <result column="AreaCode" jdbcType="VARCHAR" property="areacode"/>
        <result column="ZipCode" jdbcType="VARCHAR" property="zipcode"/>
        <result column="Address" jdbcType="VARCHAR" property="address"/>
        <result column="DeptID" jdbcType="VARCHAR" property="deptid"/>
        <result column="OrderCreateID" jdbcType="INTEGER" property="ordercreateid"/>
        <result column="OrderCreateMsg" jdbcType="VARCHAR" property="ordercreatemsg"/>
        <result column="QQ" jdbcType="VARCHAR" property="qq"/>
        <result column="ChannelCode" jdbcType="VARCHAR" property="channelcode"/>
        <result column="RequestUrl" jdbcType="VARCHAR" property="requesturl"/>
        <result column="CouponCode" jdbcType="VARCHAR" property="couponcode"/>
        <result column="IsEInvoice" jdbcType="VARCHAR" property="iseinvoice"/>
        <result column="BillCompanyName" jdbcType="VARCHAR" property="billcompanyname"/>
        <result column="BillTaxpayerNo" jdbcType="VARCHAR" property="billtaxpayerno"/>
        <result column="BillCompanyAddress" jdbcType="VARCHAR" property="billcompanyaddress"/>
        <result column="BillCompanyTelephone" jdbcType="VARCHAR" property="billcompanytelephone"/>
        <result column="BillCompanyBankName" jdbcType="VARCHAR" property="billcompanybankname"/>
        <result column="BillCompanyBankAccount" jdbcType="VARCHAR" property="billcompanybankaccount"/>
        <result column="InvioceGroup" jdbcType="VARCHAR" property="inviocegroup"/>
        <result column="WXAccount" jdbcType="VARCHAR" property="wxaccount"/>
        <result column="Pid" jdbcType="VARCHAR" property="pid"/>
        <result column="Tid" jdbcType="VARCHAR" property="tid"/>
        <result column="Sid" jdbcType="VARCHAR" property="sid"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, OrderID, CustomerName, MidPwd, Mid, EmCode, OrderPrice, OrderStatus, CreateTime,
        UpdateTime, IsNeedInvoice, InvoiceType, InvoiceTitle, Note, OrderSource, TsrCode,
        ExtendedSource, DealerCode, ReceiveName, Province, ProvinceCode, City, CityCode,
        Area, AreaCode, ZipCode, Address, DeptID, OrderCreateID, OrderCreateMsg, QQ, ChannelCode,
        RequestUrl, CouponCode, IsEInvoice, BillCompanyName, BillTaxpayerNo, BillCompanyAddress,
        BillCompanyTelephone, BillCompanyBankName, BillCompanyBankAccount, InvioceGroup,
         WXAccount, Pid, Tid, Sid
    </sql>

    <!--auto generated by MybatisCodeHelper on 2022-03-28-->
    <select id="queryByCreateTimeAndOrderStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from Pay_Order
        where CreateTime >= #{startCreateTime,jdbcType=TIMESTAMP}
          and CreateTime <![CDATA[<]]> #{endCreateTime,jdbcType=TIMESTAMP}
          and OrderStatus = #{orderStatus,jdbcType=INTEGER}
          and OrderID like '%xe%'
    </select>
</mapper>