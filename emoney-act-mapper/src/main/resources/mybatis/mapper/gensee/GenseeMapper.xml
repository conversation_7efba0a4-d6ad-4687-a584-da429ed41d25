<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.emoney.mapper.gensee.GenseeMapper" >
    <resultMap id="RecordedResult" type="cn.emoney.pojo.GenseeRecordedBasicDO" >
        <result column="lbId" property="lbId" jdbcType="VARCHAR" />
        <result column="webcastId" property="webcastId" jdbcType="VARCHAR" />
        <result column="attendeeJoinUrl" property="attendeeJoinUrl" jdbcType="NVARCHAR" />
        <result column="description" property="description" jdbcType="LONGVARCHAR" />

        <result column="Subject" property="subject" jdbcType="NVARCHAR" />
        <result column="TeacherName" property="teacherName" jdbcType="NVARCHAR" />
        <result column="StartTime" property="startTime" jdbcType="TIMESTAMP" />
        <result column="EndTime" property="endTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="LiveResult" type="cn.emoney.pojo.GenseeLiveBasicDO" >
        <result column="WebCastId" property="webcastId" jdbcType="NVARCHAR" />
        <result column="AttendeeJoinUrl" property="attendeeJoinUrl" jdbcType="NVARCHAR" />
        <result column="Description" property="description" jdbcType="NVARCHAR" />
        <result column="Subject" property="subject" jdbcType="NVARCHAR" />
        <result column="TeacherName" property="teacherName" jdbcType="NVARCHAR" />
        <result column="StartTime" property="startTime" jdbcType="TIMESTAMP" />
        <result column="EndTime" property="endTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <select id="getRecordedBasic" resultMap="RecordedResult">
        SELECT a.lbId, a.attendeeJoinUrl, a.description, a.webcastId,
               b.Subject, b.TeacherName, b.StartTime, b.EndTime
        FROM EMoney_UserTrain.dbo.GenseeBasic_LB a
                 LEFT JOIN EMoney_UserTrain.dbo.Gensee_WebCast b ON a.webcastId = b.WebCastId
        WHERE a.lbId = #{lbId}
    </select>

    <select id="getLiveBasic" resultMap="LiveResult">
        SELECT WebCastId, attendeeJoinUrl, description, Subject, TeacherName, StartTime, EndTime
        FROM EMoney_UserTrain.dbo.Gensee_WebCast
        WHERE WebCastId = #{webCastId}
    </select>

    <select id="getLiveWatchByUidAndVid" resultType="cn.emoney.pojo.bo.CourseWatchDetailDTO">
        SELECT id, UserId as uid, datediff(second, RuhuiTime, leaveTime) as duration, RuhuiTime as startTime, leaveTime as endTime, CreateTime as createTime
        FROM [EMoney_UserTrain].[dbo].GenseeDetail_BI WITH (NOLOCK)
        WHERE UserId = #{uid}
          AND Hcid = #{vid};
    </select>

    <select id="getRecordedWatchByUidAndVid" resultType="cn.emoney.pojo.bo.CourseWatchDetailDTO">
        SELECT id, uid, CAST(duration AS INT) / 1000 as duration, startTime, leaveTime as endTime, CreateTime as createTime
        FROM [EMoney_UserTrain].[dbo].GenseeHistroy_LB WITH (NOLOCK)
        WHERE uid = #{uid}
          AND vodId = #{vid};
    </select>

    <select id="getUserByLiveWatchTimeSumGte" resultType="java.lang.Integer">
        SELECT UserId as uid
        FROM [EMoney_UserTrain].[dbo].GenseeDetail_BI WITH (NOLOCK)
        WHERE Hcid = #{vid}
        GROUP BY UserId
        HAVING sum(datediff(second, RuhuiTime, leaveTime)) >= #{duration};
    </select>

    <select id="getUserByRecordedWatchTimeSumGte" resultType="java.lang.Integer">
        SELECT uid
        FROM [EMoney_UserTrain].[dbo].GenseeHistroy_LB WITH (NOLOCK)
        WHERE vodId = #{vid}
        GROUP BY uid
        HAVING sum(cast(duration AS int) / 1000) >= #{duration};
    </select>

    <select id="getUserByLiveWatchTimeSumGteByOffset" resultType="cn.emoney.pojo.CourseWatchDO">
        SELECT TOP (#{limit}) t.id, t.uid, t.duraiton
        FROM (SELECT ROW_NUMBER() OVER (ORDER BY min(id))        AS id,
                      UserId                                      as uid,
                     sum(datediff(second, RuhuiTime, leaveTime)) as duraiton
              FROM [EMoney_UserTrain].[dbo].GenseeDetail_BI WITH (NOLOCK)
              WHERE Hcid = #{vid}
                AND datediff(second, RuhuiTime, leaveTime) > 0
              GROUP BY UserId
              HAVING sum(datediff(second, RuhuiTime, leaveTime)) >= #{duration}) as t
        WHERE t.id > #{offset}
        ORDER BY t.id;
    </select>

    <select id="getUserByLiveWatchDurationGte" resultType="cn.emoney.pojo.CourseWatchDO">
        SELECT TOP (#{limit}) id, UserId as uid, datediff(second, RuhuiTime, leaveTime) as duration
        FROM [EMoney_UserTrain].[dbo].GenseeDetail_BI WITH (NOLOCK)
        WHERE id > #{offset}
          AND Hcid = #{vid}
          AND datediff(second, RuhuiTime, leaveTime) >= #{duration}
        ORDER BY id;
    </select>

    <select id="getUserByRecordedWatchDurationGteAndDate" resultType="cn.emoney.pojo.CourseWatchDO">
        SELECT TOP (#{limit}) id, uid, CAST(duration AS INT) / 1000 as duration
        FROM [EMoney_UserTrain].[dbo].GenseeHistroy_LB WITH (NOLOCK)
        WHERE id > #{offset}
          AND vodId = #{vid}
          AND CAST(leaveTime AS DATE) = #{date}
          AND CAST(duration AS INT) / 1000 >= #{duration}
        ORDER BY id;
    </select>

    <select id="getUserByRecordedWatchDurationGteAndStartTimeLte" resultType="cn.emoney.pojo.CourseWatchDO">
        SELECT TOP (#{limit}) id, uid, CAST(duration AS INT) / 1000 as duration
        FROM [EMoney_UserTrain].[dbo].GenseeHistroy_LB WITH (NOLOCK)
        WHERE id > #{offset}
          AND vodId = #{vid}
          AND startTime <![CDATA[<=]]> #{startTime}
          AND CAST(duration AS INT) / 1000 >= #{duration}
        ORDER BY id;
    </select>

    <select id="getUserByRecordedWatchDurationGteAndStartTimeGteLte" resultType="cn.emoney.pojo.CourseWatchDO">
        SELECT TOP (#{limit}) id, uid, CAST(duration AS INT) / 1000 as duration
        FROM [EMoney_UserTrain].[dbo].GenseeHistroy_LB WITH (NOLOCK)
        WHERE id > #{offset}
          AND vodId = #{vid}
          AND startTime <![CDATA[>=]]> #{inTimeHead}
          AND startTime <![CDATA[<=]]> #{inTimeTail}
          AND CAST(duration AS INT) / 1000 >= #{duration}
        ORDER BY id;
    </select>

    <select id="getUserByRecordedWatchDurationGteAndLeaveTimeLte" resultType="cn.emoney.pojo.CourseWatchDO">
        SELECT TOP (#{limit}) id, uid, CAST(duration AS INT) / 1000 as duration
        FROM [EMoney_UserTrain].[dbo].GenseeHistroy_LB WITH (NOLOCK)
        WHERE id > #{offset}
          AND vodId = #{vid}
          AND leaveTime <![CDATA[<=]]> #{leaveTime}
          AND CAST(duration AS INT) / 1000 >= #{duration}
        ORDER BY id;
    </select>
</mapper>