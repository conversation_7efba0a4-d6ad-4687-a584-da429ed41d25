<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.transfer.TbSyncOaUserInfoMapper">
  <resultMap id="BaseResultMap" type="cn.emoney.pojo.TbSyncOaUserInfoDO">
    <!--@mbg.generated-->
    <!--@Table tb_Sync_oa_user_info-->
    <result column="UserId" jdbcType="VARCHAR" property="userid" />
    <result column="UserCode" jdbcType="VARCHAR" property="usercode" />
    <result column="UserCName" jdbcType="VARCHAR" property="usercname" />
    <result column="UserDept" jdbcType="VARCHAR" property="userdept" />
    <result column="DeptName" jdbcType="VARCHAR" property="deptname" />
    <result column="Manager" jdbcType="VARCHAR" property="manager" />
    <result column="JobID" jdbcType="VARCHAR" property="jobid" />
    <result column="JobName" jdbcType="VARCHAR" property="jobname" />
    <result column="TeamID" jdbcType="VARCHAR" property="teamid" />
    <result column="TeamName" jdbcType="VARCHAR" property="teamname" />
    <result column="GroupID" jdbcType="VARCHAR" property="groupid" />
    <result column="GroupName" jdbcType="VARCHAR" property="groupname" />
    <result column="avatar" jdbcType="VARCHAR" property="avatar" />
    <result column="thumb_avatar" jdbcType="VARCHAR" property="thumbAvatar" />
    <result column="jobNum" jdbcType="VARCHAR" property="jobnum" />
    <result column="qr_code" jdbcType="VARCHAR" property="qrCode" />
    <result column="ModTime" jdbcType="TIMESTAMP" property="modtime" />
    <result column="TID" jdbcType="VARCHAR" property="tid" />
    <result column="SID" jdbcType="VARCHAR" property="sid" />
    <result column="State" jdbcType="VARCHAR" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    UserId, UserCode, UserCName, UserDept, DeptName, Manager, JobID, JobName, TeamID, 
    TeamName, GroupID, GroupName, avatar, thumb_avatar, jobNum, qr_code, ModTime, TID, 
    SID, [State]
  </sql>

  <select id="queryByUid" resultMap="BaseResultMap">
    SELECT TOP 1 oui.* FROM dbo.tb_Sync_oa_user_info oui
    LEFT JOIN dbo.tb_Sync_service_user su ON su.belong_user_id=oui.UserId
    WHERE oui.State='1242453/234' AND su.USER_TYPE=1 AND su.P_ID=#{uid}
  </select>
</mapper>