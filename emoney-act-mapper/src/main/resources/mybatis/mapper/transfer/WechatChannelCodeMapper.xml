<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.emoney.mapper.transfer.WechatChannelCodeMapper">
  <resultMap id="BaseResultMap" type="cn.emoney.pojo.WechatChannelCodeDO">
    <!--@mbg.generated-->
    <!--@Table wechat_channel_code-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="tid" jdbcType="VARCHAR" property="tid" />
    <result column="sid" jdbcType="VARCHAR" property="sid" />
    <result column="is_in_cmp" jdbcType="INTEGER" property="isInCmp" />
    <result column="is_send_welcome_msg" jdbcType="INTEGER" property="isSendWelcomeMsg" />
    <result column="is_tag" jdbcType="INTEGER" property="isTag" />
    <result column="config_id" jdbcType="VARCHAR" property="configId" />
    <result column="describe" jdbcType="VARCHAR" property="describe" />
    <result column="short_describe" jdbcType="VARCHAR" property="shortDescribe" />
    <result column="qr_code" jdbcType="VARCHAR" property="qrCode" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, code, remark, tid, sid, is_in_cmp, is_send_welcome_msg, is_tag, config_id, [describe], 
    short_describe, qr_code, createtime
  </sql>

<!--auto generated by MybatisCodeHelper on 2022-02-15-->
  <select id="queryBySid" resultMap="BaseResultMap">
    select TOP 1
    <include refid="Base_Column_List"/>
    from wechat_channel_code
    where sid=#{sid,jdbcType=VARCHAR}
  </select>
</mapper>