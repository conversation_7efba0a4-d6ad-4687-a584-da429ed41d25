package cn.emoney.common.result;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.util.List;
import java.util.stream.Stream;

@Data
public class MobilePageDTO<T> {
    private List<T> data;
    private Pager pager;

    @Data
    public static class Pager {
        private Integer page;
        private Integer rows;
        @JsonAlias("RecordCount")
        private Integer recordCount;
        @JsonAlias("IsAll")
        private Boolean isAll;
        @JsonAlias("Sort")
        private List<String> sort;
    }

    public Stream<T> stream() {
        return data.stream();
    }
}
