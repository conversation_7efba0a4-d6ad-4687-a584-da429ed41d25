package cn.emoney.common.result;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;
import org.springframework.lang.Nullable;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022-01-17
 */
@Data
public class ResultInfo<T> implements Serializable {
    @JsonAlias("RetCode")
    private String RetCode;
    @JsonAlias("RetMsg")
    private String RetMsg;
    @Nullable
    @JsonAlias("Message")
    private T Message;

    public boolean isSuccess() {
        return "0".equals(this.RetCode);
    }
}
