package cn.emoney.common.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * 问卷星AES解密工具类
 * 
 * 解密规则（基于问卷星官方文档）：
 * 1）读取推送的BASE64数据为byte[] encryptedData
 * 2）取AES加解密密钥作为AES解密的KEY
 * 3）取byte[] encryptedData的前16位做为IV
 * 4）取第16位后的字节数组做为待解密内容
 * 5）解密模式使用CBC（密码块链模式）
 * 6）填充模式使用PKCS #7（填充字符串由一个字节序列组成，每个字节填充该字节序列的长度）
 * 7）使用配置好的实例化AES对象执行解密
 * 8）使用UTF-8的方式，读取二进制数组得到原始数据
 *
 * <AUTHOR>
 * @date 2025/01/11
 */
@Slf4j
public class WjxAESUtil {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";
    private static final int IV_LENGTH = 16;

    /**
     * 解密问卷星POST的加密数据
     *
     * @param encryptedContent Base64编码的加密内容
     * @param aesKey AES密钥
     * @return 解密后的JSON字符串
     * @throws Exception 解密失败时抛出异常
     */
    public static String decrypt(String encryptedContent, String aesKey) throws Exception {
        if (encryptedContent == null || encryptedContent.trim().isEmpty()) {
            throw new IllegalArgumentException("加密内容不能为空");
        }

        if (aesKey == null || aesKey.trim().isEmpty()) {
            throw new IllegalArgumentException("AES密钥不能为空，请检查配置或确认是否需要解密");
        }

        try {
            // 1）读取推送的BASE64数据为byte[] encryptedData
            byte[] encryptedData = Base64.getDecoder().decode(encryptedContent);
            
            if (encryptedData == null || encryptedData.length < IV_LENGTH + 1) {
                throw new IllegalArgumentException("加密数据长度不足，至少需要" + (IV_LENGTH + 1) + "字节");
            }

            // 2）取AES加解密密钥作为AES解密的KEY
            byte[] keyBytes = aesKey.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, ALGORITHM);

            // 3）取byte[] encryptedData的前16位做为IV
            byte[] iv = new byte[IV_LENGTH];
            System.arraycopy(encryptedData, 0, iv, 0, IV_LENGTH);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);

            // 4）取第16位后的字节数组做为待解密内容
            byte[] cipherData = new byte[encryptedData.length - IV_LENGTH];
            System.arraycopy(encryptedData, IV_LENGTH, cipherData, 0, cipherData.length);

            // 5）6）7）解密模式使用CBC，填充模式使用PKCS5Padding（等同于PKCS7）
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
            
            byte[] decryptedData = cipher.doFinal(cipherData);

            // 8）使用UTF-8的方式，读取二进制数组得到原始数据
            String result = new String(decryptedData, StandardCharsets.UTF_8);
            
            log.debug("AES解密成功，原始数据长度: {}", result.length());
            return result;

        } catch (Exception e) {
            log.error("AES解密失败，加密内容: {}, 密钥: {}", encryptedContent, maskKey(aesKey), e);
            throw new Exception("AES解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证解密功能（可用于测试）
     * 
     * @param encryptedContent 加密内容
     * @param aesKey AES密钥
     * @return 是否解密成功
     */
    public static boolean validateDecryption(String encryptedContent, String aesKey) {
        try {
            String result = decrypt(encryptedContent, aesKey);
            return result != null && !result.trim().isEmpty();
        } catch (Exception e) {
            log.warn("解密验证失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 掩码显示密钥（用于日志记录，保护敏感信息）
     * 
     * @param key 原始密钥
     * @return 掩码后的密钥
     */
    private static String maskKey(String key) {
        if (key == null || key.length() <= 8) {
            return "****";
        }
        return key.substring(0, 4) + "****" + key.substring(key.length() - 4);
    }
}
