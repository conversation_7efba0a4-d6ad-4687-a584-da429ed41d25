package cn.emoney.common.utils;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

public class StreamUtils {

    public static <D extends Collection<?>, SEQ> Stream<D> page(Function<SEQ, D> loader,
                                                                Function<D, Optional<SEQ>> next,
                                                                SEQ init, Integer nextSize) {
        return page(loader, (c) -> c.size() == nextSize ? next.apply(c) : Optional.empty(), init);
    }

    public static <D extends Collection<?>, SEQ> Stream<D> page(Function<SEQ, D> loader,
                                                                Function<D, Optional<SEQ>> next,
                                                                SEQ init) {
        return StreamSupport.stream(Spliterators.spliteratorUnknownSize(new Iterator<D>() {
                    private SEQ iSeq = init;
                    private boolean hasNext = true;

                    @Override
                    public boolean hasNext() {
                        return this.hasNext;
                    }

                    @Override
                    public D next() {
                        D result = loader.apply(iSeq);
                        Optional<SEQ> nSeq;
                        if (result != null && (nSeq = next.apply(result)).isPresent()) {
                            this.iSeq = nSeq.get();
                            this.hasNext = true;
                        } else {
                            this.hasNext = false;
                        }
                        return result;
                    }
                }, Spliterator.ORDERED), false)
                .filter(Objects::nonNull)
                .filter(c -> !c.isEmpty());
    }

    public static <T> Stream<List<T>> partition(Stream<T> stream, int size) {
        final Spliterator<T> wrapped = stream.spliterator();
        return StreamSupport.stream(new Spliterators.AbstractSpliterator<List<T>>(
                wrapped.estimateSize(),
                wrapped.characteristics() & ~Spliterator.SORTED
        ) {
            @Override
            public boolean tryAdvance(Consumer<? super List<T>> action) {
                List<T> list = new ArrayList<>(size);
                while (wrapped.tryAdvance(list::add) && list.size() < size) {
                }
                if (list.isEmpty()) {
                    return false;
                }
                action.accept(list);
                return true;
            }

            @Override
            public long estimateSize() {
                long e = wrapped.estimateSize();
                if (e == Long.MAX_VALUE) {
                    return Long.MAX_VALUE;
                }
                return e / size + (e % size == 0 ? 0 : 1);
            }
        }, stream.isParallel());
    }
}
