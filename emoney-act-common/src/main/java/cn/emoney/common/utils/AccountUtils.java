package cn.emoney.common.utils;

import cn.emoney.common.result.userinfo.SSOResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @date 2021-12-02
 */
@Slf4j
@Component
public class AccountUtils {

    private static String ssoparsingurl;

    @Value("${ssoparsingurl}")
    public void setUrl(String ssoparsingurl) {
        AccountUtils.ssoparsingurl = ssoparsingurl;
    }

    /**
     * sso解密
     *
     * @param queryString
     * @return cn.emoney.activitycommon.result.userinfo.SSOResult
     * <AUTHOR>
     * @date 2021-12-2 14:44
     */
    public static SSOResult ssoParsing(String queryString) {
        SSOResult ssoResult = new SSOResult();
        if (StringUtils.isEmpty(queryString)) {
            return null;
        }
        String result = OkHttpUtil.get(MessageFormat.format(ssoparsingurl, queryString), null);
        ssoResult.setUserName(JsonUtil.getValue(result, "UserName"));
        ssoResult.setUid(JsonUtil.getValue(result, "Uid"));
        ssoResult.setCid(JsonUtil.getValue(result, "CId"));
        return ssoResult;
    }


}
