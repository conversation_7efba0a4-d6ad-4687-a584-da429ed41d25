package cn.emoney.common.utils;

import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

public class HttpUtils {

    public static Optional<String> getIpAddr(Function<String, String> headerGetter) {
        return streamIpAddr(headerGetter).findFirst();
    }

    public static Stream<String> streamIpAddr(Function<String, String> headerGetter) {
        return Stream.of("X-Forwarded-For",
                        "Proxy-Client-IP",
                        "WL-Proxy-Client-IP",
                        "HTTP_CLIENT_IP",
                        "HTTP_X_FORWARDED_FOR")
                .map(headerGetter)
                .filter(StringUtils::hasText)
                .flatMap(s -> Arrays.stream(s.split(",")))
                .filter(s -> !"unknown".equalsIgnoreCase(s));
    }
}
