<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>emoney-act</artifactId>
        <groupId>cn.emoney</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <groupId>cn.emoney</groupId>
    <artifactId>emoney-act-facade</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>emoney-act-facade</name>
    <description>emoney-act-facade</description>

    <dependencies>
        <dependency>
            <groupId>cn.emoney</groupId>
            <artifactId>emoney-act-service</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
            <version>2.0.9</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>actapi</finalName>
        <!-- 配置将哪些资源文件(静态文件/模板文件/mapper文件)加载到tomcat输出目录里 -->
        <resources>
            <resource>
                <directory>src/main/java</directory><!--java文件的路径-->
                <includes>
                    <include>**/*.*</include>
                </includes>
                <!-- <filtering>false</filtering>-->
            </resource>
            <resource>
                <directory>src/main/resources</directory><!--资源文件的路径-->
                <includes>
                    <include>**/*.*</include>
                </includes>
                <!-- <filtering>false</filtering>-->
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
