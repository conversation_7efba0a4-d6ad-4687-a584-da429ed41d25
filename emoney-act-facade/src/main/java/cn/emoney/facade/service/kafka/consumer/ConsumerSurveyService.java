package cn.emoney.facade.service.kafka.consumer;

import cn.emoney.common.utils.JsonUtil;
import cn.emoney.pojo.WjxSurveyResultDO;
import cn.emoney.pojo.bo.survey.WjxSurveyResultDTO;
import cn.emoney.service.SurveyService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.Iterator;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Kafka消费服务-默认
 *
 * <AUTHOR>
 * @date 2022/02/18 14:13
 **/
@Service
@Slf4j
public class ConsumerSurveyService {

    @Autowired
    private SurveyService surveyService;

    /**
     * 功能描述:
     * 消费Kafka中的新的问卷推送数据
     * @Param: [record, acknowledgment]
     * @Return: void
     * @Author: tengdengming
     * @Date: 2022/2/24 10:54
     */
    @KafkaListener(groupId = "unifySurveyGroup",topics = "newSurveyComing", containerFactory = "ackContainerFactory", autoStartup = "${spring.kafka.listener.enabled:true}")
    public void handleSurveyMessage(ConsumerRecord record, Acknowledgment acknowledgment) {
        try {

            String message = (String) record.value();
            //System.out.println("ConsumerService.handleSurveyMessage:messge="+message );

            WjxSurveyResultDO obj = JsonUtil.toBean(message, WjxSurveyResultDO.class);

            //正则匹配处理答案部分解析
            String pattern = "q[0-9]*[_]*[0-9]*[_]*[0-9]";
            JSONObject jsonObj = JSON.parseObject(message);

            Iterator iter = jsonObj.entrySet().iterator();

            JsonObject answerJson = new JsonObject();

            while (iter.hasNext()){
                Map.Entry entry = (Map.Entry)iter.next();
                if (Pattern.matches(pattern, entry.getKey().toString())){
                    answerJson.addProperty(entry.getKey().toString(),entry.getValue().toString());
                }

                if (entry.getKey()=="sojumpparm"){
                    /**
                     * 0、{scene}:场景
                     * 1、{pid}:软件PID
                     * 2、{uid}:软件用户Uid
                     * 3、{em||0x}: em号或者0x手机号
                     * 4、{unionid}:微信用户unionid
                     * 5、{sid}:软件包sid
                     * 6、{tid}:软件包tid
                     * 7、{ext1}: 扩展参数1
                     * 8、{ext2}: 扩展参数2
                     * 9、{ext3}: 扩展参数3
                     */
                    //System.out.println("*** sojumpparm="+ entry.getValue().toString() );

                    String[] privateArr = (entry.getValue().toString()+"$End").split("\\$");

                    obj.setEmscene(privateArr[0]);
                    obj.setPid(privateArr[1]);
                    if (!privateArr[2].equals("")){
                        obj.setEmuid(Integer.parseInt(privateArr[2]));
                    }
                    obj.setEmno(privateArr[3]);
                    obj.setUnionid(privateArr[4]);
                    obj.setExt1(privateArr[7]);
                    obj.setExt2(privateArr[8]);
                    obj.setExt3(privateArr[9]);
                    obj.setSojumpparam(entry.getValue().toString());

                }
            }

            obj.setAnswer(answerJson.toString());
            surveyService.dealUserSurvey(obj);

            log.info("TOPIC:newSurveyComing GROUP_ID:unifySurveyGroup, 收到消息: {}", message);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 手动提交 offset
            acknowledgment.acknowledge();
        }

    }


    /***
     * 功能描述:
     * 消费消息通知主题surveyNotice，通知各应用接口
     * @Param: [record, acknowledgment]
     * @Return: void
     * @Author: tengdengming
     * @Date: 2022/3/14 10:04
     */
    @KafkaListener(groupId = "noticeSceneGroup",topics = "surveyNotice", containerFactory = "ackNoticeContainerFactory", autoStartup = "${spring.kafka.listener.enabled:true}")
    public void handleNoticeForSurvey(ConsumerRecord record, Acknowledgment acknowledgment) {
        try {
            String message = (String) record.value();

            //System.out.println("ConsumerService.handleNoticeForSurvey message: "+message);

            WjxSurveyResultDO obj = JsonUtil.toBean(message, WjxSurveyResultDO.class);
            surveyService.noticeScene(obj);

            log.debug("TOPIC:surveyNotice GROUP_ID:noticeSceneGroup, 通知成功:{}", message);

        } catch (Exception e) {
            //System.out.println(e.getMessage());

            log.error(e.getMessage(), e);
        } finally {
            // 手动提交 offset
            acknowledgment.acknowledge();
        }

    }





}
