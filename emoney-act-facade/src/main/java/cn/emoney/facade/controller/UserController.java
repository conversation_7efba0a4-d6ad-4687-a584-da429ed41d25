package cn.emoney.facade.controller;

import cn.emoney.common.result.Result;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.pojo.CustServiceDO;
import cn.emoney.pojo.TyUserRegEventDO;
import cn.emoney.pojo.bo.FirstClassDTO;
import cn.emoney.service.FirstClassService;
import cn.emoney.service.UserService;
import cn.emoney.service.kafka.producer.ProducerService;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.MessageFormat;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-01-09
 */
@RestController
@RequestMapping("/user")
@Validated
@Slf4j
public class UserController {

    @Autowired
    private UserService userService;
    @Autowired
    private ProducerService producerService;

    private final RedisService cmpRedisService;
    private static final String redisKey_CustServiceInfo = "active-cycle-id-333:";
    private static final String redisKey_CustServiceInfo_DS = "active-cycle-id-328:";
    private static final String PIDs_DS = "888020000,888080000";

    public UserController(@Qualifier("cmpVisitRedisManager") RedisService cmpRedisService) {
        this.cmpRedisService = cmpRedisService;
    }

    @RequestMapping("/addcountbyactcode")
    public Result<String> AddCountByActCode(HttpServletRequest request) {
        String uid = request.getParameter("uid");
        String actCode = request.getParameter("actcode");
        String value = request.getParameter("value");

        String ret = userService.AddCountByActCode(actCode, uid, value);
        return Result.buildSuccessResult("200", "成功", ret);
    }

    @RequestMapping("/issubmitbyactcodes")
    public Result<String> IsSubmitByActCodes(HttpServletRequest request) {
        String uid = request.getParameter("uid");
        String actCodes = request.getParameter("actcodes");

        String ret = userService.IsSubmitByActCodes(actCodes, uid);
        return Result.buildSuccessResult("200", "成功", ret);
    }

    @CrossOrigin
    @RequestMapping("/getcountbyactcode")
    public Result<String> GetCountByActCode(HttpServletRequest request) {
        String actCodes = request.getParameter("actcode");

        String ret = userService.GetCountByActCodes(actCodes);
        return Result.buildSuccessResult("200", "成功", ret);
    }

    @RequestMapping("/privilegeusersendmsg")
    public Result<String> SendMsg_PrivilegeActUser(HttpServletRequest request) {
        String uid = request.getParameter("uid");
        String mobileX = request.getParameter("mobilex");
        String actcode = request.getParameter("actcode");

        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (StringUtils.isEmpty(mobileX)) {
            return Result.buildErrorResult("-1", "缺少mobileX");
        }
        if (StringUtils.isEmpty(actcode)) {
            return Result.buildErrorResult("-1", "缺少actcode");
        }

        producerService.sendMessage("privilegeActUserComing", uid + "_" + mobileX + "_" + actcode);
        return Result.buildSuccessResult("200", "成功", "");
    }

    @RequestMapping("/getcustserviceinfo")
    public Result<CustServiceDO> getCustServiceInfo(HttpServletRequest request) {
        String mobileX = request.getParameter("mobilex");
        String pid = request.getParameter("pid");
        if (StringUtils.isEmpty(mobileX)) {
            return Result.buildErrorResult("-1", "缺少mobileX");
        }

        if (!StringUtils.isEmpty(mobileX)) {
            String key = redisKey_CustServiceInfo + mobileX.substring(2);
            if (!StringUtils.isEmpty(pid) && PIDs_DS.indexOf(pid) > -1) {
                key = redisKey_CustServiceInfo_DS + mobileX.substring(2);
            }
            String ret = cmpRedisService.get(key, String.class);
            CustServiceDO custServiceDO = JsonUtil.toBean(ret, CustServiceDO.class);

            return Result.buildSuccessResult(custServiceDO);
        }

        return Result.buildSuccessResult();
    }

    /**
     * 体验版用户一键注册事件
     *
     * @return cn.emoney.common.result.Result<java.lang.String>
     * <AUTHOR>
     * @date 2024/3/18 11:08
     */
    @PostMapping("/pushtyuserregmsg")
    public Result<String> pushRegTyUserMessage(@RequestBody TyUserRegEventDO tyUserRegEventDO) {
        if (tyUserRegEventDO == null) {
            return Result.buildErrorResult("-1", "参数不完整");
        }
        if (tyUserRegEventDO != null && StringUtils.isEmpty(tyUserRegEventDO.getMobileX())) {
            return Result.buildErrorResult("-1", "参数不完整");
        }

        producerService.sendMessage("RegTyUserEvent", JSON.toJSONString(tyUserRegEventDO));
        return Result.buildSuccessResult();
    }
}
