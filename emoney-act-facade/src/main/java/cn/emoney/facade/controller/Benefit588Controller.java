package cn.emoney.facade.controller;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.PointAndCouponRequestDO;
import cn.emoney.pojo.bo.Benefit588DTO;
import cn.emoney.pojo.bo.FirstClassDTO;
import cn.emoney.service.Benefit588Service;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-22
 */
@RestController
@RequestMapping("/benefit588")
@Validated
@Slf4j
public class Benefit588Controller {
    @Autowired
    private Benefit588Service benefit588Service;

    /**
     * 获取所有福利列表
     *
     * @param request
     * @return null
     * <AUTHOR>
     * @date 2023/5/23 9:45
     */
    @RequestMapping("/getuserbenefitlist")
    public Result<List<Benefit588DTO>> GetUserBenefitList(HttpServletRequest request) {
        String uid = request.getParameter("uid");
        String actCode = request.getParameter("actcode");
        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult("-1", "缺少uid",null);
        }
        if (StringUtils.isEmpty(actCode)) {
            return Result.buildErrorResult("-1", "缺少uid",null);
        }

        List<Benefit588DTO> list = benefit588Service.getBenefitList(uid, actCode);
        return Result.buildSuccessResult("200", "成功", list);
    }

    /**
     * 获取用户领取福利记录
     *
     * @param request
     * @return null
     * <AUTHOR>
     * @date 2023/5/23 10:04
     */
    @RequestMapping("/getuserbenefitrecord")
    public Result<List<Act588BenifitRecordDO>> GetUserBenefitRecord(HttpServletRequest request) {
        String uid = request.getParameter("uid");
        String actCode = request.getParameter("actcode");
        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult("-1", "缺少uid",null);
        }
        if (StringUtils.isEmpty(actCode)) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }

        List<Act588BenifitRecordDO> list = benefit588Service.getUserBenefitRecord(uid, actCode);
        return Result.buildSuccessResult("200", "成功", list);
    }


    /**
     * 领取福利
     *
     * @param request
     * @return cn.emoney.common.result.Result<java.util.List < cn.emoney.pojo.Act588BenifitRecordDO>>
     * <AUTHOR>
     * @date 2023/5/23 10:05
     */
    @RequestMapping("/getbenefit")
    public Result<String> GetBenefit(HttpServletRequest request) {
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String benefitId = request.getParameter("benefitid");
        String actCode = request.getParameter("actcode");
        String day = request.getParameter("day");
        String source = request.getParameter("source");

        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (StringUtils.isEmpty(actCode)) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }
//        if (StringUtils.isEmpty(pid)) {
//            return Result.buildErrorResult("-1", "缺少pid");
//        }
        if (StringUtils.isEmpty(benefitId)) {
            return Result.buildErrorResult("-1", "缺少benefitId");
        }
        if (StringUtils.isEmpty(day)) {
            return Result.buildErrorResult("-1", "缺少day");
        }
        if (StringUtils.isEmpty(source)) {
            return Result.buildErrorResult("-1", "缺少source");
        }

        return benefit588Service.getBenefit(uid, pid, benefitId, actCode, day, source);
    }

    //封装sendPointAndCoupon方法
    /**
     * 赠送积分+优惠券
     * <AUTHOR>
     * @date 2024/4/24 下午4:21
     * @param request
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/sendpointandcoupon")
    @CrossOrigin
    public Result<String> SendPointAndCoupon(@RequestBody PointAndCouponRequestDO pointAndCouponRequestDO) {
        if (pointAndCouponRequestDO == null) {
            return Result.buildErrorResult("-1", "缺少参数");
        }

        if (StringUtils.isEmpty(pointAndCouponRequestDO.uid)) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (StringUtils.isEmpty(pointAndCouponRequestDO.pid)) {
            return Result.buildErrorResult("-1", "缺少pid");
        }
        if (StringUtils.isEmpty(pointAndCouponRequestDO.actCode)) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }
        if (StringUtils.isEmpty(pointAndCouponRequestDO.pointTaskID)) {
            return Result.buildErrorResult("-1", "缺少pointTaskID");
        }
        if (StringUtils.isEmpty(pointAndCouponRequestDO.couponActivityID)) {
            return Result.buildErrorResult("-1", "缺少couponActivityID");
        }
        if (StringUtils.isEmpty(pointAndCouponRequestDO.couponPrice)) {
            return Result.buildErrorResult("-1", "缺少couponPrice");
        }
        if (Double.parseDouble(pointAndCouponRequestDO.couponPrice) <= 0) {
            return Result.buildErrorResult("-1", "优惠券价格必须大于0");
        }
        return benefit588Service.sendPointAndCoupon(pointAndCouponRequestDO.actCode, pointAndCouponRequestDO.uid, pointAndCouponRequestDO.pid, pointAndCouponRequestDO.pointTaskID, pointAndCouponRequestDO.couponActivityID, Double.parseDouble(pointAndCouponRequestDO.couponPrice));

    }
}
