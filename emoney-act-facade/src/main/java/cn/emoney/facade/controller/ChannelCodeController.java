package cn.emoney.facade.controller;

import cn.emoney.common.enums.BaseResultCodeEnum;
import cn.emoney.common.result.Result;
import cn.emoney.pojo.TbSyncOaUserInfoDO;
import cn.emoney.pojo.WechatChannelCodeDO;
import cn.emoney.service.WechatChannelCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022-02-15
 */
@RestController
@RequestMapping("/channelcode")
@Validated
@Slf4j
public class ChannelCodeController {

    @Autowired
    private WechatChannelCodeService wechatChannelCodeService;

    /**
     * 根据sid获取二维码信息
     *
     * @param sid
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.WechatChannelCodeDO>
     * <AUTHOR>
     * @date 2022/2/15 17:33
     */
    @CrossOrigin
    @GetMapping("/querybysid")
    public Result<WechatChannelCodeDO> queryBySid(@NotNull(message = "sid不能为空") String sid) {
        try {
            WechatChannelCodeDO wechatChannelCodeDO = wechatChannelCodeService.queryBySid(sid);
            return Result.buildSuccessResult(wechatChannelCodeDO);
        } catch (Exception e) {
            log.error("channelcode/queryBySid error:" + e);
            return Result.buildErrorResult(BaseResultCodeEnum.SYSTEM_ERROR.getMsg());
        }
    }
}
