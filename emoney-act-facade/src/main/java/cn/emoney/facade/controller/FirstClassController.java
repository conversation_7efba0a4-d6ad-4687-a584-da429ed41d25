package cn.emoney.facade.controller;

import cn.emoney.common.result.Result;
import cn.emoney.common.utils.JsonUtil;
import cn.emoney.pojo.bo.FirstClassDTO;
import cn.emoney.pojo.bo.PointRecordAddRequestDTO;
import cn.emoney.service.FirstClassService;
import cn.emoney.service.PointService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-03-21
 */
@RestController
@RequestMapping("/firstclass")
@Validated
@Slf4j
public class FirstClassController {
    @Autowired
    private FirstClassService firstClassService;

    /**
     * 获取课程列表
     * <AUTHOR>
     * @date 2022/3/18 13:27
     * @param request
     * @return java.lang.String
     */
    @RequestMapping("/getuserclasslist")
    public Result<List<FirstClassDTO>> GetUserClassList(HttpServletRequest request) {
        String uid = request.getParameter("uid");
        String activityID = request.getParameter("activityID");

        List<FirstClassDTO> list = firstClassService.getUserClassList(uid, activityID, "");
        return Result.buildSuccessResult("200", "成功", list);
    }

    @RequestMapping("/addclassrecord")
    public Result<String> SetClassRecord(HttpServletRequest request){
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String classid = request.getParameter("classid");
        String platform = request.getParameter("platform");
        String activityID = request.getParameter("activityID");

        if (uid==null || uid.length()==0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }if (pid==null || pid.length()==0) {
            return Result.buildErrorResult("-1", "缺少pid");
        }if (classid==null || classid.length()==0) {
            return Result.buildErrorResult("-1", "缺少classid");
        }if (platform==null || platform.length()==0) {
            return Result.buildErrorResult("-1", "缺少platform");
        }
        boolean hasRecord = firstClassService.HasClassRecord(uid, classid,activityID);
        if (hasRecord) {
            return Result.buildErrorResult("-1", "已有观看记录");
        } else {
            //观看记录保存
            firstClassService.SetClassRecord(uid, pid, classid, platform,activityID);

            return Result.buildSuccessResult("成功");
        }
    }


    /**
     * 获取特定功能使用天数
     * <AUTHOR>
     * @date 2023/2/14 11:34
     * @param request
     * @return java.lang.String
     */
    @RequestMapping("/getspecialfundays")
    public Result<Integer> GetSpecialFunDays(HttpServletRequest request){
        String uid = request.getParameter("uid");

        Integer days = firstClassService.getSpecialFunDays(uid);
        return Result.buildSuccessResult("200", "成功",days);
    }
}
