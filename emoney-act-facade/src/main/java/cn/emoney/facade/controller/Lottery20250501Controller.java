package cn.emoney.facade.controller;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.Lottery0808PrizeDO;
import cn.emoney.pojo.LotteryCountDO;
import cn.emoney.service.Lottery20250501Service;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025/4/23 15:50
 * @param null
 * @return null
 */
@RestController
@RequestMapping("/lottery20250501")
@CrossOrigin
@Slf4j
public class Lottery20250501Controller {
    @Autowired
    private Lottery20250501Service lottery20250501Service;

    @RequestMapping("/refreshtestinfo")
    public String refreshTestInfo(String uid,String actcode,String mobileX) {
        if (uid == null || uid.length() == 0) {
            return "缺少uid";
        }
        if (actcode == null || actcode.length() == 0) {
            return "缺少actcode";
        }
        List<Act588BenifitRecordDO> list = lottery20250501Service.refreshMyLotteryList(actcode, uid);
         lottery20250501Service.refreshLotteryCount(actcode, uid,mobileX);

        return  JSON.toJSONString(list);
    }
    /**
     * 抽奖
     * <AUTHOR>
     * @date 2025/4/23 15:50
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.Lottery0808PrizeDO>
     */
    @RequestMapping("/dolottery")
    public Result<Lottery0808PrizeDO> doLottery(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String platform = request.getParameter("platform");


        if (uid == null || uid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (pid == null || pid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少pid");
        }
        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }
        if (platform == null || platform.length() == 0) {
            return Result.buildErrorResult("-1", "缺少platform");
        }

        return lottery20250501Service.doLottery(actCode, uid, pid, platform);
    }

    /**
     * 获取抽奖次数
     * <AUTHOR>
     * @date 2025/4/23 15:50
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.LotteryCountDO>
     */
    @RequestMapping("/getlotterycount")
    public Result<LotteryCountDO> getLotteryCount(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String mobilex = request.getParameter("mobilex");

        if (uid == null || uid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actcode");
        }

        LotteryCountDO ret = lottery20250501Service.getLotteryCount(actCode, uid, mobilex);
        return Result.buildSuccessResult(ret);

    }

    /**
     * 获取中奖记录
     * <AUTHOR>
     * @date 2025/4/23 15:50
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>>
     */
    @RequestMapping("/getmylotteryinfo")
    public Result<List<Act588BenifitRecordDO>> getMyLotteryList(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");

        if (uid == null || uid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }

        List<Act588BenifitRecordDO> list = lottery20250501Service.getMyLotteryList(actCode,uid);
        return Result.buildSuccessResult(list);
    }

    @RequestMapping("/getlotterylist")
    public Result<List<Act588BenifitRecordDO>> getLotteryList(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String top = request.getParameter("top");

        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }
        Integer topNum = 10;
        if(top != null){
            topNum = Integer.parseInt(top);
        }

        List<Act588BenifitRecordDO> list = lottery20250501Service.getLotteryList(topNum,actCode);
        return Result.buildSuccessResult(list);
    }
}
