package cn.emoney.facade.controller;

import cn.emoney.common.constants.RedisConstants;
import cn.emoney.common.result.Result;
import cn.emoney.common.utils.CsvExportUtil;
import cn.emoney.pojo.LotteryDetailDO;
import cn.emoney.pojo.LotteryInfo;
import cn.emoney.pojo.LotteryLogisticsPackageConfDO;
import cn.emoney.pojo.LotteryPrizeDO;
import cn.emoney.pojo.vo.AccountVO;
import cn.emoney.service.LotteryService;
import cn.emoney.service.UserService;
import cn.emoney.service.redis.RedisService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抽奖类API
 * <AUTHOR>
 * @date 2022/05/18 14:34
 **/
@RestController
@RequestMapping("/lottery")
@Slf4j
public class LotteryController {

    @Autowired
    private LotteryService lotteryService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private UserService userService;

    public String _batchNo = "0601";

    @Autowired
    RedissonClient redissonClient;

    @RequestMapping("/redisson")
    public String testRedisson() {
        //获取分布式锁，只要锁的名字一样，就是同一把锁
        RLock lock = redissonClient.getLock("NikeLock");

        //加锁（阻塞等待），默认过期时间是无限期
        lock.lock();
        try {
            //如果业务执行过长，Redisson会自动给锁续期
            Thread.sleep(3000);
            System.out.println("加锁成功，执行业务逻辑");
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            //解锁，如果业务执行完成，就不会继续续期
            lock.unlock();
        }

        return "Hello Redisson!";
    }

    /**
     * 功能描述:
     * 〈〉
     *
     * @Param: [request]
     * @Return: java.lang.String
     * @Author: tengdengming
     * @Date: 2022/6/30 15:37
     */
    @RequestMapping("/downloadlotterydetail")
    public void downloadlotterydetail(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String batchno = request.getParameter("batchno");
        String pwd = request.getParameter("pwd");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        String checkStr = sdf.format((new Date()).getTime()).substring(0, 10).replaceAll("-", "");
        if (!checkStr.equals(pwd)) {
            response.getWriter().write("Authenticate Failed.Please Check The Pwd");
            return;
        } else {
            String key = RedisConstants.Redis_Pre_Activity + "lottery:downloadlimittimes";
            Integer alreadyTimes = (redisService.get(key) == null) ? 0 : (int) redisService.get(key);

            if (alreadyTimes > 5) {
                response.getWriter().write("Download LimitTimes Reached.Sorry!");//5 times per 2 mins
                return;
            }
            redisService.set(key, alreadyTimes + 1, (long) 2 * 60);
        }

        Calendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());

        //获取近7天的抽奖明细
        calendar.add(Calendar.DATE, -30);

        String fromdate = sdf.format(calendar.getTime());
        List<LotteryDetailDO> detailList = lotteryService.getLotteryDetailsFromDate(batchno, fromdate);

        if (detailList == null) {
            response.getWriter().write("no data");
            return;
        }

        String fn = "RecentlyLotteryDetailList_" + sdf.format(new Date()) + ".csv";
        // 读取字符编码
        String utf = "UTF-8";
        // 设置响应
        response.setContentType("application/ms-txt.numberformat:@");
        response.setCharacterEncoding(utf);
        response.setHeader("Pragma", "public");
        response.setHeader("Cache-Control", "max-age=30");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fn, "UTF-8"));

        //1、组装数据
        List dataList = new ArrayList<>();

        Map map = null;
        for (LotteryDetailDO item : detailList) {
            map = new HashMap<>();
            map.put("uid", item.getUid());
            map.put("em", item.getEm());
            map.put("mobile0x", item.getMobile0x());
            map.put("mobilemask", item.getMobilemask());
            map.put("level", item.getLevel());
            map.put("group", item.getGroup());
            map.put("detid", item.getLotterytarget());
            map.put("lotterytime", item.getLotterytime());
            dataList.add(map);
        }

        //2、构造导出数据结构
        String titles = "UID,EM号,手机号密文,手机号掩码,奖品等级号,组,抽奖货品单号,抽奖时间";  // 设置表头
        String keys = "uid,em,mobile0x,mobilemask,level,group,detid,lotterytime";  //设置每列字段

        //3、文件导出
        OutputStream os = response.getOutputStream();
        CsvExportUtil.doExport(dataList, titles, keys, os);

        //CsvExportUtil.doExport(dataList , titles, keys, os);
        os.close();
        //return "";
    }

    @RequestMapping("/testRate")
    public String testRate(@RequestParam Integer loop) {

        StringBuilder sb = new StringBuilder();
        sb.append("概率验证<br>");
        int[] bingoInt = {0, 0, 0, 0};

        sb.append(JSON.toJSONString(bingoInt) + "</br>");

        List<LotteryPrizeDO> prizelist = lotteryService.initPrizeList("深度");

        for (int i = 0; i < loop; i++) {
            LotteryPrizeDO bingo = lotteryService.lotteryRateTest(prizelist);
            sb.append(JSON.toJSONString(bingo));
            if (bingo.level == 1) {
                bingoInt[0]++;
            }
            if (bingo.level == 2) {
                bingoInt[1]++;
            }
            if (bingo.level == 3) {
                bingoInt[2]++;
            }
            if (bingo.level == 4) {
                bingoInt[3]++;
            }

            sb.append("</br>");
        }

        sb.append(JSON.toJSONString(bingoInt) + "</br>");

        return sb.toString();
    }

    @RequestMapping("/dolottery")
    public String doLottery(HttpServletRequest request) {

        String mobile0x = request.getParameter("mobile0x");
        String uid = request.getParameter("uid");
        String batchno = request.getParameter("batchno");
        String callback = request.getParameter("callback");
        System.out.println("mobile0x= " + mobile0x + "batchno=" + batchno);

        String backStr = "";

        RLock lock = redissonClient.getLock("RedissonLock_" + uid);
        lock.lock();
        try {
            //Thread.sleep(10);

            if (uid.isEmpty()) {
                backStr = JSON.toJSONString(Result.buildSuccessResult("-9", "缺少UID", ""));
            }
            if (mobile0x.isEmpty()) {
                backStr = JSON.toJSONString(Result.buildSuccessResult("-9", "缺少密文手机号", ""));
            }

            batchno = _batchNo;

            try {
                log.info("user dolottery " + mobile0x + "," + uid + "," + batchno + "," + uid + "\n");

                LotteryInfo myLotteryInfo = lotteryService.getMyLotteryInfo(mobile0x, uid, batchno);

                if (myLotteryInfo != null) {

                    List<JSONObject> orderlist = myLotteryInfo.getLotteryInfoList();
                    Integer leftTimes = 0;
                    String lotteryOrder = "";
                    String lotteryDetId = "";
                    String lotteryVersion = "";

                    //循环订单检查是否有抽奖机会
                    for (JSONObject item : orderlist) {
                        int curOrderTimes = Integer.parseInt(item.getString("timesLeft"));
                        int orderStatus = Integer.parseInt(item.getString("orderStatus"));

                        if (curOrderTimes > 0 && lotteryOrder.equals("") && orderStatus == 0) {
                            lotteryOrder = item.getString("orderNo");
                            lotteryDetId = item.getString("detId");
                            //判断是否是深度版
                            lotteryVersion = item.getString("activityName").contains("深度") ? "深度" : "掘金";
                            leftTimes = leftTimes + curOrderTimes;
                        }
                    }

                    if (leftTimes <= 0) {
                        //无抽奖机会
                        backStr = JSON.toJSONString(Result.buildSuccessResult("-1", "无抽奖机会", ""));

                    } else {


                        /*获取uid绑定的账号列表*/
                        String EM = "", mobileMask = "", EM_Second = "";

                        /*List<BindAccountVO> accList = userService.GetBindAccountList(uid);

                        for (BindAccountVO item:accList) {
                            if (item.AccountType==0 ){
                                if (!EM.equals("")){
                                    EM_Second = item.getAccountName();
                                }else{
                                    EM = item.getAccountName();
                                }
                            }
                            if (item.AccountType==1){mobileMask = item.getAccountName();}
                        }*/

                        /*2022年6月15日 需求变更：部分用户发生非平行续费，抽奖则按照当前账号版本进行抽奖赠送 *********  **********/
                        //String Pid = userService.GetAccountPID(EM);
                        //mobile0x = "0xF59CF42D6503FE45B53C691091F00003";
                        List<AccountVO> emList = userService.queryAccountListByAccount(mobile0x);

                        //List<JSONObject> zhList = new ArrayList<>();
                        HashMap mapAcc = new HashMap();

//                        SimpleDateFormat sdf    = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

                        for (AccountVO item : emList) {
                            if (item.getMaskMobile() != null && !item.getMaskMobile().equals("")) {
                                mobileMask = item.getMaskMobile();
                            }

                            if (item.getPid() != null && (item.getPid() == ********* || item.getPid() == *********)) {
                                LocalDateTime endDate = (LocalDateTime) mapAcc.get("endDate");
                                if (endDate != null) {
                                    if (item.getEndDate() != null) {
                                        if (endDate.isBefore(item.getEndDate())) {
                                            mapAcc.put("endDate", item.getEndDate());
                                            mapAcc.put("em", item.getUsername());
                                            mapAcc.put("pid", item.getPid());
                                        }
                                    }
                                } else {
                                    mapAcc.put("endDate", item.getEndDate());
                                    mapAcc.put("em", item.getUsername());
                                    mapAcc.put("pid", item.getPid());
                                }

                            }

                        }

                        if (mapAcc.get("em") != null && !mapAcc.get("em").equals("")) {
                            EM = mapAcc.get("em").toString();
                        }

                        String Pid = "";
                        if (mapAcc.get("pid") != null && !mapAcc.get("pid").equals("")) {
                            Pid = mapAcc.get("pid").toString();
                        }

                        //确认赠送的EM号
                        lotteryVersion = Pid.equals("*********") ? "深度" : "掘金";

                        //抽奖
                        LotteryPrizeDO prize = lotteryService.doLottery(mobile0x, uid, batchno, lotteryVersion);

                        //发送奖品
                        Boolean sendOk = lotteryService.sendPrize(mobile0x, prize.getLogisticCode(), batchno, EM, lotteryOrder, lotteryDetId);

                  /*      if (!sendOk && !EM_Second.equals("")){//如果发送失败，则发送第二个EM号
                            sendOk = lotteryService.sendPrize(mobile0x,prize.getLogisticCode(),batchno,EM_Second,lotteryOrder,lotteryDetId);
                        }*/

                        if (sendOk) {
                            //记录中奖明细
                            LotteryDetailDO lotteryDetailDO = new LotteryDetailDO();
                            lotteryDetailDO.setUid(uid);
                            lotteryDetailDO.setEm(EM);
                            lotteryDetailDO.setMobile0x(mobile0x);
                            lotteryDetailDO.setBatchno(batchno);
                            lotteryDetailDO.setLotterytarget(lotteryDetId);
                            lotteryDetailDO.setMobile0x(mobile0x);
                            lotteryDetailDO.setMobilemask(mobileMask);
                            lotteryDetailDO.setPrizecode(prize.getCode());
                            lotteryDetailDO.setPrizename(prize.getName());
                            lotteryDetailDO.setGroup(prize.getBelongGroup());
                            lotteryDetailDO.setLevel(prize.getLevel());

                            //记录抽奖明细
                            Boolean recordRet = lotteryService.recordLotteryDetail(lotteryDetailDO);

                            if (recordRet) {
                                JSONObject ret = JSONObject.parseObject("{'prizeName':'" + prize.getName() + "','prizeCode':'" + prize.getCode() + "','level':" + prize.getLevel() + "}");
                                backStr = JSON.toJSONString(Result.buildSuccessResult("1", "抽奖成功", ret));
                                //刷新缓存
                                lotteryService.freshUserLotteryList(batchno, uid);
                            } else {
                                backStr = JSON.toJSONString(Result.buildSuccessResult("2", "抽奖成功记录失败", "OrderNo=" + lotteryOrder));
                            }

                        } else {
                            backStr = JSON.toJSONString(Result.buildSuccessResult("-2", "调用物流发送奖品失败", ""));
                        }

                    }
                } else {
                    backStr = JSON.toJSONString(Result.buildSuccessResult("-3", "无抽奖订单", ""));
                }

            } catch (Exception e) {
                log.error("抽奖失败", e);
                backStr = JSON.toJSONString(Result.buildSuccessResult("-9", "发生异常", e.getMessage()));
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.unlock();
            System.out.println("Redisson UnLock");
        }

        log.info("dolottery result " + mobile0x + "," + uid + "," + batchno + "," + uid + "\n" + backStr);

        if (callback == null || callback.equals("")) {
            return backStr;
        } else {
            return callback + "(" + backStr + ")";
        }

    }

    @RequestMapping("/getmylotteryinfo")
    public String getMyLotteryInfo(HttpServletRequest request) {
        String mobile0x = request.getParameter("mobile0x");
        String uid = request.getParameter("uid");
        String batchno = request.getParameter("batchno");
        String callback = request.getParameter("callback");

        String backStr = "";

        System.out.println("mobile0x= " + mobile0x + "batchno=" + batchno);

        if (!batchno.equals(_batchNo)) {
            backStr = JSON.toJSONString(Result.buildSuccessResult("-9", "批次号错误", ""));
        }

        if (uid.isEmpty()) {
            backStr = JSON.toJSONString(Result.buildSuccessResult("-9", "缺少UID", ""));
        }

        if (mobile0x.isEmpty()) {
            backStr = JSON.toJSONString(Result.buildSuccessResult("-9", "缺少密文手机号", ""));
        }

        LotteryInfo lotteryInfo = lotteryService.getMyLotteryInfo(mobile0x, uid, batchno);

        if (lotteryInfo.getLotteryInfoList() != null) {
            for (JSONObject item : lotteryInfo.getLotteryInfoList()) {
                String v = item.getString("orderNo");

                v = v.substring(0, 2) + "****" + v.substring(v.length() - 4, v.length());
                item.put("orderNo", v);
            }
        }

        backStr = JSON.toJSONString(Result.buildSuccessResult("1", "获取抽奖订单信息", lotteryInfo));

        if (callback == null || callback.equals("")) {
            return backStr;
        } else {
            return callback + "(" + backStr + ")";
        }

    }

    @RequestMapping("/recentlylottery")
    public String recentlylottery(HttpServletRequest request) {

        String batchno = request.getParameter("batchno");
        String callback = request.getParameter("callback");

        String backStr = "";

        List<LotteryDetailDO> recentlyLotteryList = lotteryService.getRecentlyLotteryDetailList(batchno);

        recentlyLotteryList.sort(Comparator.comparing(LotteryDetailDO::getLotterytime).reversed());

        backStr = JSON.toJSONString(Result.buildSuccessResult("1", "获取最近中奖列表", recentlyLotteryList));


        if (callback == null || callback.equals("")) {
            return backStr;
        } else {
            return callback + "(" + backStr + ")";
        }


    }

    /**
     * 根据手机号获取已购订单
     *
     * @param request
     * @return cn.emoney.common.result.Result
     * <AUTHOR>
     * @date 2024/10/25 16:47
     */
    @RequestMapping("/getlotteryorderlist")
    @ResponseBody
    @CrossOrigin
    public Result<List<LotteryLogisticsPackageConfDO>> getLotteryCount(String mobilex,String actcode) {
        if (StringUtils.isEmpty(mobilex)) {
            return Result.buildErrorResult("-1", "缺少mobileX");
        }

        if (!StringUtils.isEmpty(mobilex)) {
            String cacheKey_LotteryCount_common = RedisConstants.Redis_Pre_Activity + "LotteryCount:Common:";
            String key = cacheKey_LotteryCount_common + mobilex;

            if (!StringUtils.isEmpty(actcode)) {
                key = cacheKey_LotteryCount_common + actcode + ":" + mobilex;
            }

            List<LotteryLogisticsPackageConfDO> getBuyPackageRedisList = redisService.getList(key, LotteryLogisticsPackageConfDO.class);
            if(getBuyPackageRedisList == null){
                getBuyPackageRedisList = new ArrayList<>();
            }

            //获取手机号对应的em号,合并em号上面的订单
            List<AccountVO> emList = userService.queryAccountListByAccount(mobilex);
            for (AccountVO item : emList) {
                String emCard = "";
                if (item.getMobile() == null && item.getUsername() != null) {
                    emCard = item.getUsername();
                }
                if (!StringUtils.isEmpty(emCard)) {
                    key = cacheKey_LotteryCount_common + emCard;
                    List<LotteryLogisticsPackageConfDO> emBuyList = redisService.getList(key, LotteryLogisticsPackageConfDO.class);

                    if (emBuyList != null && emBuyList.size() > 0) {
                        getBuyPackageRedisList.addAll(emBuyList);
                    }
                }
            }

            //订单去重
            List<LotteryLogisticsPackageConfDO> uniqueList = new ArrayList<>();
            for (LotteryLogisticsPackageConfDO item : getBuyPackageRedisList) {
                List<LotteryLogisticsPackageConfDO> filterList = uniqueList.stream().filter(x -> x.orderId.equals(item.orderId)).collect(Collectors.toList());

                if (filterList.size() == 0) {
                    uniqueList.add(item);
                }
            }

            return Result.buildSuccessResult(uniqueList);
        }
        return Result.buildSuccessResult();
    }
}
