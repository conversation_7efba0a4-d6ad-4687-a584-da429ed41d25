package cn.emoney.facade.controller;

import cn.emoney.common.result.Result;
import cn.emoney.common.utils.WjxAESUtil;
import cn.emoney.pojo.WjxSurveyResultDO;
import cn.emoney.pojo.vo.survey.SurveyDisplayVO;
import cn.emoney.service.SurveyAnswerParseService;
import cn.emoney.service.SurveyService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import cn.hutool.core.date.DateTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.CompletableFuture;

/**
 * 问卷星重定向Controller
 * 处理问卷星POST请求，同时处理业务逻辑和页面展示
 *
 * <AUTHOR>
 * @date 2025/01/11
 */
@Controller
@RequestMapping("/survey")
@Slf4j
public class WjxRedirectController {

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private SurveyAnswerParseService answerParseService;

    @Value("${wjx.aes.key:}")
    private String aesKey;

    /**
     * 处理问卷星重定向POST请求
     * 同时处理业务逻辑（推送到Kafka）和页面展示
     * 支持加密和非加密两种数据格式
     *
     * @param content 问卷星POST的内容（可能是加密的，也可能是明文JSON）
     * @param model Spring MVC Model
     * @return 页面模板名称
     */
    @PostMapping("/wjx-redirect")
    public String handleWjxRedirect(@RequestParam(value = "content", required = false) String content,
                                   Model model) {
        log.info("接收到问卷星重定向请求，开始处理...");

        try {
            // 1. 参数验证
            if (StringUtils.isEmpty(content)) {
                log.error("问卷星POST内容为空");
                return buildErrorResponse(model, "未接收到问卷数据");
            }

            // 2. 数据解密/解析（支持加密和非加密两种格式）
            String decryptedJson = processWjxData(content);
            if (decryptedJson == null) {
                return buildErrorResponse(model, "数据处理失败");
            }

            log.info("数据处理成功，数据长度: {}", decryptedJson.length());
            log.debug("处理后数据: {}", decryptedJson);

            // 3. 解析为问卷结果对象
            WjxSurveyResultDO surveyResult;
            try {
                surveyResult = JSON.parseObject(decryptedJson, WjxSurveyResultDO.class);
                if (surveyResult == null) {
                    log.error("问卷数据解析为null");
                    return buildErrorResponse(model, "问卷数据格式错误");
                }
                log.info("问卷数据解析成功，joinId: {}, activity: {}",
                        surveyResult.getJoinid(), surveyResult.getActivity());
            } catch (Exception e) {
                log.error("问卷数据解析失败", e);
                return buildErrorResponse(model, "问卷数据解析失败：" + e.getMessage());
            }

            // 4. 后台处理：异步推送到Kafka队列（复用现有业务逻辑）
            CompletableFuture.runAsync(() -> {
                try {
                    JSONObject jsonObject = JSON.parseObject(decryptedJson);
                    surveyService.pushUserSurvey(jsonObject);
                    log.info("问卷数据已成功推送到Kafka队列，joinId: {}", surveyResult.getJoinid());
                } catch (Exception e) {
                    log.error("推送问卷数据到Kafka失败，joinId: {}", surveyResult.getJoinid(), e);
                }
            });

            // 5. 前台处理：解析答案数据用于页面展示
            SurveyDisplayVO displayVO;
            try {
                displayVO = answerParseService.parseSurveyAnswer(surveyResult);
                log.info("问卷答案解析成功，答题数量: {}", displayVO.getAnswerCount());
            } catch (Exception e) {
                log.error("解析问卷答案失败，joinId: {}", surveyResult.getJoinid(), e);
                return buildErrorResponse(model, "解析问卷答案失败：" + e.getMessage());
            }

            // 6. 设置页面数据并返回
            model.addAttribute("surveyData", displayVO);
            log.info("问卷星重定向处理完成，返回展示页面，joinId: {}", surveyResult.getJoinid());
            
            return "survey-result";

        } catch (Exception e) {
            log.error("处理问卷星重定向请求时发生未知错误", e);
            return buildErrorResponse(model, "系统处理错误：" + e.getMessage());
        }
    }

    /**
     * 健康检查接口（可选）
     */
    @GetMapping("/wjx-redirect/health")
    @ResponseBody
    public Result<String> healthCheck() {
        return Result.buildSuccessResult("1", "服务正常", DateTime.now().toString());
    }

    /**
     * 测试页面（可选，用于开发调试）
     */
    @GetMapping("/wjx-redirect/test")
    public String testPage(Model model) {
        // 创建测试数据
        SurveyDisplayVO testData = new SurveyDisplayVO();
        testData.setSurveyName("测试问卷");
        testData.setUserName("测试用户");
        testData.setSubmitTime("2025-01-11 10:30:00");
        testData.setStatus("success");
        testData.setMessage("这是测试页面");
        
        model.addAttribute("surveyData", testData);
        return "survey-result";
    }

    /**
     * 处理问卷星数据（支持加密和非加密两种格式）
     *
     * @param content 原始数据
     * @return 处理后的JSON字符串
     */
    private String processWjxData(String content) {
        try {
            // 首先尝试直接解析为JSON（非加密格式）
            if (isValidJson(content)) {
                log.info("检测到非加密格式数据，直接使用");
                return content;
            }

            // 如果不是有效JSON，尝试AES解密（加密格式）
            if (StringUtils.isNotEmpty(aesKey)) {
                try {
                    String decrypted = WjxAESUtil.decrypt(content, aesKey);
                    log.info("AES解密成功");
                    return decrypted;
                } catch (Exception e) {
                    log.warn("AES解密失败，可能是非加密数据: {}", e.getMessage());
                }
            } else {
                log.info("未配置AES密钥，跳过解密尝试");
            }

            // 如果解密失败，再次尝试直接使用原始数据
            log.warn("无法解密数据，尝试直接使用原始内容");
            return content;

        } catch (Exception e) {
            log.error("处理问卷星数据失败", e);
            return null;
        }
    }

    /**
     * 检查字符串是否为有效的JSON格式
     *
     * @param str 待检查的字符串
     * @return 是否为有效JSON
     */
    private boolean isValidJson(String str) {
        try {
            JSON.parseObject(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 构建错误响应
     */
    private String buildErrorResponse(Model model, String errorMessage) {
        SurveyDisplayVO errorVO = new SurveyDisplayVO();
        errorVO.setStatus("error");
        errorVO.setMessage(errorMessage);
        errorVO.setSurveyName("处理失败");
        errorVO.setSubmitTime(DateTime.now().toString());

        model.addAttribute("surveyData", errorVO);
        log.warn("返回错误页面，错误信息: {}", errorMessage);

        return "survey-result";
    }
}
