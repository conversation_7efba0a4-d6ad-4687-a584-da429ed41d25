package cn.emoney.facade.controller;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.Lottery0808PrizeDO;
import cn.emoney.service.Lottery20230808Service;
import cn.emoney.service.UserService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-28
 */
@RestController
@RequestMapping("/lottery0808")
@Slf4j
public class Lottery20230808Controller {
    @Autowired
    private Lottery20230808Service lottery20230808Service;

    @RequestMapping("/refreshtestinfo")
    public String refreshTestInfo(String uid,String actcode) {
        if (uid == null || uid.length() == 0) {
            return "缺少uid";
        }
        if (actcode == null || actcode.length() == 0) {
            return "缺少actcode";
        }
        List<Act588BenifitRecordDO> list = lottery20230808Service.refreshMyLottery0808List(actcode, uid);
        Integer count = lottery20230808Service.refreshLotteryCountByDay(actcode, uid);

        return count + "||" + JSON.toJSONString(list);
    }
    /**
     * 抽奖
     * <AUTHOR>
     * @date 2023/7/28 9:41
     * @param request
     * @param response 
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.Lottery0808PrizeDO>
     */
    @RequestMapping("/dolottery")
    public Result<Lottery0808PrizeDO> doLottery0808(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String platform = request.getParameter("platform");


        if (uid == null || uid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (pid == null || pid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少pid");
        }
        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }
        if (platform == null || platform.length() == 0) {
            return Result.buildErrorResult("-1", "缺少platform");
        }

        return lottery20230808Service.doLottery0808(actCode, uid, pid, platform);
    }

    /**
     * 赠送88积分+100优惠券
     * <AUTHOR>
     * @date 2023/7/28 9:45
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/sendpp")
    public Result<String> sendPP0808(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String type = request.getParameter("type");
        if (uid == null || uid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (pid == null || pid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少pid");
        }
        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }
        return lottery20230808Service.sendPP0808(actCode,uid,pid,type);
    }

    /**
     * 获取当天是否弹出过窗口
     * <AUTHOR>
     * @date 2023/7/28 9:48
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/gettipstatusbyday")
    public Result<String> getTipStatusByDay(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String type = request.getParameter("type");

        if (uid == null || uid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }

        boolean ret = lottery20230808Service.getTipStatusByDay(uid,type);
        return Result.buildSuccessResult(String.valueOf(ret));
    }

    /**
     * 获取当天抽奖次数
     * <AUTHOR>
     * @date 2023/7/28 9:53
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/getlotterycountbyday")
    public Result<String> getLotteryCountByDay(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");

        if (uid == null || uid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }

        Integer ret = lottery20230808Service.getLotteryCountByDay(actCode,uid);
        return Result.buildSuccessResult(String.valueOf(ret));
    }

    /**
     * 获取中奖记录
     * <AUTHOR>
     * @date 2023/7/28 9:53
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>>
     */
    @RequestMapping("/getmylotteryinfo")
    public Result<List<Act588BenifitRecordDO>> getMyLottery0808List(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");

        if (uid == null || uid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }

        List<Act588BenifitRecordDO> list = lottery20230808Service.getMyLottery0808List(actCode,uid);
        return Result.buildSuccessResult(list);
    }
}
