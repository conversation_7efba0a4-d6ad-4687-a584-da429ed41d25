package cn.emoney.facade.controller;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.bo.*;
import cn.emoney.pojo.vo.FirstClassContentVO;
import cn.emoney.pojo.vo.FirstClassCourseVO;
import cn.emoney.service.FirstClassRollService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-03-21
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/first-class")
public class FirstClassRollController {
    private final FirstClassRollService service;

    public FirstClassRollController(FirstClassRollService service) {
        this.service = service;
    }

    @GetMapping("/getuserclasslist")
    public Result<List<FirstClassDTO>> courseListCompatible(@NotNull Long questId,
                                                            @NotNull Long uid) {
        List<FirstClassCourseVO> courseList = service.userCourseList(questId, uid);
        if (courseList.isEmpty()) {
            return Result.buildSuccessResult(Collections.emptyList());
        }
        Map<Integer, String> columnMap = service.findClassContent(questId)
                .map(FirstClassContentDTO::getColumns)
                .map(columns -> columns.stream()
                        .collect(Collectors.toMap(FirstClassContentDTO.ClassColumn::getId, FirstClassContentDTO.ClassColumn::getName)))
                .orElse(Collections.emptyMap());

        List<FirstClassDTO> list = courseList.stream()
                .map(vo -> {
                    FirstClassDTO compatible = new FirstClassDTO();

                    compatible.setId(vo.getId());
                    compatible.setClassName(vo.getTitle());
                    compatible.setClassSummary(vo.getSummary());
                    compatible.setTeacherName(vo.getTeacherName());
                    compatible.setWeekComment(vo.getComment());
                    compatible.setClassUrl(vo.getVideoUrl());
                    compatible.setClassCover(vo.getVideoCover());
                    compatible.setAppCoverImg(vo.getVideoCoverApp());
                    compatible.setBeginTime(vo.getStartTime());
                    compatible.setEndTime(vo.getEndTime());
                    compatible.setParentID(vo.getParentId());
                    Optional.of(vo.getParentId())
                            .map(columnMap::get)
                            .ifPresent(compatible::setParentName);
                    compatible.setLiveVideoID(String.valueOf(vo.getCourseId()));
                    compatible.setViewCount(vo.getViewCount());
                    compatible.setBookBaseAmount(vo.getBookBaseAmount());
                    compatible.setReplayBaseAmount(vo.getReplayBaseAmount());

                    if (vo.getExtraUrls() != null) {
                        vo.getExtraUrls().forEach(url -> {
                            switch (url.getType()) {
                                case 0:
                                    // 练习
                                    compatible.setVoteUrl1(url.getUrl());
                                    break;
                                case 1:
                                    compatible.setTipName("笔记");
                                    compatible.setVoteUrl(url.getUrl());
                                    break;
                                case 2:
                                    compatible.setTipName("报告");
                                    compatible.setVoteUrl(url.getUrl());
                                    break;
                                default:
                                    break;
                            }
                        });
                    }
                    Optional.ofNullable(vo.getPlayDone()).ifPresent(compatible::setDone);
                    return compatible;
                })
                .collect(Collectors.toList());
        return Result.buildSuccessResult(list);
    }

    @GetMapping("/content")
    public Result<FirstClassContentVO> content(@NotNull Long questId) {
        return service.findClassContent(questId)
                .map(dto -> {
                    FirstClassContentVO vo = new FirstClassContentVO();
                    vo.setId(dto.getId());
                    vo.setName(dto.getName());
                    Optional.ofNullable(dto.getColumns()).ifPresent(columns -> {
                        vo.setColumns(columns.stream().map(c -> {
                            FirstClassContentVO.ClassColumn column = new FirstClassContentVO.ClassColumn();
                            column.setId(c.getId());
                            column.setName(c.getName());
                            return column;
                        }).collect(Collectors.toList()));
                    });
                    vo.setMeta(dto.getMeta());
                    vo.setStartTime(dto.getStartTime());
                    vo.setEndTime(dto.getEndTime());
                    vo.setStatus(dto.getStatus());
                    vo.setValid(dto.isValid());
                    return vo;
                })
                .map(Result::buildSuccessResult)
                .orElseGet(() -> Result.buildErrorResult("-1", "找不到任务"));
    }

    @GetMapping("/courseList")
    public Result<List<FirstClassCourseVO>> courseList(@NotNull Long questId,
                                                       @NotNull Long uid) {
        List<FirstClassCourseVO> list = service.userCourseList(questId, uid);
        return Result.buildSuccessResult(list);
    }

    @GetMapping("/classProgress")
    public Result<FirstClassProgressDTO> classProgress(@NotNull Long questId,
                                                       @NotNull Long uid) {
        return service.userClassProgress(questId, uid)
                .map(Result::buildSuccessResult)
                .orElseGet(() -> Result.buildErrorResult("-1", "找不到任务"));
    }

    @GetMapping("/courseProgress")
    public Result<FirstClassCourseProgressDTO> courseProgress(@NotNull Long questId,
                                                              @NotNull Long uid,
                                                              @NotNull Integer subId) {
        return service.userCourseProgress(questId, uid, subId)
                .map(Result::buildSuccessResult)
                .orElseGet(() -> Result.buildErrorResult("-1", "找不到课程"));
    }

    @PostMapping("/courseProgress")
    public Result<String> setCourseProgress(@NotNull Long questId,
                                            @NotNull Long uid,
                                            @NotNull Integer subId,
                                            @NotNull String platform) {
        int state = service.completeCourse(questId, uid, subId, platform);
        if (state == 1) {
            return Result.buildSuccessResult("成功");
        }
        if (state == 0) {
            return Result.buildErrorResult("2", "该进度已完成");
        }
        return Result.buildErrorResult("-1", "失败");
    }

    @GetMapping("/courseReward")
    public Result<List<FirstClassCourseRewardDTO>> courseReward(@NotNull Long questId,
                                                                @NotNull Long uid,
                                                                @NotNull Integer subId) {
        return Result.buildSuccessResult(service.userCourseReward(questId, uid, subId));
    }

    @GetMapping("/courseRecord")
    public Set<Integer> getCourseRecord(@NotNull Long questId,
                                        @NotNull Long uid) {
        return service.userCourseState(questId, uid);
    }

    @GetMapping("/courseWatchTimes")
    public Result<Integer> courseWatchTimes(@NotNull Long questId, @NotNull Long uid) {
        Integer times = service.userCourseState(questId, uid).size();
        return Result.buildSuccessResult(times);
    }

    @GetMapping("/specialFunDays")
    public Result<Integer> specialFunDays(@NotNull Long uid) {
        Integer days = service.getSpecialFunDays(uid);
        return Result.buildSuccessResult(days);
    }
}
