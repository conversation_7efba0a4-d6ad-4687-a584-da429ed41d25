package cn.emoney.facade.controller;

import cn.emoney.common.enums.BaseResultCodeEnum;
import cn.emoney.common.result.Result;
import cn.emoney.pojo.TbSyncOaUserInfoDO;
import cn.emoney.service.NoviceGuideService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2022-01-05
 */

@RestController
@RequestMapping("/noviceguide")
@Validated
@Slf4j
public class NoviceGuideController {

    @Autowired
    private NoviceGuideService noviceGuideService;

    /**
     * 根据用户uid获取归属业务员信息
     *
     * @param uid
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.TbSyncOaUserInfoDO>
     * <AUTHOR>
     * @date 2022/1/5 16:10
     */
    @RequestMapping("/querysalesmanbyuid")
    public Result<TbSyncOaUserInfoDO> querySalesmanByUid(@NotNull(message = "用户uid不能为空") Long uid) {
        try {
            TbSyncOaUserInfoDO tbSyncOaUserInfoDO = noviceGuideService.queryByUid(uid.toString());
            return Result.buildSuccessResult(tbSyncOaUserInfoDO);
        } catch (Exception e) {
            log.error("noviceguide/queryByUid error:" + e);
            return Result.buildErrorResult(BaseResultCodeEnum.SYSTEM_ERROR.getMsg());
        }
    }
}
