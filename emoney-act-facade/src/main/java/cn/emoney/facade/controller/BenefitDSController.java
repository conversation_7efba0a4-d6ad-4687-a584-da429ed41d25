package cn.emoney.facade.controller;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.vo.LoginUserInfoVO;
import cn.emoney.service.BenefitDsService;
import cn.emoney.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023-09-06
 */
@RestController
@RequestMapping("/benefitds")
@Validated
@Slf4j
public class BenefitDSController {
    @Autowired
    private BenefitDsService benefitDsService;

    @Autowired
    private UserService userService;

    @RequestMapping("/sendsignbenefit")
    public Result<String> sendSignBenefit(HttpServletRequest request) {
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String day = request.getParameter("day");

        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (StringUtils.isEmpty(day)) {
            return Result.buildErrorResult("-1", "缺少day");
        }

        //获取绑定账号
        LoginUserInfoVO loginUserInfoVO = userService.getBoundUserInfo(uid, pid);
        if (loginUserInfoVO == null) {
            return Result.buildErrorResult("-1", "未获取到绑定账号信息");
        }
        if (StringUtils.isEmpty(loginUserInfoVO.getMobileX())) {
            return Result.buildErrorResult("-1", "未获取到绑定手机号");
        }
        if (StringUtils.isEmpty(loginUserInfoVO.getAccount())) {
            return Result.buildErrorResult("-1", "未获取到绑定em号");
        }
        if (StringUtils.isEmpty(loginUserInfoVO.getPid())) {
            return Result.buildErrorResult("-1", "未获取到pid");
        }

        return benefitDsService.sendSignBenefit(Integer.parseInt(day), loginUserInfoVO.getPid(), loginUserInfoVO.getAccount(), loginUserInfoVO.getMobileX());
    }
}
