package cn.emoney.facade.controller;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.PrivilegeClientInd;
import cn.emoney.pojo.PrivilegeClientResult;
import cn.emoney.service.ActPrivilegeSevice;
import cn.emoney.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 客户端-特权指标
 * <AUTHOR>
 * @date 2024-01-23
 */
@RestController
@RequestMapping("/privilege")
@Slf4j
public class PrivilegeController {
    @Autowired
    private ActPrivilegeSevice actPrivilegeSevice;

    /**
     * 查询用户资金指标开通状态列表-客户端使用
     * <AUTHOR>
     * @date 2024/1/23 14:16
     * @param uid
     * @return cn.emoney.pojo.PrivilegeClientResult
     */
    @RequestMapping("/getuserprivilegelist")
    public PrivilegeClientResult getUserPrivilegeList(String uid){
        PrivilegeClientResult result = new PrivilegeClientResult();
        if (StringUtils.isEmpty(uid)) {
            result.setResult("-1");
            result.setStatus("1");
            result.setMsg("缺少uid");

            return result;
        }
        String actCode = "qgn20240122";
        List<PrivilegeClientInd> list = actPrivilegeSevice.getUserPrivilegeConfig(uid,actCode);
        result.setResult("0");
        result.setStatus("1");
        result.setMsg("success");
        result.setInd(list);
        return result;
    }

    /**
     * 抢攻能成功后记录
     * <AUTHOR>
     * @date 2024/1/23 14:16
     * @param uid
     * @param actCode
     * @param source
     * @return cn.emoney.common.result.Result<java.lang.Integer>
     */
    @RequestMapping("/adduserrecord")
    public Result<Integer> addUserPrivilegeRecord(String uid,String actCode,Integer source) {
        if (StringUtils.isEmpty(uid)) {
            return Result.buildErrorResult("-1", "缺少uid", null);
        }
        if (StringUtils.isEmpty(actCode)) {
            return Result.buildErrorResult("-1", "缺少actCode", null);
        }
        if (source == null) {
            return Result.buildErrorResult("-1", "缺少source", null);
        }

        int ret = actPrivilegeSevice.AddUserPrivilegeRecord(uid, actCode, source);

        if (ret > 0) {
            return Result.buildSuccessResult();
        } else {
            return Result.buildErrorResult("保存记录失败");
        }
    }
}
