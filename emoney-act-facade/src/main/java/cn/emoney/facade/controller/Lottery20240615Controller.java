package cn.emoney.facade.controller;

import cn.emoney.common.result.Result;
import cn.emoney.pojo.Act588BenifitRecordDO;
import cn.emoney.pojo.Lottery0808PrizeDO;
import cn.emoney.pojo.LotteryCountDO;
import cn.emoney.service.Lottery20240306Service;
import cn.emoney.service.Lottery20240615Service;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-02-29
 */
@RestController
@RequestMapping("/lottery0615")
@CrossOrigin
@Slf4j
public class Lottery20240615Controller {
    @Autowired
    private Lottery20240615Service lottery20240615Service;

    @RequestMapping("/refreshtestinfo")
    public String refreshTestInfo(String uid,String actcode,String mobileX) {
        if (uid == null || uid.length() == 0) {
            return "缺少uid";
        }
        if (actcode == null || actcode.length() == 0) {
            return "缺少actcode";
        }
        List<Act588BenifitRecordDO> list = lottery20240615Service.refreshMyLotteryList(actcode, uid);
         lottery20240615Service.refreshLotteryCount(actcode, uid,mobileX);

        return  JSON.toJSONString(list);
    }
    /**
     * 抽奖
     * <AUTHOR>
     * @date 2023/7/28 9:41
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<cn.emoney.pojo.Lottery0808PrizeDO>
     */
    @RequestMapping("/dolottery")
    public Result<Lottery0808PrizeDO> doLottery(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String pid = request.getParameter("pid");
        String platform = request.getParameter("platform");


        if (uid == null || uid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (pid == null || pid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少pid");
        }
        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }
        if (platform == null || platform.length() == 0) {
            return Result.buildErrorResult("-1", "缺少platform");
        }

        return lottery20240615Service.doLottery(actCode, uid, pid, platform);
    }

    /**
     * 获取当天抽奖次数
     * <AUTHOR>
     * @date 2023/7/28 9:53
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.lang.String>
     */
    @RequestMapping("/getlotterycount")
    public Result<LotteryCountDO> getLotteryCount(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");
        String mobilex = request.getParameter("mobilex");

        if (uid == null || uid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }
        if (mobilex == null || mobilex.length() == 0) {
            return Result.buildErrorResult("-1", "缺少mobilex");
        }

        LotteryCountDO ret = lottery20240615Service.getLotteryCount(actCode, uid, mobilex);
        return Result.buildSuccessResult(ret);

    }

    /**
     * 获取中奖记录
     * <AUTHOR>
     * @date 2023/7/28 9:53
     * @param request
     * @param response
     * @return cn.emoney.common.result.Result<java.util.List<cn.emoney.pojo.Act588BenifitRecordDO>>
     */
    @RequestMapping("/getmylotteryinfo")
    public Result<List<Act588BenifitRecordDO>> getMyLotteryList(HttpServletRequest request , HttpServletResponse response) {
        String actCode = request.getParameter("actcode");
        String uid = request.getParameter("uid");

        if (uid == null || uid.length() == 0) {
            return Result.buildErrorResult("-1", "缺少uid");
        }
        if (actCode == null || actCode.length() == 0) {
            return Result.buildErrorResult("-1", "缺少actCode");
        }

        List<Act588BenifitRecordDO> list = lottery20240615Service.getMyLotteryList(actCode,uid);
        return Result.buildSuccessResult(list);
    }
}
