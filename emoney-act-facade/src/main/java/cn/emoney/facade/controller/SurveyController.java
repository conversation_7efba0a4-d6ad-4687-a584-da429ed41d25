package cn.emoney.facade.controller;

import cn.emoney.common.result.Result;
import cn.emoney.common.result.userinfo.SSOResult;
import cn.emoney.common.utils.AccountUtils;
import cn.emoney.pojo.WjxSurveyResultDO;
import cn.emoney.pojo.vo.survey.WjxSurveyReceiptVO;
import cn.emoney.service.MobileService;
import cn.emoney.service.SurveyService;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;


/**
 * 提交问卷
 * <AUTHOR>
 * @date 2022-01-20
 */
@RestController
@RequestMapping("/survey")
@Slf4j
public class SurveyController {

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private MobileService  mobileService;

    /**
    * construct
    * */
    public SurveyController() {

    }

    /***
     * 功能描述:投放场景动态获取用户，透明跳转，组装soJumpParm
     * SoJumpParm占位说明  访问附加SSO跳转
     * @Param:
     * @Return:
     * @Author: tengdengming
     * @Date: 2023/6/7 19:43
     */
    @RequestMapping("/gotopc")
     public void gotopc(HttpServletRequest request , HttpServletResponse response) throws IOException {

        String wjx = request.getParameter("wjx");
        String uid = request.getParameter("uid") == null ? "" : request.getParameter("uid");
        String pid = request.getParameter("pid") == null ? "" : request.getParameter("pid");
        String sid = request.getParameter("sid") == null ? "" : request.getParameter("sid");
        String tid = request.getParameter("tid") == null ? "" : request.getParameter("tid");
        String scene = request.getParameter("scene") == null ? "" : request.getParameter("scene");
        String ext1 = request.getParameter("ext1") == null ? "" : request.getParameter("ext1");
        String ext2 = request.getParameter("ext2") == null ? "" : request.getParameter("ext2");
        String ext3 = request.getParameter("ext3") == null ? "" : request.getParameter("ext3");

        String em = "";
        try {
            SSOResult ssoResult = AccountUtils.ssoParsing(request.getQueryString());
            if (ssoResult != null) {
                em = ssoResult.getUserName();
            }
        } catch (Exception e) {
            log.error("跳转课前问卷获取用户名失败", e.getMessage());
        }

        /*
         * 0、{scene}:场景
         * 1、{pid}:软件PID
         * 2、{uid}:软件用户Uid
         * 3、{em||0x}:em号或者0x手机号
         * 4、{unionid}:微信用户unionid
         * 5、{sid}:软件包sid
         * 6、{tid}:软件包tid
         * 7、{ext1}:扩展参数1
         * 8、{ext2}:扩展参数2
         * 9、{ext3}:扩展参数3
         */

        if (StringUtils.isNumeric(em)) {
            em = mobileService.mobileEncrpt(em);
        }

        //组装sojumpparm
        String sojumpparamStr = scene + "$" + pid + "$" + uid + "$" + em + "$$" + sid + "$" + tid + "$" + ext1 + "$" + ext2 + "$" + ext3;

        String wjxUrl = wjx + "?sojumpparm=" + sojumpparamStr;
        response.sendRedirect(wjxUrl);

    }


    /**
     * 接受发送的问卷结果
     * @param submitJson
     * @return
     */
    @RequestMapping("/push")
    public Result<String> pushSurveyData(@RequestBody JSONObject submitJson) {
        //System.out.println(submitJson.toJSONString());

        try {
            log.info("WebApplication receive new survey message  "+submitJson.toString() +"\n");
            WjxSurveyReceiptVO vo = surveyService.pushUserSurvey(submitJson);

        }catch (Exception e) {
            log.error("接受问卷星问卷消息失败",e.getMessage());
        }

        return Result.buildSuccessResult("1","received", DateTime.now().toString());

    }

    @RequestMapping("/index")
    public Result<String> index(){
        return Result.buildErrorResult("0","ni","ceshi");
    }

    @RequestMapping("/sceneWait")
    public Result<String> sceneWait(@RequestBody JSONObject noticeJson){
        //System.out.println(noticeJson.toJSONString());
        return Result.buildSuccessResult("1","notice received", DateTime.now().toString());
    }


    @RequestMapping("/getActivitySurveyList")
    public Result<List<WjxSurveyResultDO>> getActivitySurveyList(Long activity,Integer pagesize, Integer pageindex){

        try {
            List<WjxSurveyResultDO> list =  surveyService.getActivitySurveyList(activity,pagesize,pageindex);
            return Result.buildSuccessResult("1","获取问卷答题列表",list);
        } catch (Exception e) {
            log.error("获取问卷答卷列表失败",e);
            e.printStackTrace();
        }

        return Result.buildErrorResult("-1","occur unexpected error with getActivitySurveyList(activity:"+activity.toString()+")");

    }


    @RequestMapping("/getSurveyListByUnionId")
    public Result<List<WjxSurveyResultDO>> getSurveyListByUserUnionId(String unionid,Integer pagesize, Integer pageindex){

        try {
            List<WjxSurveyResultDO> list =  surveyService.getUserSurveyListByUnionId(unionid,pagesize,pageindex);
            return Result.buildSuccessResult("1","获取问卷答题列表",list);
        } catch (Exception e) {
            log.error("获取问卷答卷列表失败",e);
            e.printStackTrace();
        }

        return Result.buildErrorResult("-1","occur unexpected error with getSurveyListByUserUnionId(unionId:"+unionid.toString()+")");

    }

    @RequestMapping("/getUserLatestSurveyByUid")
    public Result<List<WjxSurveyResultDO>> getUserLatestSurveyByUid(Long uid, @RequestParam(required = false, defaultValue = "20") Integer lastCounts){

        try {
            List<WjxSurveyResultDO> list =  surveyService.getUserLatestSurvey(uid, lastCounts);
            return Result.buildSuccessResult("1","获取最近" + lastCounts + "条答卷", list);
        } catch (Exception e) {
            log.error("surveyService.getUserLatestSurvey 获取问卷答卷列表失败",e);
            e.printStackTrace();
        }

        return Result.buildErrorResult("-1","occur unexpected error with getUserLatestSurveyByUid(emUid:"+uid.toString()+")");
    }

    @RequestMapping("/getUserResultByActivityID")
    public Result<WjxSurveyResultDO> getUserSurveyResultByActivityID(Long uid,Long activityId){

        try {
            WjxSurveyResultDO ret =  surveyService.getUserSurveyResultByActivityID(uid,activityId);
            return Result.buildSuccessResult("1","根据问卷ID获取用户答卷",ret);
        } catch (Exception e) {
            log.error("surveyService.getUserSurveyResultByActivityID 根据问卷ID获取用户答卷",e);
            e.printStackTrace();
        }

        return Result.buildErrorResult("-1","occur unexpected error with getUserLatestSurveyByUid(emUid:"+uid.toString()+")");
    }

    // 获取用户已做过的有标签抽取的问卷Id列表
    // 当前问卷 239811538 258993788
    private final static List<Long> EXTRACTED_ACTIVITY_ID_LIST = Arrays.asList(239811538L, 258993788L);

    @GetMapping("/getUserFinishedExtractedSurvey")
    public Result<List<Long>> getUserFinishedExtractedSurvey(@RequestParam Long uid, @RequestParam(required = false) List<Long> activityList) {
        try {
            if (activityList == null) {
                activityList = EXTRACTED_ACTIVITY_ID_LIST;
            }
            List<Long> list = surveyService.getUserFinishedSurvey(uid, activityList);
            return Result.buildSuccessResult("1","获取用户已做过的有标签抽取的问卷Id列表", list);
        } catch (Exception e) {
            log.error("surveyService.getUserFinishedExtractedSurvey 获取用户已做过的有标签抽取的问卷Id列表",e);
            e.printStackTrace();
        }
        return Result.buildErrorResult("-1","occur unexpected error with getUserFinishedExtractedSurvey(emUid:"+uid.toString()+")");
    }








}
