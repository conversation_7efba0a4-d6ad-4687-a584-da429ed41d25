package cn.emoney.facade.admin;

import cn.emoney.pojo.bo.ActivityQuestAcceptDTO;
import cn.emoney.service.ActivityQuestService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

@RestController
@RequestMapping("/0000O0/hhh/1234/quest")
public class ActQuestController {
    private final ActivityQuestService questService;

    public ActQuestController(ActivityQuestService questService) {
        this.questService = questService;
    }

    @GetMapping("cancel")
    public void cancel(@NotNull Long questId, @NotNull Long uid) {
        questService.cancel(questId, uid);
    }

    @GetMapping("pageAccept")
    public Page<ActivityQuestAcceptDTO> pageAccept(ActivityQuestAcceptDTO example, @PageableDefault Pageable pageable) {
        return questService.pageAcceptByExample(example, pageable);
    }
}
