package cn.emoney.facade.admin;

import cn.emoney.pojo.bo.CourseDetailDTO;
import cn.emoney.pojo.bo.UserCourseWatchDTO;
import cn.emoney.pojo.vo.AccountVO;
import cn.emoney.service.CourseService;
import cn.emoney.service.CourseWatchService;
import cn.emoney.service.UserGroupService;
import cn.emoney.service.UserService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/0000O0/hhh/1234")
public class ActResourceController {
    private final CourseService courseService;
    private final CourseWatchService watchService;
    private final UserGroupService userGroupService;
    private final UserService userService;

    public ActResourceController(CourseService courseService, CourseWatchService watchService, UserGroupService userGroupService, UserService userService) {
        this.courseService = courseService;
        this.watchService = watchService;
        this.userGroupService = userGroupService;
        this.userService = userService;
    }

    @GetMapping("course")
    public CourseDetailDTO courseDetail(Integer id) {
        return courseService.detail(id);
    }

    @GetMapping("watch")
    public UserCourseWatchDTO watchDetail(Integer course, Long uid) {
        return watchService.getCourseWatchDetail(course, uid);
    }

    @GetMapping("accounts")
    public List<AccountVO> accounts(String uid) {
        return userService.listAccountByUid(uid);
    }

    @GetMapping("userGroup")
    public Set<Integer> userGroupDetail(Long uid, @RequestParam List<Integer> group) {
        return userGroupService.checkGroups(uid, group);
    }
}
