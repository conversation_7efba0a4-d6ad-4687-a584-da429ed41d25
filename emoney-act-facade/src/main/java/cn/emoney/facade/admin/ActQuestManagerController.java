package cn.emoney.facade.admin;

import cn.emoney.act.dao.ActivityQuestRepository;
import cn.emoney.act.entity.ActivityQuestEntity;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/0000O0/hhh/1234/quest-manager")
public class ActQuestManagerController {
    private final ActivityQuestRepository repository;

    public ActQuestManagerController(ActivityQuestRepository repository) {
        this.repository = repository;
    }

    @GetMapping("page")
    public Page<ActivityQuestEntity> list(ActivityQuestEntity entity, @PageableDefault Pageable pageable) {
        return repository.findAll(Example.of(entity), pageable);
    }

    @PostMapping("save")
    public ActivityQuestEntity save(@RequestBody ActivityQuestEntity entity) {
        return repository.save(entity);
    }
}
