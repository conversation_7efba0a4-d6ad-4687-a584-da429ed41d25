package cn.emoney.facade.admin;

import cn.emoney.service.FirstClassRollService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/0000O0/hhh/1234/firstClass")
public class ActFirstClassController {
    private final FirstClassRollService firstClassRollService;

    public ActFirstClassController(FirstClassRollService firstClassRollService) {
        this.firstClassRollService = firstClassRollService;
    }

    @PostMapping("recheck")
    public List<Boolean> recheck(Long questId, @RequestBody Map<Long, Long> uid) {
        return uid.entrySet().stream().map((entry) ->
                firstClassRollService.completeQuest(questId, entry.getValue(), entry.getKey())
        ).collect(Collectors.toList());
    }
}
