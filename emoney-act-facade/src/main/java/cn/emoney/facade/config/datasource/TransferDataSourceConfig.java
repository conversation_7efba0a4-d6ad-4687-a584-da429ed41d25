package cn.emoney.facade.config.datasource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = "cn.emoney.mapper.transfer", sqlSessionTemplateRef = "transferSqlSessionTemplate")
public class TransferDataSourceConfig {

    @Bean(name = "transferDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.transfer")
    public DataSource transferDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "transferSqlSessionFactory")
    public SqlSessionFactory transferSqlSessionFactory(@Qualifier("transferDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mybatis/mapper/transfer/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "transferTransactionManager")
    public DataSourceTransactionManager transferTransactionManager(@Qualifier("transferDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "transferSqlSessionTemplate")
    public SqlSessionTemplate transferSqlSessionTemplate(@Qualifier("transferSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
