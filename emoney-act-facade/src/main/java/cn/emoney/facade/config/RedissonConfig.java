package cn.emoney.facade.config;

import cn.emoney.common.utils.RedissonDistributionLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RedisssonConfig
 *
 * <AUTHOR>
 * @date 2022/05/30 19:29
 **/

@Configuration
public class RedissonConfig {

/// 还原 RedissonAutoConfiguration
/// @see org.redisson.spring.starter.RedissonAutoConfiguration.redisson
//    @Value("${spring.redis.host}")
//    String redisHost;
//
//    @Value("${spring.redis.port}")
//    String redisPort;
//
//    @Value("${spring.redis.password}")
//    String redisPassword;
//
//    @Value("${spring.redis.timeout}")
//    Integer redisTimeout;
//
//    /**
//     * Redisson配置
//     * @return
//     */
//    @Bean
//    RedissonClient redissonClient() {
//        //1、创建配置
//        Config config = new Config();
//
//        redisHost = redisHost.startsWith("redis://") ? redisHost : "redis://" + redisHost;
//        SingleServerConfig serverConfig = config.useSingleServer()
//                .setAddress(redisHost + ":" + redisPort)
//                .setTimeout(redisTimeout);
//
//        if (StringUtils.isNotBlank(redisPassword)) {
//            serverConfig.setPassword(redisPassword);
//        }
//
//        return Redisson.create(config);
//    }

    /**
     * 分布式锁
     *
     * @param redissonClient
     * @return
     */
    @Bean
    public RedissonDistributionLock redissonDistributionLock(RedissonClient redissonClient) {
        return new RedissonDistributionLock(redissonClient);
    }

}